"use client";

import { <PERSON>u, Search, XIcon } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import { useContext, useEffect } from "react";
import Icons from "~/app/(client)/client/_components/billing/icons";
import { ACTIONS } from "~/store/Actions";
import { DataContext } from "~/store/GlobalState";
import { TopbarArrowLeftIcon, TopbarArrowRightIcon } from "~/svgs";

const Topbar = () => {
  const name = localStorage.getItem("channelName") || "";
  const router = useRouter();
  const { state, dispatch } = useContext(DataContext);
  const pathname = usePathname();

  // Listen for credit balance updates from agent messages
  useEffect(() => {
    const handleCreditBalanceUpdate = (event: CustomEvent) => {
      const { orgData } = event.detail;
      if (orgData) {
        dispatch({ type: ACTIONS.ORG_DATA, payload: orgData });
      }
    };

    window.addEventListener(
      "creditBalanceUpdated",
      // eslint-disable-next-line
      handleCreditBalanceUpdate as EventListener
    );

    return () => {
      window.removeEventListener(
        "creditBalanceUpdated",
        // eslint-disable-next-line
        handleCreditBalanceUpdate as EventListener
      );
    };
  }, [dispatch]);

  //

  return (
    <div className="fixed h-[60px] w-full z-40 flex items-center justify-between px-4 lg:px-6 py-3 bg-blue-500 text-white">
      <div className="hidden lg:flex items-center gap-6 relative cursor-pointer">
        <div onClick={() => router.back()}>
          <TopbarArrowLeftIcon />
        </div>
        <div onClick={() => router.forward()}>
          <TopbarArrowRightIcon />
        </div>
      </div>

      <div className="flex items-center gap-6 relative cursor-pointer">
        <div
          className="flex lg:hidden"
          onClick={() =>
            dispatch({
              type: ACTIONS.OPEN_SIDEBAR,
              payload: !state?.openSidebar,
            })
          }
        >
          {!state?.openSidebar ? (
            <Menu />
          ) : (
            <XIcon
              className="text-white cursor-pointer"
              onClick={() =>
                dispatch({ type: ACTIONS.OPEN_SIDEBAR, payload: false })
              }
            />
          )}
        </div>

        {!pathname?.includes("/organization/create") && (
          <div
            className="flex lg:hidden border w-[80px] items-center justify-center rounded text-center text-xs py-1"
            onClick={() =>
              dispatch({
                type: ACTIONS.CHANNEL_BAR,
                payload: !state?.channelBar,
              })
            }
          >
            {state?.channelBar ? <p>Close menu</p> : <p>Open menu</p>}
          </div>
        )}
      </div>

      <div className="flex items-center justify-between gap-6 w-full ml-[20px] lg:ml-[410px]">
        {pathname.includes("/client/welcome") ? (
          <div />
        ) : (
          <div className="hidden md:flex relative">
            <Search className="absolute text-white left-3 top-2.5" size={16} />
            <input
              type="text"
              placeholder={name ? `Search ${name}` : "Search"}
              className="bg-blue-100 text-white text-sm placeholder-white pl-10 pr-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400 w-48 md:w-64 lg:w-80"
            />
          </div>
        )}

        <div className="flex md:hidden" />

        {/* Right Section */}
        <div className="flex items-center gap-6 bg-white px-2 py-1 rounded-lg">
          <div
            className="flex items-center gap-2 cursor-pointer"
            onClick={() =>
              router.push("/client/settings/organisation/billing/all-plans")
            }
          >
            <div className="border border-[#d0d0fd] bg-[f1f1fe] rounded-md p-1">
              <Icons name="money" svgProps={{ color: "#8686F9" }} />
            </div>

            <p className="text-black text-[10px] sm:text-xs">
              {state && state.orgData ? state.orgData.credit_balance : 0}{" "}
              <span className="text-[#667085]">AI Credits</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Topbar;
