"use client";
import { useContext, useEffect } from "react";
import { DataContext } from "~/store/GlobalState";
import axios from "axios";
import { Centrifuge } from "centrifuge";
import { ACTIONS } from "~/store/Actions";

/** eslint-disable */

//

export default function ReplyConnection() {
  const { state, dispatch } = useContext(DataContext);

  const connectUrl: any = process.env.NEXT_PUBLIC_CONNECT_URL;

  // get connection token
  const getConnectionToken = async () => {
    const token = localStorage.getItem("token") || "";
    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_BASE_URL}/token/connection`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );
    return response.data.data.token;
  };

  //fetch subscription token
  const getSubscriptionToken = async (channel: string) => {
    const token = localStorage.getItem("token") || "";

    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}/token/subscription`,
      { channel },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    return response.data.data.token;
  };

  // centrifugo connection for notification
  useEffect(() => {
    if (state?.thread?.thread_id) {
      // Initialize Centrifuge client
      const centrifugeClient: any = new Centrifuge(connectUrl, {
        getToken: getConnectionToken,
        debug: true,
      });

      centrifugeClient.on("connect", () => {
        console.log("Connected to Centrifuge");
      });

      centrifugeClient.on("disconnect", () => {
        console.log("Disconnected from Centrifuge");
      });

      // Function to get the token for the personal channel
      const getPersonalChannelSubscriptionToken = async () => {
        return getSubscriptionToken(state?.thread?.thread_id);
      };

      // Create a subscription to the channel
      const sub = centrifugeClient.newSubscription(state?.thread?.thread_id, {
        getToken: getPersonalChannelSubscriptionToken,
      });

      sub.on("subscribed", () => {
        // console.log("ReplySubscription confirmed:".sub);
        dispatch({ type: ACTIONS.REPLY_SUBSCRIPTION, payload: sub });
      });

      // message publishing
      sub.on("publication", (ctx: any) => {
        console.log("Reply connection", ctx?.data);

        if (ctx?.data?.type === "message") {
          dispatch({
            type: ACTIONS.REPLIES,
            payload: { newMessage: ctx.data, isRealTime: true },
          });
        } else {
          // const { user, typing } = ctx.data;
          // const loggedInUser = state?.user?.username;
          // // Update the typing users in the context state
          // if (typing === true) {
          //   if (
          //     user.username !== loggedInUser &&
          //     !state?.userTyping.includes(user.username)
          //   ) {
          //     dispatch({
          //       type: ACTIONS.USER_TYPING,
          //       payload: [...state.userTyping, user.username],
          //     });
          //   }
          // } else {
          //   dispatch({
          //     type: ACTIONS.USER_TYPING,
          //     payload: state.userTyping.filter((u: any) => u !== user.username),
          //   });
          // }
        }
      });

      sub.on("error", (ctx: any) => {
        console.error(`Subscription error: ${ctx.message}`);
      });

      // Connect to Centrifuge and subscribe
      centrifugeClient.connect();
      sub.subscribe();

      // Cleanup on component unmount
      return () => {
        sub.unsubscribe();
        centrifugeClient.disconnect();
      };
    }
  }, [connectUrl, dispatch, state?.thread?.thread_id]);

  //

  return <></>;
}
