"use client";
import { useContext, useEffect, useRef, useState } from "react";
import { DataContext } from "~/store/GlobalState";
import axios from "axios";
import { Centrifuge } from "centrifuge";
import { ACTIONS } from "~/store/Actions";
import { useParams } from "next/navigation";
import { ToastContainer } from "react-toastify";

/* eslint-disable */

//

export default function NotificationConnection() {
  const params = useParams();
  const id = params.id as string;
  const { state, dispatch } = useContext(DataContext);
  const { user, orgId } = state;
  const audioPlayer = useRef<any>(null);
  const [notifications, setNotifications] = useState<any>(null);

  const connectUrl: any = process.env.NEXT_PUBLIC_CONNECT_URL;

  // get connection token
  const getConnectionToken = async () => {
    const token = localStorage.getItem("token") || "";
    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_BASE_URL}/token/connection`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );
    return response.data.data.token;
  };

  //fetch subscription token
  const getSubscriptionToken = async (channel: string) => {
    const token = localStorage.getItem("token") || "";

    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}/token/subscription`,
      { channel },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    return response.data.data.token;
  };

  // centrifugo connection for notification
  useEffect(() => {
    if (id) {
      // Initialize Centrifuge client
      const centrifugeClient: any = new Centrifuge(connectUrl, {
        getToken: getConnectionToken,
        debug: true,
      });

      centrifugeClient.on("connect", () => {
        console.log("Connected to Centrifuge");
      });

      centrifugeClient.on("disconnect", () => {
        console.log("Disconnected from Centrifuge");
      });

      // Function to get the token for the personal channel
      const getPersonalChannelSubscriptionToken = async () => {
        return getSubscriptionToken(orgId);
      };

      // Create a subscription to the channel
      const sub = centrifugeClient.newSubscription(orgId, {
        getToken: getPersonalChannelSubscriptionToken,
      });

      sub.on("subscribed", () => {
        // console.log("Subscription confirmed for notification:", sub);
        dispatch({ type: ACTIONS.NOTIFICATION_SUBSCRIPTION, payload: sub });
      });

      // message publishing
      sub.on("publication", (ctx: any) => {
        dispatch({ type: ACTIONS.NOTIFICATIONS, payload: ctx?.data });
        dispatch({ type: ACTIONS.NOTIFY, payload: ctx?.data?.data });

        const result = ctx?.data;

        // general notifications
        // if (
        //   result?.section === "thread_message" &&
        //   result?.notification_type == "new_message" &&
        //   result?.data?.user_id !== user?.user_id &&
        //   result?.data?.channels_id !== id
        // ) {
        //   playSound();
        // }

        // Channels section====================================
        if (
          result?.section === "reply_message" &&
          result?.notification_type === "new_message"
        ) {
          const message = ctx?.data?.data;
          const updates = ctx?.data?.update_change;

          dispatch({
            type: ACTIONS.UPDATE_MESSAGE_THREAD,
            payload: {
              threadId: message.thread_id,
              reply: message,
              updates,
            },
          });
        }

        // delete channel reply
        if (
          result?.section === "reply_message" &&
          result?.notification_type === "deleted"
        ) {
          const message = ctx?.data?.modification_ids;
          const updates = ctx?.data?.update_change;

          dispatch({
            type: ACTIONS.DELETE_MESSAGE_THREAD_REPLY,
            payload: {
              threadId: message.thread_id,
              messageId: message?.message_id,
              updates,
            },
          });
        }

        // delete channel message
        if (
          result?.section === "thread_message" &&
          result?.notification_type === "deleted"
        ) {
          const message = ctx?.data?.modification_ids;

          dispatch({
            type: ACTIONS.DELETE_CHANNEL_MESSAGE,
            payload: {
              threadId: message.thread_id,
            },
          });
        }

        // Edit channel message
        if (
          result?.section === "thread_message" &&
          result?.notification_type === "updated"
        ) {
          const message = ctx?.data?.data;

          dispatch({
            type: ACTIONS.EDIT_CHANNEL_MESSAGE,
            payload: {
              threadId: message.thread_id,
              newMessageData: message,
            },
          });
        }

        // Edit reply message
        if (
          result?.section === "reply_message" &&
          result?.notification_type === "updated"
        ) {
          const message = ctx?.data?.data;
          const id = ctx?.data?.modification_ids?.message_id;

          dispatch({
            type: ACTIONS.EDIT_REPLY_MESSAGE,
            payload: {
              threadId: id,
              newMessageData: message,
            },
          });
        }
      });

      sub.on("error", (ctx: any) => {
        console.error(`Subscription error: ${ctx.message}`);
      });

      // Connect to Centrifuge and subscribe
      centrifugeClient.connect();
      sub.subscribe();

      // Cleanup on component unmount
      return () => {
        sub.unsubscribe();
        centrifugeClient.disconnect();
      };
    }
  }, [id, connectUrl, dispatch]);

  // centrifugo connection
  // useEffect(() => {
  //   console.log(notifications?.data?.user_id, user?.id)
  //   if (notifications?.notification_type === "new_message" && notifications?.data?.user_id !== user?.id) {

  //   }
  // }, [notifications]);

  const playSound = () => {
    audioPlayer?.current?.play();
  };

  //

  return (
    <div>
      <ToastContainer limit={1} />
      <audio controls ref={audioPlayer} style={{ display: "none" }}>
        <source src="/audio/message.mp3" type="audio/mpeg" />
        Your browser does not support the audio element.
      </audio>
    </div>
  );
}
