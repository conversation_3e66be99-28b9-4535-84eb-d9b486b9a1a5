"use client";

import useEmblaCarousel from "embla-carousel-react";
import "./styles.css";
import { useCallback, useEffect } from "react";
import Brand from "./Brand";

const TopBrands = () => {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: true,
    slidesToScroll: 1,
  });

  const autoplay = useCallback(() => {
    if (!emblaApi) return;
    emblaApi.scrollNext();
  }, [emblaApi]);

  useEffect(() => {
    const intervalId = setInterval(autoplay, 1000);
    return () => clearInterval(intervalId);
  }, [autoplay]);

  return (
    <div className="bg-white py-4 space-y-6">
      <div className="px-6 sm:px-12 md:px-24">
        <h2 className="text-lg font-semibold text-center sm:text-left">
          Compatible with top brands
        </h2>
      </div>

      <div className="embla" ref={emblaRef}>
        <div className="embla__container flex items-center gap-6">
          <div className="embla__slide mb-2 flex justify-center">
            <Brand text="Slack" image="/images/slack-logo.png" />
          </div>

          <div className="embla__slide mb-2 flex justify-center">
            <Brand text="Chatgpt" image="/images/chatgpt-logo.png" />
          </div>

          <div className="embla__slide mb-2 flex justify-center">
            <Brand
              text="Microsoft teams"
              image="/images/microsoft-teams-logo.png"
            />
          </div>

          <div className="embla__slide mb-2 flex justify-center">
            <Brand text="Zoom" image="/images/zoom-logo.png" />
          </div>

          <div className="embla__slide mb-2 mr-6 flex justify-center">
            <Brand text="Google" image="/images/google-logo.png" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopBrands;
