import Link from "next/link";
import React from "react";

const JoinUs = () => {
  return (
    <div className="px-2 sm:px-10 lg:px-24">
      <div className="rounded-md bg-[#303073] flex flex-col items-center justify-between gap-4 sm:flex-row text-white container py-8">
        <div className="w-full md:w-3/5 space-y-5">
          <h3 className="text-xl md:text-2xl">
            Join thousands of Product Managers who are already using Telex to
            boost productivity and track performance.
          </h3>
          <p className="text-xs md:text-base text-[#E9D7FE]">
            Sign up now and boost your campaign efficiency with Telex.
          </p>
        </div>
        <div className="">
          {/* <button className='px-4 py-3 text-sm font-semibold bg-white text-[#303073] hover:bg-[#303073] hover:text-white border border-white rounded-md transition-all duration-500'>Start Your Free 14-Days Trial</button> */}
          <Link href={"/auth/sign-up"}>
            <button className="px-4 py-3 text-sm font-semibold bg-white text-[#303073] hover:bg-[#303073] hover:text-white border border-white rounded-md transition-all duration-500">
              Get Started For Free
            </button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default JoinUs;
