import Image from "next/image";
import Link from "next/link";

const Optimize = ({
  title,
  subtitle,
  body,
  optimize_img,
}: {
  title: string;
  subtitle: string;
  body: string;
  optimize_img: string;
}) => {
  return (
    <div className="px-2 sm:px-10 lg:px-24">
      <div className="rounded-md bg-[#303073] flex flex-col items-center justify-between sm:flex-row text-white pt-4 space-y-12 anotherContainer">
        <div className="space-y-3 flex-1 p-4">
          <h2 className="font-medium text-xl md:text-2xl lg:text-3xl text-[#F9F8FE]">
            {title}
          </h2>
          <p className="text-[#F9F8FE] max-sm:text-sm">{subtitle}</p>
          <p className="pt-4 max-sm:text-sm">{body}</p>
          <div className="flex items-center gap-3">
            <Link href={"/"} className="">
              <Image
                src={"/images/appstore-download.svg"}
                width={130}
                height={50}
                className=""
                alt=""
              />
            </Link>
            <Link href={"/"} className="">
              <Image
                src={"/images/googleplay-download.svg"}
                width={130}
                height={50}
                className=""
                alt=""
              />
            </Link>
          </div>
        </div>

        <div className="flex-1 flex justify-end pl-6">
          <Image
            src={optimize_img}
            className="w-full sm:w-[80%]"
            width={537}
            height={480}
            alt="optmize image"
          />
        </div>
      </div>
    </div>
  );
};

export default Optimize;
