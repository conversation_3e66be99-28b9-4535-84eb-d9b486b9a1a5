"use client";
import React from "react";
import Image from "next/image";
import externalPageImage from "../../../../public/telex-footer-logo.svg";
import Link from "next/link";
import instagramIcon from "../../../../public/instagram-fill.svg";
import xIcon from "../../../../public/xIcon.png";
import facebookIcon from "../../../../public/facebook-fill.svg";

const productLinks = [
  {
    id: 0,
    label: "Customer Support with AI",
    href: "/products/customer-support",
  },
  {
    id: 1,
    label: "Sales With AI",
    href: "/products/sales-ai",
  },
  {
    id: 2,
    label: "Devops and Site Reliability",
    href: "/products/devops-and-site-reliability",
  },
  {
    id: 3,
    label: "Marketing",
    href: "/products/marketing",
  },
  {
    id: 4,
    label: "Document Creation and Management",
    href: "/products/document-creation-and-management",
  },
  {
    id: 5,
    label: "IT Operations",
    href: "/products/it-operations",
  },
  {
    id: 6,
    label: "Video Generation",
    href: "/products/video-generation",
  },
  {
    id: 7,
    label: "Data Extraction and Analysis",
    href: "/products/data-extraction-and-analysis",
  },
  {
    id: 8,
    label: "Customer Relationship",
    href: "/products/customer-relationship",
  },
  {
    id: 9,
    label: "Lead Generation Nuturing",
    href: "/products/lead-generation-nuturing",
  },
  {
    id: 10,
    label: "Site Security",
    href: "/products/site-security",
  },
  {
    id: 11,
    label: "Content Creation",
    href: "/products/content-creation",
  },
  {
    id: 12,
    label: "Market Research",
    href: "/products/market-research",
  },
  {
    id: 13,
    label: "Document Analysis",
    href: "/products/document-analysis",
  },
  {
    id: 14,
    label: "Invoice Processing",
    href: "/products/invoice-processing",
  },
];

const marketersLinks = [
  {
    id: 0,
    label: "Product Managers",
    href: "/product-managers",
  },
  {
    id: 1,
    label: "Tech Founders",
    href: "/tech-founders",
  },
  {
    id: 2,
    label: "Software Engineers",
    href: "/software-engineers",
  },
  {
    id: 3,
    label: "Cyber Security Analysts",
    href: "/cybersecurity-analyst",
  },
  {
    id: 4,
    label: "Small Business Owners",
    href: "/small-business-owners",
  },
  {
    id: 5,
    label: "Social Media Managers",
    href: "/social-media-manager",
  },
  {
    id: 6,
    label: "DevOps",
    href: "/devops",
  },
  {
    id: 7,
    label: "Data Administrators",
    href: "/data-administrator",
  },
  {
    id: 8,
    label: "Landing Page",
    href: "/download",
  },
];

const supportLinks = [
  {
    id: 0,
    label: "Help Center",
    href: "/help",
  },
  {
    id: 1,
    label: "FAQ",
    href: "/faq",
  },
  {
    id: 2,
    label: "How Telex works",
    href: "/how-telex-works",
  },
  {
    label: "Pricing Experience",
    href: "/pricing",
  },
  {
    id: 3,
    label: "About Us",
    href: "/about-us",
  },
  {
    id: 4,
    label: "Contact Us",
    href: "/contact",
  },
  {
    id: 5,
    label: "Release Notes",
    href: "/download/release-notes",
  },
];

const legalLinks = [
  { id: 0, label: "Privacy Policy", href: "/policy" },
  { id: 1, label: "Terms of Service", href: "/terms-of-service" },
];

const Index = () => {
  return (
    <footer className="externalPageFooterBg px-6 py-10 flex flex-col gap-14 text-[#fafafa] sm:px-6 sm:py-12">
      <div className="w-full flex flex-col xl:max-w-[1280px] lg:mx-auto gap-y-[60px]">
        <div className="w-full flex flex-wrap-reverse justify-between gap-y-10 sm:gap-y-20 lg:gap-y-20 lg:flex-nowrap">
          <div className="basis-1/4">
            <Link href={"/"} className="hidden sm:hidden md:block">
              <Image src={externalPageImage} alt="Telex logo" />
            </Link>
          </div>

          <div className="w-full flex flex-col gap-y-10 md:flex-row mx-auto justify-between basis-full md:gap-12">
            <div className="flex flex-col gap-6 w-full">
              <h1 className="text-xl font-semibold">Products</h1>
              <div className="flex flex-col gap-4">
                {productLinks.map((item, index) => {
                  return (
                    <Link
                      key={index}
                      href={item.href}
                      className="hover:underline transition-all"
                    >
                      {item.label}
                    </Link>
                  );
                })}
              </div>
            </div>

            <div className="flex flex-col gap-6 w-full">
              <h1 className="text-xl font-semibold">Marketing</h1>
              <div className="flex flex-col gap-4">
                {marketersLinks.map((item, index) => {
                  return (
                    <Link
                      key={index}
                      href={item.href}
                      className="hover:underline transition-all"
                    >
                      {item.label}
                    </Link>
                  );
                })}
              </div>
            </div>

            <div className="flex flex-col gap-6 w-full">
              <h1 className="text-xl font-semibold">Support</h1>
              <div className="flex flex-col gap-4">
                {supportLinks.map((item, index) => {
                  return (
                    <Link
                      key={index}
                      href={item.href}
                      className="hover:underline transition-all"
                    >
                      {item.label}
                    </Link>
                  );
                })}
              </div>
            </div>

            <div className="flex flex-col gap-6 w-full">
              <h1 className="text-xl font-semibold">Legal</h1>
              <div className="flex flex-col gap-4">
                {legalLinks.map((item, index) => {
                  return (
                    <Link
                      key={index}
                      href={item.href}
                      className="hover:underline transition-all"
                    >
                      {item.label}
                    </Link>
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-3 lg:flex-row lg:justify-between items-center gap-y-5">
          <div className="flex gap-6 items-center">
            <Link
              href="https://www.instagram.com/telex.im/"
              className="hover:underline transition-all"
            >
              <Image src={instagramIcon} alt="instagram icon" />
            </Link>
            <Link
              href="https://x.com/thetelexapp?t=s_0dFqdKYEHm2Ix8hr-9LA&s=09"
              className="hover:underline transition-all"
            >
              <Image src={xIcon} alt="twitter icon" className="w-12" />
            </Link>
            <Link
              href="https://www.facebook.com/share/1CMcPfYvRU/"
              className="hover:underline transition-all"
            >
              <Image src={facebookIcon} alt="facebook icon" />
            </Link>
          </div>
          <div className="flex flex-col md:flex-row md:gap-x-5 justify-center items-center gap-y-5">
            <aside className="flex items-center gap-[6px]">
              <Link
                href="/policy"
                className="text-sm font-medium hover:underline transition-all"
              >
                <p className="text-[14px]">Privacy Policy</p>
              </Link>

              <Link
                href="/terms-of-service"
                className="text-sm font-medium hover:underline transition-all"
              >
                <p className="text-[14px]">Terms of Use</p>
              </Link>
            </aside>
            <span>© 2024 Telex. All Rights Reserved</span>
          </div>
        </div>
      </div>
    </footer>
  );
};
export default Index;
