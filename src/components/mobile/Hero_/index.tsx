import Image from "next/image";
import Link from "next/link";

const Hero_ = ({ title, subtitle }: { title: string; subtitle: string }) => {
  return (
    <div className="bg-[#f9f8fe] pt-8 md:pt-10 px-2 sm:px-10 lg:px-24 container">
      <div className="flex flex-col sm:flex-row justify-between items-center space-y-10">
        <div className="flex items-center sm:items-start flex-col space-y-4 text-center sm:text-start w-[95%] md:w-[70%] mx-auto">
          <h1 className="text-2xl md:text-3xl lg:text-4xl font-semibold">
            {/* <span className="text-[#7141F8]">Telex Version 1.5 is Here:</span>{" "} */}
            {title}
          </h1>
          <p className="text-[#6D7482] font-semibold w-[98%] sm:w-[70%] text-sm sm:text-lg">
            {subtitle}
          </p>
          {/* <button className="px-4 py-3 text-sm font-semibold border border-[#2A2B67] bg-[#2A2B67] text-white hover:bg-white hover:text-[#2A2B67] rounded-md transition-all duration-500">Try Telex Free For 14 Days</button> */}
          <Link href={"/auth/sign-up"}>
            <button className="px-4 py-3 text-sm font-semibold border border-[#2A2B67] bg-[#2A2B67] text-white hover:bg-white hover:text-[#2A2B67] rounded-md transition-all duration-500">
              Get Started with Telex{" "}
            </button>
          </Link>
        </div>

        <div className="w-[95%] md:w-[80%] mx-auto">
          <div
            className="flex items-end justify-center bg-no-repeat w-full bg-cover bg-top px-4 pt-4 rounded-t-xl"
            style={{
              backgroundImage: "url('/images/telex-iphone-hero-bg.svg')",
            }}
          >
            <div className="">
              <Image
                alt=""
                width={350}
                height={800}
                src={"/images/iphone-left.svg"}
              />
            </div>
            <div className="">
              <Image
                className=""
                alt=""
                width={450}
                height={1000}
                src={"/images/iphone-middle.svg"}
              />
            </div>
            {/* <div className="">
                    <Image
                    alt=""
                    width={350}
                    height={800}
                    src={"/images/iphone-right.svg"}
                    />
                </div> */}

            {/* <div className="">
                    <Image
                        className="w-full"
                        alt="Hero Image"
                        width={960}
                        height={300}
                        src={"/images/telex-landing-hero.svg"}
                    />
                </div> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero_;
