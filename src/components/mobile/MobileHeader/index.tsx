"use client";
/* eslint-disable no-unused-vars */
import Image from "next/image";
import Link from "next/link";
import React, { useEffect, useLayoutEffect, useRef, useState } from "react";
import Loading from "~/components/ui/loading";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "~/components/ui/accordion";
import { ChevronDown } from "lucide-react";
import { usePathname } from "next/navigation";
// import ExpandedTechnology from "./expanded-technology";
// import ExpandedProduct from "./expanded-product";

//eslint-disable

const Header: React.FC = () => {
  const [windowWidth, setWindowWidth] = useState(0);
  const [isOpen, setIsOpen] = useState(false);

  const logoWhite = "/logoWhite.png";
  const logoBlack = "/logoBlack.png";

  const pathname = usePathname();

  // Handle window resizing
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  return (
    <>
      {windowWidth < 767 && isOpen ? (
        <div className="bg-black opacity-50 fixed top-0 w-screen h-screen z-50 "></div>
      ) : null}
      <nav
        className={`w-full bg-white z-[1000]"  ${isOpen ? "z-50 fixed top-[50%]" : ""} lg:px-0  `}
      >
        <div
          // onClick={(windowWidth < 767 && isOpen) && toggleMenu}
          className={`w-full mx-auto flex items-center justify-between h-20 px-6 sm:px-6 md:px-6 fixed left-0 top-0 border-b-[1px] md:border-[#F2F4F7] right-0 z-40 transition-colors duration-300 ${
            windowWidth < 767 && isOpen
              ? "bg-[#5A34C6] border-[#5A34C6]"
              : "bg-white border-[#F2F4F7]"
          }`}
        >
          <div className="w-full flex mx-auto justify-between items-center max-w-[1280px] relative group">
            {/* logo-links */}
            {/* <div className="logo-link flex items-center gap-8 z-50 px-0"> */}
            <div className="flex items-center gap-12 z-50 px-0">
              <div className="shrink-0 flex items-center ">
                {windowWidth < 767 && isOpen ? (
                  <Link onClick={toggleMenu} href={"/download"}>
                    <Image
                      className="h-8 w-auto "
                      src={logoWhite}
                      alt="Logo"
                      width={100}
                      height={100}
                    />
                  </Link>
                ) : (
                  <Link href={"/download"}>
                    <Image
                      className="h-8 w-auto"
                      src={logoBlack}
                      alt="Logo"
                      width={100}
                      height={100}
                    />
                  </Link>
                )}
              </div>

              {/* <div className="items-center gap-8 hidden md:flex">
                <Link
                  href={"/download#why-telex"}
                  className={
                    pathname === "/download#why-telex"
                      ? "text-blue-400 font-bold"
                      : "font-bold text-base"
                  }
                //   target="_blank"
                >
                  Why Telex
                </Link>
              </div>

              <div className="items-center gap-8 hidden md:flex">
                <Link
                  href={"/download/release-note"}
                  className={
                    pathname === "/download/release-note"
                      ? "text-blue-400 font-bold"
                      : "font-bold text-base"
                  }
                //   target="_blank"
                >
                  Release Note
                </Link>
              </div> */}
            </div>

            <div className="buttons hidden md:flex">
              <Link
                target="_blank"
                href="https://drive.google.com/drive/folders/1ABE7kMkVWk1q3V3N9RRyhkHc3RTsD_0e?usp=sharing&authuser=1"
                className="text-white inline-flex items-center px-5 py-3 text-sm font-normal bg-[#2a2b67] border border-[#2a2b67] hover:bg-white hover:text-[#2a2b67] transition-all duration-500 rounded-md"
              >
                Download
              </Link>
            </div>

            {/* hamburger */}
            <div
              onClick={toggleMenu}
              className="md:hidden h-6 w-6 relative flex justify-center items-center z-50"
            >
              <div
                className={`w-full h-0.5 bg-current absolute transition-transform duration-300 ease-in-out ${
                  isOpen ? "rotate-45 bg-gray-50" : "-translate-y-1.5"
                }`}
              />
              <div
                className={`w-full h-0.5 bg-current absolute transition-opacity duration-300 ease-in-out ${
                  isOpen ? "opacity-0 " : "opacity-100"
                }`}
              />
              <div
                className={`w-full h-0.5 bg-current absolute transition-transform duration-300 ease-in-out ${
                  isOpen ? "-rotate-45 bg-gray-50" : "translate-y-1.5"
                }`}
              />
            </div>
          </div>
        </div>

        {/* mobile menu */}
        {isOpen && (
          <nav className="h-full md:hidden bg-[#5A34C6] fixed top-0 inset-x-0 pt-20 z-30 overflow-y-auto">
            <div className="w-full flex flex-col">
              <Link
                onClick={toggleMenu}
                target="_blank"
                href="https://drive.usercontent.google.com/download?id=1C1O0HLGaLUTnC752g71xfTkQVPLrzsZq&export=download&authuser=0"
                className=" text-white flex items-center justify-start px-6 py-6 text-sm font-semibold bg-transparent border-t border-b border-white hover:bg-purple-800"
              >
                Download
              </Link>
              <Link
                //   target="_blank"
                onClick={toggleMenu}
                href="/download/release-note"
                className=" text-white flex items-center justify-start px-6 py-6 text-sm font-semibold bg-transparent border-t border-b border-white hover:bg-purple-800"
              >
                Release Note
              </Link>
              <Link
                //   target="_blank"
                onClick={toggleMenu}
                href="/download#why-telex"
                className=" text-white flex items-center justify-start px-6 py-6 text-sm font-semibold bg-transparent border-t border-b border-white hover:bg-purple-800"
              >
                Why Choose Us
              </Link>
            </div>
          </nav>
        )}
      </nav>
    </>
  );
};

export default Header;
