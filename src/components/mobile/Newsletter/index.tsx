"use client";

import React, { useState } from "react";
import { useToast } from "~/components/ui/use-toast";

const Newsletter = () => {
  const [email, setEmail] = useState("");
  const [error, setError] = useState("");
  const { toast } = useToast();

  const validateEmail = (email: string) => {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailPattern.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateEmail(email)) {
      setError("Please enter a valid email address.");
      return;
    }

    setError("");

    const googleFormURL =
      "https://docs.google.com/forms/d/e/1FAIpQLSeeJIsHh0QWPvbOz1x9BTV2pCgXqJRvSU2lEgzZ8MPVVyEZYQ/formResponse";

    const formData = new FormData();
    formData.append("entry.1042907631", email);

    try {
      await fetch(googleFormURL, {
        method: "POST",
        body: formData,
        mode: "no-cors",
      });

      setEmail("");
      toast({
        description: "Subscribed successfully!",
      });
    } catch (error) {
      console.error("Error submitting form:", error);
      setError("Something went wrong. Please try again.");
    }
  };

  return (
    <section className="bg-[#F9F8FE] py-12 px-6">
      <div className="max-w-3xl mx-auto text-center">
        <h2 className="text-2xl sm:text-3xl font-semibold text-gray-800">
          Subscribe to our Newsletter
        </h2>
        <p className="text-gray-600 mt-2">
          Stay updated with the latest news and exclusive offers.
        </p>

        <form
          onSubmit={handleSubmit}
          className="mt-6 flex flex-col sm:flex-row items-center gap-3"
        >
          <div className="w-full">
            <input
              type="email"
              required
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className={`border rounded-md px-4 py-3 w-full focus:border-[#7141f8] ${
                error ? "border-red-500" : "border-gray-300"
              }`}
            />
            {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
          </div>
          <button
            type="submit"
            className="px-4 py-3 text-sm font-semibold border border-[#7141F8] bg-[#7141F8] text-white hover:bg-purple-500 rounded-md transition"
          >
            Subscribe
          </button>
        </form>
      </div>
    </section>
  );
};

export default Newsletter;
