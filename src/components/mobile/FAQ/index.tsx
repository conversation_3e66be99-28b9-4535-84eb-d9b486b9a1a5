import React from "react";
import {
  Accordion,
  AccordionItem,
  Accordion<PERSON>ontent,
  AccordionTrigger,
} from "~/components/ui/accordion";
import Image from "next/image";
import { CircleArrowDown } from "lucide-react";

type FaqItem = {
  question: string;
  answer: string;
};

type FaqProps = {
  faqs: FaqItem[];
};

const Faq: React.FC<FaqProps> = ({ faqs }) => {
  return (
    <div className="mx-auto max-w-[1240px] gap-[40px] flex flex-col md:flex-row justify-center items-center container">
      <div className="w-full md:w-[50%]">
        <div className="hidden md:block rounded-full overflow-hidden mx-auto w-full">
          <Image
            src="/images/faq-image.jpg"
            alt="FAQ Image"
            width={457}
            height={457}
            className="object-cover w-full h-full"
          />
        </div>
      </div>
      <div className="w-full flex items-start justify-center flex-col mx-6 md:w-[50%] sm:w-[500px] lg:w-[676px]">
        <h1 className="text-3xl font-semibold mb-4  w-full lg:w-auto lg:text-start">
          Frequently asked questions
        </h1>
        {/* <p className="mb-8 lg:text-start w-full lg:w-auto">
          Everything you need to know about the product and billing
        </p> */}

        <div className="block mx-auto mb-2 md:hidden lg:hidden ">
          <div className="rounded-full overflow-hidden w-[250px] h-[250px]">
            <Image
              src="/images/faq-image.jpg"
              alt="FAQ Image"
              width={250}
              height={250}
              className="object-cover w-full h-full"
            />
          </div>
        </div>

        <div className="w-full text-left">
          <Accordion type="single" defaultValue="item-1" collapsible>
            <AccordionItem value="item-1">
              <AccordionTrigger className="text-[#1D1D1D] py-8">
                {faqs[0].question}
                <CircleArrowDown className="h-6 w-6 shrink-0 transition-transform duration-200 ml-1" />
              </AccordionTrigger>

              <AccordionContent className="text-[#767676]">
                {faqs[0].answer}
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-2">
              <AccordionTrigger className="text-[#1D1D1D] py-8">
                {faqs[1].question}
                <CircleArrowDown className="h-6 w-6 shrink-0 transition-transform duration-200 ml-1" />
              </AccordionTrigger>

              <AccordionContent className="text-[#767676]">
                {faqs[1].answer}
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-3">
              <AccordionTrigger className="text-[#1D1D1D] py-8">
                {faqs[2].question}
                <CircleArrowDown className="h-6 w-6 shrink-0 transition-transform duration-200 ml-1" />
              </AccordionTrigger>

              <AccordionContent className="text-[#767676]">
                {faqs[2].answer}
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-4">
              <AccordionTrigger className="text-[#1D1D1D] py-8">
                {faqs[3].question}
                <CircleArrowDown className="h-6 w-6 shrink-0 transition-transform duration-200 ml-1" />
              </AccordionTrigger>

              <AccordionContent className="text-[#767676]">
                {faqs[3].answer}
              </AccordionContent>
            </AccordionItem>

            {/* <AccordionItem value="item-5">
              <AccordionTrigger className="text-[#1D1D1D] py-8">
                Can I customize channels and notifications?
                <CircleArrowDown className="h-6 w-6 shrink-0 transition-transform duration-200 ml-1" />
              </AccordionTrigger>

              <AccordionContent className="text-[#767676]">
                Yes! you can set notification preferences and manage your
                channels settings.
              </AccordionContent>
            </AccordionItem> */}
          </Accordion>
        </div>
      </div>
    </div>
  );
};

export default Faq;
