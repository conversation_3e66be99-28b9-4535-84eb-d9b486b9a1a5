import Image from "next/image";
import Link from "next/link";

const Agent = ({
  title,
  subtitle,
  about,
}: {
  title: string;
  subtitle: string;
  about: string;
}) => {
  return (
    <div className="">
      <div className="flex items-center flex-col text-center w-[95%] md:w-[70%] lg:w-[60%] mx-auto mb-8">
        <h1 className="text-2xl md:text-3xl lg:text-4xl font-semibold">
          {about}
        </h1>
      </div>

      <div className="px-2 sm:px-10 lg:px-24 mb-12">
        <div className="rounded-md bg-[#303073] flex flex-col items-center justify-between sm:flex-row text-white anotherContainer py-4 ">
          <div className="sm:w-1/2 w-full p-4">
            <h1 className="text-2xl md:text-3xl lg:text-5xl font-semibold w-full">
              {title}
            </h1>
            <p className="my-6">{subtitle}</p>
            <div className="flex">
              <Link
                target="_blank"
                // href="https://drive.google.com/drive/folders/1ABE7kMkVWk1q3V3N9RRyhkHc3RTsD_0e?usp=sharing&authuser=1"
                href="/auth/sign-up"
                className="inline-flex items-center px-4 py-3 text-sm font-semibold border border-[#7141F8] bg-white text-[#7141F8] hover:bg-[#7141F8] hover:text-white rounded-md transition-all duration-500"
              >
                Get Started with Telex
              </Link>
            </div>
          </div>
          <div
            className="w-full sm:w-2/5 flex items-center justify-center bg-center bg-contain bg-no-repeat"
            style={{ backgroundImage: "url('/images/device-background.png')" }}
          >
            <Image
              src="/images/iphone-login.svg"
              alt="Iphone 13"
              className=""
              width={360}
              height={450}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Agent;
