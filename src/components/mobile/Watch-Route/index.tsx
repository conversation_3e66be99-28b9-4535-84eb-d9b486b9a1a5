const WatchDemo = ({
  video_url,
  text,
}: {
  video_url: string;
  text: string;
}) => {
  return (
    <div className="flex flex-col items-center justify-center text-center py-20 w-full">
      <div className="flex flex-col items-center justify-center gap-4 pb-6 container">
        <h1 className="text-2xl md:text-4xl text-[#090F1C] font-semibold mb-4 ">
          How Telex Works
        </h1>
        <p className="text-[#6D7482] font-semibold w-[90%] sm:w-[70%] text-sm sm:text-lg">
          {text}
        </p>
      </div>
      <div className="w-full max-w-[1240px] h-auto rounded-lg">
        <video
          //   src="/telex-advert.mov"
          src={video_url}
          controls
          width="100%"
          height="auto"
          loop
          className="w-full h-auto"
        >
          Your browser does not support the video tag
        </video>
      </div>
    </div>
  );
};
export default WatchDemo;
