"use client";
import Image from "next/image";
import Link from "next/link";

import React, { useState } from "react";
import { useToast } from "~/components/ui/use-toast";

const SubFooter = () => {
  const [email, setEmail] = useState("");
  const [error, setError] = useState("");
  const { toast } = useToast();

  const validateEmail = (email: string) => {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailPattern.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateEmail(email)) {
      setError("Please enter a valid email address.");
      return;
    }

    setError("");

    const googleFormURL =
      "https://docs.google.com/forms/d/e/1FAIpQLSeeJIsHh0QWPvbOz1x9BTV2pCgXqJRvSU2lEgzZ8MPVVyEZYQ/formResponse";

    const formData = new FormData();
    formData.append("entry.1042907631", email);

    try {
      await fetch(googleFormURL, {
        method: "POST",
        body: formData,
        mode: "no-cors",
      });

      setEmail("");
      toast({
        description: "Subscribed successfully!",
      });
    } catch (error) {
      console.error("Error submitting form:", error);
      setError("Something went wrong. Please try again.");
    }
  };

  return (
    <div className="px-2 sm:px-10 lg:px-24 bg-[#F9F8FE] border-[#C1C6CE] border-t container">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 md:gap-4 py-8 items-center justify-between">
        <div className="flex flex-col items-start space-y-4">
          <Image
            src={"/logoBlack.png"}
            width={100}
            height={100}
            alt="logo-black"
          />
          <div className="flex items-center gap-2">
            <Link
              href={"https://www.facebook.com/share/18X2BjiZ2x/"}
              target="_blank"
            >
              {" "}
              <Image
                src={"/images/facebook.svg"}
                className="w-5 md:w-6"
                width={30}
                height={30}
                alt="facebook logo"
              />
            </Link>
            <Link href={"https://x.com/MobileTelex"} target="_blank">
              {" "}
              <Image
                src={"/images/twitter.svg"}
                className="w-5 md:w-6"
                width={30}
                height={30}
                alt="X logo"
              />
            </Link>
            <Link
              href={"https://www.instagram.com/telexmobileapp"}
              target="_blank"
            >
              {" "}
              <Image
                src={"/images/instagram.svg"}
                className="w-5 md:w-6"
                width={30}
                height={30}
                alt="idk logo"
              />
            </Link>
            <Link
              href={
                "https://www.linkedin.com/in/telex-mobile-05b996355?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app"
              }
              target="_blank"
            >
              {" "}
              <Image
                src={"/images/linkedin.svg"}
                className="w-5 md:w-6"
                width={30}
                height={30}
                alt="linkedin logo"
              />
            </Link>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-6 font-semibold text-xs md:text-sm">
          <Link
            href={"#choose-us"}
            className="hover:text-[#303073] duration-300 transition-all ease-in-out"
          >
            Why Choose Us
          </Link>
          <Link
            href={"/"}
            className="hover:text-[#303073] duration-300 transition-all ease-in-out"
          >
            Release Notes
          </Link>
        </div>

        <div className="space-y-3">
          <h3 className="font-semibold text-base md:text-lg">
            Subscribe To Newsletter
          </h3>
          {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
          <div className="border border-[#C1C6CE] w-[300px] h-[45px] rounded-lg flex">
            <input
              type="text"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter email address "
              className={`w-[85%] px-4 outline-none border rounded-l-lg ${error ? "border-red-500" : ""}`}
            />
            <button
              onClick={handleSubmit}
              className="flex items-center justify-center flex-1 font-bold text-lg bg-[#303073] text-white rounded-r-lg border border-[#303073] hover:text-[#303073] hover:bg-white transition-all duration-300"
            >
              {">"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubFooter;
