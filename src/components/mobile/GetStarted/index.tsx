"use client";

import React, { useState } from "react";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const GetStarted = ({ subtext }: { subtext: string }) => {
  const [formData, setFormData] = useState({
    fullName: "",
    profession: "",
    phoneNumber: "",
    email: "",
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const { fullName, profession, phoneNumber, email } = formData;

    if (!fullName || !profession || !phoneNumber || !email) {
      toast.error("Please fill in all fields!", {
        position: "top-right",
        autoClose: 2000,
      });
      return;
    }

    toast.success("Form submitted successfully!", {
      position: "top-right",
      autoClose: 2000,
    });

    // Reset the form after submission (optional)
    setFormData({
      fullName: "",
      profession: "",
      phoneNumber: "",
      email: "",
    });
  };

  return (
    <div className="space-y-10 bg-[#f9f8fe] m-auto sm:min-w-[600px] w-full px-4">
      <ToastContainer />
      <div className="flex items-center flex-col space-y-4 text-center w-[95%] md:w-[70%] mx-auto">
        <h1 className="text-2xl md:text-3xl lg:text-4xl font-semibold">
          Get Started with Telex
        </h1>
        <p className="text-[#6D7482] font-semibold w-[70%] text-sm sm:text-lg">
          {subtext}
        </p>
      </div>

      <div className="w-full sm:w-[70%] bg-white m-auto p-8 md:p-16 rounded-lg container">
        <form onSubmit={handleSubmit} className="w-full space-y-10">
          <div className="space-y-10 w-full">
            <div className="space-y-2 w-full">
              <label htmlFor="fullName" className="font-semibold">
                Full Name
              </label>
              <input
                type="text"
                name="fullName"
                id="fullName"
                placeholder="Your Name"
                className="w-full border border-[#E0E3E7] py-3 px-4 rounded-md outline-none"
                value={formData.fullName}
                onChange={handleChange}
              />
            </div>
            <div className="flex flex-col space-y-2">
              <label htmlFor="profession" className="font-semibold">
                Profession
              </label>
              <input
                type="text"
                name="profession"
                id="profession"
                placeholder="e.g Designer, Filmmaker, etc."
                className="w-full border border-[#E0E3E7] py-3 px-4 rounded-md outline-none"
                value={formData.profession}
                onChange={handleChange}
              />
            </div>
            <div className="flex flex-col space-y-2">
              <label htmlFor="phoneNumber" className="font-semibold">
                Phone Number
              </label>
              <input
                type="number"
                name="phoneNumber"
                id="phoneNumber"
                placeholder="Your Phone Number"
                className="w-full border border-[#E0E3E7] py-3 px-4 rounded-md outline-none"
                value={formData.phoneNumber}
                onChange={handleChange}
              />
            </div>
            <div className="flex flex-col space-y-2">
              <label htmlFor="email" className="font-semibold">
                Email
              </label>
              <input
                type="email"
                name="email"
                id="email"
                placeholder="<EMAIL>"
                className="w-full border border-[#E0E3E7] py-3 px-4 rounded-md outline-none"
                value={formData.email}
                onChange={handleChange}
              />
            </div>
            <button
              type="submit"
              className="border-[#2A2B67] mt-8 bg-[#2A2B67] text-white hover:bg-white hover:text-[#2A2B67] w-full border py-3 px-4 rounded-md outline-none transition-all duration-500 font-semibold"
            >
              Get Demo
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default GetStarted;
