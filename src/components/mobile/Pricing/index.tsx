import React from "react";
import { Check, X } from "lucide-react";
import Link from "next/link";
import { Button } from "~/components/ui/button";
import { mobilePricingData } from "~/data/data";

const Pricing = () => {
  return (
    <div className="w-full flex flex-col sm:flex-row sm:flex-wrap sm:justify-center lg:flex-nowrap justify-between gap-6 mt-6">
      {mobilePricingData.map((data, index) => (
        <div
          key={index}
          className="px-[31px] py-[32px] rounded-xl border border-[#CBD5E1] hover:border-purple-400 transition-colors space-y-[52px] flex flex-col lg:w-1/3"
        >
          <div className="flex flex-col gap-4">
            <h3 className="font-bold text-2xl">{data.title}</h3>
            <div className="flex items-end gap-1 leading-[46.5px]">
              <h1 className="text-[40px] font-bold text-[#0A0A0A]">
                {data.title !== "Free"
                  ? `$${data.amount}/month`
                  : `$${data.amount}`}
              </h1>
            </div>
            <p className="text-sm text-[#0A0A0A] w-[244px]">{data.subtitle}</p>
            <h4>{data.subheading}</h4>
          </div>

          <div className="flex flex-col gap-6">
            {data.features.map((feature, i) => (
              <div className="flex gap-2" key={i}>
                {feature.enabled ? (
                  <div className="w-[25px] h-[25px] rounded-full border border-black flex items-center justify-center">
                    <Check size={20} className="text-black" />
                  </div>
                ) : (
                  <X size={20} className="text-red-500" />
                )}
                <p
                  className={`text-sm ${
                    feature.enabled
                      ? "text-black"
                      : "text-gray-300 line-through"
                  }`}
                >
                  {feature.text}
                </p>
              </div>
            ))}
          </div>

          <Link href={data.url} passHref className="w-full">
            <Button className="bg-transparent border border-[#7141f8] text-[#7141f8] hover:bg-gradient-to-b from-[#8760f8] to-[#7141f8] hover:text-white font-medium w-full py-4">
              {data.title !== "Free" ? "Get Started" : "Get Started for Free"}
            </Button>
          </Link>
        </div>
      ))}
    </div>
  );
};

export default Pricing;
