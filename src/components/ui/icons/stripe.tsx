import { SVGProps } from "react";
const Stripe = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={85}
    height={40}
    viewBox="0 0 85 40"
    fill="none"
    {...props}
  >
    <path
      fill="#0A2540"
      fillRule="evenodd"
      d="M74.791 20.386c0-4.602-2.229-8.234-6.49-8.234-4.278 0-6.867 3.632-6.867 8.198 0 5.411 3.056 8.144 7.443 8.144 2.14 0 3.757-.485 4.98-1.169V23.73c-1.223.611-2.625.989-4.405.989-1.744 0-3.29-.612-3.488-2.733h8.791c0-.234.036-1.169.036-1.6Zm-8.88-1.708c0-2.031 1.24-2.876 2.373-2.876 1.096 0 2.265.845 2.265 2.876H65.91ZM54.495 12.152c-1.762 0-2.894.827-3.523 1.403l-.234-1.115h-3.955v20.962l4.494-.953.018-5.088c.647.468 1.6 1.133 3.182 1.133 3.218 0 6.148-2.589 6.148-8.288-.018-5.213-2.984-8.054-6.13-8.054ZM53.416 24.54c-1.06 0-1.69-.378-2.12-.845l-.019-6.67c.468-.521 1.115-.88 2.14-.88 1.635 0 2.768 1.833 2.768 4.188 0 2.41-1.115 4.207-2.769 4.207ZM40.599 11.09l4.512-.97V6.47l-4.512.954v3.667Z"
      clipRule="evenodd"
    />
    <path fill="#0A2540" d="M45.111 12.457h-4.512v15.73h4.512v-15.73Z" />
    <path
      fill="#0A2540"
      fillRule="evenodd"
      d="m35.763 13.788-.288-1.33h-3.883v15.73h4.495V17.527c1.06-1.384 2.858-1.133 3.415-.935v-4.135c-.575-.215-2.678-.61-3.739 1.33ZM26.774 8.555l-4.387.935-.018 14.4c0 2.66 1.996 4.62 4.656 4.62 1.475 0 2.553-.27 3.146-.594v-3.649c-.575.234-3.415 1.06-3.415-1.6v-6.382h3.415v-3.83h-3.415l.018-3.9ZM14.621 17.024c0-.7.575-.97 1.528-.97 1.366 0 3.092.413 4.459 1.15v-4.225c-1.493-.593-2.967-.827-4.459-.827-3.65 0-6.076 1.906-6.076 5.088 0 4.962 6.831 4.17 6.831 6.31 0 .827-.719 1.097-1.726 1.097-1.492 0-3.397-.611-4.908-1.438v4.278a12.462 12.462 0 0 0 4.908 1.025c3.74 0 6.31-1.852 6.31-5.07-.017-5.357-6.867-4.404-6.867-6.418Z"
      clipRule="evenodd"
    />
  </svg>
);
export default Stripe;
