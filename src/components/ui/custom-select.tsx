import React from "react";
import Select from "react-select";

const CustomSelect = ({
  options,
  placeholder,
  onChange,
  defaultValue,
  isDisabled,
}: any) => {
  //

  return (
    <Select
      value={defaultValue}
      options={options}
      placeholder={placeholder}
      onChange={onChange}
      isSearchable={true}
      className="basic-select border rounded-lg focus:outline-none focus:border-blue-400"
      isDisabled={isDisabled}
      styles={{
        control: (baseStyles, state) => {
          return {
            ...baseStyles,
            border: "none",
            padding: "10px 8px",
            borderRadius: "5px",
            boxShadow: state.isFocused
              ? "0px 0px 0px 1px rgb(57, 57, 136)"
              : "none",
            cursor: "pointer",
            // fontSize: "16px",
          };
        },
      }}
      theme={(theme) => ({
        ...theme,
        borderRadius: 0,
        colors: {
          ...theme.colors,
          primary25: "#fafafa",
          primary50: "#fafafa",
          primary: "blue",
        },
      })}
    />
  );
};

export default CustomSelect;
