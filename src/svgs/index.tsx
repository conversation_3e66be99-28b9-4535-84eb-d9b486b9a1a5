import React from "react";

export const PlusIcon = () => {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="32"
        height="32"
        rx="3.42667"
        fill="url(#paint0_linear_14875_48768)"
        fill-opacity="0.15"
      />
      <path
        d="M16 10.6668V21.3335"
        stroke="#1E1E1E"
        stroke-width="1.66667"
        stroke-linecap="round"
      />
      <path
        d="M10.668 16.0002H21.3346"
        stroke="#1E1E1E"
        stroke-width="1.66667"
        stroke-linecap="round"
      />
      <defs>
        <linearGradient
          id="paint0_linear_14875_48768"
          x1="0"
          y1="0"
          x2="32"
          y2="32"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-opacity="0.5" />
          <stop offset="1" stop-opacity="0.2" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const IntegrationIcon = () => {
  return (
    <svg
      width="60"
      height="60"
      viewBox="0 0 60 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_d_27201_11868)">
        <rect x="4" y="2" width="52" height="52" rx="26" fill="#303030" />
        <rect
          x="10.5"
          y="8.5"
          width="39"
          height="39"
          rx="19.5"
          fill="url(#paint0_linear_27201_11868)"
        />
        <rect
          x="10.5"
          y="8.5"
          width="39"
          height="39"
          rx="19.5"
          stroke="url(#paint1_linear_27201_11868)"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M35.4436 16.3724C34.8283 15.3359 33.676 14.4674 32.3526 13.8776C31.0229 13.285 29.473 12.952 28.0131 13.0264L28.0543 13.8343C29.3716 13.7672 30.7951 14.0691 32.0233 14.6165C33.2578 15.1667 34.248 15.9432 34.7481 16.7853L35.4436 16.3724ZM33.6625 17.584C33.1914 16.7904 32.3161 16.1346 31.3235 15.6922C30.3245 15.247 29.1591 14.9962 28.0583 15.0522L28.0995 15.8601C29.0577 15.8113 30.0967 16.0312 30.9942 16.4311C31.8979 16.8339 32.6111 17.3977 32.9669 17.9969L33.6625 17.584ZM39.8131 24.6895L37.1001 23.6012C36.5046 23.3667 35.6353 22.7043 35.2492 22.1944L33.7447 20.2076C30.8244 16.3509 25.3169 15.59 21.4708 18.5023C17.6221 21.4333 16.8532 26.9302 19.7735 30.7869L21.2699 32.763C21.6561 33.273 22.0579 34.2893 22.1328 34.918L22.4447 37.8245C22.5751 38.946 23.1366 39.9083 24.0027 40.4561C24.8769 41.0144 25.9916 41.1399 27.0507 40.7725C28.4878 40.2861 29.855 39.6855 31.1872 38.9943C31.2536 38.9607 31.3181 38.9245 31.3825 38.8882L31.3826 38.8881L31.3827 38.8881C31.4471 38.8518 31.5116 38.8156 31.578 38.782C31.7425 38.6907 31.9177 38.5915 32.0928 38.4923L32.0931 38.4921L32.0938 38.4917C32.4022 38.325 32.7025 38.1478 33.0055 37.9518C33.7231 37.5087 34.4272 37.0257 35.1072 36.5108C35.7765 36.004 36.4217 35.4652 37.0322 34.9027C37.2605 34.6964 37.4887 34.4901 37.6983 34.2813L37.6985 34.2811L37.6985 34.2811L37.6986 34.281L37.6986 34.281C37.8737 34.115 38.0487 33.949 38.2157 33.7725C38.3245 33.6734 38.4253 33.5636 38.526 33.4539L38.5261 33.4538C39.5713 32.3616 40.5253 31.1711 41.3831 29.9197C41.992 29.024 42.1523 27.9331 41.8544 26.9217C41.5485 25.8996 40.8038 25.0927 39.8131 24.6895ZM24.9477 28.1646L29.1444 21.8925C29.2965 21.6653 29.6506 21.8138 29.595 22.0815L28.7845 25.9858C28.7524 26.1403 28.8704 26.2853 29.0282 26.2853H31.4066C31.6121 26.2853 31.7291 26.5203 31.6051 26.6843L26.7036 33.1672C26.5399 33.3837 26.1972 33.2192 26.2638 32.9561L27.2993 28.8618C27.3391 28.7046 27.2202 28.5519 27.058 28.5519H25.1546C24.9556 28.5519 24.8371 28.3299 24.9477 28.1646ZM30.633 42.7273C31.966 42.1218 32.6467 40.5977 32.4381 38.9946C32.2748 39.098 32.1217 39.1968 31.9687 39.2956C31.8414 39.3831 31.7043 39.4616 31.5659 39.541C31.4993 39.5792 31.4324 39.6175 31.366 39.6572C30.8193 39.9787 30.2514 40.2806 29.6669 40.5461C29.0722 40.8162 28.4609 41.05 27.8489 41.2548C27.5918 41.3423 27.3291 41.4177 27.0768 41.4884C27.0041 41.5077 26.9349 41.5286 26.8671 41.5491L26.867 41.5491C26.7905 41.5722 26.7155 41.5949 26.639 41.6141C27.0086 42.0464 27.4751 42.4199 27.9686 42.6349C28.8468 43.0703 29.8229 43.0953 30.633 42.7273Z"
          fill="white"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_27201_11868"
          x="0"
          y="0"
          width="60"
          height="60"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="2" />
          <feGaussianBlur stdDeviation="2" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.1 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_27201_11868"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_27201_11868"
            result="shape"
          />
        </filter>
        <linearGradient
          id="paint0_linear_27201_11868"
          x1="30"
          y1="8"
          x2="30"
          y2="48"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#8860F8" />
          <stop offset="1" stop-color="#7141F8" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_27201_11868"
          x1="30"
          y1="8"
          x2="30"
          y2="48"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#8860F8" />
          <stop offset="1" stop-color="#7141F8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export const SidebarPlusIcon = () => {
  return (
    <svg
      width="26"
      height="26"
      viewBox="0 0 26 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M22.75 13C22.75 13.2155 22.6644 13.4222 22.512 13.5745C22.3597 13.7269 22.153 13.8125 21.9375 13.8125H13.8125V21.9375C13.8125 22.153 13.7269 22.3597 13.5745 22.512C13.4222 22.6644 13.2155 22.75 13 22.75C12.7845 22.75 12.5778 22.6644 12.4255 22.512C12.2731 22.3597 12.1875 22.153 12.1875 21.9375V13.8125H4.0625C3.84701 13.8125 3.64035 13.7269 3.48798 13.5745C3.3356 13.4222 3.25 13.2155 3.25 13C3.25 12.7845 3.3356 12.5778 3.48798 12.4255C3.64035 12.2731 3.84701 12.1875 4.0625 12.1875H12.1875V4.0625C12.1875 3.84701 12.2731 3.64035 12.4255 3.48798C12.5778 3.3356 12.7845 3.25 13 3.25C13.2155 3.25 13.4222 3.3356 13.5745 3.48798C13.7269 3.64035 13.8125 3.84701 13.8125 4.0625V12.1875H21.9375C22.153 12.1875 22.3597 12.2731 22.512 12.4255C22.6644 12.5778 22.75 12.7845 22.75 13Z"
        fill="white"
      />
    </svg>
  );
};

export const BellIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.125 17.5C13.125 17.6658 13.0592 17.8247 12.942 17.9419C12.8248 18.0592 12.6658 18.125 12.5 18.125H7.50002C7.33426 18.125 7.17529 18.0592 7.05808 17.9419C6.94087 17.8247 6.87502 17.6658 6.87502 17.5C6.87502 17.3342 6.94087 17.1753 7.05808 17.0581C7.17529 16.9408 7.33426 16.875 7.50002 16.875H12.5C12.6658 16.875 12.8248 16.9408 12.942 17.0581C13.0592 17.1753 13.125 17.3342 13.125 17.5ZM17.3321 15C17.2236 15.191 17.0662 15.3497 16.876 15.4595C16.6858 15.5694 16.4697 15.6265 16.25 15.625H3.75002C3.53029 15.6247 3.31451 15.5665 3.12444 15.4562C2.93437 15.346 2.77673 15.1875 2.6674 14.9969C2.55808 14.8063 2.50093 14.5903 2.50172 14.3705C2.50251 14.1508 2.5612 13.9351 2.6719 13.7453C3.10549 12.9984 3.75002 10.8859 3.75002 8.125C3.75002 6.4674 4.4085 4.87769 5.58061 3.70558C6.75271 2.53348 8.34242 1.875 10 1.875C11.6576 1.875 13.2473 2.53348 14.4194 3.70558C15.5915 4.87769 16.25 6.4674 16.25 8.125C16.25 10.8852 16.8953 12.9984 17.3289 13.7453C17.4407 13.9354 17.4999 14.1518 17.5003 14.3723C17.5007 14.5929 17.4424 14.8095 17.3313 15H17.3321ZM16.25 14.375C15.6461 13.3383 15 10.9414 15 8.125C15 6.79892 14.4732 5.52715 13.5356 4.58947C12.5979 3.65178 11.3261 3.125 10 3.125C8.67394 3.125 7.40217 3.65178 6.46449 4.58947C5.52681 5.52715 5.00002 6.79892 5.00002 8.125C5.00002 10.9422 4.35315 13.3391 3.75002 14.375H16.25Z"
        fill="white"
      />
    </svg>
  );
};

export const GearIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10 6.24998C9.25836 6.24998 8.53333 6.46991 7.91665 6.88196C7.29996 7.29402 6.81932 7.87969 6.53549 8.56491C6.25166 9.25014 6.1774 10.0041 6.32209 10.7316C6.46679 11.459 6.82394 12.1272 7.34839 12.6516C7.87283 13.1761 8.54102 13.5332 9.26845 13.6779C9.99588 13.8226 10.7499 13.7484 11.4351 13.4645C12.1203 13.1807 12.706 12.7 13.118 12.0834C13.5301 11.4667 13.75 10.7417 13.75 9.99998C13.749 9.00573 13.3536 8.0525 12.6505 7.34947C11.9475 6.64643 10.9943 6.25101 10 6.24998ZM10 12.5C9.50558 12.5 9.02223 12.3534 8.61111 12.0786C8.19999 11.8039 7.87956 11.4135 7.69034 10.9567C7.50112 10.4999 7.45161 9.9972 7.54807 9.51225C7.64454 9.0273 7.88264 8.58184 8.23227 8.23221C8.5819 7.88258 9.02736 7.64447 9.51231 7.54801C9.99726 7.45155 10.4999 7.50106 10.9567 7.69028C11.4136 7.8795 11.804 8.19993 12.0787 8.61105C12.3534 9.02217 12.5 9.50552 12.5 9.99998C12.5 10.663 12.2366 11.2989 11.7678 11.7677C11.299 12.2366 10.6631 12.5 10 12.5ZM16.875 10.1687C16.8782 10.0562 16.8782 9.94373 16.875 9.83123L18.0407 8.37498C18.1018 8.29852 18.1441 8.20877 18.1642 8.11297C18.1843 8.01716 18.1816 7.91798 18.1563 7.82341C17.9652 7.10513 17.6794 6.41548 17.3063 5.77263C17.2574 5.6885 17.1896 5.61694 17.1082 5.56364C17.0269 5.51034 16.9342 5.47677 16.8375 5.4656L14.9844 5.25935C14.9073 5.1781 14.8292 5.09998 14.75 5.02498L14.5313 3.16716C14.52 3.07045 14.4863 2.97771 14.4329 2.89633C14.3795 2.81494 14.3077 2.74717 14.2235 2.69841C13.5804 2.32603 12.8908 2.04047 12.1727 1.84919C12.0781 1.82403 11.9788 1.82146 11.883 1.84168C11.7872 1.8619 11.6975 1.90435 11.6211 1.9656L10.1688 3.12497C10.0563 3.12497 9.94379 3.12497 9.83129 3.12497L8.37504 1.96169C8.29858 1.90057 8.20883 1.85827 8.11303 1.83819C8.01722 1.8181 7.91804 1.8208 7.82347 1.84607C7.10531 2.0375 6.4157 2.32332 5.77269 2.69607C5.68857 2.74492 5.617 2.81273 5.5637 2.89411C5.5104 2.97548 5.47683 3.06818 5.46566 3.16482L5.25941 5.02107C5.17816 5.09867 5.10004 5.1768 5.02504 5.25544L3.16722 5.46872C3.07051 5.47998 2.97777 5.51367 2.89639 5.56711C2.815 5.62055 2.74723 5.69227 2.69847 5.77654C2.32609 6.41963 2.04053 7.10923 1.84925 7.82732C1.8241 7.92195 1.82152 8.02116 1.84174 8.11697C1.86196 8.21277 1.90441 8.30249 1.96566 8.37888L3.12504 9.83123C3.12504 9.94373 3.12504 10.0562 3.12504 10.1687L1.96175 11.625C1.90064 11.7014 1.85833 11.7912 1.83825 11.887C1.81816 11.9828 1.82086 12.082 1.84613 12.1765C2.03721 12.8948 2.32306 13.5845 2.69613 14.2273C2.74498 14.3114 2.81279 14.383 2.89417 14.4363C2.97555 14.4896 3.06824 14.5232 3.16488 14.5344L5.01801 14.7406C5.09561 14.8219 5.17373 14.9 5.25238 14.975L5.46879 16.8328C5.48004 16.9295 5.51373 17.0222 5.56717 17.1036C5.62061 17.185 5.69233 17.2528 5.7766 17.3015C6.4197 17.6739 7.10929 17.9595 7.82738 18.1508C7.92201 18.1759 8.02122 18.1785 8.11703 18.1583C8.21284 18.1381 8.30255 18.0956 8.37894 18.0344L9.83129 16.875C9.94379 16.8781 10.0563 16.8781 10.1688 16.875L11.625 18.0406C11.7015 18.1017 11.7912 18.144 11.887 18.1641C11.9828 18.1842 12.082 18.1815 12.1766 18.1562C12.8949 17.9651 13.5845 17.6793 14.2274 17.3062C14.3115 17.2574 14.3831 17.1896 14.4364 17.1082C14.4897 17.0268 14.5232 16.9341 14.5344 16.8375L14.7407 14.9844C14.8219 14.9073 14.9 14.8291 14.975 14.75L16.8329 14.5312C16.9296 14.52 17.0223 14.4863 17.1037 14.4328C17.1851 14.3794 17.2528 14.3077 17.3016 14.2234C17.674 13.5803 17.9595 12.8907 18.1508 12.1726C18.176 12.078 18.1786 11.9788 18.1583 11.883C18.1381 11.7872 18.0957 11.6975 18.0344 11.6211L16.875 10.1687ZM15.6172 9.66091C15.6305 9.88676 15.6305 10.1132 15.6172 10.339C15.6079 10.4937 15.6564 10.6462 15.7532 10.7672L16.8618 12.1523C16.7345 12.5566 16.5717 12.9488 16.375 13.3242L14.6094 13.5242C14.4557 13.5413 14.3137 13.6148 14.211 13.7304C14.0606 13.8996 13.9004 14.0597 13.7313 14.2101C13.6156 14.3128 13.5421 14.4548 13.525 14.6086L13.3289 16.3726C12.9536 16.5694 12.5614 16.7322 12.1571 16.8594L10.7711 15.7508C10.6602 15.6621 10.5225 15.6139 10.3805 15.614H10.343C10.1172 15.6273 9.89073 15.6273 9.66488 15.614C9.51026 15.6047 9.35769 15.6532 9.23676 15.75L7.84769 16.8594C7.44342 16.7321 7.05125 16.5693 6.67582 16.3726L6.47582 14.6094C6.45875 14.4556 6.38526 14.3136 6.26957 14.2109C6.10043 14.0605 5.94026 13.9004 5.78988 13.7312C5.68717 13.6155 5.5452 13.542 5.39144 13.525L3.62738 13.3281C3.43065 12.9527 3.26777 12.5605 3.14066 12.1562L4.24925 10.7703C4.34606 10.6494 4.3945 10.4968 4.38519 10.3422C4.37191 10.1163 4.37191 9.88988 4.38519 9.66404C4.3945 9.50941 4.34606 9.35684 4.24925 9.23591L3.14066 7.84763C3.26787 7.44336 3.43075 7.05119 3.62738 6.67576L5.39066 6.47576C5.54442 6.45869 5.68639 6.3852 5.7891 6.26951C5.93948 6.10037 6.09965 5.9402 6.26879 5.78982C6.38494 5.68704 6.45873 5.54475 6.47582 5.3906L6.67191 3.62732C7.0473 3.43059 7.43948 3.26771 7.84379 3.1406L9.22972 4.24919C9.35066 4.34599 9.50323 4.39444 9.65785 4.38513C9.8837 4.37185 10.1101 4.37185 10.336 4.38513C10.4906 4.39444 10.6432 4.34599 10.7641 4.24919L12.1524 3.1406C12.5567 3.26781 12.9488 3.43069 13.3243 3.62732L13.5243 5.3906C13.5413 5.54436 13.6148 5.68633 13.7305 5.78904C13.8996 5.93942 14.0598 6.09959 14.2102 6.26872C14.3129 6.38442 14.4549 6.45791 14.6086 6.47498L16.3727 6.67107C16.5694 7.04646 16.7323 7.43864 16.8594 7.84294L15.7508 9.22888C15.6531 9.35083 15.6046 9.50497 15.6149 9.66091H15.6172Z"
        fill="white"
      />
    </svg>
  );
};

export const TopbarArrowLeftIcon = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.75 8.99995C15.75 9.14913 15.6907 9.29221 15.5852 9.39769C15.4797 9.50318 15.3366 9.56245 15.1875 9.56245H4.17019L8.27292 13.6645C8.32519 13.7167 8.36664 13.7788 8.39493 13.8471C8.42321 13.9154 8.43777 13.9885 8.43777 14.0624C8.43777 14.1364 8.42321 14.2095 8.39493 14.2778C8.36664 14.3461 8.32519 14.4082 8.27292 14.4604C8.22066 14.5127 8.15862 14.5541 8.09033 14.5824C8.02205 14.6107 7.94886 14.6253 7.87495 14.6253C7.80104 14.6253 7.72786 14.6107 7.65957 14.5824C7.59129 14.5541 7.52925 14.5127 7.47699 14.4604L2.41449 9.39792C2.36219 9.34567 2.3207 9.28364 2.29239 9.21535C2.26408 9.14706 2.24951 9.07387 2.24951 8.99995C2.24951 8.92603 2.26408 8.85283 2.29239 8.78454C2.3207 8.71626 2.36219 8.65422 2.41449 8.60198L7.47699 3.53948C7.58253 3.43393 7.72569 3.37463 7.87495 3.37463C8.02422 3.37463 8.16738 3.43393 8.27292 3.53948C8.37847 3.64503 8.43777 3.78818 8.43777 3.93745C8.43777 4.08671 8.37847 4.22987 8.27292 4.33542L4.17019 8.43745H15.1875C15.3366 8.43745 15.4797 8.49671 15.5852 8.6022C15.6907 8.70769 15.75 8.85076 15.75 8.99995Z"
        fill="#5F5FE1"
      />
    </svg>
  );
};

export const TopbarArrowRightIcon = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.5855 9.39792L10.523 14.4604C10.4174 14.566 10.2743 14.6253 10.125 14.6253C9.97573 14.6253 9.83258 14.566 9.72703 14.4604C9.62148 14.3549 9.56219 14.2117 9.56219 14.0624C9.56219 13.9132 9.62148 13.77 9.72703 13.6645L13.8298 9.56245H2.8125C2.66332 9.56245 2.52024 9.50318 2.41475 9.39769C2.30926 9.29221 2.25 9.14913 2.25 8.99995C2.25 8.85076 2.30926 8.70769 2.41475 8.6022C2.52024 8.49671 2.66332 8.43745 2.8125 8.43745H13.8298L9.72703 4.33542C9.62148 4.22987 9.56219 4.08671 9.56219 3.93745C9.56219 3.78818 9.62148 3.64503 9.72703 3.53948C9.83258 3.43393 9.97573 3.37463 10.125 3.37463C10.2743 3.37463 10.4174 3.43393 10.523 3.53948L15.5855 8.60198C15.6378 8.65422 15.6793 8.71626 15.7076 8.78454C15.7359 8.85283 15.7504 8.92603 15.7504 8.99995C15.7504 9.07387 15.7359 9.14706 15.7076 9.21535C15.6793 9.28364 15.6378 9.34567 15.5855 9.39792Z"
        fill="#5F5FE1"
      />
    </svg>
  );
};

export const TopbarFrameIcon = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.0625 5.625V7.875C14.0625 8.02418 14.0032 8.16726 13.8977 8.27275C13.7923 8.37824 13.6492 8.4375 13.5 8.4375C13.3508 8.4375 13.2077 8.37824 13.1023 8.27275C12.9968 8.16726 12.9375 8.02418 12.9375 7.875V6.1875H11.25C11.1008 6.1875 10.9577 6.12824 10.8523 6.02275C10.7468 5.91726 10.6875 5.77418 10.6875 5.625C10.6875 5.47582 10.7468 5.33274 10.8523 5.22725C10.9577 5.12176 11.1008 5.0625 11.25 5.0625H13.5C13.6492 5.0625 13.7923 5.12176 13.8977 5.22725C14.0032 5.33274 14.0625 5.47582 14.0625 5.625ZM6.75 11.8125H5.0625V10.125C5.0625 9.97582 5.00324 9.83274 4.89775 9.72725C4.79226 9.62176 4.64918 9.5625 4.5 9.5625C4.35082 9.5625 4.20774 9.62176 4.10225 9.72725C3.99676 9.83274 3.9375 9.97582 3.9375 10.125V12.375C3.9375 12.5242 3.99676 12.6673 4.10225 12.7727C4.20774 12.8782 4.35082 12.9375 4.5 12.9375H6.75C6.89918 12.9375 7.04226 12.8782 7.14775 12.7727C7.25324 12.6673 7.3125 12.5242 7.3125 12.375C7.3125 12.2258 7.25324 12.0827 7.14775 11.9773C7.04226 11.8718 6.89918 11.8125 6.75 11.8125ZM16.3125 3.9375V14.0625C16.3125 14.3609 16.194 14.647 15.983 14.858C15.772 15.069 15.4859 15.1875 15.1875 15.1875H2.8125C2.51413 15.1875 2.22798 15.069 2.017 14.858C1.80603 14.647 1.6875 14.3609 1.6875 14.0625V3.9375C1.6875 3.63913 1.80603 3.35298 2.017 3.142C2.22798 2.93103 2.51413 2.8125 2.8125 2.8125H15.1875C15.4859 2.8125 15.772 2.93103 15.983 3.142C16.194 3.35298 16.3125 3.63913 16.3125 3.9375ZM15.1875 14.0625V3.9375H2.8125V14.0625H15.1875Z"
        fill="#5F5FE1"
      />
    </svg>
  );
};

export const PeopleIcon = () => {
  return (
    <svg
      width="21"
      height="20"
      viewBox="0 0 21 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M18.5407 16.5624C17.3508 14.5054 15.5172 13.0304 13.3774 12.3312C14.4358 11.7011 15.2582 10.7409 15.7182 9.59821C16.1781 8.45548 16.2503 7.19337 15.9235 6.00568C15.5968 4.81798 14.8892 3.77039 13.9094 3.02378C12.9296 2.27716 11.7318 1.8728 10.5 1.8728C9.26821 1.8728 8.07044 2.27716 7.09067 3.02378C6.1109 3.77039 5.40331 4.81798 5.07654 6.00568C4.74978 7.19337 4.82193 8.45548 5.28189 9.59821C5.74186 10.7409 6.56422 11.7011 7.62268 12.3312C5.48284 13.0296 3.64925 14.5046 2.4594 16.5624C2.41577 16.6336 2.38683 16.7127 2.37429 16.7953C2.36174 16.8778 2.36585 16.962 2.38638 17.0429C2.4069 17.1238 2.44341 17.1997 2.49377 17.2663C2.54413 17.3328 2.60731 17.3886 2.67958 17.4304C2.75185 17.4721 2.83175 17.499 2.91457 17.5093C2.99738 17.5197 3.08143 17.5134 3.16176 17.4907C3.24209 17.4681 3.31708 17.4296 3.38228 17.3775C3.44749 17.3254 3.50161 17.2608 3.54143 17.1874C5.01331 14.6437 7.61487 13.1249 10.5 13.1249C13.3852 13.1249 15.9867 14.6437 17.4586 17.1874C17.4985 17.2608 17.5526 17.3254 17.6178 17.3775C17.683 17.4296 17.758 17.4681 17.8383 17.4907C17.9186 17.5134 18.0027 17.5197 18.0855 17.5093C18.1683 17.499 18.2482 17.4721 18.3205 17.4304C18.3927 17.3886 18.4559 17.3328 18.5063 17.2663C18.5566 17.1997 18.5932 17.1238 18.6137 17.0429C18.6342 16.962 18.6383 16.8778 18.6258 16.7953C18.6132 16.7127 18.5843 16.6336 18.5407 16.5624ZM6.12503 7.49993C6.12503 6.63463 6.38162 5.78877 6.86235 5.06931C7.34308 4.34984 8.02636 3.78909 8.82579 3.45796C9.62522 3.12682 10.5049 3.04018 11.3535 3.20899C12.2022 3.3778 12.9818 3.79448 13.5936 4.40634C14.2055 5.01819 14.6222 5.79774 14.791 6.64641C14.9598 7.49508 14.8731 8.37474 14.542 9.17417C14.2109 9.9736 13.6501 10.6569 12.9306 11.1376C12.2112 11.6183 11.3653 11.8749 10.5 11.8749C9.34009 11.8737 8.22801 11.4124 7.40781 10.5922C6.5876 9.77195 6.12627 8.65987 6.12503 7.49993Z"
        fill="white"
      />
    </svg>
  );
};

export const DMsIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M18.1087 14.6351C18.5495 13.7391 18.7692 12.7505 18.7493 11.7522C18.7295 10.7538 18.4708 9.7747 17.9948 8.8969C17.5188 8.01909 16.8393 7.26812 16.0134 6.70687C15.1875 6.14562 14.2392 5.79043 13.2478 5.67105C12.919 4.90458 12.4401 4.21168 11.8394 3.63315C11.2386 3.05463 10.5282 2.60219 9.74987 2.30249C8.97156 2.00279 8.14115 1.86189 7.30755 1.8881C6.47395 1.9143 5.65403 2.10709 4.89609 2.45508C4.13815 2.80308 3.45752 3.29925 2.89431 3.91437C2.3311 4.52949 1.8967 5.25111 1.6167 6.03672C1.3367 6.82232 1.21676 7.65602 1.26396 8.4887C1.31116 9.32137 1.52453 10.1362 1.89151 10.8851L1.29307 12.9804C1.23967 13.1679 1.23733 13.3662 1.2863 13.5549C1.33526 13.7435 1.43375 13.9157 1.57157 14.0535C1.7094 14.1913 1.88154 14.2898 2.0702 14.3388C2.25886 14.3877 2.45718 14.3854 2.64463 14.332L4.73995 13.7336C5.37222 14.044 6.05234 14.2455 6.75167 14.3296C7.08502 15.1107 7.57404 15.8155 8.18886 16.4013C8.80369 16.987 9.53144 17.4413 10.3277 17.7365C11.1239 18.0316 11.9719 18.1614 12.82 18.1179C13.6681 18.0744 14.4984 17.8586 15.2603 17.4836L17.3556 18.082C17.543 18.1354 17.7413 18.1377 17.93 18.0888C18.1187 18.0398 18.2908 17.9413 18.4286 17.8035C18.5665 17.6657 18.6649 17.4935 18.7139 17.3049C18.7629 17.1162 18.7605 16.9179 18.7071 16.7304L18.1087 14.6351ZM4.80713 12.4398C4.74905 12.4398 4.69123 12.4477 4.63526 12.4632L2.57198 13.0531L3.16182 10.9898C3.20603 10.8326 3.18696 10.6644 3.1087 10.521C2.53402 9.466 2.35877 8.23919 2.61507 7.06544C2.87137 5.89168 3.54204 4.84959 4.50419 4.13011C5.46635 3.41063 6.65554 3.06196 7.85387 3.14798C9.0522 3.234 10.1794 3.74895 11.0289 4.59848C11.8785 5.448 12.3934 6.5752 12.4794 7.77353C12.5654 8.97186 12.2168 10.1611 11.4973 11.1232C10.7778 12.0854 9.73572 12.756 8.56197 13.0123C7.38821 13.2686 6.16141 13.0934 5.10635 12.5187C5.01488 12.4676 4.91194 12.4404 4.80713 12.4398ZM16.8384 14.7398L17.4282 16.8031L15.3649 16.2132C15.2078 16.169 15.0395 16.1881 14.8962 16.2664C13.7441 16.8932 12.392 17.0426 11.1308 16.6826C9.86955 16.3226 8.80007 15.4819 8.15245 14.3414C9.00827 14.2522 9.83644 13.9872 10.585 13.5629C11.3335 13.1386 11.9863 12.5641 12.5024 11.8756C13.0184 11.1871 13.3866 10.3993 13.5839 9.56181C13.7811 8.72427 13.8031 7.855 13.6485 7.00855C14.3975 7.18505 15.0955 7.53201 15.6884 8.02243C16.2813 8.51284 16.7531 9.13348 17.0669 9.83601C17.3807 10.5386 17.5281 11.304 17.4977 12.0729C17.4673 12.8417 17.2598 13.5932 16.8915 14.2687C16.8126 14.4127 16.7935 14.5819 16.8384 14.7398Z"
        fill="white"
      />
    </svg>
  );
};

export const AgentsIcon = () => {
  return (
    <svg
      width="21"
      height="20"
      viewBox="0 0 21 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.125 3.75H11.125V1.25C11.125 1.08424 11.0592 0.925268 10.9419 0.808058C10.8247 0.690848 10.6658 0.625 10.5 0.625C10.3342 0.625 10.1753 0.690848 10.0581 0.808058C9.94085 0.925268 9.875 1.08424 9.875 1.25V3.75H4.875C4.21196 3.75 3.57607 4.01339 3.10723 4.48223C2.63839 4.95107 2.375 5.58696 2.375 6.25V15C2.375 15.663 2.63839 16.2989 3.10723 16.7678C3.57607 17.2366 4.21196 17.5 4.875 17.5H16.125C16.788 17.5 17.4239 17.2366 17.8928 16.7678C18.3616 16.2989 18.625 15.663 18.625 15V6.25C18.625 5.58696 18.3616 4.95107 17.8928 4.48223C17.4239 4.01339 16.788 3.75 16.125 3.75ZM17.375 15C17.375 15.3315 17.2433 15.6495 17.0089 15.8839C16.7745 16.1183 16.4565 16.25 16.125 16.25H4.875C4.54348 16.25 4.22554 16.1183 3.99112 15.8839C3.7567 15.6495 3.625 15.3315 3.625 15V6.25C3.625 5.91848 3.7567 5.60054 3.99112 5.36612C4.22554 5.1317 4.54348 5 4.875 5H16.125C16.4565 5 16.7745 5.1317 17.0089 5.36612C17.2433 5.60054 17.375 5.91848 17.375 6.25V15ZM13.3125 10.625H7.6875C7.10734 10.625 6.55094 10.8555 6.1407 11.2657C5.73047 11.6759 5.5 12.2323 5.5 12.8125C5.5 13.3927 5.73047 13.9491 6.1407 14.3593C6.55094 14.7695 7.10734 15 7.6875 15H13.3125C13.8927 15 14.4491 14.7695 14.8593 14.3593C15.2695 13.9491 15.5 13.3927 15.5 12.8125C15.5 12.2323 15.2695 11.6759 14.8593 11.2657C14.4491 10.8555 13.8927 10.625 13.3125 10.625ZM11.125 11.875V13.75H9.875V11.875H11.125ZM6.75 12.8125C6.75 12.5639 6.84877 12.3254 7.02459 12.1496C7.2004 11.9738 7.43886 11.875 7.6875 11.875H8.625V13.75H7.6875C7.43886 13.75 7.2004 13.6512 7.02459 13.4754C6.84877 13.2996 6.75 13.0611 6.75 12.8125ZM13.3125 13.75H12.375V11.875H13.3125C13.5611 11.875 13.7996 11.9738 13.9754 12.1496C14.1512 12.3254 14.25 12.5639 14.25 12.8125C14.25 13.0611 14.1512 13.2996 13.9754 13.4754C13.7996 13.6512 13.5611 13.75 13.3125 13.75ZM6.125 8.4375C6.125 8.25208 6.17998 8.07082 6.283 7.91665C6.38601 7.76248 6.53243 7.64232 6.70373 7.57136C6.87504 7.50041 7.06354 7.48184 7.2454 7.51801C7.42725 7.55419 7.5943 7.64348 7.72541 7.77459C7.85652 7.9057 7.94581 8.07275 7.98199 8.2546C8.01816 8.43646 7.99959 8.62496 7.92864 8.79627C7.85768 8.96757 7.73752 9.11399 7.58335 9.217C7.42918 9.32002 7.24792 9.375 7.0625 9.375C6.81386 9.375 6.5754 9.27623 6.39959 9.10041C6.22377 8.9246 6.125 8.68614 6.125 8.4375ZM13 8.4375C13 8.25208 13.055 8.07082 13.158 7.91665C13.261 7.76248 13.4074 7.64232 13.5787 7.57136C13.75 7.50041 13.9385 7.48184 14.1204 7.51801C14.3023 7.55419 14.4693 7.64348 14.6004 7.77459C14.7315 7.9057 14.8208 8.07275 14.857 8.2546C14.8932 8.43646 14.8746 8.62496 14.8036 8.79627C14.7327 8.96757 14.6125 9.11399 14.4583 9.217C14.3042 9.32002 14.1229 9.375 13.9375 9.375C13.6889 9.375 13.4504 9.27623 13.2746 9.10041C13.0988 8.9246 13 8.68614 13 8.4375Z"
        fill="white"
      />
    </svg>
  );
};

export const HomeIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17.0961 8.10705L10.8461 2.21017C10.843 2.2075 10.8402 2.20463 10.8375 2.20158C10.6074 1.99231 10.3075 1.87634 9.99648 1.87634C9.68545 1.87634 9.38558 1.99231 9.15547 2.20158L9.14688 2.21017L2.90391 8.10705C2.77656 8.22414 2.67491 8.3664 2.60538 8.52482C2.53586 8.68323 2.49997 8.85436 2.5 9.02736V16.25C2.5 16.5815 2.6317 16.8995 2.86612 17.1339C3.10054 17.3683 3.41848 17.5 3.75 17.5H7.5C7.83152 17.5 8.14946 17.3683 8.38388 17.1339C8.6183 16.8995 8.75 16.5815 8.75 16.25V12.5H11.25V16.25C11.25 16.5815 11.3817 16.8995 11.6161 17.1339C11.8505 17.3683 12.1685 17.5 12.5 17.5H16.25C16.5815 17.5 16.8995 17.3683 17.1339 17.1339C17.3683 16.8995 17.5 16.5815 17.5 16.25V9.02736C17.5 8.85436 17.4641 8.68323 17.3946 8.52482C17.3251 8.3664 17.2234 8.22414 17.0961 8.10705ZM16.25 16.25H12.5V12.5C12.5 12.1685 12.3683 11.8506 12.1339 11.6161C11.8995 11.3817 11.5815 11.25 11.25 11.25H8.75C8.41848 11.25 8.10054 11.3817 7.86612 11.6161C7.6317 11.8506 7.5 12.1685 7.5 12.5V16.25H3.75V9.02736L3.75859 9.01955L10 3.12501L16.2422 9.01798L16.2508 9.0258L16.25 16.25Z"
        fill="white"
      />
    </svg>
  );
};

export const PencilIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17.9422 4.55782L15.4422 2.05782C15.3841 1.99971 15.3152 1.95361 15.2393 1.92215C15.1635 1.8907 15.0821 1.87451 15 1.87451C14.9179 1.87451 14.8365 1.8907 14.7607 1.92215C14.6848 1.95361 14.6159 1.99971 14.5578 2.05782L7.05781 9.55782C6.99979 9.6159 6.95378 9.68485 6.92242 9.76072C6.89105 9.8366 6.87494 9.9179 6.875 10V12.5C6.875 12.6658 6.94085 12.8247 7.05806 12.9419C7.17527 13.0592 7.33424 13.125 7.5 13.125H10C10.0821 13.1251 10.1634 13.109 10.2393 13.0776C10.3152 13.0462 10.3841 13.0002 10.4422 12.9422L17.9422 5.44219C18.0003 5.38415 18.0464 5.31522 18.0779 5.23934C18.1093 5.16347 18.1255 5.08214 18.1255 5C18.1255 4.91787 18.1093 4.83654 18.0779 4.76067C18.0464 4.68479 18.0003 4.61586 17.9422 4.55782ZM9.74141 11.875H8.125V10.2586L13.125 5.2586L14.7414 6.875L9.74141 11.875ZM15.625 5.99141L14.0086 4.375L15 3.3836L16.6164 5L15.625 5.99141ZM17.5 9.375V16.25C17.5 16.5815 17.3683 16.8995 17.1339 17.1339C16.8995 17.3683 16.5815 17.5 16.25 17.5H3.75C3.41848 17.5 3.10054 17.3683 2.86612 17.1339C2.6317 16.8995 2.5 16.5815 2.5 16.25V3.75C2.5 3.41848 2.6317 3.10054 2.86612 2.86612C3.10054 2.6317 3.41848 2.5 3.75 2.5H10.625C10.7908 2.5 10.9497 2.56585 11.0669 2.68306C11.1842 2.80027 11.25 2.95924 11.25 3.125C11.25 3.29076 11.1842 3.44973 11.0669 3.56695C10.9497 3.68416 10.7908 3.75 10.625 3.75H3.75V16.25H16.25V9.375C16.25 9.20924 16.3158 9.05027 16.4331 8.93306C16.5503 8.81585 16.7092 8.75 16.875 8.75C17.0408 8.75 17.1997 8.81585 17.3169 8.93306C17.4342 9.05027 17.5 9.20924 17.5 9.375Z"
        fill="#BABAFB"
      />
    </svg>
  );
};

export const ActionsIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.875 3.75V7.5C16.875 7.66576 16.8092 7.82473 16.6919 7.94194C16.5747 8.05915 16.4158 8.125 16.25 8.125C16.0842 8.125 15.9253 8.05915 15.8081 7.94194C15.6908 7.82473 15.625 7.66576 15.625 7.5V5.25859L12.3172 8.56719C12.1999 8.68446 12.0409 8.75035 11.875 8.75035C11.7091 8.75035 11.5501 8.68446 11.4328 8.56719C11.3155 8.44991 11.2497 8.29085 11.2497 8.125C11.2497 7.95915 11.3155 7.80009 11.4328 7.68281L14.7414 4.375H12.5C12.3342 4.375 12.1753 4.30915 12.0581 4.19194C11.9408 4.07473 11.875 3.91576 11.875 3.75C11.875 3.58424 11.9408 3.42527 12.0581 3.30806C12.1753 3.19085 12.3342 3.125 12.5 3.125H16.25C16.4158 3.125 16.5747 3.19085 16.6919 3.30806C16.8092 3.42527 16.875 3.58424 16.875 3.75ZM7.68281 11.4328L4.375 14.7414V12.5C4.375 12.3342 4.30915 12.1753 4.19194 12.0581C4.07473 11.9408 3.91576 11.875 3.75 11.875C3.58424 11.875 3.42527 11.9408 3.30806 12.0581C3.19085 12.1753 3.125 12.3342 3.125 12.5V16.25C3.125 16.4158 3.19085 16.5747 3.30806 16.6919C3.42527 16.8092 3.58424 16.875 3.75 16.875H7.5C7.66576 16.875 7.82473 16.8092 7.94194 16.6919C8.05915 16.5747 8.125 16.4158 8.125 16.25C8.125 16.0842 8.05915 15.9253 7.94194 15.8081C7.82473 15.6908 7.66576 15.625 7.5 15.625H5.25859L8.56719 12.3172C8.68446 12.1999 8.75035 12.0409 8.75035 11.875C8.75035 11.7091 8.68446 11.5501 8.56719 11.4328C8.44991 11.3155 8.29085 11.2497 8.125 11.2497C7.95915 11.2497 7.80009 11.3155 7.68281 11.4328ZM16.25 11.875C16.0842 11.875 15.9253 11.9408 15.8081 12.0581C15.6908 12.1753 15.625 12.3342 15.625 12.5V14.7414L12.3172 11.4328C12.1999 11.3155 12.0409 11.2497 11.875 11.2497C11.7091 11.2497 11.5501 11.3155 11.4328 11.4328C11.3155 11.5501 11.2497 11.7091 11.2497 11.875C11.2497 12.0409 11.3155 12.1999 11.4328 12.3172L14.7414 15.625H12.5C12.3342 15.625 12.1753 15.6908 12.0581 15.8081C11.9408 15.9253 11.875 16.0842 11.875 16.25C11.875 16.4158 11.9408 16.5747 12.0581 16.6919C12.1753 16.8092 12.3342 16.875 12.5 16.875H16.25C16.4158 16.875 16.5747 16.8092 16.6919 16.6919C16.8092 16.5747 16.875 16.4158 16.875 16.25V12.5C16.875 12.3342 16.8092 12.1753 16.6919 12.0581C16.5747 11.9408 16.4158 11.875 16.25 11.875ZM5.25859 4.375H7.5C7.66576 4.375 7.82473 4.30915 7.94194 4.19194C8.05915 4.07473 8.125 3.91576 8.125 3.75C8.125 3.58424 8.05915 3.42527 7.94194 3.30806C7.82473 3.19085 7.66576 3.125 7.5 3.125H3.75C3.58424 3.125 3.42527 3.19085 3.30806 3.30806C3.19085 3.42527 3.125 3.58424 3.125 3.75V7.5C3.125 7.66576 3.19085 7.82473 3.30806 7.94194C3.42527 8.05915 3.58424 8.125 3.75 8.125C3.91576 8.125 4.07473 8.05915 4.19194 7.94194C4.30915 7.82473 4.375 7.66576 4.375 7.5V5.25859L7.68281 8.56719C7.80009 8.68446 7.95915 8.75035 8.125 8.75035C8.29085 8.75035 8.44991 8.68446 8.56719 8.56719C8.68446 8.44991 8.75035 8.29085 8.75035 8.125C8.75035 7.95915 8.68446 7.80009 8.56719 7.68281L5.25859 4.375Z"
        fill="#667085"
      />{" "}
    </svg>
  );
};

export const BellSimpleSlashIcon = ({ color }: { color?: string }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="BellSimpleSlash">
        <path
          id="Vector"
          d="M4.21159 2.70461C4.15671 2.64274 4.09009 2.59238 4.01559 2.55645C3.9411 2.52052 3.86022 2.49972 3.77763 2.49528C3.69504 2.49084 3.61239 2.50283 3.53448 2.53057C3.45656 2.5583 3.38492 2.60122 3.32371 2.65685C3.2625 2.71247 3.21295 2.77969 3.17791 2.85461C3.14287 2.92953 3.12305 3.01065 3.1196 3.09329C3.11615 3.17592 3.12913 3.25842 3.1578 3.336C3.18646 3.41358 3.23024 3.4847 3.28659 3.54524L4.59441 4.9843C4.03899 5.93764 3.74723 7.02159 3.74909 8.12492C3.74909 10.8859 3.10378 12.9984 2.67019 13.7452C2.55946 13.9351 2.50076 14.1508 2.50001 14.3706C2.49925 14.5904 2.55647 14.8066 2.66589 14.9972C2.77531 15.1878 2.93306 15.3462 3.12324 15.4564C3.31342 15.5666 3.52929 15.6248 3.74909 15.6249H14.2678L15.7866 17.2952C15.8415 17.3571 15.9081 17.4075 15.9826 17.4434C16.0571 17.4793 16.138 17.5001 16.2206 17.5046C16.3031 17.509 16.3858 17.497 16.4637 17.4693C16.5416 17.4416 16.6133 17.3986 16.6745 17.343C16.7357 17.2874 16.7852 17.2202 16.8203 17.1452C16.8553 17.0703 16.8751 16.9892 16.8786 16.9066C16.882 16.8239 16.8691 16.7414 16.8404 16.6638C16.8117 16.5863 16.7679 16.5151 16.7116 16.4546L4.21159 2.70461ZM3.74909 14.3749C4.35066 13.3405 4.99909 10.9437 4.99909 8.12492C4.998 7.37805 5.16516 6.64053 5.48816 5.96711L13.1311 14.3749H3.74909ZM13.1241 17.4999C13.1241 17.6657 13.0582 17.8247 12.941 17.9419C12.8238 18.0591 12.6649 18.1249 12.4991 18.1249H7.49909C7.33333 18.1249 7.17436 18.0591 7.05715 17.9419C6.93994 17.8247 6.87409 17.6657 6.87409 17.4999C6.87409 17.3342 6.93994 17.1752 7.05715 17.058C7.17436 16.9408 7.33333 16.8749 7.49909 16.8749H12.4991C12.6649 16.8749 12.8238 16.9408 12.941 17.058C13.0582 17.1752 13.1241 17.3342 13.1241 17.4999ZM16.7178 14.0038C16.6448 14.0321 16.5672 14.0467 16.4889 14.0468C16.3634 14.0467 16.2408 14.0087 16.1371 13.938C16.0335 13.8672 15.9535 13.7668 15.9077 13.6499C15.3389 12.2023 14.9991 10.1366 14.9991 8.12492C14.9993 7.2504 14.7702 6.39111 14.3346 5.63281C13.899 4.87451 13.2721 4.24373 12.5165 3.80344C11.7609 3.36314 10.903 3.12872 10.0285 3.12358C9.15397 3.11844 8.29341 3.34276 7.53269 3.77414C7.38885 3.85208 7.22018 3.87052 7.0629 3.82548C6.90562 3.78045 6.77226 3.67554 6.69147 3.53328C6.61068 3.39102 6.58888 3.22276 6.63077 3.06461C6.67266 2.90646 6.77488 2.77104 6.9155 2.68743C7.86635 2.14809 8.94202 1.86758 10.0352 1.87388C11.1283 1.88019 12.2007 2.1731 13.1452 2.72338C14.0898 3.27365 14.8735 4.06204 15.4181 5.00986C15.9628 5.95768 16.2493 7.03177 16.2491 8.12492C16.2491 10.8866 16.878 12.7023 17.071 13.1937C17.1316 13.3479 17.1284 13.5199 17.0622 13.6719C16.9959 13.8238 16.8721 13.9432 16.7178 14.0038Z"
          fill={color ? color : "#667085"}
        />
      </g>
    </svg>
  );
};

export const OptionsIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {" "}
      <path
        d="M10.9375 10C10.9375 10.1854 10.8825 10.3667 10.7795 10.5208C10.6765 10.675 10.5301 10.7952 10.3588 10.8661C10.1875 10.9371 9.99896 10.9557 9.8171 10.9195C9.63525 10.8833 9.4682 10.794 9.33709 10.6629C9.20598 10.5318 9.11669 10.3648 9.08051 10.1829C9.04434 10.001 9.06291 9.81254 9.13386 9.64123C9.20482 9.46993 9.32498 9.32351 9.47915 9.2205C9.63332 9.11748 9.81458 9.0625 10 9.0625C10.2486 9.0625 10.4871 9.16127 10.6629 9.33709C10.8387 9.5129 10.9375 9.75136 10.9375 10ZM10 5.625C10.1854 5.625 10.3667 5.57002 10.5208 5.467C10.675 5.36399 10.7952 5.21757 10.8661 5.04627C10.9371 4.87496 10.9557 4.68646 10.9195 4.5046C10.8833 4.32275 10.794 4.1557 10.6629 4.02459C10.5318 3.89348 10.3648 3.80419 10.1829 3.76801C10.001 3.73184 9.81254 3.75041 9.64123 3.82136C9.46993 3.89232 9.32351 4.01248 9.2205 4.16665C9.11748 4.32082 9.0625 4.50208 9.0625 4.6875C9.0625 4.93614 9.16127 5.1746 9.33709 5.35041C9.5129 5.52623 9.75136 5.625 10 5.625ZM10 14.375C9.81458 14.375 9.63332 14.43 9.47915 14.533C9.32498 14.636 9.20482 14.7824 9.13386 14.9537C9.06291 15.125 9.04434 15.3135 9.08051 15.4954C9.11669 15.6773 9.20598 15.8443 9.33709 15.9754C9.4682 16.1065 9.63525 16.1958 9.8171 16.232C9.99896 16.2682 10.1875 16.2496 10.3588 16.1786C10.5301 16.1077 10.6765 15.9875 10.7795 15.8333C10.8825 15.6792 10.9375 15.4979 10.9375 15.3125C10.9375 15.0639 10.8387 14.8254 10.6629 14.6496C10.4871 14.4738 10.2486 14.375 10 14.375Z"
        fill="#344054"
      />
    </svg>
  );
};
export const CopyIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="Copy">
        <path
          id="Vector"
          d="M16.875 2.5H6.875C6.70924 2.5 6.55027 2.56585 6.43306 2.68306C6.31585 2.80027 6.25 2.95924 6.25 3.125V6.25H3.125C2.95924 6.25 2.80027 6.31585 2.68306 6.43306C2.56585 6.55027 2.5 6.70924 2.5 6.875V16.875C2.5 17.0408 2.56585 17.1997 2.68306 17.3169C2.80027 17.4342 2.95924 17.5 3.125 17.5H13.125C13.2908 17.5 13.4497 17.4342 13.5669 17.3169C13.6842 17.1997 13.75 17.0408 13.75 16.875V13.75H16.875C17.0408 13.75 17.1997 13.6842 17.3169 13.5669C17.4342 13.4497 17.5 13.2908 17.5 13.125V3.125C17.5 2.95924 17.4342 2.80027 17.3169 2.68306C17.1997 2.56585 17.0408 2.5 16.875 2.5ZM12.5 16.25H3.75V7.5H12.5V16.25ZM16.25 12.5H13.75V6.875C13.75 6.70924 13.6842 6.55027 13.5669 6.43306C13.4497 6.31585 13.2908 6.25 13.125 6.25H7.5V3.75H16.25V12.5Z"
          fill="#667085"
        />
      </g>
    </svg>
  );
};

type IconProps = {
  className: string;
};

export const StrikethroughIcon: React.FC<IconProps> = ({ className }) => {
  return (
    <svg
      width="16"
      height="14"
      viewBox="0 0 16 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.5 7C15.5 7.16576 15.4342 7.32473 15.3169 7.44194C15.1997 7.55915 15.0408 7.625 14.875 7.625H11.7445C12.4625 8.18047 13 8.96875 13 10.125C13 11.1672 12.4531 12.1328 11.457 12.843C10.5258 13.5086 9.29766 13.875 8 13.875C6.70234 13.875 5.47422 13.5086 4.54297 12.843C3.54688 12.1328 3 11.1672 3 10.125C3 9.95924 3.06585 9.80027 3.18306 9.68306C3.30027 9.56585 3.45924 9.5 3.625 9.5C3.79076 9.5 3.94973 9.56585 4.06694 9.68306C4.18415 9.80027 4.25 9.95924 4.25 10.125C4.25 11.4805 5.96875 12.625 8 12.625C10.0312 12.625 11.75 11.4805 11.75 10.125C11.75 8.96484 10.9266 8.28281 8.72109 7.625H1.125C0.95924 7.625 0.800269 7.55915 0.683058 7.44194C0.565848 7.32473 0.5 7.16576 0.5 7C0.5 6.83424 0.565848 6.67527 0.683058 6.55806C0.800269 6.44085 0.95924 6.375 1.125 6.375H14.875C15.0408 6.375 15.1997 6.44085 15.3169 6.55806C15.4342 6.67527 15.5 6.83424 15.5 7ZM3.96328 5.125C4.06209 5.12509 4.1595 5.10176 4.24754 5.05691C4.33558 5.01206 4.41174 4.94698 4.46976 4.867C4.52777 4.78703 4.566 4.69444 4.58131 4.59683C4.59662 4.49922 4.58857 4.39937 4.55781 4.30547C4.51308 4.16643 4.49118 4.02105 4.49297 3.875C4.49297 2.45 6.00078 1.375 8 1.375C9.47188 1.375 10.6687 1.95469 11.2031 2.92578C11.2875 3.06177 11.4209 3.1602 11.5758 3.20076C11.7306 3.24132 11.8951 3.22093 12.0353 3.14379C12.1756 3.06666 12.2809 2.93865 12.3296 2.78617C12.3782 2.63368 12.3665 2.46834 12.2969 2.32422C11.5414 0.946875 9.93516 0.125 8 0.125C5.28828 0.125 3.24297 1.73672 3.24297 3.875C3.24164 4.15314 3.28436 4.42975 3.36953 4.69453C3.41052 4.81969 3.49001 4.9287 3.59663 5.00601C3.70326 5.08331 3.83158 5.12496 3.96328 5.125Z"
        className={className}
      />
    </svg>
  );
};

export const LinkIcon: React.FC<IconProps> = ({ className }) => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.9424 6.05764C12.0005 6.11568 12.0466 6.18462 12.0781 6.26049C12.1095 6.33636 12.1257 6.41769 12.1257 6.49983C12.1257 6.58196 12.1095 6.66329 12.0781 6.73916C12.0466 6.81504 12.0005 6.88397 11.9424 6.94201L6.94241 11.942C6.88434 12.0001 6.8154 12.0461 6.73953 12.0776C6.66366 12.109 6.58234 12.1252 6.50022 12.1252C6.4181 12.1252 6.33678 12.109 6.26091 12.0776C6.18504 12.0461 6.1161 12.0001 6.05803 11.942C5.99997 11.8839 5.9539 11.815 5.92248 11.7391C5.89105 11.6633 5.87488 11.5819 5.87488 11.4998C5.87488 11.4177 5.89105 11.3364 5.92248 11.2605C5.9539 11.1846 5.99997 11.1157 6.05803 11.0576L11.058 6.05764C11.1161 5.99953 11.185 5.95343 11.2609 5.92198C11.3368 5.89052 11.4181 5.87434 11.5002 5.87434C11.5824 5.87434 11.6637 5.89052 11.7396 5.92198C11.8154 5.95343 11.8844 5.99953 11.9424 6.05764ZM15.844 2.15608C15.4377 1.74977 14.9554 1.42747 14.4246 1.20758C13.8937 0.987689 13.3248 0.874512 12.7502 0.874512C12.1757 0.874512 11.6067 0.987689 11.0759 1.20758C10.545 1.42747 10.0627 1.74977 9.65647 2.15608L7.30803 4.50373C7.19076 4.62101 7.12488 4.78007 7.12488 4.94592C7.12488 5.11177 7.19076 5.27083 7.30803 5.38811C7.42531 5.50538 7.58437 5.57127 7.75022 5.57127C7.91607 5.57127 8.07513 5.50538 8.19241 5.38811L10.5408 3.04436C11.129 2.46909 11.9204 2.14898 12.7431 2.15351C13.5658 2.15804 14.3536 2.48684 14.9354 3.06856C15.5172 3.65028 15.8462 4.43797 15.8508 5.2607C15.8555 6.08343 15.5355 6.87482 14.9604 7.46311L12.6112 9.81155C12.4939 9.92872 12.428 10.0877 12.4279 10.2535C12.4278 10.4192 12.4936 10.5783 12.6108 10.6955C12.7279 10.8128 12.8869 10.8787 13.0527 10.8788C13.2185 10.8789 13.3775 10.8131 13.4948 10.6959L15.844 8.34358C16.2503 7.93731 16.5726 7.455 16.7925 6.92417C17.0124 6.39334 17.1255 5.8244 17.1255 5.24983C17.1255 4.67526 17.0124 4.10631 16.7925 3.57548C16.5726 3.04466 16.2503 2.56234 15.844 2.15608ZM9.80803 12.6108L7.4596 14.9592C7.1706 15.2547 6.82585 15.4899 6.44531 15.6513C6.06478 15.8126 5.65602 15.8968 5.2427 15.8991C4.82938 15.9014 4.41972 15.8217 4.03743 15.6645C3.65515 15.5074 3.30783 15.276 3.01559 14.9837C2.72335 14.6914 2.492 14.344 2.33493 13.9617C2.17786 13.5794 2.09819 13.1697 2.10054 12.7564C2.10289 12.3431 2.18721 11.9343 2.34862 11.5538C2.51002 11.1733 2.74531 10.8286 3.04085 10.5397L5.3885 8.19201C5.50578 8.07474 5.57166 7.91568 5.57166 7.74983C5.57166 7.58397 5.50578 7.42491 5.3885 7.30764C5.27123 7.19036 5.11217 7.12448 4.94632 7.12448C4.78046 7.12448 4.6214 7.19036 4.50413 7.30764L2.15647 9.65608C1.33596 10.4766 0.875 11.5894 0.875 12.7498C0.875 13.9102 1.33596 15.0231 2.15647 15.8436C2.97699 16.6641 4.08984 17.125 5.25022 17.125C6.4106 17.125 7.52346 16.6641 8.34397 15.8436L10.6924 13.4944C10.8096 13.3771 10.8754 13.2181 10.8753 13.0523C10.8752 12.8865 10.8093 12.7275 10.692 12.6104C10.5747 12.4932 10.4157 12.4274 10.2499 12.4275C10.0842 12.4276 9.92521 12.4935 9.80803 12.6108Z"
        className={className}
      />
    </svg>
  );
};

export const CaseSensitive: React.FC<IconProps> = ({ className }) => {
  return (
    <svg
      width="19"
      height="13"
      viewBox="0 0 19 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.81568 1.10863C6.76521 1.00126 6.68522 0.910468 6.58506 0.846875C6.48489 0.783282 6.3687 0.749512 6.25006 0.749512C6.13141 0.749512 6.01522 0.783282 5.91506 0.846875C5.8149 0.910468 5.7349 1.00126 5.68443 1.10863L0.684432 11.7336C0.649498 11.8079 0.629537 11.8883 0.625688 11.9703C0.621838 12.0523 0.634177 12.1343 0.661998 12.2115C0.718186 12.3675 0.834028 12.4947 0.984041 12.5653C1.13405 12.6358 1.30595 12.6439 1.46191 12.5877C1.61788 12.5315 1.74513 12.4157 1.81568 12.2657L3.11724 9.50004H9.38287L10.6844 12.2657C10.7194 12.3399 10.7686 12.4066 10.8293 12.4619C10.89 12.5171 10.961 12.5599 11.0382 12.5877C11.1154 12.6155 11.1974 12.6279 11.2794 12.624C11.3614 12.6202 11.4418 12.6002 11.5161 12.5653C11.5904 12.5303 11.657 12.4811 11.7123 12.4204C11.7675 12.3597 11.8103 12.2887 11.8381 12.2115C11.8659 12.1343 11.8783 12.0523 11.8744 11.9703C11.8706 11.8883 11.8506 11.8079 11.8157 11.7336L6.81568 1.10863ZM3.70553 8.25004L6.25006 2.843L8.79459 8.25004H3.70553ZM15.6251 4.50004C14.6282 4.50004 13.8493 4.77113 13.3102 5.30629C13.1974 5.42394 13.1349 5.58103 13.1363 5.74406C13.1376 5.90708 13.2026 6.06314 13.3174 6.17892C13.4321 6.29471 13.5876 6.36107 13.7506 6.36385C13.9136 6.36663 14.0713 6.3056 14.1899 6.19379C14.4868 5.89925 14.9712 5.75004 15.6251 5.75004C16.6587 5.75004 17.5001 6.45316 17.5001 7.31254V7.5641C16.9454 7.19302 16.2924 6.99656 15.6251 7.00004C13.9016 7.00004 12.5001 8.26175 12.5001 9.81254C12.5001 11.3633 13.9016 12.625 15.6251 12.625C16.2926 12.628 16.9458 12.4307 17.5001 12.0586C17.5078 12.2244 17.5811 12.3803 17.7038 12.492C17.8265 12.6037 17.9886 12.6621 18.1544 12.6543C18.3201 12.6466 18.476 12.5733 18.5877 12.4506C18.6994 12.3279 18.7578 12.1658 18.7501 12V7.31254C18.7501 5.76175 17.3485 4.50004 15.6251 4.50004ZM15.6251 11.375C14.5915 11.375 13.7501 10.6719 13.7501 9.81254C13.7501 8.95316 14.5915 8.25004 15.6251 8.25004C16.6587 8.25004 17.5001 8.95316 17.5001 9.81254C17.5001 10.6719 16.6587 11.375 15.6251 11.375Z"
        className={className}
      />
    </svg>
  );
};

export const VideoIcon: React.FC<IconProps> = ({ className }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.6695 5.70312C19.5699 5.6497 19.4576 5.62432 19.3446 5.62969C19.2317 5.63505 19.1223 5.67097 19.0281 5.73359L16.25 7.58203V5.625C16.25 5.29348 16.1183 4.97554 15.8839 4.74112C15.6495 4.5067 15.3315 4.375 15 4.375H2.5C2.16848 4.375 1.85054 4.5067 1.61612 4.74112C1.3817 4.97554 1.25 5.29348 1.25 5.625V14.375C1.25 14.7065 1.3817 15.0245 1.61612 15.2589C1.85054 15.4933 2.16848 15.625 2.5 15.625H15C15.3315 15.625 15.6495 15.4933 15.8839 15.2589C16.1183 15.0245 16.25 14.7065 16.25 14.375V12.4219L19.0281 14.2742C19.1313 14.3412 19.252 14.3763 19.375 14.375C19.5408 14.375 19.6997 14.3092 19.8169 14.1919C19.9342 14.0747 20 13.9158 20 13.75V6.25C19.9992 6.13755 19.9681 6.02739 19.9099 5.93114C19.8518 5.83489 19.7687 5.75612 19.6695 5.70312ZM15 14.375H2.5V5.625H15V14.375ZM18.75 12.582L16.25 10.9156V9.08437L18.75 7.42188V12.582Z"
        className={className}
      />{" "}
    </svg>
  );
};

export const FilesIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.6922 5.18281L13.5672 2.05781C13.5091 1.99979 13.4402 1.95378 13.3643 1.92241C13.2884 1.89105 13.2071 1.87494 13.125 1.875H6.875C6.54348 1.875 6.22554 2.0067 5.99112 2.24112C5.7567 2.47554 5.625 2.79348 5.625 3.125V4.375H4.375C4.04348 4.375 3.72554 4.5067 3.49112 4.74112C3.2567 4.97554 3.125 5.29348 3.125 5.625V16.875C3.125 17.2065 3.2567 17.5245 3.49112 17.7589C3.72554 17.9933 4.04348 18.125 4.375 18.125H13.125C13.4565 18.125 13.7745 17.9933 14.0089 17.7589C14.2433 17.5245 14.375 17.2065 14.375 16.875V15.625H15.625C15.9565 15.625 16.2745 15.4933 16.5089 15.2589C16.7433 15.0245 16.875 14.7065 16.875 14.375V5.625C16.8751 5.5429 16.859 5.46159 16.8276 5.38572C16.7962 5.30985 16.7502 5.2409 16.6922 5.18281ZM13.125 16.875H4.375V5.625H10.3664L13.125 8.38359V14.9875C13.125 14.9922 13.125 14.9961 13.125 15C13.125 15.0039 13.125 15.0078 13.125 15.0125V16.875ZM15.625 14.375H14.375V8.125C14.3751 8.0429 14.359 7.96159 14.3276 7.88572C14.2962 7.80985 14.2502 7.7409 14.1922 7.68281L11.0672 4.55781C11.0091 4.49979 10.9402 4.45378 10.8643 4.42241C10.7884 4.39105 10.7071 4.37494 10.625 4.375H6.875V3.125H12.8664L15.625 5.88359V14.375ZM11.25 11.875C11.25 12.0408 11.1842 12.1997 11.0669 12.3169C10.9497 12.4342 10.7908 12.5 10.625 12.5H6.875C6.70924 12.5 6.55027 12.4342 6.43306 12.3169C6.31585 12.1997 6.25 12.0408 6.25 11.875C6.25 11.7092 6.31585 11.5503 6.43306 11.4331C6.55027 11.3158 6.70924 11.25 6.875 11.25H10.625C10.7908 11.25 10.9497 11.3158 11.0669 11.4331C11.1842 11.5503 11.25 11.7092 11.25 11.875ZM11.25 14.375C11.25 14.5408 11.1842 14.6997 11.0669 14.8169C10.9497 14.9342 10.7908 15 10.625 15H6.875C6.70924 15 6.55027 14.9342 6.43306 14.8169C6.31585 14.6997 6.25 14.5408 6.25 14.375C6.25 14.2092 6.31585 14.0503 6.43306 13.9331C6.55027 13.8158 6.70924 13.75 6.875 13.75H10.625C10.7908 13.75 10.9497 13.8158 11.0669 13.9331C11.1842 14.0503 11.25 14.2092 11.25 14.375Z"
        fill="#667085"
      />
    </svg>
  );
};

export const MicIcon: React.FC<IconProps> = ({ className }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10 13.75C10.9942 13.749 11.9475 13.3535 12.6505 12.6505C13.3535 11.9475 13.749 10.9942 13.75 10V5C13.75 4.00544 13.3549 3.05161 12.6517 2.34835C11.9484 1.64509 10.9946 1.25 10 1.25C9.00544 1.25 8.05161 1.64509 7.34835 2.34835C6.64509 3.05161 6.25 4.00544 6.25 5V10C6.25103 10.9942 6.64645 11.9475 7.34949 12.6505C8.05253 13.3535 9.00576 13.749 10 13.75ZM7.5 5C7.5 4.33696 7.76339 3.70107 8.23223 3.23223C8.70107 2.76339 9.33696 2.5 10 2.5C10.663 2.5 11.2989 2.76339 11.7678 3.23223C12.2366 3.70107 12.5 4.33696 12.5 5V10C12.5 10.663 12.2366 11.2989 11.7678 11.7678C11.2989 12.2366 10.663 12.5 10 12.5C9.33696 12.5 8.70107 12.2366 8.23223 11.7678C7.76339 11.2989 7.5 10.663 7.5 10V5ZM10.625 16.2188V18.125C10.625 18.2908 10.5592 18.4497 10.4419 18.5669C10.3247 18.6842 10.1658 18.75 10 18.75C9.83424 18.75 9.67527 18.6842 9.55806 18.5669C9.44085 18.4497 9.375 18.2908 9.375 18.125V16.2188C7.8341 16.062 6.40607 15.3393 5.36707 14.1907C4.32806 13.042 3.7519 11.5489 3.75 10C3.75 9.83424 3.81585 9.67527 3.93306 9.55806C4.05027 9.44085 4.20924 9.375 4.375 9.375C4.54076 9.375 4.69973 9.44085 4.81694 9.55806C4.93415 9.67527 5 9.83424 5 10C5 11.3261 5.52678 12.5979 6.46447 13.5355C7.40215 14.4732 8.67392 15 10 15C11.3261 15 12.5979 14.4732 13.5355 13.5355C14.4732 12.5979 15 11.3261 15 10C15 9.83424 15.0658 9.67527 15.1831 9.55806C15.3003 9.44085 15.4592 9.375 15.625 9.375C15.7908 9.375 15.9497 9.44085 16.0669 9.55806C16.1842 9.67527 16.25 9.83424 16.25 10C16.2481 11.5489 15.6719 13.042 14.6329 14.1907C13.5939 15.3393 12.1659 16.062 10.625 16.2188Z"
        className={className}
      />
    </svg>
  );
};

export const StarIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="Star">
        <path
          id="Vector"
          d="M18.6875 7.60087C18.6118 7.36758 18.4692 7.16171 18.2773 7.0089C18.0855 6.85609 17.8529 6.76309 17.6086 6.7415L12.9688 6.34149L11.15 2.01649C11.0553 1.78955 10.8956 1.5957 10.691 1.45935C10.4863 1.323 10.2459 1.25024 10 1.25024C9.75412 1.25024 9.51371 1.323 9.30907 1.45935C9.10443 1.5957 8.94471 1.78955 8.85002 2.01649L7.03674 6.34149L2.39142 6.74384C2.14614 6.76445 1.9124 6.85702 1.71952 7.00994C1.52663 7.16287 1.38319 7.36933 1.30718 7.60345C1.23117 7.83757 1.22598 8.08892 1.29225 8.32598C1.35852 8.56304 1.49331 8.77526 1.6797 8.93603L5.20392 12.0157L4.14767 16.5892C4.09182 16.8285 4.10776 17.079 4.19349 17.3093C4.27923 17.5396 4.43095 17.7396 4.6297 17.8841C4.82845 18.0287 5.0654 18.1114 5.31093 18.1221C5.55647 18.1327 5.79968 18.0707 6.01017 17.9438L9.99455 15.522L13.9875 17.9438C14.198 18.0707 14.4412 18.1327 14.6868 18.1221C14.9323 18.1114 15.1692 18.0287 15.368 17.8841C15.5667 17.7396 15.7185 17.5396 15.8042 17.3093C15.8899 17.079 15.9059 16.8285 15.85 16.5892L14.7945 12.011L18.318 8.93603C18.5044 8.77471 18.6389 8.56189 18.7046 8.32432C18.7704 8.08676 18.7644 7.83506 18.6875 7.60087ZM17.4985 7.9915L13.975 11.0665C13.8035 11.2157 13.676 11.4088 13.606 11.625C13.5361 11.8413 13.5265 12.0725 13.5781 12.2938L14.6367 16.8751L10.6469 14.4532C10.4522 14.3347 10.2287 14.272 10.0008 14.272C9.77289 14.272 9.54937 14.3347 9.35471 14.4532L5.37033 16.8751L6.42189 12.297C6.47357 12.0756 6.46393 11.8444 6.394 11.6281C6.32407 11.4119 6.1965 11.2188 6.02502 11.0696L2.50002 7.99618C2.49973 7.99385 2.49973 7.99149 2.50002 7.98915L7.14377 7.58759C7.37049 7.5676 7.58745 7.4861 7.77125 7.35186C7.95505 7.21763 8.09871 7.03576 8.18674 6.82587L10 2.50634L11.8125 6.82587C11.9005 7.03576 12.0442 7.21763 12.228 7.35186C12.4118 7.4861 12.6288 7.5676 12.8555 7.58759L17.5 7.98915C17.5 7.98915 17.5 7.99384 17.5 7.99462L17.4985 7.9915Z"
          fill="#667085"
        />
      </g>
    </svg>
  );
};

export const SquareSlash: React.FC<IconProps> = ({ className }) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.6"
        y="0.6"
        width="14.8"
        height="14.8"
        rx="1.4"
        stroke="#606060"
        stroke-width="1.2"
      />
      <path
        d="M11.0261 2.41211C11.1175 2.42116 11.2071 2.44305 11.2899 2.47652C11.3726 2.50998 11.4469 2.55436 11.5084 2.60713C11.5698 2.6599 11.6174 2.72003 11.6482 2.78407C11.6791 2.84812 11.6926 2.91484 11.6881 2.98041C11.6836 3.04598 11.6612 3.10912 11.6221 3.16624L4.63827 13.3438C4.57707 13.4331 4.47752 13.5042 4.35313 13.5473C4.22874 13.5904 4.08556 13.6035 3.94303 13.5848C3.86233 13.5742 3.78352 13.5538 3.71002 13.5246C3.62716 13.4911 3.55284 13.4467 3.49129 13.3938C3.42975 13.341 3.38219 13.2808 3.35135 13.2167C3.3205 13.1526 3.30698 13.0858 3.31154 13.0202C3.3161 12.9545 3.33867 12.8913 3.37794 12.8342L10.3617 2.65662C10.4009 2.5995 10.4559 2.54954 10.5237 2.50958C10.5916 2.46962 10.6708 2.44044 10.757 2.42372C10.8432 2.40699 10.9346 2.40305 11.0261 2.41211Z"
        className={className}
      />
    </svg>
  );
};

export const MagnifyingGlassIcon = () => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="MagnifyingGlass">
        <path
          id="Vector"
          d="M14.3539 13.6464L11.2245 10.5176C12.1315 9.42871 12.5838 8.03201 12.4873 6.6181C12.3908 5.20419 11.7528 3.88193 10.7062 2.92637C9.65963 1.97082 8.28492 1.45555 6.86808 1.48775C5.45125 1.51995 4.10137 2.09714 3.09926 3.09926C2.09714 4.10137 1.51995 5.45125 1.48775 6.86808C1.45555 8.28492 1.97082 9.65963 2.92637 10.7062C3.88193 11.7528 5.20419 12.3908 6.6181 12.4873C8.03201 12.5838 9.42871 12.1315 10.5176 11.2245L13.6464 14.3539C13.6928 14.4003 13.748 14.4372 13.8087 14.4623C13.8694 14.4875 13.9344 14.5004 14.0001 14.5004C14.0658 14.5004 14.1309 14.4875 14.1916 14.4623C14.2523 14.4372 14.3074 14.4003 14.3539 14.3539C14.4003 14.3074 14.4372 14.2523 14.4623 14.1916C14.4875 14.1309 14.5004 14.0658 14.5004 14.0001C14.5004 13.9344 14.4875 13.8694 14.4623 13.8087C14.4372 13.748 14.4003 13.6928 14.3539 13.6464ZM2.50014 7.00014C2.50014 6.11013 2.76406 5.2401 3.25853 4.50008C3.753 3.76006 4.4558 3.18328 5.27807 2.84268C6.10033 2.50209 7.00513 2.41298 7.87805 2.58661C8.75096 2.76024 9.55279 3.18883 10.1821 3.81816C10.8115 4.4475 11.24 5.24932 11.4137 6.12224C11.5873 6.99515 11.4982 7.89995 11.1576 8.72222C10.817 9.54449 10.2402 10.2473 9.50021 10.7418C8.76019 11.2362 7.89016 11.5001 7.00014 11.5001C5.80707 11.4988 4.66325 11.0243 3.81962 10.1807C2.976 9.33704 2.50147 8.19321 2.50014 7.00014Z"
          fill="#667085"
        />
      </g>
    </svg>
  );
};

export const UserPlusIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="UserPlus">
        <path
          id="Vector"
          d="M19.9998 10.625C19.9998 10.7907 19.934 10.9497 19.8167 11.0669C19.6995 11.1841 19.5406 11.25 19.3748 11.25H18.1248V12.5C18.1248 12.6657 18.059 12.8247 17.9417 12.9419C17.8245 13.0591 17.6656 13.125 17.4998 13.125C17.334 13.125 17.1751 13.0591 17.0579 12.9419C16.9406 12.8247 16.8748 12.6657 16.8748 12.5V11.25H15.6248C15.459 11.25 15.3001 11.1841 15.1829 11.0669C15.0656 10.9497 14.9998 10.7907 14.9998 10.625C14.9998 10.4592 15.0656 10.3002 15.1829 10.183C15.3001 10.0658 15.459 9.99995 15.6248 9.99995H16.8748V8.74995C16.8748 8.58419 16.9406 8.42522 17.0579 8.30801C17.1751 8.1908 17.334 8.12495 17.4998 8.12495C17.6656 8.12495 17.8245 8.1908 17.9417 8.30801C18.059 8.42522 18.1248 8.58419 18.1248 8.74995V9.99995H19.3748C19.5406 9.99995 19.6995 10.0658 19.8167 10.183C19.934 10.3002 19.9998 10.4592 19.9998 10.625ZM15.4787 15.2226C15.5854 15.3496 15.6373 15.5138 15.6229 15.6791C15.6086 15.8444 15.5292 15.9971 15.4021 16.1039C15.2751 16.2106 15.1109 16.2624 14.9457 16.2481C14.7804 16.2337 14.6276 16.1543 14.5209 16.0273C12.949 14.1554 10.7881 13.125 8.4373 13.125C6.08652 13.125 3.92558 14.1554 2.3537 16.0273C2.247 16.1542 2.09424 16.2335 1.92905 16.2478C1.76386 16.2621 1.59975 16.2102 1.47284 16.1035C1.34593 15.9968 1.26661 15.844 1.25233 15.6788C1.23804 15.5136 1.28996 15.3495 1.39667 15.2226C2.56386 13.8335 4.01542 12.8468 5.6287 12.3187C4.64894 11.7085 3.89458 10.7957 3.47975 9.7186C3.06491 8.64146 3.01218 7.45852 3.32952 6.34874C3.64687 5.23897 4.31702 4.26273 5.2386 3.56775C6.16018 2.87276 7.28304 2.49683 8.4373 2.49683C9.59156 2.49683 10.7144 2.87276 11.636 3.56775C12.5576 4.26273 13.2277 5.23897 13.5451 6.34874C13.8624 7.45852 13.8097 8.64146 13.3948 9.7186C12.98 10.7957 12.2257 11.7085 11.2459 12.3187C12.8592 12.8468 14.3107 13.8335 15.4787 15.2226ZM8.4373 11.875C9.24078 11.875 10.0262 11.6367 10.6943 11.1903C11.3624 10.7439 11.8831 10.1094 12.1906 9.3671C12.498 8.62478 12.5785 7.80794 12.4217 7.0199C12.265 6.23185 11.8781 5.50798 11.3099 4.93983C10.7418 4.37168 10.0179 3.98476 9.22985 3.82801C8.4418 3.67126 7.62497 3.75171 6.88265 4.05919C6.14032 4.36667 5.50585 4.88737 5.05945 5.55545C4.61306 6.22352 4.3748 7.00896 4.3748 7.81245C4.37604 8.88951 4.80445 9.9221 5.56605 10.6837C6.32764 11.4453 7.36024 11.8737 8.4373 11.875Z"
          fill="#5EA7FF"
        />
      </g>
    </svg>
  );
};

export const RobotIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="Robot">
        <path
          id="Vector"
          d="M15.625 3.75H10.625V1.25C10.625 1.08424 10.5592 0.925268 10.4419 0.808058C10.3247 0.690848 10.1658 0.625 10 0.625C9.83424 0.625 9.67527 0.690848 9.55806 0.808058C9.44085 0.925268 9.375 1.08424 9.375 1.25V3.75H4.375C3.71196 3.75 3.07607 4.01339 2.60723 4.48223C2.13839 4.95107 1.875 5.58696 1.875 6.25V15C1.875 15.663 2.13839 16.2989 2.60723 16.7678C3.07607 17.2366 3.71196 17.5 4.375 17.5H15.625C16.288 17.5 16.9239 17.2366 17.3928 16.7678C17.8616 16.2989 18.125 15.663 18.125 15V6.25C18.125 5.58696 17.8616 4.95107 17.3928 4.48223C16.9239 4.01339 16.288 3.75 15.625 3.75ZM16.875 15C16.875 15.3315 16.7433 15.6495 16.5089 15.8839C16.2745 16.1183 15.9565 16.25 15.625 16.25H4.375C4.04348 16.25 3.72554 16.1183 3.49112 15.8839C3.2567 15.6495 3.125 15.3315 3.125 15V6.25C3.125 5.91848 3.2567 5.60054 3.49112 5.36612C3.72554 5.1317 4.04348 5 4.375 5H15.625C15.9565 5 16.2745 5.1317 16.5089 5.36612C16.7433 5.60054 16.875 5.91848 16.875 6.25V15ZM12.8125 10.625H7.1875C6.60734 10.625 6.05094 10.8555 5.6407 11.2657C5.23047 11.6759 5 12.2323 5 12.8125C5 13.3927 5.23047 13.9491 5.6407 14.3593C6.05094 14.7695 6.60734 15 7.1875 15H12.8125C13.3927 15 13.9491 14.7695 14.3593 14.3593C14.7695 13.9491 15 13.3927 15 12.8125C15 12.2323 14.7695 11.6759 14.3593 11.2657C13.9491 10.8555 13.3927 10.625 12.8125 10.625ZM10.625 11.875V13.75H9.375V11.875H10.625ZM6.25 12.8125C6.25 12.5639 6.34877 12.3254 6.52459 12.1496C6.7004 11.9738 6.93886 11.875 7.1875 11.875H8.125V13.75H7.1875C6.93886 13.75 6.7004 13.6512 6.52459 13.4754C6.34877 13.2996 6.25 13.0611 6.25 12.8125ZM12.8125 13.75H11.875V11.875H12.8125C13.0611 11.875 13.2996 11.9738 13.4754 12.1496C13.6512 12.3254 13.75 12.5639 13.75 12.8125C13.75 13.0611 13.6512 13.2996 13.4754 13.4754C13.2996 13.6512 13.0611 13.75 12.8125 13.75ZM5.625 8.4375C5.625 8.25208 5.67998 8.07082 5.783 7.91665C5.88601 7.76248 6.03243 7.64232 6.20373 7.57136C6.37504 7.50041 6.56354 7.48184 6.7454 7.51801C6.92725 7.55419 7.0943 7.64348 7.22541 7.77459C7.35652 7.9057 7.44581 8.07275 7.48199 8.2546C7.51816 8.43646 7.49959 8.62496 7.42864 8.79627C7.35768 8.96757 7.23752 9.11399 7.08335 9.217C6.92918 9.32002 6.74792 9.375 6.5625 9.375C6.31386 9.375 6.0754 9.27623 5.89959 9.10041C5.72377 8.9246 5.625 8.68614 5.625 8.4375ZM12.5 8.4375C12.5 8.25208 12.555 8.07082 12.658 7.91665C12.761 7.76248 12.9074 7.64232 13.0787 7.57136C13.25 7.50041 13.4385 7.48184 13.6204 7.51801C13.8023 7.55419 13.9693 7.64348 14.1004 7.77459C14.2315 7.9057 14.3208 8.07275 14.357 8.2546C14.3932 8.43646 14.3746 8.62496 14.3036 8.79627C14.2327 8.96757 14.1125 9.11399 13.9583 9.217C13.8042 9.32002 13.6229 9.375 13.4375 9.375C13.1889 9.375 12.9504 9.27623 12.7746 9.10041C12.5988 8.9246 12.5 8.68614 12.5 8.4375Z"
          fill="#7141F8"
        />
      </g>
    </svg>
  );
};

export const ArrowDownIcon = () => {
  return (
    <svg
      width="13"
      height="12"
      viewBox="0 0 13 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="ArrowDown">
        <path
          id="Vector"
          d="M10.1405 7.01531L6.76552 10.3903C6.73069 10.4252 6.68934 10.4528 6.64381 10.4717C6.59829 10.4906 6.54949 10.5003 6.50021 10.5003C6.45093 10.5003 6.40213 10.4906 6.35661 10.4717C6.31108 10.4528 6.26972 10.4252 6.2349 10.3903L2.8599 7.01531C2.78953 6.94495 2.75 6.84951 2.75 6.75C2.75 6.65049 2.78953 6.55505 2.8599 6.48469C2.93026 6.41432 3.0257 6.37479 3.12521 6.37479C3.22472 6.37479 3.32016 6.41432 3.39052 6.48469L6.12521 9.21984V1.875C6.12521 1.77554 6.16472 1.68016 6.23504 1.60984C6.30537 1.53951 6.40075 1.5 6.50021 1.5C6.59966 1.5 6.69505 1.53951 6.76537 1.60984C6.8357 1.68016 6.87521 1.77554 6.87521 1.875V9.21984L9.6099 6.48469C9.68026 6.41432 9.7757 6.37479 9.87521 6.37479C9.97472 6.37479 10.0702 6.41432 10.1405 6.48469C10.2109 6.55505 10.2504 6.65049 10.2504 6.75C10.2504 6.84951 10.2109 6.94495 10.1405 7.01531Z"
          fill="white"
        />
      </g>
    </svg>
  );
};

export const ChatBubbleIcon = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.5 7.00002C10.5 7.13263 10.4473 7.2598 10.3536 7.35357C10.2598 7.44734 10.1326 7.50002 10 7.50002H6C5.86739 7.50002 5.74021 7.44734 5.64645 7.35357C5.55268 7.2598 5.5 7.13263 5.5 7.00002C5.5 6.86741 5.55268 6.74023 5.64645 6.64647C5.74021 6.5527 5.86739 6.50002 6 6.50002H10C10.1326 6.50002 10.2598 6.5527 10.3536 6.64647C10.4473 6.74023 10.5 6.86741 10.5 7.00002ZM10 8.50002H6C5.86739 8.50002 5.74021 8.5527 5.64645 8.64647C5.55268 8.74023 5.5 8.86741 5.5 9.00002C5.5 9.13263 5.55268 9.25981 5.64645 9.35357C5.74021 9.44734 5.86739 9.50002 6 9.50002H10C10.1326 9.50002 10.2598 9.44734 10.3536 9.35357C10.4473 9.25981 10.5 9.13263 10.5 9.00002C10.5 8.86741 10.4473 8.74023 10.3536 8.64647C10.2598 8.5527 10.1326 8.50002 10 8.50002ZM14.5 8.00002C14.5002 9.12223 14.2099 10.2254 13.6574 11.2021C13.1048 12.1788 12.3087 12.9959 11.3467 13.5737C10.3847 14.1515 9.28942 14.4703 8.16759 14.4993C7.04575 14.5282 5.93554 14.2662 4.945 13.7388L2.81687 14.4481C2.64068 14.5069 2.4516 14.5154 2.27083 14.4728C2.09006 14.4301 1.92474 14.3379 1.79341 14.2066C1.66207 14.0753 1.56991 13.91 1.52725 13.7292C1.48459 13.5484 1.49312 13.3593 1.55187 13.1831L2.26125 11.055C1.79759 10.1833 1.53862 9.21739 1.504 8.23063C1.46937 7.24387 1.66 6.26219 2.06142 5.3601C2.46283 4.45802 3.06448 3.65924 3.8207 3.02439C4.57691 2.38954 5.46782 1.93532 6.42579 1.69619C7.38376 1.45707 8.38362 1.43932 9.34948 1.64431C10.3153 1.8493 11.2218 2.27163 12.0001 2.87925C12.7783 3.48687 13.4079 4.2638 13.8411 5.15108C14.2743 6.03835 14.4996 7.01265 14.5 8.00002ZM13.5 8.00002C13.4998 7.15635 13.3054 6.32404 12.9321 5.56748C12.5587 4.81093 12.0163 4.15042 11.3468 3.63704C10.6773 3.12366 9.89863 2.77119 9.07111 2.60689C8.24359 2.44258 7.38936 2.47086 6.57452 2.68952C5.75968 2.90818 5.00606 3.31137 4.37197 3.86789C3.73788 4.42441 3.24032 5.11935 2.91779 5.89893C2.59525 6.67852 2.45639 7.52186 2.51193 8.3637C2.56748 9.20554 2.81595 10.0233 3.23812 10.7538C3.27356 10.8151 3.29556 10.8832 3.30267 10.9537C3.30978 11.0242 3.30184 11.0954 3.27937 11.1625L2.5 13.5L4.8375 12.7206C4.88841 12.7033 4.94183 12.6944 4.99562 12.6944C5.08344 12.6945 5.16966 12.7178 5.24562 12.7619C6.08175 13.2457 7.03054 13.5007 7.99653 13.5013C8.96251 13.5019 9.91162 13.2481 10.7484 12.7654C11.5851 12.2827 12.2799 11.5881 12.763 10.7516C13.246 9.91501 13.5002 8.96601 13.5 8.00002Z"
        fill="#BABAFB"
      />
    </svg>
  );
};

export const DropdownIcon = ({ className }: { className?: string }) => {
  return (
    <svg
      className={`w-[13px] h-13px] transition-transform duration-300 ${className}`}
      width="12"
      height="10"
      viewBox="0 0 22 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M21.7076 1.7075L11.7076 11.7075C11.6147 11.8005 11.5044 11.8742 11.383 11.9246C11.2616 11.9749 11.1315 12.0008 11.0001 12.0008C10.8686 12.0008 10.7385 11.9749 10.6171 11.9246C10.4957 11.8742 10.3854 11.8005 10.2926 11.7075L0.292555 1.7075C0.152544 1.56765 0.0571762 1.38939 0.0185258 1.19531C-0.0201245 1.00122 -0.000319928 0.800033 0.0754325 0.61721C0.151185 0.434387 0.279479 0.278151 0.444073 0.16828C0.608667 0.0584092 0.802159 -0.000155428 1.00006 3.09801e-07H21.0001C21.198 -0.000155428 21.3914 0.0584092 21.556 0.16828C21.7206 0.278151 21.8489 0.434387 21.9247 0.61721C22.0004 0.800033 22.0202 1.00122 21.9816 1.19531C21.9429 1.38939 21.8476 1.56765 21.7076 1.7075Z"
        fill="#BABAFB"
      />
    </svg>
  );
};

export const BrowseIcon = ({ active = false }) => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 26 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0 1C0 0.734784 0.105357 0.48043 0.292893 0.292893C0.48043 0.105357 0.734784 0 1 0H23C23.2652 0 23.5196 0.105357 23.7071 0.292893C23.8946 0.48043 24 0.734784 24 1C24 1.26522 23.8946 1.51957 23.7071 1.70711C23.5196 1.89464 23.2652 2 23 2H1C0.734784 2 0.48043 1.89464 0.292893 1.70711C0.105357 1.51957 0 1.26522 0 1ZM1 10H10C10.2652 10 10.5196 9.89464 10.7071 9.70711C10.8946 9.51957 11 9.26522 11 9C11 8.73478 10.8946 8.48043 10.7071 8.29289C10.5196 8.10536 10.2652 8 10 8H1C0.734784 8 0.48043 8.10536 0.292893 8.29289C0.105357 8.48043 0 8.73478 0 9C0 9.26522 0.105357 9.51957 0.292893 9.70711C0.48043 9.89464 0.734784 10 1 10ZM12 16H1C0.734784 16 0.48043 16.1054 0.292893 16.2929C0.105357 16.4804 0 16.7348 0 17C0 17.2652 0.105357 17.5196 0.292893 17.7071C0.48043 17.8946 0.734784 18 1 18H12C12.2652 18 12.5196 17.8946 12.7071 17.7071C12.8946 17.5196 13 17.2652 13 17C13 16.7348 12.8946 16.4804 12.7071 16.2929C12.5196 16.1054 12.2652 16 12 16ZM25.7075 17.7075C25.6146 17.8005 25.5043 17.8742 25.3829 17.9246C25.2615 17.9749 25.1314 18.0008 25 18.0008C24.8686 18.0008 24.7385 17.9749 24.6171 17.9246C24.4957 17.8742 24.3854 17.8005 24.2925 17.7075L21.75 15.17C20.716 15.8522 19.4657 16.1262 18.2412 15.9391C17.0167 15.752 15.9052 15.117 15.1222 14.1572C14.3392 13.1973 13.9403 11.981 14.0029 10.7439C14.0655 9.50672 14.5851 8.33686 15.461 7.46096C16.3369 6.58505 17.5067 6.06546 18.7439 6.00288C19.981 5.94029 21.1973 6.33915 22.1572 7.12219C23.117 7.90522 23.752 9.01667 23.9391 10.2412C24.1262 11.4657 23.8522 12.716 23.17 13.75L25.7075 16.2875C25.8012 16.3805 25.8756 16.4911 25.9264 16.6129C25.9772 16.7348 26.0033 16.8655 26.0033 16.9975C26.0033 17.1295 25.9772 17.2602 25.9264 17.3821C25.8756 17.5039 25.8012 17.6145 25.7075 17.7075ZM19 14C19.5933 14 20.1734 13.8241 20.6667 13.4944C21.1601 13.1648 21.5446 12.6962 21.7716 12.1481C21.9987 11.5999 22.0581 10.9967 21.9424 10.4147C21.8266 9.83279 21.5409 9.29824 21.1213 8.87868C20.7018 8.45912 20.1672 8.1734 19.5853 8.05764C19.0033 7.94189 18.4001 8.0013 17.8519 8.22836C17.3038 8.45542 16.8352 8.83994 16.5056 9.33329C16.1759 9.82664 16 10.4067 16 11C16 11.7956 16.3161 12.5587 16.8787 13.1213C17.4413 13.6839 18.2044 14 19 14Z"
        fill={active ? "white" : "#BABAFB"}
      />
    </svg>
  );
};

export const PaperCranesRight: React.FC<IconProps> = ({ className }) => {
  return (
    <svg
      width="17"
      height="18"
      viewBox="0 0 17 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.1249 8.99123C16.1255 9.21394 16.0665 9.43276 15.9542 9.62506C15.8418 9.81735 15.6801 9.97613 15.4858 10.085L2.36787 17.5858C2.17939 17.6926 1.96656 17.7491 1.7499 17.7498C1.55009 17.7494 1.35329 17.7011 1.176 17.609C0.998705 17.5168 0.846083 17.3835 0.730927 17.2203C0.61577 17.057 0.541434 16.8685 0.51415 16.6705C0.486866 16.4726 0.507429 16.271 0.574116 16.0826L2.71083 9.83654C2.73189 9.77475 2.77177 9.7211 2.82487 9.68313C2.87798 9.64515 2.94164 9.62477 3.00693 9.62483H8.6249C8.71058 9.62501 8.79538 9.60758 8.87404 9.57362C8.9527 9.53965 9.02354 9.48987 9.08215 9.42738C9.14076 9.36488 9.1859 9.29101 9.21475 9.21033C9.24361 9.12966 9.25557 9.04391 9.2499 8.95842C9.23573 8.7977 9.16136 8.64828 9.0417 8.54006C8.92204 8.43184 8.76591 8.37282 8.60458 8.37483H3.0124C2.94721 8.37494 2.88361 8.35467 2.83052 8.31684C2.77743 8.27902 2.73749 8.22554 2.7163 8.16389L0.572553 1.91389C0.489071 1.6744 0.480415 1.41518 0.547736 1.17065C0.615057 0.926122 0.755168 0.707858 0.949465 0.544842C1.14376 0.381826 1.38305 0.28177 1.63556 0.257962C1.88806 0.234154 2.14183 0.287719 2.36318 0.411545L15.4882 7.90295C15.6812 8.01165 15.8419 8.1697 15.9538 8.36093C16.0657 8.55216 16.1247 8.76968 16.1249 8.99123Z"
        className={className}
      />
    </svg>
  );
};

export const ClockIcon = ({ color }: { color?: string }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10 1.875C8.39303 1.875 6.82214 2.35152 5.486 3.24431C4.14985 4.1371 3.10844 5.40605 2.49348 6.8907C1.87852 8.37535 1.71762 10.009 2.03112 11.5851C2.34463 13.1612 3.11846 14.6089 4.25476 15.7452C5.39106 16.8815 6.8388 17.6554 8.4149 17.9689C9.99099 18.2824 11.6247 18.1215 13.1093 17.5065C14.594 16.8916 15.8629 15.8502 16.7557 14.514C17.6485 13.1779 18.125 11.607 18.125 10C18.1227 7.84581 17.266 5.78051 15.7427 4.25727C14.2195 2.73403 12.1542 1.87727 10 1.875ZM10 16.875C8.64026 16.875 7.31105 16.4718 6.18046 15.7164C5.04987 14.9609 4.16868 13.8872 3.64833 12.6309C3.12798 11.3747 2.99183 9.99237 3.2571 8.65875C3.52238 7.32513 4.17716 6.10013 5.13864 5.13864C6.10013 4.17716 7.32514 3.52237 8.65876 3.2571C9.99238 2.99183 11.3747 3.12798 12.631 3.64833C13.8872 4.16868 14.9609 5.04987 15.7164 6.18045C16.4718 7.31104 16.875 8.64025 16.875 10C16.8729 11.8227 16.1479 13.5702 14.8591 14.8591C13.5702 16.1479 11.8227 16.8729 10 16.875ZM15 10C15 10.1658 14.9342 10.3247 14.8169 10.4419C14.6997 10.5592 14.5408 10.625 14.375 10.625H10C9.83424 10.625 9.67527 10.5592 9.55806 10.4419C9.44085 10.3247 9.375 10.1658 9.375 10V5.625C9.375 5.45924 9.44085 5.30027 9.55806 5.18306C9.67527 5.06585 9.83424 5 10 5C10.1658 5 10.3247 5.06585 10.4419 5.18306C10.5592 5.30027 10.625 5.45924 10.625 5.625V9.375H14.375C14.5408 9.375 14.6997 9.44085 14.8169 9.55806C14.9342 9.67527 15 9.83424 15 10Z"
        fill={color ? color : "#475467"}
      />
    </svg>
  );
};

export const HideUserIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.1242 4.37501C13.1242 4.20925 13.1901 4.05028 13.3073 3.93307C13.4245 3.81586 13.5835 3.75001 13.7492 3.75001H17.4992C17.665 3.75001 17.824 3.81586 17.9412 3.93307C18.0584 4.05028 18.1242 4.20925 18.1242 4.37501C18.1242 4.54077 18.0584 4.69974 17.9412 4.81695C17.824 4.93416 17.665 5.00001 17.4992 5.00001H13.7492C13.5835 5.00001 13.4245 4.93416 13.3073 4.81695C13.1901 4.69974 13.1242 4.54077 13.1242 4.37501ZM18.0117 8.64688C18.2973 10.3442 18.036 12.0883 17.2657 13.6275C16.4953 15.1666 15.2558 16.4211 13.7259 17.2099C12.1961 17.9986 10.4553 18.2807 8.75465 18.0156C7.05404 17.7504 5.4817 16.9516 4.26465 15.7346C3.04761 14.5175 2.24884 12.9452 1.98366 11.2446C1.71849 9.54397 2.00064 7.80309 2.78937 6.27329C3.5781 4.74348 4.83261 3.50393 6.37175 2.73358C7.9109 1.96324 9.65504 1.70197 11.3524 1.98751C11.5146 2.0162 11.6589 2.10779 11.754 2.24236C11.8491 2.37693 11.8871 2.54359 11.8599 2.70609C11.8328 2.86859 11.7425 3.01378 11.6088 3.11008C11.4751 3.20638 11.3088 3.246 11.1461 3.22032C10.1603 3.05448 9.15014 3.10546 8.18601 3.3697C7.22188 3.63394 6.32691 4.10511 5.56338 4.7504C4.79986 5.39569 4.18612 6.19961 3.76488 7.10622C3.34364 8.01282 3.12502 9.00032 3.12423 10C3.12275 11.683 3.7413 13.3075 4.86173 14.5633C5.55878 13.5532 6.53893 12.7717 7.67892 12.3172C7.06656 11.8349 6.61972 11.1737 6.40053 10.4257C6.18135 9.67767 6.20071 8.87992 6.45594 8.1434C6.71116 7.40688 7.18955 6.7682 7.82459 6.31617C8.45963 5.86415 9.21974 5.62124 9.99923 5.62124C10.7787 5.62124 11.5388 5.86415 12.1739 6.31617C12.8089 6.7682 13.2873 7.40688 13.5425 8.1434C13.7977 8.87992 13.8171 9.67767 13.5979 10.4257C13.3787 11.1737 12.9319 11.8349 12.3195 12.3172C13.4595 12.7717 14.4397 13.5532 15.1367 14.5633C16.2572 13.3075 16.8757 11.683 16.8742 10C16.8743 9.61574 16.8424 9.23213 16.7789 8.85313C16.7645 8.77183 16.7664 8.68849 16.7844 8.60791C16.8024 8.52734 16.8362 8.45113 16.8839 8.3837C16.9315 8.31626 16.992 8.25893 17.062 8.21502C17.1319 8.17111 17.2098 8.14149 17.2912 8.12786C17.3727 8.11424 17.456 8.11688 17.5364 8.13565C17.6168 8.15441 17.6927 8.18892 17.7597 8.23717C17.8267 8.28543 17.8834 8.34649 17.9267 8.41681C17.97 8.48713 17.9989 8.56533 18.0117 8.64688ZM9.99923 11.875C10.4937 11.875 10.977 11.7284 11.3882 11.4537C11.7993 11.179 12.1197 10.7885 12.3089 10.3317C12.4981 9.8749 12.5477 9.37224 12.4512 8.88728C12.3547 8.40233 12.1166 7.95687 11.767 7.60724C11.4174 7.25761 10.9719 7.01951 10.487 6.92305C10.002 6.82658 9.49933 6.87609 9.04252 7.06531C8.5857 7.25453 8.19526 7.57496 7.92055 7.98608C7.64585 8.39721 7.49923 8.88056 7.49923 9.37501C7.49923 10.0381 7.76262 10.6739 8.23146 11.1428C8.7003 11.6116 9.33619 11.875 9.99923 11.875ZM9.99923 16.875C11.5253 16.8765 13.0081 16.3678 14.2117 15.4297C13.7596 14.7226 13.1367 14.1406 12.4005 13.7376C11.6644 13.3345 10.8385 13.1232 9.99923 13.1232C9.15991 13.1232 8.3341 13.3345 7.59792 13.7376C6.86173 14.1406 6.23886 14.7226 5.78673 15.4297C6.9904 16.3678 8.47316 16.8765 9.99923 16.875Z"
        fill="#667085"
      />
    </svg>
  );
};

export const MailIcon = () => {
  return (
    <svg
      width="20"
      height="21"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17.5 4.25H2.5C2.33424 4.25 2.17527 4.31585 2.05806 4.43306C1.94085 4.55027 1.875 4.70924 1.875 4.875V15.5C1.875 15.8315 2.0067 16.1495 2.24112 16.3839C2.47554 16.6183 2.79348 16.75 3.125 16.75H16.875C17.2065 16.75 17.5245 16.6183 17.7589 16.3839C17.9933 16.1495 18.125 15.8315 18.125 15.5V4.875C18.125 4.70924 18.0592 4.55027 17.9419 4.43306C17.8247 4.31585 17.6658 4.25 17.5 4.25ZM10 10.9023L4.10703 5.5H15.893L10 10.9023ZM7.71172 10.5L3.125 14.7039V6.29609L7.71172 10.5ZM8.63672 11.3477L9.57422 12.2109C9.68953 12.3168 9.84035 12.3755 9.99687 12.3755C10.1534 12.3755 10.3042 12.3168 10.4195 12.2109L11.357 11.3477L15.8883 15.5H4.10703L8.63672 11.3477ZM12.2883 10.5L16.875 6.29531V14.7047L12.2883 10.5Z"
        fill="#475467"
      />
    </svg>
  );
};

export const BellSnoozeIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.8753 11.25C11.8753 11.4158 11.8094 11.5747 11.6922 11.6919C11.575 11.8092 11.416 11.875 11.2503 11.875H8.75027C8.63716 11.8749 8.52618 11.8442 8.42918 11.786C8.33218 11.7278 8.25278 11.6444 8.19945 11.5446C8.14613 11.4449 8.12087 11.3325 8.12638 11.2196C8.13189 11.1066 8.16796 10.9972 8.23074 10.9031L10.0823 8.125H8.75027C8.58451 8.125 8.42554 8.05915 8.30833 7.94194C8.19111 7.82473 8.12527 7.66576 8.12527 7.5C8.12527 7.33424 8.19111 7.17527 8.30833 7.05806C8.42554 6.94085 8.58451 6.875 8.75027 6.875H11.2503C11.3634 6.87507 11.4744 6.90583 11.5714 6.96401C11.6684 7.02219 11.7478 7.1056 11.8011 7.20535C11.8544 7.30511 11.8797 7.41746 11.8742 7.53044C11.8686 7.64342 11.8326 7.75278 11.7698 7.84688L9.92214 10.625H11.2503C11.416 10.625 11.575 10.6908 11.6922 10.8081C11.8094 10.9253 11.8753 11.0842 11.8753 11.25ZM17.3315 15C17.2231 15.1909 17.0658 15.3495 16.8758 15.4593C16.6857 15.5692 16.4698 15.6264 16.2503 15.625H13.062C12.9178 16.3306 12.5343 16.9647 11.9764 17.4201C11.4185 17.8756 10.7204 18.1243 10.0003 18.1243C9.28009 18.1243 8.58202 17.8756 8.02412 17.4201C7.46622 16.9647 7.08275 16.3306 6.93855 15.625H3.75027C3.53053 15.6247 3.31475 15.5665 3.12469 15.4562C2.93462 15.346 2.77698 15.1875 2.66765 14.9969C2.55832 14.8063 2.50117 14.5903 2.50196 14.3705C2.50275 14.1508 2.56145 13.9351 2.67214 13.7453C3.10574 12.9984 3.75027 10.8859 3.75027 8.125C3.75027 6.4674 4.40875 4.87769 5.58085 3.70558C6.75295 2.53348 8.34266 1.875 10.0003 1.875C11.6579 1.875 13.2476 2.53348 14.4197 3.70558C15.5918 4.87769 16.2503 6.4674 16.2503 8.125C16.2503 10.8852 16.8956 12.9984 17.3292 13.7453C17.441 13.9354 17.5001 14.1518 17.5005 14.3723C17.5009 14.5929 17.4426 14.8095 17.3315 15ZM11.7675 15.625H8.23308C8.36257 15.9902 8.60199 16.3063 8.91844 16.5298C9.23489 16.7533 9.61282 16.8734 10.0003 16.8734C10.3877 16.8734 10.7656 16.7533 11.0821 16.5298C11.3985 16.3063 11.638 15.9902 11.7675 15.625ZM16.2503 14.375C15.6464 13.3383 15.0003 10.9414 15.0003 8.125C15.0003 6.79892 14.4735 5.52715 13.5358 4.58947C12.5981 3.65178 11.3263 3.125 10.0003 3.125C8.67418 3.125 7.40241 3.65178 6.46473 4.58947C5.52705 5.52715 5.00027 6.79892 5.00027 8.125C5.00027 10.9422 4.35339 13.3391 3.75027 14.375H16.2503Z"
        fill="#98A2B3"
      />
    </svg>
  );
};

export const ChannelPlusIcon = () => {
  return (
    <svg width="13" height="13" viewBox="0 0 24 24" fill="none">
      <path
        d="M22 0H2C1.46957 0 0.960859 0.210714 0.585786 0.585786C0.210714 0.960859 0 1.46957 0 2V22C0 22.5304 0.210714 23.0391 0.585786 23.4142C0.960859 23.7893 1.46957 24 2 24H22C22.5304 24 23.0391 23.7893 23.4142 23.4142C23.7893 23.0391 24 22.5304 24 22V2C24 1.46957 23.7893 0.960859 23.4142 0.585786C23.0391 0.210714 22.5304 0 22 0ZM19 13H13V19C13 19.2652 12.8946 19.5196 12.7071 19.7071C12.5196 19.8946 12.2652 20 12 20C11.7348 20 11.4804 19.8946 11.2929 19.7071C11.1054 19.5196 11 19.2652 11 19V13H5C4.73478 13 4.48043 12.8946 4.29289 12.7071C4.10536 12.5196 4 12.2652 4 12C4 11.7348 4.10536 11.4804 4.29289 11.2929C4.48043 11.1054 4.73478 11 5 11H11V5C11 4.73478 11.1054 4.48043 11.2929 4.29289C11.4804 4.10536 11.7348 4 12 4C12.2652 4 12.5196 4.10536 12.7071 4.29289C12.8946 4.48043 13 4.73478 13 5V11H19C19.2652 11 19.5196 11.1054 19.7071 11.2929C19.8946 11.4804 20 11.7348 20 12C20 12.2652 19.8946 12.5196 19.7071 12.7071C19.5196 12.8946 19.2652 13 19 13Z"
        fill="#BABAFB"
      />
    </svg>
  );
};
