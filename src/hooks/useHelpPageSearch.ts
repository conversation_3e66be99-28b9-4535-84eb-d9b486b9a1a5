"use client";

import { useState, useCallback } from "react";
import { GetRequest } from "~/utils/request";

interface SearchResult {
  id: string;
  title: string;
}

export const useHelpPageSearch = () => {
  const [query, setQuery] = useState("");
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const searchData = useCallback(async () => {
    if (!query.trim()) {
      setResults([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem("token");
      if (!token) throw new Error("No token found");

      const response = await GetRequest(
        `/help-center/articles/search?title=${encodeURIComponent(query)}`,
        token
      );

      if (response?.status === 200 && response.data?.status === "success") {
        const data: SearchResult[] = response.data?.data || [];
        setResults(data);
      } else {
        throw new Error("Failed to fetch search results");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  }, [query]);

  return {
    query,
    results,
    isLoading,
    error,
    searchData,
    setQuery,
    setResults,
  };
};
