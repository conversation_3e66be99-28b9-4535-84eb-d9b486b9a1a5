import axios from "axios";
import cogoToast from "cogo-toast";
// import { E<PERSON>r<PERSON><PERSON>hook, SuccessWebhook } from "./webhook-request";
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;
const INTEGRATION_URL = process.env.NEXT_PUBLIC_INTEGRATION_URL;

// ============DO NOT TOUCH THIS FILE OR MAKE CHANGES TO IT==============================
// ============DO NOT TOUCH THIS FILE OR MAKE CHANGES TO IT==============================
// ============DO NOT TOUCH THIS FILE OR MAKE CHANGES TO IT==============================
// ============DO NOT TOUCH THIS FILE OR MAKE CHANGES TO IT==============================

// ==============GET REQUEST=================================
export const GetRequest = async (url: string, token?: string) => {
  try {
    const res = await axios.get(BASE_URL + url, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    return res;
  } catch (error: any) {
    if (
      error?.response?.status === 401 ||
      error?.response?.data?.status_code === 401
    ) {
      localStorage.clear();
      window.location.href = "/auth/login";
    }
    // cogoToast.error(error?.response?.data?.message);
    return error;
  }
};

// ==========POST REQUEST=====================
export const PostRequest = async (url: string, data?: any, token?: string) => {
  try {
    const res = await axios.post(BASE_URL + url, data, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    // SuccessWebhook(
    //   "Telex",
    //   `${res?.data?.message} - ${res?.status} \n `,
    //   `URL: ${res?.request?.responseUrl} \n
    //   MESSAGE: ${res?.request?.response}
    //   `,
    //   "success"
    // );

    return res;
  } catch (error: any) {
    cogoToast.error(error?.response?.data?.message);
    if (error?.response?.status === 401) {
      localStorage.clear();
      window.location.href = "/auth/login";
    }
    if (error?.response?.data?.status_code === 401) {
      localStorage.clear();
      window.location.href = "/auth/login";
    }

    // ErrorWebhook(
    //   "Telex",
    //   `${error?.response?.statusText} - ${error?.response?.status} \n ${error?.response?.data?.message}`,
    //   `URL: ${ error?.request?.responseUrl} \n
    //   MESSAGE: ${ error?.request?.response}
    //   `,
    //   "error"
    // );

    return error;
  }
};

// ==========PATCH REQUEST=====================
export const PatchRequest = async (url: string, data?: any, token?: string) => {
  try {
    const res = await axios.patch(BASE_URL + url, data, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    return res;
  } catch (error: any) {
    cogoToast.error(error?.response?.data?.message);
    if (error?.response?.status === 401) {
      localStorage.clear();
      window.location.href = "/auth/login";
    }
    if (error?.response?.data?.status_code === 401) {
      localStorage.clear();
      window.location.href = "/auth/login";
    }
    return error;
  }
};

// ==========PUT REQUEST=====================
export const PutRequest = async (url: string, data?: any, token?: string) => {
  try {
    const res = await axios.put(BASE_URL + url, data, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    return res;
  } catch (error: any) {
    cogoToast.error(error?.response?.data?.message);
    if (error?.response?.status === 401) {
      localStorage.clear();
      window.location.href = "/auth/login";
    }
    if (error?.response?.data?.status_code === 401) {
      localStorage.clear();
      window.location.href = "/auth/login";
    }
    return error;
  }
};

// ==========DELETE REQUEST=====================
export const DeleteRequest = async (url: string, token?: string) => {
  try {
    const res = await axios.delete(BASE_URL + url, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    return res;
  } catch (error: any) {
    cogoToast.error(error?.response?.data?.message);
    if (error?.response?.status === 401) {
      localStorage.clear();
      window.location.href = "/auth/login";
    }
    if (error?.response?.data?.status_code === 401) {
      localStorage.clear();
      window.location.href = "/auth/login";
    }
    return error;
  }
};

export const DeleteRequests = async (
  url: string,
  data?: any,
  token?: string
) => {
  try {
    const res = await axios.delete(BASE_URL + url, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      data,
    });

    return res;
  } catch (error: any) {
    cogoToast.error(error?.response?.data?.message);
    if (error?.response?.status === 401) {
      localStorage.clear();
      window.location.href = "/auth/login";
    }
    if (error?.response?.data?.status_code === 401) {
      localStorage.clear();
      window.location.href = "/auth/login";
    }
    return error;
  }
};

// ============INTEGRATION SECTION============

// ==============GET REQUEST=================================
export const IntegrationGetRequest = async (url: string, token?: string) => {
  try {
    const res = await axios.get(INTEGRATION_URL + url, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    return res;
  } catch (error: any) {
    if (error?.response?.status === 401) {
      localStorage.clear();
      window.location.href = "/auth/login";
    }
    if (error?.response?.data?.status_code === 401) {
      localStorage.clear();
      window.location.href = "/auth/login";
    }
    // cogoToast.error(error?.response?.data?.message);
    return error;
  }
};

// ==========POST REQUEST=====================
export const IntegrationPostRequest = async (
  url: string,
  data?: any,
  token?: string
) => {
  try {
    const res = await axios.post(INTEGRATION_URL + url, data, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    return res;
  } catch (error: any) {
    cogoToast.error(error?.response?.data?.message);
    if (error?.response?.status === 401) {
      localStorage.clear();
      window.location.href = "/auth/login";
    }

    return error;
  }
};

// ==========DELETE REQUEST=====================
export const IntegrationDeleteRequest = async (
  url: string,
  data?: any,
  token?: string
) => {
  try {
    const res = await axios.delete(INTEGRATION_URL + url, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      data,
    });

    return res;
  } catch (error: any) {
    cogoToast.error(error?.response?.data?.message);
    if (error?.response?.status === 401) {
      localStorage.clear();
      window.location.href = "/auth/login";
    }

    return error;
  }
};
