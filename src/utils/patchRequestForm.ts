import axios from "axios";
import cogoToast from "cogo-toast";
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;

// ==========PATCH REQUEST=====================
export const PatchRequestForForm = async (
  url: string,
  data?: any,
  token?: string
) => {
  try {
    const res = await axios.patch(BASE_URL + url, data, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return res;
  } catch (error: any) {
    cogoToast.error(error?.response?.data?.message);
    return error;
  }
};
