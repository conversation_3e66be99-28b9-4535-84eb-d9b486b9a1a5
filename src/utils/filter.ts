// The filtering function
export const search = (mainData: any, inputData: string) => {
  const res = mainData?.filter((item: any) => {
    return Object.values(item).join(" ").toLowerCase().match(inputData);
  });
  return res;
};

export const FilterMethod = (mainData: any, inputData: string) => {
  const res = mainData?.filter((item: any) => {
    return Object.values(item.data)
      .join(" ")
      .toLowerCase()
      .match(inputData.toLowerCase());
  });
  return res;
};
