"use client";
import Image from "next/image";
import React from "react";
import images from "~/assets/images";
import { Button } from "~/components/ui/button";
import { cn } from "~/lib/utils";

// Add print styles
const printStyles = `
  @media print {
    body {
      -webkit-print-color-adjust: exact !important;
      color-adjust: exact !important;
    }
    .print\\:hidden { display: none !important; }
    .print\\:block { display: block !important; }
    .print\\:border-0 { border: 0 !important; }
    .print\\:shadow-none { box-shadow: none !important; }
    @page {
      margin: 0in;
      size: A4;
    }
  }
`;

interface InvoicePageProps {
  params: {
    id: string;
  };
}

/* eslint-disable */
export default function InvoicePage({ params }: InvoicePageProps) {
  // Use params to get the invoice ID
  const invoiceId = params.id;
  const logoBlack = "/logoBlack.png";

  // Mock data - replace with actual API call
  const invoiceData = {
    invoiceNo: `#${invoiceId}`,
    date: "1 June 2025",
    generatedBy: "Lexi - Invoice Generator",
    office: {
      name: "Telex",
      address: "Musterstraße 12, 10115 Berlin, Germany",
      phone: "+49 1512 3456789",
    },
    biller: {
      name: "Alex Pratt",
      company: "Timbu Tech Ltd",
    },
    items: [
      {
        description: "Telex Starter Plan",
        period: "for May 31 2025 - June 31 2025",
        date: "1 June 2025",
        amount: "$13.45",
        vatPercent: "6.25%",
        vat: "$1.25",
        total: "$15.00",
      },
    ],
    paymentMethod: {
      type: "VISA",
      lastFour: "4242",
      processor: "via Stripe",
    },
  };

  const handleDownload = () => {
    window.print();
  };

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: printStyles }} />
      <div className="flex justify-between">
        <Image
          className="h-screen w-auto "
          src={images.border}
          alt="Logo"
          width={200}
          height={200}
        />
        <div className="bg-white">
          <div className="bg-white  rounded-lg px-8 print:border-0 print:shadow-none">
            {/* Invoice Header */}
            <div className="flex justify-between items-start mb-8">
              <div className="flex flex-col items-start  gap-3 mt-20">
                <Image
                  className="h-16 w-auto "
                  src={logoBlack}
                  alt="Logo"
                  width={200}
                  height={200}
                />
                <div className="mt-12">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-24 ">
                    <div>
                      <h3 className="text-sm text-gray-600 mb-2">
                        Office Information
                      </h3>
                      <div className=" text-gray-900 space-y-1">
                        <div>{invoiceData.office.address}</div>
                        <div>{invoiceData.office.phone}</div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm text-gray-600 mb-2">
                        Biller Information
                      </h3>
                      <div className=" text-gray-900 space-y-1">
                        <div className="font-medium text-gray-900">
                          {invoiceData.biller.name}
                        </div>
                        <div>{invoiceData.biller.company}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="text-right flex flex-col items-center bg-[#F6F7F9] border rounded-b-2xl px-8 pt-12 pb-8">
                <h2 className="text-3xl font-bold text-gray-900 mb-8">
                  INVOICE
                </h2>
                <div className="space-y-1 text-sm text-gray-600">
                  <div className="border bg-white flex items-center gap-6 p-2 rounded-lg mb-4">
                    <div className="text-start">
                      <span className="font-medium text-xs text-[#475467] whitespace-nowrap">
                        Invoice No
                      </span>
                      <div className="font-bold text-gray-900 whitespace-nowrap">
                        {invoiceData.invoiceNo}
                      </div>
                    </div>
                    <div className="w-[1px] h-5 bg-gray-300"></div>
                    <div className="text-start">
                      <span className="font-medium text-xs text-[#475467]">
                        Date
                      </span>
                      <div className="font-bold text-gray-900 whitespace-nowrap">
                        {invoiceData.date}
                      </div>
                    </div>
                  </div>
                  <div className="border bg-white flex flex-col items-start p-2 rounded-lg">
                    <span className="font-medium text-xs text-[#475467]">
                      Generated By
                    </span>
                    <div className="font-bold text-gray-900 flex items-center gap-1 mt-1">
                      <Image
                        src={images.bot}
                        alt={""}
                        width={20}
                        height={20}
                        className={cn(
                          `border rounded bg-[#E6FAEF] border-[#E6EAEF]`
                        )}
                      />
                      {invoiceData.generatedBy}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="mb-8 mt-16">
              <div className="overflow-hidden border border-gray-200 rounded-lg">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">
                        Description
                      </th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">
                        Date
                      </th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">
                        Amount
                      </th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">
                        VAT %
                      </th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">
                        VAT
                      </th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">
                        Total
                      </th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">
                        Payment Method
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {invoiceData.items.map((item, index) => (
                      <tr key={index} className="border-t border-gray-200">
                        <td className="px-4 py-4">
                          <div>
                            <div className="font-medium text-gray-900">
                              {item.description}
                            </div>
                            <div className="text-sm text-gray-500">
                              {item.period}
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900">
                          {item.date}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900">
                          {item.amount}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900">
                          {item.vatPercent}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900">
                          {item.vat}
                        </td>
                        <td className="px-4 py-4 text-sm font-medium text-gray-900">
                          {item.total}
                        </td>
                        <td className="px-4 py-4">
                          <div className="flex items-center gap-2">
                            <Image
                              src={images.visaLogo}
                              alt={""}
                              width={50}
                              height={50}
                              className={cn(
                                `border rounded bg-white border-[#F5F5F5] py-2 px-2`
                              )}
                            />
                            <span className="text-sm text-gray-900">
                              ••••{invoiceData.paymentMethod.lastFour} (
                              {invoiceData.paymentMethod.processor})
                            </span>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
            <div className="w-full flex justify-end">
              <Button
                variant={"outline"}
                onClick={handleDownload}
                className="border border-[#7141F8] text-[#7141F8] hover:bg-[#7141F8] hover:text-white px-6 py-2"
              >
                Download Invoice
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
