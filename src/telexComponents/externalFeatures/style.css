.embla {
  overflow: hidden;
}

.pagination-dots {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.pagination-dot {
  width: 20px;
  height: 5px;
  background-color: #ccc;
  border-radius: 10px;
  margin: 0 5px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.pagination-dot.active {
  width: 50px;
  /* background-color: #8860F8; */
  background-color: #7141f8;
}

.embla__slide {
  flex: 0 0 100%; /* 1 slide per frame by default */
}

@media (min-width: 768px) {
  .embla__slide {
    flex: 0 0 50%; /* 2 slides per frame for tablets */
  }
}

@media (min-width: 1024px) {
  .embla__slide {
    flex: 0 0 33.33%; /* 3 slides per frame for desktops */
  }
}

@media (min-width: 1280px) {
  .embla__slide {
    flex: 0 0 25%; /* 4 slides per frame for larger screens */
  }
}