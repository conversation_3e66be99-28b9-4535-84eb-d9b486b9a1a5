"use client";
import { Star } from "lucide-react";
import SingleFeatureCardComponent from "../singleFeatureCardComponent";
import React, { useState, useEffect, useCallback } from "react";
import useEmblaCarousel from "embla-carousel-react";
import "./style.css";

// const channels = [
//   { text: "Real time notifications in ", color: "black" },
//   { text: "Channels", color: "#8860F8" },
// ];

const Monitoring = [
  { text: "Your 24/7 Employees,", color: "#7141F8" },
  { text: " Always Working", color: "black" },
];

const Team = [
  { text: "All Your Team, ", color: "#E73002" },
  { text: "One Place, Total Visibility", color: "black" },
];

const Agent = [
  { text: "Unlimited Agent Access, ", color: "#00C777" },
  { text: " Unlimited Potential Agents", color: "black" },
];

const Alerts = [
  { text: "Your Alerts, Your Way, ", color: "#0275E7" },
  {
    text: "Zero Noise",
    color: "black",
  },
];

export default function Features() {
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: true,
    slidesToScroll: 1,
  });
  const [selectedIndex, setSelectedIndex] = useState(0);
  const slidesCount = 5;

  const autoplay = useCallback(() => {
    if (!emblaApi) return;
    emblaApi.scrollNext();
  }, [emblaApi]);

  useEffect(() => {
    const intervalId = setInterval(autoplay, 5000);
    return () => clearInterval(intervalId);
  }, [autoplay]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    emblaApi.on("select", onSelect);
    onSelect();
  }, [emblaApi, onSelect]);

  const scrollTo = useCallback(
    (index: number) => {
      if (!emblaApi) return;
      emblaApi.scrollTo(index);
    },
    [emblaApi]
  );

  //

  return (
    <section className="container mx-auto px-4 md:px-6 pt-8 pb-16">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-center">
          <div className="px-3 w-fit py-2 text-sm mb-4 font-medium text-[#40258D] border-2 border-[#F1F1FE] rounded-[50px] bg-white flex items-center gap-1">
            <Star className="w-4 h-4" />
            <span>Features</span>
          </div>
        </div>
        <div className="text-center mt-4 mb-10 max-w-3xl mx-auto w-full">
          <h2 className="text-[#101828] font-semibold sm:text-4xl text-3xl leading-10">
            Smart Features, Smarter Business: Why Telex Fits Right In
          </h2>
          <p className="text-[#344054] text-base font-normal leading-6 mt-2">
            {" "}
            Work with your team and with AI agents in the workspace. Let your
            team handle the tough stuff, while the agents do everything else.
          </p>
        </div>

        <div className="embla" ref={emblaRef}>
          <div className="embla__container flex items-center gap-6">
            <div className="embla__slide mb-2 ml-6">
              <SingleFeatureCardComponent
                mainText={Monitoring}
                subText="Your agents work round the clock to make sure nothing slips your fingers. They will react to opportunities day and night, closing deals and growing the business."
                icon="/monitoring-bell.svg"
                image="/temporary-monitor.png"
                className="flex-col"
              />
            </div>

            <div className="embla__slide mb-2">
              <SingleFeatureCardComponent
                mainText={Team}
                subText="Human or AI, everyone is in one workspace and can interact with themselves."
                icon="/team-bell.svg"
                image="/teams-visibility.png"
                className="flex-col"
              />
            </div>

            <div className="embla__slide mb-2">
              <SingleFeatureCardComponent
                mainText={Agent}
                subText=""
                icon="/agent-bot.svg"
                image="/agent-access.png"
                className="flex-col"
              />
            </div>

            <div className="embla__slide mb-2">
              <SingleFeatureCardComponent
                mainText={Alerts}
                subText="Decide exactly what type of alerts you want, and how often. Whether it’s instant updates, daily summaries, or critical-only pings, you stay in control."
                icon="/alerts-line.svg"
                image="/alert-noise.png"
                className="flex-col"
              />
            </div>

            {/* <div className="embla__slide mb-2">
            <SingleFeatureCardComponent
              mainText={collaboration}
              subText="Bring your entire team together inone place for efficient communication and kniwledge sharing."
              icon="/ChatTeardropText.svg"
              image="/collaboration.svg"
              className="flex-col-reverse pb-4 sm:pb-0"
            />
          </div> */}

            {/* <div className="embla__slide mb-2 mr-6">
            <SingleFeatureCardComponent
              mainText={integrations}
              subText="Seamlessly integrate with your existing tools to create a unified workflow and access data from multiple sources."
              icon="/PlugsConnectedPurple.svg"
              image="/integrations.svg"
              className="flex-col"
            />
          </div> */}
          </div>
        </div>

        <div className="pagination-dots">
          {Array.from({ length: slidesCount }).map((_, index) => (
            <button
              key={index}
              className={`pagination-dot ${selectedIndex === index ? "active" : ""}`}
              onClick={() => scrollTo(index)}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
