"use client";
import React, { useRef } from "react";
import { testimonials } from "./data";
import Testimonial from "./testimonial";
import { Heart, ArrowLeft, ArrowRight } from "lucide-react";
import { Button } from "~/components/ui/button";
import "./style.css";
import Image from "next/image";

const TestimonialList: React.FC = () => {
  const scrollRef = useRef<HTMLDivElement | null>(null);

  const scroll = (direction: "left" | "right") => {
    if (scrollRef.current) {
      const scrollAmount = 300;
      scrollRef.current.scrollBy({
        left: direction === "left" ? -scrollAmount : scrollAmount,
        behavior: "smooth",
      });
    }
  };

  return (
    <section className="bg-[#FAFAFF] py-20 relative">
      <Image
        src="/images/uptime-monitoring/decoration-1.svg"
        alt="decoration"
        width={100}
        height={100}
        className="absolute top-0 left-0 z-0"
      />
      <Image
        src="/images/uptime-monitoring/decoration-2.svg"
        alt="decoration"
        width={350}
        height={350}
        className="absolute bottom-0 right-0"
      />
      <div className="mx-auto xl:px-14 px-6 w-full max-w-[1400px]">
        <div className="flex justify-center">
          <div className="px-3 w-fit py-2 text-sm mb-4 font-medium text-[#40258D] border-2 border-[#F1F1FE] rounded-[50px] bg-white flex items-center gap-1">
            <Heart className="w-4 h-4" />
            <span>Wall of Love</span>
          </div>
        </div>

        <div className="text-center mt-4 mb-10 max-w-3xl mx-auto w-full">
          <h2 className="text-[#101828] font-semibold sm:text-4xl text-3xl leading-10">
            Telex Is Loved By Teams That Scale
          </h2>
          <p className="text-[#344054] text-base font-normal leading-6 mt-2 mx-auto">
            From startups to growing businesses, hear what our devoted users
            have to say about using Telex to gain back billable hours.
          </p>
        </div>

        <div className="relative">
          <div
            ref={scrollRef}
            className="flex overflow-x-auto scroll-smooth gap-6 px-4 no-scrollbar"
            style={{ scrollBehavior: "smooth", width: "100%" }}
          >
            {testimonials.map((testimonial) => (
              <div key={testimonial.id} className="flex-shrink-0">
                <Testimonial testimonial={testimonial} />
              </div>
            ))}
          </div>

          <div className="flex justify-center mt-10 gap-6">
            <Button
              onClick={() => scroll("left")}
              className="p-2 rounded-[5px] bg-white border border-[#E5E8FF] shadow-md hover:bg-gray-100 flex items-center justify-center"
            >
              <ArrowLeft className="h-5 w-5 text-[#101828]" />
            </Button>
            <Button
              onClick={() => scroll("right")}
              className="p-2 rounded-[5px] bg-white border border-[#E5E8FF] shadow-md hover:bg-gray-100 flex items-center justify-center"
            >
              <ArrowRight className="h-5 w-5 text-[#101828]" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialList;
