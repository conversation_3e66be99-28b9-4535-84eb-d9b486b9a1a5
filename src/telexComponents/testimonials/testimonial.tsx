import React from "react";
import { Testimonial as TestimonialType } from "./data";
import Image from "next/image";
import { Quote } from "lucide-react";

interface TestimonialProps {
  testimonial: TestimonialType;
}

const Testimonial: React.FC<TestimonialProps> = ({ testimonial }) => {
  return (
    <article className="sm:w-[350px] min-w-[325px] w-[325px] p-6 rounded-[24px] bg-white h-[360px] border border-[#E4E7EC] flex flex-col justify-between">
      <div className="flex-1">
        <Quote className="text-[#7141F8] w-8 h-8" />
        <p className="text-base text-[#101828] font-normal leading-normal mt-4">
          {testimonial.text}
        </p>
      </div>
      <div className="flex items-center mt-4 pt-6 border-t border-dashed border-[#E6EAEF]">
        <Image
          src={testimonial.image}
          alt={testimonial.author}
          width={44}
          height={44}
          className="mr-4 w-11 h-11 rounded-full object-cover"
        />
        <div>
          <h4 className="text-sm text-[#4B4BB4] font-semibold ">
            {testimonial.author}
          </h4>
          <p className="text-sm text-[#667085] font-normal">
            {testimonial.company}
          </p>
        </div>
      </div>
    </article>
  );
};

export default Testimonial;
