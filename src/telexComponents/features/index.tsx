import SingleFeatureCardComponent from "../singleFeatureCardComponent";
// THE REST OF THE COLORED TEXTS FOR THE MAINTEXT

// const exampleMainText1 = [
//     { text: "AI Summaries ", color: "#8860F8" },
//     { text: "for Intelligent Insights", color: "black" },
//   ];
//   const exampleMainText2 = [
//     { text: "Actionable notifications for faster ", color: "black" },
//     { text: "issue resolution", color: "#00C777" },
//   ];
//   const exampleMainText3 = [
//     { text: "Centralized communication for improved ", color: "black" },
//     { text: "collaboration.", color: "#0275E7" },
//   ];
//   const exampleMainText4 = [
//     { text: "Integrations ", color: "#A302E7" },
//     { text: "for connecting various applications", color: "black" },
//   ];

// REST OF THE SINGLE FEATURE CARD COMPONENTS AND THEIR IMAGE AND ICON URLS
{
  /* <SingleFeatureCardComponent
            mainText={exampleMainText}
            subText="Leverage AI to gain deeper understanding of your notifications and potential fixes."
            icon="/StarFour.svg"
            image="/images/realTimeImage.png"
          />
          <SingleFeatureCardComponent
            mainText={exampleMainText2}
            subText="Get real-time insights and improve decision-making with AI-powered analytics."
            icon="/CheckCircle.svg"
            image="/images/notificationgreen.png"
          />
          <SingleFeatureCardComponent
            mainText={exampleMainText3}
            subText="Leverage AI to gain deeper understanding of your notifications and potential fixes."
            icon="/ChatTeardropText.svg"
            image="/images/collaboration.png"
          />
          <SingleFeatureCardComponent
            mainText={exampleMainText4}
            subText="Leverage AI to gain deeper understanding of your notifications and potential fixes."
            icon="/PlugsConnected.svg"
            image="/images/integrations.png"
          /> */
}

const exampleMainText = [
  { text: "Real-time ", color: "#E73002" },
  { text: "alerts and insights", color: "black" },
];
const exampleMainText1 = [
  { text: "AI Summaries ", color: "#8860F8" },
  { text: "for Intelligent Insights", color: "black" },
];
const Features = () => {
  return (
    <section className="space-y-2">
      <SingleFeatureCardComponent
        mainText={exampleMainText1}
        subText="Leverage AI to gain deeper understanding of your notifications and potential fixes."
        icon="/StarFour.svg"
        image="/images/realTimeImage.png"
      />
      <SingleFeatureCardComponent
        mainText={exampleMainText}
        subText="Receive instant notifications about critical events and gain actionable insights to make informed decisions."
        icon="/Bell.svg"
        image="/images/realTimeImage.png"
      />
    </section>
  );
};

export default Features;
