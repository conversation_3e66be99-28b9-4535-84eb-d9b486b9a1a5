import Image from "next/image";
import React from "react";

interface Props {
  /*
   * card image
   */
  image: string;
  /*
   * card icon
   */
  icon: string;
  /*
   * Main text message as an array of objects containing text and color
   */
  mainText: { text: string; color: string }[];
  /*
   * sub message text
   */
  subText: string;

  className?: string;
}

const SingleFeatureCardComponent = ({
  image,
  icon,
  mainText,
  subText,
  className,
}: Props) => {
  // Getting the color from the mainText prop
  const gradientColor =
    mainText.find((part) => part.color !== "black")?.color || "transparent";

  return (
    <div
      className={`relative flex gap-[56px] ${className} items-end justify-center self-stretch first:rounded-t-lg rounded-b-lg px-[32px] md:px-[56px] bg-gray-100 overflow-hidden bg-[url('/images/cardbg.svg')] pt-[56px]`}
    >
      {/* Gradient overlay */}
      <div
        className="absolute bottom-0 left-0 h-full w-full opacity-50"
        style={{
          background: `linear-gradient(to top right, ${gradientColor} 0%, white 15%)`,
          filter: "blur(20px)",
        }}
      ></div>
      <div className="space-y-4 md:w-[490px] relative z-10 md:pb-[32px] xl:pb-[56px]">
        <Image src={icon} alt={icon} width={39} height={39} />
        <h2 className="text-[24px] md:text-[32px] font-bold leading-[32.48px] w-full md:max-w-[454px] md:leading-snug">
          {mainText.map((part, index) => (
            <span key={index} style={{ color: part.color }}>
              {part.text}
            </span>
          ))}
        </h2>
        <p className="text-[#475467] md:max-w-[454px]">{subText}</p>
      </div>
      <div className="w-full relative z-10 flex-none">
        {image ? (
          <>
            <Image src={image} alt={image} width={580} height={344} />
          </>
        ) : null}
      </div>
    </div>
  );
};

export default SingleFeatureCardComponent;
