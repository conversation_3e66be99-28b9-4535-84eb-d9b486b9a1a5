"use client";
import { MenuIcon, X } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

function HelpCenterSideBar() {
  const [showSidebar, setShowSideBar] = useState(false);
  const sideLinks = [
    {
      id: "1",
      title: "Getting Started",
      sublinks: [
        {
          id: "1",
          name: "Welcome",
          link: "#welcome",
        },
      ],
    },
    {
      id: "2",
      title: "Channel Managment",
      sublinks: [
        {
          id: "1",
          name: "How can a Channel be created on Telex?",
          link: "#create",
        },
        {
          id: "2",
          name: "Can more than one channel be created on Telex?",
          link: "#more",
        },
        {
          id: "3",
          name: "How many members are allowed per channel?",
          link: "#members",
        },
        {
          id: "4",
          name: "Can a channel be retrieved after it is deleted?",
          link: "#retieved",
        },
      ],
    },
    {
      id: "1",
      title: "Telex AI Summary Integration",
      sublinks: [
        {
          id: "1",
          name: "How does Telex Ai Summary Works",
          link: "#how",
        },
      ],
    },
    {
      id: "1",
      title: "Apps Integration",
      sublinks: [
        {
          id: "1",
          name: "How do i integrate apps with telex",
          link: "#app",
        },
      ],
    },
    {
      id: "1",
      title: "User Roles Permission",
      sublinks: [
        {
          id: "1",
          name: "How can i add members into a channel",
          link: "#add",
        },
      ],
    },
    {
      id: "1",
      title: "Custom Workflows",
      sublinks: [
        {
          id: "1",
          name: "Can i create custom work flows ",
          link: "#workflow",
        },
      ],
    },
    {
      id: "1",
      title: "Teams",
      sublinks: [
        {
          id: "1",
          name: "How do i manage my team roles and permission ",
          link: "#team",
        },
      ],
    },
  ];
  return (
    <>
      <MenuIcon
        className="md:hidden ml-6"
        onClick={() => setShowSideBar(true)}
      />
      {showSidebar ? (
        <div className="fixed z-[999999999] p-6 top-0 md:p-0 bg-white w-4/6 md:w-3/12 md:flex flex-col gap-8 md:sticky h-screen md:top-5 md:bg-transparent">
          <X className="" onClick={() => setShowSideBar(false)} />
          {sideLinks.map((links) => (
            <div className="flex flex-col gap-4 " key={links.id}>
              <h1 className="font-bold text-lg">{links.title}</h1>
              {links.sublinks.map((subLink) => (
                <Link
                  href={subLink.link}
                  key={subLink.id}
                  className="text-[#656565] text-[16px] hover:text-[#262626]"
                >
                  {subLink.name}
                </Link>
              ))}
            </div>
          ))}
        </div>
      ) : (
        ""
      )}
    </>
  );
}

export default HelpCenterSideBar;
