import React from "react";
import {
  Accordion,
  AccordionItem,
  AccordionContent,
  AccordionTrigger,
} from "~/components/ui/accordion";
import { ChevronDown } from "lucide-react";

const PricingFaqs = () => {
  return (
    <Accordion type="single" collapsible>
      <AccordionItem value="item-1">
        <AccordionTrigger className="text-[#0A0A0A] font-medium py-8">
          What payment methods do you accept?
          <ChevronDown className="h-6 w-6 shrink-0 transition-transform duration-200 ml-1" />
        </AccordionTrigger>

        <AccordionContent className="text-[#767676] text-sm">
          Yes, we offer a 14-day free trial for new users. You can explore all
          the features of our premium plan without any cost during this period.
        </AccordionContent>
      </AccordionItem>

      <AccordionItem value="item-2">
        <AccordionTrigger className="text-[#0A0A0A] font-medium py-8">
          Is there a discount for annual subscriptions?
          <ChevronDown className="h-6 w-6 shrink-0 transition-transform duration-200 ml-1" />
        </AccordionTrigger>

        <AccordionContent className="text-[#767676] text-sm">
          Yes, we offer a 14-day free trial for new users. You can explore all
          the features of our premium plan without any cost during this period.
        </AccordionContent>
      </AccordionItem>

      <AccordionItem value="item-3">
        <AccordionTrigger className="text-[#0A0A0A] font-medium py-8">
          Do you offer a free trial?
          <ChevronDown className="h-6 w-6 shrink-0 transition-transform duration-200 ml-1" />
        </AccordionTrigger>

        <AccordionContent className="text-[#767676] text-sm">
          Yes, we offer a 14-day free trial for new users. You can explore all
          the features of our premium plan without any cost during this period.
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

export default PricingFaqs;
