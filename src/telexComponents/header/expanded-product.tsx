"use client";
import { ArrowRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useState, useRef } from "react";

interface Props {
  setExpandProduct: any;
  handleMouseEnter: () => void;
  handleMouseLeave: () => void;
  isClosing: boolean;
}

const ExpandedProduct = ({
  setExpandProduct,
  handleMouseEnter,
  handleMouseLeave,
  isClosing,
}: Props) => {
  const pathname = usePathname();
  const [isVisible, setIsVisible] = useState(false);
  const closeTimeoutRef = useRef<any>(null);

  // Use effect to handle animation timing
  useEffect(() => {
    // Clear any existing timeouts when component mounts or unmounts
    return () => {
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
      }
    };
  }, []);

  // Use effect to handle visibility
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 50);

    return () => clearTimeout(timer);
  }, []);

  // Handle clicking on a product link
  const handleProductClick = () => {
    // Clear any existing timeout
    if (closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
    }

    // Wait for the animation to complete before actually closing
    closeTimeoutRef.current = setTimeout(() => {
      setExpandProduct(false);
    }, 500); // Match the CSS transition duration
  };

  // Handle mouse enter to cancel closing
  const handleLocalMouseEnter = () => {
    // Clear any existing timeout
    if (closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = null;
    }

    // Call the parent's handler
    handleMouseEnter();
  };

  // Handle mouse leave
  const handleLocalMouseLeave = () => {
    // Call the parent's handler
    handleMouseLeave();
  };

  //   const products = [
  //     {
  //       icon: "product-1.svg",
  //       title: "Application Performance Monitoring",
  //       description: "Measuring speed, responsiveness, and performance of apps",
  //       href: "/products/application-monitoring",
  //     },
  //     {
  //       icon: "product-2.svg",
  //       title: "Cloud Monitoring",
  //       description: "Watching over cloud infrastructure and resources",
  //       href: "/products/cloud-monitoring",
  //     },
  //     {
  //       icon: "product-3.svg",
  //       title: "Database Monitoring",
  //       description: "Tracking uptime, query speeds, and stability of databases",
  //       href: "/products/database-monitoring",
  //     },
  //     {
  //       icon: "product-4.svg",
  //       title: "Log Analysis",
  //       description: "Parsing and analyzing system logs for issues",
  //       href: "/products/log-analysis",
  //     },
  //     {
  //       icon: "product-5.svg",
  //       title: "Webhook Testing",
  //       description: "Validating and troubleshooting webhook deliveries",
  //       href: "/products/webhook-tester",
  //     },
  //     {
  //       icon: "product-6.svg",
  //       title: "Website Testing",
  //       description: "Ensuring websites load and work as expected",
  //       href: "/products/website-testing",
  //     },
  //     {
  //       icon: "product-7.svg",
  //       title: "Uptime Monitoring",
  //       description: "Constant system availability checks and early warnings",
  //       href: "/products/uptime-monitoring",
  //     },
  //   ];

  const products = [
    {
      icon: "product-1.svg",
      title: "Customer Support with AI",
      description: "Enhance customer support with AI assistance",
      href: "/products/customer-support",
    },
    {
      icon: "product-2.svg",
      title: "Sales With AI",
      description: "Boost sales performance using AI technology",
      href: "/products/sales-ai",
    },
    {
      icon: "product-3.svg",
      title: "Devops and Site Reliability",
      description: "Improve site reliability and DevOps efficiency",
      href: "/products/devops-and-site-reliability",
    },
    {
      icon: "product-4.svg",
      title: "Marketing",
      description: "Enhance marketing strategies and campaigns",
      href: "/products/marketing",
    },
    {
      icon: "product-5.svg",
      title: "Document Creation and Management",
      description: "Streamline document workflows and management",
      href: "/products/document-creation-and-management",
    },
    {
      icon: "product-6.svg",
      title: "IT Operations",
      description: "Optimize IT operations and management",
      href: "/products/it-operations",
    },
    {
      icon: "product-7.svg",
      title: "Video Generation",
      description: "Create and manage video content efficiently",
      href: "/products/video-generation",
    },
    {
      icon: "product-8.svg",
      title: "Data Extraction and Analysis",
      description: "Extract and analyze data effectively",
      href: "/products/data-extraction-and-analysis",
    },
    {
      icon: "product-9.svg",
      title: "Customer Relationship",
      description: "Build and maintain strong customer relationships",
      href: "/products/customer-relationship",
    },
    {
      icon: "product-10.svg",
      title: "Lead Generation Nuturing",
      description: "Generate and nurture quality leads",
      href: "/products/lead-generation-nuturing",
    },
    {
      icon: "product-11.svg",
      title: "Site Security",
      description: "Ensure robust website security",
      href: "/products/site-security",
    },
    {
      icon: "product-12.svg",
      title: "Content Creation",
      description: "Create engaging content efficiently",
      href: "/products/content-creation",
    },
    {
      icon: "product-13.svg",
      title: "Market Research",
      description: "Conduct comprehensive market research",
      href: "/products/market-research",
    },
    {
      icon: "product-14.svg",
      title: "Document Analysis",
      description: "Analyze documents with precision",
      href: "/products/document-analysis",
    },
    {
      icon: "product-15.svg",
      title: "Invoice Processing",
      description: "Process invoices efficiently and accurately",
      href: "/products/invoice-processing",
    },
  ];

  return (
    <div
      className={`hidden md:block absolute top-[4.7rem] left-0 right-0 bg-white z-[2000] rounded-[10px] border border-[#E6EAEF] max-w-6xl shadow-[0px_2px_4px_0px_#1018281A] p-4 transition-all duration-500 ${
        isVisible && !isClosing
          ? "opacity-100 translate-y-0"
          : "opacity-0 translate-y-[-10px]"
      }`}
      onMouseEnter={handleLocalMouseEnter}
      onMouseLeave={handleLocalMouseLeave}
      style={{
        marginTop: "-10px",
        paddingTop: "20px",
      }}
    >
      {/* Speech bubble arrow */}
      <div
        className={`absolute -top-[10px] left-[11rem] w-5 h-5 bg-white transform rotate-45 border-t border-l border-[#E6EAEF] rounded-tl-[4px] transition-opacity duration-500 ${
          isVisible && !isClosing ? "opacity-100" : "opacity-0"
        }`}
      />

      {/* Main content */}
      <div
        className={`transition-opacity duration-500 ${
          isVisible && !isClosing ? "opacity-100" : "opacity-0"
        }`}
      >
        <div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
          onClick={handleProductClick}
        >
          {products.map((product, index) => (
            <Link
              key={product.href}
              href={product.href}
              className={`flex items-start p-2 rounded-[10px] hover:bg-[#F1F1FE] transition-all duration-200 gap-4 hover:scale-[1.02] ${
                pathname === product.href
                  ? "bg-[#F1F1FE]"
                  : "border border-[#E6EAEF]"
              }`}
              style={{
                transitionDelay: `${index * 20}ms`,
                opacity: isVisible && !isClosing ? 1 : 0,
                transform:
                  isVisible && !isClosing
                    ? "translateY(0)"
                    : "translateY(10px)",
              }}
            >
              <div className="flex items-start gap-3">
                <Image
                  src={`/images/products/${product.icon}`}
                  alt={product.title}
                  width={40}
                  height={40}
                />
                <div>
                  <h3 className="text-[#101828] font-medium text-sm mb-1">
                    {product.title}
                  </h3>
                  <p className="text-xs text-[#475467]">
                    {product.description}
                  </p>
                </div>
              </div>
              <div className="w-[30px] flex justify-end self-center">
                {pathname === product.href && (
                  <ArrowRight size={16} color="#7141F8" />
                )}
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ExpandedProduct;
