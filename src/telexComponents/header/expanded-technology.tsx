"use client";

import Link from "next/link";

//eslint-disable

interface Props {
  setExpandTechnology: any;
  handleMouseEnter: any;
  handleMouseLeave: any;
}

const ExpandedTechnology = ({
  setExpandTechnology,
  handleMouseEnter,
  handleMouseLeave,
}: Props) => {
  return (
    <div
      className="hidden md:flex h-fit items-start justify-between px-5 lg:px-20 p-10 gap-4 bg-white text-black absolute sm:top-[4rem] lg:top-[4rem] left-[0]  right-[0] z-[2000] border-t-[9px] border-solid border-primary-500"
      style={{
        boxShadow: "0px 20px 50px 0px rgba(18, 17, 39, 0.08)",
      }}
      onMouseOver={handleMouseEnter}
      onMouseOut={handleMouseLeave}
      onClick={() => setExpandTechnology(false)}
    >
      <div className="w-full h-full lg:pl-5 flex-1">
        <div className="bg-[#8860F812] p-5 rounded-xs mb-8">
          <h2 className="text-primary-500 text-[18px] lg:text-[22px] mb-1 font-semibold">
            Technologies
          </h2>
          <p className="text-[12px]">
            Our comprehensive strategies for offers for valuable insights on
            technologies
          </p>
        </div>

        <div className="md:grid md:grid-cols-4">
          <div className="flex flex-col gap-2">
            <span className="text-[13px] font-medium pl-3">
              Application Performance Monitoring
            </span>

            <Link
              href="/technology/application-monitoring/monitor-web-application"
              className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2"
            >
              Monitor Web Applications
            </Link>
            <Link
              href="/technology/application-monitoring/cloud-infrastructure"
              className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2"
            >
              Cloud Infrastructure Monitoring
            </Link>
            <Link
              href="/technology/application-monitoring/mobile-application"
              className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2"
            >
              Mobile Applications Monitoring
            </Link>
            <Link
              href="/technology/application-monitoring/enterprise-monitoring"
              className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2"
            >
              Enterprise Systems Monitoring
            </Link>
            <Link
              href="/technology/application-monitoring/microservices"
              className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2"
            >
              Monitoring Microservices
            </Link>
            <Link
              href="/technology/application-monitoring/api-monitoring"
              className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2"
            >
              APIs Monitoring
            </Link>

            <Link
              href="/technology/application-monitoring/database-system-monitoring"
              className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2"
            >
              Database Systems Monitoring
            </Link>

            {/* <Link
              href="/technology/application-monitoring/devops-pipeline-monitoring"
              className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2"
            >
              DevOps Pipelines Monitoring
            </Link> */}
          </div>

          <div className="flex flex-col gap-2">
            <span className="text-[13px] font-medium pl-3">
              Database Monitoring
            </span>

            <Link
              href="/technology/database-monitoring/mysql"
              className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2"
            >
              MySQL
            </Link>
            <Link
              href="/technology/database-monitoring/postgre"
              className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2"
            >
              PostgreSQL
            </Link>
            <Link
              href="/technology/database-monitoring/mongodb"
              className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2"
            >
              MongoDB
            </Link>
            <Link
              href="/technology/database-monitoring/redis"
              className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2"
            >
              Redis
            </Link>
            <Link
              href="/technology/database-monitoring/microsoft-sql-server"
              className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2"
            >
              Microsoft SQL Server
            </Link>
            <Link
              href="/technology/database-monitoring/oracle"
              className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2"
            >
              Oracle
            </Link>
          </div>

          {/* <div className="flex flex-col gap-5">
            <span className="text-[13px] font-medium pl-3">Cloud Monitoring</span>

            <Link href="#" className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2"></Link>
            <Link href="#" className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2">
              Network Monitoring
            </Link>
            <Link href="#" className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2">
              Log Monitoring
            </Link>
            <Link
              href="/products/webhook-testing"
              className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2"
            >
              Webhook Testing
            </Link>
          </div>

          <div className="flex flex-col gap-5">
            <span className="text-[13px] font-medium pl-3">
              Cloud Infrastructure Monitoring
            </span>

            <Link href="#" className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2">
              Server Monitoring
            </Link>
            <Link href="#" className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2">
              Cloud Monitoring
            </Link>
            <Link href="#" className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2">
              Network Monitoring
            </Link>
            <Link href="#" className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2">
              Log Monitoring
            </Link>
            <Link
              href="/products/webhook-testing"
              className="text-[13px] text-gray-500 hover:bg-gray-100 rounded-md hover:text-primary-500 px-3 py-2"
            >
              Webhook Testing
            </Link>
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default ExpandedTechnology;
