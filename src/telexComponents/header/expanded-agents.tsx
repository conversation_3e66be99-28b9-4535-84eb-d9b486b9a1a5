"use client";
import Image from "next/image";
/* eslint-disable no-unused-vars */
import Link from "next/link";
import { useState } from "react";

//eslint-disable

interface Props {
  setExpandAgents: any;
  handleMouseEnter: any;
  handleMouseLeave: any;
}

const ExpandedAgents = ({
  handleMouseEnter,
  handleMouseLeave,
  setExpandAgents,
}: Props) => {
  return (
    <div
      className={`hidden md:flex h-fit items-start justify-between px-5 lg:px-10 p-10 gap-4 bg-white text-black absolute sm:top-[4rem] lg:top-[4rem] left-[0]  right-[0] z-[2000] border-t-[9px] border-solid border-primary-500 `}
      style={{
        boxShadow: "0px 20px 50px 0px rgba(18, 17, 39, 0.08)",
      }}
      onMouseOver={handleMouseEnter}
      onMouseOut={handleMouseLeave}
    >
      <div className="w-full h-full lg:pl-5 flex-1">
        <div className="bg-[#8860F812] p-5 rounded-xs mb-8">
          <h2 className="text-primary-500 text-[18px] lg:text-[22px] mb-1 font-semibold">
            Meet Your AI Agents
          </h2>
          <p className="text-[12px]">
            Discover and manage all your agents in one place — from performance
            insights to activity tracking, roles, and real-time status updates.
          </p>
        </div>

        <div className="flex flex-col gap-4 md:grid md:grid-cols-3">
          <div className="">
            <Link
              href={"/agents/ai-social-media-handler"}
              className="flex items-center gap-4 p-3 border border-[#EAEAEA] hover:border-[#E53C00] rounded-md cursor-pointer hover:text-primary-500 mb-3"
            >
              <Image
                src={"/images/agent-ruby-icon.svg"}
                width={37}
                height={45}
                className="w-12"
                alt="Agent Icon"
              />
              <div className="text-[15px] text-gray-500">
                Meet Ruby, your AI Social Media Handler
              </div>
            </Link>
            <Link
              href={"/agents/ai-meeting-summarizer"}
              className="flex items-center gap-4 p-3 border border-[#EAEAEA] hover:border-[#E53C00] rounded-md cursor-pointer hover:text-primary-500 mb-3"
            >
              <Image
                src={"/images/agent-drew-icon.svg"}
                width={37}
                height={45}
                className="w-12"
                alt="Agent Icon"
              />
              <div className="text-[15px] text-gray-500">
                Meet Ani, your AI Meeting Summarizer
              </div>
            </Link>
            <Link
              href={"/agents/ai-powered-support-agent"}
              className="flex items-center gap-4 p-3 border border-[#EAEAEA] hover:border-[#E53C00] rounded-md cursor-pointer hover:text-primary-500 mb-3"
            >
              <Image
                src={"/images/agent-bob-icon.svg"}
                width={37}
                height={45}
                className="w-12"
                alt="Agent Icon"
              />
              <div className="text-[15px] text-gray-500">
                Meet Bob, your AI-Powered Support Agent
              </div>
            </Link>
            <Link
              href={"/agents/ai-blogger-agent"}
              className="flex items-center gap-4 p-3 border border-[#EAEAEA] hover:border-[#E53C00] rounded-md cursor-pointer hover:text-primary-500 mb-3"
            >
              <Image
                src={"/images/agent-elena-icon.svg"}
                width={37}
                height={45}
                className="w-12"
                alt="Agent Icon"
              />
              <div className="text-[15px] text-gray-500">
                Meet Mike, your Blogger Agent
              </div>
            </Link>
            <Link
              href={"/"}
              className="flex items-center gap-4 p-3 border border-[#EAEAEA] hover:border-[#E53C00] rounded-md cursor-pointer hover:text-primary-500 mb-3"
            >
              <Image
                src={"/images/agent-ruby-icon.svg"}
                width={37}
                height={45}
                className="w-12"
                alt="Agent Icon"
              />
              <div className="text-[15px] text-gray-500">
                Meet Susan, your AI Sales Agent
              </div>
            </Link>
          </div>
          <div>
            <Link
              href={"/agents/ai-email-marketing-agent"}
              className="flex items-center gap-4 p-3 border border-[#EAEAEA] hover:border-[#E53C00] rounded-md cursor-pointer hover:text-primary-500 mb-3"
            >
              <Image
                src={"/images/agent-ruby-icon.svg"}
                width={37}
                height={45}
                className="w-12"
                alt="Agent Icon"
              />
              <div className="text-[15px] text-gray-500">
                Meet Lex, your Email Marketing Agent
              </div>
            </Link>
            <Link
              href={"/"}
              className="flex items-center gap-4 p-3 border border-[#EAEAEA] hover:border-[#E53C00] rounded-md cursor-pointer hover:text-primary-500 mb-3"
            >
              <Image
                src={"/images/agent-ruby-icon.svg"}
                width={37}
                height={45}
                className="w-12"
                alt="Agent Icon"
              />
              <div className="text-[15px] text-gray-500">
                Meet Ruby, your Profile Icon Agent
              </div>
            </Link>
            <Link
              href={"/agents/ai-sales-analysis"}
              className="flex items-center gap-4 p-3 border border-[#EAEAEA] hover:border-[#E53C00] rounded-md cursor-pointer hover:text-primary-500 mb-3"
            >
              <Image
                src={"/images/agent-ruby-icon.svg"}
                width={37}
                height={45}
                className="w-12"
                alt="Agent Icon"
              />
              <div className="text-[15px] text-gray-500">
                Meet Lynx, your Sales Analysis Agent
              </div>
            </Link>
            <Link
              href={"/"}
              className="flex items-center gap-4 p-3 border border-[#EAEAEA] hover:border-[#E53C00] rounded-md cursor-pointer hover:text-primary-500 mb-3"
            >
              <Image
                src={"/images/agent-ruby-icon.svg"}
                width={37}
                height={45}
                className="w-12"
                alt="Agent Icon"
              />
              <div className="text-[15px] text-gray-500">
                Meet Alfred, your Code Error Agent
              </div>
            </Link>
            <Link
              href={"/"}
              className="flex items-center gap-4 p-3 border border-[#EAEAEA] hover:border-[#E53C00] rounded-md cursor-pointer hover:text-primary-500 mb-3"
            >
              <Image
                src={"/images/agent-ruby-icon.svg"}
                width={37}
                height={45}
                className="w-12"
                alt="Agent Icon"
              />
              <div className="text-[15px] text-gray-500">
                Meet Jeff, your Server Monitoring Agent
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExpandedAgents;
