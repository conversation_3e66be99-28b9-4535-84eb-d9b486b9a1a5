"use client";
/* eslint-disable no-unused-vars */
import Link from "next/link";

//eslint-disable

interface Props {
  expandTechnology: boolean;
  setExpandTechnology: any;
}

const ExpandedTechnology = ({
  expandTechnology,
  setExpandTechnology,
}: Props) => {
  return (
    <div
      className="hidden md:flex h-fit items-start justify-between px-5 lg:px-20 p-10 gap-4 bg-white text-black absolute sm:top-[4rem] lg:top-[4rem] left-[0]  right-[0] z-[2000] border-t-[9px] border-solid border-primary-500"
      style={{
        boxShadow: "0px 20px 50px 0px rgba(18, 17, 39, 0.08)",
      }}
      onMouseOver={() => {
        setExpandTechnology(true);
      }}
      onMouseOut={() => {
        setExpandTechnology(false);
      }}
      onClick={() => setExpandTechnology(false)}
    >
      <div className="w-full h-full lg:pl-5 flex-1">
        <div className="bg-[#8860F812] p-5 rounded-xs mb-8">
          <h2 className="text-primary-500 text-[18px] lg:text-[22px] mb-1 font-semibold">
            Technologies
          </h2>
          <p className="text-[12px]">
            Our comprehensive strategies for offers for valuable insights on
            technologies
          </p>
        </div>

        <div className="md:grid md:grid-cols-4">
          <div className="flex flex-col gap-5">
            <span className="text-[13px] font-medium">
              Application Performance Monitoring
            </span>

            <Link href="#" className="text-[13px] text-primary-500">
              Monitor Web Applications
            </Link>
            <Link href="#" className="text-[13px] text-primary-500">
              Cloud Infrastructure Monitoring
            </Link>
            <Link href="#" className="text-[13px] text-primary-500">
              Mobile Applications Monitoring
            </Link>
            <Link href="#" className="text-[13px] text-primary-500">
              Enterprise Systems Monitoring
            </Link>
            <Link href="#" className="text-[13px] text-primary-500">
              Monitoring Microservices
            </Link>
            <Link
              href="/products/webhook-testing"
              className="text-[13px] text-primary-500"
            >
              APIs Monitoring
            </Link>

            <Link href="#" className="text-[13px] text-primary-500">
              Database Systems Monitoring
            </Link>

            <Link href="#" className="text-[13px] text-primary-500">
              DevOps Pipelines Monitoring
            </Link>
            <Link href="#" className="text-[13px] text-primary-500">
              Streaming Platforms Monitoring
            </Link>
            <Link href="#" className="text-[13px] text-primary-500">
              IoT Devices Monitoring
            </Link>
            {/* <Link href="#" className="text-[13px] text-primary-500">
              Security Systems Monitoring
            </Link>
            <Link href="#" className="text-[13px] text-primary-500">
              Financial Systems Monitoring
            </Link> */}
          </div>

          <div className="flex flex-col gap-5">
            <span className="text-[13px] font-medium">Database Monitoring</span>

            <Link
              href="/technology/database-monitoring/mysql"
              className="text-[13px] text-primary-500"
            >
              MySQL
            </Link>
            <Link
              href="/technology/database-monitoring/postgre"
              className="text-[13px] text-primary-500"
            >
              PostgreSQL
            </Link>
            <Link
              href="/technology/database-monitoring/mongodb"
              className="text-[13px] text-primary-500"
            >
              MongoDB
            </Link>
            <Link
              href="/technology/database-monitoring/redis"
              className="text-[13px] text-primary-500"
            >
              Redis
            </Link>
            <Link
              href="/technology/database-monitoring/microsoft-sql-server"
              className="text-[13px] text-primary-500"
            >
              Microsoft SQL Server
            </Link>
            <Link
              href="/technology/database-monitoring/oracle"
              className="text-[13px] text-primary-500"
            >
              Oracle
            </Link>
          </div>

          {/* <div className="flex flex-col gap-5">
            <span className="text-[13px] font-medium">Cloud Monitoring</span>

            <Link href="#" className="text-[13px] text-primary-500"></Link>
            <Link href="#" className="text-[13px] text-primary-500">
              Network Monitoring
            </Link>
            <Link href="#" className="text-[13px] text-primary-500">
              Log Monitoring
            </Link>
            <Link
              href="/products/webhook-testing"
              className="text-[13px] text-primary-500"
            >
              Webhook Testing
            </Link>
          </div>

          <div className="flex flex-col gap-5">
            <span className="text-[13px] font-medium">
              Cloud Infrastructure Monitoring
            </span>

            <Link href="#" className="text-[13px] text-primary-500">
              Server Monitoring
            </Link>
            <Link href="#" className="text-[13px] text-primary-500">
              Cloud Monitoring
            </Link>
            <Link href="#" className="text-[13px] text-primary-500">
              Network Monitoring
            </Link>
            <Link href="#" className="text-[13px] text-primary-500">
              Log Monitoring
            </Link>
            <Link
              href="/products/webhook-testing"
              className="text-[13px] text-primary-500"
            >
              Webhook Testing
            </Link>
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default ExpandedTechnology;
