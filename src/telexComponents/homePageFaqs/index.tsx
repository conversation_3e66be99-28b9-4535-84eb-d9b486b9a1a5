"use client";
import React, { useState } from "react";
import {
  Accordion,
  AccordionItem,
  AccordionContent,
  AccordionTrigger,
} from "~/components/ui/accordion";
import { Sparkle, Plus, Minus } from "lucide-react";
import Link from "next/link";

interface FaqItem {
  id: number;
  question: string;
  answer: string;
}

interface FaqProps {
  faq?: FaqItem[];
}

export default function Faq({ faq: externalFaq }: FaqProps) {
  const [openItem, setOpenItem] = useState<string | null>(null);
  // You can set custom FAQs on the page you're using this component by passing the faq props
  const defaultFaq = [
    {
      id: 0,
      question: "How does Telex work?",
      answer: `<p>Telex works on three simple layers that create one powerful workspace:</p>

      <ul style="margin: 16px 0; padding-left: 20px; list-style: none;">
        <li style="margin-bottom: 2px;">
          <strong > • The Foundation:</strong> At its core, Telex is a polished and intuitive communication hub, much like the collaboration tools you already know. You have channels, threads, and direct messages for your team.
        </li>
        <li style="margin-bottom: 2px;">
          <strong > • The Magic:</strong> The difference is our no-code AI builder. This allows you to create specialized "AI colleagues" from easy-to-use templates or from scratch. You can give them names, roles, and specific instructions on what tasks to perform.
        </li>
        <li style="margin-bottom: 2px;">
          <strong > • The Workflow:</strong> You collaborate with these AI colleagues directly in your channels, just as you would with a human. You can <span style="color: #059669; font-weight: 500;">@mention</span> them to assign work, ask questions, or have them generate reports. They work alongside your human team, providing updates and completing tasks 24/7.
        </li>
      </ul>

      <p style="margin-top: 16px;">In short, Telex combines a familiar chat interface with a powerful AI factory, creating a single command center for your entire business.</p>`,
    },
    {
      id: 1,
      question: "Is it only for AI or can my team also use it?",
      answer: `<p style="margin-bottom: 16px;">Telex is designed specifically for your entire team-both human and Al. This is our core principle. We believe the future of work isn't about replacing humans, but about augmenting them with powerful Al teammates.</p>
      <p style="margin-bottom: 16px;">Your human team members will use Telex for all their communication, file sharing, and project collaboration. Your Al colleagues exist in the same channels, ready to be assigned work. This creates a true hybrid team where, for example, your human marketing lead can directly ask your "Al Research Assistant" to pull the latest stats for a report, all in the same project channel.</p>`,
    },
    {
      id: 2,
      question: "Can I create custom agents?",
      answer: `<p style="margin-bottom: 16px;">Absolutely. This is the heart of the Telex platform.</p>
      <p style="margin-bottom: 16px;">While we provide a library of pre-built templates to get you started in minutes (e.g., "Social Media Assistant," "Customer Support Agent"), our intuitive, no-code builder gives you the power to create completely custom AI colleagues tailored to your exact business needs.</p>
      <p style="margin-bottom: 16px;">You can define their purpose, give them access to specific knowledge, and build unique workflows for them to follow. If you can write a list of instructions, you can build a custom agent in Telex.</p>
`,
    },
    {
      id: 3,
      question: "Can it integrate with my current systems?",
      answer: `<p style="margin-bottom: 16px;">Yes. A truly useful AI colleague needs to be able to work with the tools your business already relies on. Our integration philosophy is built on two pillars:</p>
      <ul style="margin: 16px 0; padding-left: 20px; list-style: none;">
        <li style="margin-bottom: 2px;">
          <strong > • Native Integrations:</strong> We offer built-in connections to popular platforms like Google Drive, HubSpot, Salesforce, Jira, and more, allowing your agents to pull data and perform actions in those systems.
        </li>
        <li style="margin-bottom: 2px;">
          <strong > • Open Connectivity:</strong> For everything else, our AI agents can interact with any platform that has an API. You can configure them to send and receive information via webhooks and API calls, effectively giving your AI team the ability to connect to virtually any modern software tool.
        </li>
      </ul>
`,
    },
    {
      id: 4,
      question: "What platforms is it available on?",
      answer: `<p>Telex is designed to be available wherever you work. You can access our platform on:</p>
      <ul style="margin: 16px 0; padding-left: 20px; list-style: none;">
        <li style="margin-bottom: 2px;">
          <strong > • Web:</strong> A full-featured experience in any modern web browser.
        </li>
        <li style="margin-bottom: 2px;">
          <strong > • Desktop:</strong> A dedicated native app for both Windows and macOS.
        </li>
        <li style="margin-bottom: 2px;">
          <strong > • Mobile:</strong> A companion app for iOS and Android for when you're on the go.
        </li>
      </ul>
      <p>Mobile: A companion app for iOS and Android for when you're on the go. Our goal is to provide a seamless experience across all your devices, so you're always connected to your human and AI team members.
</p>
`,
    },
  ];

  const faqList = externalFaq || defaultFaq;

  return (
    <>
      <main className="flex justify-center w-full max-w-3xl m-auto pt-[40px] md:pt-[96px] pb-[50px]">
        <div className="w-full">
          <div className="flex justify-center">
            <div className="px-3 w-fit py-2 text-sm mb-4 font-medium text-[#40258D] border-2 border-[#F1F1FE] rounded-[50px] bg-white flex items-center gap-1">
              <Sparkle className="w-4 h-4" />
              <span>Faqs</span>
            </div>
          </div>

          <div className="text-center mb-10">
            <h2 className="text-[#101828] font-semibold sm:text-4xl text-3xl leading-10">
              Got a Question? We Have The Answer
            </h2>
            <p className="text-[#344054] text-base font-normal leading-6 mt-2 max-w-lg mx-auto">
              Here’s everything you may want to know before you bring Telex
              agents on board.
            </p>
          </div>

          <div className="flex justify-center w-full items-center mb-10">
            <Accordion
              type="single"
              collapsible
              className="w-full flex flex-col gap-[16px]"
              value={openItem ?? undefined}
              onValueChange={(value) => setOpenItem(value)}
            >
              {faqList.map((item) => {
                return (
                  <AccordionItem
                    value={`item-${item.id}`}
                    className="mb-6 border-0"
                    key={item.id}
                  >
                    <AccordionTrigger className="flex justify-between items-center w-full p-8 rounded-lg bg-[#FAFAFF] border border-[#E5E8FF]">
                      <p className="w-full leading-[150%] font-semibold text-[#101828] text-[14px] md:text-[16px] text-left">
                        {item.question}
                      </p>
                      {openItem === `item-${item.id}` ? (
                        <Minus className="text-[#7141F8] w-6 h-6" />
                      ) : (
                        <Plus className="text-[#7141F8] w-6 h-6" />
                      )}
                    </AccordionTrigger>
                    <AccordionContent className="mt-4 px-[20px]">
                      <div
                        className="w-full lg:text-[16px] sm:text-[16px] xs:text-[14px] leading-relaxed text-[#344054]"
                        dangerouslySetInnerHTML={{ __html: item.answer }}
                      />
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
            </Accordion>
          </div>
          <div className="w-full">
            <p className="w-full justify-center items-center text-[18px] text-center inline-block mb-[24px] font-normal text-[#475467]">
              Have a question not listed?
            </p>
            <Link href="/contact">
              <button className="border text-sm rounded-md px-4 mx-auto font-semibold py-2 border-[#7141F8] bg-white text-[#7141F8] block">
                Contact Us
              </button>
            </Link>
          </div>
        </div>
      </main>
    </>
  );
}
