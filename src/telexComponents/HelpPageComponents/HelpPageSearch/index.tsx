"use client";

import React, { useState } from "react";
import { useSearchContext } from "~/store/HelpPageSearchContext";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";
import Link from "next/link";
import HelpPaginationDesktop from "../HelpPaginationDesktop";
import HelpPaginationMobile from "../HelpPaginationMobile";
import { CircleX } from "lucide-react";

const HelpPageSearch: React.FC = () => {
  const { query, results, isLoading, error, searchData, setQuery, setResults } =
    useSearchContext();

  const [currentPage, setCurrentPage] = useState(1);
  const postsPerPage = 3;
  const lastPostIndex = currentPage * postsPerPage;
  const firstPostIndex = lastPostIndex - postsPerPage;
  const currentPosts = results.slice(firstPostIndex, lastPostIndex);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    searchData();
  };

  const handleLinkClick = () => {
    setResults([]);
  };

  return (
    <div className="relative w-full">
      <form onSubmit={handleSubmit} className="mt-4 mb-20 flex justify-center">
        <div className="md:w-[618px] flex gap-3 p-[5px] rounded-[10px] md:border md:border-[#ADAEAF] md:border-opacity-45">
          <Input
            type="text"
            value={query}
            placeholder="Search for articles"
            className="placeholder:text-[#8B8C8D] md:border-none"
            onChange={(e) => setQuery(e.target.value)}
            aria-label="Search"
          />

          <div className="flex gap-4 items-center">
            {query != "" ? (
              <CircleX
                color="#8055F8"
                className="hover:cursor-pointer"
                onClick={() => {
                  setQuery("");
                  setResults([]);
                }}
              />
            ) : null}
            <Button
              type="submit"
              className="bg-[#8055F8] font-normal text-[#F9FAFB] rounded-[8px] hover:bg-opacity-80"
            >
              Search
            </Button>
          </div>
        </div>
      </form>

      {isLoading && <div>Searching...</div>}
      {error && <div>Error: {error}</div>}

      {currentPosts.length > 0 && (
        <div className="absolute top-full left-0 right-0 z-[9999] bg-[#FAFAFA] max-h-[450px] rounded-md mt-8 border border-[#8055F8]">
          {currentPosts.map((article) => (
            <Link
              href={`/help/${article.category_id}/${article.article_id}`}
              key={article.id}
              onClick={handleLinkClick}
            >
              <Alert className="hover:bg-[#ffffff] rounded-none  border-b border-t-0 border-x-0 bg-[#FAFAFA] p-[15px] py-6">
                <div className="flex gap-4 items-center leading-[140%]">
                  <div>
                    <AlertTitle className="text-[16px] font-bold">
                      {article.title}
                    </AlertTitle>
                    <AlertDescription className="text-[16px]">
                      {article.content}
                    </AlertDescription>
                  </div>
                </div>
              </Alert>
            </Link>
          ))}

          <div className="my-8">
            <HelpPaginationDesktop
              totalPosts={results.length}
              postsPerPage={postsPerPage}
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
            />
          </div>

          <div className="my-8">
            <HelpPaginationMobile
              totalPosts={results.length}
              postsPerPage={postsPerPage}
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default HelpPageSearch;
