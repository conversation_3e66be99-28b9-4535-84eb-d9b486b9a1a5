import React from "react";
import Link from "next/link";
import { ChevronRight } from "lucide-react";

interface BreadcrumbItem {
  label: string;
  href: string;
}

interface HelpBreadcrumbsProps {
  items: BreadcrumbItem[];
}

const HelpBreadcrumbs: React.FC<HelpBreadcrumbsProps> = ({ items }) => {
  return (
    <nav className="flex py-5" aria-label="Breadcrumb">
      <ol className="flex items-center space-x-1 text-sm text-gray-600 text-[10px] md:text-[13px]">
        <li>
          <Link href="/help" className="hover:text-[#8860F8]">
            All Articles
          </Link>
        </li>
        {items.map((item, index) => (
          <li key={index}>
            <div className="flex items-center">
              <ChevronRight className="h-4 w-4 mx-2 text-gray-400" />
              <Link
                href={item.href}
                className={
                  index === items.length - 1
                    ? "text-[#8860F8]"
                    : "hover:text-[#8860F8]"
                }
              >
                {item.label}
              </Link>
            </div>
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default HelpBreadcrumbs;
