import React from "react";

interface PaginationProps {
  totalPosts: number;
  postsPerPage: number;
  setCurrentPage: any;
  currentPage: number;
}

const HelpPaginationMobile: React.FC<PaginationProps> = ({
  totalPosts,
  postsPerPage,
  setCurrentPage,
  currentPage,
}) => {
  const pages: number[] = [];
  for (let i = 1; i <= Math.ceil(totalPosts / postsPerPage); i++) {
    pages.push(i);
  }

  return (
    <div className="flex flex-wrap justify-center mt-[1rem] md:hidden">
      {pages.map((page) => (
        <button
          key={page}
          onClick={() => setCurrentPage(page)}
          className={`${
            page === currentPage
              ? "bg-gradient-to-b from-[#8860F8] to-[#7141F8] text-[#fff]"
              : ""
          } w-[38px] h-[38px] text-[16px] my-0 mx-[6px] cursor-pointer bg-[#E3D9FE] border border-[#7141F8] transition-all duration-300 ease-in rounded-md`}
        >
          {page}
        </button>
      ))}
    </div>
  );
};

export default HelpPaginationMobile;
