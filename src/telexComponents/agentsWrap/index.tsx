import React from "react";
import Image from "next/image";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "~/components/ui/button";
import Link from "next/link";

const AgentsWrap = () => {
  return (
    <div className="bg-[#FAFAFF] w-full py-20">
      <div className="flex items-center lg:gap-16 gap-6 container xl:px-12 px-6 md:flex-row flex-col space-y-6">
        <div className="md:w-1/2 w-full">
          <div className="px-3 w-fit py-2 text-sm mb-4 font-medium text-[#40258D] border-2 border-[#F1F1FE] rounded-[50px] bg-white flex items-center gap-1">
            <Bot className="w-4 h-4" />
            <span>Agents</span>
          </div>

          <div>
            <h2 className="text-[#101828] font-semibold sm:text-4xl text-3xl leading-10">
              Agents That Keep Things Running Smoothly
            </h2>
            <p className="text-[#344054] text-base font-normal leading-6 mt-2">
              Say hello to the AI agents working tirelessly behind the scenes,
              so you and your team can focus on moving things forward.
            </p>
            <Link href="/agents">
              <Button className="bg-gradient-to-b from-[#8860F8] to-[#7141F8] hover:bg-opacity-80 text-white font-medium p-6 glex gap-4 mt-4">
                Browse Agents
                <ArrowRight />
              </Button>
            </Link>
          </div>
        </div>

        <div className="md:w-1/2 w-full">
          <Image
            src="/agents-wrap.png"
            alt="agents-wrap"
            width={690}
            height={500}
            className="w-full h-auto"
          />
        </div>
      </div>
    </div>
  );
};

export default AgentsWrap;
