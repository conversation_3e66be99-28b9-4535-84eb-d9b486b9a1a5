"use client";

import React, { createContext, useContext, ReactNode } from "react";
import { useHelpPageSearch } from "~/hooks/useHelpPageSearch";

interface SearchResult {
  id: string;
  title: string;
}

interface SearchContextType {
  query: string;
  results: any[];
  isLoading: boolean;
  error: string | null;
  searchData: () => Promise<void>;
  setQuery: React.Dispatch<React.SetStateAction<string>>;
  setResults: React.Dispatch<React.SetStateAction<SearchResult[]>>;
}

const SearchContext = createContext<SearchContextType | undefined>(undefined);

export const SearchProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const searchState = useHelpPageSearch();

  return (
    <SearchContext.Provider value={searchState}>
      {children}
    </SearchContext.Provider>
  );
};

export const useSearchContext = () => {
  const context = useContext(SearchContext);
  if (context === undefined) {
    throw new Error("useSearchContext must be used within a SearchProvider");
  }
  return context;
};
