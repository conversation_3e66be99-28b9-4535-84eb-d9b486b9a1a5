export interface HowItWorks {
  heading: string;
  body: string;
}

export const howItWorks: HowItWorks[] = [
  {
    heading: "Establish Your Presence",
    body: "Create an account with your details to commence organizing and governing your notifications today.",
  },
  {
    heading: "Harmonize Your Applications",
    body: "Connect Telex with existing apps and systems using our easy integration process.",
  },
  {
    heading: "Custom notification",
    body: "Set up custom notification channels, adjust alert settings, and preferences to fit your team's needs.",
  },
  {
    heading: "Receive alerts",
    body: "Get real-time notifications for critical events and start collaborating immediately.",
  },
];
