// country.ts

export const countries = [
  {
    country: "United States",
    callingCode: "+1",
    states: [
      "Alabama",
      "Alaska",
      "Arizona",
      "Arkansas",
      "California",
      "Colorado",
      "Connecticut",
      "Delaware",
      "Florida",
      "Georgia",
      "Hawaii",
      "Idaho",
      "Illinois",
      "Indiana",
      "Iowa",
      "Kansas",
      "Kentucky",
      "Louisiana",
      "Maine",
      "Maryland",
      "Massachusetts",
      "Michigan",
      "Minnesota",
      "Mississippi",
      "Missouri",
      "Montana",
      "Nebraska",
      "Nevada",
      "New Hampshire",
      "New Jersey",
      "New Mexico",
      "New York",
      "North Carolina",
      "North Dakota",
      "Ohio",
      "Oklahoma",
      "Oregon",
      "Pennsylvania",
      "Rhode Island",
      "South Carolina",
      "South Dakota",
      "Tennessee",
      "Texas",
      "Utah",
      "Vermont",
      "Virginia",
      "Washington",
      "West Virginia",
      "Wisconsin",
      "Wyoming",
    ],
  },
  {
    country: "Canada",
    callingCode: "+1",
    states: [
      "Alberta",
      "British Columbia",
      "Manitoba",
      "New Brunswick",
      "Newfoundland and Labrador",
      "Nova Scotia",
      "Ontario",
      "Prince Edward Island",
      "Quebec",
      "Saskatchewan",
    ],
  },
  {
    country: "Australia",
    callingCode: "+61",
    states: [
      "New South Wales",
      "Victoria",
      "Queensland",
      "Western Australia",
      "South Australia",
      "Tasmania",
      "Australian Capital Territory",
      "Northern Territory",
    ],
  },
  {
    country: "India",
    callingCode: "+91",
    states: [
      "Andhra Pradesh",
      "Arunachal Pradesh",
      "Assam",
      "Bihar",
      "Chhattisgarh",
      "Goa",
      "Gujarat",
      "Haryana",
      "Himachal Pradesh",
      "Jharkhand",
      "Karnataka",
      "Kerala",
      "Madhya Pradesh",
      "Maharashtra",
      "Manipur",
      "Meghalaya",
      "Mizoram",
      "Nagaland",
      "Odisha",
      "Punjab",
      "Rajasthan",
      "Sikkim",
      "Tamil Nadu",
      "Telangana",
      "Tripura",
      "Uttar Pradesh",
      "Uttarakhand",
      "West Bengal",
    ],
  },
  {
    country: "Germany",
    callingCode: "+49",
    states: [
      "Baden-Württemberg",
      "Bavaria",
      "Berlin",
      "Brandenburg",
      "Bremen",
      "Hamburg",
      "Hesse",
      "Lower Saxony",
      "Mecklenburg-Vorpommern",
      "North Rhine-Westphalia",
      "Rhineland-Palatinate",
      "Saarland",
      "Saxony",
      "Saxony-Anhalt",
      "Schleswig-Holstein",
      "Thuringia",
    ],
  },
  {
    country: "Brazil",
    callingCode: "+55",
    states: [
      "Acre",
      "Alagoas",
      "Amapá",
      "Amazonas",
      "Bahia",
      "Ceará",
      "Distrito Federal",
      "Espírito Santo",
      "Goiás",
      "Maranhão",
      "Mato Grosso",
      "Mato Grosso do Sul",
      "Minas Gerais",
      "Pará",
      "Paraíba",
      "Paraná",
      "Pernambuco",
      "Piauí",
      "Rio de Janeiro",
      "Rio Grande do Norte",
      "Rio Grande do Sul",
      "Rondônia",
      "Roraima",
      "Santa Catarina",
      "São Paulo",
      "Sergipe",
      "Tocantins",
    ],
  },
  {
    country: "China",
    callingCode: "+86",
    states: [
      "Anhui",
      "Beijing",
      "Chongqing",
      "Fujian",
      "Gansu",
      "Guangdong",
      "Guangxi",
      "Guizhou",
      "Hainan",
      "Hebei",
      "Heilongjiang",
      "Henan",
      "Hong Kong",
      "Hubei",
      "Hunan",
      "Inner Mongolia",
      "Jiangsu",
      "Jiangxi",
      "Jilin",
      "Liaoning",
      "Macau",
      "Ningxia",
      "Qinghai",
      "Shaanxi",
      "Shandong",
      "Shanghai",
      "Shanxi",
      "Sichuan",
      "Tianjin",
      "Tibet",
      "Xinjiang",
      "Yunnan",
      "Zhejiang",
    ],
  },
  {
    country: "Russia",
    allingCode: "+7",
    states: [
      "Adygea",
      "Altai",
      "Altai Krai",
      "Amur",
      "Arkhangelsk",
      "Astrakhan",
      "Bashkortostan",
      "Belgorod",
      "Bryansk",
      "Buryatia",
      "Chechnya",
      "Chelyabinsk",
      "Chukotka",
      "Chuvashia",
      "Dagestan",
      "Ingushetia",
      "Irkutsk",
      "Ivanovo",
      "Jewish Autonomous Oblast",
      "Kabardino-Balkaria",
      "Kaliningrad",
      "Kalmykia",
      "Kaluga",
      "Kamchatka",
      "Karachay-Cherkessia",
      "Karelia",
      "Kemerovo",
      "Khabarovsk",
      "Khakassia",
      "Khanty-Mansi",
      "Kirov",
      "Komi",
      "Kostroma",
      "Krasnodar",
      "Krasnoyarsk",
      "Kurgan",
      "Kursk",
      "Leningrad",
      "Lipetsk",
      "Magadan",
      "Mari El",
      "Mordovia",
      "Moscow",
      "Moscow Oblast",
      "Murmansk",
      "Nenets",
      "Nizhny Novgorod",
      "North Ossetia-Alania",
      "Novgorod",
      "Novosibirsk",
      "Omsk",
      "Orenburg",
      "Oryol",
      "Penza",
      "Perm",
      "Primorsky",
      "Pskov",
      "Rostov",
      "Ryazan",
      "Saint Petersburg",
      "Sakha",
      "Sakhalin",
      "Samara",
      "Saratov",
      "Smolensk",
      "Stavropol",
      "Sverdlovsk",
      "Tambov",
      "Tatarstan",
      "Tomsk",
      "Tula",
      "Tuva",
      "Tver",
      "Tyumen",
      "Udmurtia",
      "Ulyanovsk",
      "Vladimir",
      "Volgograd",
      "Vologda",
      "Voronezh",
      "Yamalo-Nenets",
      "Yaroslavl",
      "Zabaykalsky",
    ],
  },
  {
    country: "Mexico",
    callingCode: "+52",
    states: [
      "Aguascalientes",
      "Baja California",
      "Baja California Sur",
      "Campeche",
      "Chiapas",
      "Chihuahua",
      "Coahuila",
      "Colima",
      "Durango",
      "Guanajuato",
      "Guerrero",
      "Hidalgo",
      "Jalisco",
      "Mexico City",
      "Mexico State",
      "Michoacán",
      "Morelos",
      "Nayarit",
      "Nuevo León",
      "Oaxaca",
      "Puebla",
      "Querétaro",
      "Quintana Roo",
      "San Luis Potosí",
      "Sinaloa",
      "Sonora",
      "Tabasco",
      "Tamaulipas",
      "Tlaxcala",
      "Veracruz",
      "Yucatán",
      "Zacatecas",
    ],
  },
  {
    country: "Nigeria",
    callingCode: "+234",
    states: [
      "Abia",
      "Adamawa",
      "Akwa Ibom",
      "Anambra",
      "Bauchi",
      "Bayelsa",
      "Benue",
      "Borno",
      "Cross River",
      "Delta",
      "Ebonyi",
      "Edo",
      "Ekiti",
      "Enugu",
      "Federal Capital Territory",
      "Gombe",
      "Imo",
      "Jigawa",
      "Kaduna",
      "Kano",
      "Katsina",
      "Kebbi",
      "Kogi",
      "Kwara",
      "Lagos",
      "Nasarawa",
      "Niger",
      "Ogun",
      "Ondo",
      "Osun",
      "Oyo",
      "Plateau",
      "Rivers",
      "Sokoto",
      "Taraba",
      "Yobe",
      "Zamfara",
    ],
  },
  {
    country: "United Kingdom",
    callingCode: "+44",
    states: ["England", "Northern Ireland", "Scotland", "Wales"],
  },
  {
    country: "South Africa",
    callingCode: "+27",
    states: [
      "Eastern Cape",
      "Free State",
      "Gauteng",
      "KwaZulu-Natal",
      "Limpopo",
      "Mpumalanga",
      "North West",
      "Northern Cape",
      "Western Cape",
    ],
  },
  {
    country: "Argentina",
    callingCode: "+54",
    states: [
      "Buenos Aires",
      "Catamarca",
      "Chaco",
      "Chubut",
      "Córdoba",
      "Corrientes",
      "Entre Ríos",
      "Formosa",
      "Jujuy",
      "La Pampa",
      "La Rioja",
      "Mendoza",
      "Misiones",
      "Neuquén",
      "Río Negro",
      "Salta",
      "San Juan",
      "San Luis",
      "Santa Cruz",
      "Santa Fe",
      "Santiago del Estero",
      "Tierra del Fuego",
      "Tucumán",
    ],
  },
  {
    country: "France",
    callingCode: "+33",
    states: [
      "Auvergne-Rhône-Alpes",
      "Bourgogne-Franche-Comté",
      "Brittany",
      "Centre-Val de Loire",
      "Corsica",
      "Grand Est",
      "Hauts-de-France",
      "Île-de-France",
      "Normandy",
      "Nouvelle-Aquitaine",
      "Occitanie",
      "Pays de la Loire",
      "Provence-Alpes-Côte d'Azur",
    ],
  },
  {
    country: "Italy",
    callingCode: "+39",
    states: [
      "Abruzzo",
      "Aosta Valley",
      "Apulia",
      "Basilicata",
      "Calabria",
      "Campania",
      "Emilia-Romagna",
      "Friuli Venezia Giulia",
      "Lazio",
      "Liguria",
      "Lombardy",
      "Marche",
      "Molise",
      "Piedmont",
      "Sardinia",
      "Sicily",
      "Trentino-Alto Adige/Südtirol",
      "Tuscany",
      "Umbria",
      "Veneto",
    ],
  },
  {
    country: "Japan",
    callingCode: "+81",
    states: [
      "Aichi",
      "Akita",
      "Aomori",
      "Chiba",
      "Ehime",
      "Fukui",
      "Fukuoka",
      "Fukushima",
      "Gifu",
      "Gunma",
      "Hiroshima",
      "Hokkaido",
      "Hyogo",
      "Ibaraki",
      "Ishikawa",
      "Iwate",
      "Kagawa",
      "Kagoshima",
      "Kanagawa",
      "Kochi",
      "Kumamoto",
      "Kyoto",
      "Mie",
      "Miyagi",
      "Miyazaki",
      "Nagano",
      "Nagasaki",
      "Nara",
      "Niigata",
      "Oita",
      "Okayama",
      "Okinawa",
      "Osaka",
      "Saga",
      "Saitama",
      "Shiga",
      "Shimane",
      "Shizuoka",
      "Tochigi",
      "Tokushima",
      "Tokyo",
      "Tottori",
      "Toyama",
      "Wakayama",
      "Yamagata",
      "Yamaguchi",
      "Yamanashi",
    ],
  },
  {
    country: "Indonesia",
    callingCode: "+62",
    states: [
      "Aceh",
      "Bali",
      "Banten",
      "Bengkulu",
      "Central Java",
      "Central Kalimantan",
      "Central Sulawesi",
      "East Java",
      "East Kalimantan",
      "East Nusa Tenggara",
      "Gorontalo",
      "Jakarta",
      "Jambi",
      "Lampung",
      "Maluku",
      "North Kalimantan",
      "North Maluku",
      "North Sulawesi",
      "North Sumatra",
      "Papua",
      "Riau",
      "Riau Islands",
      "Southeast Sulawesi",
      "South Kalimantan",
      "South Sulawesi",
      "South Sumatra",
      "West Java",
      "West Kalimantan",
      "West Nusa Tenggara",
      "West Papua",
      "West Sulawesi",
      "West Sumatra",
      "Yogyakarta",
    ],
  },
  {
    country: "Pakistan",
    callingCode: "+92",
    states: [
      "Azad Kashmir",
      "Balochistan",
      "Gilgit-Baltistan",
      "Islamabad Capital Territory",
      "Khyber Pakhtunkhwa",
      "Punjab",
      "Sindh",
    ],
  },
  {
    country: "Saudi Arabia",
    callingCode: "+966",
    states: [
      "Al Bahah",
      "Al Jawf",
      "Al Madinah",
      "Al-Qassim",
      "Eastern Province",
      "Ha'il",
      "Jizan",
      "Makkah",
      "Najran",
      "Northern Borders",
      "Riyadh",
      "Tabuk",
      "Asir",
    ],
  },
  {
    country: "South Korea",
    callingCode: "+82",
    states: [
      "Busan",
      "Chungbuk",
      "Chungnam",
      "Daegu",
      "Daejeon",
      "Gangwon",
      "Gwangju",
      "Gyeongbuk",
      "Gyeonggi",
      "Gyeongnam",
      "Incheon",
      "Jeju",
      "Jeonbuk",
      "Jeonnam",
      "Sejong",
      "Seoul",
      "Ulsan",
    ],
  },
  {
    country: "Spain",
    callingCode: "+34",
    states: [
      "Andalusia",
      "Aragon",
      "Asturias",
      "Balearic Islands",
      "Basque Country",
      "Canary Islands",
      "Cantabria",
      "Castile and León",
      "Castile-La Mancha",
      "Catalonia",
      "Extremadura",
      "Galicia",
      "La Rioja",
      "Madrid",
      "Murcia",
      "Navarre",
      "Valencian Community",
    ],
  },
  {
    country: "Turkey",
    callingCode: "+90",
    states: [
      "Adana",
      "Adıyaman",
      "Afyonkarahisar",
      "Ağrı",
      "Aksaray",
      "Amasya",
      "Ankara",
      "Antalya",
      "Ardahan",
      "Artvin",
      "Aydın",
      "Balıkesir",
      "Bartın",
      "Batman",
      "Bayburt",
      "Bilecik",
      "Bingöl",
      "Bitlis",
      "Bolu",
      "Burdur",
      "Bursa",
      "Çanakkale",
      "Çankırı",
      "Çorum",
      "Denizli",
      "Diyarbakır",
      "Düzce",
      "Edirne",
      "Elazığ",
      "Erzincan",
      "Erzurum",
      "Eskişehir",
      "Gaziantep",
      "Giresun",
      "Gümüşhane",
      "Hakkari",
      "Hatay",
      "Iğdır",
      "Isparta",
      "Istanbul",
      "İzmir",
      "Kahramanmaraş",
      "Karabük",
      "Karaman",
      "Kars",
      "Kastamonu",
      "Kayseri",
      "Kilis",
      "Kırıkkale",
      "Kırklareli",
      "Kırşehir",
      "Kocaeli",
      "Konya",
      "Kütahya",
      "Malatya",
      "Manisa",
      "Mardin",
      "Mersin",
      "Muğla",
      "Muş",
      "Nevşehir",
      "Niğde",
      "Ordu",
      "Osmaniye",
      "Rize",
      "Sakarya",
      "Samsun",
      "Siirt",
      "Sinop",
      "Sivas",
      "Şanlıurfa",
      "Şırnak",
      "Tekirdağ",
      "Tokat",
      "Trabzon",
      "Tunceli",
      "Uşak",
      "Van",
      "Yalova",
      "Yozgat",
      "Zonguldak",
    ],
  },
  {
    country: "Ukraine",
    callingCode: "+380",
    states: [
      "Cherkasy",
      "Chernihiv",
      "Chernivtsi",
      "Crimea",
      "Dnipropetrovsk",
      "Donetsk",
      "Ivano-Frankivsk",
      "Kharkiv",
      "Kherson",
      "Khmelnytskyi",
      "Kirovohrad",
      "Kyiv",
      "Luhansk",
      "Lviv",
      "Mykolaiv",
      "Odessa",
      "Poltava",
      "Rivne",
      "Sumy",
      "Ternopil",
      "Vinnytsia",
      "Volyn",
      "Zakarpattia",
      "Zaporizhzhia",
      "Zhytomyr",
    ],
  },
  {
    country: "Vietnam",
    callingCode: "+84",
    states: [
      "An Giang",
      "Ba Ria-Vung Tau",
      "Bac Giang",
      "Bac Kan",
      "Bac Lieu",
      "Bac Ninh",
      "Ben Tre",
      "Binh Dinh",
      "Binh Duong",
      "Binh Phuoc",
      "Binh Thuan",
      "Ca Mau",
      "Cao Bang",
      "Da Nang",
      "Dak Lak",
      "Dak Nong",
      "Dien Bien",
      "Dong Nai",
      "Dong Thap",
      "Gia Lai",
      "Ha Giang",
      "Ha Nam",
      "Ha Tinh",
      "Hai Duong",
      "Hai Phong",
      "Hanoi",
      "Hau Giang",
      "Ho Chi Minh City",
      "Hoa Binh",
      "Hung Yen",
      "Khanh Hoa",
      "Kien Giang",
      "Kon Tum",
      "Lai Chau",
      "Lam Dong",
      "Lang Son",
      "Lao Cai",
      "Long An",
      "Nam Dinh",
      "Nghe An",
      "Ninh Binh",
      "Ninh Thuan",
      "Phu Tho",
      "Phu Yen",
      "Quang Binh",
      "Quang Nam",
      "Quang Ngai",
      "Quang Ninh",
      "Quang Tri",
      "Soc Trang",
      "Son La",
      "Tay Ninh",
      "Thai Binh",
      "Thai Nguyen",
      "Thanh Hoa",
      "Thua Thien-Hue",
      "Tien Giang",
      "Tra Vinh",
      "Tuyen Quang",
      "Vinh Long",
      "Vinh Phuc",
      "Yen Bai",
    ],
  },
  {
    country: "Egypt",
    callingCode: "+20",
    states: [
      "Alexandria",
      "Aswan",
      "Asyut",
      "Beheira",
      "Beni Suef",
      "Cairo",
      "Dakahlia",
      "Damietta",
      "Faiyum",
      "Gharbia",
      "Giza",
      "Ismailia",
      "Kafr El Sheikh",
      "Luxor",
      "Matruh",
      "Minya",
      "Monufia",
      "New Valley",
      "North Sinai",
      "Port Said",
      "Qalyubia",
      "Qena",
      "Red Sea",
      "Sharqia",
      "Sohag",
      "South Sinai",
      "Suez",
    ],
  },
  {
    country: "Ethiopia",
    callingCode: "+251",
    states: [
      "Addis Ababa",
      "Afar",
      "Amhara",
      "Benishangul-Gumuz",
      "Dire Dawa",
      "Gambela",
      "Harari",
      "Oromia",
      "Sidama",
      "Somali",
      "Southern Nations, Nationalities, and Peoples' Region",
      "Tigray",
    ],
  },
  {
    country: "Greece",
    callingCode: "+30",
    states: [
      "Achaea",
      "Aetolia-Acarnania",
      "Arcadia",
      "Argolis",
      "Attica",
      "Boeotia",
      "Chania",
      "Corfu",
      "Corinthia",
      "Drama",
      "Euboea",
      "Evrytania",
      "Florina",
      "Halkidiki",
      "Heraklion",
      "Ilia",
      "Imathia",
      "Ioannina",
      "Karditsa",
      "Kastoria",
      "Kavala",
      "Kefalonia",
      "Kilkis",
      "Kozani",
      "Laconia",
      "Larissa",
      "Lasithi",
      "Lefkada",
      "Lesbos",
      "Magnesia",
      "Messenia",
      "Pella",
      "Phthiotis",
      "Pieria",
      "Preveza",
      "Rethymno",
      "Rhodope",
      "Samos",
      "Serres",
      "Thessaloniki",
      "Thesprotia",
      "Trikala",
      "Xanthi",
      "Zakynthos",
    ],
  },
  {
    country: "Malaysia",
    callingCode: "+60",
    states: [
      "Johor",
      "Kedah",
      "Kelantan",
      "Malacca",
      "Negeri Sembilan",
      "Pahang",
      "Penang",
      "Perak",
      "Perlis",
      "Sabah",
      "Sarawak",
      "Selangor",
      "Terengganu",
      "Kuala Lumpur",
      "Labuan",
      "Putrajaya",
    ],
  },
  {
    country: "Myanmar",
    callingCode: "+95",
    states: [
      "Ayeyarwady",
      "Bago",
      "Chin",
      "Kachin",
      "Kayah",
      "Kayin",
      "Magway",
      "Mandalay",
      "Mon",
      "Naypyidaw",
      "Rakhine",
      "Sagaing",
      "Shan",
      "Tanintharyi",
      "Yangon",
    ],
  },
  {
    country: "Nepal",
    callingCode: "+977",
    states: [
      "Bagmati",
      "Gandaki",
      "Karnali",
      "Koshi",
      "Lumbini",
      "Madhesh",
      "Sudurpashchim",
    ],
  },
  {
    country: "Philippines",
    callingCode: "+63",
    states: [
      "Abra",
      "Agusan del Norte",
      "Agusan del Sur",
      "Aklan",
      "Albay",
      "Antique",
      "Apayao",
      "Aurora",
      "Basilan",
      "Bataan",
      "Batanes",
      "Batangas",
      "Benguet",
      "Biliran",
      "Bohol",
      "Bukidnon",
      "Bulacan",
      "Cagayan",
      "Camarines Norte",
      "Camarines Sur",
      "Camiguin",
      "Capiz",
      "Catanduanes",
      "Cavite",
      "Cebu",
      "Compostela Valley",
      "Cotabato",
      "Davao del Norte",
      "Davao del Sur",
      "Davao Occidental",
      "Davao Oriental",
      "Dinagat Islands",
      "Eastern Samar",
      "Guimaras",
      "Ifugao",
      "Ilocos Norte",
      "Ilocos Sur",
      "Iloilo",
      "Isabela",
      "Kalinga",
      "La Union",
      "Laguna",
      "Lanao del Norte",
      "Lanao del Sur",
      "Leyte",
      "Maguindanao",
      "Marinduque",
      "Masbate",
      "Misamis Occidental",
      "Misamis Oriental",
      "Mountain Province",
      "Negros Occidental",
      "Negros Oriental",
      "Northern Samar",
      "Nueva Ecija",
      "Nueva Vizcaya",
      "Occidental Mindoro",
      "Oriental Mindoro",
      "Palawan",
      "Pampanga",
      "Pangasinan",
      "Quezon",
      "Quirino",
      "Rizal",
      "Romblon",
      "Samar",
      "Sarangani",
      "Siquijor",
      "Sorsogon",
      "South Cotabato",
      "Southern Leyte",
      "Sultan Kudarat",
      "Sulu",
      "Surigao del Norte",
      "Surigao del Sur",
      "Tarlac",
      "Tawi-Tawi",
      "Zambales",
      "Zamboanga del Norte",
      "Zamboanga del Sur",
      "Zamboanga Sibugay",
    ],
  },
  {
    country: "Thailand",
    callingCode: "+66",
    states: [
      "Amnat Charoen",
      "Ang Thong",
      "Bangkok",
      "Bueng Kan",
      "Buriram",
      "Chachoengsao",
      "Chai Nat",
      "Chaiyaphum",
      "Chanthaburi",
      "Chiang Mai",
      "Chiang Rai",
      "Chonburi",
      "Chumphon",
      "Kalasin",
      "Kamphaeng Phet",
      "Kanchanaburi",
      "Khon Kaen",
      "Krabi",
      "Lampang",
      "Lamphun",
      "Loei",
      "Lopburi",
      "Mae Hong Son",
      "Maha Sarakham",
      "Mukdahan",
      "Nakhon Nayok",
      "Nakhon Pathom",
      "Nakhon Phanom",
      "Nakhon Ratchasima",
      "Nakhon Sawan",
      "Nakhon Si Thammarat",
      "Nan",
      "Narathiwat",
      "Nong Bua Lam Phu",
      "Nong Khai",
      "Nonthaburi",
      "Pathum Thani",
      "Pattani",
      "Pattaya",
      "Phang Nga",
      "Phatthalung",
      "Phayao",
      "Phetchabun",
    ],
  },
];
