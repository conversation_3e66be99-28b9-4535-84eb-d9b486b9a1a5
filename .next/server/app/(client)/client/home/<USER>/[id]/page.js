(()=>{var e={};e.id=815,e.ids=[815],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},27346:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>f,pages:()=>u,routeModule:()=>d,tree:()=>s}),r(37872),r(41025),r(44796),r(79411),r(40755),r(72406);var n=r(17471),o=r(83436),i=r(75072),a=r.n(i),c=r(88612),l={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>c[e]);r.d(t,l);let s=["",{children:["(client)",{children:["client",{children:["home",{children:["agents",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,37872)),"/Users/<USER>/work/tauri/telex/src/app/(client)/client/home/<USER>/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,41025)),"/Users/<USER>/work/tauri/telex/src/app/(client)/client/home/<USER>"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,44796)),"/Users/<USER>/work/tauri/telex/src/app/(client)/client/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,79411)),"/Users/<USER>/work/tauri/telex/src/app/(client)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,40755)),"/Users/<USER>/work/tauri/telex/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,72406)),"/Users/<USER>/work/tauri/telex/src/app/not-found.tsx"]}],u=["/Users/<USER>/work/tauri/telex/src/app/(client)/client/home/<USER>/[id]/page.tsx"],f="/(client)/client/home/<USER>/[id]/page",p={require:r,loadChunk:()=>Promise.resolve()},d=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/(client)/client/home/<USER>/[id]/page",pathname:"/client/home/<USER>/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:s}})},10715:(e,t,r)=>{Promise.resolve().then(r.bind(r,82069))},57872:(e,t,r)=>{Promise.resolve().then(r.bind(r,49827))},14134:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function o(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function i(e,t,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var c=new o(n,i||e,a),l=r?r+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],c]:e._events[l].push(c):(e._events[l]=c,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),c.prototype.eventNames=function(){var e,n,o=[];if(0===this._eventsCount)return o;for(n in e=this._events)t.call(e,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(e)):o},c.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},c.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},c.prototype.emit=function(e,t,n,o,i,a){var c=r?r+e:e;if(!this._events[c])return!1;var l,s,u=this._events[c],f=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),f){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,o),!0;case 5:return u.fn.call(u.context,t,n,o,i),!0;case 6:return u.fn.call(u.context,t,n,o,i,a),!0}for(s=1,l=Array(f-1);s<f;s++)l[s-1]=arguments[s];u.fn.apply(u.context,l)}else{var p,d=u.length;for(s=0;s<d;s++)switch(u[s].once&&this.removeListener(e,u[s].fn,void 0,!0),f){case 1:u[s].fn.call(u[s].context);break;case 2:u[s].fn.call(u[s].context,t);break;case 3:u[s].fn.call(u[s].context,t,n);break;case 4:u[s].fn.call(u[s].context,t,n,o);break;default:if(!l)for(p=1,l=Array(f-1);p<f;p++)l[p-1]=arguments[p];u[s].fn.apply(u[s].context,l)}}return!0},c.prototype.on=function(e,t,r){return i(this,e,t,r,!1)},c.prototype.once=function(e,t,r){return i(this,e,t,r,!0)},c.prototype.removeListener=function(e,t,n,o){var i=r?r+e:e;if(!this._events[i])return this;if(!t)return a(this,i),this;var c=this._events[i];if(c.fn)c.fn!==t||o&&!c.once||n&&c.context!==n||a(this,i);else{for(var l=0,s=[],u=c.length;l<u;l++)(c[l].fn!==t||o&&!c[l].once||n&&c[l].context!==n)&&s.push(c[l]);s.length?this._events[i]=1===s.length?s[0]:s:a(this,i)}return this},c.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,e.exports=c},73662:(e,t,r)=>{var n=r(90121)(r(1055),"DataView");e.exports=n},7592:(e,t,r)=>{var n=r(27454),o=r(7669),i=r(78917),a=r(82450),c=r(31272);function l(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}l.prototype.clear=n,l.prototype.delete=o,l.prototype.get=i,l.prototype.has=a,l.prototype.set=c,e.exports=l},23223:(e,t,r)=>{var n=r(53506),o=r(62950),i=r(27505),a=r(54384),c=r(85424);function l(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}l.prototype.clear=n,l.prototype.delete=o,l.prototype.get=i,l.prototype.has=a,l.prototype.set=c,e.exports=l},13991:(e,t,r)=>{var n=r(90121)(r(1055),"Map");e.exports=n},85251:(e,t,r)=>{var n=r(31670),o=r(12881),i=r(26350),a=r(95729),c=r(46030);function l(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}l.prototype.clear=n,l.prototype.delete=o,l.prototype.get=i,l.prototype.has=a,l.prototype.set=c,e.exports=l},49709:(e,t,r)=>{var n=r(90121)(r(1055),"Promise");e.exports=n},87114:(e,t,r)=>{var n=r(90121)(r(1055),"Set");e.exports=n},80199:(e,t,r)=>{var n=r(85251),o=r(73735),i=r(50215);function a(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new n;++t<r;)this.add(e[t])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,e.exports=a},62819:(e,t,r)=>{var n=r(23223),o=r(18767),i=r(44),a=r(58601),c=r(32457),l=r(46366);function s(e){var t=this.__data__=new n(e);this.size=t.size}s.prototype.clear=o,s.prototype.delete=i,s.prototype.get=a,s.prototype.has=c,s.prototype.set=l,e.exports=s},35255:(e,t,r)=>{var n=r(1055).Symbol;e.exports=n},99149:(e,t,r)=>{var n=r(1055).Uint8Array;e.exports=n},36981:(e,t,r)=>{var n=r(90121)(r(1055),"WeakMap");e.exports=n},42748:e=>{e.exports=function(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}},74172:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(!t(e[r],r,e))return!1;return!0}},54275:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=0,i=[];++r<n;){var a=e[r];t(a,r,e)&&(i[o++]=a)}return i}},52828:(e,t,r)=>{var n=r(50913);e.exports=function(e,t){return!!(null==e?0:e.length)&&n(e,t,0)>-1}},73659:e=>{e.exports=function(e,t,r){for(var n=-1,o=null==e?0:e.length;++n<o;)if(r(t,e[n]))return!0;return!1}},88771:(e,t,r)=>{var n=r(35035),o=r(88414),i=r(39134),a=r(67079),c=r(7528),l=r(73069),s=Object.prototype.hasOwnProperty;e.exports=function(e,t){var r=i(e),u=!r&&o(e),f=!r&&!u&&a(e),p=!r&&!u&&!f&&l(e),d=r||u||f||p,h=d?n(e.length,String):[],y=h.length;for(var v in e)(t||s.call(e,v))&&!(d&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||c(v,y)))&&h.push(v);return h}},54078:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=Array(n);++r<n;)o[r]=t(e[r],r,e);return o}},49826:e=>{e.exports=function(e,t){for(var r=-1,n=t.length,o=e.length;++r<n;)e[o+r]=t[r];return e}},29436:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}},41992:e=>{e.exports=function(e){return e.split("")}},41035:(e,t,r)=>{var n=r(38592);e.exports=function(e,t){for(var r=e.length;r--;)if(n(e[r][0],t))return r;return -1}},46814:(e,t,r)=>{var n=r(45173);e.exports=function(e,t,r){"__proto__"==t&&n?n(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}},77447:(e,t,r)=>{var n=r(18785),o=r(98387)(n);e.exports=o},83009:(e,t,r)=>{var n=r(77447);e.exports=function(e,t){var r=!0;return n(e,function(e,n,o){return r=!!t(e,n,o)}),r}},38095:(e,t,r)=>{var n=r(60097);e.exports=function(e,t,r){for(var o=-1,i=e.length;++o<i;){var a=e[o],c=t(a);if(null!=c&&(void 0===l?c==c&&!n(c):r(c,l)))var l=c,s=a}return s}},85328:e=>{e.exports=function(e,t,r,n){for(var o=e.length,i=r+(n?1:-1);n?i--:++i<o;)if(t(e[i],i,e))return i;return -1}},39571:(e,t,r)=>{var n=r(49826),o=r(29787);e.exports=function e(t,r,i,a,c){var l=-1,s=t.length;for(i||(i=o),c||(c=[]);++l<s;){var u=t[l];r>0&&i(u)?r>1?e(u,r-1,i,a,c):n(c,u):a||(c[c.length]=u)}return c}},26573:(e,t,r)=>{var n=r(14786)();e.exports=n},18785:(e,t,r)=>{var n=r(26573),o=r(30852);e.exports=function(e,t){return e&&n(e,t,o)}},74522:(e,t,r)=>{var n=r(63126),o=r(59601);e.exports=function(e,t){t=n(t,e);for(var r=0,i=t.length;null!=e&&r<i;)e=e[o(t[r++])];return r&&r==i?e:void 0}},26290:(e,t,r)=>{var n=r(49826),o=r(39134);e.exports=function(e,t,r){var i=t(e);return o(e)?i:n(i,r(e))}},2117:(e,t,r)=>{var n=r(35255),o=r(16388),i=r(67432),a=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?o(e):i(e)}},60492:e=>{e.exports=function(e,t){return e>t}},2473:e=>{e.exports=function(e,t){return null!=e&&t in Object(e)}},50913:(e,t,r)=>{var n=r(85328),o=r(18752),i=r(89426);e.exports=function(e,t,r){return t==t?i(e,t,r):n(e,o,r)}},70960:(e,t,r)=>{var n=r(2117),o=r(5328);e.exports=function(e){return o(e)&&"[object Arguments]"==n(e)}},28589:(e,t,r)=>{var n=r(19058),o=r(5328);e.exports=function e(t,r,i,a,c){return t===r||(null!=t&&null!=r&&(o(t)||o(r))?n(t,r,i,a,e,c):t!=t&&r!=r)}},19058:(e,t,r)=>{var n=r(62819),o=r(76316),i=r(38975),a=r(84473),c=r(20901),l=r(39134),s=r(67079),u=r(73069),f="[object Arguments]",p="[object Array]",d="[object Object]",h=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,y,v,m){var b=l(e),g=l(t),x=b?p:c(e),w=g?p:c(t);x=x==f?d:x,w=w==f?d:w;var O=x==d,j=w==d,S=x==w;if(S&&s(e)){if(!s(t))return!1;b=!0,O=!1}if(S&&!O)return m||(m=new n),b||u(e)?o(e,t,r,y,v,m):i(e,t,x,r,y,v,m);if(!(1&r)){var A=O&&h.call(e,"__wrapped__"),E=j&&h.call(t,"__wrapped__");if(A||E){var k=A?e.value():e,P=E?t.value():t;return m||(m=new n),v(k,P,r,y,m)}}return!!S&&(m||(m=new n),a(e,t,r,y,v,m))}},96104:(e,t,r)=>{var n=r(62819),o=r(28589);e.exports=function(e,t,r,i){var a=r.length,c=a,l=!i;if(null==e)return!c;for(e=Object(e);a--;){var s=r[a];if(l&&s[2]?s[1]!==e[s[0]]:!(s[0]in e))return!1}for(;++a<c;){var u=(s=r[a])[0],f=e[u],p=s[1];if(l&&s[2]){if(void 0===f&&!(u in e))return!1}else{var d=new n;if(i)var h=i(f,p,u,e,t,d);if(!(void 0===h?o(p,f,3,i,d):h))return!1}}return!0}},18752:e=>{e.exports=function(e){return e!=e}},3871:(e,t,r)=>{var n=r(79953),o=r(29299),i=r(36106),a=r(39150),c=/^\[object .+?Constructor\]$/,l=Object.prototype,s=Function.prototype.toString,u=l.hasOwnProperty,f=RegExp("^"+s.call(u).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!i(e)||o(e))&&(n(e)?f:c).test(a(e))}},18491:(e,t,r)=>{var n=r(2117),o=r(98045),i=r(5328),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&o(e.length)&&!!a[n(e)]}},54399:(e,t,r)=>{var n=r(58895),o=r(69322),i=r(11791),a=r(39134),c=r(30902);e.exports=function(e){return"function"==typeof e?e:null==e?i:"object"==typeof e?a(e)?o(e[0],e[1]):n(e):c(e)}},56985:(e,t,r)=>{var n=r(54656),o=r(56094),i=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return o(e);var t=[];for(var r in Object(e))i.call(e,r)&&"constructor"!=r&&t.push(r);return t}},74934:e=>{e.exports=function(e,t){return e<t}},35563:(e,t,r)=>{var n=r(77447),o=r(33472);e.exports=function(e,t){var r=-1,i=o(e)?Array(e.length):[];return n(e,function(e,n,o){i[++r]=t(e,n,o)}),i}},58895:(e,t,r)=>{var n=r(96104),o=r(14191),i=r(28259);e.exports=function(e){var t=o(e);return 1==t.length&&t[0][2]?i(t[0][0],t[0][1]):function(r){return r===e||n(r,e,t)}}},69322:(e,t,r)=>{var n=r(28589),o=r(52430),i=r(75661),a=r(93452),c=r(41449),l=r(28259),s=r(59601);e.exports=function(e,t){return a(e)&&c(t)?l(s(e),t):function(r){var a=o(r,e);return void 0===a&&a===t?i(r,e):n(t,a,3)}}},40517:(e,t,r)=>{var n=r(54078),o=r(74522),i=r(54399),a=r(35563),c=r(9433),l=r(94477),s=r(84565),u=r(11791),f=r(39134);e.exports=function(e,t,r){t=t.length?n(t,function(e){return f(e)?function(t){return o(t,1===e.length?e[0]:e)}:e}):[u];var p=-1;return t=n(t,l(i)),c(a(e,function(e,r,o){return{criteria:n(t,function(t){return t(e)}),index:++p,value:e}}),function(e,t){return s(e,t,r)})}},75777:e=>{e.exports=function(e){return function(t){return null==t?void 0:t[e]}}},83183:(e,t,r)=>{var n=r(74522);e.exports=function(e){return function(t){return n(t,e)}}},20046:e=>{var t=Math.ceil,r=Math.max;e.exports=function(e,n,o,i){for(var a=-1,c=r(t((n-e)/(o||1)),0),l=Array(c);c--;)l[i?c:++a]=e,e+=o;return l}},18514:(e,t,r)=>{var n=r(11791),o=r(88721),i=r(60780);e.exports=function(e,t){return i(o(e,t,n),e+"")}},78567:(e,t,r)=>{var n=r(28842),o=r(45173),i=r(11791),a=o?function(e,t){return o(e,"toString",{configurable:!0,enumerable:!1,value:n(t),writable:!0})}:i;e.exports=a},5750:e=>{e.exports=function(e,t,r){var n=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(r=r>o?o:r)<0&&(r+=o),o=t>r?0:r-t>>>0,t>>>=0;for(var i=Array(o);++n<o;)i[n]=e[n+t];return i}},48638:(e,t,r)=>{var n=r(77447);e.exports=function(e,t){var r;return n(e,function(e,n,o){return!(r=t(e,n,o))}),!!r}},9433:e=>{e.exports=function(e,t){var r=e.length;for(e.sort(t);r--;)e[r]=e[r].value;return e}},35035:e=>{e.exports=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}},60429:(e,t,r)=>{var n=r(35255),o=r(54078),i=r(39134),a=r(60097),c=1/0,l=n?n.prototype:void 0,s=l?l.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(i(t))return o(t,e)+"";if(a(t))return s?s.call(t):"";var r=t+"";return"0"==r&&1/t==-c?"-0":r}},16595:(e,t,r)=>{var n=r(8280),o=/^\s+/;e.exports=function(e){return e?e.slice(0,n(e)+1).replace(o,""):e}},94477:e=>{e.exports=function(e){return function(t){return e(t)}}},22647:(e,t,r)=>{var n=r(80199),o=r(52828),i=r(73659),a=r(85173),c=r(32407),l=r(81812);e.exports=function(e,t,r){var s=-1,u=o,f=e.length,p=!0,d=[],h=d;if(r)p=!1,u=i;else if(f>=200){var y=t?null:c(e);if(y)return l(y);p=!1,u=a,h=new n}else h=t?[]:d;e:for(;++s<f;){var v=e[s],m=t?t(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=h.length;b--;)if(h[b]===m)continue e;t&&h.push(m),d.push(v)}else u(h,m,r)||(h!==d&&h.push(m),d.push(v))}return d}},85173:e=>{e.exports=function(e,t){return e.has(t)}},63126:(e,t,r)=>{var n=r(39134),o=r(93452),i=r(6782),a=r(35164);e.exports=function(e,t){return n(e)?e:o(e,t)?[e]:i(a(e))}},57456:(e,t,r)=>{var n=r(5750);e.exports=function(e,t,r){var o=e.length;return r=void 0===r?o:r,!t&&r>=o?e:n(e,t,r)}},60716:(e,t,r)=>{var n=r(60097);e.exports=function(e,t){if(e!==t){var r=void 0!==e,o=null===e,i=e==e,a=n(e),c=void 0!==t,l=null===t,s=t==t,u=n(t);if(!l&&!u&&!a&&e>t||a&&c&&s&&!l&&!u||o&&c&&s||!r&&s||!i)return 1;if(!o&&!a&&!u&&e<t||u&&r&&i&&!o&&!a||l&&r&&i||!c&&i||!s)return -1}return 0}},84565:(e,t,r)=>{var n=r(60716);e.exports=function(e,t,r){for(var o=-1,i=e.criteria,a=t.criteria,c=i.length,l=r.length;++o<c;){var s=n(i[o],a[o]);if(s){if(o>=l)return s;return s*("desc"==r[o]?-1:1)}}return e.index-t.index}},10853:(e,t,r)=>{var n=r(1055)["__core-js_shared__"];e.exports=n},98387:(e,t,r)=>{var n=r(33472);e.exports=function(e,t){return function(r,o){if(null==r)return r;if(!n(r))return e(r,o);for(var i=r.length,a=t?i:-1,c=Object(r);(t?a--:++a<i)&&!1!==o(c[a],a,c););return r}}},14786:e=>{e.exports=function(e){return function(t,r,n){for(var o=-1,i=Object(t),a=n(t),c=a.length;c--;){var l=a[e?c:++o];if(!1===r(i[l],l,i))break}return t}}},60091:(e,t,r)=>{var n=r(57456),o=r(65963),i=r(28930),a=r(35164);e.exports=function(e){return function(t){var r=o(t=a(t))?i(t):void 0,c=r?r[0]:t.charAt(0),l=r?n(r,1).join(""):t.slice(1);return c[e]()+l}}},40824:(e,t,r)=>{var n=r(54399),o=r(33472),i=r(30852);e.exports=function(e){return function(t,r,a){var c=Object(t);if(!o(t)){var l=n(r,3);t=i(t),r=function(e){return l(c[e],e,c)}}var s=e(t,r,a);return s>-1?c[l?t[s]:s]:void 0}}},95601:(e,t,r)=>{var n=r(20046),o=r(81914),i=r(36452);e.exports=function(e){return function(t,r,a){return a&&"number"!=typeof a&&o(t,r,a)&&(r=a=void 0),t=i(t),void 0===r?(r=t,t=0):r=i(r),a=void 0===a?t<r?1:-1:i(a),n(t,r,a,e)}}},32407:(e,t,r)=>{var n=r(87114),o=r(66183),i=r(81812),a=n&&1/i(new n([,-0]))[1]==1/0?function(e){return new n(e)}:o;e.exports=a},45173:(e,t,r)=>{var n=r(90121),o=function(){try{var e=n(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();e.exports=o},76316:(e,t,r)=>{var n=r(80199),o=r(29436),i=r(85173);e.exports=function(e,t,r,a,c,l){var s=1&r,u=e.length,f=t.length;if(u!=f&&!(s&&f>u))return!1;var p=l.get(e),d=l.get(t);if(p&&d)return p==t&&d==e;var h=-1,y=!0,v=2&r?new n:void 0;for(l.set(e,t),l.set(t,e);++h<u;){var m=e[h],b=t[h];if(a)var g=s?a(b,m,h,t,e,l):a(m,b,h,e,t,l);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!o(t,function(e,t){if(!i(v,t)&&(m===e||c(m,e,r,a,l)))return v.push(t)})){y=!1;break}}else if(!(m===b||c(m,b,r,a,l))){y=!1;break}}return l.delete(e),l.delete(t),y}},38975:(e,t,r)=>{var n=r(35255),o=r(99149),i=r(38592),a=r(76316),c=r(22710),l=r(81812),s=n?n.prototype:void 0,u=s?s.valueOf:void 0;e.exports=function(e,t,r,n,s,f,p){switch(r){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)break;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":if(e.byteLength!=t.byteLength||!f(new o(e),new o(t)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var d=c;case"[object Set]":var h=1&n;if(d||(d=l),e.size!=t.size&&!h)break;var y=p.get(e);if(y)return y==t;n|=2,p.set(e,t);var v=a(d(e),d(t),n,s,f,p);return p.delete(e),v;case"[object Symbol]":if(u)return u.call(e)==u.call(t)}return!1}},84473:(e,t,r)=>{var n=r(1736),o=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,i,a,c){var l=1&r,s=n(e),u=s.length;if(u!=n(t).length&&!l)return!1;for(var f=u;f--;){var p=s[f];if(!(l?p in t:o.call(t,p)))return!1}var d=c.get(e),h=c.get(t);if(d&&h)return d==t&&h==e;var y=!0;c.set(e,t),c.set(t,e);for(var v=l;++f<u;){var m=e[p=s[f]],b=t[p];if(i)var g=l?i(b,m,p,t,e,c):i(m,b,p,e,t,c);if(!(void 0===g?m===b||a(m,b,r,i,c):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=e.constructor,w=t.constructor;x!=w&&"constructor"in e&&"constructor"in t&&!("function"==typeof x&&x instanceof x&&"function"==typeof w&&w instanceof w)&&(y=!1)}return c.delete(e),c.delete(t),y}},71914:e=>{var t="object"==typeof global&&global&&global.Object===Object&&global;e.exports=t},1736:(e,t,r)=>{var n=r(26290),o=r(14161),i=r(30852);e.exports=function(e){return n(e,i,o)}},87142:(e,t,r)=>{var n=r(14618);e.exports=function(e,t){var r=e.__data__;return n(t)?r["string"==typeof t?"string":"hash"]:r.map}},14191:(e,t,r)=>{var n=r(41449),o=r(30852);e.exports=function(e){for(var t=o(e),r=t.length;r--;){var i=t[r],a=e[i];t[r]=[i,a,n(a)]}return t}},90121:(e,t,r)=>{var n=r(3871),o=r(20431);e.exports=function(e,t){var r=o(e,t);return n(r)?r:void 0}},90070:(e,t,r)=>{var n=r(11853)(Object.getPrototypeOf,Object);e.exports=n},16388:(e,t,r)=>{var n=r(35255),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,c=n?n.toStringTag:void 0;e.exports=function(e){var t=i.call(e,c),r=e[c];try{e[c]=void 0;var n=!0}catch(e){}var o=a.call(e);return n&&(t?e[c]=r:delete e[c]),o}},14161:(e,t,r)=>{var n=r(54275),o=r(9448),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols,c=a?function(e){return null==e?[]:n(a(e=Object(e)),function(t){return i.call(e,t)})}:o;e.exports=c},20901:(e,t,r)=>{var n=r(73662),o=r(13991),i=r(49709),a=r(87114),c=r(36981),l=r(2117),s=r(39150),u="[object Map]",f="[object Promise]",p="[object Set]",d="[object WeakMap]",h="[object DataView]",y=s(n),v=s(o),m=s(i),b=s(a),g=s(c),x=l;(n&&x(new n(new ArrayBuffer(1)))!=h||o&&x(new o)!=u||i&&x(i.resolve())!=f||a&&x(new a)!=p||c&&x(new c)!=d)&&(x=function(e){var t=l(e),r="[object Object]"==t?e.constructor:void 0,n=r?s(r):"";if(n)switch(n){case y:return h;case v:return u;case m:return f;case b:return p;case g:return d}return t}),e.exports=x},20431:e=>{e.exports=function(e,t){return null==e?void 0:e[t]}},21091:(e,t,r)=>{var n=r(63126),o=r(88414),i=r(39134),a=r(7528),c=r(98045),l=r(59601);e.exports=function(e,t,r){t=n(t,e);for(var s=-1,u=t.length,f=!1;++s<u;){var p=l(t[s]);if(!(f=null!=e&&r(e,p)))break;e=e[p]}return f||++s!=u?f:!!(u=null==e?0:e.length)&&c(u)&&a(p,u)&&(i(e)||o(e))}},65963:e=>{var t=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return t.test(e)}},27454:(e,t,r)=>{var n=r(22466);e.exports=function(){this.__data__=n?n(null):{},this.size=0}},7669:e=>{e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},78917:(e,t,r)=>{var n=r(22466),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(n){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(t,e)?t[e]:void 0}},82450:(e,t,r)=>{var n=r(22466),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return n?void 0!==t[e]:o.call(t,e)}},31272:(e,t,r)=>{var n=r(22466);e.exports=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=n&&void 0===t?"__lodash_hash_undefined__":t,this}},29787:(e,t,r)=>{var n=r(35255),o=r(88414),i=r(39134),a=n?n.isConcatSpreadable:void 0;e.exports=function(e){return i(e)||o(e)||!!(a&&e&&e[a])}},7528:e=>{var t=/^(?:0|[1-9]\d*)$/;e.exports=function(e,r){var n=typeof e;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&t.test(e))&&e>-1&&e%1==0&&e<r}},81914:(e,t,r)=>{var n=r(38592),o=r(33472),i=r(7528),a=r(36106);e.exports=function(e,t,r){if(!a(r))return!1;var c=typeof t;return("number"==c?!!(o(r)&&i(t,r.length)):"string"==c&&t in r)&&n(r[t],e)}},93452:(e,t,r)=>{var n=r(39134),o=r(60097),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;e.exports=function(e,t){if(n(e))return!1;var r=typeof e;return!!("number"==r||"symbol"==r||"boolean"==r||null==e||o(e))||a.test(e)||!i.test(e)||null!=t&&e in Object(t)}},14618:e=>{e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},29299:(e,t,r)=>{var n=r(10853),o=function(){var e=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();e.exports=function(e){return!!o&&o in e}},54656:e=>{var t=Object.prototype;e.exports=function(e){var r=e&&e.constructor;return e===("function"==typeof r&&r.prototype||t)}},41449:(e,t,r)=>{var n=r(36106);e.exports=function(e){return e==e&&!n(e)}},53506:e=>{e.exports=function(){this.__data__=[],this.size=0}},62950:(e,t,r)=>{var n=r(41035),o=Array.prototype.splice;e.exports=function(e){var t=this.__data__,r=n(t,e);return!(r<0)&&(r==t.length-1?t.pop():o.call(t,r,1),--this.size,!0)}},27505:(e,t,r)=>{var n=r(41035);e.exports=function(e){var t=this.__data__,r=n(t,e);return r<0?void 0:t[r][1]}},54384:(e,t,r)=>{var n=r(41035);e.exports=function(e){return n(this.__data__,e)>-1}},85424:(e,t,r)=>{var n=r(41035);e.exports=function(e,t){var r=this.__data__,o=n(r,e);return o<0?(++this.size,r.push([e,t])):r[o][1]=t,this}},31670:(e,t,r)=>{var n=r(7592),o=r(23223),i=r(13991);e.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},12881:(e,t,r)=>{var n=r(87142);e.exports=function(e){var t=n(this,e).delete(e);return this.size-=t?1:0,t}},26350:(e,t,r)=>{var n=r(87142);e.exports=function(e){return n(this,e).get(e)}},95729:(e,t,r)=>{var n=r(87142);e.exports=function(e){return n(this,e).has(e)}},46030:(e,t,r)=>{var n=r(87142);e.exports=function(e,t){var r=n(this,e),o=r.size;return r.set(e,t),this.size+=r.size==o?0:1,this}},22710:e=>{e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e,n){r[++t]=[n,e]}),r}},28259:e=>{e.exports=function(e,t){return function(r){return null!=r&&r[e]===t&&(void 0!==t||e in Object(r))}}},47560:(e,t,r)=>{var n=r(49356);e.exports=function(e){var t=n(e,function(e){return 500===r.size&&r.clear(),e}),r=t.cache;return t}},22466:(e,t,r)=>{var n=r(90121)(Object,"create");e.exports=n},56094:(e,t,r)=>{var n=r(11853)(Object.keys,Object);e.exports=n},70823:(e,t,r)=>{e=r.nmd(e);var n=r(71914),o=t&&!t.nodeType&&t,i=o&&e&&!e.nodeType&&e,a=i&&i.exports===o&&n.process,c=function(){try{var e=i&&i.require&&i.require("util").types;if(e)return e;return a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=c},67432:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},11853:e=>{e.exports=function(e,t){return function(r){return e(t(r))}}},88721:(e,t,r)=>{var n=r(42748),o=Math.max;e.exports=function(e,t,r){return t=o(void 0===t?e.length-1:t,0),function(){for(var i=arguments,a=-1,c=o(i.length-t,0),l=Array(c);++a<c;)l[a]=i[t+a];a=-1;for(var s=Array(t+1);++a<t;)s[a]=i[a];return s[t]=r(l),n(e,this,s)}}},1055:(e,t,r)=>{var n=r(71914),o="object"==typeof self&&self&&self.Object===Object&&self,i=n||o||Function("return this")();e.exports=i},73735:e=>{e.exports=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this}},50215:e=>{e.exports=function(e){return this.__data__.has(e)}},81812:e=>{e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e){r[++t]=e}),r}},60780:(e,t,r)=>{var n=r(78567),o=r(65421)(n);e.exports=o},65421:e=>{var t=Date.now;e.exports=function(e){var r=0,n=0;return function(){var o=t(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return e.apply(void 0,arguments)}}},18767:(e,t,r)=>{var n=r(23223);e.exports=function(){this.__data__=new n,this.size=0}},44:e=>{e.exports=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}},58601:e=>{e.exports=function(e){return this.__data__.get(e)}},32457:e=>{e.exports=function(e){return this.__data__.has(e)}},46366:(e,t,r)=>{var n=r(23223),o=r(13991),i=r(85251);e.exports=function(e,t){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([e,t]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(e,t),this.size=r.size,this}},89426:e=>{e.exports=function(e,t,r){for(var n=r-1,o=e.length;++n<o;)if(e[n]===t)return n;return -1}},28930:(e,t,r)=>{var n=r(41992),o=r(65963),i=r(54014);e.exports=function(e){return o(e)?i(e):n(e)}},6782:(e,t,r)=>{var n=r(47560),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,a=n(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(o,function(e,r,n,o){t.push(n?o.replace(i,"$1"):r||e)}),t});e.exports=a},59601:(e,t,r)=>{var n=r(60097),o=1/0;e.exports=function(e){if("string"==typeof e||n(e))return e;var t=e+"";return"0"==t&&1/e==-o?"-0":t}},39150:e=>{var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},8280:e=>{var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},54014:e=>{var t="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",o="[^"+t+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",c="(?:"+r+"|"+n+")?",l="[\\ufe0e\\ufe0f]?",s="(?:\\u200d(?:"+[o,i,a].join("|")+")"+l+c+")*",u=RegExp(n+"(?="+n+")|(?:"+[o+r+"?",r,i,a,"["+t+"]"].join("|")+")"+(l+c+s),"g");e.exports=function(e){return e.match(u)||[]}},28842:e=>{e.exports=function(e){return function(){return e}}},46797:(e,t,r)=>{var n=r(36106),o=r(37912),i=r(65847),a=Math.max,c=Math.min;e.exports=function(e,t,r){var l,s,u,f,p,d,h=0,y=!1,v=!1,m=!0;if("function"!=typeof e)throw TypeError("Expected a function");function b(t){var r=l,n=s;return l=s=void 0,h=t,f=e.apply(n,r)}function g(e){var r=e-d,n=e-h;return void 0===d||r>=t||r<0||v&&n>=u}function x(){var e,r,n,i=o();if(g(i))return w(i);p=setTimeout(x,(e=i-d,r=i-h,n=t-e,v?c(n,u-r):n))}function w(e){return(p=void 0,m&&l)?b(e):(l=s=void 0,f)}function O(){var e,r=o(),n=g(r);if(l=arguments,s=this,d=r,n){if(void 0===p)return h=e=d,p=setTimeout(x,t),y?b(e):f;if(v)return clearTimeout(p),p=setTimeout(x,t),b(d)}return void 0===p&&(p=setTimeout(x,t)),f}return t=i(t)||0,n(r)&&(y=!!r.leading,u=(v="maxWait"in r)?a(i(r.maxWait)||0,t):u,m="trailing"in r?!!r.trailing:m),O.cancel=function(){void 0!==p&&clearTimeout(p),h=0,l=d=s=p=void 0},O.flush=function(){return void 0===p?f:w(o())},O}},38592:e=>{e.exports=function(e,t){return e===t||e!=e&&t!=t}},81881:(e,t,r)=>{var n=r(74172),o=r(83009),i=r(54399),a=r(39134),c=r(81914);e.exports=function(e,t,r){var l=a(e)?n:o;return r&&c(e,t,r)&&(t=void 0),l(e,i(t,3))}},56092:(e,t,r)=>{var n=r(40824)(r(77634));e.exports=n},77634:(e,t,r)=>{var n=r(85328),o=r(54399),i=r(99345),a=Math.max;e.exports=function(e,t,r){var c=null==e?0:e.length;if(!c)return -1;var l=null==r?0:i(r);return l<0&&(l=a(c+l,0)),n(e,o(t,3),l)}},93496:(e,t,r)=>{var n=r(39571),o=r(96577);e.exports=function(e,t){return n(o(e,t),1)}},52430:(e,t,r)=>{var n=r(74522);e.exports=function(e,t,r){var o=null==e?void 0:n(e,t);return void 0===o?r:o}},75661:(e,t,r)=>{var n=r(2473),o=r(21091);e.exports=function(e,t){return null!=e&&o(e,t,n)}},11791:e=>{e.exports=function(e){return e}},88414:(e,t,r)=>{var n=r(70960),o=r(5328),i=Object.prototype,a=i.hasOwnProperty,c=i.propertyIsEnumerable,l=n(function(){return arguments}())?n:function(e){return o(e)&&a.call(e,"callee")&&!c.call(e,"callee")};e.exports=l},39134:e=>{var t=Array.isArray;e.exports=t},33472:(e,t,r)=>{var n=r(79953),o=r(98045);e.exports=function(e){return null!=e&&o(e.length)&&!n(e)}},98254:(e,t,r)=>{var n=r(2117),o=r(5328);e.exports=function(e){return!0===e||!1===e||o(e)&&"[object Boolean]"==n(e)}},67079:(e,t,r)=>{e=r.nmd(e);var n=r(1055),o=r(38136),i=t&&!t.nodeType&&t,a=i&&e&&!e.nodeType&&e,c=a&&a.exports===i?n.Buffer:void 0,l=c?c.isBuffer:void 0;e.exports=l||o},20569:(e,t,r)=>{var n=r(28589);e.exports=function(e,t){return n(e,t)}},79953:(e,t,r)=>{var n=r(2117),o=r(36106);e.exports=function(e){if(!o(e))return!1;var t=n(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},98045:e=>{e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},92952:(e,t,r)=>{var n=r(28768);e.exports=function(e){return n(e)&&e!=+e}},26301:e=>{e.exports=function(e){return null==e}},28768:(e,t,r)=>{var n=r(2117),o=r(5328);e.exports=function(e){return"number"==typeof e||o(e)&&"[object Number]"==n(e)}},36106:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},5328:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},56054:(e,t,r)=>{var n=r(2117),o=r(90070),i=r(5328),a=Object.prototype,c=Function.prototype.toString,l=a.hasOwnProperty,s=c.call(Object);e.exports=function(e){if(!i(e)||"[object Object]"!=n(e))return!1;var t=o(e);if(null===t)return!0;var r=l.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&c.call(r)==s}},20664:(e,t,r)=>{var n=r(2117),o=r(39134),i=r(5328);e.exports=function(e){return"string"==typeof e||!o(e)&&i(e)&&"[object String]"==n(e)}},60097:(e,t,r)=>{var n=r(2117),o=r(5328);e.exports=function(e){return"symbol"==typeof e||o(e)&&"[object Symbol]"==n(e)}},73069:(e,t,r)=>{var n=r(18491),o=r(94477),i=r(70823),a=i&&i.isTypedArray,c=a?o(a):n;e.exports=c},30852:(e,t,r)=>{var n=r(88771),o=r(56985),i=r(33472);e.exports=function(e){return i(e)?n(e):o(e)}},39989:e=>{e.exports=function(e){var t=null==e?0:e.length;return t?e[t-1]:void 0}},96577:(e,t,r)=>{var n=r(54078),o=r(54399),i=r(35563),a=r(39134);e.exports=function(e,t){return(a(e)?n:i)(e,o(t,3))}},91663:(e,t,r)=>{var n=r(46814),o=r(18785),i=r(54399);e.exports=function(e,t){var r={};return t=i(t,3),o(e,function(e,o,i){n(r,o,t(e,o,i))}),r}},50454:(e,t,r)=>{var n=r(38095),o=r(60492),i=r(11791);e.exports=function(e){return e&&e.length?n(e,i,o):void 0}},32063:(e,t,r)=>{var n=r(38095),o=r(60492),i=r(54399);e.exports=function(e,t){return e&&e.length?n(e,i(t,2),o):void 0}},49356:(e,t,r)=>{var n=r(85251);function o(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw TypeError("Expected a function");var r=function(){var n=arguments,o=t?t.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=e.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,e.exports=o},91654:(e,t,r)=>{var n=r(38095),o=r(74934),i=r(11791);e.exports=function(e){return e&&e.length?n(e,i,o):void 0}},32759:(e,t,r)=>{var n=r(38095),o=r(54399),i=r(74934);e.exports=function(e,t){return e&&e.length?n(e,o(t,2),i):void 0}},66183:e=>{e.exports=function(){}},37912:(e,t,r)=>{var n=r(1055);e.exports=function(){return n.Date.now()}},30902:(e,t,r)=>{var n=r(75777),o=r(83183),i=r(93452),a=r(59601);e.exports=function(e){return i(e)?n(a(e)):o(e)}},37167:(e,t,r)=>{var n=r(95601)();e.exports=n},23420:(e,t,r)=>{var n=r(29436),o=r(54399),i=r(48638),a=r(39134),c=r(81914);e.exports=function(e,t,r){var l=a(e)?n:i;return r&&c(e,t,r)&&(t=void 0),l(e,o(t,3))}},567:(e,t,r)=>{var n=r(39571),o=r(40517),i=r(18514),a=r(81914),c=i(function(e,t){if(null==e)return[];var r=t.length;return r>1&&a(e,t[0],t[1])?t=[]:r>2&&a(t[0],t[1],t[2])&&(t=[t[0]]),o(e,n(t,1),[])});e.exports=c},9448:e=>{e.exports=function(){return[]}},38136:e=>{e.exports=function(){return!1}},37253:(e,t,r)=>{var n=r(46797),o=r(36106);e.exports=function(e,t,r){var i=!0,a=!0;if("function"!=typeof e)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(e,t,{leading:i,maxWait:t,trailing:a})}},36452:(e,t,r)=>{var n=r(65847),o=1/0;e.exports=function(e){return e?(e=n(e))===o||e===-o?(e<0?-1:1)*17976931348623157e292:e==e?e:0:0===e?e:0}},99345:(e,t,r)=>{var n=r(36452);e.exports=function(e){var t=n(e),r=t%1;return t==t?r?t-r:t:0}},65847:(e,t,r)=>{var n=r(16595),o=r(36106),i=r(60097),a=0/0,c=/^[-+]0x[0-9a-f]+$/i,l=/^0b[01]+$/i,s=/^0o[0-7]+$/i,u=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(i(e))return a;if(o(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=o(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=n(e);var r=l.test(e);return r||s.test(e)?u(e.slice(2),r?2:8):c.test(e)?a:+e}},35164:(e,t,r)=>{var n=r(60429);e.exports=function(e){return null==e?"":n(e)}},82891:(e,t,r)=>{var n=r(54399),o=r(22647);e.exports=function(e,t){return e&&e.length?o(e,n(t,2)):[]}},20886:(e,t,r)=>{var n=r(60091)("toUpperCase");e.exports=n},48830:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(87593).Z)("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},16034:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(87593).Z)("Forward",[["polyline",{points:"15 17 20 12 15 7",key:"1w3sku"}],["path",{d:"M4 18v-2a4 4 0 0 1 4-4h12",key:"jmiej9"}]])},1563:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(87593).Z)("Hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]])},56502:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(87593).Z)("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},3124:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(87593).Z)("MessageCircleMore",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}],["path",{d:"M8 12h.01",key:"czm47f"}],["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 12h.01",key:"1l6xoz"}]])},4602:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(87593).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},97737:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(87593).Z)("SmilePlus",[["path",{d:"M22 11v1a10 10 0 1 1-9-10",key:"ew0xw9"}],["path",{d:"M8 14s1.5 2 4 2 4-2 4-2",key:"1y1vjs"}],["line",{x1:"9",x2:"9.01",y1:"9",y2:"9",key:"yxxnd0"}],["line",{x1:"15",x2:"15.01",y1:"9",y2:"9",key:"1p4y9e"}],["path",{d:"M16 5h6",key:"1vod17"}],["path",{d:"M19 2v6",key:"4bpg5p"}]])},85528:(e,t)=>{"use strict";/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=Symbol.for("react.element"),n=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),l=Symbol.for("react.context"),s=Symbol.for("react.server_context"),u=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),h=Symbol.for("react.lazy");Symbol.for("react.offscreen"),Symbol.for("react.module.reference"),t.isFragment=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case o:case a:case i:case f:case p:return e;default:switch(e=e&&e.$$typeof){case s:case l:case u:case h:case d:case c:return e;default:return t}}case n:return t}}}(e)===o}},64591:(e,t,r)=>{"use strict";e.exports=r(85528)},12764:(e,t,r)=>{"use strict";r.d(t,{Z:()=>d});var n=r(12902),o=r(87489),i=r(5160),a=r(21912),c=r(32437),l=r(42451),s=r(61347),u=r(31426),f=r(27940),p=r(36804);function d({name:e}){let{state:t,dispatch:r}=(0,l.useContext)(c.R),{orgData:d}=t,[h,y]=(0,l.useState)(!1),v=async()=>{localStorage.clear(),window.location.href="/auth/login"};return n.jsx("div",{className:"",children:(0,n.jsxs)(o.J2,{children:[n.jsx(o.xo,{asChild:!0,children:(0,n.jsxs)("div",{className:"flex items-center gap-1 cursor-pointer",children:[n.jsx("h6",{className:"text-lg leading-[26px] font-semibold text-white",children:e||d?.name}),n.jsx(i.Z,{className:"text-white mt-1"})]})}),(0,n.jsxs)(o.yk,{align:"start",className:"w-[270px] p-0 rounded-md shadow-xl",onClick:()=>r({type:u.a.CHANNEL_BAR,payload:!t?.channelBar}),children:[(0,n.jsxs)("div",{className:"",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 p-3 border-b font-medium text-sm",children:[n.jsx("div",{className:"size-9 rounded border overflow-hidden flex items-center justify-center",children:d?.logo_url?n.jsx(s.default,{src:d?.logo_url,alt:"",width:50,height:50,unoptimized:!0,className:"size-9"}):n.jsx("h3",{className:"text-primary-500 font-bold text-sm",children:(0,f.Qm)(d?.name)})}),n.jsx("div",{className:"text-sm",children:d?.name})]}),(0,n.jsxs)("div",{className:"flex gap-2 text-xs px-3 py-3 font-medium border-b hover:bg-blue-500 hover:text-white cursor-pointer",children:["\uD83D\uDE80",(0,n.jsxs)("div",{children:["Your ",n.jsx("strong",{children:"Pro trial"})," lasts through"," ",n.jsx("strong",{children:"June 13"}),".",n.jsx("a",{href:"#",className:"text-blue-600 block hover:underline",children:"See upgrade options"})]})]})]}),(0,n.jsxs)("ul",{className:"text-sm pb-3",children:[(0,n.jsxs)("li",{onClick:()=>r({type:u.a.INVITE_MODAL,payload:!0}),className:"hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 text-[15px]",children:["Invite people to ",d?.name]}),n.jsx("li",{className:"hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]",children:n.jsx(p.default,{href:"/client/settings/personal/account",children:"settings"})}),(0,n.jsxs)("li",{className:"relative hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]",onMouseEnter:()=>y(!0),onMouseLeave:()=>y(!1),children:["Apps ",n.jsx(a.Z,{className:"h-4 w-4"}),h&&(0,n.jsxs)("ul",{className:"absolute left-full top-0 w-[200px] bg-white text-black rounded-[7px] shadow-lg border border-[#E6EAEF] overflow-hidden",children:[n.jsx("li",{className:"hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]",children:"App1"}),n.jsx("li",{className:"hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]",children:"App2"}),n.jsx("li",{className:"hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]",children:"App3"}),n.jsx("li",{className:"hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]",children:"App4"})]})]}),n.jsx("li",{onClick:v,className:"hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 font-medium text-[15px]",children:"Sign out"})]})]})]})})}},82069:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>dJ});var n={};r.r(n),r.d(n,{scaleBand:()=>nS,scaleDiverging:()=>function e(){var t=is(cE()(o0));return t.copy=function(){return cj(t,e())},nb.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=ig(cE()).domain([.1,1,10]);return t.copy=function(){return cj(t,e()).base(t.base())},nb.apply(t,arguments)},scaleDivergingPow:()=>ck,scaleDivergingSqrt:()=>cP,scaleDivergingSymlog:()=>function e(){var t=iO(cE());return t.copy=function(){return cj(t,e()).constant(t.constant())},nb.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e=+e)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,oJ),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,oJ):[0,1],is(n)},scaleImplicit:()=>nO,scaleLinear:()=>iu,scaleLog:()=>function e(){let t=ig(o3()).domain([1,10]);return t.copy=()=>o5(t,e()).base(t.base()),nm.apply(t,arguments),t},scaleOrdinal:()=>nj,scalePoint:()=>nA,scalePow:()=>ik,scaleQuantile:()=>function e(){var t,r=[],n=[],o=[];function i(){var e=0,t=Math.max(1,n.length);for(o=Array(t-1);++e<t;)o[e-1]=function(e,t,r=oc){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,o=(n-1)*t,i=Math.floor(o),a=+r(e[i],i,e);return a+(+r(e[i+1],i+1,e)-a)*(o-i)}}(r,e/t);return a}function a(e){return null==e||isNaN(e=+e)?t:n[os(o,e)]}return a.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?o[t-1]:r[0],t<o.length?o[t]:r[r.length-1]]},a.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t=+t)||r.push(t);return r.sort(on),i()},a.range=function(e){return arguments.length?(n=Array.from(e),i()):n.slice()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.quantiles=function(){return o.slice()},a.copy=function(){return e().domain(r).range(n).unknown(t)},nm.apply(a,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,o=1,i=[.5],a=[0,1];function c(e){return null!=e&&e<=e?a[os(i,e,0,o)]:t}function l(){var e=-1;for(i=Array(o);++e<o;)i[e]=((e+1)*n-(e-o)*r)/(o+1);return c}return c.domain=function(e){return arguments.length?([r,n]=e,r=+r,n=+n,l()):[r,n]},c.range=function(e){return arguments.length?(o=(a=Array.from(e)).length-1,l()):a.slice()},c.invertExtent=function(e){var t=a.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,i[0]]:t>=o?[i[o-1],n]:[i[t-1],i[t]]},c.unknown=function(e){return arguments.length&&(t=e),c},c.thresholds=function(){return i.slice()},c.copy=function(){return e().domain([r,n]).range(a).unknown(t)},nm.apply(is(c),arguments)},scaleRadial:()=>function e(){var t,r=o6(),n=[0,1],o=!1;function i(e){var n,i=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(i)?t:o?Math.round(i):i}return i.invert=function(e){return r.invert(iM(e))},i.domain=function(e){return arguments.length?(r.domain(e),i):r.domain()},i.range=function(e){return arguments.length?(r.range((n=Array.from(e,oJ)).map(iM)),i):n.slice()},i.rangeRound=function(e){return i.range(e).round(!0)},i.round=function(e){return arguments.length?(o=!!e,i):o},i.clamp=function(e){return arguments.length?(r.clamp(e),i):r.clamp()},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return e(r.domain(),n).round(o).clamp(r.clamp()).unknown(t)},nm.apply(i,arguments),is(i)},scaleSequential:()=>function e(){var t=is(cO()(o0));return t.copy=function(){return cj(t,e())},nb.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=ig(cO()).domain([1,10]);return t.copy=function(){return cj(t,e()).base(t.base())},nb.apply(t,arguments)},scaleSequentialPow:()=>cS,scaleSequentialQuantile:()=>function e(){var t=[],r=o0;function n(e){if(null!=e&&!isNaN(e=+e))return r((os(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r=+r)||t.push(r);return t.sort(on),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t=+t)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n=+n)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t=+t))){if(t<=0||n<2)return iT(e);if(t>=1)return i_(e);var n,o=(n-1)*t,i=Math.floor(o),a=i_((function e(t,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(t.length-1,o)),!(n<=r&&r<=o))return t;for(i=void 0===i?iN:function(e=on){if(e===on)return iN;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(i);o>n;){if(o-n>600){let a=o-n+1,c=r-n+1,l=Math.log(a),s=.5*Math.exp(2*l/3),u=.5*Math.sqrt(l*s*(a-s)/a)*(c-a/2<0?-1:1),f=Math.max(n,Math.floor(r-c*s/a+u)),p=Math.min(o,Math.floor(r+(a-c)*s/a+u));e(t,r,f,p,i)}let a=t[r],c=n,l=o;for(iC(t,n,r),i(t[o],a)>0&&iC(t,n,o);c<l;){for(iC(t,c,l),++c,--l;0>i(t[c],a);)++c;for(;i(t[l],a)>0;)--l}0===i(t[n],a)?iC(t,n,l):iC(t,++l,o),l<=r&&(n=l+1),r<=l&&(o=l-1)}return t})(e,i).subarray(0,i+1));return a+(iT(e.subarray(i+1))-a)*(o-i)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},nb.apply(n,arguments)},scaleSequentialSqrt:()=>cA,scaleSequentialSymlog:()=>function e(){var t=iO(cO());return t.copy=function(){return cj(t,e()).constant(t.constant())},nb.apply(t,arguments)},scaleSqrt:()=>iP,scaleSymlog:()=>function e(){var t=iO(o3());return t.copy=function(){return o5(t,e()).constant(t.constant())},nm.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],o=1;function i(e){return null!=e&&e<=e?n[os(r,e,0,o)]:t}return i.domain=function(e){return arguments.length?(o=Math.min((r=Array.from(e)).length,n.length-1),i):r.slice()},i.range=function(e){return arguments.length?(n=Array.from(e),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return e().domain(r).range(n).unknown(t)},nm.apply(i,arguments)},scaleTime:()=>cx,scaleUtc:()=>cw,tickFormat:()=>il});var o=r(12902),i=r(42451),a=r.n(i),c=r(61347),l=r(90803),s=r(4602),u=r(97737),f=r(3124),p=r(16034),d=r(70408),h=r(48830),y=r(34099),v=r(8659),m=r(28442),b=r(37253),g=r.n(b),x=r(20664),w=r.n(x),O=r(92952),j=r.n(O),S=r(52430),A=r.n(S),E=r(28768),k=r.n(E),P=r(26301),M=r.n(P),_=function(e){return 0===e?0:e>0?1:-1},T=function(e){return w()(e)&&e.indexOf("%")===e.length-1},N=function(e){return k()(e)&&!j()(e)},C=function(e){return N(e)||w()(e)},D=0,I=function(e){var t=++D;return"".concat(e||"").concat(t)},B=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!N(e)&&!w()(e))return n;if(T(e)){var i=e.indexOf("%");r=t*parseFloat(e.slice(0,i))/100}else r=+e;return j()(r)&&(r=n),o&&r>t&&(r=t),r},R=function(e){if(!e)return null;var t=Object.keys(e);return t&&t.length?e[t[0]]:null},L=function(e){if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++){if(r[e[n]])return!0;r[e[n]]=!0}return!1},F=function(e,t){return N(e)&&N(t)?function(r){return e+r*(t-e)}:function(){return t}};function z(e,t,r){return e&&e.length?e.find(function(e){return e&&("function"==typeof t?t(e):A()(e,t))===r}):null}var $=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]},U=r(79953),q=r.n(U),Z=r(36106),W=r.n(Z),H=r(64591);function X(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}function V(e){return(V="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var Y=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],G=["points","pathLength"],K={svg:["viewBox","children"],polygon:G,polyline:G},J=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],Q=function(e,t){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,i.isValidElement)(e)&&(r=e.props),!W()(r))return null;var n={};return Object.keys(r).forEach(function(e){J.includes(e)&&(n[e]=t||function(t){return r[e](r,t)})}),n},ee=function(e,t,r){if(!W()(e)||"object"!==V(e))return null;var n=null;return Object.keys(e).forEach(function(o){var i=e[o];J.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(e){return i(t,r,e),null})}),n},et=["children"],er=["children"];function en(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var eo={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},ei=function(e){return"string"==typeof e?e:e?e.displayName||e.name||"Component":""},ea=null,ec=null,el=function e(t){if(t===ea&&Array.isArray(ec))return ec;var r=[];return i.Children.forEach(t,function(t){M()(t)||((0,H.isFragment)(t)?r=r.concat(e(t.props.children)):r.push(t))}),ec=r,ea=t,r};function es(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(function(e){return ei(e)}):[ei(t)],el(e).forEach(function(e){var t=A()(e,"type.displayName")||A()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}function eu(e,t){var r=es(e,t);return r&&r[0]}var ef=function(e){if(!e||!e.props)return!1;var t=e.props,r=t.width,n=t.height;return!!N(r)&&!(r<=0)&&!!N(n)&&!(n<=0)},ep=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],ed=function(e,t,r,n){var o,i=null!==(o=null==K?void 0:K[n])&&void 0!==o?o:[];return t.startsWith("data-")||!q()(e)&&(n&&i.includes(t)||Y.includes(t))||r&&J.includes(t)},eh=function(e,t,r){if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,i.isValidElement)(e)&&(n=e.props),!W()(n))return null;var o={};return Object.keys(n).forEach(function(e){var i;ed(null===(i=n)||void 0===i?void 0:i[e],e,t,r)&&(o[e]=n[e])}),o},ey=function e(t,r){if(t===r)return!0;var n=i.Children.count(t);if(n!==i.Children.count(r))return!1;if(0===n)return!0;if(1===n)return ev(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var a=t[o],c=r[o];if(Array.isArray(a)||Array.isArray(c)){if(!e(a,c))return!1}else if(!ev(a,c))return!1}return!0},ev=function(e,t){if(M()(e)&&M()(t))return!0;if(!M()(e)&&!M()(t)){var r=e.props||{},n=r.children,o=en(r,et),i=t.props||{},a=i.children,c=en(i,er);if(n&&a)return X(o,c)&&ey(n,a);if(!n&&!a)return X(o,c)}return!1},em=function(e,t){var r=[],n={};return el(e).forEach(function(e,o){if(e&&e.type&&w()(e.type)&&ep.indexOf(e.type)>=0)r.push(e);else if(e){var i=ei(e.type),a=t[i]||{},c=a.handler,l=a.once;if(c&&(!l||!n[i])){var s=c(e,i,o);r.push(s),n[i]=!0}}}),r},eb=function(e){var t=e&&e.type;return t&&eo[t]?eo[t]:null};function eg(e){return(eg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ex(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ew(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ex(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=eg(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eg(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eg(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ex(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function eO(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var ej=(0,i.forwardRef)(function(e,t){var r,n=e.aspect,o=e.initialDimension,c=void 0===o?{width:-1,height:-1}:o,l=e.width,s=void 0===l?"100%":l,u=e.height,f=void 0===u?"100%":u,p=e.minWidth,d=void 0===p?0:p,h=e.minHeight,y=e.maxHeight,v=e.children,b=e.debounce,x=void 0===b?0:b,w=e.id,O=e.className,j=e.onResize,S=e.style,A=(0,i.useRef)(null),E=(0,i.useRef)();E.current=j,(0,i.useImperativeHandle)(t,function(){return Object.defineProperty(A.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),A.current},configurable:!0})});var k=function(e){if(Array.isArray(e))return e}(r=(0,i.useState)({containerWidth:c.width,containerHeight:c.height}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{for(i=(r=r.call(e)).next;!(l=(n=i.call(r)).done)&&(c.push(n.value),2!==c.length);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(r,2)||function(e,t){if(e){if("string"==typeof e)return eO(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return eO(e,2)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),P=k[0],M=k[1],_=(0,i.useCallback)(function(e,t){M(function(r){var n=Math.round(e),o=Math.round(t);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,i.useEffect)(function(){var e=function(e){var t,r=e[0].contentRect,n=r.width,o=r.height;_(n,o),null===(t=E.current)||void 0===t||t.call(E,n,o)};x>0&&(e=g()(e,x,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),r=A.current.getBoundingClientRect();return _(r.width,r.height),t.observe(A.current),function(){t.disconnect()}},[_,x]);var N=(0,i.useMemo)(function(){var e=P.containerWidth,t=P.containerHeight;if(e<0||t<0)return null;$(T(s)||T(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",s,f),$(!n||n>0,"The aspect(%s) must be greater than zero.",n);var r=T(s)?e:s,o=T(f)?t:f;n&&n>0&&(r?o=r/n:o&&(r=o*n),y&&o>y&&(o=y)),$(r>0||o>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,o,s,f,d,h,n);var c=!Array.isArray(v)&&ei(v.type).endsWith("Chart");return a().Children.map(v,function(e){return a().isValidElement(e)?(0,i.cloneElement)(e,ew({width:r,height:o},c?{style:ew({height:"100%",width:"100%",maxHeight:o,maxWidth:r},e.props.style)}:{})):e})},[n,v,f,y,h,d,P,s]);return a().createElement("div",{id:w?"".concat(w):void 0,className:(0,m.Z)("recharts-responsive-container",O),style:ew(ew({},void 0===S?{}:S),{},{width:s,height:f,minWidth:d,minHeight:h,maxHeight:y}),ref:A},N)}),eS=r(37167),eA=r.n(eS),eE=r(567),ek=r.n(eE);function eP(e,t){if(!e)throw Error("Invariant failed")}var eM=["children","width","height","viewBox","className","style","title","desc"];function e_(){return(e_=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function eT(e){var t=e.children,r=e.width,n=e.height,o=e.viewBox,i=e.className,c=e.style,l=e.title,s=e.desc,u=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,eM),f=o||{width:r,height:n,x:0,y:0},p=(0,m.Z)("recharts-surface",i);return a().createElement("svg",e_({},eh(u,!0,"svg"),{className:p,width:r,height:n,style:c,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),a().createElement("title",null,l),a().createElement("desc",null,s),t)}var eN=["children","className"];function eC(){return(eC=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var eD=a().forwardRef(function(e,t){var r=e.children,n=e.className,o=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,eN),i=(0,m.Z)("recharts-layer",n);return a().createElement("g",eC({className:i},eh(o,!0),{ref:t}),r)});function eI(e){return(eI="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eB(){return(eB=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function eR(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function eL(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eF(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eL(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=eI(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eI(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eI(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eL(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ez(e){return Array.isArray(e)&&C(e[0])&&C(e[1])?e.join(" ~ "):e}var e$=function(e){var t=e.separator,r=void 0===t?" : ":t,n=e.contentStyle,o=e.itemStyle,i=void 0===o?{}:o,c=e.labelStyle,l=e.payload,s=e.formatter,u=e.itemSorter,f=e.wrapperClassName,p=e.labelClassName,d=e.label,h=e.labelFormatter,y=e.accessibilityLayer,v=eF({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),b=eF({margin:0},void 0===c?{}:c),g=!M()(d),x=g?d:"",w=(0,m.Z)("recharts-default-tooltip",f),O=(0,m.Z)("recharts-tooltip-label",p);return g&&h&&null!=l&&(x=h(d,l)),a().createElement("div",eB({className:w,style:v},void 0!==y&&y?{role:"status","aria-live":"assertive"}:{}),a().createElement("p",{className:O,style:b},a().isValidElement(x)?x:"".concat(x)),function(){if(l&&l.length){var e=(u?ek()(l,u):l).map(function(e,t){if("none"===e.type)return null;var n=eF({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},i),o=e.formatter||s||ez,c=e.value,u=e.name,f=c,p=u;if(o&&null!=f&&null!=p){var d=o(c,u,e,t,l);if(Array.isArray(d)){var h=function(e){if(Array.isArray(e))return e}(d)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{for(i=(r=r.call(e)).next;!(l=(n=i.call(r)).done)&&(c.push(n.value),2!==c.length);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(d,2)||function(e,t){if(e){if("string"==typeof e)return eR(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return eR(e,2)}}(d,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();f=h[0],p=h[1]}else f=d}return a().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(t),style:n},C(p)?a().createElement("span",{className:"recharts-tooltip-item-name"},p):null,C(p)?a().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,a().createElement("span",{className:"recharts-tooltip-item-value"},f),a().createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return a().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null}())};function eU(e){return(eU="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eq(e,t,r){var n;return(n=function(e,t){if("object"!=eU(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eU(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==eU(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var eZ="recharts-tooltip-wrapper",eW={visibility:"hidden"};function eH(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,o=e.offsetTopLeft,i=e.position,a=e.reverseDirection,c=e.tooltipDimension,l=e.viewBox,s=e.viewBoxDimension;if(i&&N(i[n]))return i[n];var u=r[n]-c-o,f=r[n]+o;return t[n]?a[n]?u:f:a[n]?u<l[n]?Math.max(f,l[n]):Math.max(u,l[n]):f+c>l[n]+s?Math.max(u,l[n]):Math.max(f,l[n])}function eX(e){return(eX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eV(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eY(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eV(Object(r),!0).forEach(function(t){eQ(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eV(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function eG(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(eG=function(){return!!e})()}function eK(e){return(eK=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function eJ(e,t){return(eJ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function eQ(e,t,r){return(t=e0(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function e0(e){var t=function(e,t){if("object"!=eX(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eX(t)?t:t+""}var e1=function(e){var t;function r(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r);for(var e,t,n,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=r,n=[].concat(i),t=eK(t),eQ(e=function(e,t){if(t&&("object"===eX(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,eG()?Reflect.construct(t,n||[],eK(this).constructor):t.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),eQ(e,"handleKeyDown",function(t){if("Escape"===t.key){var r,n,o,i;e.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=e.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(o=null===(i=e.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})}}),e}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&eJ(e,t)}(r,e),t=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();(Math.abs(e.width-this.state.lastBoundingBox.width)>1||Math.abs(e.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:e.width,height:e.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var e,t;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(e=this.props.coordinate)||void 0===e?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null===(t=this.props.coordinate)||void 0===t?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var e,t,r,n,o,i,c,l,s,u,f,p,d,h,y,v,b,g,x,w=this,O=this.props,j=O.active,S=O.allowEscapeViewBox,A=O.animationDuration,E=O.animationEasing,k=O.children,P=O.coordinate,M=O.hasPayload,_=O.isAnimationActive,T=O.offset,C=O.position,D=O.reverseDirection,I=O.useTranslate3d,B=O.viewBox,R=O.wrapperStyle,L=(p=(e={allowEscapeViewBox:S,coordinate:P,offsetTopLeft:T,position:C,reverseDirection:D,tooltipBox:this.state.lastBoundingBox,useTranslate3d:I,viewBox:B}).allowEscapeViewBox,d=e.coordinate,h=e.offsetTopLeft,y=e.position,v=e.reverseDirection,b=e.tooltipBox,g=e.useTranslate3d,x=e.viewBox,b.height>0&&b.width>0&&d?(r=(t={translateX:u=eH({allowEscapeViewBox:p,coordinate:d,key:"x",offsetTopLeft:h,position:y,reverseDirection:v,tooltipDimension:b.width,viewBox:x,viewBoxDimension:x.width}),translateY:f=eH({allowEscapeViewBox:p,coordinate:d,key:"y",offsetTopLeft:h,position:y,reverseDirection:v,tooltipDimension:b.height,viewBox:x,viewBoxDimension:x.height}),useTranslate3d:g}).translateX,n=t.translateY,s={transform:t.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):s=eW,{cssProperties:s,cssClasses:(i=(o={translateX:u,translateY:f,coordinate:d}).coordinate,c=o.translateX,l=o.translateY,(0,m.Z)(eZ,eq(eq(eq(eq({},"".concat(eZ,"-right"),N(c)&&i&&N(i.x)&&c>=i.x),"".concat(eZ,"-left"),N(c)&&i&&N(i.x)&&c<i.x),"".concat(eZ,"-bottom"),N(l)&&i&&N(i.y)&&l>=i.y),"".concat(eZ,"-top"),N(l)&&i&&N(i.y)&&l<i.y)))}),F=L.cssClasses,z=L.cssProperties,$=eY(eY({transition:_&&j?"transform ".concat(A,"ms ").concat(E):void 0},z),{},{pointerEvents:"none",visibility:!this.state.dismissed&&j&&M?"visible":"hidden",position:"absolute",top:0,left:0},R);return a().createElement("div",{tabIndex:-1,className:F,style:$,ref:function(e){w.wrapperNode=e}},k)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,e0(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent),e2={isSsr:!0,get:function(e){return e2[e]},set:function(e,t){if("string"==typeof e)e2[e]=t;else{var r=Object.keys(e);r&&r.length&&r.forEach(function(t){e2[t]=e[t]})}}},e4=r(82891),e5=r.n(e4);function e3(e,t,r){return!0===t?e5()(e,r):q()(t)?e5()(e,t):e}function e6(e){return(e6="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function e7(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function e8(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?e7(Object(r),!0).forEach(function(t){tr(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):e7(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function e9(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(e9=function(){return!!e})()}function te(e){return(te=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function tt(e,t){return(tt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function tr(e,t,r){return(t=tn(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function tn(e){var t=function(e,t){if("object"!=e6(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=e6(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==e6(t)?t:t+""}function to(e){return e.dataKey}var ti=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=te(e),function(e,t){if(t&&("object"===e6(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,e9()?Reflect.construct(e,t||[],te(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&tt(e,t)}(r,e),t=[{key:"render",value:function(){var e,t=this,r=this.props,n=r.active,o=r.allowEscapeViewBox,i=r.animationDuration,c=r.animationEasing,l=r.content,s=r.coordinate,u=r.filterNull,f=r.isAnimationActive,p=r.offset,d=r.payload,h=r.payloadUniqBy,y=r.position,v=r.reverseDirection,m=r.useTranslate3d,b=r.viewBox,g=r.wrapperStyle,x=null!=d?d:[];u&&x.length&&(x=e3(d.filter(function(e){return null!=e.value&&(!0!==e.hide||t.props.includeHidden)}),h,to));var w=x.length>0;return a().createElement(e1,{allowEscapeViewBox:o,animationDuration:i,animationEasing:c,isAnimationActive:f,active:n,coordinate:s,hasPayload:w,offset:p,position:y,reverseDirection:v,useTranslate3d:m,viewBox:b,wrapperStyle:g},(e=e8(e8({},this.props),{},{payload:x}),a().isValidElement(l)?a().cloneElement(l,e):"function"==typeof l?a().createElement(l,e):a().createElement(e$,e)))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,tn(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent);tr(ti,"displayName","Tooltip"),tr(ti,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!e2.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var ta=r(20886),tc=r.n(ta);let tl=Math.cos,ts=Math.sin,tu=Math.sqrt,tf=Math.PI,tp=2*tf,td={draw(e,t){let r=tu(t/tf);e.moveTo(r,0),e.arc(0,0,r,0,tp)}},th=tu(1/3),ty=2*th,tv=ts(tf/10)/ts(7*tf/10),tm=ts(tp/10)*tv,tb=-tl(tp/10)*tv,tg=tu(3),tx=tu(3)/2,tw=1/tu(12),tO=(tw/2+1)*3;function tj(e){return function(){return e}}let tS=Math.PI,tA=2*tS,tE=tA-1e-6;function tk(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class tP{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?tk:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return tk;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,o,i){this._append`C${+e},${+t},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(e,t,r,n,o){if(e=+e,t=+t,r=+r,n=+n,(o=+o)<0)throw Error(`negative radius: ${o}`);let i=this._x1,a=this._y1,c=r-e,l=n-t,s=i-e,u=a-t,f=s*s+u*u;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(f>1e-6){if(Math.abs(u*c-l*s)>1e-6&&o){let p=r-i,d=n-a,h=c*c+l*l,y=Math.sqrt(h),v=Math.sqrt(f),m=o*Math.tan((tS-Math.acos((h+f-(p*p+d*d))/(2*y*v)))/2),b=m/v,g=m/y;Math.abs(b-1)>1e-6&&this._append`L${e+b*s},${t+b*u}`,this._append`A${o},${o},0,0,${+(u*p>s*d)},${this._x1=e+g*c},${this._y1=t+g*l}`}else this._append`L${this._x1=e},${this._y1=t}`}}arc(e,t,r,n,o,i){if(e=+e,t=+t,i=!!i,(r=+r)<0)throw Error(`negative radius: ${r}`);let a=r*Math.cos(n),c=r*Math.sin(n),l=e+a,s=t+c,u=1^i,f=i?n-o:o-n;null===this._x1?this._append`M${l},${s}`:(Math.abs(this._x1-l)>1e-6||Math.abs(this._y1-s)>1e-6)&&this._append`L${l},${s}`,r&&(f<0&&(f=f%tA+tA),f>tE?this._append`A${r},${r},0,1,${u},${e-a},${t-c}A${r},${r},0,1,${u},${this._x1=l},${this._y1=s}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=tS)},${u},${this._x1=e+r*Math.cos(o)},${this._y1=t+r*Math.sin(o)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function tM(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new tP(t)}function t_(e){return(t_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}tP.prototype,tu(3),tu(3);var tT=["type","size","sizeType"];function tN(){return(tN=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function tC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function tD(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?tC(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=t_(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=t_(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==t_(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tC(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var tI={symbolCircle:td,symbolCross:{draw(e,t){let r=tu(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=tu(t/ty),n=r*th;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=tu(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=tu(.8908130915292852*t),n=tm*r,o=tb*r;e.moveTo(0,-r),e.lineTo(n,o);for(let t=1;t<5;++t){let i=tp*t/5,a=tl(i),c=ts(i);e.lineTo(c*r,-a*r),e.lineTo(a*n-c*o,c*n+a*o)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-tu(t/(3*tg));e.moveTo(0,2*r),e.lineTo(-tg*r,-r),e.lineTo(tg*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=tu(t/tO),n=r/2,o=r*tw,i=r*tw+r,a=-n;e.moveTo(n,o),e.lineTo(n,i),e.lineTo(a,i),e.lineTo(-.5*n-tx*o,tx*n+-.5*o),e.lineTo(-.5*n-tx*i,tx*n+-.5*i),e.lineTo(-.5*a-tx*i,tx*a+-.5*i),e.lineTo(-.5*n+tx*o,-.5*o-tx*n),e.lineTo(-.5*n+tx*i,-.5*i-tx*n),e.lineTo(-.5*a+tx*i,-.5*i-tx*a),e.closePath()}}},tB=Math.PI/180,tR=function(e,t,r){if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*tB;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},tL=function(e){var t,r=e.type,n=void 0===r?"circle":r,o=e.size,i=void 0===o?64:o,c=e.sizeType,l=void 0===c?"area":c,s=tD(tD({},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,tT)),{},{type:n,size:i,sizeType:l}),u=s.className,f=s.cx,p=s.cy,d=eh(s,!0);return f===+f&&p===+p&&i===+i?a().createElement("path",tN({},d,{className:(0,m.Z)("recharts-symbols",u),transform:"translate(".concat(f,", ").concat(p,")"),d:(t=tI["symbol".concat(tc()(n))]||td,(function(e,t){let r=null,n=tM(o);function o(){let o;if(r||(r=o=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),o)return r=null,o+""||null}return e="function"==typeof e?e:tj(e||td),t="function"==typeof t?t:tj(void 0===t?64:+t),o.type=function(t){return arguments.length?(e="function"==typeof t?t:tj(t),o):e},o.size=function(e){return arguments.length?(t="function"==typeof e?e:tj(+e),o):t},o.context=function(e){return arguments.length?(r=null==e?null:e,o):r},o})().type(t).size(tR(i,l,n))())})):null};function tF(e){return(tF="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function tz(){return(tz=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function t$(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function tU(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(tU=function(){return!!e})()}function tq(e){return(tq=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function tZ(e,t){return(tZ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function tW(e,t,r){return(t=tH(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function tH(e){var t=function(e,t){if("object"!=tF(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=tF(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==tF(t)?t:t+""}tL.registerSymbol=function(e,t){tI["symbol".concat(tc()(e))]=t};var tX=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=tq(e),function(e,t){if(t&&("object"===tF(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,tU()?Reflect.construct(e,t||[],tq(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&tZ(e,t)}(r,e),t=[{key:"renderIcon",value:function(e){var t=this.props.inactiveColor,r=32/6,n=32/3,o=e.inactive?t:e.color;if("plainline"===e.type)return a().createElement("line",{strokeWidth:4,fill:"none",stroke:o,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===e.type)return a().createElement("path",{strokeWidth:4,fill:"none",stroke:o,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===e.type)return a().createElement("path",{stroke:"none",fill:o,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(a().isValidElement(e.legendIcon)){var i=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?t$(Object(r),!0).forEach(function(t){tW(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t$(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return delete i.legendIcon,a().cloneElement(e.legendIcon,i)}return a().createElement(tL,{fill:o,cx:16,cy:16,size:32,sizeType:"diameter",type:e.type})}},{key:"renderItems",value:function(){var e=this,t=this.props,r=t.payload,n=t.iconSize,o=t.layout,i=t.formatter,c=t.inactiveColor,l={x:0,y:0,width:32,height:32},s={display:"horizontal"===o?"inline-block":"block",marginRight:10},u={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(t,r){var o=t.formatter||i,f=(0,m.Z)(tW(tW({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",t.inactive));if("none"===t.type)return null;var p=q()(t.value)?null:t.value;$(!q()(t.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var d=t.inactive?c:t.color;return a().createElement("li",tz({className:f,style:s,key:"legend-item-".concat(r)},ee(e.props,t,r)),a().createElement(eT,{width:n,height:n,viewBox:l,style:u},e.renderIcon(t)),a().createElement("span",{className:"recharts-legend-item-text",style:{color:d}},o?o(p,t,r):p))})}},{key:"render",value:function(){var e=this.props,t=e.payload,r=e.layout,n=e.align;return t&&t.length?a().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,tH(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent);function tV(e){return(tV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}tW(tX,"displayName","Legend"),tW(tX,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var tY=["ref"];function tG(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function tK(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?tG(Object(r),!0).forEach(function(t){t2(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tG(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function tJ(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,t4(n.key),n)}}function tQ(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(tQ=function(){return!!e})()}function t0(e){return(t0=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function t1(e,t){return(t1=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function t2(e,t,r){return(t=t4(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function t4(e){var t=function(e,t){if("object"!=tV(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=tV(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==tV(t)?t:t+""}function t5(e){return e.value}var t3=function(e){var t,r;function n(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n);for(var e,t,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return t=n,r=[].concat(i),t=t0(t),t2(e=function(e,t){if(t&&("object"===tV(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,tQ()?Reflect.construct(t,r||[],t0(this).constructor):t.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),e}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&t1(e,t)}(n,e),t=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();return e.height=this.wrapperNode.offsetHeight,e.width=this.wrapperNode.offsetWidth,e}return null}},{key:"updateBBox",value:function(){var e=this.props.onBBoxUpdate,t=this.getBBox();t?(Math.abs(t.width-this.lastBoundingBox.width)>1||Math.abs(t.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=t.width,this.lastBoundingBox.height=t.height,e&&e(t)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,e&&e(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?tK({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(e){var t,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,c=n.margin,l=n.chartWidth,s=n.chartHeight;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(t="center"===i&&"vertical"===o?{left:((l||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:c&&c.right||0}:{left:c&&c.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(r="middle"===a?{top:((s||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),tK(tK({},t),r)}},{key:"render",value:function(){var e=this,t=this.props,r=t.content,n=t.width,o=t.height,i=t.wrapperStyle,c=t.payloadUniqBy,l=t.payload,s=tK(tK({position:"absolute",width:n||"auto",height:o||"auto"},this.getDefaultPosition(i)),i);return a().createElement("div",{className:"recharts-legend-wrapper",style:s,ref:function(t){e.wrapperNode=t}},function(e,t){if(a().isValidElement(e))return a().cloneElement(e,t);if("function"==typeof e)return a().createElement(e,t);t.ref;var r=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(t,tY);return a().createElement(tX,r)}(r,tK(tK({},this.props),{},{payload:e3(l,c,t5)})))}}],r=[{key:"getWithHeight",value:function(e,t){var r=tK(tK({},this.defaultProps),e.props).layout;return"vertical"===r&&N(e.props.height)?{height:e.props.height}:"horizontal"===r?{width:e.props.width||t}:null}}],t&&tJ(n.prototype,t),r&&tJ(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function t6(){return(t6=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}t2(t3,"displayName","Legend"),t2(t3,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var t7=function(e){var t=e.cx,r=e.cy,n=e.r,o=e.className,a=(0,m.Z)("recharts-dot",o);return t===+t&&r===+r&&n===+n?i.createElement("circle",t6({},eh(e,!1),Q(e),{className:a,cx:t,cy:r,r:n})):null},t8=r(79143),t9=r.n(t8),re=Object.getOwnPropertyNames,rt=Object.getOwnPropertySymbols,rr=Object.prototype.hasOwnProperty;function rn(e,t){return function(r,n,o){return e(r,n,o)&&t(r,n,o)}}function ro(e){return function(t,r,n){if(!t||!r||"object"!=typeof t||"object"!=typeof r)return e(t,r,n);var o=n.cache,i=o.get(t),a=o.get(r);if(i&&a)return i===r&&a===t;o.set(t,r),o.set(r,t);var c=e(t,r,n);return o.delete(t),o.delete(r),c}}function ri(e){return re(e).concat(rt(e))}var ra=Object.hasOwn||function(e,t){return rr.call(e,t)};function rc(e,t){return e===t||!e&&!t&&e!=e&&t!=t}var rl=Object.getOwnPropertyDescriptor,rs=Object.keys;function ru(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function rf(e,t){return rc(e.getTime(),t.getTime())}function rp(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function rd(e,t){return e===t}function rh(e,t,r){var n,o,i=e.size;if(i!==t.size)return!1;if(!i)return!0;for(var a=Array(i),c=e.entries(),l=0;(n=c.next())&&!n.done;){for(var s=t.entries(),u=!1,f=0;(o=s.next())&&!o.done;){if(a[f]){f++;continue}var p=n.value,d=o.value;if(r.equals(p[0],d[0],l,f,e,t,r)&&r.equals(p[1],d[1],p[0],d[0],e,t,r)){u=a[f]=!0;break}f++}if(!u)return!1;l++}return!0}function ry(e,t,r){var n=rs(e),o=n.length;if(rs(t).length!==o)return!1;for(;o-- >0;)if(!rO(e,t,r,n[o]))return!1;return!0}function rv(e,t,r){var n,o,i,a=ri(e),c=a.length;if(ri(t).length!==c)return!1;for(;c-- >0;)if(!rO(e,t,r,n=a[c])||(o=rl(e,n),i=rl(t,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function rm(e,t){return rc(e.valueOf(),t.valueOf())}function rb(e,t){return e.source===t.source&&e.flags===t.flags}function rg(e,t,r){var n,o,i=e.size;if(i!==t.size)return!1;if(!i)return!0;for(var a=Array(i),c=e.values();(n=c.next())&&!n.done;){for(var l=t.values(),s=!1,u=0;(o=l.next())&&!o.done;){if(!a[u]&&r.equals(n.value,o.value,n.value,o.value,e,t,r)){s=a[u]=!0;break}u++}if(!s)return!1}return!0}function rx(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function rw(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function rO(e,t,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!e.$$typeof||!!t.$$typeof)||ra(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var rj=Array.isArray,rS="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,rA=Object.assign,rE=Object.prototype.toString.call.bind(Object.prototype.toString),rk=rP();function rP(e){void 0===e&&(e={});var t,r,n,o,i,a,c,l,s,u,f,p,d,h=e.circular,y=e.createInternalComparator,v=e.createState,m=e.strict,b=(r=(t=function(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,o={areArraysEqual:n?rv:ru,areDatesEqual:rf,areErrorsEqual:rp,areFunctionsEqual:rd,areMapsEqual:n?rn(rh,rv):rh,areNumbersEqual:rc,areObjectsEqual:n?rv:ry,arePrimitiveWrappersEqual:rm,areRegExpsEqual:rb,areSetsEqual:n?rn(rg,rv):rg,areTypedArraysEqual:n?rv:rx,areUrlsEqual:rw};if(r&&(o=rA({},o,r(o))),t){var i=ro(o.areArraysEqual),a=ro(o.areMapsEqual),c=ro(o.areObjectsEqual),l=ro(o.areSetsEqual);o=rA({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:l})}return o}(e)).areArraysEqual,n=t.areDatesEqual,o=t.areErrorsEqual,i=t.areFunctionsEqual,a=t.areMapsEqual,c=t.areNumbersEqual,l=t.areObjectsEqual,s=t.arePrimitiveWrappersEqual,u=t.areRegExpsEqual,f=t.areSetsEqual,p=t.areTypedArraysEqual,d=t.areUrlsEqual,function(e,t,h){if(e===t)return!0;if(null==e||null==t)return!1;var y=typeof e;if(y!==typeof t)return!1;if("object"!==y)return"number"===y?c(e,t,h):"function"===y&&i(e,t,h);var v=e.constructor;if(v!==t.constructor)return!1;if(v===Object)return l(e,t,h);if(rj(e))return r(e,t,h);if(null!=rS&&rS(e))return p(e,t,h);if(v===Date)return n(e,t,h);if(v===RegExp)return u(e,t,h);if(v===Map)return a(e,t,h);if(v===Set)return f(e,t,h);var m=rE(e);return"[object Date]"===m?n(e,t,h):"[object RegExp]"===m?u(e,t,h):"[object Map]"===m?a(e,t,h):"[object Set]"===m?f(e,t,h):"[object Object]"===m?"function"!=typeof e.then&&"function"!=typeof t.then&&l(e,t,h):"[object URL]"===m?d(e,t,h):"[object Error]"===m?o(e,t,h):"[object Arguments]"===m?l(e,t,h):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&s(e,t,h)}),g=y?y(b):function(e,t,r,n,o,i,a){return b(e,t,a)};return function(e){var t=e.circular,r=e.comparator,n=e.createState,o=e.equals,i=e.strict;if(n)return function(e,a){var c=n(),l=c.cache;return r(e,a,{cache:void 0===l?t?new WeakMap:void 0:l,equals:o,meta:c.meta,strict:i})};if(t)return function(e,t){return r(e,t,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(e,t){return r(e,t,a)}}({circular:void 0!==h&&h,comparator:b,createState:v,equals:g,strict:void 0!==m&&m})}function rM(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>t)e(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function r_(e){return(r_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function rT(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function rN(e){return(rN="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function rC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function rD(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rC(Object(r),!0).forEach(function(t){rI(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rC(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function rI(e,t,r){var n;return(n=function(e,t){if("object"!==rN(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==rN(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"===rN(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}rP({strict:!0}),rP({circular:!0}),rP({circular:!0,strict:!0}),rP({createInternalComparator:function(){return rc}}),rP({strict:!0,createInternalComparator:function(){return rc}}),rP({circular:!0,createInternalComparator:function(){return rc}}),rP({circular:!0,createInternalComparator:function(){return rc},strict:!0});var rB=function(e){return e},rR=function(e,t){return Object.keys(t).reduce(function(r,n){return rD(rD({},r),{},rI({},n,e(n,t[n])))},{})},rL=function(e,t,r){return e.map(function(e){return"".concat(e.replace(/([A-Z])/g,function(e){return"-".concat(e.toLowerCase())})," ").concat(t,"ms ").concat(r)}).join(",")},rF=function(e,t,r,n,o,i,a,c){};function rz(e,t){if(e){if("string"==typeof e)return r$(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return r$(e,t)}}function r$(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var rU=function(e,t){return[0,3*e,3*t-6*e,3*e-3*t+1]},rq=function(e,t){return e.map(function(e,r){return e*Math.pow(t,r)}).reduce(function(e,t){return e+t})},rZ=function(e,t){return function(r){return rq(rU(e,t),r)}},rW=function(){for(var e,t,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],c=n[2],l=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,c=1,l=1;break;case"ease":i=.25,a=.1,c=.25,l=1;break;case"ease-in":i=.42,a=0,c=1,l=1;break;case"ease-out":i=.42,a=0,c=.58,l=1;break;case"ease-in-out":i=0,a=0,c=.58,l=1;break;default:var s=n[0].split("(");if("cubic-bezier"===s[0]&&4===s[1].split(")")[0].split(",").length){var u,f=function(e){if(Array.isArray(e))return e}(u=s[1].split(")")[0].split(",").map(function(e){return parseFloat(e)}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{for(i=(r=r.call(e)).next;!(l=(n=i.call(r)).done)&&(c.push(n.value),4!==c.length);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(u,4)||rz(u,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],c=f[2],l=f[3]}else rF(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}rF([i,c,a,l].every(function(e){return"number"==typeof e&&e>=0&&e<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=rZ(i,c),d=rZ(a,l),h=(e=i,t=c,function(r){var n;return rq([].concat(function(e){if(Array.isArray(e))return r$(e)}(n=rU(e,t).map(function(e,t){return e*t}).slice(1))||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(n)||rz(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(e){for(var t=e>1?1:e,r=t,n=0;n<8;++n){var o,i=p(r)-t,a=h(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return d(r)};return y.isStepper=!1,y},rH=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.stiff,r=void 0===t?100:t,n=e.damping,o=void 0===n?8:n,i=e.dt,a=void 0===i?17:i,c=function(e,t,n){var i=n+(-(e-t)*r-n*o)*a/1e3,c=n*a/1e3+e;return 1e-4>Math.abs(c-t)&&1e-4>Math.abs(i)?[t,0]:[c,i]};return c.isStepper=!0,c.dt=a,c},rX=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return rW(n);case"spring":return rH();default:if("cubic-bezier"===n.split("(")[0])return rW(n);rF(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",t)}return"function"==typeof n?n:(rF(!1,"[configEasing]: first argument type should be function or string, instead received %s",t),null)};function rV(e){return(rV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function rY(e){return function(e){if(Array.isArray(e))return r0(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||rQ(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function rG(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function rK(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rG(Object(r),!0).forEach(function(t){rJ(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rG(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function rJ(e,t,r){var n;return(n=function(e,t){if("object"!==rV(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==rV(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"===rV(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function rQ(e,t){if(e){if("string"==typeof e)return r0(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return r0(e,t)}}function r0(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var r1=function(e,t,r){return e+(t-e)*r},r2=function(e){return e.from!==e.to},r4=function e(t,r,n){var o=rR(function(e,r){if(r2(r)){var n,o=function(e){if(Array.isArray(e))return e}(n=t(r.from,r.to,r.velocity))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{for(i=(r=r.call(e)).next;!(l=(n=i.call(r)).done)&&(c.push(n.value),2!==c.length);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(n,2)||rQ(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return rK(rK({},r),{},{from:i,velocity:a})}return r},r);return n<1?rR(function(e,t){return r2(t)?rK(rK({},t),{},{velocity:r1(t.velocity,o[e].velocity,n),from:r1(t.from,o[e].from,n)}):t},r):e(t,o,n-1)};let r5=function(e,t,r,n,o){var i,a,c=[Object.keys(e),Object.keys(t)].reduce(function(e,t){return e.filter(function(e){return t.includes(e)})}),l=c.reduce(function(r,n){return rK(rK({},r),{},rJ({},n,[e[n],t[n]]))},{}),s=c.reduce(function(r,n){return rK(rK({},r),{},rJ({},n,{from:e[n],velocity:0,to:t[n]}))},{}),u=-1,f=function(){return null};return f=r.isStepper?function(n){i||(i=n);var a=(n-i)/r.dt;s=r4(r,s,a),o(rK(rK(rK({},e),t),rR(function(e,t){return t.from},s))),i=n,Object.values(s).filter(r2).length&&(u=requestAnimationFrame(f))}:function(i){a||(a=i);var c=(i-a)/n,s=rR(function(e,t){return r1.apply(void 0,rY(t).concat([r(c)]))},l);if(o(rK(rK(rK({},e),t),s)),c<1)u=requestAnimationFrame(f);else{var p=rR(function(e,t){return r1.apply(void 0,rY(t).concat([r(1)]))},l);o(rK(rK(rK({},e),t),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(u)}}};function r3(e){return(r3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var r6=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function r7(e){return function(e){if(Array.isArray(e))return r8(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return r8(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return r8(e,void 0)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r8(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function r9(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ne(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?r9(Object(r),!0).forEach(function(t){nt(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):r9(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function nt(e,t,r){return(t=nr(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nr(e){var t=function(e,t){if("object"!==r3(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==r3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===r3(t)?t:String(t)}function nn(e,t){return(nn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function no(e,t){if(t&&("object"===r3(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return ni(e)}function ni(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function na(e){return(na=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var nc=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&nn(e,t)}(o,e);var t,r,n=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,r=na(o);return e=t?Reflect.construct(r,arguments,na(this).constructor):r.apply(this,arguments),no(this,e)});function o(e,t){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,o);var r,i=(r=n.call(this,e,t)).props,a=i.isActive,c=i.attributeName,l=i.from,s=i.to,u=i.steps,f=i.children,p=i.duration;if(r.handleStyleChange=r.handleStyleChange.bind(ni(r)),r.changeStyle=r.changeStyle.bind(ni(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:s}),no(r);if(u&&u.length)r.state={style:u[0].style};else if(l){if("function"==typeof f)return r.state={style:l},no(r);r.state={style:c?nt({},c,l):l}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var e=this.props,t=e.isActive,r=e.canBegin;this.mounted=!0,t&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(e){var t=this.props,r=t.isActive,n=t.canBegin,o=t.attributeName,i=t.shouldReAnimate,a=t.to,c=t.from,l=this.state.style;if(n){if(!r){var s={style:o?nt({},o,a):a};this.state&&l&&(o&&l[o]!==a||!o&&l!==a)&&this.setState(s);return}if(!rk(e.to,a)||!e.canBegin||!e.isActive){var u=!e.canBegin||!e.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=u||i?c:e.to;if(this.state&&l){var p={style:o?nt({},o,f):f};(o&&l[o]!==f||!o&&l!==f)&&this.setState(p)}this.runAnimation(ne(ne({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var e=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}},{key:"handleStyleChange",value:function(e){this.changeStyle(e)}},{key:"changeStyle",value:function(e){this.mounted&&this.setState({style:e})}},{key:"runJSAnimation",value:function(e){var t=this,r=e.from,n=e.to,o=e.duration,i=e.easing,a=e.begin,c=e.onAnimationEnd,l=e.onAnimationStart,s=r5(r,n,rX(i),o,this.changeStyle);this.manager.start([l,a,function(){t.stopJSAnimation=s()},o,c])}},{key:"runStepAnimation",value:function(e){var t=this,r=e.steps,n=e.begin,o=e.onAnimationStart,i=r[0],a=i.style,c=i.duration;return this.manager.start([o].concat(r7(r.reduce(function(e,n,o){if(0===o)return e;var i=n.duration,a=n.easing,c=void 0===a?"ease":a,l=n.style,s=n.properties,u=n.onAnimationEnd,f=o>0?r[o-1]:n,p=s||Object.keys(l);if("function"==typeof c||"spring"===c)return[].concat(r7(e),[t.runJSAnimation.bind(t,{from:f.style,to:l,duration:i,easing:c}),i]);var d=rL(p,i,c),h=ne(ne(ne({},f.style),l),{},{transition:d});return[].concat(r7(e),[h,i,u]).filter(rB)},[a,Math.max(void 0===c?0:c,n)])),[e.onAnimationEnd]))}},{key:"runAnimation",value:function(e){if(!this.manager){var t,r,n;this.manager=(t=function(){return null},r=!1,n=function e(n){if(!r){if(Array.isArray(n)){if(!n.length)return;var o=function(e){if(Array.isArray(e))return e}(n)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(n)||function(e,t){if(e){if("string"==typeof e)return rT(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rT(e,void 0)}}(n)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);if("number"==typeof i){rM(e.bind(null,a),i);return}e(i),rM(e.bind(null,a));return}"object"===r_(n)&&t(n),"function"==typeof n&&n()}},{stop:function(){r=!0},start:function(e){r=!1,n(e)},subscribe:function(e){return t=e,function(){t=function(){return null}}}})}var o=e.begin,i=e.duration,a=e.attributeName,c=e.to,l=e.easing,s=e.onAnimationStart,u=e.onAnimationEnd,f=e.steps,p=e.children,d=this.manager;if(this.unSubscribe=d.subscribe(this.handleStyleChange),"function"==typeof l||"function"==typeof p||"spring"===l){this.runJSAnimation(e);return}if(f.length>1){this.runStepAnimation(e);return}var h=a?nt({},a,c):c,y=rL(Object.keys(h),i,l);d.start([s,o,ne(ne({},h),{},{transition:y}),i,u])}},{key:"render",value:function(){var e=this.props,t=e.children,r=(e.begin,e.duration),n=(e.attributeName,e.easing,e.isActive),o=(e.steps,e.from,e.to,e.canBegin,e.onAnimationEnd,e.shouldReAnimate,e.onAnimationReStart,function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,r6)),c=i.Children.count(t),l=this.state.style;if("function"==typeof t)return t(l);if(!n||0===c||r<=0)return t;var s=function(e){var t=e.props,r=t.style,n=t.className;return(0,i.cloneElement)(e,ne(ne({},o),{},{style:ne(ne({},void 0===r?{}:r),l),className:n}))};return 1===c?s(i.Children.only(t)):a().createElement("div",null,i.Children.map(t,function(e){return s(e)}))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,nr(n.key),n)}}(o.prototype,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(i.PureComponent);function nl(e){return(nl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ns(){return(ns=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function nu(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function nf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function np(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nf(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=nl(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=nl(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==nl(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nf(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}nc.displayName="Animate",nc.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},nc.propTypes={from:t9().oneOfType([t9().object,t9().string]),to:t9().oneOfType([t9().object,t9().string]),attributeName:t9().string,duration:t9().number,begin:t9().number,easing:t9().oneOfType([t9().string,t9().func]),steps:t9().arrayOf(t9().shape({duration:t9().number.isRequired,style:t9().object.isRequired,easing:t9().oneOfType([t9().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),t9().func]),properties:t9().arrayOf("string"),onAnimationEnd:t9().func})),children:t9().oneOfType([t9().node,t9().func]),isActive:t9().bool,canBegin:t9().bool,onAnimationEnd:t9().func,shouldReAnimate:t9().bool,onAnimationStart:t9().func,onAnimationReStart:t9().func};var nd=function(e,t,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,l=r>=0?1:-1,s=n>=0&&r>=0||n<0&&r<0?1:0;if(a>0&&o instanceof Array){for(var u=[0,0,0,0],f=0;f<4;f++)u[f]=o[f]>a?a:o[f];i="M".concat(e,",").concat(t+c*u[0]),u[0]>0&&(i+="A ".concat(u[0],",").concat(u[0],",0,0,").concat(s,",").concat(e+l*u[0],",").concat(t)),i+="L ".concat(e+r-l*u[1],",").concat(t),u[1]>0&&(i+="A ".concat(u[1],",").concat(u[1],",0,0,").concat(s,",\n        ").concat(e+r,",").concat(t+c*u[1])),i+="L ".concat(e+r,",").concat(t+n-c*u[2]),u[2]>0&&(i+="A ".concat(u[2],",").concat(u[2],",0,0,").concat(s,",\n        ").concat(e+r-l*u[2],",").concat(t+n)),i+="L ".concat(e+l*u[3],",").concat(t+n),u[3]>0&&(i+="A ".concat(u[3],",").concat(u[3],",0,0,").concat(s,",\n        ").concat(e,",").concat(t+n-c*u[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(e,",").concat(t+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(e+l*p,",").concat(t,"\n            L ").concat(e+r-l*p,",").concat(t,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(e+r,",").concat(t+c*p,"\n            L ").concat(e+r,",").concat(t+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(e+r-l*p,",").concat(t+n,"\n            L ").concat(e+l*p,",").concat(t+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(e,",").concat(t+n-c*p," Z")}else i="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},nh=function(e,t){if(!e||!t)return!1;var r=e.x,n=e.y,o=t.x,i=t.y,a=t.width,c=t.height;return!!(Math.abs(a)>0&&Math.abs(c)>0)&&r>=Math.min(o,o+a)&&r<=Math.max(o,o+a)&&n>=Math.min(i,i+c)&&n<=Math.max(i,i+c)},ny={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},nv=function(e){var t,r=np(np({},ny),e),n=(0,i.useRef)(),o=function(e){if(Array.isArray(e))return e}(t=(0,i.useState)(-1))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{for(i=(r=r.call(e)).next;!(l=(n=i.call(r)).done)&&(c.push(n.value),2!==c.length);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(t,2)||function(e,t){if(e){if("string"==typeof e)return nu(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nu(e,2)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=o[0],l=o[1];(0,i.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var e=n.current.getTotalLength();e&&l(e)}catch(e){}},[]);var s=r.x,u=r.y,f=r.width,p=r.height,d=r.radius,h=r.className,y=r.animationEasing,v=r.animationDuration,b=r.animationBegin,g=r.isAnimationActive,x=r.isUpdateAnimationActive;if(s!==+s||u!==+u||f!==+f||p!==+p||0===f||0===p)return null;var w=(0,m.Z)("recharts-rectangle",h);return x?a().createElement(nc,{canBegin:c>0,from:{width:f,height:p,x:s,y:u},to:{width:f,height:p,x:s,y:u},duration:v,animationEasing:y,isActive:x},function(e){var t=e.width,o=e.height,i=e.x,l=e.y;return a().createElement(nc,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:b,duration:v,isActive:g,easing:y},a().createElement("path",ns({},eh(r,!0),{className:w,d:nd(i,l,t,o,d),ref:n})))}):a().createElement("path",ns({},eh(r,!0),{className:w,d:nd(s,u,f,p,d)}))};function nm(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function nb(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class ng extends Map{constructor(e,t=nw){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(nx(this,e))}has(e){return super.has(nx(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function nx({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function nw(e){return null!==e&&"object"==typeof e?e.valueOf():e}let nO=Symbol("implicit");function nj(){var e=new ng,t=[],r=[],n=nO;function o(o){let i=e.get(o);if(void 0===i){if(n!==nO)return n;e.set(o,i=t.push(o)-1)}return r[i%r.length]}return o.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new ng,r))e.has(n)||e.set(n,t.push(n)-1);return o},o.range=function(e){return arguments.length?(r=Array.from(e),o):r.slice()},o.unknown=function(e){return arguments.length?(n=e,o):n},o.copy=function(){return nj(t,r).unknown(n)},nm.apply(o,arguments),o}function nS(){var e,t,r=nj().unknown(void 0),n=r.domain,o=r.range,i=0,a=1,c=!1,l=0,s=0,u=.5;function f(){var r=n().length,f=a<i,p=f?a:i,d=f?i:a;e=(d-p)/Math.max(1,r-l+2*s),c&&(e=Math.floor(e)),p+=(d-p-e*(r-l))*u,t=e*(1-l),c&&(p=Math.round(p),t=Math.round(t));var h=(function(e,t,r){e=+e,t=+t,r=(o=arguments.length)<2?(t=e,e=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((t-e)/r)),i=Array(o);++n<o;)i[n]=e+n*r;return i})(r).map(function(t){return p+e*t});return o(f?h.reverse():h)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([i,a]=e,i=+i,a=+a,f()):[i,a]},r.rangeRound=function(e){return[i,a]=e,i=+i,a=+a,c=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(c=!!e,f()):c},r.padding=function(e){return arguments.length?(l=Math.min(1,s=+e),f()):l},r.paddingInner=function(e){return arguments.length?(l=Math.min(1,e),f()):l},r.paddingOuter=function(e){return arguments.length?(s=+e,f()):s},r.align=function(e){return arguments.length?(u=Math.max(0,Math.min(1,e)),f()):u},r.copy=function(){return nS(n(),[i,a]).round(c).paddingInner(l).paddingOuter(s).align(u)},nm.apply(f(),arguments)}function nA(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(nS.apply(null,arguments).paddingInner(1))}function nE(e){return(nE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nk(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nP(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nk(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=nE(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=nE(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==nE(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nk(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var nM={widthCache:{},cacheCount:0},n_={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},nT="recharts_measurement_span",nN=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||e2.isSsr)return{width:0,height:0};var n=(Object.keys(t=nP({},r)).forEach(function(e){t[e]||delete t[e]}),t),o=JSON.stringify({text:e,copyStyle:n});if(nM.widthCache[o])return nM.widthCache[o];try{var i=document.getElementById(nT);i||((i=document.createElement("span")).setAttribute("id",nT),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var a=nP(nP({},n_),n);Object.assign(i.style,a),i.textContent="".concat(e);var c=i.getBoundingClientRect(),l={width:c.width,height:c.height};return nM.widthCache[o]=l,++nM.cacheCount>2e3&&(nM.cacheCount=0,nM.widthCache={}),l}catch(e){return{width:0,height:0}}};function nC(e){return(nC="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function nD(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return nI(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nI(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nI(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function nB(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,function(e){var t=function(e,t){if("object"!=nC(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=nC(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==nC(t)?t:t+""}(n.key),n)}}var nR=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nL=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nF=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,nz=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,n$={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},nU=Object.keys(n$),nq=function(){var e,t;function r(e,t){(function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")})(this,r),this.num=e,this.unit=t,this.num=e,this.unit=t,Number.isNaN(e)&&(this.unit=""),""===t||nF.test(t)||(this.num=NaN,this.unit=""),nU.includes(t)&&(this.num=e*n$[t],this.unit="px")}return e=[{key:"add",value:function(e){return this.unit!==e.unit?new r(NaN,""):new r(this.num+e.num,this.unit)}},{key:"subtract",value:function(e){return this.unit!==e.unit?new r(NaN,""):new r(this.num-e.num,this.unit)}},{key:"multiply",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new r(NaN,""):new r(this.num*e.num,this.unit||e.unit)}},{key:"divide",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new r(NaN,""):new r(this.num/e.num,this.unit||e.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],t=[{key:"parse",value:function(e){var t,n=nD(null!==(t=nz.exec(e))&&void 0!==t?t:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],e&&nB(r.prototype,e),t&&nB(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}();function nZ(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,n=nD(null!==(r=nR.exec(t))&&void 0!==r?r:[],4),o=n[1],i=n[2],a=n[3],c=nq.parse(null!=o?o:""),l=nq.parse(null!=a?a:""),s="*"===i?c.multiply(l):c.divide(l);if(s.isNaN())return"NaN";t=t.replace(nR,s.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var u,f=nD(null!==(u=nL.exec(t))&&void 0!==u?u:[],4),p=f[1],d=f[2],h=f[3],y=nq.parse(null!=p?p:""),v=nq.parse(null!=h?h:""),m="+"===d?y.add(v):y.subtract(v);if(m.isNaN())return"NaN";t=t.replace(nL,m.toString())}return t}var nW=/\(([^()]*)\)/;function nH(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t=e;t.includes("(");){var r=nD(nW.exec(t),2)[1];t=t.replace(nW,nZ(r))}return t}(t),t=nZ(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var nX=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],nV=["dx","dy","angle","className","breakAll"];function nY(){return(nY=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function nG(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function nK(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return nJ(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nJ(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nJ(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var nQ=/[ \f\n\r\t\v\u2028\u2029]+/,n0=function(e){var t=e.children,r=e.breakAll,n=e.style;try{var o=[];M()(t)||(o=r?t.toString().split(""):t.toString().split(nQ));var i=o.map(function(e){return{word:e,width:nN(e,n).width}}),a=r?0:nN("\xa0",n).width;return{wordsWithComputedWidth:i,spaceWidth:a}}catch(e){return null}},n1=function(e,t,r,n,o){var i,a=e.maxLines,c=e.children,l=e.style,s=e.breakAll,u=N(a),f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce(function(e,t){var i=t.word,a=t.width,c=e[e.length-1];return c&&(null==n||o||c.width+a+r<Number(n))?(c.words.push(i),c.width+=a+r):e.push({words:[i],width:a}),e},[])},p=f(t);if(!u)return p;for(var d=function(e){var t=f(n0({breakAll:s,style:l,children:c.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>a||t.reduce(function(e,t){return e.width>t.width?e:t}).width>Number(n),t]},h=0,y=c.length-1,v=0;h<=y&&v<=c.length-1;){var m=Math.floor((h+y)/2),b=nK(d(m-1),2),g=b[0],x=b[1],w=nK(d(m),1)[0];if(g||w||(h=m+1),g&&w&&(y=m-1),!g&&w){i=x;break}v++}return i||p},n2=function(e){return[{words:M()(e)?[]:e.toString().split(nQ)}]},n4=function(e){var t=e.width,r=e.scaleToFit,n=e.children,o=e.style,i=e.breakAll,a=e.maxLines;if((t||r)&&!e2.isSsr){var c=n0({breakAll:i,children:n,style:o});return c?n1({breakAll:i,children:n,maxLines:a,style:o},c.wordsWithComputedWidth,c.spaceWidth,t,r):n2(n)}return n2(n)},n5="#808080",n3=function(e){var t,r=e.x,n=void 0===r?0:r,o=e.y,c=void 0===o?0:o,l=e.lineHeight,s=void 0===l?"1em":l,u=e.capHeight,f=void 0===u?"0.71em":u,p=e.scaleToFit,d=void 0!==p&&p,h=e.textAnchor,y=e.verticalAnchor,v=e.fill,b=void 0===v?n5:v,g=nG(e,nX),x=(0,i.useMemo)(function(){return n4({breakAll:g.breakAll,children:g.children,maxLines:g.maxLines,scaleToFit:d,style:g.style,width:g.width})},[g.breakAll,g.children,g.maxLines,d,g.style,g.width]),w=g.dx,O=g.dy,j=g.angle,S=g.className,A=g.breakAll,E=nG(g,nV);if(!C(n)||!C(c))return null;var k=n+(N(w)?w:0),P=c+(N(O)?O:0);switch(void 0===y?"end":y){case"start":t=nH("calc(".concat(f,")"));break;case"middle":t=nH("calc(".concat((x.length-1)/2," * -").concat(s," + (").concat(f," / 2))"));break;default:t=nH("calc(".concat(x.length-1," * -").concat(s,")"))}var M=[];if(d){var _=x[0].width,T=g.width;M.push("scale(".concat((N(T)?T/_:1)/_,")"))}return j&&M.push("rotate(".concat(j,", ").concat(k,", ").concat(P,")")),M.length&&(E.transform=M.join(" ")),a().createElement("text",nY({},eh(E,!0),{x:k,y:P,className:(0,m.Z)("recharts-text",S),textAnchor:void 0===h?"start":h,fill:b.includes("url")?n5:b}),x.map(function(e,r){var n=e.words.join(A?"":" ");return a().createElement("tspan",{x:k,dy:0===r?t:s,key:"".concat(n,"-").concat(r)},n)}))};let n6=Math.sqrt(50),n7=Math.sqrt(10),n8=Math.sqrt(2);function n9(e,t,r){let n,o,i;let a=(t-e)/Math.max(0,r),c=Math.floor(Math.log10(a)),l=a/Math.pow(10,c),s=l>=n6?10:l>=n7?5:l>=n8?2:1;return(c<0?(n=Math.round(e*(i=Math.pow(10,-c)/s)),o=Math.round(t*i),n/i<e&&++n,o/i>t&&--o,i=-i):(n=Math.round(e/(i=Math.pow(10,c)*s)),o=Math.round(t/i),n*i<e&&++n,o*i>t&&--o),o<n&&.5<=r&&r<2)?n9(e,t,2*r):[n,o,i]}function oe(e,t,r){if(t=+t,e=+e,!((r=+r)>0))return[];if(e===t)return[e];let n=t<e,[o,i,a]=n?n9(t,e,r):n9(e,t,r);if(!(i>=o))return[];let c=i-o+1,l=Array(c);if(n){if(a<0)for(let e=0;e<c;++e)l[e]=-((i-e)/a);else for(let e=0;e<c;++e)l[e]=(i-e)*a}else if(a<0)for(let e=0;e<c;++e)l[e]=-((o+e)/a);else for(let e=0;e<c;++e)l[e]=(o+e)*a;return l}function ot(e,t,r){return n9(e=+e,t=+t,r=+r)[2]}function or(e,t,r){t=+t,e=+e,r=+r;let n=t<e,o=n?ot(t,e,r):ot(e,t,r);return(n?-1:1)*(o<0?-(1/o):o)}function on(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function oo(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function oi(e){let t,r,n;function o(e,n,o=0,i=e.length){if(o<i){if(0!==t(n,n))return i;do{let t=o+i>>>1;0>r(e[t],n)?o=t+1:i=t}while(o<i)}return o}return 2!==e.length?(t=on,r=(t,r)=>on(e(t),r),n=(t,r)=>e(t)-r):(t=e===on||e===oo?e:oa,r=e,n=e),{left:o,center:function(e,t,r=0,i=e.length){let a=o(e,t,r,i-1);return a>r&&n(e[a-1],t)>-n(e[a],t)?a-1:a},right:function(e,n,o=0,i=e.length){if(o<i){if(0!==t(n,n))return i;do{let t=o+i>>>1;0>=r(e[t],n)?o=t+1:i=t}while(o<i)}return o}}}function oa(){return 0}function oc(e){return null===e?NaN:+e}let ol=oi(on),os=ol.right;function ou(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function of(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function op(){}ol.left,oi(oc).center;var od="\\s*([+-]?\\d+)\\s*",oh="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",oy="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",ov=/^#([0-9a-f]{3,8})$/,om=RegExp(`^rgb\\(${od},${od},${od}\\)$`),ob=RegExp(`^rgb\\(${oy},${oy},${oy}\\)$`),og=RegExp(`^rgba\\(${od},${od},${od},${oh}\\)$`),ox=RegExp(`^rgba\\(${oy},${oy},${oy},${oh}\\)$`),ow=RegExp(`^hsl\\(${oh},${oy},${oy}\\)$`),oO=RegExp(`^hsla\\(${oh},${oy},${oy},${oh}\\)$`),oj={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function oS(){return this.rgb().formatHex()}function oA(){return this.rgb().formatRgb()}function oE(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=ov.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?ok(t):3===r?new o_(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?oP(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?oP(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=om.exec(e))?new o_(t[1],t[2],t[3],1):(t=ob.exec(e))?new o_(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=og.exec(e))?oP(t[1],t[2],t[3],t[4]):(t=ox.exec(e))?oP(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=ow.exec(e))?oB(t[1],t[2]/100,t[3]/100,1):(t=oO.exec(e))?oB(t[1],t[2]/100,t[3]/100,t[4]):oj.hasOwnProperty(e)?ok(oj[e]):"transparent"===e?new o_(NaN,NaN,NaN,0):null}function ok(e){return new o_(e>>16&255,e>>8&255,255&e,1)}function oP(e,t,r,n){return n<=0&&(e=t=r=NaN),new o_(e,t,r,n)}function oM(e,t,r,n){var o;return 1==arguments.length?((o=e)instanceof op||(o=oE(o)),o)?new o_((o=o.rgb()).r,o.g,o.b,o.opacity):new o_:new o_(e,t,r,null==n?1:n)}function o_(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function oT(){return`#${oI(this.r)}${oI(this.g)}${oI(this.b)}`}function oN(){let e=oC(this.opacity);return`${1===e?"rgb(":"rgba("}${oD(this.r)}, ${oD(this.g)}, ${oD(this.b)}${1===e?")":`, ${e})`}`}function oC(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function oD(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function oI(e){return((e=oD(e))<16?"0":"")+e.toString(16)}function oB(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new oL(e,t,r,n)}function oR(e){if(e instanceof oL)return new oL(e.h,e.s,e.l,e.opacity);if(e instanceof op||(e=oE(e)),!e)return new oL;if(e instanceof oL)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,o=Math.min(t,r,n),i=Math.max(t,r,n),a=NaN,c=i-o,l=(i+o)/2;return c?(a=t===i?(r-n)/c+(r<n)*6:r===i?(n-t)/c+2:(t-r)/c+4,c/=l<.5?i+o:2-i-o,a*=60):c=l>0&&l<1?0:a,new oL(a,c,l,e.opacity)}function oL(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function oF(e){return(e=(e||0)%360)<0?e+360:e}function oz(e){return Math.max(0,Math.min(1,e||0))}function o$(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function oU(e,t,r,n,o){var i=e*e,a=i*e;return((1-3*e+3*i-a)*t+(4-6*i+3*a)*r+(1+3*e+3*i-3*a)*n+a*o)/6}ou(op,oE,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:oS,formatHex:oS,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return oR(this).formatHsl()},formatRgb:oA,toString:oA}),ou(o_,oM,of(op,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new o_(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new o_(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new o_(oD(this.r),oD(this.g),oD(this.b),oC(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:oT,formatHex:oT,formatHex8:function(){return`#${oI(this.r)}${oI(this.g)}${oI(this.b)}${oI((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:oN,toString:oN})),ou(oL,function(e,t,r,n){return 1==arguments.length?oR(e):new oL(e,t,r,null==n?1:n)},of(op,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new oL(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new oL(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,o=2*r-n;return new o_(o$(e>=240?e-240:e+120,o,n),o$(e,o,n),o$(e<120?e+240:e-120,o,n),this.opacity)},clamp(){return new oL(oF(this.h),oz(this.s),oz(this.l),oC(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=oC(this.opacity);return`${1===e?"hsl(":"hsla("}${oF(this.h)}, ${100*oz(this.s)}%, ${100*oz(this.l)}%${1===e?")":`, ${e})`}`}}));let oq=e=>()=>e;function oZ(e,t){var r=t-e;return r?function(t){return e+t*r}:oq(isNaN(e)?t:e)}let oW=function e(t){var r,n=1==(r=+(r=t))?oZ:function(e,t){var n,o,i;return t-e?(n=e,o=t,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(e){return Math.pow(n+e*o,i)}):oq(isNaN(e)?t:e)};function o(e,t){var r=n((e=oM(e)).r,(t=oM(t)).r),o=n(e.g,t.g),i=n(e.b,t.b),a=oZ(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=o(t),e.b=i(t),e.opacity=a(t),e+""}}return o.gamma=e,o}(1);function oH(e){return function(t){var r,n,o=t.length,i=Array(o),a=Array(o),c=Array(o);for(r=0;r<o;++r)n=oM(t[r]),i[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return i=e(i),a=e(a),c=e(c),n.opacity=1,function(e){return n.r=i(e),n.g=a(e),n.b=c(e),n+""}}}function oX(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}oH(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),o=e[n],i=e[n+1],a=n>0?e[n-1]:2*o-i,c=n<t-1?e[n+2]:2*i-o;return oU((r-n/t)*t,a,o,i,c)}}),oH(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),o=e[(n+t-1)%t],i=e[n%t],a=e[(n+1)%t],c=e[(n+2)%t];return oU((r-n/t)*t,o,i,a,c)}});var oV=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,oY=RegExp(oV.source,"g");function oG(e,t){var r,n,o=typeof t;return null==t||"boolean"===o?oq(t):("number"===o?oX:"string"===o?(n=oE(t))?(t=n,oW):function(e,t){var r,n,o,i,a,c=oV.lastIndex=oY.lastIndex=0,l=-1,s=[],u=[];for(e+="",t+="";(o=oV.exec(e))&&(i=oY.exec(t));)(a=i.index)>c&&(a=t.slice(c,a),s[l]?s[l]+=a:s[++l]=a),(o=o[0])===(i=i[0])?s[l]?s[l]+=i:s[++l]=i:(s[++l]=null,u.push({i:l,x:oX(o,i)})),c=oY.lastIndex;return c<t.length&&(a=t.slice(c),s[l]?s[l]+=a:s[++l]=a),s.length<2?u[0]?(r=u[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=u.length,function(e){for(var r,n=0;n<t;++n)s[(r=u[n]).i]=r.x(e);return s.join("")})}:t instanceof oE?oW:t instanceof Date?function(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,o=e?Math.min(n,e.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=oG(e[r],t[r]);for(;r<n;++r)a[r]=t[r];return function(e){for(r=0;r<o;++r)a[r]=i[r](e);return a}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},o={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=oG(e[r],t[r]):o[r]=t[r];return function(e){for(r in n)o[r]=n[r](e);return o}}:oX:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,o=t.slice();return function(i){for(r=0;r<n;++r)o[r]=e[r]*(1-i)+t[r]*i;return o}})(e,t)}function oK(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function oJ(e){return+e}var oQ=[0,1];function o0(e){return e}function o1(e,t){var r;return(t-=e=+e)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function o2(e,t,r){var n=e[0],o=e[1],i=t[0],a=t[1];return o<n?(n=o1(o,n),i=r(a,i)):(n=o1(n,o),i=r(i,a)),function(e){return i(n(e))}}function o4(e,t,r){var n=Math.min(e.length,t.length)-1,o=Array(n),i=Array(n),a=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++a<n;)o[a]=o1(e[a],e[a+1]),i[a]=r(t[a],t[a+1]);return function(t){var r=os(e,t,1,n)-1;return i[r](o[r](t))}}function o5(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function o3(){var e,t,r,n,o,i,a=oQ,c=oQ,l=oG,s=o0;function u(){var e,t,r,l=Math.min(a.length,c.length);return s!==o0&&(e=a[0],t=a[l-1],e>t&&(r=e,e=t,t=r),s=function(r){return Math.max(e,Math.min(t,r))}),n=l>2?o4:o2,o=i=null,f}function f(t){return null==t||isNaN(t=+t)?r:(o||(o=n(a.map(e),c,l)))(e(s(t)))}return f.invert=function(r){return s(t((i||(i=n(c,a.map(e),oX)))(r)))},f.domain=function(e){return arguments.length?(a=Array.from(e,oJ),u()):a.slice()},f.range=function(e){return arguments.length?(c=Array.from(e),u()):c.slice()},f.rangeRound=function(e){return c=Array.from(e),l=oK,u()},f.clamp=function(e){return arguments.length?(s=!!e||o0,u()):s!==o0},f.interpolate=function(e){return arguments.length?(l=e,u()):l},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,u()}}function o6(){return o3()(o0,o0)}var o7=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function o8(e){var t;if(!(t=o7.exec(e)))throw Error("invalid format: "+e);return new o9({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function o9(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function ie(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function it(e){return(e=ie(Math.abs(e)))?e[1]:NaN}function ir(e,t){var r=ie(e,t);if(!r)return e+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}o8.prototype=o9.prototype,o9.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let io={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>ir(100*e,t),r:ir,s:function(e,t){var r=ie(e,t);if(!r)return e+"";var n=r[0],o=r[1],i=o-(cD=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+Array(1-i).join("0")+ie(e,Math.max(0,t+i-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function ii(e){return e}var ia=Array.prototype.map,ic=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function il(e,t,r,n){var o,i,a=or(e,t,r);switch((n=o8(null==n?",f":n)).type){case"s":var c=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(i=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(it(c)/3)))-it(Math.abs(a))))||(n.precision=i),cR(n,c);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(i=Math.max(0,it(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(o=Math.abs(o=a)))-it(o))+1)||(n.precision=i-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(i=Math.max(0,-it(Math.abs(a))))||(n.precision=i-("%"===n.type)*2)}return cB(n)}function is(e){var t=e.domain;return e.ticks=function(e){var r=t();return oe(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return il(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,o,i=t(),a=0,c=i.length-1,l=i[a],s=i[c],u=10;for(s<l&&(o=l,l=s,s=o,o=a,a=c,c=o);u-- >0;){if((o=ot(l,s,r))===n)return i[a]=l,i[c]=s,t(i);if(o>0)l=Math.floor(l/o)*o,s=Math.ceil(s/o)*o;else if(o<0)l=Math.ceil(l*o)/o,s=Math.floor(s*o)/o;else break;n=o}return e},e}function iu(){var e=o6();return e.copy=function(){return o5(e,iu())},nm.apply(e,arguments),is(e)}function ip(e,t){e=e.slice();var r,n=0,o=e.length-1,i=e[n],a=e[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),e[n]=t.floor(i),e[o]=t.ceil(a),e}function id(e){return Math.log(e)}function ih(e){return Math.exp(e)}function iy(e){return-Math.log(-e)}function iv(e){return-Math.exp(-e)}function im(e){return isFinite(e)?+("1e"+e):e<0?0:e}function ib(e){return(t,r)=>-e(-t,r)}function ig(e){let t,r;let n=e(id,ih),o=n.domain,i=10;function a(){var a,c;return t=(a=i)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),e=>Math.log(e)/a),r=10===(c=i)?im:c===Math.E?Math.exp:e=>Math.pow(c,e),o()[0]<0?(t=ib(t),r=ib(r),e(iy,iv)):e(id,ih),n}return n.base=function(e){return arguments.length?(i=+e,a()):i},n.domain=function(e){return arguments.length?(o(e),a()):o()},n.ticks=e=>{let n,a;let c=o(),l=c[0],s=c[c.length-1],u=s<l;u&&([l,s]=[s,l]);let f=t(l),p=t(s),d=null==e?10:+e,h=[];if(!(i%1)&&p-f<d){if(f=Math.floor(f),p=Math.ceil(p),l>0){for(;f<=p;++f)for(n=1;n<i;++n)if(!((a=f<0?n/r(-f):n*r(f))<l)){if(a>s)break;h.push(a)}}else for(;f<=p;++f)for(n=i-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<l)){if(a>s)break;h.push(a)}2*h.length<d&&(h=oe(l,s,d))}else h=oe(f,p,Math.min(p-f,d)).map(r);return u?h.reverse():h},n.tickFormat=(e,o)=>{if(null==e&&(e=10),null==o&&(o=10===i?"s":","),"function"!=typeof o&&(i%1||null!=(o=o8(o)).precision||(o.trim=!0),o=cB(o)),e===1/0)return o;let a=Math.max(1,i*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*i<i-.5&&(n*=i),n<=a?o(e):""}},n.nice=()=>o(ip(o(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function ix(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function iw(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function iO(e){var t=1,r=e(ix(1),iw(t));return r.constant=function(r){return arguments.length?e(ix(t=+r),iw(t)):t},is(r)}function ij(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function iS(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function iA(e){return e<0?-e*e:e*e}function iE(e){var t=e(o0,o0),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(o0,o0):.5===r?e(iS,iA):e(ij(r),ij(1/r)):r},is(t)}function ik(){var e=iE(o3());return e.copy=function(){return o5(e,ik()).exponent(e.exponent())},nm.apply(e,arguments),e}function iP(){return ik.apply(null,arguments).exponent(.5)}function iM(e){return Math.sign(e)*e*e}function i_(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let o of e)null!=(o=t(o,++n,e))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function iT(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let o of e)null!=(o=t(o,++n,e))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}function iN(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:e>t?1:0)}function iC(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}cB=(cI=function(e){var t,r,n,o=void 0===e.grouping||void 0===e.thousands?ii:(t=ia.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var o=e.length,i=[],a=0,c=t[0],l=0;o>0&&c>0&&(l+c+1>n&&(c=Math.max(1,n-l)),i.push(e.substring(o-=c,o+c)),!((l+=c+1)>n));)c=t[a=(a+1)%t.length];return i.reverse().join(r)}),i=void 0===e.currency?"":e.currency[0]+"",a=void 0===e.currency?"":e.currency[1]+"",c=void 0===e.decimal?".":e.decimal+"",l=void 0===e.numerals?ii:(n=ia.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return n[+e]})}),s=void 0===e.percent?"%":e.percent+"",u=void 0===e.minus?"−":e.minus+"",f=void 0===e.nan?"NaN":e.nan+"";function p(e){var t=(e=o8(e)).fill,r=e.align,n=e.sign,p=e.symbol,d=e.zero,h=e.width,y=e.comma,v=e.precision,m=e.trim,b=e.type;"n"===b?(y=!0,b="g"):io[b]||(void 0===v&&(v=12),m=!0,b="g"),(d||"0"===t&&"="===r)&&(d=!0,t="0",r="=");var g="$"===p?i:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?a:/[%p]/.test(b)?s:"",w=io[b],O=/[defgprs%]/.test(b);function j(e){var i,a,s,p=g,j=x;if("c"===b)j=w(e)+j,e="";else{var S=(e=+e)<0||1/e<0;if(e=isNaN(e)?f:w(Math.abs(e),v),m&&(e=function(e){t:for(var t,r=e.length,n=1,o=-1;n<r;++n)switch(e[n]){case".":o=t=n;break;case"0":0===o&&(o=n),t=n;break;default:if(!+e[n])break t;o>0&&(o=0)}return o>0?e.slice(0,o)+e.slice(t+1):e}(e)),S&&0==+e&&"+"!==n&&(S=!1),p=(S?"("===n?n:u:"-"===n||"("===n?"":n)+p,j=("s"===b?ic[8+cD/3]:"")+j+(S&&"("===n?")":""),O){for(i=-1,a=e.length;++i<a;)if(48>(s=e.charCodeAt(i))||s>57){j=(46===s?c+e.slice(i+1):e.slice(i))+j,e=e.slice(0,i);break}}}y&&!d&&(e=o(e,1/0));var A=p.length+e.length+j.length,E=A<h?Array(h-A+1).join(t):"";switch(y&&d&&(e=o(E+e,E.length?h-j.length:1/0),E=""),r){case"<":e=p+e+j+E;break;case"=":e=p+E+e+j;break;case"^":e=E.slice(0,A=E.length>>1)+p+e+j+E.slice(A);break;default:e=E+p+e+j}return l(e)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return e+""},j}return{format:p,formatPrefix:function(e,t){var r=p(((e=o8(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(it(t)/3))),o=Math.pow(10,-n),i=ic[8+n/3];return function(e){return r(o*e)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,cR=cI.formatPrefix;let iD=new Date,iI=new Date;function iB(e,t,r,n){function o(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return o.floor=t=>(e(t=new Date(+t)),t),o.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),o.round=e=>{let t=o(e),r=o.ceil(e);return e-t<r-e?t:r},o.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),o.range=(r,n,i)=>{let a;let c=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return c;do c.push(a=new Date(+r)),t(r,i),e(r);while(a<r&&r<n);return c},o.filter=r=>iB(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e){if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}}),r&&(o.count=(t,n)=>(iD.setTime(+t),iI.setTime(+n),e(iD),e(iI),Math.floor(r(iD,iI))),o.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?o.filter(n?t=>n(t)%e==0:t=>o.count(0,t)%e==0):o:null),o}let iR=iB(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);iR.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?iB(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):iR:null,iR.range;let iL=iB(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());iL.range;let iF=iB(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());iF.range;let iz=iB(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());iz.range;let i$=iB(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());i$.range;let iU=iB(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());iU.range;let iq=iB(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);iq.range;let iZ=iB(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);iZ.range;let iW=iB(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function iH(e){return iB(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}iW.range;let iX=iH(0),iV=iH(1),iY=iH(2),iG=iH(3),iK=iH(4),iJ=iH(5),iQ=iH(6);function i0(e){return iB(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}iX.range,iV.range,iY.range,iG.range,iK.range,iJ.range,iQ.range;let i1=i0(0),i2=i0(1),i4=i0(2),i5=i0(3),i3=i0(4),i6=i0(5),i7=i0(6);i1.range,i2.range,i4.range,i5.range,i3.range,i6.range,i7.range;let i8=iB(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());i8.range;let i9=iB(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());i9.range;let ae=iB(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());ae.every=e=>isFinite(e=Math.floor(e))&&e>0?iB(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,ae.range;let at=iB(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function ar(e,t,r,n,o,i){let a=[[iL,1,1e3],[iL,5,5e3],[iL,15,15e3],[iL,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function c(t,r,n){let o=Math.abs(r-t)/n,i=oi(([,,e])=>e).right(a,o);if(i===a.length)return e.every(or(t/31536e6,r/31536e6,n));if(0===i)return iR.every(Math.max(or(t,r,n),1));let[c,l]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return c.every(l)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let o=r&&"function"==typeof r.range?r:c(e,t,r),i=o?o.range(e,+t+1):[];return n?i.reverse():i},c]}at.every=e=>isFinite(e=Math.floor(e))&&e>0?iB(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,at.range;let[an,ao]=ar(at,i9,i1,iW,iU,iz),[ai,aa]=ar(ae,i8,iX,iq,i$,iF);function ac(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function al(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function as(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var au={"-":"",_:" ",0:"0"},af=/^\s*\d+/,ap=/^%/,ad=/[\\^$*+?|[\]().{}]/g;function ah(e,t,r){var n=e<0?"-":"",o=(n?-e:e)+"",i=o.length;return n+(i<r?Array(r-i+1).join(t)+o:o)}function ay(e){return e.replace(ad,"\\$&")}function av(e){return RegExp("^(?:"+e.map(ay).join("|")+")","i")}function am(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function ab(e,t,r){var n=af.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function ag(e,t,r){var n=af.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function ax(e,t,r){var n=af.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function aw(e,t,r){var n=af.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function aO(e,t,r){var n=af.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function aj(e,t,r){var n=af.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function aS(e,t,r){var n=af.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function aA(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function aE(e,t,r){var n=af.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function ak(e,t,r){var n=af.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function aP(e,t,r){var n=af.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function aM(e,t,r){var n=af.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function a_(e,t,r){var n=af.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function aT(e,t,r){var n=af.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function aN(e,t,r){var n=af.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function aC(e,t,r){var n=af.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function aD(e,t,r){var n=af.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function aI(e,t,r){var n=ap.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function aB(e,t,r){var n=af.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function aR(e,t,r){var n=af.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function aL(e,t){return ah(e.getDate(),t,2)}function aF(e,t){return ah(e.getHours(),t,2)}function az(e,t){return ah(e.getHours()%12||12,t,2)}function a$(e,t){return ah(1+iq.count(ae(e),e),t,3)}function aU(e,t){return ah(e.getMilliseconds(),t,3)}function aq(e,t){return aU(e,t)+"000"}function aZ(e,t){return ah(e.getMonth()+1,t,2)}function aW(e,t){return ah(e.getMinutes(),t,2)}function aH(e,t){return ah(e.getSeconds(),t,2)}function aX(e){var t=e.getDay();return 0===t?7:t}function aV(e,t){return ah(iX.count(ae(e)-1,e),t,2)}function aY(e){var t=e.getDay();return t>=4||0===t?iK(e):iK.ceil(e)}function aG(e,t){return e=aY(e),ah(iK.count(ae(e),e)+(4===ae(e).getDay()),t,2)}function aK(e){return e.getDay()}function aJ(e,t){return ah(iV.count(ae(e)-1,e),t,2)}function aQ(e,t){return ah(e.getFullYear()%100,t,2)}function a0(e,t){return ah((e=aY(e)).getFullYear()%100,t,2)}function a1(e,t){return ah(e.getFullYear()%1e4,t,4)}function a2(e,t){var r=e.getDay();return ah((e=r>=4||0===r?iK(e):iK.ceil(e)).getFullYear()%1e4,t,4)}function a4(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+ah(t/60|0,"0",2)+ah(t%60,"0",2)}function a5(e,t){return ah(e.getUTCDate(),t,2)}function a3(e,t){return ah(e.getUTCHours(),t,2)}function a6(e,t){return ah(e.getUTCHours()%12||12,t,2)}function a7(e,t){return ah(1+iZ.count(at(e),e),t,3)}function a8(e,t){return ah(e.getUTCMilliseconds(),t,3)}function a9(e,t){return a8(e,t)+"000"}function ce(e,t){return ah(e.getUTCMonth()+1,t,2)}function ct(e,t){return ah(e.getUTCMinutes(),t,2)}function cr(e,t){return ah(e.getUTCSeconds(),t,2)}function cn(e){var t=e.getUTCDay();return 0===t?7:t}function co(e,t){return ah(i1.count(at(e)-1,e),t,2)}function ci(e){var t=e.getUTCDay();return t>=4||0===t?i3(e):i3.ceil(e)}function ca(e,t){return e=ci(e),ah(i3.count(at(e),e)+(4===at(e).getUTCDay()),t,2)}function cc(e){return e.getUTCDay()}function cl(e,t){return ah(i2.count(at(e)-1,e),t,2)}function cs(e,t){return ah(e.getUTCFullYear()%100,t,2)}function cu(e,t){return ah((e=ci(e)).getUTCFullYear()%100,t,2)}function cf(e,t){return ah(e.getUTCFullYear()%1e4,t,4)}function cp(e,t){var r=e.getUTCDay();return ah((e=r>=4||0===r?i3(e):i3.ceil(e)).getUTCFullYear()%1e4,t,4)}function cd(){return"+0000"}function ch(){return"%"}function cy(e){return+e}function cv(e){return Math.floor(+e/1e3)}function cm(e){return new Date(e)}function cb(e){return e instanceof Date?+e:+new Date(+e)}function cg(e,t,r,n,o,i,a,c,l,s){var u=o6(),f=u.invert,p=u.domain,d=s(".%L"),h=s(":%S"),y=s("%I:%M"),v=s("%I %p"),m=s("%a %d"),b=s("%b %d"),g=s("%B"),x=s("%Y");function w(e){return(l(e)<e?d:c(e)<e?h:a(e)<e?y:i(e)<e?v:n(e)<e?o(e)<e?m:b:r(e)<e?g:x)(e)}return u.invert=function(e){return new Date(f(e))},u.domain=function(e){return arguments.length?p(Array.from(e,cb)):p().map(cm)},u.ticks=function(t){var r=p();return e(r[0],r[r.length-1],null==t?10:t)},u.tickFormat=function(e,t){return null==t?w:s(t)},u.nice=function(e){var r=p();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?p(ip(r,e)):u},u.copy=function(){return o5(u,cg(e,t,r,n,o,i,a,c,l,s))},u}function cx(){return nm.apply(cg(ai,aa,ae,i8,iX,iq,i$,iF,iL,cF).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function cw(){return nm.apply(cg(an,ao,at,i9,i1,iZ,iU,iz,iL,cz).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function cO(){var e,t,r,n,o,i=0,a=1,c=o0,l=!1;function s(t){return null==t||isNaN(t=+t)?o:c(0===r?.5:(t=(n(t)-e)*r,l?Math.max(0,Math.min(1,t)):t))}function u(e){return function(t){var r,n;return arguments.length?([r,n]=t,c=e(r,n),s):[c(0),c(1)]}}return s.domain=function(o){return arguments.length?([i,a]=o,e=n(i=+i),t=n(a=+a),r=e===t?0:1/(t-e),s):[i,a]},s.clamp=function(e){return arguments.length?(l=!!e,s):l},s.interpolator=function(e){return arguments.length?(c=e,s):c},s.range=u(oG),s.rangeRound=u(oK),s.unknown=function(e){return arguments.length?(o=e,s):o},function(o){return n=o,e=o(i),t=o(a),r=e===t?0:1/(t-e),s}}function cj(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function cS(){var e=iE(cO());return e.copy=function(){return cj(e,cS()).exponent(e.exponent())},nb.apply(e,arguments)}function cA(){return cS.apply(null,arguments).exponent(.5)}function cE(){var e,t,r,n,o,i,a,c=0,l=.5,s=1,u=1,f=o0,p=!1;function d(e){return isNaN(e=+e)?a:(e=.5+((e=+i(e))-t)*(u*e<u*t?n:o),f(p?Math.max(0,Math.min(1,e)):e))}function h(e){return function(t){var r,n,o;return arguments.length?([r,n,o]=t,f=function(e,t){void 0===t&&(t=e,e=oG);for(var r=0,n=t.length-1,o=t[0],i=Array(n<0?0:n);r<n;)i[r]=e(o,o=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return i[t](e-t)}}(e,[r,n,o]),d):[f(0),f(.5),f(1)]}}return d.domain=function(a){return arguments.length?([c,l,s]=a,e=i(c=+c),t=i(l=+l),r=i(s=+s),n=e===t?0:.5/(t-e),o=t===r?0:.5/(r-t),u=t<e?-1:1,d):[c,l,s]},d.clamp=function(e){return arguments.length?(p=!!e,d):p},d.interpolator=function(e){return arguments.length?(f=e,d):f},d.range=h(oG),d.rangeRound=h(oK),d.unknown=function(e){return arguments.length?(a=e,d):a},function(a){return i=a,e=a(c),t=a(l),r=a(s),n=e===t?0:.5/(t-e),o=t===r?0:.5/(r-t),u=t<e?-1:1,d}}function ck(){var e=iE(cE());return e.copy=function(){return cj(e,ck()).exponent(e.exponent())},nb.apply(e,arguments)}function cP(){return ck.apply(null,arguments).exponent(.5)}function cM(e,t){if((o=e.length)>1)for(var r,n,o,i=1,a=e[t[0]],c=a.length;i<o;++i)for(n=a,a=e[t[i]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function c_(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function cT(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function cN(e,t){return e[t]}function cC(e){let t=[];return t.key=e,t}cF=(cL=function(e){var t=e.dateTime,r=e.date,n=e.time,o=e.periods,i=e.days,a=e.shortDays,c=e.months,l=e.shortMonths,s=av(o),u=am(o),f=av(i),p=am(i),d=av(a),h=am(a),y=av(c),v=am(c),m=av(l),b=am(l),g={a:function(e){return a[e.getDay()]},A:function(e){return i[e.getDay()]},b:function(e){return l[e.getMonth()]},B:function(e){return c[e.getMonth()]},c:null,d:aL,e:aL,f:aq,g:a0,G:a2,H:aF,I:az,j:a$,L:aU,m:aZ,M:aW,p:function(e){return o[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:cy,s:cv,S:aH,u:aX,U:aV,V:aG,w:aK,W:aJ,x:null,X:null,y:aQ,Y:a1,Z:a4,"%":ch},x={a:function(e){return a[e.getUTCDay()]},A:function(e){return i[e.getUTCDay()]},b:function(e){return l[e.getUTCMonth()]},B:function(e){return c[e.getUTCMonth()]},c:null,d:a5,e:a5,f:a9,g:cu,G:cp,H:a3,I:a6,j:a7,L:a8,m:ce,M:ct,p:function(e){return o[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:cy,s:cv,S:cr,u:cn,U:co,V:ca,w:cc,W:cl,x:null,X:null,y:cs,Y:cf,Z:cd,"%":ch},w={a:function(e,t,r){var n=d.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=m.exec(t.slice(r));return n?(e.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return S(e,t,r,n)},d:aP,e:aP,f:aD,g:aS,G:aj,H:a_,I:a_,j:aM,L:aC,m:ak,M:aT,p:function(e,t,r){var n=s.exec(t.slice(r));return n?(e.p=u.get(n[0].toLowerCase()),r+n[0].length):-1},q:aE,Q:aB,s:aR,S:aN,u:ag,U:ax,V:aw,w:ab,W:aO,x:function(e,t,n){return S(e,r,t,n)},X:function(e,t,r){return S(e,n,t,r)},y:aS,Y:aj,Z:aA,"%":aI};function O(e,t){return function(r){var n,o,i,a=[],c=-1,l=0,s=e.length;for(r instanceof Date||(r=new Date(+r));++c<s;)37===e.charCodeAt(c)&&(a.push(e.slice(l,c)),null!=(o=au[n=e.charAt(++c)])?n=e.charAt(++c):o="e"===n?" ":"0",(i=t[n])&&(n=i(r,o)),a.push(n),l=c+1);return a.push(e.slice(l,c)),a.join("")}}function j(e,t){return function(r){var n,o,i=as(1900,void 0,1);if(S(i,e,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!t||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=al(as(i.y,0,1))).getUTCDay())>4||0===o?i2.ceil(n):i2(n),n=iZ.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=ac(as(i.y,0,1))).getDay())>4||0===o?iV.ceil(n):iV(n),n=iq.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:"W"in i?1:0),o="Z"in i?al(as(i.y,0,1)).getUTCDay():ac(as(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,al(i)):ac(i)}}function S(e,t,r,n){for(var o,i,a=0,c=t.length,l=r.length;a<c;){if(n>=l)return -1;if(37===(o=t.charCodeAt(a++))){if(!(i=w[(o=t.charAt(a++))in au?t.charAt(a++):o])||(n=i(e,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return g.x=O(r,g),g.X=O(n,g),g.c=O(t,g),x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),{format:function(e){var t=O(e+="",g);return t.toString=function(){return e},t},parse:function(e){var t=j(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=j(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,cL.parse,cz=cL.utcFormat,cL.utcParse,Array.prototype.slice;var cD,cI,cB,cR,cL,cF,cz,c$,cU,cq=r(50454),cZ=r.n(cq),cW=r(91654),cH=r.n(cW),cX=r(93496),cV=r.n(cX),cY=r(20569),cG=r.n(cY),cK=!0,cJ="[DecimalError] ",cQ=cJ+"Invalid argument: ",c0=cJ+"Exponent out of range: ",c1=Math.floor,c2=Math.pow,c4=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,c5=c1(1286742750677284.5),c3={};function c6(e,t){var r,n,o,i,a,c,l,s,u=e.constructor,f=u.precision;if(!e.s||!t.s)return t.s||(t=new u(e)),cK?la(t,f):t;if(l=e.d,s=t.d,a=e.e,o=t.e,l=l.slice(),i=a-o){for(i<0?(n=l,i=-i,c=s.length):(n=s,o=a,c=l.length),i>(c=(a=Math.ceil(f/7))>c?a+1:c+1)&&(i=c,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((c=l.length)-(i=s.length)<0&&(i=c,n=s,s=l,l=n),r=0;i;)r=(l[--i]=l[i]+s[i]+r)/1e7|0,l[i]%=1e7;for(r&&(l.unshift(r),++o),c=l.length;0==l[--c];)l.pop();return t.d=l,t.e=o,cK?la(t,f):t}function c7(e,t,r){if(e!==~~e||e<t||e>r)throw Error(cQ+e)}function c8(e){var t,r,n,o=e.length-1,i="",a=e[0];if(o>0){for(i+=a,t=1;t<o;t++)(r=7-(n=e[t]+"").length)&&(i+=ln(r)),i+=n;(r=7-(n=(a=e[t])+"").length)&&(i+=ln(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}c3.absoluteValue=c3.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},c3.comparedTo=c3.cmp=function(e){var t,r,n,o;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(o=e.d.length)?n:o;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},c3.decimalPlaces=c3.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},c3.dividedBy=c3.div=function(e){return c9(this,new this.constructor(e))},c3.dividedToIntegerBy=c3.idiv=function(e){var t=this.constructor;return la(c9(this,new t(e),0,1),t.precision)},c3.equals=c3.eq=function(e){return!this.cmp(e)},c3.exponent=function(){return lt(this)},c3.greaterThan=c3.gt=function(e){return this.cmp(e)>0},c3.greaterThanOrEqualTo=c3.gte=function(e){return this.cmp(e)>=0},c3.isInteger=c3.isint=function(){return this.e>this.d.length-2},c3.isNegative=c3.isneg=function(){return this.s<0},c3.isPositive=c3.ispos=function(){return this.s>0},c3.isZero=function(){return 0===this.s},c3.lessThan=c3.lt=function(e){return 0>this.cmp(e)},c3.lessThanOrEqualTo=c3.lte=function(e){return 1>this.cmp(e)},c3.logarithm=c3.log=function(e){var t,r=this.constructor,n=r.precision,o=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(cU))throw Error(cJ+"NaN");if(this.s<1)throw Error(cJ+(this.s?"NaN":"-Infinity"));return this.eq(cU)?new r(0):(cK=!1,t=c9(lo(this,o),lo(e,o),o),cK=!0,la(t,n))},c3.minus=c3.sub=function(e){return e=new this.constructor(e),this.s==e.s?lc(this,e):c6(this,(e.s=-e.s,e))},c3.modulo=c3.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(cJ+"NaN");return this.s?(cK=!1,t=c9(this,e,0,1).times(e),cK=!0,this.minus(t)):la(new r(this),n)},c3.naturalExponential=c3.exp=function(){return le(this)},c3.naturalLogarithm=c3.ln=function(){return lo(this)},c3.negated=c3.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},c3.plus=c3.add=function(e){return e=new this.constructor(e),this.s==e.s?c6(this,e):lc(this,(e.s=-e.s,e))},c3.precision=c3.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(cQ+e);if(t=lt(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},c3.squareRoot=c3.sqrt=function(){var e,t,r,n,o,i,a,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(cJ+"NaN")}for(e=lt(this),cK=!1,0==(o=Math.sqrt(+this))||o==1/0?(((t=c8(this.d)).length+e)%2==0&&(t+="0"),o=Math.sqrt(t),e=c1((e+1)/2)-(e<0||e%2),n=new c(t=o==1/0?"5e"+e:(t=o.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new c(o.toString()),o=a=(r=c.precision)+3;;)if(n=(i=n).plus(c9(this,i,a+2)).times(.5),c8(i.d).slice(0,a)===(t=c8(n.d)).slice(0,a)){if(t=t.slice(a-3,a+1),o==a&&"4999"==t){if(la(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=t)break;a+=4}return cK=!0,la(n,r)},c3.times=c3.mul=function(e){var t,r,n,o,i,a,c,l,s,u=this.constructor,f=this.d,p=(e=new u(e)).d;if(!this.s||!e.s)return new u(0);for(e.s*=this.s,r=this.e+e.e,(l=f.length)<(s=p.length)&&(i=f,f=p,p=i,a=l,l=s,s=a),i=[],n=a=l+s;n--;)i.push(0);for(n=s;--n>=0;){for(t=0,o=l+n;o>n;)c=i[o]+p[n]*f[o-n-1]+t,i[o--]=c%1e7|0,t=c/1e7|0;i[o]=(i[o]+t)%1e7|0}for(;!i[--a];)i.pop();return t?++r:i.shift(),e.d=i,e.e=r,cK?la(e,u.precision):e},c3.toDecimalPlaces=c3.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(c7(e,0,1e9),void 0===t?t=n.rounding:c7(t,0,8),la(r,e+lt(r)+1,t))},c3.toExponential=function(e,t){var r,n=this,o=n.constructor;return void 0===e?r=ll(n,!0):(c7(e,0,1e9),void 0===t?t=o.rounding:c7(t,0,8),r=ll(n=la(new o(n),e+1,t),!0,e+1)),r},c3.toFixed=function(e,t){var r,n,o=this.constructor;return void 0===e?ll(this):(c7(e,0,1e9),void 0===t?t=o.rounding:c7(t,0,8),r=ll((n=la(new o(this),e+lt(this)+1,t)).abs(),!1,e+lt(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},c3.toInteger=c3.toint=function(){var e=this.constructor;return la(new e(this),lt(this)+1,e.rounding)},c3.toNumber=function(){return+this},c3.toPower=c3.pow=function(e){var t,r,n,o,i,a,c=this,l=c.constructor,s=+(e=new l(e));if(!e.s)return new l(cU);if(!(c=new l(c)).s){if(e.s<1)throw Error(cJ+"Infinity");return c}if(c.eq(cU))return c;if(n=l.precision,e.eq(cU))return la(c,n);if(a=(t=e.e)>=(r=e.d.length-1),i=c.s,a){if((r=s<0?-s:s)<=9007199254740991){for(o=new l(cU),t=Math.ceil(n/7+4),cK=!1;r%2&&ls((o=o.times(c)).d,t),0!==(r=c1(r/2));)ls((c=c.times(c)).d,t);return cK=!0,e.s<0?new l(cU).div(o):la(o,n)}}else if(i<0)throw Error(cJ+"NaN");return i=i<0&&1&e.d[Math.max(t,r)]?-1:1,c.s=1,cK=!1,o=e.times(lo(c,n+12)),cK=!0,(o=le(o)).s=i,o},c3.toPrecision=function(e,t){var r,n,o=this,i=o.constructor;return void 0===e?(r=lt(o),n=ll(o,r<=i.toExpNeg||r>=i.toExpPos)):(c7(e,1,1e9),void 0===t?t=i.rounding:c7(t,0,8),r=lt(o=la(new i(o),e,t)),n=ll(o,e<=r||r<=i.toExpNeg,e)),n},c3.toSignificantDigits=c3.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(c7(e,1,1e9),void 0===t?t=r.rounding:c7(t,0,8)),la(new r(this),e,t)},c3.toString=c3.valueOf=c3.val=c3.toJSON=c3[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=lt(this),t=this.constructor;return ll(this,e<=t.toExpNeg||e>=t.toExpPos)};var c9=function(){function e(e,t){var r,n=0,o=e.length;for(e=e.slice();o--;)r=e[o]*t+n,e[o]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(e[o]!=t[o]){i=e[o]>t[o]?1:-1;break}return i}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=e[r]<t[r]?1:0,e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,o,i,a){var c,l,s,u,f,p,d,h,y,v,m,b,g,x,w,O,j,S,A=n.constructor,E=n.s==o.s?1:-1,k=n.d,P=o.d;if(!n.s)return new A(n);if(!o.s)throw Error(cJ+"Division by zero");for(s=0,l=n.e-o.e,j=P.length,w=k.length,h=(d=new A(E)).d=[];P[s]==(k[s]||0);)++s;if(P[s]>(k[s]||0)&&--l,(b=null==i?i=A.precision:a?i+(lt(n)-lt(o))+1:i)<0)return new A(0);if(b=b/7+2|0,s=0,1==j)for(u=0,P=P[0],b++;(s<w||u)&&b--;s++)g=1e7*u+(k[s]||0),h[s]=g/P|0,u=g%P|0;else{for((u=1e7/(P[0]+1)|0)>1&&(P=e(P,u),k=e(k,u),j=P.length,w=k.length),x=j,v=(y=k.slice(0,j)).length;v<j;)y[v++]=0;(S=P.slice()).unshift(0),O=P[0],P[1]>=1e7/2&&++O;do u=0,(c=t(P,y,j,v))<0?(m=y[0],j!=v&&(m=1e7*m+(y[1]||0)),(u=m/O|0)>1?(u>=1e7&&(u=1e7-1),p=(f=e(P,u)).length,v=y.length,1==(c=t(f,y,p,v))&&(u--,r(f,j<p?S:P,p))):(0==u&&(c=u=1),f=P.slice()),(p=f.length)<v&&f.unshift(0),r(y,f,v),-1==c&&(v=y.length,(c=t(P,y,j,v))<1&&(u++,r(y,j<v?S:P,v))),v=y.length):0===c&&(u++,y=[0]),h[s++]=u,c&&y[0]?y[v++]=k[x]||0:(y=[k[x]],v=1);while((x++<w||void 0!==y[0])&&b--)}return h[0]||h.shift(),d.e=l,la(d,a?i+lt(d)+1:i)}}();function le(e,t){var r,n,o,i,a,c=0,l=0,s=e.constructor,u=s.precision;if(lt(e)>16)throw Error(c0+lt(e));if(!e.s)return new s(cU);for(null==t?(cK=!1,a=u):a=t,i=new s(.03125);e.abs().gte(.1);)e=e.times(i),l+=5;for(a+=Math.log(c2(2,l))/Math.LN10*2+5|0,r=n=o=new s(cU),s.precision=a;;){if(n=la(n.times(e),a),r=r.times(++c),c8((i=o.plus(c9(n,r,a))).d).slice(0,a)===c8(o.d).slice(0,a)){for(;l--;)o=la(o.times(o),a);return s.precision=u,null==t?(cK=!0,la(o,u)):o}o=i}}function lt(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function lr(e,t,r){if(t>e.LN10.sd())throw cK=!0,r&&(e.precision=r),Error(cJ+"LN10 precision limit exceeded");return la(new e(e.LN10),t)}function ln(e){for(var t="";e--;)t+="0";return t}function lo(e,t){var r,n,o,i,a,c,l,s,u,f=1,p=e,d=p.d,h=p.constructor,y=h.precision;if(p.s<1)throw Error(cJ+(p.s?"NaN":"-Infinity"));if(p.eq(cU))return new h(0);if(null==t?(cK=!1,s=y):s=t,p.eq(10))return null==t&&(cK=!0),lr(h,s);if(s+=10,h.precision=s,n=(r=c8(d)).charAt(0),!(15e14>Math.abs(i=lt(p))))return l=lr(h,s+2,y).times(i+""),p=lo(new h(n+"."+r.slice(1)),s-10).plus(l),h.precision=y,null==t?(cK=!0,la(p,y)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=c8((p=p.times(e)).d)).charAt(0),f++;for(i=lt(p),n>1?(p=new h("0."+r),i++):p=new h(n+"."+r.slice(1)),c=a=p=c9(p.minus(cU),p.plus(cU),s),u=la(p.times(p),s),o=3;;){if(a=la(a.times(u),s),c8((l=c.plus(c9(a,new h(o),s))).d).slice(0,s)===c8(c.d).slice(0,s))return c=c.times(2),0!==i&&(c=c.plus(lr(h,s+2,y).times(i+""))),c=c9(c,new h(f),s),h.precision=y,null==t?(cK=!0,la(c,y)):c;c=l,o+=2}}function li(e,t){var r,n,o;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(o=t.length;48===t.charCodeAt(o-1);)--o;if(t=t.slice(n,o)){if(o-=n,r=r-n-1,e.e=c1(r/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&e.d.push(+t.slice(0,n)),o-=7;n<o;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=o;for(;n--;)t+="0";if(e.d.push(+t),cK&&(e.e>c5||e.e<-c5))throw Error(c0+r)}else e.s=0,e.e=0,e.d=[0];return e}function la(e,t,r){var n,o,i,a,c,l,s,u,f=e.d;for(a=1,i=f[0];i>=10;i/=10)a++;if((n=t-a)<0)n+=7,o=t,s=f[u=0];else{if((u=Math.ceil((n+1)/7))>=(i=f.length))return e;for(a=1,s=i=f[u];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(c=s/(i=c2(10,a-o-1))%10|0,l=t<0||void 0!==f[u+1]||s%i,l=r<4?(c||l)&&(0==r||r==(e.s<0?3:2)):c>5||5==c&&(4==r||l||6==r&&(n>0?o>0?s/c2(10,a-o):0:f[u-1])%10&1||r==(e.s<0?8:7))),t<1||!f[0])return l?(i=lt(e),f.length=1,t=t-i-1,f[0]=c2(10,(7-t%7)%7),e.e=c1(-t/7)||0):(f.length=1,f[0]=e.e=e.s=0),e;if(0==n?(f.length=u,i=1,u--):(f.length=u+1,i=c2(10,7-n),f[u]=o>0?(s/c2(10,a-o)%c2(10,o)|0)*i:0),l)for(;;){if(0==u){1e7==(f[0]+=i)&&(f[0]=1,++e.e);break}if(f[u]+=i,1e7!=f[u])break;f[u--]=0,i=1}for(n=f.length;0===f[--n];)f.pop();if(cK&&(e.e>c5||e.e<-c5))throw Error(c0+lt(e));return e}function lc(e,t){var r,n,o,i,a,c,l,s,u,f,p=e.constructor,d=p.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new p(e),cK?la(t,d):t;if(l=e.d,f=t.d,n=t.e,s=e.e,l=l.slice(),a=s-n){for((u=a<0)?(r=l,a=-a,c=f.length):(r=f,n=s,c=l.length),a>(o=Math.max(Math.ceil(d/7),c)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((u=(o=l.length)<(c=f.length))&&(c=o),o=0;o<c;o++)if(l[o]!=f[o]){u=l[o]<f[o];break}a=0}for(u&&(r=l,l=f,f=r,t.s=-t.s),c=l.length,o=f.length-c;o>0;--o)l[c++]=0;for(o=f.length;o>a;){if(l[--o]<f[o]){for(i=o;i&&0===l[--i];)l[i]=1e7-1;--l[i],l[o]+=1e7}l[o]-=f[o]}for(;0===l[--c];)l.pop();for(;0===l[0];l.shift())--n;return l[0]?(t.d=l,t.e=n,cK?la(t,d):t):new p(0)}function ll(e,t,r){var n,o=lt(e),i=c8(e.d),a=i.length;return t?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+ln(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+ln(-o-1)+i,r&&(n=r-a)>0&&(i+=ln(n))):o>=a?(i+=ln(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+ln(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=ln(n))),e.s<0?"-"+i:i}function ls(e,t){if(e.length>t)return e.length=t,!0}function lu(e){if(!e||"object"!=typeof e)throw Error(cJ+"Object expected");var t,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<o.length;t+=3)if(void 0!==(n=e[r=o[t]])){if(c1(n)===n&&n>=o[t+1]&&n<=o[t+2])this[r]=n;else throw Error(cQ+r+": "+n)}if(void 0!==(n=e[r="LN10"])){if(n==Math.LN10)this[r]=new this(n);else throw Error(cQ+r+": "+n)}return this}var c$=function e(t){var r,n,o;function i(e){if(!(this instanceof i))return new i(e);if(this.constructor=i,e instanceof i){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(cQ+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return li(this,e.toString())}if("string"!=typeof e)throw Error(cQ+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,c4.test(e))li(this,e);else throw Error(cQ+e)}if(i.prototype=c3,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=e,i.config=i.set=lu,void 0===t&&(t={}),t)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)t.hasOwnProperty(n=o[r++])||(t[n]=this[n]);return i.config(t),i}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});cU=new c$(1);let lf=c$;function lp(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var ld=function(e){return e},lh={},ly=function(e){return e===lh},lv=function(e){return function t(){return 0==arguments.length||1==arguments.length&&ly(arguments.length<=0?void 0:arguments[0])?t:e.apply(void 0,arguments)}},lm=function(e){return function e(t,r){return 1===t?r:lv(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(e){return e!==lh}).length;return a>=t?r.apply(void 0,o):e(t-a,lv(function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=o.map(function(e){return ly(e)?t.shift():e});return r.apply(void 0,((function(e){if(Array.isArray(e))return lp(e)})(i)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(i)||function(e,t){if(e){if("string"==typeof e)return lp(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lp(e,void 0)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(t))}))})}(e.length,e)},lb=function(e,t){for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},lg=lm(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(e){return t[e]}).map(e)}),lx=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return ld;var n=t.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(e,t){return t(e)},o.apply(void 0,arguments))}},lw=function(e){return Array.isArray(e)?e.reverse():e.split("").reverse.join("")},lO=function(e){var t=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return t&&o.every(function(e,r){return e===t[r]})?r:(t=o,r=e.apply(void 0,o))}};lm(function(e,t,r){var n=+e;return n+r*(+t-n)}),lm(function(e,t,r){var n=t-+e;return(r-e)/(n=n||1/0)}),lm(function(e,t,r){var n=t-+e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});let lj={rangeStep:function(e,t,r){for(var n=new lf(e),o=0,i=[];n.lt(t)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(e){return 0===e?1:Math.floor(new lf(e).abs().log(10).toNumber())+1}};function lS(e){return function(e){if(Array.isArray(e))return lk(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||lE(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function lA(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,c=e[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!t||r.length!==t);n=!0);}catch(e){o=!0,i=e}finally{try{n||null==c.return||c.return()}finally{if(o)throw i}}return r}}(e,t)||lE(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function lE(e,t){if(e){if("string"==typeof e)return lk(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lk(e,t)}}function lk(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function lP(e){var t=lA(e,2),r=t[0],n=t[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function lM(e,t,r){if(e.lte(0))return new lf(0);var n=lj.getDigitCount(e.toNumber()),o=new lf(10).pow(n),i=e.div(o),a=1!==n?.05:.1,c=new lf(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return t?c:new lf(Math.ceil(c))}function l_(e,t,r){var n=1,o=new lf(e);if(!o.isint()&&r){var i=Math.abs(e);i<1?(n=new lf(10).pow(lj.getDigitCount(e)-1),o=new lf(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new lf(Math.floor(e)))}else 0===e?o=new lf(Math.floor((t-1)/2)):r||(o=new lf(Math.floor(e)));var a=Math.floor((t-1)/2);return lx(lg(function(e){return o.add(new lf(e-a).mul(n)).toNumber()}),lb)(0,t)}var lT=lO(function(e){var t=lA(e,2),r=t[0],n=t[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=lA(lP([r,n]),2),l=c[0],s=c[1];if(l===-1/0||s===1/0){var u=s===1/0?[l].concat(lS(lb(0,o-1).map(function(){return 1/0}))):[].concat(lS(lb(0,o-1).map(function(){return-1/0})),[s]);return r>n?lw(u):u}if(l===s)return l_(l,o,i);var f=function e(t,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-t)/(n-1)))return{step:new lf(0),tickMin:new lf(0),tickMax:new lf(0)};var c=lM(new lf(r).sub(t).div(n-1),o,a),l=Math.ceil((i=t<=0&&r>=0?new lf(0):(i=new lf(t).add(r).div(2)).sub(new lf(i).mod(c))).sub(t).div(c).toNumber()),s=Math.ceil(new lf(r).sub(i).div(c).toNumber()),u=l+s+1;return u>n?e(t,r,n,o,a+1):(u<n&&(s=r>0?s+(n-u):s,l=r>0?l:l+(n-u)),{step:c,tickMin:i.sub(new lf(l).mul(c)),tickMax:i.add(new lf(s).mul(c))})}(l,s,a,i),p=f.step,d=f.tickMin,h=f.tickMax,y=lj.rangeStep(d,h.add(new lf(.1).mul(p)),p);return r>n?lw(y):y});lO(function(e){var t=lA(e,2),r=t[0],n=t[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=lA(lP([r,n]),2),l=c[0],s=c[1];if(l===-1/0||s===1/0)return[r,n];if(l===s)return l_(l,o,i);var u=lM(new lf(s).sub(l).div(a-1),i,0),f=lx(lg(function(e){return new lf(l).add(new lf(e).mul(u)).toNumber()}),lb)(0,a).filter(function(e){return e>=l&&e<=s});return r>n?lw(f):f});var lN=lO(function(e,t){var r=lA(e,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=lA(lP([n,o]),2),c=a[0],l=a[1];if(c===-1/0||l===1/0)return[n,o];if(c===l)return[c];var s=lM(new lf(l).sub(c).div(Math.max(t,2)-1),i,0),u=[].concat(lS(lj.rangeStep(new lf(c),new lf(l).sub(new lf(.99).mul(s)),s)),[l]);return n>o?lw(u):u}),lC=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function lD(e){return(lD="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function lI(){return(lI=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function lB(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function lR(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(lR=function(){return!!e})()}function lL(e){return(lL=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function lF(e,t){return(lF=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function lz(e,t,r){return(t=l$(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function l$(e){var t=function(e,t){if("object"!=lD(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=lD(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==lD(t)?t:t+""}var lU=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=lL(e),function(e,t){if(t&&("object"===lD(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,lR()?Reflect.construct(e,t||[],lL(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&lF(e,t)}(r,e),t=[{key:"render",value:function(){var e=this.props,t=e.offset,r=e.layout,n=e.width,o=e.dataKey,i=e.data,c=e.dataPointFormatter,l=e.xAxis,s=e.yAxis,u=eh(function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,lC),!1);"x"===this.props.direction&&"number"!==l.type&&eP(!1);var f=i.map(function(e){var i,f,p=c(e,o),d=p.x,h=p.y,y=p.value,v=p.errorVal;if(!v)return null;var m=[];if(Array.isArray(v)){var b=function(e){if(Array.isArray(e))return e}(v)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{for(i=(r=r.call(e)).next;!(l=(n=i.call(r)).done)&&(c.push(n.value),2!==c.length);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(v,2)||function(e,t){if(e){if("string"==typeof e)return lB(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lB(e,2)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=b[0],f=b[1]}else i=f=v;if("vertical"===r){var g=l.scale,x=h+t,w=x+n,O=x-n,j=g(y-i),S=g(y+f);m.push({x1:S,y1:w,x2:S,y2:O}),m.push({x1:j,y1:x,x2:S,y2:x}),m.push({x1:j,y1:w,x2:j,y2:O})}else if("horizontal"===r){var A=s.scale,E=d+t,k=E-n,P=E+n,M=A(y-i),_=A(y+f);m.push({x1:k,y1:_,x2:P,y2:_}),m.push({x1:E,y1:M,x2:E,y2:_}),m.push({x1:k,y1:M,x2:P,y2:M})}return a().createElement(eD,lI({className:"recharts-errorBar",key:"bar-".concat(m.map(function(e){return"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)}))},u),m.map(function(e){return a().createElement("line",lI({},e,{key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)}))}))});return a().createElement(eD,{className:"recharts-errorBars"},f)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,l$(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function lq(e){return(lq="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function lZ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function lW(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?lZ(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=lq(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=lq(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==lq(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):lZ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}lz(lU,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),lz(lU,"displayName","ErrorBar");var lH=function(e){var t,r=e.children,n=e.formattedGraphicalItems,o=e.legendWidth,i=e.legendContent,a=eu(r,t3);if(!a)return null;var c=t3.defaultProps,l=void 0!==c?lW(lW({},c),a.props):{};return t=a.props&&a.props.payload?a.props&&a.props.payload:"children"===i?(n||[]).reduce(function(e,t){var r=t.item,n=t.props,o=n.sectors||n.data||[];return e.concat(o.map(function(e){return{type:a.props.iconType||r.props.legendType,value:e.name,color:e.fill,payload:e}}))},[]):(n||[]).map(function(e){var t=e.item,r=t.type.defaultProps,n=void 0!==r?lW(lW({},r),t.props):{},o=n.dataKey,i=n.name,a=n.legendType;return{inactive:n.hide,dataKey:o,type:l.iconType||a||"square",color:l2(t),value:i||o,payload:n}}),lW(lW(lW({},l),t3.getWithHeight(a,o)),{},{payload:t,item:a})};function lX(e){return(lX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function lV(e){return function(e){if(Array.isArray(e))return lY(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return lY(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lY(e,void 0)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function lY(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function lG(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function lK(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?lG(Object(r),!0).forEach(function(t){lJ(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):lG(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function lJ(e,t,r){var n;return(n=function(e,t){if("object"!=lX(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=lX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==lX(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function lQ(e,t,r){return M()(e)||M()(t)?r:C(t)?A()(e,t,r):q()(t)?t(e):r}function l0(e,t,r,n){var o=cV()(e,function(e){return lQ(e,t)});if("number"===r){var i=o.filter(function(e){return N(e)||parseFloat(e)});return i.length?[cH()(i),cZ()(i)]:[1/0,-1/0]}return(n?o.filter(function(e){return!M()(e)}):o).map(function(e){return C(e)||e instanceof Date?e:""})}var l1=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(t=null==r?void 0:r.length)&&void 0!==t?t:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var c=o.range,l=0;l<a;l++){var s=l>0?n[l-1].coordinate:n[a-1].coordinate,u=n[l].coordinate,f=l>=a-1?n[0].coordinate:n[l+1].coordinate,p=void 0;if(_(u-s)!==_(f-u)){var d=[];if(_(f-u)===_(c[1]-c[0])){p=f;var h=u+c[1]-c[0];d[0]=Math.min(h,(h+s)/2),d[1]=Math.max(h,(h+s)/2)}else{p=s;var y=f+c[1]-c[0];d[0]=Math.min(u,(y+u)/2),d[1]=Math.max(u,(y+u)/2)}var v=[Math.min(u,(p+u)/2),Math.max(u,(p+u)/2)];if(e>v[0]&&e<=v[1]||e>=d[0]&&e<=d[1]){i=n[l].index;break}}else{var m=Math.min(s,f),b=Math.max(s,f);if(e>(m+u)/2&&e<=(b+u)/2){i=n[l].index;break}}}else for(var g=0;g<a;g++)if(0===g&&e<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&e>(r[g].coordinate+r[g-1].coordinate)/2&&e<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&e>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i},l2=function(e){var t,r,n=e.type.displayName,o=null!==(t=e.type)&&void 0!==t&&t.defaultProps?lK(lK({},e.type.defaultProps),e.props):e.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},l4=function(e){var t=e.barSize,r=e.totalSize,n=e.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),c=0,l=a.length;c<l;c++)for(var s=o[a[c]].stackGroups,u=Object.keys(s),f=0,p=u.length;f<p;f++){var d=s[u[f]],h=d.items,y=d.cateAxisId,v=h.filter(function(e){return ei(e.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?lK(lK({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];i[x]||(i[x]=[]);var w=M()(g)?t:g;i[x].push({item:v[0],stackList:v.slice(1),barSize:M()(w)?void 0:B(w,r,0)})}}return i},l5=function(e){var t,r=e.barGap,n=e.barCategoryGap,o=e.bandSize,i=e.sizeList,a=void 0===i?[]:i,c=e.maxBarSize,l=a.length;if(l<1)return null;var s=B(r,o,0,!0),u=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=o/l,d=a.reduce(function(e,t){return e+t.barSize||0},0);(d+=(l-1)*s)>=o&&(d-=(l-1)*s,s=0),d>=o&&p>0&&(f=!0,p*=.9,d=l*p);var h={offset:((o-d)/2>>0)-s,size:0};t=a.reduce(function(e,t){var r={item:t.item,position:{offset:h.offset+h.size+s,size:f?p:t.barSize}},n=[].concat(lV(e),[r]);return h=n[n.length-1].position,t.stackList&&t.stackList.length&&t.stackList.forEach(function(e){n.push({item:e,position:h})}),n},u)}else{var y=B(n,o,0,!0);o-2*y-(l-1)*s<=0&&(s=0);var v=(o-2*y-(l-1)*s)/l;v>1&&(v>>=0);var m=c===+c?Math.min(v,c):v;t=a.reduce(function(e,t,r){var n=[].concat(lV(e),[{item:t.item,position:{offset:y+(v+s)*r+(v-m)/2,size:m}}]);return t.stackList&&t.stackList.length&&t.stackList.forEach(function(e){n.push({item:e,position:n[n.length-1].position})}),n},u)}return t},l3=function(e,t,r,n){var o=r.children,i=r.width,a=r.margin,c=lH({children:o,legendWidth:i-(a.left||0)-(a.right||0)});if(c){var l=n||{},s=l.width,u=l.height,f=c.align,p=c.verticalAlign,d=c.layout;if(("vertical"===d||"horizontal"===d&&"middle"===p)&&"center"!==f&&N(e[f]))return lK(lK({},e),{},lJ({},f,e[f]+(s||0)));if(("horizontal"===d||"vertical"===d&&"center"===f)&&"middle"!==p&&N(e[p]))return lK(lK({},e),{},lJ({},p,e[p]+(u||0)))}return e},l6=function(e,t,r,n,o){var i=es(t.props.children,lU).filter(function(e){var t;return t=e.props.direction,!!M()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===t?"xAxis"===o:"y"!==t||"yAxis"===o)});if(i&&i.length){var a=i.map(function(e){return e.props.dataKey});return e.reduce(function(e,t){var n=lQ(t,r);if(M()(n))return e;var o=Array.isArray(n)?[cH()(n),cZ()(n)]:[n,n],i=a.reduce(function(e,r){var n=lQ(t,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,e[0]),Math.max(a,e[1])]},[1/0,-1/0]);return[Math.min(i[0],e[0]),Math.max(i[1],e[1])]},[1/0,-1/0])}return null},l7=function(e,t,r,n,o){var i=t.map(function(t){return l6(e,t,r,o,n)}).filter(function(e){return!M()(e)});return i&&i.length?i.reduce(function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]},[1/0,-1/0]):null},l8=function(e,t,r,n,o){var i=t.map(function(t){var i=t.props.dataKey;return"number"===r&&i&&l6(e,t,i,n)||l0(e,i,r,o)});if("number"===r)return i.reduce(function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]},[1/0,-1/0]);var a={};return i.reduce(function(e,t){for(var r=0,n=t.length;r<n;r++)a[t[r]]||(a[t[r]]=!0,e.push(t[r]));return e},[])},l9=function(e,t){return"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t},se=function(e,t,r){if(!e)return null;var n=e.scale,o=e.duplicateDomain,i=e.type,a=e.range,c="scaleBand"===e.realScaleType?n.bandwidth()/2:2,l=(t||r)&&"category"===i&&n.bandwidth?n.bandwidth()/c:0;return(l="angleAxis"===e.axisType&&(null==a?void 0:a.length)>=2?2*_(a[0]-a[1])*l:l,t&&(e.ticks||e.niceTicks))?(e.ticks||e.niceTicks).map(function(e){return{coordinate:n(o?o.indexOf(e):e)+l,value:e,offset:l}}).filter(function(e){return!j()(e.coordinate)}):e.isCategorical&&e.categoricalDomain?e.categoricalDomain.map(function(e,t){return{coordinate:n(e)+l,value:e,index:t,offset:l}}):n.ticks&&!r?n.ticks(e.tickCount).map(function(e){return{coordinate:n(e)+l,value:e,offset:l}}):n.domain().map(function(e,t){return{coordinate:n(e)+l,value:o?o[e]:e,index:t,offset:l}})},st=new WeakMap,sr=function(e,t){if("function"!=typeof t)return e;st.has(e)||st.set(e,new WeakMap);var r=st.get(e);if(r.has(t))return r.get(t);var n=function(){e.apply(void 0,arguments),t.apply(void 0,arguments)};return r.set(t,n),n},sn=function(e,t,r){var o=e.scale,i=e.type,a=e.layout,c=e.axisType;if("auto"===o)return"radial"===a&&"radiusAxis"===c?{scale:nS(),realScaleType:"band"}:"radial"===a&&"angleAxis"===c?{scale:iu(),realScaleType:"linear"}:"category"===i&&t&&(t.indexOf("LineChart")>=0||t.indexOf("AreaChart")>=0||t.indexOf("ComposedChart")>=0&&!r)?{scale:nA(),realScaleType:"point"}:"category"===i?{scale:nS(),realScaleType:"band"}:{scale:iu(),realScaleType:"linear"};if(w()(o)){var l="scale".concat(tc()(o));return{scale:(n[l]||nA)(),realScaleType:n[l]?l:"point"}}return q()(o)?{scale:o}:{scale:nA(),realScaleType:"point"}},so=function(e){var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=e(t[0]),c=e(t[r-1]);(a<o||a>i||c<o||c>i)&&e.domain([t[0],t[r-1]])}},si={sign:function(e){var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var o=0,i=0,a=0;a<t;++a){var c=j()(e[a][r][1])?e[a][r][0]:e[a][r][1];c>=0?(e[a][r][0]=o,e[a][r][1]=o+c,o=e[a][r][1]):(e[a][r][0]=i,e[a][r][1]=i+c,i=e[a][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,o,i=0,a=e[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}cM(e,t)}},none:cM,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,o=e[t[0]],i=o.length;n<i;++n){for(var a=0,c=0;a<r;++a)c+=e[a][n][1]||0;o[n][1]+=o[n][0]=-c/2}cM(e,t)}},wiggle:function(e,t){if((o=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var c=0,l=0,s=0;c<o;++c){for(var u=e[t[c]],f=u[a][1]||0,p=(f-(u[a-1][1]||0))/2,d=0;d<c;++d){var h=e[t[d]];p+=(h[a][1]||0)-(h[a-1][1]||0)}l+=f,s+=p*f}r[a-1][1]+=r[a-1][0]=i,l&&(i-=s/l)}r[a-1][1]+=r[a-1][0]=i,cM(e,t)}},positive:function(e){var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var o=0,i=0;i<t;++i){var a=j()(e[i][r][1])?e[i][r][0]:e[i][r][1];a>=0?(e[i][r][0]=o,e[i][r][1]=o+a,o=e[i][r][1]):(e[i][r][0]=0,e[i][r][1]=0)}}},sa=function(e,t,r){var n=t.map(function(e){return e.props.dataKey}),o=si[r];return(function(){var e=tj([]),t=cT,r=cM,n=cN;function o(o){var i,a,c=Array.from(e.apply(this,arguments),cC),l=c.length,s=-1;for(let e of o)for(i=0,++s;i<l;++i)(c[i][s]=[0,+n(e,c[i].key,s,o)]).data=e;for(i=0,a=c_(t(c));i<l;++i)c[a[i]].index=i;return r(c,a),c}return o.keys=function(t){return arguments.length?(e="function"==typeof t?t:tj(Array.from(t)),o):e},o.value=function(e){return arguments.length?(n="function"==typeof e?e:tj(+e),o):n},o.order=function(e){return arguments.length?(t=null==e?cT:"function"==typeof e?e:tj(Array.from(e)),o):t},o.offset=function(e){return arguments.length?(r=null==e?cM:e,o):r},o})().keys(n).value(function(e,t){return+lQ(e,t,0)}).order(cT).offset(o)(e)},sc=function(e,t,r,n,o,i){if(!e)return null;var a=(i?t.reverse():t).reduce(function(e,t){var o,i=null!==(o=t.type)&&void 0!==o&&o.defaultProps?lK(lK({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(i.hide)return e;var c=i[r],l=e[c]||{hasStack:!1,stackGroups:{}};if(C(a)){var s=l.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};s.items.push(t),l.hasStack=!0,l.stackGroups[a]=s}else l.stackGroups[I("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[t]};return lK(lK({},e),{},lJ({},c,l))},{});return Object.keys(a).reduce(function(t,i){var c=a[i];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(t,i){var a=c.stackGroups[i];return lK(lK({},t),{},lJ({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:sa(e,a.items,o)}))},{})),lK(lK({},t),{},lJ({},i,c))},{})},sl=function(e,t){var r=t.realScaleType,n=t.type,o=t.tickCount,i=t.originalDomain,a=t.allowDecimals,c=r||t.scale;if("auto"!==c&&"linear"!==c)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var l=e.domain();if(!l.length)return null;var s=lT(l,o,a);return e.domain([cH()(s),cZ()(s)]),{niceTicks:s}}return o&&"number"===n?{niceTicks:lN(e.domain(),o,a)}:null},ss=function(e,t){var r,n=(null!==(r=e.type)&&void 0!==r&&r.defaultProps?lK(lK({},e.type.defaultProps),e.props):e.props).stackId;if(C(n)){var o=t[n];if(o){var i=o.items.indexOf(e);return i>=0?o.stackedData[i]:null}}return null},su=function(e,t,r){return Object.keys(e).reduce(function(n,o){var i=e[o].stackedData.reduce(function(e,n){var o=n.slice(t,r+1).reduce(function(e,t){return[cH()(t.concat([e[0]]).filter(N)),cZ()(t.concat([e[1]]).filter(N))]},[1/0,-1/0]);return[Math.min(e[0],o[0]),Math.max(e[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(e){return e===1/0||e===-1/0?0:e})},sf=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,sp=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,sd=function(e,t,r){if(q()(e))return e(t,r);if(!Array.isArray(e))return t;var n=[];if(N(e[0]))n[0]=r?e[0]:Math.min(e[0],t[0]);else if(sf.test(e[0])){var o=+sf.exec(e[0])[1];n[0]=t[0]-o}else q()(e[0])?n[0]=e[0](t[0]):n[0]=t[0];if(N(e[1]))n[1]=r?e[1]:Math.max(e[1],t[1]);else if(sp.test(e[1])){var i=+sp.exec(e[1])[1];n[1]=t[1]+i}else q()(e[1])?n[1]=e[1](t[1]):n[1]=t[1];return n},sh=function(e,t,r){if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var o=ek()(t,function(e){return e.coordinate}),i=1/0,a=1,c=o.length;a<c;a++){var l=o[a],s=o[a-1];i=Math.min((l.coordinate||0)-(s.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},sy=function(e,t,r){return!e||!e.length||cG()(e,A()(r,"type.defaultProps.domain"))?t:e},sv=function(e,t){var r=e.type.defaultProps?lK(lK({},e.type.defaultProps),e.props):e.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,c=r.tooltipType,l=r.chartType,s=r.hide;return lK(lK({},eh(e,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:l2(e),value:lQ(t,n),type:c,payload:t,chartType:l,hide:s})};function sm(e){return(sm="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function sb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function sg(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?sb(Object(r),!0).forEach(function(t){sx(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sb(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function sx(e,t,r){var n;return(n=function(e,t){if("object"!=sm(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=sm(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==sm(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var sw=["Webkit","Moz","O","ms"],sO=function(e,t){if(!e)return null;var r=e.replace(/(\w)/,function(e){return e.toUpperCase()}),n=sw.reduce(function(e,n){return sg(sg({},e),{},sx({},n+r,t))},{});return n[e]=t,n};function sj(e){return(sj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function sS(){return(sS=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function sA(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function sE(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?sA(Object(r),!0).forEach(function(t){sT(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sA(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function sk(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,sN(n.key),n)}}function sP(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(sP=function(){return!!e})()}function sM(e){return(sM=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function s_(e,t){return(s_=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function sT(e,t,r){return(t=sN(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sN(e){var t=function(e,t){if("object"!=sj(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=sj(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==sj(t)?t:t+""}var sC=function(e){var t=e.data,r=e.startIndex,n=e.endIndex,o=e.x,i=e.width,a=e.travellerWidth;if(!t||!t.length)return{};var c=t.length,l=nA().domain(eA()(0,c)).range([o,o+i-a]),s=l.domain().map(function(e){return l(e)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:l(r),endX:l(n),scale:l,scaleValues:s}},sD=function(e){return e.changedTouches&&!!e.changedTouches.length},sI=function(e){var t,r;function n(e){var t,r,o;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[e],r=sM(r),sT(t=function(e,t){if(t&&("object"===sj(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,sP()?Reflect.construct(r,o||[],sM(this).constructor):r.apply(this,o)),"handleDrag",function(e){t.leaveTimer&&(clearTimeout(t.leaveTimer),t.leaveTimer=null),t.state.isTravellerMoving?t.handleTravellerMove(e):t.state.isSlideMoving&&t.handleSlideDrag(e)}),sT(t,"handleTouchMove",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&t.handleDrag(e.changedTouches[0])}),sT(t,"handleDragEnd",function(){t.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var e=t.props,r=e.endIndex,n=e.onDragEnd,o=e.startIndex;null==n||n({endIndex:r,startIndex:o})}),t.detachDragEndListener()}),sT(t,"handleLeaveWrapper",function(){(t.state.isTravellerMoving||t.state.isSlideMoving)&&(t.leaveTimer=window.setTimeout(t.handleDragEnd,t.props.leaveTimeOut))}),sT(t,"handleEnterSlideOrTraveller",function(){t.setState({isTextActive:!0})}),sT(t,"handleLeaveSlideOrTraveller",function(){t.setState({isTextActive:!1})}),sT(t,"handleSlideDragStart",function(e){var r=sD(e)?e.changedTouches[0]:e;t.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),t.attachDragEndListener()}),t.travellerDragStartHandlers={startX:t.handleTravellerDragStart.bind(t,"startX"),endX:t.handleTravellerDragStart.bind(t,"endX")},t.state={},t}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&s_(e,t)}(n,e),t=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(e){var t=e.startX,r=e.endX,o=this.state.scaleValues,i=this.props,a=i.gap,c=i.data.length-1,l=n.getIndexInRange(o,Math.min(t,r)),s=n.getIndexInRange(o,Math.max(t,r));return{startIndex:l-l%a,endIndex:s===c?c:s-s%a}}},{key:"getTextOfTick",value:function(e){var t=this.props,r=t.data,n=t.tickFormatter,o=t.dataKey,i=lQ(r[e],o,e);return q()(n)?n(i,e):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(e){var t=this.state,r=t.slideMoveStartX,n=t.startX,o=t.endX,i=this.props,a=i.x,c=i.width,l=i.travellerWidth,s=i.startIndex,u=i.endIndex,f=i.onChange,p=e.pageX-r;p>0?p=Math.min(p,a+c-l-o,a+c-l-n):p<0&&(p=Math.max(p,a-n,a-o));var d=this.getIndex({startX:n+p,endX:o+p});(d.startIndex!==s||d.endIndex!==u)&&f&&f(d),this.setState({startX:n+p,endX:o+p,slideMoveStartX:e.pageX})}},{key:"handleTravellerDragStart",value:function(e,t){var r=sD(t)?t.changedTouches[0]:t;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:e,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(e){var t=this.state,r=t.brushMoveStartX,n=t.movingTravellerId,o=t.endX,i=t.startX,a=this.state[n],c=this.props,l=c.x,s=c.width,u=c.travellerWidth,f=c.onChange,p=c.gap,d=c.data,h={startX:this.state.startX,endX:this.state.endX},y=e.pageX-r;y>0?y=Math.min(y,l+s-u-a):y<0&&(y=Math.max(y,l-a)),h[n]=a+y;var v=this.getIndex(h),m=v.startIndex,b=v.endIndex,g=function(){var e=d.length-1;return"startX"===n&&(o>i?m%p==0:b%p==0)||o<i&&b===e||"endX"===n&&(o>i?b%p==0:m%p==0)||o>i&&b===e};this.setState(sT(sT({},n,a+y),"brushMoveStartX",e.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(e,t){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,c=this.state[t],l=o.indexOf(c);if(-1!==l){var s=l+e;if(-1!==s&&!(s>=o.length)){var u=o[s];"startX"===t&&u>=a||"endX"===t&&u<=i||this.setState(sT({},t,u),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,o=e.height,i=e.fill,c=e.stroke;return a().createElement("rect",{stroke:c,fill:i,x:t,y:r,width:n,height:o})}},{key:"renderPanorama",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,o=e.height,c=e.data,l=e.children,s=e.padding,u=i.Children.only(l);return u?a().cloneElement(u,{x:t,y:r,width:n,height:o,margin:s,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(e,t){var r,o,i=this,c=this.props,l=c.y,s=c.travellerWidth,u=c.height,f=c.traveller,p=c.ariaLabel,d=c.data,h=c.startIndex,y=c.endIndex,v=Math.max(e,this.props.x),m=sE(sE({},eh(this.props,!1)),{},{x:v,y:l,width:s,height:u}),b=p||"Min value: ".concat(null===(r=d[h])||void 0===r?void 0:r.name,", Max value: ").concat(null===(o=d[y])||void 0===o?void 0:o.name);return a().createElement(eD,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":e,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[t],onTouchStart:this.travellerDragStartHandlers[t],onKeyDown:function(e){["ArrowLeft","ArrowRight"].includes(e.key)&&(e.preventDefault(),e.stopPropagation(),i.handleTravellerMoveKeyboard("ArrowRight"===e.key?1:-1,t))},onFocus:function(){i.setState({isTravellerFocused:!0})},onBlur:function(){i.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(f,m))}},{key:"renderSlide",value:function(e,t){var r=this.props,n=r.y,o=r.height,i=r.stroke,c=r.travellerWidth;return a().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:i,fillOpacity:.2,x:Math.min(e,t)+c,y:n,width:Math.max(Math.abs(t-e)-c,0),height:o})}},{key:"renderText",value:function(){var e=this.props,t=e.startIndex,r=e.endIndex,n=e.y,o=e.height,i=e.travellerWidth,c=e.stroke,l=this.state,s=l.startX,u=l.endX,f={pointerEvents:"none",fill:c};return a().createElement(eD,{className:"recharts-brush-texts"},a().createElement(n3,sS({textAnchor:"end",verticalAnchor:"middle",x:Math.min(s,u)-5,y:n+o/2},f),this.getTextOfTick(t)),a().createElement(n3,sS({textAnchor:"start",verticalAnchor:"middle",x:Math.max(s,u)+i+5,y:n+o/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var e=this.props,t=e.data,r=e.className,n=e.children,o=e.x,i=e.y,c=e.width,l=e.height,s=e.alwaysShowText,u=this.state,f=u.startX,p=u.endX,d=u.isTextActive,h=u.isSlideMoving,y=u.isTravellerMoving,v=u.isTravellerFocused;if(!t||!t.length||!N(o)||!N(i)||!N(c)||!N(l)||c<=0||l<=0)return null;var b=(0,m.Z)("recharts-brush",r),g=1===a().Children.count(n),x=sO("userSelect","none");return a().createElement(eD,{className:b,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:x},this.renderBackground(),g&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(d||h||y||v||s)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(e){var t=e.x,r=e.y,n=e.width,o=e.height,i=e.stroke,c=Math.floor(r+o/2)-1;return a().createElement(a().Fragment,null,a().createElement("rect",{x:t,y:r,width:n,height:o,fill:i,stroke:"none"}),a().createElement("line",{x1:t+1,y1:c,x2:t+n-1,y2:c,fill:"none",stroke:"#fff"}),a().createElement("line",{x1:t+1,y1:c+2,x2:t+n-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(e,t){return a().isValidElement(e)?a().cloneElement(e,t):q()(e)?e(t):n.renderDefaultTraveller(t)}},{key:"getDerivedStateFromProps",value:function(e,t){var r=e.data,n=e.width,o=e.x,i=e.travellerWidth,a=e.updateId,c=e.startIndex,l=e.endIndex;if(r!==t.prevData||a!==t.prevUpdateId)return sE({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?sC({data:r,width:n,x:o,travellerWidth:i,startIndex:c,endIndex:l}):{scale:null,scaleValues:null});if(t.scale&&(n!==t.prevWidth||o!==t.prevX||i!==t.prevTravellerWidth)){t.scale.range([o,o+n-i]);var s=t.scale.domain().map(function(e){return t.scale(e)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:t.scale(e.startIndex),endX:t.scale(e.endIndex),scaleValues:s}}return null}},{key:"getIndexInRange",value:function(e,t){for(var r=e.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);e[i]>t?o=i:n=i}return t>=e[o]?o:n}}],t&&sk(n.prototype,t),r&&sk(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function sB(e){return(sB="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function sR(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function sL(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?sR(Object(r),!0).forEach(function(t){sF(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sR(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function sF(e,t,r){var n;return(n=function(e,t){if("object"!=sB(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=sB(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==sB(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sz(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}sT(sI,"displayName","Brush"),sT(sI,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var s$=Math.PI/180,sU=function(e,t,r,n){return{x:e+Math.cos(-s$*n)*r,y:t+Math.sin(-s$*n)*r}},sq=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},sZ=function(e,t){var r=e.x,n=e.y;return Math.sqrt(Math.pow(r-t.x,2)+Math.pow(n-t.y,2))},sW=function(e,t){var r=e.x,n=e.y,o=t.cx,i=t.cy,a=sZ({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var c=Math.acos((r-o)/a);return n>i&&(c=2*Math.PI-c),{radius:a,angle:180*c/Math.PI,angleInRadian:c}},sH=function(e){var t=e.startAngle,r=e.endAngle,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}},sX=function(e,t){var r,n=sW({x:e.x,y:e.y},t),o=n.radius,i=n.angle,a=t.innerRadius,c=t.outerRadius;if(o<a||o>c)return!1;if(0===o)return!0;var l=sH(t),s=l.startAngle,u=l.endAngle,f=i;if(s<=u){for(;f>u;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=u}else{for(;f>s;)f-=360;for(;f<u;)f+=360;r=f>=u&&f<=s}return r?sL(sL({},t),{},{radius:o,angle:f+360*Math.min(Math.floor(t.startAngle/360),Math.floor(t.endAngle/360))}):null},sV=function(e){return(0,i.isValidElement)(e)||q()(e)||"boolean"==typeof e?"":e.className};function sY(e){return(sY="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var sG=["offset"];function sK(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function sJ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function sQ(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?sJ(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=sY(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=sY(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==sY(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sJ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function s0(){return(s0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var s1=function(e){var t=e.value,r=e.formatter,n=M()(e.children)?t:e.children;return q()(r)?r(n):n},s2=function(e,t,r){var n,o,i=e.position,c=e.viewBox,l=e.offset,s=e.className,u=c.cx,f=c.cy,p=c.innerRadius,d=c.outerRadius,h=c.startAngle,y=c.endAngle,v=c.clockWise,b=(p+d)/2,g=_(y-h)*Math.min(Math.abs(y-h),360),x=g>=0?1:-1;"insideStart"===i?(n=h+x*l,o=v):"insideEnd"===i?(n=y-x*l,o=!v):"end"===i&&(n=y+x*l,o=v),o=g<=0?o:!o;var w=sU(u,f,b,n),O=sU(u,f,b,n+(o?1:-1)*359),j="M".concat(w.x,",").concat(w.y,"\n    A").concat(b,",").concat(b,",0,1,").concat(o?0:1,",\n    ").concat(O.x,",").concat(O.y),S=M()(e.id)?I("recharts-radial-line-"):e.id;return a().createElement("text",s0({},r,{dominantBaseline:"central",className:(0,m.Z)("recharts-radial-bar-label",s)}),a().createElement("defs",null,a().createElement("path",{id:S,d:j})),a().createElement("textPath",{xlinkHref:"#".concat(S)},t))},s4=function(e){var t=e.viewBox,r=e.offset,n=e.position,o=t.cx,i=t.cy,a=t.innerRadius,c=t.outerRadius,l=(t.startAngle+t.endAngle)/2;if("outside"===n){var s=sU(o,i,c+r,l),u=s.x;return{x:u,y:s.y,textAnchor:u>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=sU(o,i,(a+c)/2,l);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},s5=function(e){var t=e.viewBox,r=e.parentViewBox,n=e.offset,o=e.position,i=t.x,a=t.y,c=t.width,l=t.height,s=l>=0?1:-1,u=s*n,f=s>0?"end":"start",p=s>0?"start":"end",d=c>=0?1:-1,h=d*n,y=d>0?"end":"start",v=d>0?"start":"end";if("top"===o)return sQ(sQ({},{x:i+c/2,y:a-s*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:c}:{});if("bottom"===o)return sQ(sQ({},{x:i+c/2,y:a+l+u,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(a+l),0),width:c}:{});if("left"===o){var m={x:i-h,y:a+l/2,textAnchor:y,verticalAnchor:"middle"};return sQ(sQ({},m),r?{width:Math.max(m.x-r.x,0),height:l}:{})}if("right"===o){var b={x:i+c+h,y:a+l/2,textAnchor:v,verticalAnchor:"middle"};return sQ(sQ({},b),r?{width:Math.max(r.x+r.width-b.x,0),height:l}:{})}var g=r?{width:c,height:l}:{};return"insideLeft"===o?sQ({x:i+h,y:a+l/2,textAnchor:v,verticalAnchor:"middle"},g):"insideRight"===o?sQ({x:i+c-h,y:a+l/2,textAnchor:y,verticalAnchor:"middle"},g):"insideTop"===o?sQ({x:i+c/2,y:a+u,textAnchor:"middle",verticalAnchor:p},g):"insideBottom"===o?sQ({x:i+c/2,y:a+l-u,textAnchor:"middle",verticalAnchor:f},g):"insideTopLeft"===o?sQ({x:i+h,y:a+u,textAnchor:v,verticalAnchor:p},g):"insideTopRight"===o?sQ({x:i+c-h,y:a+u,textAnchor:y,verticalAnchor:p},g):"insideBottomLeft"===o?sQ({x:i+h,y:a+l-u,textAnchor:v,verticalAnchor:f},g):"insideBottomRight"===o?sQ({x:i+c-h,y:a+l-u,textAnchor:y,verticalAnchor:f},g):W()(o)&&(N(o.x)||T(o.x))&&(N(o.y)||T(o.y))?sQ({x:i+B(o.x,c),y:a+B(o.y,l),textAnchor:"end",verticalAnchor:"end"},g):sQ({x:i+c/2,y:a+l/2,textAnchor:"middle",verticalAnchor:"middle"},g)};function s3(e){var t,r=e.offset,n=sQ({offset:void 0===r?5:r},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,sG)),o=n.viewBox,c=n.position,l=n.value,s=n.children,u=n.content,f=n.className,p=n.textBreakAll;if(!o||M()(l)&&M()(s)&&!(0,i.isValidElement)(u)&&!q()(u))return null;if((0,i.isValidElement)(u))return(0,i.cloneElement)(u,n);if(q()(u)){if(t=(0,i.createElement)(u,n),(0,i.isValidElement)(t))return t}else t=s1(n);var d="cx"in o&&N(o.cx),h=eh(n,!0);if(d&&("insideStart"===c||"insideEnd"===c||"end"===c))return s2(n,t,h);var y=d?s4(n):s5(n);return a().createElement(n3,s0({className:(0,m.Z)("recharts-label",void 0===f?"":f)},h,y,{breakAll:p}),t)}s3.displayName="Label";var s6=function(e){var t=e.cx,r=e.cy,n=e.angle,o=e.startAngle,i=e.endAngle,a=e.r,c=e.radius,l=e.innerRadius,s=e.outerRadius,u=e.x,f=e.y,p=e.top,d=e.left,h=e.width,y=e.height,v=e.clockWise,m=e.labelViewBox;if(m)return m;if(N(h)&&N(y)){if(N(u)&&N(f))return{x:u,y:f,width:h,height:y};if(N(p)&&N(d))return{x:p,y:d,width:h,height:y}}return N(u)&&N(f)?{x:u,y:f,width:0,height:0}:N(t)&&N(r)?{cx:t,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:l||0,outerRadius:s||c||a||0,clockWise:v}:e.viewBox?e.viewBox:{}};s3.parseViewBox=s6,s3.renderCallByParent=function(e,t){var r,n,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&o&&!e.label)return null;var c=e.children,l=s6(e),s=es(c,s3).map(function(e,r){return(0,i.cloneElement)(e,{viewBox:t||l,key:"label-".concat(r)})});return o?[(r=e.label,n=t||l,r?!0===r?a().createElement(s3,{key:"label-implicit",viewBox:n}):C(r)?a().createElement(s3,{key:"label-implicit",viewBox:n,value:r}):(0,i.isValidElement)(r)?r.type===s3?(0,i.cloneElement)(r,{key:"label-implicit",viewBox:n}):a().createElement(s3,{key:"label-implicit",content:r,viewBox:n}):q()(r)?a().createElement(s3,{key:"label-implicit",content:r,viewBox:n}):W()(r)?a().createElement(s3,s0({viewBox:n},r,{key:"label-implicit"})):null:null)].concat(function(e){if(Array.isArray(e))return sK(e)}(s)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(s)||function(e,t){if(e){if("string"==typeof e)return sK(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sK(e,void 0)}}(s)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):s};var s7=function(e,t){var r=e.alwaysShow,n=e.ifOverflow;return r&&(n="extendDomain"),n===t},s8=r(91663),s9=r.n(s8),ue=r(81881),ut=r.n(ue);function ur(e){return(ur="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function un(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,uc(n.key),n)}}function uo(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ui(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?uo(Object(r),!0).forEach(function(t){ua(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):uo(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ua(e,t,r){return(t=uc(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function uc(e){var t=function(e,t){if("object"!=ur(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ur(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ur(t)?t:t+""}var ul=function(e,t){var r=e.x,n=e.y,o=t.x,i=t.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},us=function(){var e,t;function r(e){(function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")})(this,r),this.scale=e}return e=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.bandAware,n=t.position;if(void 0!==e){if(n)switch(n){case"start":default:return this.scale(e);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}},{key:"isInRange",value:function(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}}],t=[{key:"create",value:function(e){return new r(e)}}],e&&un(r.prototype,e),t&&un(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}();ua(us,"EPS",1e-4);var uu=function(e){var t=Object.keys(e).reduce(function(t,r){return ui(ui({},t),{},ua({},r,us.create(e[r])))},{});return ui(ui({},t),{},{apply:function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return s9()(e,function(e,r){return t[r].apply(e,{bandAware:n,position:o})})},isInRange:function(e){return ut()(e,function(e,r){return t[r].isInRange(e)})}})};function uf(){return(uf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function up(e){return(up="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ud(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function uh(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ud(Object(r),!0).forEach(function(t){ub(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ud(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function uy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(uy=function(){return!!e})()}function uv(e){return(uv=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function um(e,t){return(um=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function ub(e,t,r){return(t=ug(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ug(e){var t=function(e,t){if("object"!=up(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=up(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==up(t)?t:t+""}var ux=function(e){var t=e.x,r=e.y,n=e.xAxis,o=e.yAxis,i=uu({x:n.scale,y:o.scale}),a=i.apply({x:t,y:r},{bandAware:!0});return s7(e,"discard")&&!i.isInRange(a)?null:a},uw=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=uv(e),function(e,t){if(t&&("object"===up(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,uy()?Reflect.construct(e,t||[],uv(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&um(e,t)}(r,e),t=[{key:"render",value:function(){var e=this.props,t=e.x,n=e.y,o=e.r,i=e.alwaysShow,c=e.clipPathId,l=C(t),s=C(n);if($(void 0===i,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!l||!s)return null;var u=ux(this.props);if(!u)return null;var f=u.x,p=u.y,d=this.props,h=d.shape,y=d.className,v=uh(uh({clipPath:s7(this.props,"hidden")?"url(#".concat(c,")"):void 0},eh(this.props,!0)),{},{cx:f,cy:p});return a().createElement(eD,{className:(0,m.Z)("recharts-reference-dot",y)},r.renderDot(h,v),s3.renderCallByParent(this.props,{x:f-o,y:p-o,width:2*o,height:2*o}))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ug(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);ub(uw,"displayName","ReferenceDot"),ub(uw,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),ub(uw,"renderDot",function(e,t){return a().isValidElement(e)?a().cloneElement(e,t):q()(e)?e(t):a().createElement(t7,uf({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"}))});var uO=r(23420),uj=r.n(uO);r(56092);var uS=r(49356),uA=r.n(uS)()(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),uE=(0,i.createContext)(void 0),uk=(0,i.createContext)(void 0),uP=(0,i.createContext)(void 0),uM=(0,i.createContext)({}),u_=(0,i.createContext)(void 0),uT=(0,i.createContext)(0),uN=(0,i.createContext)(0),uC=function(e){var t=e.state,r=t.xAxisMap,n=t.yAxisMap,o=t.offset,i=e.clipPathId,c=e.children,l=e.width,s=e.height,u=uA(o);return a().createElement(uE.Provider,{value:r},a().createElement(uk.Provider,{value:n},a().createElement(uM.Provider,{value:o},a().createElement(uP.Provider,{value:u},a().createElement(u_.Provider,{value:i},a().createElement(uT.Provider,{value:s},a().createElement(uN.Provider,{value:l},c)))))))},uD=function(e){var t=(0,i.useContext)(uE);null!=t||eP(!1);var r=t[e];return null!=r||eP(!1),r},uI=function(e){var t=(0,i.useContext)(uk);null!=t||eP(!1);var r=t[e];return null!=r||eP(!1),r};function uB(e){return(uB="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function uR(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(uR=function(){return!!e})()}function uL(e){return(uL=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function uF(e,t){return(uF=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function uz(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function u$(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?uz(Object(r),!0).forEach(function(t){uU(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):uz(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function uU(e,t,r){return(t=uq(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function uq(e){var t=function(e,t){if("object"!=uB(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=uB(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==uB(t)?t:t+""}function uZ(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function uW(){return(uW=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var uH=function(e,t,r,n,o,i,a,c,l){var s=o.x,u=o.y,f=o.width,p=o.height;if(r){var d=l.y,h=e.y.apply(d,{position:i});if(s7(l,"discard")&&!e.y.isInRange(h))return null;var y=[{x:s+f,y:h},{x:s,y:h}];return"left"===c?y.reverse():y}if(t){var v=l.x,m=e.x.apply(v,{position:i});if(s7(l,"discard")&&!e.x.isInRange(m))return null;var b=[{x:m,y:u+p},{x:m,y:u}];return"top"===a?b.reverse():b}if(n){var g=l.segment.map(function(t){return e.apply(t,{position:i})});return s7(l,"discard")&&uj()(g,function(t){return!e.isInRange(t)})?null:g}return null};function uX(e){var t,r,n,o=e.x,c=e.y,l=e.segment,s=e.xAxisId,u=e.yAxisId,f=e.shape,p=e.className,d=e.alwaysShow,h=(0,i.useContext)(u_),y=uD(s),v=uI(u),b=(0,i.useContext)(uP);if(!h||!b)return null;$(void 0===d,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var g=uH(uu({x:y.scale,y:v.scale}),C(o),C(c),l&&2===l.length,b,e.position,y.orientation,v.orientation,e);if(!g)return null;var x=function(e){if(Array.isArray(e))return e}(g)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{for(i=(r=r.call(e)).next;!(l=(n=i.call(r)).done)&&(c.push(n.value),2!==c.length);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(g,2)||function(e,t){if(e){if("string"==typeof e)return uZ(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uZ(e,2)}}(g,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),w=x[0],O=w.x,j=w.y,S=x[1],A=S.x,E=S.y,k=u$(u$({clipPath:s7(e,"hidden")?"url(#".concat(h,")"):void 0},eh(e,!0)),{},{x1:O,y1:j,x2:A,y2:E});return a().createElement(eD,{className:(0,m.Z)("recharts-reference-line",p)},(t=f,r=k,a().isValidElement(t)?a().cloneElement(t,r):q()(t)?t(r):a().createElement("line",uW({},r,{className:"recharts-reference-line-line"}))),s3.renderCallByParent(e,ul({x:(n={x1:O,y1:j,x2:A,y2:E}).x1,y:n.y1},{x:n.x2,y:n.y2})))}var uV=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=uL(e),function(e,t){if(t&&("object"===uB(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,uR()?Reflect.construct(e,t||[],uL(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&uF(e,t)}(r,e),t=[{key:"render",value:function(){return a().createElement(uX,this.props)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,uq(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function uY(){return(uY=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function uG(e){return(uG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function uK(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function uJ(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?uK(Object(r),!0).forEach(function(t){u2(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):uK(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function uQ(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(uQ=function(){return!!e})()}function u0(e){return(u0=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function u1(e,t){return(u1=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function u2(e,t,r){return(t=u4(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function u4(e){var t=function(e,t){if("object"!=uG(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=uG(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==uG(t)?t:t+""}uU(uV,"displayName","ReferenceLine"),uU(uV,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});var u5=function(e,t,r,n,o){var i=o.x1,a=o.x2,c=o.y1,l=o.y2,s=o.xAxis,u=o.yAxis;if(!s||!u)return null;var f=uu({x:s.scale,y:u.scale}),p={x:e?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},d={x:t?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(l,{position:"end"}):f.y.rangeMax};return!s7(o,"discard")||f.isInRange(p)&&f.isInRange(d)?ul(p,d):null},u3=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=u0(e),function(e,t){if(t&&("object"===uG(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,uQ()?Reflect.construct(e,t||[],u0(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&u1(e,t)}(r,e),t=[{key:"render",value:function(){var e=this.props,t=e.x1,n=e.x2,o=e.y1,i=e.y2,c=e.className,l=e.alwaysShow,s=e.clipPathId;$(void 0===l,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var u=C(t),f=C(n),p=C(o),d=C(i),h=this.props.shape;if(!u&&!f&&!p&&!d&&!h)return null;var y=u5(u,f,p,d,this.props);if(!y&&!h)return null;var v=s7(this.props,"hidden")?"url(#".concat(s,")"):void 0;return a().createElement(eD,{className:(0,m.Z)("recharts-reference-area",c)},r.renderRect(h,uJ(uJ({clipPath:v},eh(this.props,!0)),y)),s3.renderCallByParent(this.props,y))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,u4(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function u6(e){return function(e){if(Array.isArray(e))return u7(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return u7(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return u7(e,void 0)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u7(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}u2(u3,"displayName","ReferenceArea"),u2(u3,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),u2(u3,"renderRect",function(e,t){return a().isValidElement(e)?a().cloneElement(e,t):q()(e)?e(t):a().createElement(nv,uY({},t,{className:"recharts-reference-area-rect"}))});var u8=function(e,t,r,n,o){var i=es(e,uV),a=es(e,uw),c=[].concat(u6(i),u6(a)),l=es(e,u3),s="".concat(n,"Id"),u=n[0],f=t;if(c.length&&(f=c.reduce(function(e,t){if(t.props[s]===r&&s7(t.props,"extendDomain")&&N(t.props[u])){var n=t.props[u];return[Math.min(e[0],n),Math.max(e[1],n)]}return e},f)),l.length){var p="".concat(u,"1"),d="".concat(u,"2");f=l.reduce(function(e,t){if(t.props[s]===r&&s7(t.props,"extendDomain")&&N(t.props[p])&&N(t.props[d])){var n=t.props[p],o=t.props[d];return[Math.min(e[0],n,o),Math.max(e[1],n,o)]}return e},f)}return o&&o.length&&(f=o.reduce(function(e,t){return N(t)?[Math.min(e[0],t),Math.max(e[1],t)]:e},f)),f},u9=r(14134),fe=new(r.n(u9)()),ft="recharts.syncMouseEvents";function fr(e){return(fr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fn(e,t,r){return(t=fo(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fo(e){var t=function(e,t){if("object"!=fr(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=fr(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==fr(t)?t:t+""}var fi=function(){var e,t;return e=function e(){(function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")})(this,e),fn(this,"activeIndex",0),fn(this,"coordinateList",[]),fn(this,"layout","horizontal")},t=[{key:"setDetails",value:function(e){var t,r=e.coordinateList,n=void 0===r?null:r,o=e.container,i=void 0===o?null:o,a=e.layout,c=void 0===a?null:a,l=e.offset,s=void 0===l?null:l,u=e.mouseHandlerCallback,f=void 0===u?null:u;this.coordinateList=null!==(t=null!=n?n:this.coordinateList)&&void 0!==t?t:[],this.container=null!=i?i:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=s?s:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(e){if(0!==this.coordinateList.length)switch(e.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(e){this.activeIndex=e}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var e,t,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null===(e=window)||void 0===e?void 0:e.scrollX)||0,l=(null===(t=window)||void 0===t?void 0:t.scrollY)||0,s=o+this.offset.top+i/2+l;this.mouseHandlerCallback({pageX:n+a+c,pageY:s})}}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,fo(n.key),n)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}(),fa=r(56054),fc=r.n(fa),fl=r(98254),fs=r.n(fl);function fu(e){return(fu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ff(){return(ff=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function fp(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function fd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fh(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fd(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=fu(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=fu(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==fu(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fd(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var fy=function(e,t,r,n,o){var i=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-i/2,",").concat(t+o)+"L ".concat(e+r-i/2-n,",").concat(t+o)+"L ".concat(e,",").concat(t," Z")},fv={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},fm=function(e){var t,r=fh(fh({},fv),e),n=(0,i.useRef)(),o=function(e){if(Array.isArray(e))return e}(t=(0,i.useState)(-1))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{for(i=(r=r.call(e)).next;!(l=(n=i.call(r)).done)&&(c.push(n.value),2!==c.length);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(t,2)||function(e,t){if(e){if("string"==typeof e)return fp(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fp(e,2)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=o[0],l=o[1];(0,i.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var e=n.current.getTotalLength();e&&l(e)}catch(e){}},[]);var s=r.x,u=r.y,f=r.upperWidth,p=r.lowerWidth,d=r.height,h=r.className,y=r.animationEasing,v=r.animationDuration,b=r.animationBegin,g=r.isUpdateAnimationActive;if(s!==+s||u!==+u||f!==+f||p!==+p||d!==+d||0===f&&0===p||0===d)return null;var x=(0,m.Z)("recharts-trapezoid",h);return g?a().createElement(nc,{canBegin:c>0,from:{upperWidth:0,lowerWidth:0,height:d,x:s,y:u},to:{upperWidth:f,lowerWidth:p,height:d,x:s,y:u},duration:v,animationEasing:y,isActive:g},function(e){var t=e.upperWidth,o=e.lowerWidth,i=e.height,l=e.x,s=e.y;return a().createElement(nc,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:b,duration:v,easing:y},a().createElement("path",ff({},eh(r,!0),{className:x,d:fy(l,s,t,o,i),ref:n})))}):a().createElement("g",null,a().createElement("path",ff({},eh(r,!0),{className:x,d:fy(s,u,f,p,d)})))};function fb(e){return(fb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fg(){return(fg=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function fx(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fw(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fx(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=fb(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=fb(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==fb(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fx(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var fO=function(e){var t=e.cx,r=e.cy,n=e.radius,o=e.angle,i=e.sign,a=e.isExternal,c=e.cornerRadius,l=e.cornerIsExternal,s=c*(a?1:-1)+n,u=Math.asin(c/s)/s$,f=l?o:o+i*u;return{center:sU(t,r,s,f),circleTangency:sU(t,r,n,f),lineTangency:sU(t,r,s*Math.cos(u*s$),l?o-i*u:o),theta:u}},fj=function(e){var t,r=e.cx,n=e.cy,o=e.innerRadius,i=e.outerRadius,a=e.startAngle,c=_((t=e.endAngle)-a)*Math.min(Math.abs(t-a),359.999),l=a+c,s=sU(r,n,i,a),u=sU(r,n,i,l),f="M ".concat(s.x,",").concat(s.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(a>l),",\n    ").concat(u.x,",").concat(u.y,"\n  ");if(o>0){var p=sU(r,n,o,a),d=sU(r,n,o,l);f+="L ".concat(d.x,",").concat(d.y,"\n            A ").concat(o,",").concat(o,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(a<=l),",\n            ").concat(p.x,",").concat(p.y," Z")}else f+="L ".concat(r,",").concat(n," Z");return f},fS=function(e){var t=e.cx,r=e.cy,n=e.innerRadius,o=e.outerRadius,i=e.cornerRadius,a=e.forceCornerRadius,c=e.cornerIsExternal,l=e.startAngle,s=e.endAngle,u=_(s-l),f=fO({cx:t,cy:r,radius:o,angle:l,sign:u,cornerRadius:i,cornerIsExternal:c}),p=f.circleTangency,d=f.lineTangency,h=f.theta,y=fO({cx:t,cy:r,radius:o,angle:s,sign:-u,cornerRadius:i,cornerIsExternal:c}),v=y.circleTangency,m=y.lineTangency,b=y.theta,g=c?Math.abs(l-s):Math.abs(l-s)-h-b;if(g<0)return a?"M ".concat(d.x,",").concat(d.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):fj({cx:t,cy:r,innerRadius:n,outerRadius:o,startAngle:l,endAngle:s});var x="M ".concat(d.x,",").concat(d.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(g>180),",").concat(+(u<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var w=fO({cx:t,cy:r,radius:n,angle:l,sign:u,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),O=w.circleTangency,j=w.lineTangency,S=w.theta,A=fO({cx:t,cy:r,radius:n,angle:s,sign:-u,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),E=A.circleTangency,k=A.lineTangency,P=A.theta,M=c?Math.abs(l-s):Math.abs(l-s)-S-P;if(M<0&&0===i)return"".concat(x,"L").concat(t,",").concat(r,"Z");x+="L".concat(k.x,",").concat(k.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(E.x,",").concat(E.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(M>180),",").concat(+(u>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(j.x,",").concat(j.y,"Z")}else x+="L".concat(t,",").concat(r,"Z");return x},fA={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},fE=function(e){var t,r=fw(fw({},fA),e),n=r.cx,o=r.cy,i=r.innerRadius,c=r.outerRadius,l=r.cornerRadius,s=r.forceCornerRadius,u=r.cornerIsExternal,f=r.startAngle,p=r.endAngle,d=r.className;if(c<i||f===p)return null;var h=(0,m.Z)("recharts-sector",d),y=c-i,v=B(l,y,0,!0);return t=v>0&&360>Math.abs(f-p)?fS({cx:n,cy:o,innerRadius:i,outerRadius:c,cornerRadius:Math.min(v,y/2),forceCornerRadius:s,cornerIsExternal:u,startAngle:f,endAngle:p}):fj({cx:n,cy:o,innerRadius:i,outerRadius:c,startAngle:f,endAngle:p}),a().createElement("path",fg({},eh(r,!0),{className:h,d:t,role:"img"}))},fk=["option","shapeType","propTransformer","activeClassName","isActive"];function fP(e){return(fP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function fM(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f_(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fM(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=fP(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=fP(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==fP(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fM(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function fT(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return a().createElement(nv,r);case"trapezoid":return a().createElement(fm,r);case"sector":return a().createElement(fE,r);case"symbols":if("symbols"===t)return a().createElement(tL,r);break;default:return null}}function fN(e){var t,r=e.option,n=e.shapeType,o=e.propTransformer,c=e.activeClassName,l=e.isActive,s=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,fk);if((0,i.isValidElement)(r))t=(0,i.cloneElement)(r,f_(f_({},s),(0,i.isValidElement)(r)?r.props:r));else if(q()(r))t=r(s);else if(fc()(r)&&!fs()(r)){var u=(void 0===o?function(e,t){return f_(f_({},t),e)}:o)(r,s);t=a().createElement(fT,{shapeType:n,elementProps:u})}else t=a().createElement(fT,{shapeType:n,elementProps:s});return l?a().createElement(eD,{className:void 0===c?"recharts-active-shape":c},t):t}function fC(e,t){return null!=t&&"trapezoids"in e.props}function fD(e,t){return null!=t&&"sectors"in e.props}function fI(e,t){return null!=t&&"points"in e.props}function fB(e,t){var r,n,o=e.x===(null==t||null===(r=t.labelViewBox)||void 0===r?void 0:r.x)||e.x===t.x,i=e.y===(null==t||null===(n=t.labelViewBox)||void 0===n?void 0:n.y)||e.y===t.y;return o&&i}function fR(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function fL(e,t){var r=e.x===t.x,n=e.y===t.y,o=e.z===t.z;return r&&n&&o}function fF(){}function fz(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function f$(e){this._context=e}function fU(e){this._context=e}function fq(e){this._context=e}f$.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:fz(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:fz(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},fU.prototype={areaStart:fF,areaEnd:fF,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:fz(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},fq.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:fz(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class fZ{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function fW(e){this._context=e}function fH(e){this._context=e}function fX(e){return new fH(e)}function fV(e,t,r){var n=e._x1-e._x0,o=t-e._x1,i=(e._y1-e._y0)/(n||o<0&&-0),a=(r-e._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function fY(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function fG(e,t,r){var n=e._x0,o=e._y0,i=e._x1,a=e._y1,c=(i-n)/3;e._context.bezierCurveTo(n+c,o+c*t,i-c,a-c*r,i,a)}function fK(e){this._context=e}function fJ(e){this._context=new fQ(e)}function fQ(e){this._context=e}function f0(e){this._context=e}function f1(e){var t,r,n=e.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=e[0]+2*e[1],t=1;t<n-1;++t)o[t]=1,i[t]=4,a[t]=4*e[t]+2*e[t+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=o[t]/i[t-1],i[t]-=r,a[t]-=r*a[t-1];for(o[n-1]=a[n-1]/i[n-1],t=n-2;t>=0;--t)o[t]=(a[t]-o[t+1])/i[t];for(t=0,i[n-1]=(e[n]+o[n-1])/2;t<n-1;++t)i[t]=2*e[t+1]-o[t+1];return[o,i]}function f2(e,t){this._context=e,this._t=t}function f4(e){return e[0]}function f5(e){return e[1]}function f3(e,t){var r=tj(!0),n=null,o=fX,i=null,a=tM(c);function c(c){var l,s,u,f=(c=c_(c)).length,p=!1;for(null==n&&(i=o(u=a())),l=0;l<=f;++l)!(l<f&&r(s=c[l],l,c))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+e(s,l,c),+t(s,l,c));if(u)return i=null,u+""||null}return e="function"==typeof e?e:void 0===e?f4:tj(e),t="function"==typeof t?t:void 0===t?f5:tj(t),c.x=function(t){return arguments.length?(e="function"==typeof t?t:tj(+t),c):e},c.y=function(e){return arguments.length?(t="function"==typeof e?e:tj(+e),c):t},c.defined=function(e){return arguments.length?(r="function"==typeof e?e:tj(!!e),c):r},c.curve=function(e){return arguments.length?(o=e,null!=n&&(i=o(n)),c):o},c.context=function(e){return arguments.length?(null==e?n=i=null:i=o(n=e),c):n},c}function f6(e,t,r){var n=null,o=tj(!0),i=null,a=fX,c=null,l=tM(s);function s(s){var u,f,p,d,h,y=(s=c_(s)).length,v=!1,m=Array(y),b=Array(y);for(null==i&&(c=a(h=l())),u=0;u<=y;++u){if(!(u<y&&o(d=s[u],u,s))===v){if(v=!v)f=u,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=u-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}}v&&(m[u]=+e(d,u,s),b[u]=+t(d,u,s),c.point(n?+n(d,u,s):m[u],r?+r(d,u,s):b[u]))}if(h)return c=null,h+""||null}function u(){return f3().defined(o).curve(a).context(i)}return e="function"==typeof e?e:void 0===e?f4:tj(+e),t="function"==typeof t?t:void 0===t?tj(0):tj(+t),r="function"==typeof r?r:void 0===r?f5:tj(+r),s.x=function(t){return arguments.length?(e="function"==typeof t?t:tj(+t),n=null,s):e},s.x0=function(t){return arguments.length?(e="function"==typeof t?t:tj(+t),s):e},s.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:tj(+e),s):n},s.y=function(e){return arguments.length?(t="function"==typeof e?e:tj(+e),r=null,s):t},s.y0=function(e){return arguments.length?(t="function"==typeof e?e:tj(+e),s):t},s.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:tj(+e),s):r},s.lineX0=s.lineY0=function(){return u().x(e).y(t)},s.lineY1=function(){return u().x(e).y(r)},s.lineX1=function(){return u().x(n).y(t)},s.defined=function(e){return arguments.length?(o="function"==typeof e?e:tj(!!e),s):o},s.curve=function(e){return arguments.length?(a=e,null!=i&&(c=a(i)),s):a},s.context=function(e){return arguments.length?(null==e?i=c=null:c=a(i=e),s):i},s}function f7(e){return(f7="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function f8(){return(f8=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function f9(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pe(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f9(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=f7(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=f7(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==f7(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f9(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}fW.prototype={areaStart:fF,areaEnd:fF,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}},fH.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},fK.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:fG(this,this._t0,fY(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t=+t,(e=+e)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,fG(this,fY(this,r=fV(this,e,t)),r);break;default:fG(this,this._t0,r=fV(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(fJ.prototype=Object.create(fK.prototype)).point=function(e,t){fK.prototype.point.call(this,t,e)},fQ.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,o,i){this._context.bezierCurveTo(t,e,n,r,i,o)}},f0.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r){if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=f1(e),o=f1(t),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],e[a],t[a])}(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},f2.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var pt={curveBasisClosed:function(e){return new fU(e)},curveBasisOpen:function(e){return new fq(e)},curveBasis:function(e){return new f$(e)},curveBumpX:function(e){return new fZ(e,!0)},curveBumpY:function(e){return new fZ(e,!1)},curveLinearClosed:function(e){return new fW(e)},curveLinear:fX,curveMonotoneX:function(e){return new fK(e)},curveMonotoneY:function(e){return new fJ(e)},curveNatural:function(e){return new f0(e)},curveStep:function(e){return new f2(e,.5)},curveStepAfter:function(e){return new f2(e,1)},curveStepBefore:function(e){return new f2(e,0)}},pr=function(e){return e.x===+e.x&&e.y===+e.y},pn=function(e){return e.x},po=function(e){return e.y},pi=function(e,t){if(q()(e))return e;var r="curve".concat(tc()(e));return("curveMonotone"===r||"curveBump"===r)&&t?pt["".concat(r).concat("vertical"===t?"Y":"X")]:pt[r]||fX},pa=function(e){var t,r=e.type,n=e.points,o=void 0===n?[]:n,i=e.baseLine,a=e.layout,c=e.connectNulls,l=void 0!==c&&c,s=pi(void 0===r?"linear":r,a),u=l?o.filter(function(e){return pr(e)}):o;if(Array.isArray(i)){var f=l?i.filter(function(e){return pr(e)}):i,p=u.map(function(e,t){return pe(pe({},e),{},{base:f[t]})});return(t="vertical"===a?f6().y(po).x1(pn).x0(function(e){return e.base.x}):f6().x(pn).y1(po).y0(function(e){return e.base.y})).defined(pr).curve(s),t(p)}return(t="vertical"===a&&N(i)?f6().y(po).x1(pn).x0(i):N(i)?f6().x(pn).y1(po).y0(i):f3().x(pn).y(po)).defined(pr).curve(s),t(u)},pc=function(e){var t=e.className,r=e.points,n=e.path,o=e.pathRef;if((!r||!r.length)&&!n)return null;var a=r&&r.length?pa(e):n;return i.createElement("path",f8({},eh(e,!1),Q(e),{className:(0,m.Z)("recharts-curve",t),d:a,ref:o}))};function pl(e){return(pl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var ps=["x","y","top","left","width","height","className"];function pu(){return(pu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function pf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var pp=function(e){var t=e.x,r=void 0===t?0:t,n=e.y,o=void 0===n?0:n,i=e.top,c=void 0===i?0:i,l=e.left,s=void 0===l?0:l,u=e.width,f=void 0===u?0:u,p=e.height,d=void 0===p?0:p,h=e.className,y=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pf(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=pl(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=pl(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==pl(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pf(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:r,y:o,top:c,left:s,width:f,height:d},function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,ps));return N(r)&&N(o)&&N(f)&&N(d)&&N(c)&&N(s)?a().createElement("path",pu({},eh(y,!0),{className:(0,m.Z)("recharts-cross",h),d:"M".concat(r,",").concat(c,"v").concat(d,"M").concat(s,",").concat(o,"h").concat(f)})):null};function pd(e){var t=e.cx,r=e.cy,n=e.radius,o=e.startAngle,i=e.endAngle;return{points:[sU(t,r,n,o),sU(t,r,n,i)],cx:t,cy:r,radius:n,startAngle:o,endAngle:i}}function ph(e){return(ph="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function py(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pv(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?py(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=ph(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ph(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ph(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):py(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pm(e){var t,r,n,o,a=e.element,c=e.tooltipEventType,l=e.isActive,s=e.activeCoordinate,u=e.activePayload,f=e.offset,p=e.activeTooltipIndex,d=e.tooltipAxisBandSize,h=e.layout,y=e.chartName,v=null!==(r=a.props.cursor)&&void 0!==r?r:null===(n=a.type.defaultProps)||void 0===n?void 0:n.cursor;if(!a||!v||!l||!s||"ScatterChart"!==y&&"axis"!==c)return null;var b=pc;if("ScatterChart"===y)o=s,b=pp;else if("BarChart"===y)t=d/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===h?s.x-t:f.left+.5,y:"horizontal"===h?f.top+.5:s.y-t,width:"horizontal"===h?d:f.width-1,height:"horizontal"===h?f.height-1:d},b=nv;else if("radial"===h){var g=pd(s),x=g.cx,w=g.cy,O=g.radius;o={cx:x,cy:w,startAngle:g.startAngle,endAngle:g.endAngle,innerRadius:O,outerRadius:O},b=fE}else o={points:function(e,t,r){var n,o,i,a;if("horizontal"===e)i=n=t.x,o=r.top,a=r.top+r.height;else if("vertical"===e)a=o=t.y,n=r.left,i=r.left+r.width;else if(null!=t.cx&&null!=t.cy){if("centric"!==e)return pd(t);var c=t.cx,l=t.cy,s=t.innerRadius,u=t.outerRadius,f=t.angle,p=sU(c,l,s,f),d=sU(c,l,u,f);n=p.x,o=p.y,i=d.x,a=d.y}return[{x:n,y:o},{x:i,y:a}]}(h,s,f)},b=pc;var j=pv(pv(pv(pv({stroke:"#ccc",pointerEvents:"none"},f),o),eh(v,!1)),{},{payload:u,payloadIndex:p,className:(0,m.Z)("recharts-tooltip-cursor",v.className)});return(0,i.isValidElement)(v)?(0,i.cloneElement)(v,j):(0,i.createElement)(b,j)}var pb=["item"],pg=["children","className","width","height","style","compact","title","desc"];function px(e){return(px="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pw(){return(pw=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function pO(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(c.push(n.value),c.length!==t);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(e,t)||pP(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pj(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function pS(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(pS=function(){return!!e})()}function pA(e){return(pA=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function pE(e,t){return(pE=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function pk(e){return function(e){if(Array.isArray(e))return pM(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||pP(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pP(e,t){if(e){if("string"==typeof e)return pM(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pM(e,t)}}function pM(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function p_(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pT(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p_(Object(r),!0).forEach(function(t){pN(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p_(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pN(e,t,r){return(t=pC(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pC(e){var t=function(e,t){if("object"!=px(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=px(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==px(t)?t:t+""}var pD={xAxis:["bottom","top"],yAxis:["left","right"]},pI={width:"100%",height:"100%"},pB={x:0,y:0};function pR(e){return e}var pL=function(e,t,r,n){var o=t.find(function(e){return e&&e.index===r});if(o){if("horizontal"===e)return{x:o.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:o.coordinate};if("centric"===e){var i=o.coordinate,a=n.radius;return pT(pT(pT({},n),sU(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var c=o.coordinate,l=n.angle;return pT(pT(pT({},n),sU(n.cx,n.cy,c,l)),{},{angle:l,radius:c})}return pB},pF=function(e,t){var r=t.graphicalItems,n=t.dataStartIndex,o=t.dataEndIndex,i=(null!=r?r:[]).reduce(function(e,t){var r=t.props.data;return r&&r.length?[].concat(pk(e),pk(r)):e},[]);return i.length>0?i:e&&e.length&&N(n)&&N(o)?e.slice(n,o+1):[]};function pz(e){return"number"===e?[0,"auto"]:void 0}var p$=function(e,t,r,n){var o=e.graphicalItems,i=e.tooltipAxis,a=pF(t,e);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,c){var l,s,u=null!==(l=c.props.data)&&void 0!==l?l:t;return(u&&e.dataStartIndex+e.dataEndIndex!==0&&e.dataEndIndex-e.dataStartIndex>=r&&(u=u.slice(e.dataStartIndex,e.dataEndIndex+1)),s=i.dataKey&&!i.allowDuplicatedCategory?z(void 0===u?a:u,i.dataKey,n):u&&u[r]||a[r])?[].concat(pk(o),[sv(c,s)]):o},[])},pU=function(e,t,r,n){var o=n||{x:e.chartX,y:e.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=e.orderedTooltipTicks,c=e.tooltipAxis,l=e.tooltipTicks,s=l1(i,a,l,c);if(s>=0&&l){var u=l[s]&&l[s].value,f=p$(e,t,s,u),p=pL(r,a,s,o);return{activeTooltipIndex:s,activeLabel:u,activePayload:f,activeCoordinate:p}}return null},pq=function(e,t){var r=t.axes,n=t.graphicalItems,o=t.axisType,i=t.axisIdKey,a=t.stackGroups,c=t.dataStartIndex,l=t.dataEndIndex,s=e.layout,u=e.children,f=e.stackOffset,p=l9(s,o);return r.reduce(function(t,r){var d=void 0!==r.type.defaultProps?pT(pT({},r.type.defaultProps),r.props):r.props,h=d.type,y=d.dataKey,v=d.allowDataOverflow,m=d.allowDuplicatedCategory,b=d.scale,g=d.ticks,x=d.includeHidden,w=d[i];if(t[w])return t;var O=pF(e.data,{graphicalItems:n.filter(function(e){var t;return(i in e.props?e.props[i]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[i])===w}),dataStartIndex:c,dataEndIndex:l}),j=O.length;(function(e,t,r){if("number"===r&&!0===t&&Array.isArray(e)){var n=null==e?void 0:e[0],o=null==e?void 0:e[1];if(n&&o&&N(n)&&N(o))return!0}return!1})(d.domain,v,h)&&(E=sd(d.domain,null,v),p&&("number"===h||"auto"!==b)&&(P=l0(O,y,"category")));var S=pz(h);if(!E||0===E.length){var A,E,k,P,_,T=null!==(_=d.domain)&&void 0!==_?_:S;if(y){if(E=l0(O,y,h),"category"===h&&p){var C=L(E);m&&C?(k=E,E=eA()(0,j)):m||(E=sy(T,E,r).reduce(function(e,t){return e.indexOf(t)>=0?e:[].concat(pk(e),[t])},[]))}else if("category"===h)E=m?E.filter(function(e){return""!==e&&!M()(e)}):sy(T,E,r).reduce(function(e,t){return e.indexOf(t)>=0||""===t||M()(t)?e:[].concat(pk(e),[t])},[]);else if("number"===h){var D=l7(O,n.filter(function(e){var t,r,n=i in e.props?e.props[i]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[i],o="hide"in e.props?e.props.hide:null===(r=e.type.defaultProps)||void 0===r?void 0:r.hide;return n===w&&(x||!o)}),y,o,s);D&&(E=D)}p&&("number"===h||"auto"!==b)&&(P=l0(O,y,"category"))}else E=p?eA()(0,j):a&&a[w]&&a[w].hasStack&&"number"===h?"expand"===f?[0,1]:su(a[w].stackGroups,c,l):l8(O,n.filter(function(e){var t=i in e.props?e.props[i]:e.type.defaultProps[i],r="hide"in e.props?e.props.hide:e.type.defaultProps.hide;return t===w&&(x||!r)}),h,s,!0);"number"===h?(E=u8(u,E,w,o,g),T&&(E=sd(T,E,v))):"category"===h&&T&&E.every(function(e){return T.indexOf(e)>=0})&&(E=T)}return pT(pT({},t),{},pN({},w,pT(pT({},d),{},{axisType:o,domain:E,categoricalDomain:P,duplicateDomain:k,originalDomain:null!==(A=d.domain)&&void 0!==A?A:S,isCategorical:p,layout:s})))},{})},pZ=function(e,t){var r=t.graphicalItems,n=t.Axis,o=t.axisType,i=t.axisIdKey,a=t.stackGroups,c=t.dataStartIndex,l=t.dataEndIndex,s=e.layout,u=e.children,f=pF(e.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:l}),p=f.length,d=l9(s,o),h=-1;return r.reduce(function(e,t){var y,v=(void 0!==t.type.defaultProps?pT(pT({},t.type.defaultProps),t.props):t.props)[i],m=pz("number");return e[v]?e:(h++,y=d?eA()(0,p):a&&a[v]&&a[v].hasStack?u8(u,y=su(a[v].stackGroups,c,l),v,o):u8(u,y=sd(m,l8(f,r.filter(function(e){var t,r,n=i in e.props?e.props[i]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[i],o="hide"in e.props?e.props.hide:null===(r=e.type.defaultProps)||void 0===r?void 0:r.hide;return n===v&&!o}),"number",s),n.defaultProps.allowDataOverflow),v,o),pT(pT({},e),{},pN({},v,pT(pT({axisType:o},n.defaultProps),{},{hide:!0,orientation:A()(pD,"".concat(o,".").concat(h%2),null),domain:y,originalDomain:m,isCategorical:d,layout:s}))))},{})},pW=function(e,t){var r=t.axisType,n=void 0===r?"xAxis":r,o=t.AxisComp,i=t.graphicalItems,a=t.stackGroups,c=t.dataStartIndex,l=t.dataEndIndex,s=e.children,u="".concat(n,"Id"),f=es(s,o),p={};return f&&f.length?p=pq(e,{axes:f,graphicalItems:i,axisType:n,axisIdKey:u,stackGroups:a,dataStartIndex:c,dataEndIndex:l}):i&&i.length&&(p=pZ(e,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:u,stackGroups:a,dataStartIndex:c,dataEndIndex:l})),p},pH=function(e){var t=R(e),r=se(t,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:ek()(r,function(e){return e.coordinate}),tooltipAxis:t,tooltipAxisBandSize:sh(t,r)}},pX=function(e){var t=e.children,r=e.defaultShowTooltip,n=eu(t,sI),o=0,i=0;return e.data&&0!==e.data.length&&(i=e.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},pV=function(e){return"horizontal"===e?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===e?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===e?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},pY=function(e,t){var r=e.props,n=e.graphicalItems,o=e.xAxisMap,i=void 0===o?{}:o,a=e.yAxisMap,c=void 0===a?{}:a,l=r.width,s=r.height,u=r.children,f=r.margin||{},p=eu(u,sI),d=eu(u,t3),h=Object.keys(c).reduce(function(e,t){var r=c[t],n=r.orientation;return r.mirror||r.hide?e:pT(pT({},e),{},pN({},n,e[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(i).reduce(function(e,t){var r=i[t],n=r.orientation;return r.mirror||r.hide?e:pT(pT({},e),{},pN({},n,A()(e,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),v=pT(pT({},y),h),m=v.bottom;p&&(v.bottom+=p.props.height||sI.defaultProps.height),d&&t&&(v=l3(v,n,r,t));var b=l-v.left-v.right,g=s-v.top-v.bottom;return pT(pT({brushBottom:m},v),{},{width:Math.max(b,0),height:Math.max(g,0)})},pG=["points","className","baseLinePoints","connectNulls"];function pK(){return(pK=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function pJ(e){return function(e){if(Array.isArray(e))return pQ(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return pQ(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pQ(e,void 0)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pQ(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var p0=function(e){return e&&e.x===+e.x&&e.y===+e.y},p1=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[[]];return e.forEach(function(e){p0(e)?t[t.length-1].push(e):t[t.length-1].length>0&&t.push([])}),p0(e[0])&&t[t.length-1].push(e[0]),t[t.length-1].length<=0&&(t=t.slice(0,-1)),t},p2=function(e,t){var r=p1(e);t&&(r=[r.reduce(function(e,t){return[].concat(pJ(e),pJ(t))},[])]);var n=r.map(function(e){return e.reduce(function(e,t,r){return"".concat(e).concat(0===r?"M":"L").concat(t.x,",").concat(t.y)},"")}).join("");return 1===r.length?"".concat(n,"Z"):n},p4=function(e,t,r){var n=p2(e,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(p2(t.reverse(),r).slice(1))},p5=function(e){var t=e.points,r=e.className,n=e.baseLinePoints,o=e.connectNulls,i=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,pG);if(!t||!t.length)return null;var c=(0,m.Z)("recharts-polygon",r);if(n&&n.length){var l=i.stroke&&"none"!==i.stroke,s=p4(t,n,o);return a().createElement("g",{className:c},a().createElement("path",pK({},eh(i,!0),{fill:"Z"===s.slice(-1)?i.fill:"none",stroke:"none",d:s})),l?a().createElement("path",pK({},eh(i,!0),{fill:"none",d:p2(t,o)})):null,l?a().createElement("path",pK({},eh(i,!0),{fill:"none",d:p2(n,o)})):null)}var u=p2(t,o);return a().createElement("path",pK({},eh(i,!0),{fill:"Z"===u.slice(-1)?i.fill:"none",className:c,d:u}))};function p3(e){return(p3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function p6(){return(p6=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function p7(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p8(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p7(Object(r),!0).forEach(function(t){dn(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p7(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function p9(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,di(n.key),n)}}function de(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(de=function(){return!!e})()}function dt(e){return(dt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function dr(e,t){return(dr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function dn(e,t,r){return(t=di(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function di(e){var t=function(e,t){if("object"!=p3(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=p3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==p3(t)?t:t+""}var da=Math.PI/180,dc=function(e){var t,r;function n(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),e=n,t=arguments,e=dt(e),function(e,t){if(t&&("object"===p3(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,de()?Reflect.construct(e,t||[],dt(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&dr(e,t)}(n,e),t=[{key:"getTickLineCoord",value:function(e){var t=this.props,r=t.cx,n=t.cy,o=t.radius,i=t.orientation,a=t.tickSize,c=sU(r,n,o,e.coordinate),l=sU(r,n,o+("inner"===i?-1:1)*(a||8),e.coordinate);return{x1:c.x,y1:c.y,x2:l.x,y2:l.y}}},{key:"getTickTextAnchor",value:function(e){var t=this.props.orientation,r=Math.cos(-e.coordinate*da);return r>1e-5?"outer"===t?"start":"end":r<-.00001?"outer"===t?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.cx,r=e.cy,n=e.radius,o=e.axisLine,i=e.axisLineType,c=p8(p8({},eh(this.props,!1)),{},{fill:"none"},eh(o,!1));if("circle"===i)return a().createElement(t7,p6({className:"recharts-polar-angle-axis-line"},c,{cx:t,cy:r,r:n}));var l=this.props.ticks.map(function(e){return sU(t,r,n,e.coordinate)});return a().createElement(p5,p6({className:"recharts-polar-angle-axis-line"},c,{points:l}))}},{key:"renderTicks",value:function(){var e=this,t=this.props,r=t.ticks,o=t.tick,i=t.tickLine,c=t.tickFormatter,l=t.stroke,s=eh(this.props,!1),u=eh(o,!1),f=p8(p8({},s),{},{fill:"none"},eh(i,!1)),p=r.map(function(t,r){var p=e.getTickLineCoord(t),d=p8(p8(p8({textAnchor:e.getTickTextAnchor(t)},s),{},{stroke:"none",fill:l},u),{},{index:r,payload:t,x:p.x2,y:p.y2});return a().createElement(eD,p6({className:(0,m.Z)("recharts-polar-angle-axis-tick",sV(o)),key:"tick-".concat(t.coordinate)},ee(e.props,t,r)),i&&a().createElement("line",p6({className:"recharts-polar-angle-axis-tick-line"},f,p)),o&&n.renderTickItem(o,d,c?c(t.value,r):t.value))});return a().createElement(eD,{className:"recharts-polar-angle-axis-ticks"},p)}},{key:"render",value:function(){var e=this.props,t=e.ticks,r=e.radius,n=e.axisLine;return!(r<=0)&&t&&t.length?a().createElement(eD,{className:(0,m.Z)("recharts-polar-angle-axis",this.props.className)},n&&this.renderAxisLine(),this.renderTicks()):null}}],r=[{key:"renderTickItem",value:function(e,t,r){return a().isValidElement(e)?a().cloneElement(e,t):q()(e)?e(t):a().createElement(n3,p6({},t,{className:"recharts-polar-angle-axis-tick-value"}),r)}}],t&&p9(n.prototype,t),r&&p9(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);dn(dc,"displayName","PolarAngleAxis"),dn(dc,"axisType","angleAxis"),dn(dc,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var dl=r(32063),ds=r.n(dl),du=r(32759),df=r.n(du),dp=["cx","cy","angle","ticks","axisLine"],dd=["ticks","tick","angle","tickFormatter","stroke"];function dh(e){return(dh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function dy(){return(dy=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function dv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dm(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dv(Object(r),!0).forEach(function(t){dj(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dv(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function db(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function dg(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dS(n.key),n)}}function dx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(dx=function(){return!!e})()}function dw(e){return(dw=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function dO(e,t){return(dO=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function dj(e,t,r){return(t=dS(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dS(e){var t=function(e,t){if("object"!=dh(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=dh(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==dh(t)?t:t+""}var dA=function(e){var t,r;function n(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),e=n,t=arguments,e=dw(e),function(e,t){if(t&&("object"===dh(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,dx()?Reflect.construct(e,t||[],dw(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&dO(e,t)}(n,e),t=[{key:"getTickValueCoord",value:function(e){var t=e.coordinate,r=this.props,n=r.angle;return sU(r.cx,r.cy,t,n)}},{key:"getTickTextAnchor",value:function(){var e;switch(this.props.orientation){case"left":e="end";break;case"right":e="start";break;default:e="middle"}return e}},{key:"getViewBox",value:function(){var e=this.props,t=e.cx,r=e.cy,n=e.angle,o=e.ticks,i=ds()(o,function(e){return e.coordinate||0});return{cx:t,cy:r,startAngle:n,endAngle:n,innerRadius:df()(o,function(e){return e.coordinate||0}).coordinate||0,outerRadius:i.coordinate||0}}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.cx,r=e.cy,n=e.angle,o=e.ticks,i=e.axisLine,c=db(e,dp),l=o.reduce(function(e,t){return[Math.min(e[0],t.coordinate),Math.max(e[1],t.coordinate)]},[1/0,-1/0]),s=sU(t,r,l[0],n),u=sU(t,r,l[1],n),f=dm(dm(dm({},eh(c,!1)),{},{fill:"none"},eh(i,!1)),{},{x1:s.x,y1:s.y,x2:u.x,y2:u.y});return a().createElement("line",dy({className:"recharts-polar-radius-axis-line"},f))}},{key:"renderTicks",value:function(){var e=this,t=this.props,r=t.ticks,o=t.tick,i=t.angle,c=t.tickFormatter,l=t.stroke,s=db(t,dd),u=this.getTickTextAnchor(),f=eh(s,!1),p=eh(o,!1),d=r.map(function(t,r){var s=e.getTickValueCoord(t),d=dm(dm(dm(dm({textAnchor:u,transform:"rotate(".concat(90-i,", ").concat(s.x,", ").concat(s.y,")")},f),{},{stroke:"none",fill:l},p),{},{index:r},s),{},{payload:t});return a().createElement(eD,dy({className:(0,m.Z)("recharts-polar-radius-axis-tick",sV(o)),key:"tick-".concat(t.coordinate)},ee(e.props,t,r)),n.renderTickItem(o,d,c?c(t.value,r):t.value))});return a().createElement(eD,{className:"recharts-polar-radius-axis-ticks"},d)}},{key:"render",value:function(){var e=this.props,t=e.ticks,r=e.axisLine,n=e.tick;return t&&t.length?a().createElement(eD,{className:(0,m.Z)("recharts-polar-radius-axis",this.props.className)},r&&this.renderAxisLine(),n&&this.renderTicks(),s3.renderCallByParent(this.props,this.getViewBox())):null}}],r=[{key:"renderTickItem",value:function(e,t,r){return a().isValidElement(e)?a().cloneElement(e,t):q()(e)?e(t):a().createElement(n3,dy({},t,{className:"recharts-polar-radius-axis-tick-value"}),r)}}],t&&dg(n.prototype,t),r&&dg(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);dj(dA,"displayName","PolarRadiusAxis"),dj(dA,"axisType","radiusAxis"),dj(dA,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});var dE=r(39989),dk=r.n(dE);function dP(e){return(dP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var dM=["valueAccessor"],d_=["data","dataKey","clockWise","id","textBreakAll"];function dT(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function dN(){return(dN=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function dC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dD(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dC(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=dP(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=dP(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==dP(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dC(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function dI(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}var dB=function(e){return Array.isArray(e.value)?dk()(e.value):e.value};function dR(e){var t=e.valueAccessor,r=void 0===t?dB:t,n=dI(e,dM),o=n.data,i=n.dataKey,c=n.clockWise,l=n.id,s=n.textBreakAll,u=dI(n,d_);return o&&o.length?a().createElement(eD,{className:"recharts-label-list"},o.map(function(e,t){var n=M()(i)?r(e,t):lQ(e&&e.payload,i),o=M()(l)?{}:{id:"".concat(l,"-").concat(t)};return a().createElement(s3,dN({},eh(e,!0),u,o,{parentViewBox:e.parentViewBox,value:n,textBreakAll:s,viewBox:s3.parseViewBox(M()(c)?e:dD(dD({},e),{},{clockWise:c})),key:"label-".concat(t),index:t}))})):null}dR.displayName="LabelList",dR.renderCallByParent=function(e,t){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&n&&!e.label)return null;var o=es(e.children,dR).map(function(e,r){return(0,i.cloneElement)(e,{data:t,key:"labelList-".concat(r)})});return n?[(r=e.label)?!0===r?a().createElement(dR,{key:"labelList-implicit",data:t}):a().isValidElement(r)||q()(r)?a().createElement(dR,{key:"labelList-implicit",data:t,content:r}):W()(r)?a().createElement(dR,dN({data:t},r,{key:"labelList-implicit"})):null:null].concat(function(e){if(Array.isArray(e))return dT(e)}(o)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(o)||function(e,t){if(e){if("string"==typeof e)return dT(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return dT(e,void 0)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):o};var dL=function(e){return null};function dF(e){return(dF="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function dz(){return(dz=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function d$(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function dU(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d$(Object(r),!0).forEach(function(t){dX(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d$(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function dq(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dV(n.key),n)}}function dZ(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(dZ=function(){return!!e})()}function dW(e){return(dW=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function dH(e,t){return(dH=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function dX(e,t,r){return(t=dV(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dV(e){var t=function(e,t){if("object"!=dF(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=dF(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==dF(t)?t:t+""}dL.displayName="Cell";var dY=function(e){var t,r;function n(e){var t,r,o;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[e],r=dW(r),dX(t=function(e,t){if(t&&("object"===dF(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,dZ()?Reflect.construct(r,o||[],dW(this).constructor):r.apply(this,o)),"pieRef",null),dX(t,"sectorRefs",[]),dX(t,"id",I("recharts-pie-")),dX(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),q()(e)&&e()}),dX(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),q()(e)&&e()}),t.state={isAnimationFinished:!e.isAnimationActive,prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,sectorToFocus:0},t}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&dH(e,t)}(n,e),t=[{key:"isActiveIndex",value:function(e){var t=this.props.activeIndex;return Array.isArray(t)?-1!==t.indexOf(e):e===t}},{key:"hasActiveIndex",value:function(){var e=this.props.activeIndex;return Array.isArray(e)?0!==e.length:e||0===e}},{key:"renderLabels",value:function(e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var t=this.props,r=t.label,o=t.labelLine,i=t.dataKey,c=t.valueKey,l=eh(this.props,!1),s=eh(r,!1),u=eh(o,!1),f=r&&r.offsetRadius||20,p=e.map(function(e,t){var p=(e.startAngle+e.endAngle)/2,d=sU(e.cx,e.cy,e.outerRadius+f,p),h=dU(dU(dU(dU({},l),e),{},{stroke:"none"},s),{},{index:t,textAnchor:n.getTextAnchor(d.x,e.cx)},d),y=dU(dU(dU(dU({},l),e),{},{fill:"none",stroke:e.fill},u),{},{index:t,points:[sU(e.cx,e.cy,e.outerRadius,p),d]}),v=i;return M()(i)&&M()(c)?v="value":M()(i)&&(v=c),a().createElement(eD,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},o&&n.renderLabelLineItem(o,y,"line"),n.renderLabelItem(r,h,lQ(e,v)))});return a().createElement(eD,{className:"recharts-pie-labels"},p)}},{key:"renderSectorsStatically",value:function(e){var t=this,r=this.props,n=r.activeShape,o=r.blendStroke,i=r.inactiveShape;return e.map(function(r,c){if((null==r?void 0:r.startAngle)===0&&(null==r?void 0:r.endAngle)===0&&1!==e.length)return null;var l=t.isActiveIndex(c),s=i&&t.hasActiveIndex()?i:null,u=dU(dU({},r),{},{stroke:o?r.fill:r.stroke,tabIndex:-1});return a().createElement(eD,dz({ref:function(e){e&&!t.sectorRefs.includes(e)&&t.sectorRefs.push(e)},tabIndex:-1,className:"recharts-pie-sector"},ee(t.props,r,c),{key:"sector-".concat(null==r?void 0:r.startAngle,"-").concat(null==r?void 0:r.endAngle,"-").concat(r.midAngle,"-").concat(c)}),a().createElement(fN,dz({option:l?n:s,isActive:l,shapeType:"sector"},u)))})}},{key:"renderSectorsWithAnimation",value:function(){var e=this,t=this.props,r=t.sectors,n=t.isAnimationActive,o=t.animationBegin,i=t.animationDuration,c=t.animationEasing,l=t.animationId,s=this.state,u=s.prevSectors,f=s.prevIsAnimationActive;return a().createElement(nc,{begin:o,duration:i,isActive:n,easing:c,from:{t:0},to:{t:1},key:"pie-".concat(l,"-").concat(f),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(t){var n=t.t,o=[],i=(r&&r[0]).startAngle;return r.forEach(function(e,t){var r=u&&u[t],a=t>0?A()(e,"paddingAngle",0):0;if(r){var c=F(r.endAngle-r.startAngle,e.endAngle-e.startAngle),l=dU(dU({},e),{},{startAngle:i+a,endAngle:i+c(n)+a});o.push(l),i=l.endAngle}else{var s=F(0,e.endAngle-e.startAngle)(n),f=dU(dU({},e),{},{startAngle:i+a,endAngle:i+s+a});o.push(f),i=f.endAngle}}),a().createElement(eD,null,e.renderSectorsStatically(o))})}},{key:"attachKeyboardHandlers",value:function(e){var t=this;e.onkeydown=function(e){if(!e.altKey)switch(e.key){case"ArrowLeft":var r=++t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[r].focus(),t.setState({sectorToFocus:r});break;case"ArrowRight":var n=--t.state.sectorToFocus<0?t.sectorRefs.length-1:t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[n].focus(),t.setState({sectorToFocus:n});break;case"Escape":t.sectorRefs[t.state.sectorToFocus].blur(),t.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var e=this.props,t=e.sectors,r=e.isAnimationActive,n=this.state.prevSectors;return r&&t&&t.length&&(!n||!cG()(n,t))?this.renderSectorsWithAnimation():this.renderSectorsStatically(t)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var e=this,t=this.props,r=t.hide,n=t.sectors,o=t.className,i=t.label,c=t.cx,l=t.cy,s=t.innerRadius,u=t.outerRadius,f=t.isAnimationActive,p=this.state.isAnimationFinished;if(r||!n||!n.length||!N(c)||!N(l)||!N(s)||!N(u))return null;var d=(0,m.Z)("recharts-pie",o);return a().createElement(eD,{tabIndex:this.props.rootTabIndex,className:d,ref:function(t){e.pieRef=t}},this.renderSectors(),i&&this.renderLabels(n),s3.renderCallByParent(this.props,null,!1),(!f||p)&&dR.renderCallByParent(this.props,n,!1))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return t.prevIsAnimationActive!==e.isAnimationActive?{prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:[],isAnimationFinished:!0}:e.isAnimationActive&&e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:t.curSectors,isAnimationFinished:!0}:e.sectors!==t.curSectors?{curSectors:e.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(e,t){return e>t?"start":e<t?"end":"middle"}},{key:"renderLabelLineItem",value:function(e,t,r){if(a().isValidElement(e))return a().cloneElement(e,t);if(q()(e))return e(t);var n=(0,m.Z)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return a().createElement(pc,dz({},t,{key:r,type:"linear",className:n}))}},{key:"renderLabelItem",value:function(e,t,r){if(a().isValidElement(e))return a().cloneElement(e,t);var n=r;if(q()(e)&&(n=e(t),a().isValidElement(n)))return n;var o=(0,m.Z)("recharts-pie-label-text","boolean"==typeof e||q()(e)?"":e.className);return a().createElement(n3,dz({},t,{alignmentBaseline:"middle",className:o}),n)}}],t&&dq(n.prototype,t),r&&dq(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);dX(dY,"displayName","Pie"),dX(dY,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!e2.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),dX(dY,"parseDeltaAngle",function(e,t){return _(t-e)*Math.min(Math.abs(t-e),360)}),dX(dY,"getRealPieData",function(e){var t=e.data,r=e.children,n=eh(e,!1),o=es(r,dL);return t&&t.length?t.map(function(e,t){return dU(dU(dU({payload:e},n),e),o&&o[t]&&o[t].props)}):o&&o.length?o.map(function(e){return dU(dU({},n),e.props)}):[]}),dX(dY,"parseCoordinateOfPie",function(e,t){var r=t.top,n=t.left,o=t.width,i=t.height,a=sq(o,i);return{cx:n+B(e.cx,o,o/2),cy:r+B(e.cy,i,i/2),innerRadius:B(e.innerRadius,a,0),outerRadius:B(e.outerRadius,a,.8*a),maxRadius:e.maxRadius||Math.sqrt(o*o+i*i)/2}}),dX(dY,"getComposedData",function(e){var t,r,n=e.item,o=e.offset,i=void 0!==n.type.defaultProps?dU(dU({},n.type.defaultProps),n.props):n.props,a=dY.getRealPieData(i);if(!a||!a.length)return null;var c=i.cornerRadius,l=i.startAngle,s=i.endAngle,u=i.paddingAngle,f=i.dataKey,p=i.nameKey,d=i.valueKey,h=i.tooltipType,y=Math.abs(i.minAngle),v=dY.parseCoordinateOfPie(i,o),m=dY.parseDeltaAngle(l,s),b=Math.abs(m),g=f;M()(f)&&M()(d)?($(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),g="value"):M()(f)&&($(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),g=d);var x=a.filter(function(e){return 0!==lQ(e,g,0)}).length,w=b-x*y-(b>=360?x:x-1)*u,O=a.reduce(function(e,t){var r=lQ(t,g,0);return e+(N(r)?r:0)},0);return O>0&&(t=a.map(function(e,t){var n,o=lQ(e,g,0),i=lQ(e,p,t),a=(N(o)?o:0)/O,s=(n=t?r.endAngle+_(m)*u*(0!==o?1:0):l)+_(m)*((0!==o?y:0)+a*w),f=(n+s)/2,d=(v.innerRadius+v.outerRadius)/2,b=[{name:i,value:o,payload:e,dataKey:g,type:h}],x=sU(v.cx,v.cy,d,f);return r=dU(dU(dU({percent:a,cornerRadius:c,name:i,tooltipPayload:b,midAngle:f,middleRadius:d,tooltipPosition:x},e),v),{},{value:lQ(e,g),startAngle:n,endAngle:s,payload:e,paddingAngle:_(m)*u})})),dU(dU({},v),{},{sectors:t,data:a})});var dG=function(e){var t=e.chartName,r=e.GraphicalChild,n=e.defaultTooltipEventType,o=void 0===n?"axis":n,c=e.validateTooltipEventTypes,l=void 0===c?["axis"]:c,s=e.axisComponents,u=e.legendContent,f=e.formatAxisMap,p=e.defaultProps,d=function(e,t){var r=t.graphicalItems,n=t.stackGroups,o=t.offset,i=t.updateId,a=t.dataStartIndex,c=t.dataEndIndex,l=e.barSize,u=e.layout,f=e.barGap,p=e.barCategoryGap,d=e.maxBarSize,h=pV(u),y=h.numericAxisName,v=h.cateAxisName,m=!!r&&!!r.length&&r.some(function(e){var t=ei(e&&e.type);return t&&t.indexOf("Bar")>=0}),b=[];return r.forEach(function(r,h){var g=pF(e.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:c}),x=void 0!==r.type.defaultProps?pT(pT({},r.type.defaultProps),r.props):r.props,w=x.dataKey,O=x.maxBarSize,j=x["".concat(y,"Id")],S=x["".concat(v,"Id")],A=s.reduce(function(e,r){var n=t["".concat(r.axisType,"Map")],o=x["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||eP(!1);var i=n[o];return pT(pT({},e),{},pN(pN({},r.axisType,i),"".concat(r.axisType,"Ticks"),se(i)))},{}),E=A[v],k=A["".concat(v,"Ticks")],P=n&&n[j]&&n[j].hasStack&&ss(r,n[j].stackGroups),_=ei(r.type).indexOf("Bar")>=0,T=sh(E,k),N=[],C=m&&l4({barSize:l,stackGroups:n,totalSize:"xAxis"===v?A[v].width:"yAxis"===v?A[v].height:void 0});if(_){var D,I,B=M()(O)?d:O,R=null!==(D=null!==(I=sh(E,k,!0))&&void 0!==I?I:B)&&void 0!==D?D:0;N=l5({barGap:f,barCategoryGap:p,bandSize:R!==T?R:T,sizeList:C[S],maxBarSize:B}),R!==T&&(N=N.map(function(e){return pT(pT({},e),{},{position:pT(pT({},e.position),{},{offset:e.position.offset-R/2})})}))}var L=r&&r.type&&r.type.getComposedData;L&&b.push({props:pT(pT({},L(pT(pT({},A),{},{displayedData:g,props:e,dataKey:w,item:r,bandSize:T,barPosition:N,offset:o,stackedData:P,layout:u,dataStartIndex:a,dataEndIndex:c}))),{},pN(pN(pN({key:r.key||"item-".concat(h)},y,A[y]),v,A[v]),"animationId",i)),childIndex:el(e.children).indexOf(r),item:r})}),b},h=function(e,n){var o=e.props,i=e.dataStartIndex,a=e.dataEndIndex,c=e.updateId;if(!ef({props:o}))return null;var l=o.children,u=o.layout,p=o.stackOffset,h=o.data,y=o.reverseStackOrder,v=pV(u),m=v.numericAxisName,b=v.cateAxisName,g=es(l,r),x=sc(h,g,"".concat(m,"Id"),"".concat(b,"Id"),p,y),w=s.reduce(function(e,t){var r="".concat(t.axisType,"Map");return pT(pT({},e),{},pN({},r,pW(o,pT(pT({},t),{},{graphicalItems:g,stackGroups:t.axisType===m&&x,dataStartIndex:i,dataEndIndex:a}))))},{}),O=pY(pT(pT({},w),{},{props:o,graphicalItems:g}),null==n?void 0:n.legendBBox);Object.keys(w).forEach(function(e){w[e]=f(o,w[e],O,e.replace("Map",""),t)});var j=pH(w["".concat(b,"Map")]),S=d(o,pT(pT({},w),{},{dataStartIndex:i,dataEndIndex:a,updateId:c,graphicalItems:g,stackGroups:x,offset:O}));return pT(pT({formattedGraphicalItems:S,graphicalItems:g,offset:O,stackGroups:x},j),w)},y=function(e){var r;function n(e){var r,o,c,l,s;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),l=n,s=[e],l=pA(l),pN(c=function(e,t){if(t&&("object"===px(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,pS()?Reflect.construct(l,s||[],pA(this).constructor):l.apply(this,s)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),pN(c,"accessibilityManager",new fi),pN(c,"handleLegendBBoxUpdate",function(e){if(e){var t=c.state,r=t.dataStartIndex,n=t.dataEndIndex,o=t.updateId;c.setState(pT({legendBBox:e},h({props:c.props,dataStartIndex:r,dataEndIndex:n,updateId:o},pT(pT({},c.state),{},{legendBBox:e}))))}}),pN(c,"handleReceiveSyncEvent",function(e,t,r){c.props.syncId===e&&(r!==c.eventEmitterSymbol||"function"==typeof c.props.syncMethod)&&c.applySyncEvent(t)}),pN(c,"handleBrushChange",function(e){var t=e.startIndex,r=e.endIndex;if(t!==c.state.dataStartIndex||r!==c.state.dataEndIndex){var n=c.state.updateId;c.setState(function(){return pT({dataStartIndex:t,dataEndIndex:r},h({props:c.props,dataStartIndex:t,dataEndIndex:r,updateId:n},c.state))}),c.triggerSyncEvent({dataStartIndex:t,dataEndIndex:r})}}),pN(c,"handleMouseEnter",function(e){var t=c.getMouseInfo(e);if(t){var r=pT(pT({},t),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseEnter;q()(n)&&n(r,e)}}),pN(c,"triggeredAfterMouseMove",function(e){var t=c.getMouseInfo(e),r=t?pT(pT({},t),{},{isTooltipActive:!0}):{isTooltipActive:!1};c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseMove;q()(n)&&n(r,e)}),pN(c,"handleItemMouseEnter",function(e){c.setState(function(){return{isTooltipActive:!0,activeItem:e,activePayload:e.tooltipPayload,activeCoordinate:e.tooltipPosition||{x:e.cx,y:e.cy}}})}),pN(c,"handleItemMouseLeave",function(){c.setState(function(){return{isTooltipActive:!1}})}),pN(c,"handleMouseMove",function(e){e.persist(),c.throttleTriggeredAfterMouseMove(e)}),pN(c,"handleMouseLeave",function(e){c.throttleTriggeredAfterMouseMove.cancel();var t={isTooltipActive:!1};c.setState(t),c.triggerSyncEvent(t);var r=c.props.onMouseLeave;q()(r)&&r(t,e)}),pN(c,"handleOuterEvent",function(e){var t,r=eb(e),n=A()(c.props,"".concat(r));r&&q()(n)&&n(null!==(t=/.*touch.*/i.test(r)?c.getMouseInfo(e.changedTouches[0]):c.getMouseInfo(e))&&void 0!==t?t:{},e)}),pN(c,"handleClick",function(e){var t=c.getMouseInfo(e);if(t){var r=pT(pT({},t),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onClick;q()(n)&&n(r,e)}}),pN(c,"handleMouseDown",function(e){var t=c.props.onMouseDown;q()(t)&&t(c.getMouseInfo(e),e)}),pN(c,"handleMouseUp",function(e){var t=c.props.onMouseUp;q()(t)&&t(c.getMouseInfo(e),e)}),pN(c,"handleTouchMove",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&c.throttleTriggeredAfterMouseMove(e.changedTouches[0])}),pN(c,"handleTouchStart",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&c.handleMouseDown(e.changedTouches[0])}),pN(c,"handleTouchEnd",function(e){null!=e.changedTouches&&e.changedTouches.length>0&&c.handleMouseUp(e.changedTouches[0])}),pN(c,"handleDoubleClick",function(e){var t=c.props.onDoubleClick;q()(t)&&t(c.getMouseInfo(e),e)}),pN(c,"handleContextMenu",function(e){var t=c.props.onContextMenu;q()(t)&&t(c.getMouseInfo(e),e)}),pN(c,"triggerSyncEvent",function(e){void 0!==c.props.syncId&&fe.emit(ft,c.props.syncId,e,c.eventEmitterSymbol)}),pN(c,"applySyncEvent",function(e){var t=c.props,r=t.layout,n=t.syncMethod,o=c.state.updateId,i=e.dataStartIndex,a=e.dataEndIndex;if(void 0!==e.dataStartIndex||void 0!==e.dataEndIndex)c.setState(pT({dataStartIndex:i,dataEndIndex:a},h({props:c.props,dataStartIndex:i,dataEndIndex:a,updateId:o},c.state)));else if(void 0!==e.activeTooltipIndex){var l=e.chartX,s=e.chartY,u=e.activeTooltipIndex,f=c.state,p=f.offset,d=f.tooltipTicks;if(!p)return;if("function"==typeof n)u=n(d,e);else if("value"===n){u=-1;for(var y=0;y<d.length;y++)if(d[y].value===e.activeLabel){u=y;break}}var v=pT(pT({},p),{},{x:p.left,y:p.top}),m=Math.min(l,v.x+v.width),b=Math.min(s,v.y+v.height),g=d[u]&&d[u].value,x=p$(c.state,c.props.data,u),w=d[u]?{x:"horizontal"===r?d[u].coordinate:m,y:"horizontal"===r?b:d[u].coordinate}:pB;c.setState(pT(pT({},e),{},{activeLabel:g,activeCoordinate:w,activePayload:x,activeTooltipIndex:u}))}else c.setState(e)}),pN(c,"renderCursor",function(e){var r,n=c.state,o=n.isTooltipActive,i=n.activeCoordinate,l=n.activePayload,s=n.offset,u=n.activeTooltipIndex,f=n.tooltipAxisBandSize,p=c.getTooltipEventType(),d=null!==(r=e.props.active)&&void 0!==r?r:o,h=c.props.layout,y=e.key||"_recharts-cursor";return a().createElement(pm,{key:y,activeCoordinate:i,activePayload:l,activeTooltipIndex:u,chartName:t,element:e,isActive:d,layout:h,offset:s,tooltipAxisBandSize:f,tooltipEventType:p})}),pN(c,"renderPolarAxis",function(e,t,r){var n=A()(e,"type.axisType"),o=A()(c.state,"".concat(n,"Map")),a=e.type.defaultProps,l=void 0!==a?pT(pT({},a),e.props):e.props,s=o&&o[l["".concat(n,"Id")]];return(0,i.cloneElement)(e,pT(pT({},s),{},{className:(0,m.Z)(n,s.className),key:e.key||"".concat(t,"-").concat(r),ticks:se(s,!0)}))}),pN(c,"renderPolarGrid",function(e){var t=e.props,r=t.radialLines,n=t.polarAngles,o=t.polarRadius,a=c.state,l=a.radiusAxisMap,s=a.angleAxisMap,u=R(l),f=R(s),p=f.cx,d=f.cy,h=f.innerRadius,y=f.outerRadius;return(0,i.cloneElement)(e,{polarAngles:Array.isArray(n)?n:se(f,!0).map(function(e){return e.coordinate}),polarRadius:Array.isArray(o)?o:se(u,!0).map(function(e){return e.coordinate}),cx:p,cy:d,innerRadius:h,outerRadius:y,key:e.key||"polar-grid",radialLines:r})}),pN(c,"renderLegend",function(){var e=c.state.formattedGraphicalItems,t=c.props,r=t.children,n=t.width,o=t.height,a=c.props.margin||{},l=lH({children:r,formattedGraphicalItems:e,legendWidth:n-(a.left||0)-(a.right||0),legendContent:u});if(!l)return null;var s=l.item,f=pj(l,pb);return(0,i.cloneElement)(s,pT(pT({},f),{},{chartWidth:n,chartHeight:o,margin:a,onBBoxUpdate:c.handleLegendBBoxUpdate}))}),pN(c,"renderTooltip",function(){var e,t=c.props,r=t.children,n=t.accessibilityLayer,o=eu(r,ti);if(!o)return null;var a=c.state,l=a.isTooltipActive,s=a.activeCoordinate,u=a.activePayload,f=a.activeLabel,p=a.offset,d=null!==(e=o.props.active)&&void 0!==e?e:l;return(0,i.cloneElement)(o,{viewBox:pT(pT({},p),{},{x:p.left,y:p.top}),active:d,label:f,payload:d?u:[],coordinate:s,accessibilityLayer:n})}),pN(c,"renderBrush",function(e){var t=c.props,r=t.margin,n=t.data,o=c.state,a=o.offset,l=o.dataStartIndex,s=o.dataEndIndex,u=o.updateId;return(0,i.cloneElement)(e,{key:e.key||"_recharts-brush",onChange:sr(c.handleBrushChange,e.props.onChange),data:n,x:N(e.props.x)?e.props.x:a.left,y:N(e.props.y)?e.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:N(e.props.width)?e.props.width:a.width,startIndex:l,endIndex:s,updateId:"brush-".concat(u)})}),pN(c,"renderReferenceElement",function(e,t,r){if(!e)return null;var n=c.clipPathId,o=c.state,a=o.xAxisMap,l=o.yAxisMap,s=o.offset,u=e.type.defaultProps||{},f=e.props,p=f.xAxisId,d=void 0===p?u.xAxisId:p,h=f.yAxisId,y=void 0===h?u.yAxisId:h;return(0,i.cloneElement)(e,{key:e.key||"".concat(t,"-").concat(r),xAxis:a[d],yAxis:l[y],viewBox:{x:s.left,y:s.top,width:s.width,height:s.height},clipPathId:n})}),pN(c,"renderActivePoints",function(e){var t=e.item,r=e.activePoint,o=e.basePoint,i=e.childIndex,a=e.isRange,c=[],l=t.props.key,s=void 0!==t.item.type.defaultProps?pT(pT({},t.item.type.defaultProps),t.item.props):t.item.props,u=s.activeDot,f=pT(pT({index:i,dataKey:s.dataKey,cx:r.x,cy:r.y,r:4,fill:l2(t.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},eh(u,!1)),Q(u));return c.push(n.renderActiveDot(u,f,"".concat(l,"-activePoint-").concat(i))),o?c.push(n.renderActiveDot(u,pT(pT({},f),{},{cx:o.x,cy:o.y}),"".concat(l,"-basePoint-").concat(i))):a&&c.push(null),c}),pN(c,"renderGraphicChild",function(e,t,r){var n=c.filterFormatItem(e,t,r);if(!n)return null;var o=c.getTooltipEventType(),a=c.state,l=a.isTooltipActive,s=a.tooltipAxis,u=a.activeTooltipIndex,f=a.activeLabel,p=eu(c.props.children,ti),d=n.props,h=d.points,y=d.isRange,v=d.baseLine,m=void 0!==n.item.type.defaultProps?pT(pT({},n.item.type.defaultProps),n.item.props):n.item.props,b=m.activeDot,g=m.hide,x=m.activeBar,w=m.activeShape,O={};"axis"!==o&&p&&"click"===p.props.trigger?O={onClick:sr(c.handleItemMouseEnter,e.props.onClick)}:"axis"!==o&&(O={onMouseLeave:sr(c.handleItemMouseLeave,e.props.onMouseLeave),onMouseEnter:sr(c.handleItemMouseEnter,e.props.onMouseEnter)});var j=(0,i.cloneElement)(e,pT(pT({},n.props),O));if(!g&&l&&p&&(b||x||w)){if(u>=0){if(s.dataKey&&!s.allowDuplicatedCategory){var S="function"==typeof s.dataKey?function(e){return"function"==typeof s.dataKey?s.dataKey(e.payload):null}:"payload.".concat(s.dataKey.toString());E=z(h,S,f),k=y&&v&&z(v,S,f)}else E=null==h?void 0:h[u],k=y&&v&&v[u];if(w||x){var A=void 0!==e.props.activeIndex?e.props.activeIndex:u;return[(0,i.cloneElement)(e,pT(pT(pT({},n.props),O),{},{activeIndex:A})),null,null]}if(!M()(E))return[j].concat(pk(c.renderActivePoints({item:n,activePoint:E,basePoint:k,childIndex:u,isRange:y})))}else{var E,k,P,_=(null!==(P=c.getItemByXY(c.state.activeCoordinate))&&void 0!==P?P:{graphicalItem:j}).graphicalItem,T=_.item,N=void 0===T?e:T,C=_.childIndex,D=pT(pT(pT({},n.props),O),{},{activeIndex:C});return[(0,i.cloneElement)(N,D),null,null]}}return y?[j,null,null]:[j,null]}),pN(c,"renderCustomized",function(e,t,r){return(0,i.cloneElement)(e,pT(pT({key:"recharts-customized-".concat(r)},c.props),c.state))}),pN(c,"renderMap",{CartesianGrid:{handler:pR,once:!0},ReferenceArea:{handler:c.renderReferenceElement},ReferenceLine:{handler:pR},ReferenceDot:{handler:c.renderReferenceElement},XAxis:{handler:pR},YAxis:{handler:pR},Brush:{handler:c.renderBrush,once:!0},Bar:{handler:c.renderGraphicChild},Line:{handler:c.renderGraphicChild},Area:{handler:c.renderGraphicChild},Radar:{handler:c.renderGraphicChild},RadialBar:{handler:c.renderGraphicChild},Scatter:{handler:c.renderGraphicChild},Pie:{handler:c.renderGraphicChild},Funnel:{handler:c.renderGraphicChild},Tooltip:{handler:c.renderCursor,once:!0},PolarGrid:{handler:c.renderPolarGrid,once:!0},PolarAngleAxis:{handler:c.renderPolarAxis},PolarRadiusAxis:{handler:c.renderPolarAxis},Customized:{handler:c.renderCustomized}}),c.clipPathId="".concat(null!==(r=e.id)&&void 0!==r?r:I("recharts"),"-clip"),c.throttleTriggeredAfterMouseMove=g()(c.triggeredAfterMouseMove,null!==(o=e.throttleDelay)&&void 0!==o?o:1e3/60),c.state={},c}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&pE(e,t)}(n,e),r=[{key:"componentDidMount",value:function(){var e,t;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(e=this.props.margin.left)&&void 0!==e?e:0,top:null!==(t=this.props.margin.top)&&void 0!==t?t:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var e=this.props,t=e.children,r=e.data,n=e.height,o=e.layout,i=eu(t,ti);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,l=p$(this.state,r,a,c),s=this.state.tooltipTicks[a].coordinate,u=(this.state.offset.top+n)/2,f="horizontal"===o?{x:s,y:u}:{y:s,x:u},p=this.state.formattedGraphicalItems.find(function(e){return"Scatter"===e.item.type.name});p&&(f=pT(pT({},f),p.props.points[a].tooltipPosition),l=p.props.points[a].tooltipPayload);var d={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:l,activeCoordinate:f};this.setState(d),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(e,t){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==t.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==e.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==e.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}})}return null}},{key:"componentDidUpdate",value:function(e){ey([eu(e.children,ti)],[eu(this.props.children,ti)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var e=eu(this.props.children,ti);if(e&&"boolean"==typeof e.props.shared){var t=e.props.shared?"axis":"item";return l.indexOf(t)>=0?t:o}return o}},{key:"getMouseInfo",value:function(e){if(!this.container)return null;var t=this.container,r=t.getBoundingClientRect(),n={top:r.top+window.scrollY-document.documentElement.clientTop,left:r.left+window.scrollX-document.documentElement.clientLeft},o={chartX:Math.round(e.pageX-n.left),chartY:Math.round(e.pageY-n.top)},i=r.width/t.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var c=this.state,l=c.xAxisMap,s=c.yAxisMap,u=this.getTooltipEventType(),f=pU(this.state,this.props.data,this.props.layout,a);if("axis"!==u&&l&&s){var p=R(l).scale,d=R(s).scale,h=p&&p.invert?p.invert(o.chartX):null,y=d&&d.invert?d.invert(o.chartY):null;return pT(pT({},o),{},{xValue:h,yValue:y},f)}return f?pT(pT({},o),f):null}},{key:"inRange",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=e/r,i=t/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var c=this.state,l=c.angleAxisMap,s=c.radiusAxisMap;return l&&s?sX({x:o,y:i},R(l)):null}},{key:"parseEventsOfWrapper",value:function(){var e=this.props.children,t=this.getTooltipEventType(),r=eu(e,ti),n={};return r&&"axis"===t&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),pT(pT({},Q(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){fe.on(ft,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){fe.removeListener(ft,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(e,t,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===e||a.props.key===e.key||t===ei(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var e=this.clipPathId,t=this.state.offset,r=t.left,n=t.top,o=t.height,i=t.width;return a().createElement("defs",null,a().createElement("clipPath",{id:e},a().createElement("rect",{x:r,y:n,height:o,width:i})))}},{key:"getXScales",value:function(){var e=this.state.xAxisMap;return e?Object.entries(e).reduce(function(e,t){var r=pO(t,2),n=r[0],o=r[1];return pT(pT({},e),{},pN({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var e=this.state.yAxisMap;return e?Object.entries(e).reduce(function(e,t){var r=pO(t,2),n=r[0],o=r[1];return pT(pT({},e),{},pN({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(e){var t;return null===(t=this.state.xAxisMap)||void 0===t||null===(t=t[e])||void 0===t?void 0:t.scale}},{key:"getYScaleByAxisId",value:function(e){var t;return null===(t=this.state.yAxisMap)||void 0===t||null===(t=t[e])||void 0===t?void 0:t.scale}},{key:"getItemByXY",value:function(e){var t=this.state,r=t.formattedGraphicalItems,n=t.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],c=a.props,l=a.item,s=void 0!==l.type.defaultProps?pT(pT({},l.type.defaultProps),l.props):l.props,u=ei(l.type);if("Bar"===u){var f=(c.data||[]).find(function(t){return nh(e,t)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===u){var p=(c.data||[]).find(function(t){return sX(e,t)});if(p)return{graphicalItem:a,payload:p}}else if(fC(a,n)||fD(a,n)||fI(a,n)){var d=function(e){var t,r,n,o=e.activeTooltipItem,i=e.graphicalItem,a=e.itemData,c=(fC(i,o)?t="trapezoids":fD(i,o)?t="sectors":fI(i,o)&&(t="points"),t),l=fC(i,o)?null===(r=o.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:fD(i,o)?null===(n=o.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:fI(i,o)?o.payload:{},s=a.filter(function(e,t){var r=cG()(l,e),n=i.props[c].filter(function(e){var t;return(fC(i,o)?t=fB:fD(i,o)?t=fR:fI(i,o)&&(t=fL),t)(e,o)}),a=i.props[c].indexOf(n[n.length-1]);return r&&t===a});return a.indexOf(s[s.length-1])}({graphicalItem:a,activeTooltipItem:n,itemData:s.data}),h=void 0===s.activeIndex?d:s.activeIndex;return{graphicalItem:pT(pT({},a),{},{childIndex:h}),payload:fI(a,n)?s.data[d]:a.props.data[d]}}}return null}},{key:"render",value:function(){var e,t,r=this;if(!ef(this))return null;var n=this.props,o=n.children,i=n.className,c=n.width,l=n.height,s=n.style,u=n.compact,f=n.title,p=n.desc,d=eh(pj(n,pg),!1);if(u)return a().createElement(uC,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement(eT,pw({},d,{width:c,height:l,title:f,desc:p}),this.renderClipPath(),em(o,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!==(e=this.props.tabIndex)&&void 0!==e?e:0,d.role=null!==(t=this.props.role)&&void 0!==t?t:"application",d.onKeyDown=function(e){r.accessibilityManager.keyboardEvent(e)},d.onFocus=function(){r.accessibilityManager.focus()});var h=this.parseEventsOfWrapper();return a().createElement(uC,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement("div",pw({className:(0,m.Z)("recharts-wrapper",i),style:pT({position:"relative",cursor:"default",width:c,height:l},s)},h,{ref:function(e){r.container=e}}),a().createElement(eT,pw({},d,{width:c,height:l,title:f,desc:p,style:pI}),this.renderClipPath(),em(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pC(n.key),n)}}(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.Component);pN(y,"displayName",t),pN(y,"defaultProps",pT({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},p)),pN(y,"getDerivedStateFromProps",function(e,t){var r=e.dataKey,n=e.data,o=e.children,i=e.width,a=e.height,c=e.layout,l=e.stackOffset,s=e.margin,u=t.dataStartIndex,f=t.dataEndIndex;if(void 0===t.updateId){var p=pX(e);return pT(pT(pT({},p),{},{updateId:0},h(pT(pT({props:e},p),{},{updateId:0}),t)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(r!==t.prevDataKey||n!==t.prevData||i!==t.prevWidth||a!==t.prevHeight||c!==t.prevLayout||l!==t.prevStackOffset||!X(s,t.prevMargin)){var d=pX(e),y={chartX:t.chartX,chartY:t.chartY,isTooltipActive:t.isTooltipActive},v=pT(pT({},pU(t,n,c)),{},{updateId:t.updateId+1}),m=pT(pT(pT({},d),y),v);return pT(pT(pT({},m),h(pT({props:e},m),t)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(!ey(o,t.prevChildren)){var b,g,x,w,O=eu(o,sI),j=O&&null!==(b=null===(g=O.props)||void 0===g?void 0:g.startIndex)&&void 0!==b?b:u,S=O&&null!==(x=null===(w=O.props)||void 0===w?void 0:w.endIndex)&&void 0!==x?x:f,A=M()(n)||j!==u||S!==f?t.updateId+1:t.updateId;return pT(pT({updateId:A},h(pT(pT({props:e},t),{},{updateId:A,dataStartIndex:j,dataEndIndex:S}),t)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:S})}return null}),pN(y,"renderActiveDot",function(e,t,r){var n;return n=(0,i.isValidElement)(e)?(0,i.cloneElement)(e,t):q()(e)?e(t):a().createElement(t7,t),a().createElement(eD,{className:"recharts-active-dot",key:r},n)});var v=(0,i.forwardRef)(function(e,t){return a().createElement(y,pw({},e,{ref:t}))});return v.displayName=y.displayName,v}({chartName:"PieChart",GraphicalChild:dY,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:dc},{axisType:"radiusAxis",AxisComp:dA}],formatAxisMap:function(e,t,r,n,o){var i=e.width,a=e.height,c=e.startAngle,l=e.endAngle,s=B(e.cx,i,i/2),u=B(e.cy,a,a/2),f=sq(i,a,r),p=B(e.innerRadius,f,0),d=B(e.outerRadius,f,.8*f);return Object.keys(t).reduce(function(e,r){var i,a=t[r],f=a.domain,h=a.reversed;if(M()(a.range))"angleAxis"===n?i=[c,l]:"radiusAxis"===n&&(i=[p,d]),h&&(i=[i[1],i[0]]);else{var y,v=function(e){if(Array.isArray(e))return e}(y=i=a.range)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,c=[],l=!0,s=!1;try{for(i=(r=r.call(e)).next;!(l=(n=i.call(r)).done)&&(c.push(n.value),2!==c.length);l=!0);}catch(e){s=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(y,2)||function(e,t){if(e){if("string"==typeof e)return sz(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sz(e,2)}}(y,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();c=v[0],l=v[1]}var m=sn(a,o),b=m.realScaleType,g=m.scale;g.domain(f).range(i),so(g);var x=sl(g,sL(sL({},a),{},{realScaleType:b})),w=sL(sL(sL({},a),x),{},{range:i,radius:d,realScaleType:b,scale:g,cx:s,cy:u,innerRadius:p,outerRadius:d,startAngle:c,endAngle:l});return sL(sL({},e),{},sF({},r,w))},{})},defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}});let dK=[{id:1,sender:"Ruby - Social Media Handler",text:"Hello Alex, what would you like me to do for you today?",timestamp:"2021-01-01 12:00:00",avatar:y.Z.bot,color:"#E6FAEF",loading:!0,type:"textBlock"},{id:2,sender:"Adaeze Ndupu",text:"I want to add social media handles for my brand",timestamp:"2021-01-01 12:00:00",avatar:"https://avatars.githubusercontent.com/u/1214686",type:"textBlock"},{id:3,type:"socialMediaBlock",text:"Let’s connect your accounts. Which platforms do you want me to monitor? You can add more later \uD83D\uDE09",timestamp:"2021-01-01 12:00:00",avatar:y.Z.bot,color:"#E6FAEF",platforms:[{name:"Instagram",icon:y.Z.instagram},{name:"Twitter",icon:y.Z.twitter},{name:"Tiktok",icon:y.Z.tiktok},{name:"Facebook",icon:y.Z.facebook},{name:"Youtube",icon:y.Z.youtube}]},{id:4,sender:"Adaeze Ndupu",text:"My notification settings?",timestamp:"2021-01-01 12:00:00",avatar:"https://avatars.githubusercontent.com/u/1214686",type:"textBlock"},{id:5,sender:"Ruby - Social Media Handler",text:"How would you like to be notified?",timestamp:"2021-01-01 12:00:00",avatar:y.Z.bot,color:"#E6FAEF",type:"optionBlock",options:["Real-time alerts (Instant notifications for mentions, trends, and engagement spikes)","Daily summary (A digest of everything important)","Both real-time & daily summary"]},{id:6,sender:"Adaeze Ndupu",text:"Yeah phrases like “ai language game” “learn a language”, “how to learn french”",timestamp:"2021-01-01 12:00:00",avatar:"https://avatars.githubusercontent.com/u/1214686",type:"textBlock"},{id:7,sender:"Ruby - Social Media Handler",text:"Want me to analyze sentiment? I'll flag positive, neutral, and negative mentions",timestamp:"2021-01-01 12:00:00",avatar:y.Z.bot,color:"#E6FAEF",type:"optionSubmitBlock",options:["Yes","No"],submitText:"Send"},{id:8,sender:"Adaeze Ndupu",text:"I would love to add some links",timestamp:"2021-01-01 12:00:00",avatar:"https://avatars.githubusercontent.com/u/1214686",type:"textBlock"},{id:9,sender:"Ruby - Social Media Handler",text:"Want to track engagement on specific posts? Share the link to the post in question",timestamp:"2021-01-01 12:00:00",avatar:y.Z.bot,color:"#E6FAEF",type:"linkBlock"},{id:10,sender:"Adaeze Ndupu",text:"I want to see some alerts",timestamp:"2021-01-01 12:00:00",avatar:"https://avatars.githubusercontent.com/u/1214686",type:"textBlock"},{id:11,sender:"Ruby - Social Media Handler",text:"All alerts will be sent there",timestamp:"2021-01-01 12:00:00",avatar:y.Z.bot,color:"#E6FAEF",options:[{name:"Red Alerts",description:"Critical information is being shared",icon:"\uD83D\uDD34"},{name:"Green Alerts",description:"Routine updates and general insights are being shared",icon:"\uD83D\uDFE2"},{name:"Black Alerts",description:"Actions that require user intervention are being shared",icon:"⚫"}],type:"alertBlock"},{id:12,sender:"Adaeze Ndupu",text:"I want to see how my posts are performing",timestamp:"2021-01-01 12:00:00",avatar:"https://avatars.githubusercontent.com/u/1214686",type:"textBlock"},{id:13,sender:"Ruby - Social Media Handler",timestamp:"2021-01-01 12:00:00",avatar:y.Z.bot,type:"postBlock",platform:"Instagram",icon:y.Z.instagram,color:"#E6FAEF",posts:[{alert:"green",review:"Your latest post is gaining traction!",image:"https://avatars.githubusercontent.com/u/1214686",title:"Product Launch day!\uD83D\uDE80",likes:"+5k",comments:"230",shares:"180",suggestion:"Reply to top comments & use stories to drive traffic",type:"media"},{alert:"red",review:"Negative Comment Alert \uD83D\uDEA8",type:"comment",comment:"Your website is slow! Tried checking out, but it crashed",sender:"AdebisiEmediong",suggestion:"Acknowledge & offer a solution"}]},{id:13,sender:"Adaeze Ndupu",text:"What about tiktok",timestamp:"2021-01-01 12:00:00",avatar:"https://avatars.githubusercontent.com/u/1214686",type:"textBlock"},{id:13,sender:"Ruby - Social Media Handler",timestamp:"2021-01-01 12:00:00",avatar:y.Z.bot,type:"postBlock",color:"#E6FAEF",platform:"Tiktok",icon:y.Z.tiktok,posts:[{alert:"green",review:"New Video Performance",image:"https://avatars.githubusercontent.com/u/1214686",title:"AI in Action at AlexTech",likes:"1.2k",comments:"400",shares:"180",suggestion:"Engage with comments & post a follow-up",type:"media"},{alert:"black",review:"Competitor Alert!",image:"https://avatars.githubusercontent.com/u/1214686",title:"@Allnnovators just posted about a similar topic!",suggestion:"Duet their video for engagement boost",type:"media"}]},{id:14,sender:"Knox - Code Analyser",text:"To get started, I need access to your code. You can either:",timestamp:"2021-01-01 12:00:00",avatar:y.Z.bot,color:"#FEE8E6",type:"codeOptionsBlock",options:["Upload Project","Paste Code"]},{id:15,sender:"Knox - Code Analyser",timestamp:"2021-01-01 12:00:00",avatar:y.Z.bot,type:"codeAlertBlock",color:"#FEE8E6",text:"I found 1 critical security vulnerability in your code",codes:[{language:"php",code:`<?php
$username = $_GET['username'];
$query = "SELECT * FROM users WHERE username = '$username'";
$result = mysqli_query($conn, $query);
?>||`,errorLines:[3],alert:"red"},{review:"⚠ SQL Injection Risk Found!",language:"php",problem:"This query is vulnerable because it inserts user input directly into the database.",riskLevel:"\uD83D\uDD34 Critical",solution:"Use prepared statements to prevent malicious injections.",code:`$stmt = $conn->prepare("SELECT * FROM users WHERE username = ?");
$stmt->bind_param("s", $username);
$stmt->execute();`,alert:"red"}]},{id:16,sender:"Knox - Code Analyser",text:"Would you like to apply this fix now?",timestamp:"2021-01-01 12:00:00",avatar:y.Z.bot,color:"#FEE8E6",type:"codeOptionsBlock",options:["Mark as Reviewed","Apply fix"]},{id:17,sender:"Lynx - Site Broken Link Analyser",text:"Let’s analyse your links. What URLs would you like me to check? You can add multiple links \uD83D\uDE09",timestamp:"2021-01-01 12:00:00",avatar:y.Z.bot,color:"#FEE8E6",type:"domainLinkBlock",links:[]},{id:18,sender:"Lynx - Site Broken Link Analyser",text:"I found 1 broken link in your code",timestamp:"2021-01-01 12:00:00",avatar:y.Z.bot,color:"#FEE8E6",type:"summaryReportBlock",alert:"black",title:"Timbu Tech Ltd Summary Report",chartData:[{name:"Checked pages",value:5,color:"#F5A30B"},{name:"Checked links",value:99,color:"#7086FD"},{name:"Active Links",value:95,color:"#14B8A6"},{name:"Broken Links",value:4,color:"#FD7072"}],tableOverview:{title:"Blog",linkCount:35,brokenLinkCount:1},tableData:[{link:"https://www.timbu.com",status:"broken",issueType:"The page no longer exists (404 Error).",suggestedFix:"Redirect to a relevant article or remove the broken link."},{link:"https://www.timbu.com/update",status:"active",issueType:"---",suggestedFix:"---"}]}],dJ=()=>{let[e,t]=(0,i.useState)(dK),r=(0,i.useRef)(null),[n,m]=(0,i.useState)([]),[b,g]=(0,i.useState)({}),[x,w]=(0,i.useState)(!0),[O,j]=(0,i.useState)([]),[S,A]=(0,i.useState)(!0),[E,k]=(0,i.useState)([]),[P,M]=(0,i.useState)([]),[_,T]=(0,i.useState)(!0),[N,C]=(0,i.useState)(""),[D,I]=(0,i.useState)("markdown"),[B,R]=(0,i.useState)({}),L=()=>{r.current?.scrollIntoView({behavior:"smooth"})};(0,i.useEffect)(()=>{L()},[e]);let F=r=>{r.trim()&&(t([...e,{id:Date.now().toString(),text:r,timestamp:"2021-01-01 12:00:00",sender:"Adaeze Ndupu",avatar:"https://avatars.githubusercontent.com/u/1214686",type:"textBlock"}]),"paste code"===r.toLowerCase()&&setTimeout(()=>{z()},100),"apply fix"===r.toLowerCase()&&setTimeout(()=>{$()},100))},z=()=>{let e={id:Date.now().toString(),sender:"Knox - Code Analyser",timestamp:"2021-01-01 12:00:00",avatar:y.Z.bot,color:"#FEE8E6",type:"codeBlock",language:"markdown",code:`-----------------------------------------------
Paste your code below for analysis:
-----------------------------------------------`,state:"input"};t(t=>[...t,e])},$=()=>{let e={id:15,sender:"Knox - Code Analyser",timestamp:"2021-01-01 12:00:00",avatar:y.Z.bot,type:"codeAlertBlock",color:"#FEE8E6",text:"Your code is now secure! Let me know if you need another scan.",codes:[{language:"php",code:`<?php
$username = $_GET['username'];
// Using prepared statements to prevent SQL injection
$stmt = $conn->prepare("SELECT * FROM users WHERE username = ?");
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();
?>||`,alert:"green"}]};t(t=>[...t,e])},U=e=>new Date(e).toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric",year:"numeric"}),q=e=>{m(n.filter(t=>t.name!==e))},Z=e=>{j(O.filter((t,r)=>r!==e))},W=e=>{k(E.filter((t,r)=>r!==e)),M(P.filter((t,r)=>r!==e))},H=()=>{let e=[{name:"Instagram",icon:y.Z.instagram},{name:"Twitter",icon:y.Z.twitter},{name:"Tiktok",icon:y.Z.tiktok},{name:"Facebook",icon:y.Z.facebook},{name:"Youtube",icon:y.Z.youtube}].find(e=>!n.some(t=>t.name===e.name));e&&m([...n,e])},X=()=>{j([...O,""])},V=()=>{k([...E,""]),M([...P,""])},Y=()=>{Object.keys(b).length===n.length&&Object.values(b).every(e=>e.trim())&&w(!1)},G=()=>{O.every(e=>e.trim())&&A(!1)},K=()=>{E.every(e=>e.trim())&&P.every(e=>e.trim())&&T(!1)},J=(e,t)=>{g({...b,[e]:t})},Q=(e,t)=>{let r=[...O];r[e]=t,j(r)},ee=(e,t)=>{let r=[...E];r[e]=t,k(r)},et=(e,t)=>{let r=[...P];r[e]=t,M(r)},er=r=>{if("textBlock"===r.type)return o.jsx("p",{className:"text-[#344054] text-[15px] mb-2",children:r.text});if("linkBlock"===r.type)return(0,o.jsxs)("div",{className:"flex flex-col gap-2 mb-2",children:[o.jsx("p",{className:"text-[#344054] text-[15px]",children:r.text}),(0,o.jsxs)("div",{className:"p-2 bg-[#F1F1FE] rounded-[5px] flex flex-col gap-4",children:[O.map((e,t)=>(0,o.jsxs)("div",{className:"flex items-center gap-3",children:[(0,o.jsxs)("div",{className:"flex-1 max-w-[400px] flex items-center bg-white border border-[#E6EAEF] rounded py-[9px] px-3",children:[o.jsx("div",{className:"text-[#98A2B3] text-sm mr-2 whitespace-nowrap",children:"https:// |"}),o.jsx("input",{type:"text",placeholder:"example.com",value:e,onChange:e=>Q(t,e.target.value),disabled:!S,className:"bg-transparent flex-1 outline-none text-[15px] text-[#344054] placeholder:text-[#98A2B3]"})]}),S&&o.jsx("button",{onClick:()=>Z(t),children:o.jsx(l.Z,{size:20,strokeWidth:1.5,className:"text-[#344054]"})})]},t)),S&&(0,o.jsxs)("div",{className:"flex items-center gap-4",children:[(0,o.jsxs)(v.z,{className:"flex items-center gap-2 p-0",onClick:X,children:[o.jsx(s.Z,{size:14,strokeWidth:1,className:"text-[#7141F8]"}),o.jsx("span",{className:"text-[#7141F8] text-xs",children:"Add a link"})]}),o.jsx(v.z,{className:"bg-[#7141F8] text-white h-8 w-full max-w-[75px]",onClick:G,disabled:!O.length||!O.every(e=>e.trim()),children:o.jsx("span",{children:"Send"})})]})]})]});if("domainLinkBlock"===r.type)return(0,o.jsxs)("div",{className:"flex flex-col w-full gap-2 mb-2",children:[o.jsx("p",{className:"text-[#344054] text-[15px]",children:r.text}),(0,o.jsxs)("div",{className:"p-2 bg-[#F1F1FE] rounded-[5px] flex flex-col gap-4 overflow-x-auto scrollbar-none [&::-webkit-scrollbar]:hidden",children:[E.length>0?E.map((e,t)=>(0,o.jsxs)("div",{className:"flex items-center gap-3",children:[(0,o.jsxs)("div",{className:"flex gap-2 w-full max-w-[500px]",children:[o.jsx("div",{className:"flex w-[174px] max-w-[174px] items-center bg-white border border-[#E6EAEF] rounded py-[9px] px-3",children:o.jsx("input",{type:"text",placeholder:"E.g Main Domain",value:P[t]||"",onChange:e=>et(t,e.target.value),disabled:!_,className:"bg-transparent flex-1 outline-none text-[15px] text-[#344054] placeholder:text-[#98A2B3]"})}),(0,o.jsxs)("div",{className:"flex flex-1 items-center bg-white border border-[#E6EAEF] rounded py-[9px] px-3 overflow-hidden",children:[o.jsx("div",{className:"text-[#98A2B3] text-sm mr-2 whitespace-nowrap",children:"https:// |"}),o.jsx("input",{type:"text",placeholder:"example.com",value:e,onChange:e=>ee(t,e.target.value),disabled:!_,className:"bg-transparent outline-none text-[15px] text-[#344054] placeholder:text-[#98A2B3] min-w-0"})]})]}),_&&o.jsx("button",{onClick:()=>W(t),children:o.jsx(l.Z,{size:20,strokeWidth:1.5,className:"text-[#344054]"})})]},t)):(0,o.jsxs)("div",{className:"flex items-center gap-3",children:[(0,o.jsxs)("div",{className:"flex gap-2 w-full max-w-[500px]",children:[o.jsx("div",{className:"flex w-[174px] max-w-[174px] items-center bg-white border border-[#E6EAEF] rounded py-[9px] px-3",children:o.jsx("input",{type:"text",placeholder:"E.g Main Domain",className:"bg-transparent flex-1 outline-none text-[15px] text-[#344054] placeholder:text-[#98A2B3]",onChange:e=>{0===P.length&&et(0,e.target.value)}})}),(0,o.jsxs)("div",{className:"flex flex-1 items-center bg-white border border-[#E6EAEF] rounded py-[9px] px-3 overflow-hidden",children:[o.jsx("div",{className:"text-[#98A2B3] text-sm mr-2 whitespace-nowrap",children:"https:// |"}),o.jsx("input",{type:"text",placeholder:"example.com",className:"bg-transparent outline-none text-[15px] text-[#344054] placeholder:text-[#98A2B3]",onChange:e=>{0===E.length&&ee(0,e.target.value)}})]})]}),o.jsx("button",{onClick:()=>{0===E.length&&0===P.length&&(k([""]),M([""]))},children:o.jsx(l.Z,{size:20,strokeWidth:1.5,className:"text-[#344054]"})})]}),_&&(0,o.jsxs)("div",{className:"flex items-center gap-4",children:[(0,o.jsxs)(v.z,{className:"flex items-center gap-2 p-0",onClick:V,children:[o.jsx(s.Z,{size:14,strokeWidth:1,className:"text-[#7141F8]"}),o.jsx("span",{className:"text-[#7141F8] text-xs",children:"Add a link"})]}),o.jsx(v.z,{className:"bg-[#7141F8] text-white h-8 w-full max-w-[75px]",onClick:K,disabled:!E.length||!E.every(e=>e.trim())||!P.every(e=>e.trim()),children:o.jsx("span",{children:"Send"})})]})]})]});if("socialMediaBlock"===r.type)return 0===n.length&&r.platforms&&m(r.platforms),(0,o.jsxs)("div",{className:"flex flex-col gap-2 mb-2",children:[o.jsx("p",{className:"text-[#344054] text-[15px]",children:r.text}),(0,o.jsxs)("div",{className:"p-2 bg-[#F1F1FE] rounded-[5px] flex flex-col gap-4",children:[n.map((e,t)=>(0,o.jsxs)("div",{className:"flex items-center gap-3",children:[o.jsx(c.default,{src:e.icon,alt:e.name,width:36,height:36,className:"rounded-[3px]"}),(0,o.jsxs)("div",{className:"flex-1 w-full max-w-[400px] flex items-center bg-white border border-[#E6EAEF] rounded py-[9px] px-3",children:[o.jsx("div",{className:"text-[#98A2B3] text-sm mr-2 whitespace-nowrap",children:"@ |"}),o.jsx("input",{type:"text",placeholder:"socialmediahandle",value:b[e.name]||"",onChange:t=>J(e.name,t.target.value),disabled:!x,className:"bg-transparent flex-1 outline-none text-[15px] text-[#344054] placeholder:text-[#98A2B3]"})]}),x&&o.jsx("button",{onClick:()=>q(e.name),children:o.jsx(l.Z,{size:20,strokeWidth:1.5,className:"text-[#344054]"})})]},`${e.name}-${t}`)),x&&(0,o.jsxs)("div",{className:"flex items-center gap-4",children:[(0,o.jsxs)(v.z,{className:"flex items-center gap-2 p-0",onClick:H,disabled:n.length>=5,children:[o.jsx(s.Z,{size:14,strokeWidth:1,className:"text-[#7141F8] disabled:text-[#98A2B3]"}),o.jsx("span",{className:"text-[#7141F8] disabled:text-[#98A2B3] text-xs",children:"Add an account"})]}),o.jsx(v.z,{className:"bg-[#7141F8] text-white h-8 w-full max-w-[75px]",onClick:Y,disabled:Object.keys(b).length!==n.length||!Object.values(b).every(e=>e.trim()),children:o.jsx("span",{children:"Send"})})]})]})]});if("optionBlock"===r.type)return(0,o.jsxs)("div",{className:"flex flex-col gap-2 mb-2",children:[o.jsx("p",{className:"text-[#344054] text-[15px]",children:r.text}),o.jsx("div",{className:"flex flex-wrap gap-4 p-2 rounded-[5px] bg-[#F1F1FE]",children:r.options.map((e,t)=>o.jsx("div",{className:"flex items-center gap-3 p-[10px] rounded-[9px] border border-[#E6EAEF] bg-white",children:(0,o.jsxs)("label",{htmlFor:`option-${t}`,className:"flex items-center gap-3 cursor-pointer",children:[o.jsx("span",{className:"text-[#344054] text-sm",children:e}),(0,o.jsxs)("div",{className:"relative flex items-center",children:[o.jsx("input",{type:"radio",name:"option",id:`option-${t}`,className:"peer h-4 w-4 appearance-none rounded-full border border-[#C5CCD3] bg-white checked:border-[#6868F7] cursor-pointer",defaultChecked:0===t}),o.jsx("div",{className:"pointer-events-none absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 opacity-0 peer-checked:opacity-100 ",children:o.jsx("div",{className:"h-1.5 w-1.5 bg-[#6868F7] rounded-full"})})]})]})},t))})]});if("optionSubmitBlock"===r.type)return(0,o.jsxs)("div",{className:"flex flex-col gap-2 w-full mb-2",children:[o.jsx("p",{className:"text-[#344054] text-[15px]",children:r.text}),(0,o.jsxs)("div",{className:"flex justify-between flex-1 items-center gap-4 p-2 rounded-[5px] bg-[#F1F1FE]",children:[o.jsx("div",{className:"w-fit flex gap-4",children:r.options.map((e,t)=>o.jsx("div",{className:"flex items-center gap-3 p-[10px] rounded-[9px] border border-[#E6EAEF] bg-white",children:(0,o.jsxs)("label",{htmlFor:`optionSubmit-${t}`,className:"flex items-center gap-3 cursor-pointer",children:[o.jsx("span",{className:"text-[#344054] text-sm",children:e}),(0,o.jsxs)("div",{className:"relative flex items-center",children:[o.jsx("input",{type:"radio",name:"optionSubmit",id:`optionSubmit-${t}`,className:"peer h-4 w-4 appearance-none rounded-full border border-[#C5CCD3] bg-white checked:border-[#6868F7] cursor-pointer",defaultChecked:0===t}),o.jsx("div",{className:"pointer-events-none absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 opacity-0 peer-checked:opacity-100 ",children:o.jsx("div",{className:"h-1.5 w-1.5 bg-[#6868F7] rounded-full"})})]})]})},t))}),o.jsx(v.z,{className:"bg-[#7141F8] text-white h-8 w-full max-w-[75px]",children:r.submitText})]})]});if("alertBlock"===r.type)return(0,o.jsxs)("div",{className:"flex flex-col gap-2 w-full mb-2",children:[o.jsx("p",{className:"text-[#344054] text-[15px]",children:r.text}),o.jsx("div",{className:"flex flex-col w-full gap-2 p-2 rounded-[5px] bg-[#F1F1FE]",children:r.options.map((e,t)=>(0,o.jsxs)("div",{className:"flex items-center gap-3",children:[(0,o.jsxs)("span",{className:"text-[#667085] text-[15px]",children:[e.icon," ",e.name,":"]}),o.jsx("span",{className:"text-[#344054] text-[15px]",children:e.description})]},t))})]});else if("postBlock"===r.type)return o.jsx("div",{className:"flex flex-col gap-2 w-full mb-2",children:o.jsx("div",{className:"flex flex-col w-full space-y-4 p-2 rounded-[5px] bg-[#F1F1FE]",children:r.posts.map((e,t)=>(0,o.jsxs)(a().Fragment,{children:[(0,o.jsxs)("div",{className:"flex gap-2",children:[(0,o.jsxs)("div",{className:"relative flex flex-col items-center",children:[o.jsx("div",{className:`w-[6px] h-[6px] rounded-[2px] ${"red"===e.alert?"bg-[#FB6B61]":"black"===e.alert?"bg-[#344054]":"bg-[#5EDF9A]"}`}),o.jsx("div",{className:`w-[1px] h-full ${"red"===e.alert?"bg-[#FC9A93]":"black"===e.alert?"bg-[#98A2B3]":"bg-[#91E9BA]"}`}),o.jsx("div",{className:`w-[6px] h-[6px] rounded-[2px] ${"red"===e.alert?"bg-[#FB6B61]":"black"===e.alert?"bg-[#344054]":"bg-[#5EDF9A]"}`})]}),(0,o.jsxs)("div",{className:"flex-1 flex flex-col gap-2",children:[0===t&&(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[o.jsx(c.default,{src:r.icon,alt:r.name,width:24,height:24,className:"rounded-[3px]"}),(0,o.jsxs)("span",{className:"text-[#344054] text-[15px]",children:[r.platform," Update"]})]}),o.jsx("h4",{className:"text-[#344054] text-[15px]",children:e.review}),(0,o.jsxs)("div",{className:"flex items-center gap-2 py-1 px-[6px] rounded-[3px] bg-white",children:["media"===e.type&&o.jsx(c.default,{src:e.image,alt:e.title,width:72,height:72,className:"rounded-[3px] border border-black/5"}),(0,o.jsxs)("div",{className:"flex flex-col gap-2",children:["media"===e.type&&(0,o.jsxs)("h5",{className:"text-[#344054] text-[15px]",children:['"',e?.title,'"']}),"media"===e.type&&(e?.likes||e?.comments||e?.shares)&&(0,o.jsxs)("div",{className:"flex items-center gap-1",children:[(0,o.jsxs)("span",{className:"text-[#667085] text-[15px]",children:[e?.likes," Likes"]}),o.jsx("span",{className:"text-[#667085] text-[15px]",children:"|"}),(0,o.jsxs)("span",{className:"text-[#667085] text-[15px]",children:[e?.comments," Comments"]}),o.jsx("span",{className:"text-[#667085] text-[15px]",children:"|"}),(0,o.jsxs)("span",{className:"text-[#667085] text-[15px]",children:[e?.shares," Shares"]})]}),"comment"===e.type&&(0,o.jsxs)("div",{className:"flex flex-wrap gap-1",children:[(0,o.jsxs)("span",{className:"text-[#667085] text-[15px]",children:["@",e?.sender,":"]}),(0,o.jsxs)("span",{className:"text-[#344054] text-[15px]",children:['"',e?.comment,'"']})]}),o.jsx(v.z,{className:"p-0 h-fit text-[#7141F8] text-[13px] w-fit",children:"media"===e.type?"View Post":"View Comment"})]})]}),(0,o.jsxs)("div",{className:"flex flex-wrap gap-1",children:[o.jsx("span",{className:"text-[#667085] text-[15px]",children:"Suggested Action:"}),o.jsx("span",{className:"text-[#344054] text-[15px]",children:e?.suggestion})]})]})]}),t!==r.posts.length-1&&o.jsx("div",{className:"h-[1px] bg-white w-full"})]},t))})});else if("codeOptionsBlock"===r.type){let e=B[r.id];return(0,o.jsxs)("div",{className:"flex flex-col gap-2 w-full mb-2",children:[o.jsx("p",{className:"text-[#344054] text-[15px]",children:r.text}),o.jsx("div",{className:"flex gap-4 p-2 rounded-[5px] bg-[#F1F1FE]",children:r.options.map((t,n)=>{let i=B[r.id]===t;return e?o.jsx("div",{className:`text-[13px] flex items-center ${i?"text-[#7141F8]":"text-[#667085]"}`,children:t},n):o.jsx(v.z,{className:`h-8 ${0===n?"border border-[#7141F8] bg-white text-[#7141F8]":"bg-[#7141F8] text-white"}`,onClick:()=>{R({...B,[r.id]:t}),F(t)},children:t},n)})})]})}else if("codeBlock"===r.type)return o.jsx("div",{className:"w-full p-2 rounded-[5px] bg-[#F1F1FE] mb-2",children:(0,o.jsxs)("div",{className:"border border-[#D0D0FD] rounded-[7px] overflow-hidden",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center px-4 py-2 h-[42px] bg-[#F6F7F9] ",children:[o.jsx("span",{className:"text-[#344054] text-[13px] font-semibold",children:"input"===r.state?D:r.language}),o.jsx(v.z,{className:`p-0 h-fit text-[#667085] ${"display"===r.state?"text-opacity-100":"text-opacity-40"} text-[13px]`,onClick:()=>navigator.clipboard.writeText(r.code),disabled:"display"!==r.state,children:"Copy code"})]}),"input"===r.state?(0,o.jsxs)("div",{className:"p-4 bg-white",children:[o.jsx("pre",{className:"whitespace-pre-wrap mb-2 text-[#344054] text-[13px]",children:r.code}),o.jsx("textarea",{value:N,onChange:e=>{C(e.target.value),I("php")},className:"w-full h-[150px] outline-none resize-none text-[#344054] text-[13px]"}),o.jsx("div",{className:"flex justify-end mt-2",children:o.jsx(v.z,{className:"bg-[#7141F8] text-white h-8 px-6 py-3",disabled:!N,onClick:()=>{let n={...r,code:r.code+"\n"+N,language:D,state:"display"};t(e.map(e=>e.id===r.id?n:e)),C("")},children:"Send"})})]}):o.jsx("pre",{className:"p-4 bg-white overflow-x-auto text-[#344054] text-[13px]",children:o.jsx("code",{children:r.code})})]})});else if("codeAlertBlock"===r.type)return(0,o.jsxs)("div",{className:"flex flex-col gap-2 w-full mb-2",children:[o.jsx("p",{className:"text-[#344054] text-[15px]",children:r.text}),o.jsx("div",{className:"flex flex-col w-full space-y-4 p-2 rounded-[5px] bg-[#F1F1FE]",children:r.codes.map((e,t)=>(0,o.jsxs)(a().Fragment,{children:[(0,o.jsxs)("div",{className:"flex gap-2",children:[(0,o.jsxs)("div",{className:"relative flex flex-col items-center flex-shrink-0",children:[o.jsx("div",{className:`w-[6px] h-[6px] rounded-[2px] ${"red"===e.alert?"bg-[#FB6B61]":"black"===e.alert?"bg-[#344054]":"bg-[#5EDF9A]"}`}),o.jsx("div",{className:`w-[1px] h-full ${"red"===e.alert?"bg-[#FC9A93]":"black"===e.alert?"bg-[#98A2B3]":"bg-[#91E9BA]"}`}),o.jsx("div",{className:`w-[6px] h-[6px] rounded-[2px] ${"red"===e.alert?"bg-[#FB6B61]":"black"===e.alert?"bg-[#344054]":"bg-[#5EDF9A]"}`})]}),(0,o.jsxs)("div",{className:"flex flex-col gap-2 w-full overflow-hidden",children:[e.review&&o.jsx("p",{className:"text-[#344054] text-[15px] break-words",children:e.review}),e.problem&&e.riskLevel&&e.solution&&[{label:"Problem",value:e.problem},{label:"Risk Level",value:e.riskLevel},{label:"Solution",value:e.solution}].map((e,t)=>(0,o.jsxs)("div",{className:"flex flex-wrap gap-1",children:[(0,o.jsxs)("span",{className:"text-[#667085] text-[15px]",children:[e.label,":"]}),o.jsx("span",{className:"text-[#344054] text-[15px] break-words",children:e.value})]},t)),(0,o.jsxs)("div",{className:"border border-[#D0D0FD] rounded-[7px] overflow-hidden",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center px-4 py-2 h-[42px] bg-[#F6F7F9]",children:[o.jsx("span",{className:"text-[#344054] text-[13px] font-semibold",children:e.language}),o.jsx(v.z,{className:"p-0 h-fit text-[#667085] text-[13px]",onClick:()=>navigator.clipboard.writeText(e.code),children:"Copy code"})]}),o.jsx("pre",{className:"p-4 bg-white overflow-x-auto text-[#344054] text-[13px] max-w-full",children:o.jsx("code",{className:"break-words whitespace-pre-wrap",children:e.errorLines?o.jsx(o.Fragment,{children:e.code.split("\n").map((t,r)=>o.jsx("div",{className:e.errorLines?.includes(r+1)?"bg-[#FEE8E6] text-[#F81404] px-1 rounded":"",children:t},r))}):e.code})})]})]})]}),t!==r.codes.length-1&&o.jsx("div",{className:"h-[1px] bg-white w-full"})]},t))})]});else if("summaryReportBlock"===r.type)return(0,o.jsxs)("div",{className:"flex flex-col gap-2 w-full mb-2",children:[o.jsx("p",{className:"text-[#344054] text-[15px]",children:r.text}),(0,o.jsxs)("div",{className:"w-full flex gap-2 p-2 rounded-[5px] bg-[#F1F1FE] mb-2",children:[(0,o.jsxs)("div",{className:"relative flex flex-col items-center flex-shrink-0",children:[o.jsx("div",{className:`w-[6px] h-[6px] rounded-[2px] ${"red"===r.alert?"bg-[#FB6B61]":"black"===r.alert?"bg-[#344054]":"bg-[#5EDF9A]"}`}),o.jsx("div",{className:`w-[1px] h-full ${"red"===r.alert?"bg-[#FC9A93]":"black"===r.alert?"bg-[#98A2B3]":"bg-[#91E9BA]"}`}),o.jsx("div",{className:`w-[6px] h-[6px] rounded-[2px] ${"red"===r.alert?"bg-[#FB6B61]":"black"===r.alert?"bg-[#344054]":"bg-[#5EDF9A]"}`})]}),(0,o.jsxs)("div",{className:"flex flex-col gap-2 w-full",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[o.jsx(c.default,{src:"https://avatars.githubusercontent.com/u/10347539?v=4",alt:"org",width:24,height:24,className:"rounded-[3px]"}),o.jsx("span",{className:"text-[#344054] text-[15px]",children:"Timbu Tech Ltd"})]}),(0,o.jsxs)("div",{className:"w-full border border-[#D0D0FD] rounded-[7px] overflow-hidden",children:[o.jsx("div",{className:"px-4 py-2 h-[42px] bg-[#F6F7F9] border-b border-[#DDE6EB]",children:o.jsx("span",{className:"text-[#344054] text-[13px] font-semibold",children:r.title})}),(0,o.jsxs)("div",{className:"py-4 bg-white flex flex-col overflow-x-auto text-[#344054] text-[13px]",children:[(0,o.jsxs)("div",{className:"flex items-center justify-center gap-5 flex-wrap border-b border-[#E6EAEF] w-full",children:[o.jsx("div",{className:"h-[250px] w-[200px] flex justify-center items-center",children:o.jsx(ej,{width:"100%",height:"100%",children:o.jsx(dG,{children:(0,o.jsxs)(dY,{data:r.chartData,cx:"50%",cy:"50%",innerRadius:60,outerRadius:80,paddingAngle:2,dataKey:"value",children:[r.chartData.map((e,t)=>o.jsx(dL,{fill:e.color},`cell-${t}`)),o.jsx(s3,{content:({viewBox:e})=>{let{cx:t,cy:r}=e;return o.jsx("text",{x:t,y:r,textAnchor:"middle",dominantBaseline:"middle",className:"text-xs fill-[#344054]",children:"Full Scan"})},position:"center"})]})})})}),o.jsx("div",{className:"flex flex-col gap-3 justify-center",children:r.chartData.map((e,t)=>(0,o.jsxs)("div",{className:"flex items-center gap-1",children:[o.jsx("div",{className:"w-2 h-2 rounded-full",style:{backgroundColor:e.color}}),(0,o.jsxs)("span",{className:"text-[#667085] text-xs",children:[e.name," ",o.jsx("span",{className:"text-black font-semibold ml-1",children:e.value})]})]},t))})]}),(0,o.jsxs)("div",{className:"p-6",children:[(0,o.jsxs)("div",{className:"flex items-center gap-[10px]",children:[o.jsx("span",{className:"text-[#475467] text-[13px] font-medium",children:r.tableOverview.title}),o.jsx("span",{className:"w-2 h-2 rounded-full bg-[#E4E7EC]"}),(0,o.jsxs)("span",{className:"text-[#475467] text-[13px] font-medium",children:[r.tableOverview.linkCount," links"]}),o.jsx("span",{className:"w-2 h-2 rounded-full bg-[#E4E7EC]"}),(0,o.jsxs)("span",{className:"text-[#475467] text-[13px] font-medium",children:[o.jsx("span",{className:"text-[#FD7072]",children:r.tableOverview.brokenLinkCount})," ","broken"]})]}),o.jsx("div",{className:"mt-4 overflow-x-auto scrollbar-none [&::-webkit-scrollbar]:hidden rounded-[7px] border border-[#E4E7EC]",children:(0,o.jsxs)("table",{className:"w-full border-collapse",children:[o.jsx("thead",{children:(0,o.jsxs)("tr",{className:"bg-[#F6F7F9] h-[32px]",children:[o.jsx("th",{className:"text-left text-[10px] font-medium text-[#667085] p-[10px]",children:"Link"}),o.jsx("th",{className:"text-left text-[10px] font-medium text-[#667085] p-[10px]",children:"Status"}),o.jsx("th",{className:"text-left text-[10px] font-medium text-[#667085] p-[10px]",children:"Issue Type"}),o.jsx("th",{className:"text-left text-[10px] font-medium text-[#667085] p-[10px]",children:"Suggested Fix"})]})}),o.jsx("tbody",{children:r.tableData.map((e,t)=>(0,o.jsxs)("tr",{className:`${t!==r.tableData.length-1?"border-b border-[#F6F7F9]":""}`,children:[o.jsx("td",{className:"text-xs text-[#5F5FE1] p-[10px] h-[56px]",children:o.jsx("a",{href:e.link,target:"_blank",rel:"noreferrer",children:e.link})}),o.jsx("td",{className:"text-xs p-[10px] h-[56px]",children:o.jsx("span",{className:`px-4 py-1 rounded-[20px] text-xs border capitalize ${"broken"===e.status?"bg-[#FEE8E6] text-[#700902] border-[#FC9A93]":"bg-[#E6FAEF] text-[#005C2B] border-[#91E9BA]"}`,children:e.status})}),o.jsx("td",{className:"text-xs text-[#475467] p-[10px] h-[56px]",children:e.issueType}),o.jsx("td",{className:"text-xs text-[#475467] p-[10px] h-[56px]",children:e.suggestedFix})]},t))})]})})]})]})]})]})]})]})};return o.jsx("div",{className:"flex h-[calc(100vh-70px)] relative w-full overflow-hidden",children:o.jsx("div",{className:"flex flex-col flex-1 transition-[margin] duration-300 ease-in-out",children:o.jsx("div",{className:"flex-1 flex flex-col justify-end relative overflow-hidden",children:o.jsx("div",{className:"overflow-y-auto pb-5 mb-[180px] [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]",children:(0,o.jsxs)("div",{className:"flex flex-col",children:[e.map((t,r)=>{let n=r>0?e[r-1]:null,i=!n||n.sender!==t.sender,l=!n||U(t.timestamp)!==U(n.timestamp);return(0,o.jsxs)(a().Fragment,{children:[l&&(0,o.jsxs)("div",{className:"relative my-4",children:[o.jsx("div",{className:"absolute inset-0 flex items-center",children:o.jsx("div",{className:"w-full border-t border-dotted border-[#E6EAEF]"})}),o.jsx("div",{className:"relative flex justify-center",children:o.jsx("span",{className:"bg-white px-4 py-1 text-[13px] text-[#101828] border border-[#E6EAEF] rounded-[30px]",children:U(t.timestamp)})})]}),(0,o.jsxs)("div",{className:`bg-white group hover:bg-gray-50 transition-colors flex items-start px-5 ${i&&"mt-5"}`,children:[o.jsx("div",{className:"w-8 flex-shrink-0 mr-2 flex items-center justify-center",children:i?o.jsx(c.default,{src:t.avatar,alt:"avatar",width:40,height:40,className:"rounded-[7px]",style:{backgroundColor:t?.color,border:"1px solid #E6EAEF"}}):o.jsx("span",{className:"text-xs text-[#98A2B3] opacity-0 group-hover:opacity-100 transition-opacity mt-1",children:new Date(t.timestamp).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit",hour12:!1})})}),(0,o.jsxs)("div",{className:"flex-grow",children:[i&&(0,o.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[o.jsx("span",{className:"font-bold text-[15px] text-[#1D2939]",children:t.sender}),o.jsx("span",{className:"text-xs text-[#98A2B3]",children:new Date(t.timestamp).toLocaleTimeString([],{hour:"numeric",minute:"2-digit",hour12:!0})})]}),(0,o.jsxs)("div",{className:"flex items-start justify-between relative",children:[er(t),(0,o.jsxs)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity flex items-center ml-4 absolute right-0 bottom-full bg-white shadow-md rounded-[8px] border border-[#E6EAEF] p-[2px]",children:[o.jsx("button",{className:"pb-[4px] px-[10px] hover:bg-gray-200 rounded",children:o.jsx("span",{className:"inline-flex items-center justify-center w-4 h-4 text-base",children:"✅"})}),o.jsx("button",{className:"pb-[4px] px-[10px] hover:bg-gray-200 rounded",children:o.jsx("span",{className:"inline-flex items-center justify-center w-4 h-4 text-base",children:"\uD83D\uDC40"})}),o.jsx("button",{className:"pb-[4px] px-[10px] hover:bg-gray-200 rounded",children:o.jsx("span",{className:"inline-flex items-center justify-center w-4 h-4 text-base",children:"\uD83D\uDE4C"})}),o.jsx("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:o.jsx(u.Z,{size:16,className:"text-[#667085]"})}),o.jsx("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:o.jsx(f.Z,{size:16,className:"text-[#667085]"})}),o.jsx("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:o.jsx(p.Z,{size:16,className:"text-[#667085]"})}),o.jsx("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:o.jsx(d.Z,{size:16,className:"text-[#667085]"})}),o.jsx("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:o.jsx(h.Z,{size:16,className:"text-[#667085]"})})]})]})]})]})]},t.id)}),o.jsx("div",{ref:r})]})})})})})}},49827:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(12902),o=r(42451),i=r(51692),a=r(94227),c=r(32437);let l=function({children:e}){let{state:t}=(0,o.useContext)(c.R);return t?.channelLoading?null:(0,n.jsxs)("div",{className:"w-full flex relative overflow-auto",children:[n.jsx(i.Z,{}),n.jsx(a.default,{}),n.jsx("div",{className:"w-full lg:ml-[495px] mt-[60px] relative",children:e})]})}},51692:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var n=r(12902),o=r(42451),i=r(32437);r(2849),r(30333),r(31426);var a=r(71032),c=r(22655);function l(){let{state:e,dispatch:t}=(0,o.useContext)(i.R),{orgId:r}=e;(0,c.usePathname)();let l=(0,o.useRef)(null);return process.env.NEXT_PUBLIC_CONNECT_URL,(0,n.jsxs)("div",{children:[n.jsx(a.Ix,{limit:1}),(0,n.jsxs)("audio",{controls:!0,ref:l,style:{display:"none"},children:[n.jsx("source",{src:"/audio/message.mp3",type:"audio/mpeg"}),"Your browser does not support the audio element."]})]})}r(27940),process.env.NEXT_PUBLIC_CLIENT_URL},37872:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>a,__esModule:()=>i,default:()=>c});var n=r(77599);let o=(0,n.createProxy)(String.raw`/Users/<USER>/work/tauri/telex/src/app/(client)/client/home/<USER>/[id]/page.tsx`),{__esModule:i,$$typeof:a}=o;o.default;let c=(0,n.createProxy)(String.raw`/Users/<USER>/work/tauri/telex/src/app/(client)/client/home/<USER>/[id]/page.tsx#default`)},41025:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>a,__esModule:()=>i,default:()=>c});var n=r(77599);let o=(0,n.createProxy)(String.raw`/Users/<USER>/work/tauri/telex/src/app/(client)/client/home/<USER>/Users/<USER>/work/tauri/telex/src/app/(client)/client/home/<USER>"../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[9811,1528,6804,6863,8644,8506,4340,5459,116,1032,333,8592,5496,6604,8854,7935,4227],()=>r(27346));module.exports=n})();