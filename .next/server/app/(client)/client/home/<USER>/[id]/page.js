(()=>{var e={};e.id=8187,e.ids=[8187],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},49411:e=>{"use strict";e.exports=require("node:path")},97742:e=>{"use strict";e.exports=require("node:process")},41041:e=>{"use strict";e.exports=require("node:url")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},36377:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>c}),r(24514),r(41025),r(44796),r(79411),r(40755),r(72406);var s=r(17471),i=r(83436),l=r(75072),n=r.n(l),a=r(88612),o={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>a[e]);r.d(t,o);let c=["",{children:["(client)",{children:["client",{children:["home",{children:["channels",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,24514)),"/Users/<USER>/work/tauri/telex/src/app/(client)/client/home/<USER>/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,41025)),"/Users/<USER>/work/tauri/telex/src/app/(client)/client/home/<USER>"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,44796)),"/Users/<USER>/work/tauri/telex/src/app/(client)/client/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,79411)),"/Users/<USER>/work/tauri/telex/src/app/(client)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,40755)),"/Users/<USER>/work/tauri/telex/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,72406)),"/Users/<USER>/work/tauri/telex/src/app/not-found.tsx"]}],u=["/Users/<USER>/work/tauri/telex/src/app/(client)/client/home/<USER>/[id]/page.tsx"],d="/(client)/client/home/<USER>/[id]/page",x={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/(client)/client/home/<USER>/[id]/page",pathname:"/client/home/<USER>/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},5882:(e,t,r)=>{Promise.resolve().then(r.bind(r,10492))},57872:(e,t,r)=>{Promise.resolve().then(r.bind(r,49827))},10492:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(12902),i=r(42451),l=r(58256),n=r(56e3),a=r(10517),o=r(54766),c=r(11495),u=r(32437),d=r(61714),x=r(34078),p=r(11467),h=r(9386),m=r(13031),f=r(86705);let g=()=>{let{state:e}=(0,i.useContext)(u.R),{fetchMoreData:t,hasMore:r}=(0,m.Z)(),g=async(t,r,s,i)=>{let l={content:s,thread_id:r,media:i,mentions:e?.mentions};await (0,p.xo)(`/threads/${t}`,l)},v=async(t,r,s,i)=>{let l={content:s,channels_id:t,user_id:e?.user?.id,thread_id:e?.thread?.thread_id,media:i,mentions:e?.mentions};await (0,p.xo)(`/channels/${t}/messages`,l)};return e?.channelloading?null:(0,s.jsxs)("div",{className:"flex h-[calc(100vh-70px)] relative w-full overflow-hidden",children:[s.jsx(o.Z,{}),s.jsx(c.Z,{}),(0,s.jsxs)("div",{className:`relative flex flex-col flex-1 ${e?.reply?"xl:mr-[440px]":""}`,children:[s.jsx(l.Z,{}),s.jsx(a.Z,{}),s.jsx("div",{className:"absolute bottom-0 w-full",children:e?.channelloading||e?.channelDetails?.access!==!1?e?.channelDetails?.archived?s.jsx(x.Z,{}):s.jsx(n.Z,{subscription:e?.channelSubscription,sendMessage:g}):s.jsx(d.Z,{})})]}),s.jsx("div",{className:`fixed mt-[60px] right-0 top-0 z-20 w-full sm:w-[408px] h-full bg-white border-l border-[#E6EAEF] ${e?.hoverProfile?"translate-x-0":"translate-x-full"}`,children:s.jsx(f.Z,{})}),s.jsx("div",{className:`fixed mt-[60px] right-0 top-0 z-20 w-full sm:w-[440px] h-full bg-white border-l border-[#E6EAEF] transition-transform duration-300 ease-in-out ${e?.reply?"translate-x-0":"translate-x-full"}`,children:s.jsx(h.Z,{handleSendMessage:v,fetchMoreData:t,hasMore:r})})]})}},49827:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(12902),i=r(42451),l=r(51692),n=r(94227),a=r(32437);let o=function({children:e}){let{state:t}=(0,i.useContext)(a.R);return t?.channelLoading?null:(0,s.jsxs)("div",{className:"w-full flex relative overflow-auto",children:[s.jsx(l.Z,{}),s.jsx(n.default,{}),s.jsx("div",{className:"w-full lg:ml-[495px] mt-[60px] relative",children:e})]})}},24514:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>n,__esModule:()=>l,default:()=>a});var s=r(77599);let i=(0,s.createProxy)(String.raw`/Users/<USER>/work/tauri/telex/src/app/(client)/client/home/<USER>/[id]/page.tsx`),{__esModule:l,$$typeof:n}=i;i.default;let a=(0,s.createProxy)(String.raw`/Users/<USER>/work/tauri/telex/src/app/(client)/client/home/<USER>/[id]/page.tsx#default`)},41025:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>n,__esModule:()=>l,default:()=>a});var s=r(77599);let i=(0,s.createProxy)(String.raw`/Users/<USER>/work/tauri/telex/src/app/(client)/client/home/<USER>/Users/<USER>/work/tauri/telex/src/app/(client)/client/home/<USER>"../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9811,1528,6804,6863,8644,8506,4340,5459,116,1032,333,8592,6416,1236,8240,6268,9054,4166,5496,6604,8854,7935,9528,4227,6e3,5648,7054,4278],()=>r(36377));module.exports=s})();