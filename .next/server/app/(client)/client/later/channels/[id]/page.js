(()=>{var e={};e.id=5508,e.ids=[5508],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},49411:e=>{"use strict";e.exports=require("node:path")},97742:e=>{"use strict";e.exports=require("node:process")},41041:e=>{"use strict";e.exports=require("node:url")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},66102:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>o}),r(26578),r(71905),r(44796),r(79411),r(40755),r(72406);var s=r(17471),a=r(83436),l=r(75072),n=r.n(l),i=r(88612),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);r.d(t,c);let o=["",{children:["(client)",{children:["client",{children:["later",{children:["channels",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,26578)),"/Users/<USER>/work/tauri/telex/src/app/(client)/client/later/channels/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71905)),"/Users/<USER>/work/tauri/telex/src/app/(client)/client/later/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,44796)),"/Users/<USER>/work/tauri/telex/src/app/(client)/client/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,79411)),"/Users/<USER>/work/tauri/telex/src/app/(client)/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,40755)),"/Users/<USER>/work/tauri/telex/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,72406)),"/Users/<USER>/work/tauri/telex/src/app/not-found.tsx"]}],d=["/Users/<USER>/work/tauri/telex/src/app/(client)/client/later/channels/[id]/page.tsx"],u="/(client)/client/later/channels/[id]/page",x={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/(client)/client/later/channels/[id]/page",pathname:"/client/later/channels/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},93688:(e,t,r)=>{Promise.resolve().then(r.bind(r,52378))},91376:(e,t,r)=>{Promise.resolve().then(r.bind(r,5073))},90803:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(87593).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},52378:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(12902),a=r(42451),l=r(58256),n=r(56e3),i=r(10517),c=r(54766),o=r(11495),d=r(32437),u=r(61714),x=r(34078),p=r(11467),h=r(9386),m=r(13031),f=r(86705);let g=()=>{let{state:e}=(0,a.useContext)(d.R),{fetchMoreData:t,hasMore:r}=(0,m.Z)(),g=async(t,r,s,a)=>{let l={content:s,thread_id:r,media:a,mentions:e?.mentions};await (0,p.xo)(`/threads/${t}`,l)},v=async(t,r,s,a)=>{let l={content:s,channels_id:t,user_id:e?.user?.id,thread_id:e?.thread?.thread_id,media:a,mentions:e?.mentions};await (0,p.xo)(`/channels/${t}/messages`,l)};return e?.channelloading?null:(0,s.jsxs)("div",{className:"flex h-[calc(100vh-70px)] relative w-full overflow-hidden",children:[s.jsx(c.Z,{}),s.jsx(o.Z,{}),(0,s.jsxs)("div",{className:`relative flex flex-col flex-1 ${e?.reply?"mr-[440px]":""}`,children:[s.jsx(l.Z,{}),s.jsx(i.Z,{}),s.jsx("div",{className:"absolute bottom-0 w-full",children:e?.channelloading||e?.channelDetails?.access!==!1?e?.channelDetails?.archived?s.jsx(x.Z,{}):s.jsx(n.Z,{subscription:e?.channelSubscription,sendMessage:g}):s.jsx(u.Z,{})})]}),s.jsx("div",{className:`fixed mt-[60px] right-0 top-0 z-20 w-full sm:w-[408px] h-full bg-white border-l border-[#E6EAEF] ${e?.hoverProfile?"translate-x-0":"translate-x-full"}`,children:s.jsx(f.Z,{})}),s.jsx("div",{className:`fixed mt-[60px] right-0 top-0 z-20 w-full sm:w-[440px] h-full bg-white border-l border-[#E6EAEF] transition-transform duration-300 ease-in-out ${e?.reply?"translate-x-0":"translate-x-full"}`,children:s.jsx(h.Z,{handleSendMessage:v,fetchMoreData:t,hasMore:r})})]})}},5073:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var s=r(12902),a=r(42451),l=r(51692),n=r(32437),i=r(31426),c=r(90803),o=r(1563),d=r(61347),u=r(34099),x=r(22655),p=r(12764),h=r(11467),m=r(27940),f=r(90488),g=r(8592);function v(){let{state:e,dispatch:t}=(0,a.useContext)(n.R),{later:r,dataId:l}=e,v=(0,a.useRef)(null),b=(0,a.useRef)({});(0,x.useParams)().id2;let y=(0,x.useRouter)(),w=async e=>{t({type:i.a.DATA_ID,payload:e?.id}),localStorage.setItem("data-id",e?.id),(e?.channel_type==="Public"||e?.channel_type==="Private Channel")&&_(e),e?.channel_type==="Direct Message"&&N(e),e?.channel_type==="Group Direct Message"&&j(e)},_=async e=>{localStorage.setItem("channelName",e?.channel_name),localStorage.setItem("channelId",e?.channel_id),y.push(`/client/later/channels/${e?.channel_id}`)},j=async e=>{localStorage.setItem("channelName",e?.channel_name),localStorage.setItem("channelId",e?.channel_id),y.push(`/client/later/dms/${e?.channel_id}`)},N=async e=>{localStorage.setItem("channelName",e?.channel_name),localStorage.setItem("channelId",e?.channel_id);let t=localStorage.getItem("orgId")||"",r={chat_type:"user",participant_id:e?.id},s=await (0,h.xo)(`/organisations/${t}/dms`,r);(s?.status===200||s?.status===201)&&y.push(`/client/later/dm/${s?.data?.data?.channel_id}/${s?.data?.data?.participant_id}`)};return s.jsx(s.Fragment,{children:(0,s.jsxs)("div",{className:`fixed top-[60px] bottom-[60px] left-0 lg:left-[145px] h-[calc(100vh-30px)] bg-blue-300 lg:translate-x-0 ${e?.channelBar===!0&&e?.openSidebar?"translate-x-[145px]":e?.channelBar!==!0||e?.openSidebar?"-translate-x-full ":"translate-x-0"}
      pt-4 flex flex-col gap-6 sm:w-[350px] transition-transform duration-300 ease-in-out z-20`,children:[(0,s.jsxs)("div",{className:"flex items-center justify-between px-3 ",children:[s.jsx("div",{className:"flex items-center gap-[5px] md:justify-between w-full",children:s.jsx(p.Z,{name:"Later"})}),s.jsx(c.Z,{className:"block md:hidden text-gray-500 cursor-pointer",onClick:()=>t({type:i.a.CHANNEL_BAR,payload:!1})})]}),s.jsx("div",{className:"overflow-auto [&::-webkit-scrollbar]:hidden text-blue-50 cursor-pointer pb-40",ref:v,onScroll:()=>{v.current&&sessionStorage.setItem("sidebar-scroll",v.current.scrollTop.toString())},onClick:()=>t({type:i.a.CHANNEL_BAR,payload:!1}),children:(0,s.jsxs)("div",{className:"flex flex-col",children:[r?.map((e,t)=>{let r=e?.id===l;return s.jsxs("div",{className:`px-3 py-4 hover:bg-[#4B4BB4] border-t border-gray-500 ${r?"bg-[#4B4BB4]":""}`,onClick:()=>w(e),children:[s.jsxs("div",{className:"flex-1 flex items-center gap-1 mb-3",children:[e?.channel_type==="Private Channel"?s.jsx(g.khe,{className:f.cn("size-3",r?"text-white":"text-blue-50")}):s.jsx(o.Z,{className:f.cn("size-3",r?"text-white":"text-blue-50")}),s.jsx("p",{className:f.cn("text-[13px] leading-4 lowercase truncate w-[180px]",r?"font-semibold text-white":"text-blue-50"),title:e?.channel_name,children:e?.channel_name})]}),s.jsxs("div",{ref:t=>{b.current[e?.id]=t},className:"flex items-start gap-3",children:[s.jsx("div",{className:"relative size-8 rounded-[5px] border overflow-hidden",children:s.jsx(d.default,{width:36,height:36,src:e?.avatar_url?e?.avatar_url:e?.entity_type=="user"||e?.entity_type===""?u.Z?.user:u.Z?.bot,className:"rounded-[5px] size-8 object-cover",alt:e?.username})}),s.jsxs("div",{className:"-mt-1",children:[s.jsx("span",{className:"text-[15px] font-semibold text-white break-words max-w-[300px] line-clamp-2",children:e?.username}),s.jsx("p",{className:"text-[14px] text-[#D0D0FD] break-words max-w-[230px] line-clamp-2",children:m.ju(e?.content)})]})]})]},t)}),0===r.length&&s.jsx("p",{className:"self-center mt-10",children:"No available data"})]})})]})})}let b=function({children:e}){return(0,s.jsxs)("div",{className:"w-full flex relative",children:[s.jsx(l.Z,{}),s.jsx(v,{}),s.jsx("div",{className:"w-full lg:ml-[495px] mt-[60px] relative",children:e})]})}},26578:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>n,__esModule:()=>l,default:()=>i});var s=r(77599);let a=(0,s.createProxy)(String.raw`/Users/<USER>/work/tauri/telex/src/app/(client)/client/later/channels/[id]/page.tsx`),{__esModule:l,$$typeof:n}=a;a.default;let i=(0,s.createProxy)(String.raw`/Users/<USER>/work/tauri/telex/src/app/(client)/client/later/channels/[id]/page.tsx#default`)},71905:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>n,__esModule:()=>l,default:()=>i});var s=r(77599);let a=(0,s.createProxy)(String.raw`/Users/<USER>/work/tauri/telex/src/app/(client)/client/later/layout.tsx`),{__esModule:l,$$typeof:n}=a;a.default;let i=(0,s.createProxy)(String.raw`/Users/<USER>/work/tauri/telex/src/app/(client)/client/later/layout.tsx#default`)},11628:(e,t,r)=>{"use strict";r.d(t,{gm:()=>l});var s=r(42451);r(12902);var a=s.createContext(void 0);function l(e){let t=s.useContext(a);return e||t||"ltr"}},1153:(e,t,r)=>{"use strict";r.d(t,{M:()=>c});var s,a=r(42451),l=r(37159),n=(s||(s=r.t(a,2)))[" useId ".trim().toString()]||(()=>void 0),i=0;function c(e){let[t,r]=a.useState(n());return(0,l.b)(()=>{e||r(e=>e??String(i++))},[e]),e||(t?`radix-${t}`:"")}},73834:(e,t,r)=>{"use strict";r.d(t,{C2:()=>n,TX:()=>i,fC:()=>c});var s=r(42451),a=r(7452),l=r(12902),n=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),i=s.forwardRef((e,t)=>(0,l.jsx)(a.WV.span,{...e,ref:t,style:{...n,...e.style}}));i.displayName="VisuallyHidden";var c=i}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9811,1528,6804,6863,8644,8506,5459,116,1032,333,8592,6416,1236,8240,6268,9054,4166,5496,6604,8854,7935,9528,6e3,5648,7054,4278],()=>r(66102));module.exports=s})();