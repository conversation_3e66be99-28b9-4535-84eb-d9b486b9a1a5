"use strict";exports.id=4278,exports.ids=[4278],exports.modules={10517:(e,s,t)=>{t.d(s,{Z:()=>w});var a=t(12902),l=t(42451),r=t.n(l),n=t(22655),i=t(31426),c=t(32437),d=t(11467);let o=()=>{let e=(0,n.useParams)().id,{state:s,dispatch:t}=(0,l.useContext)(c.R),a=localStorage.getItem("token")||"",[r,o]=(0,l.useState)(1),[x,h]=(0,l.useState)(!0),[m,u]=(0,l.useState)(!0),p=async(s=1)=>{try{let a=await (0,d.Gl)(`/threads/channels/${e}?page=${s}&limit=80`);if(a?.status===200||a?.status===201){let e=Array.isArray(a.data?.data)?a.data?.data:[];t({type:i.a.MESSAGES,payload:{newThreads:e,newPage:s}}),s>1&&e.length>0?h(!0):h(e.length>=80)}u(!1)}catch(e){console.error("Error fetching threads:",e),h(!1)}finally{o(s)}};return(0,l.useEffect)(()=>{e&&a&&p(1).finally(()=>t({type:i.a.MESSAGE_LOADING,payload:!1}))},[e,a,t,s?.countCallback]),{fetchMoreData:()=>{x&&p(r+1)},hasMore:x,loading:m}};var x=t(91582),h=t(24140),m=t(61347),u=t(34099);let p=()=>{let{state:e,dispatch:s}=(0,l.useContext)(c.R),{channelDetails:t}=e;return a.jsx("div",{className:"bg-gradient-to-b from-[#E3D9FE] via-white via-[73.49%] to-white to-[96.49%] h-[50vh]",children:a.jsx("div",{className:"px-5 pt-[150px]",children:a.jsx("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[a.jsx(m.default,{src:u.Z.disco,alt:"logo",width:64,height:64}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("h1",{className:"text-[28px] font-bold text-[#1D2939]",children:["Welcome to #",t?.name]}),(0,a.jsxs)("p",{className:"text-[15px] text-[#475467]",children:["Share all information relating to ",t?.name," here. All team members await you! \uD83D\uDE09"]}),(0,a.jsxs)("div",{onClick:()=>s({type:i.a.CHANNEL_INVITE,payload:!0}),className:"mt-5 border border-[#E6EAEF] px-2 py-3 rounded-[7px] max-w-[344px] flex items-center gap-[10px] cursor-pointer",children:[a.jsx("div",{children:a.jsx(m.default,{src:u.Z.teammates,alt:"teammates",width:32,height:32})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-[15px] font-semibold text-[#1D2939] mb-1",children:"Invite teammates"}),a.jsx("p",{className:"text-[13px] text-[#475467]",children:"Add your entire team in seconds"})]})]})]})]})})})})},g=({item:e})=>(0,a.jsxs)("div",{className:`relative bg-white group hover:bg-gray-50 py-2 transition-colors flex items-start px-3 mx-5 border-2 rounded-lg my-5 ${e?.status==="success"?"border-[#00CC5F]":"border-[#F81404]"}`,children:[a.jsx("div",{className:"w-8 mr-2 flex items-center justify-center",children:a.jsx(m.default,{src:e?.avatar_url||u.Z?.user,alt:"avatar",width:40,height:40,className:"rounded-[7px] border"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx("span",{className:"font-bold pb-1 text-[15px] text-[#1D2939]",children:e?.username}),a.jsx("span",{className:"text-xs text-[#98A2B3]",children:new Date(e?.created_at).toLocaleTimeString([],{hour:"numeric",minute:"2-digit",hour12:!0})})]}),a.jsx("div",{className:"relative flex items-start justify-between",children:(0,a.jsxs)("div",{className:"gap-2",children:[a.jsx("small",{className:"text-sm font-bold text-neutral-700",children:e?.event_name}),a.jsx("small",{className:"text-sm text-neutral-500 mb-1",children:e?.message.split("\\n").map((e,s)=>{let t=e.match(/^(.*?):\s*(.*)$/);return a.jsx("p",{style:{whiteSpace:"pre-line",wordBreak:"break-word",overflowWrap:"break-word"},className:"mb-1",children:t?a.jsxs(a.Fragment,{children:[a.jsxs("strong",{children:[t[1],":"]})," \xa0\xa0 ",t[2]]}):e},s)})})]})})]})]});var f=t(56428),b=t(18830),j=t(54485),v=t(29225),N=t(87137),y=t(8592);let w=()=>{let{fetchMoreData:e,hasMore:s,loading:t}=o(),{state:w,dispatch:E}=(0,l.useContext)(c.R),{messages:C,user:A,isEdit:k,thread:F,notify:S,bookmarks:_,dataId:L}=w,Z=(0,x.p)(C),$=(0,n.useParams)().id,I=(0,n.usePathname)(),[D,z]=(0,l.useState)(!1),[R,T]=(0,l.useState)(null),O=(0,l.useRef)(null),G=(0,l.useRef)(!1),B=async()=>{await (0,d.Gl)(`/threads/channels/${$}?page=1&limit=1`)};(0,l.useEffect)(()=>{let e=O.current;if(!e)return;let s=()=>{let s=e.scrollTop>=0;s?(B(),G.current=!0):!s&&G.current&&(G.current=!1),z(!s)};return e.addEventListener("scroll",s),s(),()=>{e.removeEventListener("scroll",s)}},[]),(0,l.useEffect)(()=>{D||S?.user_id===A?.user_id||B()},[C]);let P=async e=>{await (0,d.an)(`/threads/${F?.thread_id}/channels/${$}`,{content:e}),E({type:i.a.IS_EDIT,payload:!1})};return((0,l.useEffect)(()=>{let e=localStorage.getItem("data-id");if(!e||!I.includes("/later"))return;let s=document.getElementById(`thread-${e}`);s&&(s.scrollIntoView({behavior:"auto",block:"center"}),s.classList.add("bg-yellow-100"),setTimeout(()=>{s.classList.remove("bg-yellow-100")},1500))},[L,t]),t)?null:(0,a.jsxs)("div",{id:"scrollableDivs",ref:O,style:{height:s||C?.length?"100vh":"",overflowY:"scroll",display:"flex",flexDirection:"column-reverse"},className:"w-full pb-40",children:[D&&w?.threadCount>0&&(0,a.jsxs)(j.C,{onClick:()=>{O.current&&(O.current.scrollTop=0),E({type:i.a.COUNT_CALLBACK,payload:!w?.countCallback})},className:"absolute bottom-40 z-20 mx-auto cursor-pointer -translate-x-[50%] left-1/2 px-3 py-1.5 flex gap-1 bg-primary-500 font-normal text-white text-[0.8125rem] border border-[E6EAEF]",children:[a.jsx(v.Z,{}),"Latest messages"]}),a.jsx(h.Z,{dataLength:C?.length||0,next:e,hasMore:s,loader:C?.length!==0&&a.jsx("h4",{className:"my-5 text-xs text-center",children:"Loading older threads..."}),style:{display:"flex",flexDirection:"column-reverse",overflowY:"visible"},scrollableTarget:"scrollableDivs",inverse:!0,children:Object.entries(Z)?.map(([e,s])=>a.jsxs(l.Fragment,{children:[s?.map((e,t)=>{let l=s[t+1],n=!l||l.user_id!==e.user_id,i=_?.some(s=>s.thread_id===e.thread_id);return a.jsx(r().Fragment,{children:e?.type==="thread"?a.jsx(g,{item:e}):a.jsx(a.Fragment,{children:k&&F?.thread_id===e?.thread_id?a.jsxs("div",{className:"flex mb-5 mt-2 z-10 px-5 py-3 bg-blue-50 w-full",children:[a.jsx("div",{className:"size-10 mb-2 mr-3",children:a.jsx(m.default,{src:e?.avatar_url?e?.avatar_url:e?.user_type=="user"||e?.user_type===""?u.Z?.user:u.Z?.bot,alt:"avatar",width:80,height:40,className:"rounded-[7px] border size-10"})}),a.jsx(b.Z,{subscription:w?.channelSubscription,sendMessage:P})]}):a.jsxs("div",{id:`thread-${e.thread_id}`,className:`${e.is_pinned?"bg-yellow-50":i?"bg-primary-50":"hover:bg-gray-50"} duration-500 ease-in-out`,children:[e?.is_pinned?a.jsxs("div",{className:"flex items-center gap-2 bg-yellow-50 pl-10 text-[13px] font-semibold text-blue-100 pt-2",children:[a.jsx(N.Z,{size:13,className:"text-[#667085] mt-[3px]"}),"Pinned by"," ",A?.email===e?.pinned_details?.email?"you":e?.pinned_details?.username]}):i?a.jsxs("div",{id:`thread-${e.thread_id}`,className:"flex items-center gap-2 pl-10 text-[13px] font-bold text-blue-100 pt-2",children:[a.jsx(y.$8n,{fontSize:13,className:"text-[#667085]"}),"Saved for Later"]}):null,a.jsx(f.Z,{item:e,shouldShowAvatar:n,setPopupId:T,popupId:R})]})})},t)}),a.jsxs("div",{className:"relative my-2",children:[a.jsx("div",{className:"absolute inset-0 flex items-center",children:a.jsx("div",{className:"w-full border-t border-dotted border-[#E6EAEF]"})}),a.jsx("div",{className:"relative flex justify-center",children:a.jsx("span",{className:"bg-white px-4 py-1 text-[13px] text-[#101828] border border-[#E6EAEF] rounded-[30px]",children:e})})]})]},e))}),!s&&a.jsx(p,{})]})}},34078:(e,s,t)=>{t.d(s,{Z:()=>x});var a=t(12902),l=t(22655),r=t(42451),n=t(8659),i=t(99075),c=t(31426),d=t(32437),o=t(11467);let x=()=>{let{state:e,dispatch:s}=(0,r.useContext)(d.R),[t,x]=(0,r.useState)(!1),h=(0,l.useParams)().id,m=async()=>{let t={archived:e?.channelDetails?.archived!==!0};x(!0);let a=await (0,o.an)(`/channels/${h}/archive`,t);(a?.status===200||a?.status===201)&&s({type:c.a.CHANNEL_CALLBACK,payload:!e?.channelCallback}),x(!1)};return(0,a.jsxs)("div",{className:"h-[170px] bg-neutral-100 border-t flex flex-col items-center justify-center ",children:[(0,a.jsxs)("h1",{className:"mb-2 text-xl font-semibold",children:["# ",e?.channelDetails?.name]}),(0,a.jsxs)("p",{className:"text-base mb-3",children:["You are viewing #",e?.channelDetails?.name," in an archived channel"]}),a.jsx(n.z,{onClick:m,className:"bg-blue-500 text-white px-5",children:t?(0,a.jsxs)("span",{className:"flex items-center gap-x-2",children:[a.jsx("span",{className:"animate-pulse",children:"Loading..."})," ",a.jsx(i.Z,{width:"20",height:"40"})]}):a.jsx("span",{children:"Unarchive channel"})})]})}},58256:(e,s,t)=>{t.d(s,{Z:()=>$});var a=t(12902),l=t(4602),r=t(48830),n=t(42451),i=t(55945),c=t(8659),d=t(81035),o=t(39336),x=t(62058),h=t(34099),m=t(61347),u=t(22655),p=t(11467),g=t(81163);let f=({isOpen:e,agents:s,showModal:t,setCallback:l,callback:r})=>{let i=(0,u.useRouter)(),f=(0,u.useParams)().id,[b,j]=(0,n.useState)(""),[v,N]=(0,n.useState)(s.filter(e=>e.is_active).map(e=>e.id)),y=async e=>{let s=localStorage.getItem("orgId")||"",t=!v.includes(e?.id);N(s=>t?[...s,e?.id]:s.filter(s=>s!==e?.id)),await (0,p.xo)(`/organisations/${s}/agents/${e?.id}/channels/${f}`,{status:!e?.is_active}),l(!r)},w=(0,g.y)(s,b);return e?(0,a.jsxs)("div",{className:"absolute top-full right-0 mt-2.5 w-[339px] bg-white rounded-[7px] shadow-lg border border-[#E6EAEF] z-20",children:[a.jsx("div",{className:"p-3 border-b border-[#E6EAEF] overflow-hidden",children:(0,a.jsxs)("div",{className:"flex items-center p-[10px] gap-2 border border-[#E6EAEF] rounded-[6px] h-10",children:[a.jsx(d.Z,{className:"w-5 h-5 text-[#667085]"}),a.jsx(x.I,{placeholder:"Find an agent",className:"w-full border-none p-0 h-full",value:b,onChange:e=>j(e.target.value)})]})}),0===s.length?a.jsx("div",{className:"py-5 text-[15px] px-3 text-center",children:"You have no activated agents"}):(0,a.jsxs)("div",{className:"pt-3 px-3 pb-[6px] max-h-[200px] overflow-y-auto",children:[a.jsx("h3",{className:"text-blue-500 font-semibold text-[13px] mb-2",children:"Activated Agents"}),w?.map(e=>a.jsxs("div",{className:"flex items-center justify-between py-[10px] hover:bg-gray-50 cursor-pointer",onClick:()=>y(e),children:[a.jsxs("div",{className:"flex items-center gap-3",children:[a.jsx("div",{className:"w-5 h-5 rounded-sm border border-[#E6EAEF] flex items-center justify-center relative bg-green-100",children:a.jsx(m.default,{src:e?.app_logo||h.Z?.bot,alt:e.app_name,width:20,height:20})}),a.jsx("p",{className:"text-[13px] font-semibold text-[#344054]",children:e.app_name})]}),a.jsxs("div",{className:`w-5 h-5 rounded-full border ${e.is_active?"border-[#8686F9] bg-[#8686F9]":"border-[#E6EAEF]"} flex items-center justify-center`,children:[e.is_active&&a.jsx(o.Z,{className:"w-3 h-3 text-white"})," "]})]},e.id))]}),(0,a.jsxs)("div",{className:"bg-[#F6F7F9] p-3 flex gap-10",children:[a.jsx(c.z,{onClick:()=>i.push("/client/agents/browse-agents"),variant:"outline",className:"w-full h-9",children:a.jsx("span",{children:"Browse Agents"})}),a.jsx(c.z,{onClick:t,variant:"default",className:"w-full bg-[#7141F8] text-white h-9",children:a.jsx("span",{children:"Add New Agent"})})]})]}):null};var b=t(36804),j=t(21912),v=t(80842),N=t(90488),y=t(7638),w=t(86874),E=t(29243),C=t(90803),A=t(99075);function k(){let[e,s]=(0,n.useState)(!1),[t,l]=(0,n.useState)("all"),[r,i]=(0,n.useState)(!1),[d,o]=(0,n.useState)(!1),[x,h]=(0,n.useState)(!1),[m,g]=(0,n.useState)(!1),f=(0,u.useParams)().id,j=async()=>{g(!0),await (0,p.xo)(`/channels/${f}/notification-preference`,{muted:x,at_mentions:"all"===t||"mentions"===t,at_channel:"all"===t||"channels"===t,device_type:"web"}),g(!1),s(!1)};return(0,a.jsxs)(y.Vq,{open:e,onOpenChange:s,children:[a.jsx(y.hg,{asChild:!0,children:a.jsx("div",{className:"px-4 py-[10px] text-[15px] text-[#101828] hover:bg-[#F1F1FE] flex items-center justify-between cursor-pointer",children:"Notification Settings"})}),(0,a.jsxs)(y.cZ,{className:"sm:max-w-md rounded-md px-6 py-5",children:[(0,a.jsxs)(y.fK,{className:"flex flex-row justify-between items-center",children:[a.jsx(y.$N,{className:"text-xl font-semibold text-gray-900",children:"Notifications"}),a.jsx("button",{onClick:()=>s(!1),children:a.jsx(C.Z,{className:"w-4 h-4 text-gray-500"})})]}),(0,a.jsxs)("div",{className:"mt-4 space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"text-sm font-medium text-gray-900 mb-2 block",children:"Send a notification for"}),(0,a.jsxs)(w.E,{value:t,onValueChange:e=>l(e),className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(w.m,{value:"all",id:"all"}),a.jsx("label",{htmlFor:"all",className:"text-sm text-gray-700",children:"All new messages"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(w.m,{value:"mentions",id:"mentions"}),a.jsx("label",{htmlFor:"mentions",className:"text-sm text-gray-700",children:"Mentions"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(w.m,{value:"channels",id:"channels"}),a.jsx("label",{htmlFor:"channels",className:"text-sm text-gray-700",children:"Channels"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-3 pt-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(E.X,{id:"mobile",checked:r,onCheckedChange:e=>i(!!e)}),a.jsx("label",{htmlFor:"mobile",className:"text-sm text-gray-700",children:"Use different settings for mobile devices"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(E.X,{id:"thread",checked:d,onCheckedChange:e=>o(!!e)}),a.jsx("label",{htmlFor:"thread",className:"text-sm text-gray-700",children:"Get notified about all thread replies in this channel"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx(E.X,{id:"mute",checked:x,onCheckedChange:e=>h(!!e)}),a.jsx("label",{htmlFor:"mute",className:"text-sm text-gray-700",children:"Mute channel"})]})]})]}),(0,a.jsxs)("p",{className:"text-[12px] text-gray-500 mt-4 leading-tight",children:["Note: You can set notification keywords and change your workspace-wide settings in your"," ",a.jsx(b.default,{href:"/client/settings/personal/notifications",className:"text-indigo-600 underline",children:"settings"}),"."]}),(0,a.jsxs)("div",{className:"flex justify-end gap-2 mt-6",children:[a.jsx(c.z,{variant:"ghost",className:"text-sm text-gray-700",onClick:()=>s(!1),children:"Cancel"}),(0,a.jsxs)(c.z,{className:"flex gap-1 items-center bg-blue-500 text-white hover:bg-blue-700 text-sm",disabled:m,onClick:j,children:["Save Changes",m&&a.jsx(A.Z,{})]})]})]})]})}var F=t(31426),S=t(32437);let _=({isOpen:e,onClose:s})=>{let[t,l]=(0,n.useState)({copy:!1}),r=(0,u.useRouter)(),i=(0,n.useRef)(null),{state:c,dispatch:d}=(0,n.useContext)(S.R),[o,x]=(0,n.useState)(!1),h=(0,u.useParams)(),m=h?.id;if((0,n.useEffect)(()=>{let e=e=>{e.target.closest('[role="dialog"]')||!i.current||i.current.contains(e.target)||s()};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[s]),!e)return null;let g=e=>{l(s=>({...s,[e]:!s[e]}))},f="px-4 py-[10px] text-[15px] text-[#101828] hover:bg-[#F1F1FE] flex items-center justify-between cursor-pointer",y="h-px bg-[#E6EAEF]",w=async()=>{x(!0);let e=await (0,p.xo)(`/channels/${m}/leave`,{});(e?.status===200||e?.status===201)&&(d({type:F.a.CALLBACK,payload:!c?.callback}),s()),x(!1)};return a.jsx("div",{ref:i,className:"absolute top-full right-0 w-[220px] mt-2.5 bg-white rounded-[7px] shadow-lg border border-[#E6EAEF] z-20",onClick:()=>d({type:F.a.ACTIVE_TAB,payload:"about"}),children:(0,a.jsxs)("div",{children:[a.jsx(v.Z,{className:(0,N.cn)(f,"w-full"),children:"Open channel details"}),a.jsx("div",{className:y}),a.jsx(k,{}),a.jsx("div",{className:f,onClick:()=>r.push("/workflows/new"),children:"Add a workflow"}),a.jsx("div",{className:y}),(0,a.jsxs)("div",{className:`${f} relative`,onClick:()=>g("copy"),onMouseEnter:()=>l({copy:!0}),onMouseLeave:()=>l({copy:!1}),children:["Copy",a.jsx(j.Z,{className:"w-4 h-4",color:"#343330"}),t.copy&&(0,a.jsxs)("div",{className:"absolute right-full top-0 w-[200px] bg-white rounded-[7px] shadow-lg border border-[#E6EAEF]",children:[a.jsx("div",{className:f,children:"Channel Link"}),a.jsx("div",{className:f,children:"Channel ID"})]})]}),a.jsx("div",{className:f,onClick:()=>r.push("/channel/search"),children:"Search in channel"}),a.jsx(b.default,{href:"/channel/new-window",target:"_blank",className:f,children:"Open in new window"}),a.jsx("div",{className:y}),(0,a.jsxs)("div",{className:`${f} text-[#B00E03]`,onClick:w,children:["Leave channel"," ",o&&a.jsx(A.Z,{color:"red",height:"15px",width:"15px"})]})]})})};var L=t(87419);let Z=({isOpen:e,workflows:s})=>{let t=(0,u.useRouter)(),l=(0,u.useParams)().id,[r,i]=(0,n.useState)(""),[g,f]=(0,n.useState)(s?.filter(e=>e.is_active).map(e=>e.id)),b=async e=>{let s=!g.includes(e?.id);f(t=>s?[...t,e?.id]:t.filter(s=>s!==e?.id));let t={workflow_id:e?.id,channel_id:l};await (0,p.xo)("/channel-workflows",t),L.Z.success("Workflow activated for this channel")},j=s?.filter(e=>e.name.toLowerCase().includes(r.toLowerCase()));return e?(0,a.jsxs)("div",{className:"absolute z-50 top-full right-0 mt-2.5 w-[339px] bg-white rounded-[7px] shadow-lg border border-[#E6EAEF]",children:[a.jsx("div",{className:"p-3 border-b border-[#E6EAEF] overflow-hidden",children:(0,a.jsxs)("div",{className:"flex items-center p-[10px] gap-2 border border-[#E6EAEF] rounded-[6px] h-10",children:[a.jsx(d.Z,{className:"w-5 h-5 text-[#667085]"}),a.jsx(x.I,{placeholder:"Find a workflow",className:"w-full border-none p-0 h-full",value:r,onChange:e=>i(e.target.value)})]})}),0===s.length?a.jsx("div",{className:"py-5 text-[15px] px-3 text-center",children:"You have no workflows"}):(0,a.jsxs)("div",{className:"pt-3 px-3 pb-[6px] max-h-[200px] overflow-y-auto",children:[a.jsx("h3",{className:"text-blue-500 font-semibold text-[13px] mb-2",children:"Workflows"}),j?.map(e=>{let s=g.includes(e.id);return a.jsxs("div",{className:"flex items-center justify-between py-[10px] hover:bg-gray-50 cursor-pointer",onClick:()=>b(e),children:[a.jsxs("div",{className:"flex items-center gap-3",children:[a.jsx("div",{className:"w-5 h-5 rounded-sm border border-[#E6EAEF] flex items-center justify-center relative bg-green-100",children:a.jsx(m.default,{src:h.Z?.blueBot,alt:e.name,width:20,height:20})}),a.jsx("p",{className:"text-[13px] font-semibold text-[#344054]",children:e.name})]}),a.jsx("div",{className:`w-5 h-5 rounded-full border ${s?"border-[#8686F9] bg-[#8686F9]":"border-[#E6EAEF]"} flex items-center justify-center`,children:s&&a.jsx(o.Z,{className:"w-3 h-3 text-white"})})]},e.id)})]}),(0,a.jsxs)("div",{className:"bg-[#F6F7F9] p-3 flex items-center justify-between gap-3",children:[a.jsx(c.z,{onClick:()=>t.push("/client/workflows"),variant:"outline",className:"flex-1 h-9",children:a.jsx("span",{children:"Browse Workflows"})}),a.jsx(c.z,{onClick:()=>t.push("/client/workflows/new"),variant:"default",className:"flex-1 bg-[#7141F8] text-white h-9",children:a.jsx("span",{children:"Add New"})})]})]}):null},$=()=>{let[e,s]=(0,n.useState)(!1),[t,d]=(0,n.useState)(!1),[o,h]=(0,n.useState)(!1),m=(0,n.useRef)(null),g=(0,n.useRef)(null),b=(0,n.useRef)(null),{state:j,dispatch:N}=(0,n.useContext)(S.R),{channelDetails:w,agentModal:E}=j,[C,k]=(0,n.useState)([]),[$,I]=(0,n.useState)(""),[D,z]=(0,n.useState)(!1),[R,T]=(0,n.useState)(!1),O=(0,u.useParams)().id;(0,n.useEffect)(()=>{let e=e=>{m.current&&!m.current.contains(e.target)&&s(!1)},t=e=>{g.current&&!g.current.contains(e.target)&&d(!1)};return document.addEventListener("mousedown",e),document.addEventListener("mousedown",t),()=>{document.removeEventListener("mousedown",e),document.removeEventListener("mousedown",t)}},[]),(0,n.useEffect)(()=>{(async()=>{let e=await (0,p.Gl)(`/organisations/${j?.orgId}/channels/${O}/agents`);(e?.status===200||e?.status===201)&&(k(e?.data?.data),N({type:F.a.CHANNEL_AGENTS,payload:e?.data?.data}))})()},[R,j?.orgId,O]),(0,n.useEffect)(()=>{let e=localStorage.getItem("orgId");(async()=>{let s=await (0,p.Gl)(`/channel-workflows/organisations/${e}/channels/${O}`);(s?.status===200||s?.status===201)&&N({type:F.a.CHANNEL_WORKFLOWS,payload:s?.data?.data})})()},[R,j?.orgId,O]);let G=async e=>{e.preventDefault();let s=localStorage.getItem("orgId")||"";z(!0);let t=await (0,p.xo)(`/organisations/${s}/agents`,{json_url:$});(t?.status===200||t?.status===201)&&(T(!R),N({type:F.a.AGENT_CALLBACK,payload:!j?.agentCallback}),L.Z.success(t?.data?.message),setTimeout(()=>{N({type:F.a.AGENT_MODAL,payload:!1})},1e3)),z(!1)};return(0,a.jsxs)("nav",{className:"flex items-center justify-between px-3 py-3 md:p-5 border-b border-[#E6EAEF]",children:[a.jsx(v.Z,{children:(0,a.jsxs)("h2",{className:"text-base lg:text-lg font-bold hover:text-blue-300",children:[w?.name?"#":""," ",w?.name]})}),!j?.channelloading&&j?.channelDetails?.access===!0&&!j?.channelDetails?.archived&&(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("div",{className:"relative hidden sm:flex",ref:g,children:[(0,a.jsxs)(c.z,{variant:"outline",className:"border-blue-50 h-9 hover:bg-primary-50",onClick:()=>d(!t),children:[a.jsx(l.Z,{className:"w-5 h-5",color:"#8686F9"}),a.jsx("span",{className:"ml-1 text-[13px] font-semibold text-blue-200",children:"Add a workflow"})]}),a.jsx(Z,{isOpen:t,workflows:j?.channelWorkflows})]}),(0,a.jsxs)("div",{className:"relative hidden sm:flex",ref:m,children:[(0,a.jsxs)(c.z,{variant:"outline",className:"border-blue-50 h-9 hover:bg-primary-50",onClick:()=>s(!e),children:[a.jsx(l.Z,{className:"w-5 h-5",color:"#8686F9"}),a.jsx("span",{className:"ml-1 text-[13px] font-semibold text-blue-200",children:"Add an Agent"})]}),a.jsx(f,{isOpen:e,agents:C,showModal:()=>{N({type:F.a.AGENT_MODAL,payload:!0}),s(!1)},setCallback:T,callback:R})]}),a.jsx(y.Vq,{open:E,onOpenChange:()=>N({type:F.a.AGENT_MODAL,payload:E}),children:(0,a.jsxs)(y.cZ,{className:"w-full max-w-md",children:[a.jsx(y.fK,{className:"mb-5",children:a.jsx(y.$N,{className:"text-blue-500",children:"Provide your Agent Json url"})}),(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[a.jsx("label",{className:"text-sm font-medium",children:"Json url"}),a.jsx(x.I,{value:$,onChange:e=>I(e.target.value),placeholder:"Enter json url"})]}),(0,a.jsxs)(y.cN,{className:"mt-4 flex justify-end gap-2",children:[a.jsx(c.z,{variant:"outline",onClick:()=>N({type:F.a.AGENT_MODAL,payload:!1}),children:"Cancel"}),(0,a.jsxs)(c.z,{onClick:G,disabled:!$,className:"bg-blue-500 gap-1 text-white px-8",children:["Save",D&&a.jsx(A.Z,{})]})]})]})}),a.jsx("div",{className:"w-px h-5 bg-[#E6EAEF] hidden lg:block"}),a.jsx(v.Z,{children:a.jsx("div",{onClick:()=>N({type:F.a.ACTIVE_TAB,payload:"people"}),className:"hidden lg:flex rounded-[5px] border border-[#E6EAEF] p-2 h-9 cursor-pointer hover:bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-center gap-1.5",children:[w?.users?.slice(0,3).map((e,s)=>a.jsx(i.qE,{className:`rounded-[5px] w-5 h-5 border border-[#E6EAEF] object-cover ${s>0?"-ml-2.5":""}`,children:a.jsx(i.F$,{src:e?.profile?.avatar_url||"/images/user.png",className:"object-cover"})},e.id)),w?.users?.length>3&&(0,a.jsxs)("span",{className:"text-[13px] font-semibold text-[#344054]",children:["+",w.users.length-3]})]})})}),(0,a.jsxs)("div",{className:"relative",ref:b,children:[a.jsx(c.z,{variant:"outline",className:`p-2 border-[#E6EAEF] h-9 ${o?"bg-[#F6F7F9]":""}`,onClick:()=>h(!o),children:a.jsx(r.Z,{className:"w-5 h-5",color:"#344054"})}),a.jsx(_,{isOpen:o,onClose:()=>h(!1)})]})]})]})}},61714:(e,s,t)=>{t.d(s,{Z:()=>x});var a=t(12902),l=t(22655),r=t(42451),n=t(8659),i=t(99075),c=t(31426),d=t(32437),o=t(11467);let x=()=>{let{state:e,dispatch:s}=(0,r.useContext)(d.R),[t,x]=(0,r.useState)(!1),h=(0,l.useParams)().id,m=async()=>{x(!0);let t=JSON.parse(localStorage.getItem("user")||"{}"),a=await (0,o.xo)(`/channels/${h}/join`,{username:t?.username});(a?.status===200||a?.status===200)&&s({type:c.a.CHANNEL_CALLBACK,payload:!e?.channelCallback}),x(!1)};return(0,a.jsxs)("div",{className:"h-[170px] bg-neutral-100 border-t flex flex-col items-center justify-center ",children:[(0,a.jsxs)("h1",{className:"mb-2 text-xl font-semibold",children:["# ",e?.channelDetails?.name]}),a.jsx("p",{className:"text-base mb-3",children:"You are not a member of this channel"}),a.jsx(n.z,{onClick:m,className:"bg-blue-500 text-white px-5",children:t?(0,a.jsxs)("span",{className:"flex items-center gap-x-2",children:[a.jsx("span",{className:"animate-pulse",children:"Joining..."})," ",a.jsx(i.Z,{width:"20",height:"40"})]}):a.jsx("span",{children:"Join channel"})})]})}},18830:(e,s,t)=>{t.d(s,{Z:()=>w});var a=t(12902),l=t(42451),r=t(29252),n=t(59017),i=t(57294),c=t(90305),d=t(40032),o=t(41833),x=t(56320),h=t(80340),m=t(19769),u=t(32437),p=t(89205),g=t(7638),f=t(62058),b=t(8659),j=t(83527),v=t(40035),N=t(31426),y=t(87489);let w=({subscription:e,sendMessage:s})=>{let{editor:t}=(0,m.Z)(e),{state:w,dispatch:E}=(0,l.useContext)(u.R),[C,A]=(0,l.useState)(!1),[k,F]=(0,l.useState)(""),[S,_]=(0,l.useState)(""),[L,Z]=(0,l.useState)(!1),[$,I]=(0,l.useState)(!0),D=async a=>{a.preventDefault();let l=t?.getHTML();l?.replace(/<[^>]+>/g,"").trim()&&(e?s(l):console.log("No connection detected"))};return(0,l.useEffect)(()=>{t&&(t?.commands.setContent(w?.thread?.message),t.commands.focus())},[t]),a.jsx(a.Fragment,{children:(0,a.jsxs)("div",{className:`bg-white border rounded border-[#E6EAEF] w-full ${w?.reply?"right-[520px]":"right-0"}`,children:[$&&(0,a.jsxs)("div",{className:"border-b border-[#E6EAEF] flex items-center gap-2 mb-2 bg-[#F9FAFB] pl-3 pr-4 py-[5px]",children:[a.jsx("button",{onClick:()=>t?.chain().focus().toggleBold().run(),className:`p-1.5 hover:bg-gray-100 rounded ${t?.isActive("bold")?"bg-gray-200 font-semibold text-black":""}`,children:a.jsx(r.Z,{size:18,color:t?.isActive("bold")?"#444444":"#CACACA"})}),a.jsx("button",{onClick:()=>t?.chain().focus().toggleItalic().run(),className:`p-1.5 hover:bg-gray-100 rounded ${t?.isActive("italic")?"bg-gray-200 font-semibold text-black":""}`,children:a.jsx(n.Z,{size:18,color:t?.isActive("italic")?"#444444":"#CACACA"})}),a.jsx("button",{onClick:()=>t?.chain().focus().toggleStrike().run(),className:`p-1.5 hover:bg-gray-100 rounded ${t?.isActive("strike")?"bg-gray-200 font-semibold text-black":""}`,children:a.jsx(i.Z,{size:18,color:t?.isActive("strike")?"#444444":"#CACACA"})}),a.jsx("div",{className:"w-px h-5 bg-[#E6EAEF]"}),(0,a.jsxs)(g.Vq,{open:C,onOpenChange:A,children:[a.jsx(g.hg,{asChild:!0,children:a.jsx("button",{onClick:()=>A(!0),className:`p-1.5 hover:bg-gray-100 rounded ${t?.isActive("link")?"bg-gray-200 font-semibold text-black":""}`,children:a.jsx(c.Z,{size:18,color:t?.isActive("link")?"#444444":"#CACACA"})})}),(0,a.jsxs)(g.cZ,{className:"w-full max-w-md",children:[a.jsx(g.fK,{children:a.jsx(g.$N,{className:"font-semibold",children:"Add link"})}),(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[a.jsx("label",{className:"text-sm font-medium",children:"Text"}),a.jsx(f.I,{value:k,onChange:e=>F(e.target.value),placeholder:"Enter link text"}),a.jsx("label",{className:"text-sm font-medium mt-2",children:"Link"}),a.jsx(f.I,{value:S,onChange:e=>_(e.target.value),placeholder:"Enter URL",type:"url"})]}),(0,a.jsxs)(g.cN,{className:"mt-4 flex justify-end gap-2",children:[a.jsx(b.z,{variant:"outline",onClick:()=>A(!1),children:"Cancel"}),a.jsx(b.z,{onClick:()=>{k&&S&&(t?.chain().focus().insertContent(`<a href="${S}" target="_blank" rel="noopener noreferrer">${k}</a>`).run(),A(!1),F(""),_(""))},disabled:!k||!S,className:"bg-blue-500 text-white px-10",children:"Save"})]})]})]}),a.jsx("button",{onClick:()=>t?.chain().focus().toggleOrderedList().run(),className:`p-1.5 hover:bg-gray-100 rounded ${t?.isActive("orderedList")?"bg-gray-200 font-semibold text-black":""}`,children:a.jsx(d.Z,{size:18,color:t?.isActive("orderedList")?"#444444":"#CACACA"})}),a.jsx("button",{onClick:()=>t?.chain().focus().toggleBulletList().run(),className:`p-1.5 hover:bg-gray-100 rounded ${t?.isActive("bulletList")?"bg-gray-200 font-semibold text-black":""}`,children:a.jsx(o.Z,{size:18,color:t?.isActive("bulletList")?"#444444":"#CACACA"})}),a.jsx("div",{className:"w-px h-5 bg-[#E6EAEF]"}),a.jsx("button",{onClick:()=>t?.chain().focus().toggleCode().run(),className:`p-1.5 hover:bg-gray-100 rounded ${t?.isActive("code")?"bg-gray-200 font-semibold text-black":""}`,children:a.jsx(x.Z,{size:18,color:t?.isActive("code")?"#444444":"#CACACA"})})]}),a.jsx("div",{className:"flex-1 relative px-3",children:a.jsx(p.kg,{editor:t,className:"py-2 rounded-md flex flex-row overflow-auto",onKeyDown:e=>{"Enter"===e.key&&(e.shiftKey?(e.preventDefault(),t?.commands.enter()):(e.preventDefault(),D(e)))}})}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 py-2 pl-3 pr-4",children:[a.jsx("button",{title:"show formatting",onClick:()=>I(e=>!e),className:"p-1.5 hover:bg-gray-100 rounded text-[#606060] underline",children:"Aa"}),a.jsx("div",{className:"relative",children:(0,a.jsxs)(y.J2,{open:L,onOpenChange:Z,children:[a.jsx(y.xo,{asChild:!0,children:a.jsx("button",{className:"p-1.5 hover:bg-gray-100 rounded",children:a.jsx(h.Z,{size:18,color:"#606060"})})}),a.jsx(y.yk,{className:"p-0 w-full max-w-xs",children:a.jsx(j.Z,{data:v,onEmojiSelect:e=>{t?(t.chain().focus().insertContent(e?.native).run(),Z(!1)):console.log("editor not available")}})})]})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 py-2 pl-3 pr-4",children:[a.jsx("button",{className:"bg-gray-100 rounded py-2 px-4 text-xs",onClick:()=>E({type:N.a.IS_EDIT,payload:!1}),children:"Cancel"}),a.jsx("button",{onClick:D,className:"bg-blue-500 rounded py-2 px-4 text-white text-xs",children:"Save"})]})]})]})})}},13031:(e,s,t)=>{t.d(s,{Z:()=>c});var a=t(22655),l=t(42451),r=t(31426),n=t(32437),i=t(11467);let c=()=>{let e=(0,a.useParams)().id,{state:s,dispatch:t}=(0,l.useContext)(n.R),c=localStorage.getItem("token")||"",[d,o]=(0,l.useState)(1),[x,h]=(0,l.useState)(!0),[m,u]=(0,l.useState)(!0),p=async(a=1)=>{try{let l=await (0,i.Gl)(`/threads/${s?.thread?.thread_id}/channels/${e}?page=${a}&limit=30`);if(l?.status===200||l?.status===201){let e=Array.isArray(l.data?.data)?l.data?.data:[];t({type:r.a.REPLIES,payload:{newThreads:e,newPage:a}}),a>1&&e.length>0?h(!0):h(e.length>=30)}u(!1)}catch(e){console.error("Error fetching threads:",e),h(!1)}finally{o(a)}};return(0,l.useEffect)(()=>{s?.thread?.thread_id&&c&&p(1).finally(()=>t({type:r.a.MESSAGE_LOADING,payload:!1}))},[s?.thread,c,t]),{fetchMoreData:()=>{x&&p(d+1)},hasMore:x,loading:m}}},11495:(e,s,t)=>{t.d(s,{Z:()=>d});var a=t(12902),l=t(22655),r=t(42451),n=t(31426),i=t(32437),c=t(11467);let d=()=>{let e=(0,l.useParams)().id,{state:s,dispatch:t}=(0,r.useContext)(i.R);return(0,r.useEffect)(()=>{e&&(async()=>{let s=await (0,c.Gl)(`/channels/${e}`);(s?.status===200||s?.status===201)&&t({type:n.a.CHANNEL_DETAILS,payload:s?.data?.data}),t({type:n.a.CHANNEL_LOADING,payload:!1})})()},[t,e,s?.channelCallback]),a.jsx(a.Fragment,{})}},54766:(e,s,t)=>{t.d(s,{Z:()=>i});var a=t(12902),l=t(42451),r=t(32437);t(2849),t(30333),t(31426);var n=t(22655);function i(){(0,n.useParams)().id;let{state:e,dispatch:s}=(0,l.useContext)(r.R);return process.env.NEXT_PUBLIC_CONNECT_URL,a.jsx("div",{})}},29243:(e,s,t)=>{t.d(s,{X:()=>c});var a=t(12902),l=t(42451),r=t(75634),n=t(39336),i=t(90488);let c=l.forwardRef(({className:e,...s},t)=>a.jsx(r.fC,{ref:t,className:(0,i.cn)("peer h-[15px] w-[15px] shrink-0 rounded-sm border border-[#adadeaf] ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary-500 data-[state=checked]:text-primary-500",e),...s,children:a.jsx(r.z$,{className:(0,i.cn)("flex items-center justify-center text-current"),children:a.jsx(n.Z,{className:"h-[9px] w-[9px] text-white",strokeWidth:5})})}));c.displayName=r.fC.displayName},86874:(e,s,t)=>{t.d(s,{E:()=>c,m:()=>d});var a=t(12902),l=t(42451),r=t(44236),n=t(30088),i=t(90488);let c=l.forwardRef(({className:e,...s},t)=>a.jsx(r.fC,{className:(0,i.cn)("grid gap-2",e),...s,ref:t}));c.displayName=r.fC.displayName;let d=l.forwardRef(({className:e,...s},t)=>a.jsx(r.ck,{ref:t,className:(0,i.cn)("relative aspect-square h-4 w-4 rounded-full border border-primary-500 text-primary-500 ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...s,children:a.jsx(r.z$,{className:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2",children:a.jsx(n.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})}));d.displayName=r.ck.displayName},81163:(e,s,t)=>{t.d(s,{y:()=>a});let a=(e,s)=>e?.filter(e=>Object.values(e).join(" ").toLowerCase().match(s))}};