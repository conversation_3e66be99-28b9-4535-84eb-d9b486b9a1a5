"use strict";exports.id=9528,exports.ids=[9528],exports.modules={3720:(e,t,s)=>{s.d(t,{Z:()=>_});var a=s(12902),r=s(42451),l=s(67607);let i=({file:e})=>a.jsx("div",{className:"border rounded-lg shadow-sm bg-white min-h-[200px] w-[200px] overflow-hidden",children:(0,a.jsxs)("a",{href:e.file_link,target:"_blank",rel:"noopener noreferrer",children:[a.jsx("div",{className:"flex items-center justify-between p-3 px-2 border-b bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 w-full min-w-0",children:[a.jsx(l.Z,{size:20,className:"text-red-500 flex-shrink-0"}),a.jsx("div",{className:"text-xs font-medium text-gray-900 truncate min-w-0",children:e.file_name})]})}),a.jsx("div",{className:"flex justify-center items-center mt-10",children:a.jsx(l.Z,{size:50,className:"text-red-500"})})]})});var n=s(20129),o=s(67463),d=s(70875),c=s(89416),x=s.n(c);s(8151),s(70410),s(11801),s(51156),s(23804);var m=s(90803),p=s(87787),h=s(645),u=s(4602),g=s(45368),y=s.n(g),j=s(61347),f=s(34099);let b=({onClose:e,item:t,image:s})=>{let[l,i]=(0,r.useState)(1),[n,o]=(0,r.useState)(0);return(0,r.useEffect)(()=>{let t=t=>{"Escape"===t.key&&e()};return document.addEventListener("keydown",t),()=>document.removeEventListener("keydown",t)},[e]),(0,a.jsxs)("div",{className:"fixed inset-0 z-50 bg-black bg-opacity-90 flex flex-col justify-between p-4",children:[(0,a.jsxs)("div",{className:"relative flex justify-between items-start text-white",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(j.default,{src:s?.file_link||f.Z?.user,width:80,height:80,alt:"Image thumbnail",unoptimized:!0,className:"h-10 w-10 rounded border object-cover"}),(0,a.jsxs)("div",{className:"overflow-hidden",children:[a.jsx("p",{className:"font-medium",children:t?.username}),(0,a.jsxs)("p",{className:"flex flex-wrap text-sm text-gray-300",children:[y()(t.created_at).startOf("minute").fromNow()," in #",t?.channel_name," – ",s?.file_name]})]})]}),a.jsx("button",{onClick:e,className:"absolute right-5 sm:top-5 text-white hover:text-gray-300",children:a.jsx(m.Z,{size:24})})]}),a.jsx("div",{className:"flex-grow flex items-center justify-center overflow-hidden",children:a.jsx("img",{src:s?.file_link,alt:s?.file_name||"full image",style:{transform:`scale(${l}) rotate(${n}deg)`,transition:"transform 0.1s ease-out"},className:"max-h-[90vh] max-w-full object-contain"})}),a.jsx("div",{className:"flex justify-between items-center text-white",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx("button",{onClick:()=>{i(1),o(0)},className:"px-2 py-1 border rounded hover:bg-white/10",title:"Reset Zoom & Rotation",children:a.jsx(p.Z,{size:18})}),a.jsx("button",{onClick:()=>{i(e=>Math.max(e-.25,.5))},className:"px-2 py-1 border rounded hover:bg-white/10",title:"Zoom Out",children:a.jsx(h.Z,{size:18})}),a.jsx("div",{className:"w-24 h-1 bg-gray-600 rounded-full relative overflow-hidden",children:a.jsx("div",{className:"absolute h-full bg-white rounded-full",style:{width:`${(l-.5)/2.5*100}%`,left:"0%"}})}),a.jsx("button",{onClick:()=>{i(e=>Math.min(e+.25,3))},className:"px-2 py-1 border rounded hover:bg-white/10",title:"Zoom In",children:a.jsx(u.Z,{size:18})})]})})]})};var v=s(90141),w=s(65497);let N=({mediaItem:e,setIsOpen:t,setImage:s})=>{let[l,i]=(0,r.useState)(!1),n=async t=>{t.stopPropagation();try{let t=await fetch(e.file_link),s=await t.blob(),a=window.URL.createObjectURL(s),r=document.createElement("a");r.href=a,r.download=e.file_name,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(a)}catch(e){console.error("Download failed:",e)}};return(0,a.jsxs)("div",{className:"relative rounded-md overflow-hidden w-full md:w-[350px] h-[300px]",onMouseEnter:()=>i(!0),onMouseLeave:()=>i(!1),children:[a.jsx("img",{src:e.file_link,alt:e.file_name,className:"w-full md:w-[350px] h-[300px] rounded-md object-cover border cursor-pointer",onClick:()=>{t(!0),s(e)}}),l&&(0,a.jsxs)("a",{href:"#",onClick:n,className:"flex items-center gap-1 absolute top-2 right-2 bg-white py-1 px-2 rounded-lg cursor-pointer z-10",title:"Download Image",children:[a.jsx("div",{className:"hover:bg-gray-200 rounded-md p-1",children:a.jsx(v.Z,{size:18})}),a.jsx("div",{className:"hover:bg-gray-200 rounded-md p-1",children:a.jsx(w.Z,{size:18})})]})]})},_=({item:e})=>{let[t,s]=(0,r.useState)(!1),[l,c]=(0,r.useState)(null),m=e.message.replace(/\n{2,}/g,"\n\n").replace(/^\n+|\n+$/g,"");return(0,r.useEffect)(()=>{void 0!==x()&&x().highlightAll()},[m]),(0,a.jsxs)("div",{children:[a.jsx("div",{style:{whiteSpace:"pre-line",wordBreak:"break-word",overflowWrap:"break-word"},className:"text-[#344054] text-[13px] lg:text-[15px] font-[400] whitespace-pre-wrap break-words custom-message",children:a.jsx(n.UG,{remarkPlugins:[o.Z],rehypePlugins:[d.Z],components:{a:({...e})=>a.jsx("a",{...e,style:{textDecoration:"underline",color:"blue"},target:"_blank",rel:"noopener noreferrer"}),table:e=>a.jsx("table",{style:{width:"100%"},...e}),th:e=>a.jsx("th",{style:{border:"1px solid #ccc",padding:"8px"},...e}),td:e=>a.jsx("td",{style:{border:"1px solid #ccc",padding:"8px"},...e}),p:t=>a.jsx("p",{style:{color:e?.type==="system"?"#aaa":""},...t}),code:({className:e,children:t,...s})=>{let r=/language-(\w+)/.exec(e||""),l={className:e,...s,style:{whiteSpace:"pre-wrap",wordBreak:"break-word",overflowX:"hidden"}},i={className:e,...s,style:{wordBreak:"break-word",whiteSpace:"pre-wrap"}};return r?a.jsx("pre",{...l,children:a.jsx("code",{...i,children:String(t).replace(/\n$/,"")})}):a.jsx("code",{className:e,...s,children:t})}},children:m})}),e.media&&e.media.length>0&&a.jsx("div",{className:"mt-2 flex items-start flex-wrap gap-4",children:e?.media?.map(e=>e.mime_type.includes("application")?a.jsx(i,{file:e},e.id):a.jsx(N,{mediaItem:e,setIsOpen:s,setImage:c},e.id))}),t&&a.jsx(b,{item:e,image:l,onClose:()=>s(!1)})]})}},56428:(e,t,s)=>{s.d(t,{Z:()=>V});var a=s(12902),r=s(42451),l=s(97737),i=s(3124),n=s(16034),o=s(20995),d=s(70408),c=s(3720),x=s(32437),m=s(31426),p=s(22655),h=s(61347),u=s(98063),g=s(34099);function y({users:e,totalReplies:t,lastReplyTime:s,handleReply:r}){let l=e?.slice(0,4),i=e?.length-l?.length;return(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-sm text-gray-600 mt-2",children:[(0,a.jsxs)("div",{className:"flex space-x-[2px]",children:[l?.map((e,t)=>a.jsx("div",{className:"w-6 h-6 size-5 rounded-md border-2 border-white overflow-hidden",children:a.jsx(h.default,{src:e.avatar_url||g.Z?.user,alt:`User ${e.id}`,width:24,height:24,className:"size-5 object-cover"})},t)),i>0&&(0,a.jsxs)("div",{className:"w-6 h-6 rounded-full bg-gray-300 border-2 border-white text-xs flex items-center justify-center text-gray-700 font-medium",children:["+",i]})]}),l?.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("span",{onClick:r,className:"text-blue-600 hover:underline cursor-pointer ml-2",children:[t," replies"]}),(0,a.jsxs)("span",{className:"text-gray-500 ml-1",children:["Last reply ",(0,u.WU)(s)]})]})]})}var j=s(87489),f=s(83527),b=s(40035),v=s(6416),w=s(48830),N=s(62123),_=s(84476),C=s(56502),k=s(87137),L=s(67323),Z=s(19016),E=s(55280),S=s(13182),z=s(7638),A=s(8659),R=s(90803),M=s(45368),T=s.n(M),P=s(11467),I=s(99075);let O=({open:e,setOpen:t})=>{let{state:s}=(0,r.useContext)(x.R),{thread:l}=s,i=(0,p.useParams)().id,[n,o]=(0,r.useState)(!1),d=async()=>{o(!0);let e=await (0,P.jx)(`/threads/${l?.thread_id}/channels/${i}`);(e?.status===200||e?.status===201)&&t(!1),o(!1)};return a.jsx(z.Vq,{open:e,onOpenChange:t,children:(0,a.jsxs)(z.cZ,{className:"sm:max-w-[550px] rounded-lg p-0 overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-b border-gray-200",children:[a.jsx(z.$N,{className:"text-lg font-semibold",children:"Delete message"}),a.jsx("button",{onClick:()=>t(!1),className:"text-gray-500 hover:text-gray-700 transition","aria-label":"Close",children:a.jsx(R.Z,{className:"w-5 h-5"})})]}),a.jsx("div",{className:"px-6 pt-4 text-sm text-gray-700",children:"Are you sure you want to delete this message? This cannot be undone."}),a.jsx("div",{className:"px-6 py-4 max-h-[400px] overflow-auto",children:(0,a.jsxs)("div",{className:"border border-gray-200 rounded p-3 flex gap-3 items-start bg-white",children:[a.jsx("div",{className:"w-8 h-8 bg-green-100 rounded flex items-center justify-center text-white text-sm font-bold",children:a.jsx(h.default,{src:l?.avatar_url?l?.avatar_url:l?.user_type=="user"||l?.user_type===""?g.Z?.user:g.Z?.bot,alt:"avatar",width:32,height:32,className:"rounded"})}),(0,a.jsxs)("div",{className:"flex flex-col gap-[2px] text-sm w-full",children:[(0,a.jsxs)("div",{className:"flex gap-2 items-baseline",children:[a.jsx("span",{className:"font-bold text-gray-800",children:l?.username}),a.jsx("span",{className:"text-xs text-gray-500",children:T()(l?.created_at).calendar()})]}),a.jsx(c.Z,{item:l})]})]})}),(0,a.jsxs)(z.cN,{className:"px-6 pb-4 pt-2",children:[a.jsx(A.z,{variant:"outline",className:"rounded-md text-sm px-4 py-2 border border-gray-300",onClick:()=>t(!1),children:"Cancel"}),(0,a.jsxs)(A.z,{className:"bg-rose-600 hover:bg-rose-700 text-white text-sm px-4 py-2 rounded-md shadow-sm",onClick:d,children:["Delete ",n&&a.jsx(I.Z,{})]})]})]})})},D=({open:e,setOpen:t})=>{let{state:s}=(0,r.useContext)(x.R),{thread:l}=s,i=(0,p.useParams)().id,[n,o]=(0,r.useState)(!1),d=async()=>{o(!0),await (0,P.i1)(`/channels/pin/${i}/thread/${l?.thread_id}`),t(!1),o(!1)};return a.jsx(z.Vq,{open:e,onOpenChange:t,children:(0,a.jsxs)(z.cZ,{className:"sm:max-w-[550px] rounded-lg p-0 overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-b border-gray-200",children:[a.jsx(z.$N,{className:"text-lg font-semibold",children:"Remove pinned item"}),a.jsx("button",{onClick:()=>t(!1),className:"text-gray-500 hover:text-gray-700 transition","aria-label":"Close",children:a.jsx(R.Z,{className:"w-5 h-5"})})]}),a.jsx("div",{className:"px-6 pt-4 text-sm text-gray-700",children:"Are you sure you want to remove this pinned item."}),a.jsx("div",{className:"px-6 py-4 max-h-[400px] overflow-auto",children:(0,a.jsxs)("div",{className:"border border-gray-200 rounded p-3 flex gap-3 items-start bg-white",children:[a.jsx("div",{className:"w-8 h-8 bg-green-100 rounded flex items-center justify-center text-white text-sm font-bold",children:a.jsx(h.default,{src:l?.avatar_url?l?.avatar_url:l?.user_type=="user"||l?.user_type===""?g.Z?.user:g.Z?.bot,alt:"avatar",width:32,height:32,className:"rounded"})}),(0,a.jsxs)("div",{className:"flex flex-col gap-[2px] text-sm w-full",children:[(0,a.jsxs)("div",{className:"flex gap-2 items-baseline",children:[a.jsx("span",{className:"font-bold text-gray-800",children:l?.username}),a.jsx("span",{className:"text-xs text-gray-500",children:T()(l?.created_at).calendar()})]}),a.jsx(c.Z,{item:l})]})]})}),(0,a.jsxs)(z.cN,{className:"px-6 pb-4 pt-2",children:[a.jsx(A.z,{variant:"outline",className:"rounded-md text-sm px-4 py-2 border border-gray-300",onClick:()=>t(!1),children:"Cancel"}),(0,a.jsxs)(A.z,{className:"bg-rose-600 hover:bg-rose-700 text-white text-sm px-4 py-2 rounded-md shadow-sm",onClick:d,children:["Remove pinned item ",n&&a.jsx(I.Z,{})]})]})]})})},$=({item:e})=>{let[t,s]=(0,r.useState)(!1),[l,i]=(0,r.useState)(!1),{state:n,dispatch:o}=(0,r.useContext)(x.R),{user:c}=n,h=(0,p.useParams)().id,[u,g]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=document.body;return t?e.classList.add("overflow-hidden"):e.classList.remove("overflow-hidden"),()=>{e.classList.remove("overflow-hidden")}},[t]);let y=async()=>{s(!1);let t={thread_id:e.thread_id};await (0,P.Q_)(`/channels/pin/${h}/thread`,t)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(j.J2,{open:t,onOpenChange:s,children:[a.jsx(j.xo,{asChild:!0,children:a.jsx("button",{onClick:e=>e.stopPropagation(),className:"py-[7px] px-[10px] hover:bg-gray-200 rounded","aria-label":"More options",children:a.jsx(w.Z,{size:18,className:"text-[#667085]"})})}),a.jsx(j.yk,{className:"max-width-[350px] p-0 bg-white border border-gray-200 rounded-md shadow-lg",align:"end",children:(0,a.jsxs)(v.E.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},transition:{duration:.1},className:"popover-content",children:[a.jsx("div",{className:"group flex items-center justify-between px-4 py-1 my-3 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(N.Z,{size:16}),a.jsx("span",{children:"Turn off notifications for replies"})]})}),a.jsx("hr",{}),(0,a.jsxs)("div",{className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(d.Z,{size:16}),a.jsx("span",{children:"Mark unread"})]}),a.jsx("span",{className:"ml-4 text-xs text-gray-400 group-hover:text-white",children:"U"})]}),a.jsx("hr",{}),(0,a.jsxs)("div",{className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(_.Z,{size:16}),a.jsx("span",{children:"Remind me about this"})]}),a.jsx("span",{className:"ml-4 text-xs text-gray-400 group-hover:text-white",children:">"})]}),a.jsx("hr",{}),(0,a.jsxs)("div",{className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(C.Z,{size:16}),a.jsx("span",{children:"Copy link"})]}),a.jsx("span",{className:"ml-4 text-xs text-gray-400 group-hover:text-white",children:"L"})]}),a.jsx("hr",{}),e.is_pinned?(0,a.jsxs)("div",{onClick:t=>{t.stopPropagation(),o({type:m.a.THREAD,payload:e}),g(!0),s(!1)},className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(k.Z,{size:16}),a.jsx("span",{children:"Un-pin from channel"})]}),a.jsx("span",{className:"ml-4 text-xs text-gray-400 group-hover:text-white",children:"P"})]}):(0,a.jsxs)("div",{onClick:y,className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(k.Z,{size:16}),a.jsx("span",{children:"Pin to channel"})]}),a.jsx("span",{className:"ml-4 text-xs text-gray-400 group-hover:text-white",children:"P"})]}),a.jsx("hr",{}),a.jsx("div",{className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(L.Z,{size:16}),a.jsx("span",{children:"Start a huddle in thread..."})]})}),a.jsx("hr",{}),c?.user_id===e?.user_id&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{onClick:()=>{o({type:m.a.THREAD,payload:e}),o({type:m.a.IS_EDIT,payload:!0})},className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(Z.Z,{size:16}),a.jsx("span",{children:"Edit message"})]}),a.jsx("span",{className:"ml-4 text-xs text-gray-400 group-hover:text-white",children:"E"})]}),a.jsx("hr",{}),(0,a.jsxs)("div",{onClick:()=>{o({type:m.a.THREAD,payload:e}),i(!0)},className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-red-500 hover:bg-red-500 hover:text-white cursor-pointer",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(E.Z,{size:16}),a.jsx("span",{children:"Delete message..."})]}),a.jsx("span",{className:"ml-4 text-xs group-hover:text-white",children:"delete"})]}),a.jsx("hr",{})]}),a.jsx("div",{className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(S.Z,{size:16}),a.jsx("span",{children:"More message shortcuts..."})]})})]})})]}),t&&a.jsx("div",{className:"fixed inset-0 bg-black/1 z-50",onClick:()=>s(!1),"aria-hidden":"true"}),a.jsx(O,{open:l,setOpen:i}),a.jsx(D,{open:u,setOpen:g})]})},q=({item:e})=>a.jsx($,{item:e});var U=s(65099),F=s(28854);function H({item:e,handleOpen:t}){let[s,l]=(0,r.useState)(!1),i=(0,r.useRef)(null),n=(0,r.useRef)(null),{state:o,dispatch:d}=(0,r.useContext)(x.R),{user:c}=o,u=(0,p.useRouter)(),y=()=>{n.current&&clearTimeout(n.current),i.current=setTimeout(()=>{l(!0)},300)},f=()=>{i.current&&clearTimeout(i.current),n.current=setTimeout(()=>{l(!1)},200)},b=async()=>{localStorage.setItem("channelName",e?.username);let t=localStorage.getItem("orgId")||"",s={chat_type:"user",participant_id:e?.user_id},a=await (0,P.xo)(`/organisations/${t}/dms`,s);(a?.status===200||a?.status===201)&&u.push(`/client/home/<USER>/${a?.data?.data?.channel_id}/${a?.data?.data?.participant_id}/dm`)};return(0,a.jsxs)(U.fC,{open:s,onOpenChange:l,children:[a.jsx(j.xo,{asChild:!0,children:a.jsx("div",{onMouseEnter:y,onMouseLeave:f,className:"hidden lg:flex cursor-pointer size-9 mb-2 overflow-hidden",onClick:t,children:a.jsx(h.default,{src:e?.avatar_url?e?.avatar_url:e?.user_type=="user"||e?.user_type===""?g.Z?.user:g.Z?.bot,alt:"avatar",width:40,height:40,className:"rounded-[7px] border size-9 object-cover"})})}),(0,a.jsxs)(j.yk,{onMouseEnter:y,onMouseLeave:f,sideOffset:8,side:"top",className:"z-50 w-auto rounded-md border border-gray-200 bg-white shadow-lg p-4",align:"start",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[a.jsx(h.default,{src:e?.avatar_url?e?.avatar_url:e?.user_type=="user"||e?.user_type===""?g.Z?.user:g.Z?.bot,alt:"avatar",width:80,height:80,className:"rounded-[7px] border size-20 object-cover"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"font-semibold text-[15px] text-black",children:[e?.full_name?.trim()||e?.username," ",e?.user_id===c?.user_id&&a.jsx("span",{className:"text-gray-500",children:"(you)"})]}),e?.user_id===c?.user_id?a.jsx("p",{className:"text-sm text-gray-500 mt-0.5",children:c.title}):a.jsx("p",{className:"text-sm text-gray-500 mt-0.5",children:e.email})]})]}),(0,a.jsxs)("div",{className:"flex items-center text-[15px] text-gray-600 mt-4",children:[a.jsx(_.Z,{className:"w-4 h-4 mr-2"}),T()(e?.created_at).format("LT")," local time"]}),e?.user_id===c?.user_id?a.jsx("button",{onClick:()=>d({type:m.a.STATUS,payload:!0}),className:"mt-3 w-full border text-sm font-medium border-gray-300 rounded-md py-1.5 hover:bg-gray-50 transition",children:"Set a status"}):(0,a.jsxs)("button",{onClick:b,className:"flex items-center justify-center gap-1 mt-3 w-full border text-sm font-medium border-gray-300 rounded-md py-1.5 hover:bg-gray-50 transition",children:[a.jsx(F.mT,{}),"Message"]})]})]})}var B=s(94679),J=s(4865);let V=({item:e,shouldShowAvatar:t,setPopupId:s,popupId:u})=>{let{state:v,dispatch:w}=(0,r.useContext)(x.R),{bookmarks:N,user:_}=v,C=(0,p.usePathname)(),[k,L]=(0,r.useState)(!1),[Z,E]=(0,r.useState)(!1),S=(0,p.useParams)().id,z=N?.some(t=>t.thread_id===e.thread_id),[A,R]=(0,r.useState)([]),M=()=>{w({type:m.a.THREAD,payload:e}),w({type:m.a.REPLY,payload:!0})},T=async t=>{L(!1);let s={thread_id:e?.thread_id,type:"thread",reaction:t.native};await (0,P.xo)(`/reactions/${S}`,s),L(!1),E(!1)},I=async t=>{L(!1);let s={thread_id:e?.thread_id,type:"thread",reaction:t};await (0,P.xo)(`/reactions/${S}`,s)},O=()=>{w({type:m.a.USER_DATA,payload:e}),w({type:m.a.HOVER_PROFILE,payload:!0})},D=async()=>{let t=[...N,{id:e.id,thread_id:e.thread_id}];w({type:m.a.BOOKMARKS,payload:t});let s=localStorage.getItem("orgId")||"",a={channels_id:e?.channels_id,thread_id:e.thread_id,type:"message"};await (0,P.Q_)(`/organisations/${s}/thread/save`,a)},$=async()=>{let t=N.filter(t=>t.thread_id!==e.thread_id);w({type:m.a.BOOKMARKS,payload:t});let s=localStorage.getItem("orgId")||"";await (0,P.i1)(`/organisations/${s}/saved/message/${e.thread_id}`)},U=async t=>{let s=await (0,P.Gl)(`/reactions/${t}/thread/${e?.thread_id}`);(s?.status===200||s?.status===201)&&R(s?.data?.data?.usernames)};return(0,a.jsxs)("div",{className:`relative group py-1 transition-colors flex items-start px-5 gap-2
      `,children:[a.jsx("div",{className:"w-flex items-center justify-center",children:t?(0,a.jsxs)(a.Fragment,{children:[a.jsx(H,{item:e,handleOpen:O}),a.jsx("div",{className:"flex lg:hidden cursor-pointer size-9 mb-2 overflow-hidden",onClick:O,children:a.jsx(h.default,{src:e?.avatar_url?e?.avatar_url:e?.user_type=="user"||e?.user_type===""?g.Z?.user:g.Z?.bot,alt:"avatar",width:40,height:40,className:"rounded-[7px] border size-9 object-cover"})})]}):a.jsx("span",{className:"block text-xs w-[36px] text-[#98A2B3] mt-1 opacity-0 group-hover:opacity-100 transition-opacity",children:new Date(e?.created_at).toLocaleTimeString([],{hour:"numeric",minute:"2-digit",hour12:!0}).replace(/\s?(am|pm)/i,"")})}),(0,a.jsxs)("div",{children:[t&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx("span",{className:"hover font-bold text-[15px] text-[#1D2939] cursor-pointer",onClick:O,children:e?.username||e?.email}),a.jsx("span",{className:"text-xs text-[#98A2B3] mt-[1px]",children:new Date(e?.created_at).toLocaleTimeString([],{hour:"numeric",minute:"2-digit",hour12:!0}).replace(/\s?(am|pm)/i,"")})]}),a.jsx("div",{className:"relative flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(c.Z,{item:e}),a.jsx("div",{className:"text-[9px] text-neutral-500 mt-[2px]",children:e?.edited?"(edited)":""})]})}),(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-2 rounded-md mt-1",children:[e?.reactions?.map((e,t)=>{let s=_?.username,r=(A||[]).map(e=>e===s?"you":e),l="";if(0===r.length)l=" ";else if(1===r.length)l=r[0];else{let e=r[r.length-1],t=r.slice(0,-1).join(", ");l=`${t} and ${e}`}return a.jsx(B.pn,{children:a.jsxs(B.u,{children:[a.jsx(B.aJ,{asChild:!0,children:a.jsxs("div",{onMouseEnter:()=>U(e.reaction_id),onClick:t=>{I(e.reaction),t.stopPropagation()},className:"bg-primary-50 text-[13px] cursor-pointer text-blue-100 border border-blue-400 flex items-center justify-center h-[27px] py-1 px-3 rounded-2xl",children:[e?.reaction," ",e?.reaction_count]})}),a.jsxs(B._v,{className:"bg-black text-white p-2 rounded-md text-sm",children:[a.jsx(J.Ce,{className:"fill-black"}),a.jsx("div",{className:"text-5xl mx-auto text-center bg-white rounded-lg flex items-center justify-center p-2 w-[70px] mb-2",children:e.reaction}),l&&a.jsxs("span",{children:[l," reacted with ",e?.reaction]})]})]})},t)}),(0,a.jsxs)(j.J2,{open:Z,onOpenChange:E,children:[a.jsx(B.pn,{delayDuration:0,children:(0,a.jsxs)(B.u,{children:[a.jsx(B.aJ,{asChild:!0,children:a.jsx(j.xo,{asChild:!0,children:e?.reactions?.length>0&&a.jsx("div",{className:"bg-primary-50 text-[13px] cursor-pointer text-blue-100 h-[27px] flex items-center justify-center py-1 px-3 rounded-full border hover:border-blue-400",onClick:e=>e.stopPropagation(),children:a.jsx(l.Z,{size:16})})})}),(0,a.jsxs)(B._v,{className:"bg-black text-white p-2 rounded-md text-sm",children:[a.jsx(J.Ce,{className:"fill-black"}),a.jsx("span",{children:"Add reaction..."})]})]})}),a.jsx(j.yk,{className:"p-0 w-full max-w-xs",align:"end",children:a.jsx(f.Z,{data:b,onEmojiSelect:T})})]})]}),e?.message_count>0&&a.jsx(y,{users:e?.messages,totalReplies:e?.message_count,lastReplyTime:e.last_reply||e?.created_at,handleReply:M}),e?.type!=="system"&&(0,a.jsxs)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity hidden lg:flex items-center ml-4 absolute right-5 -top-6 z-10 bg-white shadow-md rounded-[8px] border border-[#E6EAEF] p-[2px]",children:[!C?.includes("/agents")&&(0,a.jsxs)(j.J2,{open:k,onOpenChange:L,children:[a.jsx(j.xo,{asChild:!0,children:a.jsx("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:a.jsx(l.Z,{size:18,className:"text-[#667085]"})})}),a.jsx(j.yk,{className:"p-0 w-full max-w-xs",align:"end",children:a.jsx(f.Z,{data:b,onEmojiSelect:T})})]}),!C?.includes("/agents")&&a.jsx("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",onClick:M,children:a.jsx(i.Z,{size:18,className:"text-[#667085]"})}),a.jsx("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:a.jsx(n.Z,{size:18,className:"text-[#667085]"})}),z?a.jsx("button",{onClick:$,className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:a.jsx(o.Z,{size:18,className:"text-primary-500"})}):a.jsx("button",{onClick:D,className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:a.jsx(d.Z,{size:18,className:"text-[#667085]"})}),a.jsx(q,{item:e})]}),e?.type!=="system"&&u===e.thread_id&&(0,a.jsxs)("div",{onClick:()=>{s(t=>t===e.thread_id?null:e.thread_id)},className:"flex lg:hidden items-center ml-4 absolute right-5 -top-6 z-10 bg-white shadow-md rounded-[8px] border border-[#E6EAEF] p-[2px]",children:[!C?.includes("/agents")&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(j.J2,{open:k,onOpenChange:L,children:[a.jsx(j.xo,{asChild:!0,children:a.jsx("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:a.jsx(l.Z,{size:18,className:"text-[#667085]"})})}),a.jsx(j.yk,{className:"p-0 w-full max-w-xs",align:"end",children:a.jsx(f.Z,{data:b,onEmojiSelect:T})})]}),a.jsx("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",onClick:M,children:a.jsx(i.Z,{size:18,className:"text-[#667085]"})})]}),a.jsx("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:a.jsx(n.Z,{size:18,className:"text-[#667085]"})}),z?a.jsx("button",{onClick:$,className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:a.jsx(o.Z,{size:18,className:"text-primary-500"})}):a.jsx("button",{onClick:D,className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:a.jsx(d.Z,{size:18,className:"text-[#667085]"})}),a.jsx(q,{item:e})]})]})]})}},19769:(e,t,s)=>{s.d(t,{Z:()=>p});var a=s(42451),r=s(89205),l=s(25290),i=s(29138),n=s(58687),o=s(72144),d=s(62432),c=s(49113),x=s(32437),m=s(31426);let p=e=>{let{state:t,dispatch:s}=(0,a.useContext)(x.R),p=localStorage.getItem("channelName")||"",[h,u]=(0,a.useState)(!0);return{editor:(0,r.jE)({extensions:[l.Z.configure({bulletList:{HTMLAttributes:{class:"list-disc pl-5"}},orderedList:{HTMLAttributes:{class:"list-decimal pl-5"}},listItem:{HTMLAttributes:{class:"tracking-wide"}},paragraph:{HTMLAttributes:{class:"tracking-wide"}}}),i.Z.configure({placeholder:`Message #${p}`}),n.Z.configure({HTMLAttributes:{class:"mention",style:"color: blue; font-weight: normal; background:#F1F1FE; padding-left:2px; padding-right:2px"},suggestion:{items:e=>{let s=String(e||"").toLowerCase(),a=t?.orgMembers?.filter(e=>(e?.name&&e?.name!==" "?e?.name:e?.email).toLowerCase().includes(s))??[];return"@channel".includes(s)?[{id:"channel",name:"@channel",profile_url:"/images/megaphone.png",full_name:"Notify everyone in this channel",is_online:!1},...a]:a},render:()=>{let e=null,a=-1,r=t=>{if(!e)return;let s=Array.from(e.querySelectorAll("button"));if(0!==s.length){if("ArrowDown"===t.key){if(t.preventDefault(),t.stopPropagation(),-1!==a&&s[a]){s[a].classList.remove("hover");let e=s[a].querySelector(".name-span"),t=s[a].querySelector(".secondary-span");e&&e.classList.remove("text-white"),t&&t.classList.remove("text-white")}a=(a+1)%s.length;let e=s[a];if(e){e.classList.add("hover");let t=e.querySelector(".name-span"),s=e.querySelector(".secondary-span");t&&t.classList.add("text-white"),s&&s.classList.add("text-white"),e.scrollIntoView({block:"nearest"})}}else if("ArrowUp"===t.key){if(t.preventDefault(),t.stopPropagation(),-1!==a&&s[a]){s[a].classList.remove("hover");let e=s[a].querySelector(".name-span"),t=s[a].querySelector(".secondary-span");e&&e.classList.remove("text-white"),t&&t.classList.remove("text-white")}a=(a-1+s.length)%s.length;let e=s[a];if(e){e.classList.add("hover");let t=e.querySelector(".name-span"),s=e.querySelector(".secondary-span");t&&t.classList.add("text-white"),s&&s.classList.add("text-white"),e.scrollIntoView({block:"nearest"})}}else if("Enter"===t.key){let a=e.querySelector("button.hover");if(a){t.preventDefault(),t.stopPropagation(),a.click();return}if(1===s.length){t.preventDefault(),t.stopPropagation(),s[0].click();return}}else"Escape"===t.key&&(e.remove(),e=null,a=-1,t.preventDefault(),t.stopPropagation())}},l=t=>{e&&!e.contains(t.target)&&(e.remove(),e=null,a=-1)};return{onStart:({query:t,command:s,clientRect:n})=>{(e=document.createElement("div")).className="absolute border border-gray-300 rounded-lg shadow-lg bg-[#F9FAFB] overflow-y-auto z-50",document.body.appendChild(e),document.addEventListener("keydown",r,!0),document.addEventListener("mousedown",l),i(e,t,s,n);let o=e.querySelector("button");if(o){o.classList.add("hover");let e=o.querySelector(".name-span"),t=o.querySelector(".secondary-span");e&&e.classList.add("text-white"),t&&t.classList.add("text-white"),a=0}else a=-1},onUpdate:({query:t,command:s,clientRect:r})=>{if(!e)return;e.innerHTML="",i(e,t,s,r);let l=e.querySelector("button");if(l){l.classList.add("hover");let e=l.querySelector(".name-span"),t=l.querySelector(".secondary-span");e&&e.classList.add("text-white"),t&&t.classList.add("text-white"),a=0}else a=-1},onExit:()=>{e&&e.remove(),document.removeEventListener("keydown",r,!0),document.removeEventListener("mousedown",l),e=null,a=-1}};function i(e,r,l,i){let n=String(r||"").toLowerCase(),o=t?.orgMembers?.filter(e=>(e.name||e.email).toLowerCase().includes(n))??[],d=""===n||"@channel".includes(n)?[{id:"00000000-0000-0000-0000-000000000000",name:"@channel",profile_url:"/images/megaphone.png",full_name:"Notify everyone in this channel",is_online:!1,type:"user"},...o]:o;if(0===d.length){e&&(e.style.display="none");return}e&&(e.style.display="");let c="function"==typeof i?i():null;if(c&&e){let t=c.top+window.scrollY,s=Math.min(48*d.length,300),a=t>s+48?t-s-c.height:c.bottom+window.scrollY;Object.assign(e.style,{top:`${a}px`,left:`${c.left+window.scrollX}px`,maxHeight:"300px"})}d.forEach(r=>{let i=document.createElement("button");i.className="group flex items-center px-3 py-2 text-left w-full gap-3",i.addEventListener("mouseover",()=>{Array.from(e.querySelectorAll("button")).forEach((e,t)=>{if(e===i){if(!e.classList.contains("hover")){e.classList.add("hover");let s=e.querySelector(".name-span"),r=e.querySelector(".secondary-span");s&&s.classList.add("text-white"),r&&r.classList.add("text-white"),a=t}}else if(e.classList.contains("hover")){e.classList.remove("hover");let t=e.querySelector(".name-span"),s=e.querySelector(".secondary-span");t&&t.classList.remove("text-white"),s&&s.classList.remove("text-white")}})});let n=document.createElement("div");n.className="w-6 h-6 flex items-center justify-center rounded-md bg-gray-200 text-white font-bold text-sm overflow-hidden",n.style.minWidth="1.5rem";let o=document.createElement("img");o.src=r.profile_url||"/images/user.png",o.alt=r.name||r.email,o.className="w-full h-full rounded-md object-cover border",n.appendChild(o);let d=document.createElement("div");d.className="flex items-center gap-2";let c=document.createElement("div");c.className="flex items-center gap-2";let x=document.createElement("span");x.textContent=r.name||r.email,x.className="text-sm font-bold capitalize text-gray-800 name-span";let p=document.createElement("div");"@channel"!==r.name&&(p.className="size-2 rounded-full border border-gray-500"),c.appendChild(x),"channel"!==r.id&&c.appendChild(p);let h=document.createElement("span");h.textContent=r?.id==="00000000-0000-0000-0000-000000000000"?r.full_name:r.name&&r.email&&r.name.toLowerCase()!==r.email.toLowerCase()?r.email:"",h.className="text-xs text-gray-500 secondary-span",d.appendChild(c),h.textContent&&d.appendChild(h),i.appendChild(n),i.appendChild(d),i.onclick=()=>{let e=r.name;e&&""!==e.trim()||(e=r.email);let a=e.replace(/^@/,""),i={id:r.id,label:a};if("channel"!==r.id){let e={id:r.id,label:a,type:"user"};t.mentions.some(t=>t.id===e.id)||s({type:m.a.MENTIONS,payload:[e]})}l(i)},e.appendChild(i)})}}}}),c.ZP.configure({openOnClick:!0,linkOnPaste:!0,autolink:!0,defaultProtocol:"https",HTMLAttributes:{class:"text-primary-500"}}),o.ZP,d.ZP.configure({HTMLAttributes:{class:"language-javascript"}})],onCreate:({editor:e})=>{e.commands.focus()},onUpdate:({editor:e})=>{u(e.isEmpty)},editorProps:{handlePaste(t,s,a){let r=s.clipboardData?.items;if(r){for(let t of r)if(-1!==t.type.indexOf("image")){let s=t.getAsFile();return s&&e?.(s),!0}}return!1}}}),isEmpty:h}}},12764:(e,t,s)=>{s.d(t,{Z:()=>p});var a=s(12902),r=s(87489),l=s(5160),i=s(21912),n=s(32437),o=s(42451),d=s(61347),c=s(31426),x=s(27940),m=s(36804);function p({name:e}){let{state:t,dispatch:s}=(0,o.useContext)(n.R),{orgData:p}=t,[h,u]=(0,o.useState)(!1),g=async()=>{localStorage.clear(),window.location.href="/auth/login"};return a.jsx("div",{className:"",children:(0,a.jsxs)(r.J2,{children:[a.jsx(r.xo,{asChild:!0,children:(0,a.jsxs)("div",{className:"flex items-center gap-1 cursor-pointer",children:[a.jsx("h6",{className:"text-lg leading-[26px] font-semibold text-white",children:e||p?.name}),a.jsx(l.Z,{className:"text-white mt-1"})]})}),(0,a.jsxs)(r.yk,{align:"start",className:"w-[270px] p-0 rounded-md shadow-xl",onClick:()=>s({type:c.a.CHANNEL_BAR,payload:!t?.channelBar}),children:[(0,a.jsxs)("div",{className:"",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 p-3 border-b font-medium text-sm",children:[a.jsx("div",{className:"size-9 rounded border overflow-hidden flex items-center justify-center",children:p?.logo_url?a.jsx(d.default,{src:p?.logo_url,alt:"",width:50,height:50,unoptimized:!0,className:"size-9"}):a.jsx("h3",{className:"text-primary-500 font-bold text-sm",children:(0,x.Qm)(p?.name)})}),a.jsx("div",{className:"text-sm",children:p?.name})]}),(0,a.jsxs)("div",{className:"flex gap-2 text-xs px-3 py-3 font-medium border-b hover:bg-blue-500 hover:text-white cursor-pointer",children:["\uD83D\uDE80",(0,a.jsxs)("div",{children:["Your ",a.jsx("strong",{children:"Pro trial"})," lasts through"," ",a.jsx("strong",{children:"June 13"}),".",a.jsx("a",{href:"#",className:"text-blue-600 block hover:underline",children:"See upgrade options"})]})]})]}),(0,a.jsxs)("ul",{className:"text-sm pb-3",children:[(0,a.jsxs)("li",{onClick:()=>s({type:c.a.INVITE_MODAL,payload:!0}),className:"hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 text-[15px]",children:["Invite people to ",p?.name]}),a.jsx("li",{className:"hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]",children:a.jsx(m.default,{href:"/client/settings/personal/account",children:"settings"})}),(0,a.jsxs)("li",{className:"relative hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]",onMouseEnter:()=>u(!0),onMouseLeave:()=>u(!1),children:["Apps ",a.jsx(i.Z,{className:"h-4 w-4"}),h&&(0,a.jsxs)("ul",{className:"absolute left-full top-0 w-[200px] bg-white text-black rounded-[7px] shadow-lg border border-[#E6EAEF] overflow-hidden",children:[a.jsx("li",{className:"hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]",children:"App1"}),a.jsx("li",{className:"hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]",children:"App2"}),a.jsx("li",{className:"hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]",children:"App3"}),a.jsx("li",{className:"hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]",children:"App4"})]})]}),a.jsx("li",{onClick:g,className:"hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 font-medium text-[15px]",children:"Sign out"})]})]})]})})}},51692:(e,t,s)=>{s.d(t,{Z:()=>o});var a=s(12902),r=s(42451),l=s(32437);s(2849),s(30333),s(31426);var i=s(71032),n=s(22655);function o(){let{state:e,dispatch:t}=(0,r.useContext)(l.R),{orgId:s}=e;(0,n.usePathname)();let o=(0,r.useRef)(null);return process.env.NEXT_PUBLIC_CONNECT_URL,(0,a.jsxs)("div",{children:[a.jsx(i.Ix,{limit:1}),(0,a.jsxs)("audio",{controls:!0,ref:o,style:{display:"none"},children:[a.jsx("source",{src:"/audio/message.mp3",type:"audio/mpeg"}),"Your browser does not support the audio element."]})]})}s(27940),process.env.NEXT_PUBLIC_CLIENT_URL},62058:(e,t,s)=>{s.d(t,{I:()=>i});var a=s(12902),r=s(42451),l=s(90488);let i=r.forwardRef(({className:e,type:t,...s},r)=>a.jsx("input",{type:t,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-4 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus:outline-none disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...s}));i.displayName="Input"},91582:(e,t,s)=>{s.d(t,{p:()=>i});var a=s(81823),r=s(84763),l=s(60586);let i=e=>{let t={};return e?.forEach(e=>{let s;let i=new Date(e.created_at);t[s=a.z(i)?"Today":r.g(i)?"Yesterday":l.WU(i,"MMMM d, yyyy")]||(t[s]=[]),t[s].push(e)}),t}}};