"use strict";exports.id=6e3,exports.ids=[6e3],exports.modules={56e3:(e,D,u)=>{u.d(D,{Z:()=>P});var t=u(12902),s=u(42451),l=u(29252),r=u(59017),a=u(57294),i=u(90305),n=u(40032),o=u(41833),c=u(56320),d=u(89362),E=u(90803),x=u(99963),h=u(80340),m=u(73794),g=u(29793),p=u(43421),b=u(20833),f=u(28575),j=u(61347),v=u(19769),y=u(32437),A=u(11467),C=u(22655),N=u(87614),k=u(89205),w=u(7638),Z=u(62058),_=u(8659),F=u(83527),L=u(40035),z=u(99075),$=u(31426),R=u(87489),O=u(53218),S=u.n(O),T=u(31333),U=u.n(T);let B=e=>{let{state:D}=(0,s.useContext)(y.R);return{handleTyping:U()(u=>{e&&e?.publish({user:{id:D?.user?.id,username:D?.user?.username},typing:u,type:"typing"})},50)}},I={":-)":"\uD83D\uDE0A",":)":"\uD83D\uDE0A","(:":"\uD83D\uDE0A","=)":"\uD83D\uDE0A",":-D":"\uD83D\uDE00",":D":"\uD83D\uDE00",xD:"\uD83D\uDE06",XD:"\uD83D\uDE06",":-(":"\uD83D\uDE1E",":(":"\uD83D\uDE1E","):":"\uD83D\uDE1E","=(":"\uD83D\uDE1E",";-)":"\uD83D\uDE09",";)":"\uD83D\uDE09",";]":"\uD83D\uDE09","*-)":"\uD83D\uDE09",":-P":"\uD83D\uDE1B",":P":"\uD83D\uDE1B",":-p":"\uD83D\uDE1B",":p":"\uD83D\uDE1B",":-o":"\uD83D\uDE2E",":o":"\uD83D\uDE2E",":-O":"\uD83D\uDE2E",":O":"\uD83D\uDE2E",":-*":"\uD83D\uDE18",":*":"\uD83D\uDE18",":-x":"\uD83D\uDE18",":x":"\uD83D\uDE18",":'(":"\uD83D\uDE22",":'‑(":"\uD83D\uDE22",":'‑)":"\uD83D\uDE02",":'D":"\uD83D\uDE02",":-/":"\uD83D\uDE15",":/":"\uD83D\uDE15",":-\\":"\uD83D\uDE15",":\\":"\uD83D\uDE15",":-|":"\uD83D\uDE10",":|":"\uD83D\uDE10",":-]":"\uD83D\uDE10",":]":"\uD83D\uDE10","<3":"❤️","</3":"\uD83D\uDC94","<\\3":"\uD83D\uDC94","♥":"❤️",":3":"\uD83D\uDE3A",":-3":"\uD83D\uDE3A",":>":"\uD83D\uDE3A",":^)":"\uD83D\uDE04","O:)":"\uD83D\uDE07","O:-)":"\uD83D\uDE07","0:)":"\uD83D\uDE07","0:-)":"\uD83D\uDE07",">:(":"\uD83D\uDE20",">:-(":"\uD83D\uDE20","D:<":"\uD83D\uDE20",D8:"\uD83D\uDE31","D-:<":"\uD83D\uDE20","D':":"\uD83D\uDE31",">:O":"\uD83D\uDE21",">:o":"\uD83D\uDE21",":-$":"\uD83E\uDD10",":$":"\uD83E\uDD10",":-#":"\uD83E\uDD10",":#":"\uD83E\uDD10",":-X":"\uD83E\uDD10",":X":"\uD83E\uDD10",":-!":"\uD83E\uDD10","B-)":"\uD83D\uDE0E","B)":"\uD83D\uDE0E","8-)":"\uD83D\uDE0E","8)":"\uD83D\uDE0E",":-B":"\uD83D\uDE0E",":-b":"\uD83D\uDE0E","|-)":"\uD83D\uDE34","|)":"\uD83D\uDE34","-_-":"\uD83D\uDE11","=_=":"\uD83D\uDE11",">.<":"\uD83D\uDE23",">_>":"\uD83D\uDE12","<_<":"\uD83D\uDE12","-.-":"\uD83D\uDE12",u_u:"\uD83D\uDE14","^_^":"\uD83D\uDE0A","^.^":"\uD83D\uDE0A","^-^":"\uD83D\uDE0A",":‑]":"\uD83D\uDE42","=]":"\uD83D\uDE42",":}":"\uD83D\uDE42",":-}":"\uD83D\uDE42",":')":"\uD83D\uDE02",":‑')":"\uD83D\uDE02",":‑))":"\uD83D\uDE02",":’-D":"\uD83D\uDE02",":L":"\uD83D\uDE15",":S":"\uD83D\uDE16",":Z":"\uD83D\uDE36",":T":"\uD83D\uDE24","D:":"\uD83D\uDE27",DX:"\uD83D\uDE27",QQ:"\uD83D\uDE2D",T_T:"\uD83D\uDE2D","T-T":"\uD83D\uDE2D",TT:"\uD83D\uDE2D",":-@":"\uD83D\uDE21",":@":"\uD83D\uDE21",">:D":"\uD83D\uDE08",">;]":"\uD83D\uDE08",">:)":"\uD83D\uDE08",">:3":"\uD83D\uDE08","<(^_^)>":"\uD83E\uDD17","<(o_o<)":"\uD83E\uDD17","(>o_o)>":"\uD83E\uDD17","(>'-')>":"\uD83E\uDD17","<('-'<)":"\uD83E\uDD17","ヽ(•‿•)ノ":"\uD83D\uDE0A","(╯\xb0□\xb0）╯︵ ┻━┻":"\uD83D\uDE21","┬─┬ ノ( ゜-゜ノ)":"\uD83D\uDE0C","(☞ﾟヮﾟ)☞":"\uD83D\uDC49","☜(ﾟヮﾟ☜)":"\uD83D\uDC48","(づ｡◕‿‿◕｡)づ":"\uD83E\uDD17","(~_^)":"\uD83D\uDE09","(^_−)☆":"\uD83D\uDE09","(^_^)":"\uD83D\uDE0A","(^.^)":"\uD83D\uDE0A","(-_-)":"\uD83D\uDE34","(\xac_\xac)":"\uD83D\uDE12","(ಠ_ಠ)":"\uD83D\uDE11","(ʘ‿ʘ)":"\uD83D\uDE33","(ಥ﹏ಥ)":"\uD83D\uDE2D","(\xac‿\xac)":"\uD83D\uDE0F","(ง •̀_•́)ง":"\uD83D\uDCAA"},P=({subscription:e,sendMessage:D,show:u=!0})=>{let{state:O,dispatch:T}=(0,s.useContext)(y.R),U=(0,C.useParams)().id,P=(0,N.AE)(),[M,X]=(0,s.useState)(!1),[K,Q]=(0,s.useState)(""),[q,H]=(0,s.useState)(""),[J,V]=(0,s.useState)(!1),[G,W]=(0,s.useState)(!0),Y=(0,s.useRef)(null),[ee,eD]=(0,s.useState)([]),[eu,et]=(0,s.useState)([]),[es,el]=(0,s.useState)([]),{handleTyping:er}=B(e);(0,s.useEffect)(()=>()=>{ee.forEach(e=>URL.revokeObjectURL(e.preview))},[ee]);let{editor:ea,isEmpty:ei}=(0,v.Z)(e=>{let D={id:Date.now()+Math.random(),file:e,type:"image",preview:URL.createObjectURL(e)};eD(e=>[...e,D]),el(e=>[...e,D.id]);let u=new FormData;u.append("files",e),(0,A.x9)("/files/upload-files",u).then(e=>{let D=e?.data?.data[0];D&&ea&&et(e=>[...e,D])}).catch(e=>{console.error("Image paste upload failed",e)}).finally(()=>{el(e=>e.filter(e=>e!==D.id))})}),en=async e=>{let D=e.target.files;if(!D)return;let u=Array.from(D).map(e=>{let D=e.type.split("/")[0];return{id:Date.now()+Math.random(),file:e,type:D,preview:"image"===D||"video"===D?URL.createObjectURL(e):null}});for(let e of(eD(e=>[...e,...u]),u)){el(D=>[...D,e.id]);let D=new FormData;D.append("files",e.file);try{let e=await (0,A.x9)("/files/upload-files",D);e?.data?.data&&et(D=>[...D,...e.data.data])}catch(e){console.error("Upload failed",e)}finally{el(D=>D.filter(D=>D!==e.id))}}},eo=e=>{eD(D=>D.filter(u=>u!==D[e])),et(D=>D.filter((D,u)=>u!==e))},ec=e=>{let D=e;for(let e in I){let u=RegExp(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"g");D=D.replace(u,I[e])}return D},ed=async u=>{if(u.preventDefault(),!ea)return;let t=ea.getHTML(),s=ea.getText().trim(),l=s.length>0,r=eu.length>0;if(!l&&!r)return;let a=ec(s),i=S().shortnameToUnicode(a);t=t.replace(s,i),e?(ea.commands.clearContent(),eD([]),et([]),D(U,P,t,eu),T({type:$.a.CLEAR_MENTIONS}),er(!1)):console.log("No connection detected")};return(0,s.useEffect)(()=>{ea&&ea.commands.focus()},[ea]),t.jsx(t.Fragment,{children:(0,t.jsxs)("div",{onClick:()=>ea&&ea.commands.focus(),className:`bg-white border rounded-xl mx-3 md:mx-5 border-[#E6EAEF] overflow-hidden ${O?.reply?"right-[520px]":"right-0"} ${ea?.isFocused?"border-primary-400":"border-gray-200"}`,children:[G&&(0,t.jsxs)("div",{className:"border-b border-[#E6EAEF] flex items-center gap-2 bg-[#F9FAFB] pl-3 pr-4 py-[5px]",children:[t.jsx("button",{onClick:()=>ea?.chain().focus().toggleBold().run(),className:`p-1.5 hover:bg-gray-100 rounded ${ea?.isActive("bold")?"bg-gray-200 font-semibold text-black":""}`,children:t.jsx(l.Z,{size:18,color:ea?.isActive("bold")?"#444444":"#CACACA"})}),t.jsx("button",{onClick:()=>ea?.chain().focus().toggleItalic().run(),className:`p-1.5 hover:bg-gray-100 rounded ${ea?.isActive("italic")?"bg-gray-200 font-semibold text-black":""}`,children:t.jsx(r.Z,{size:18,color:ea?.isActive("italic")?"#444444":"#CACACA"})}),t.jsx("button",{onClick:()=>ea?.chain().focus().toggleStrike().run(),className:`p-1.5 hover:bg-gray-100 rounded ${ea?.isActive("strike")?"bg-gray-200 font-semibold text-black":""}`,children:t.jsx(a.Z,{size:18,color:ea?.isActive("strike")?"#444444":"#CACACA"})}),t.jsx("div",{className:"w-px h-5 bg-[#E6EAEF]"}),(0,t.jsxs)(w.Vq,{open:M,onOpenChange:X,children:[t.jsx(w.hg,{asChild:!0,children:t.jsx("button",{onClick:()=>X(!0),className:`p-1.5 hover:bg-gray-100 rounded ${ea?.isActive("link")?"bg-gray-200 font-semibold text-black":""}`,children:t.jsx(i.Z,{size:18,color:ea?.isActive("link")?"#444444":"#CACACA"})})}),(0,t.jsxs)(w.cZ,{className:"w-full max-w-md",children:[t.jsx(w.fK,{children:t.jsx(w.$N,{className:"font-semibold",children:"Add link"})}),(0,t.jsxs)("div",{className:"flex flex-col gap-2",children:[t.jsx("label",{className:"text-sm font-medium",children:"Text"}),t.jsx(Z.I,{value:K,onChange:e=>Q(e.target.value),placeholder:"Enter link text"}),t.jsx("label",{className:"text-sm font-medium mt-2",children:"Link"}),t.jsx(Z.I,{value:q,onChange:e=>H(e.target.value),placeholder:"Enter URL",type:"url"})]}),(0,t.jsxs)(w.cN,{className:"mt-4 flex justify-end gap-2",children:[t.jsx(_.z,{variant:"outline",onClick:()=>X(!1),children:"Cancel"}),t.jsx(_.z,{onClick:()=>{K&&q&&(ea?.chain().focus().insertContent(`<a href="${q}" target="_blank" rel="noopener noreferrer">${K}</a>`).run(),X(!1),Q(""),H(""))},disabled:!K||!q,className:"bg-blue-500 text-white px-10",children:"Save"})]})]})]}),t.jsx("button",{onClick:()=>ea?.chain().focus().toggleOrderedList().run(),className:`p-1.5 hover:bg-gray-100 rounded ${ea?.isActive("orderedList")?"bg-gray-200 font-semibold text-black":""}`,children:t.jsx(n.Z,{size:18,color:ea?.isActive("orderedList")?"#444444":"#CACACA"})}),t.jsx("button",{onClick:()=>ea?.chain().focus().toggleBulletList().run(),className:`p-1.5 hover:bg-gray-100 rounded ${ea?.isActive("bulletList")?"bg-gray-200 font-semibold text-black":""}`,children:t.jsx(o.Z,{size:18,color:ea?.isActive("bulletList")?"#444444":"#CACACA"})}),t.jsx("div",{className:"w-px h-5 bg-[#E6EAEF]"}),t.jsx("button",{onClick:()=>ea?.chain().focus().toggleCode().run(),className:`p-1.5 hover:bg-gray-100 rounded ${ea?.isActive("code")?"bg-gray-200 font-semibold text-black":""}`,children:t.jsx(c.Z,{size:18,color:ea?.isActive("code")?"#444444":"#CACACA"})})]}),(0,t.jsxs)("div",{className:"md:flex-1 relative px-3",children:[t.jsx(k.kg,{editor:ea,className:"py-2 rounded-md flex flex-row overflow-auto",onKeyDown:e=>{"Enter"===e.key&&(e.shiftKey?(e.preventDefault(),ea?.commands.enter()):(e.preventDefault(),ed(e)))}}),t.jsx("div",{className:`flex gap-3 ${ee?.length>0?"mt-3":""}`,children:ee?.map((e,D)=>t.jsxs("div",{className:"relative w-[70px] h-[70px]",children:["image"===e.type?t.jsx(j.default,{src:e.preview,alt:`Uploaded ${D}`,width:70,height:70,className:"w-[70px] h-[70px] rounded-md object-cover border border-primary-400 cursor-pointer"}):"application"===e.type?t.jsx("div",{className:"w-[70px] h-[70px] flex items-center justify-center border border-primary-500 rounded-md bg-gray-100",children:t.jsxs("a",{href:URL.createObjectURL(e.file),target:"_blank",rel:"noopener noreferrer",className:"flex flex-col items-center",children:[t.jsx(d.Z,{size:24,color:"#606060"}),t.jsx("span",{className:"text-xs text-blue-500 mt-1",children:"PDF"})]})}):null,t.jsx("button",{onClick:()=>eo(D),className:"absolute -top-1 -right-2 p-1 bg-gray-500 text-white rounded-full w-5 h-5 text-xs flex items-center justify-center",children:t.jsx(E.Z,{size:14})}),es.includes(e?.id)&&t.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-50",children:t.jsx(z.Z,{})})]},D))})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 py-2 pl-3 pr-4",children:[(0,t.jsxs)("button",{onClick:()=>Y.current?.click(),className:"p-1.5 hover:bg-gray-100 rounded-full bg-[#F2F4F7]",children:[t.jsx("input",{type:"file",ref:Y,style:{display:"none"},onChange:en,accept:"image/*, application/pdf",multiple:!0}),t.jsx(x.Z,{size:18,color:"#606060"})]}),t.jsx("button",{title:"show formatting",onClick:()=>W(e=>!e),className:"p-1.5 hover:bg-gray-100 rounded text-[#606060] underline",children:"Aa"}),t.jsx("div",{className:"relative",children:(0,t.jsxs)(R.J2,{open:J,onOpenChange:V,children:[t.jsx(R.xo,{asChild:!0,children:t.jsx("button",{onClick:e=>e.stopPropagation(),className:"p-1.5 hover:bg-gray-100 rounded",children:t.jsx(h.Z,{size:18,color:"#606060"})})}),t.jsx(R.yk,{className:"p-0 w-full max-w-xs",children:t.jsx(F.Z,{data:L,onEmojiSelect:e=>{ea?(ea.chain().focus().insertContent(e?.native).run(),V(!1)):console.log("editor not available")}})})]})}),t.jsx("button",{onClick:()=>{ea?.chain().focus().insertContent("@").run()},className:"p-1.5 hover:bg-gray-100 rounded",children:t.jsx(m.Z,{size:18,color:"#606060"})}),u&&(0,t.jsxs)(s.Fragment,{children:[t.jsx("div",{className:"w-px h-5 bg-[#E6EAEF] hidden sm:flex"}),t.jsx("button",{className:"p-1.5 hover:bg-gray-100 rounded hidden sm:flex",children:t.jsx(g.Z,{size:18,color:"#606060"})}),t.jsx("button",{className:"p-1.5 hover:bg-gray-100 rounded hidden sm:flex",children:t.jsx(p.Z,{size:18,color:"#606060"})}),t.jsx("div",{className:"w-px h-5 bg-[#E6EAEF]"}),t.jsx("button",{className:"p-1.5 hover:bg-gray-100 rounded hidden sm:flex",children:t.jsx(b.Z,{size:18,color:"#606060",className:"[&>path]:stroke-[2.5]"})})]})]}),t.jsx("div",{className:"flex items-center gap-1 py-2 pl-3 pr-4",children:t.jsx("button",{type:"submit",className:"p-1.5 hover:bg-gray-100 rounded size-8 flex items-center justify-center",onClick:ed,disabled:ei&&ee?.length===0,children:t.jsx(f.Z,{color:ei&&ee?.length===0?"#999":"black"})})})]})]})})}}};