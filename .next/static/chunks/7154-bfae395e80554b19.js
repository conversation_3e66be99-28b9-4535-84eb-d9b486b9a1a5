(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7154,2305],{39713:function(e,t,r){"use strict";r.d(t,{default:function(){return o.a}});var n=r(74033),o=r.n(n)},74033:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return d},getImageProps:function(){return l}});let n=r(60723),o=r(25738),a=r(28863),i=n._(r(44543));function l(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let d=a.Image},6389:function(){},75737:function(e,t,r){"use strict";r.d(t,{VY:function(){return ei},h4:function(){return eo},ck:function(){return en},fC:function(){return er},xz:function(){return ea}});var n=r(32486),o=r(32192),a=r(35614),i=r(29626),l=r(20100),d=r(31413),c=r(89801),s=r(79315),u=r(53486),f=r(21971),p=r(75376),v="Collapsible",[b,m]=(0,o.b)(v),[g,h]=b(v),x=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:o,defaultOpen:a,disabled:i,onOpenChange:l,...s}=e,[u,b]=(0,d.T)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:v});return(0,p.jsx)(g,{scope:r,disabled:i,contentId:(0,f.M)(),open:u,onOpenToggle:n.useCallback(()=>b(e=>!e),[b]),children:(0,p.jsx)(c.WV.div,{"data-state":R(u),"data-disabled":i?"":void 0,...s,ref:t})})});x.displayName=v;var w="CollapsibleTrigger",j=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,...n}=e,o=h(w,r);return(0,p.jsx)(c.WV.button,{type:"button","aria-controls":o.contentId,"aria-expanded":o.open||!1,"data-state":R(o.open),"data-disabled":o.disabled?"":void 0,disabled:o.disabled,...n,ref:t,onClick:(0,l.M)(e.onClick,o.onOpenToggle)})});j.displayName=w;var y="CollapsibleContent",C=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=h(y,e.__scopeCollapsible);return(0,p.jsx)(u.z,{present:r||o.open,children:e=>{let{present:r}=e;return(0,p.jsx)(A,{...n,ref:t,present:r})}})});C.displayName=y;var A=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:o,children:a,...l}=e,d=h(y,r),[u,f]=n.useState(o),v=n.useRef(null),b=(0,i.e)(t,v),m=n.useRef(0),g=m.current,x=n.useRef(0),w=x.current,j=d.open||u,C=n.useRef(j),A=n.useRef(void 0);return n.useEffect(()=>{let e=requestAnimationFrame(()=>C.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,s.b)(()=>{let e=v.current;if(e){A.current=A.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();m.current=t.height,x.current=t.width,C.current||(e.style.transitionDuration=A.current.transitionDuration,e.style.animationName=A.current.animationName),f(o)}},[d.open,o]),(0,p.jsx)(c.WV.div,{"data-state":R(d.open),"data-disabled":d.disabled?"":void 0,id:d.contentId,hidden:!j,...l,ref:b,style:{"--radix-collapsible-content-height":g?"".concat(g,"px"):void 0,"--radix-collapsible-content-width":w?"".concat(w,"px"):void 0,...e.style},children:j&&a})});function R(e){return e?"open":"closed"}var _=r(398),k="Accordion",I=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[N,D,O]=(0,a.B)(k),[S,P]=(0,o.b)(k,[O,m]),z=m(),M=n.forwardRef((e,t)=>{let{type:r,...n}=e;return(0,p.jsx)(N.Provider,{scope:e.__scopeAccordion,children:"multiple"===r?(0,p.jsx)(L,{...n,ref:t}):(0,p.jsx)(H,{...n,ref:t})})});M.displayName=k;var[T,V]=S(k),[E,W]=S(k,{collapsible:!1}),H=n.forwardRef((e,t)=>{let{value:r,defaultValue:o,onValueChange:a=()=>{},collapsible:i=!1,...l}=e,[c,s]=(0,d.T)({prop:r,defaultProp:null!=o?o:"",onChange:a,caller:k});return(0,p.jsx)(T,{scope:e.__scopeAccordion,value:n.useMemo(()=>c?[c]:[],[c]),onItemOpen:s,onItemClose:n.useCallback(()=>i&&s(""),[i,s]),children:(0,p.jsx)(E,{scope:e.__scopeAccordion,collapsible:i,children:(0,p.jsx)(K,{...l,ref:t})})})}),L=n.forwardRef((e,t)=>{let{value:r,defaultValue:o,onValueChange:a=()=>{},...i}=e,[l,c]=(0,d.T)({prop:r,defaultProp:null!=o?o:[],onChange:a,caller:k}),s=n.useCallback(e=>c(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return[...t,e]}),[c]),u=n.useCallback(e=>c(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter(t=>t!==e)}),[c]);return(0,p.jsx)(T,{scope:e.__scopeAccordion,value:l,onItemOpen:s,onItemClose:u,children:(0,p.jsx)(E,{scope:e.__scopeAccordion,collapsible:!0,children:(0,p.jsx)(K,{...i,ref:t})})})}),[B,F]=S(k),K=n.forwardRef((e,t)=>{let{__scopeAccordion:r,disabled:o,dir:a,orientation:d="vertical",...s}=e,u=n.useRef(null),f=(0,i.e)(u,t),v=D(r),b="ltr"===(0,_.gm)(a),m=(0,l.M)(e.onKeyDown,e=>{var t;if(!I.includes(e.key))return;let r=e.target,n=v().filter(e=>{var t;return!(null===(t=e.ref.current)||void 0===t?void 0:t.disabled)}),o=n.findIndex(e=>e.ref.current===r),a=n.length;if(-1===o)return;e.preventDefault();let i=o,l=a-1,c=()=>{(i=o+1)>l&&(i=0)},s=()=>{(i=o-1)<0&&(i=l)};switch(e.key){case"Home":i=0;break;case"End":i=l;break;case"ArrowRight":"horizontal"===d&&(b?c():s());break;case"ArrowDown":"vertical"===d&&c();break;case"ArrowLeft":"horizontal"===d&&(b?s():c());break;case"ArrowUp":"vertical"===d&&s()}null===(t=n[i%a].ref.current)||void 0===t||t.focus()});return(0,p.jsx)(B,{scope:r,disabled:o,direction:a,orientation:d,children:(0,p.jsx)(N.Slot,{scope:r,children:(0,p.jsx)(c.WV.div,{...s,"data-orientation":d,ref:f,onKeyDown:o?void 0:m})})})}),U="AccordionItem",[q,G]=S(U),Y=n.forwardRef((e,t)=>{let{__scopeAccordion:r,value:n,...o}=e,a=F(U,r),i=V(U,r),l=z(r),d=(0,f.M)(),c=n&&i.value.includes(n)||!1,s=a.disabled||e.disabled;return(0,p.jsx)(q,{scope:r,open:c,disabled:s,triggerId:d,children:(0,p.jsx)(x,{"data-orientation":a.orientation,"data-state":et(c),...l,...o,ref:t,disabled:s,open:c,onOpenChange:e=>{e?i.onItemOpen(n):i.onItemClose(n)}})})});Y.displayName=U;var $="AccordionHeader",J=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,o=F(k,r),a=G($,r);return(0,p.jsx)(c.WV.h3,{"data-orientation":o.orientation,"data-state":et(a.open),"data-disabled":a.disabled?"":void 0,...n,ref:t})});J.displayName=$;var Q="AccordionTrigger",X=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,o=F(k,r),a=G(Q,r),i=W(Q,r),l=z(r);return(0,p.jsx)(N.ItemSlot,{scope:r,children:(0,p.jsx)(j,{"aria-disabled":a.open&&!i.collapsible||void 0,"data-orientation":o.orientation,id:a.triggerId,...l,...n,ref:t})})});X.displayName=Q;var Z="AccordionContent",ee=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,o=F(k,r),a=G(Z,r),i=z(r);return(0,p.jsx)(C,{role:"region","aria-labelledby":a.triggerId,"data-orientation":o.orientation,...i,...n,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function et(e){return e?"open":"closed"}ee.displayName=Z;var er=M,en=Y,eo=J,ea=X,ei=ee},398:function(e,t,r){"use strict";r.d(t,{gm:function(){return a}});var n=r(32486);r(75376);var o=n.createContext(void 0);function a(e){let t=n.useContext(o);return e||t||"ltr"}},21971:function(e,t,r){"use strict";r.d(t,{M:function(){return d}});var n,o=r(32486),a=r(79315),i=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function d(e){let[t,r]=o.useState(i());return(0,a.b)(()=>{e||r(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}}}]);