"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5463],{9824:function(e,t,r){r.d(t,{Z:function(){return s}});var n=r(32486);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:s,className:c="",children:d,iconNode:u,...f}=e;return(0,n.createElement)("svg",{ref:t,...o,width:l,height:l,stroke:r,strokeWidth:s?24*Number(i)/Number(l):i,className:a("lucide",c),...f},[...u.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),s=(e,t)=>{let r=(0,n.forwardRef)((r,o)=>{let{className:s,...c}=r;return(0,n.createElement)(i,{ref:o,iconNode:t,className:a("lucide-".concat(l(e)),s),...c})});return r.displayName="".concat(e),r}},53532:function(e,t,r){r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},51888:function(e,t,r){r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},71753:function(e,t,r){r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},64632:function(e,t,r){r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},16669:function(e,t,r){r.d(t,{default:function(){return l.a}});var n=r(6092),l=r.n(n)},65683:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return n}});let n=r(60723)._(r(32486)).default.createContext(null)},19660:function(e,t,r){r.d(t,{Cd:function(){return c},X:function(){return d},bZ:function(){return s}});var n=r(75376),l=r(32486),a=r(53447),o=r(58983);let i=(0,a.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),s=l.forwardRef((e,t)=>{let{className:r,variant:l,...a}=e;return(0,n.jsx)("div",{ref:t,role:"alert",className:(0,o.cn)(i({variant:l}),r),...a})});s.displayName="Alert";let c=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,n.jsx)("h5",{ref:t,className:(0,o.cn)("mb-1 font-medium leading-none tracking-tight",r),...l})});c.displayName="AlertTitle";let d=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.cn)("text-sm [&_p]:leading-relaxed",r),...l})});d.displayName="AlertDescription"},11492:function(e,t,r){r.d(t,{d:function(){return s},z:function(){return c}});var n=r(75376),l=r(32486),a=r(91007),o=r(53447),i=r(58983);let s=(0,o.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=l.forwardRef((e,t)=>{let{className:r,variant:l,size:o,asChild:c=!1,...d}=e,u=c?a.g7:"button";return(0,n.jsx)(u,{className:(0,i.cn)(s({variant:l,size:o,className:r})),ref:t,...d})});c.displayName="Button"},25575:function(e,t,r){r.d(t,{I:function(){return o}});var n=r(75376),l=r(32486),a=r(58983);let o=l.forwardRef((e,t)=>{let{className:r,type:l,...o}=e;return(0,n.jsx)("input",{type:l,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-4 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus:outline-none disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...o})});o.displayName="Input"},73003:function(e,t,r){var n=r(75376);r(32486);var l=r(10983);t.Z=e=>{let{height:t,width:r,color:a}=e;return(0,n.jsx)(l.iT,{height:t||20,width:r||20,color:a||"#fff",visible:!0,ariaLabel:"oval-loading",secondaryColor:a||"#fff",strokeWidth:5,strokeWidthSecondary:5})}},58983:function(e,t,r){r.d(t,{cn:function(){return a},k:function(){return o}});var n=r(89824),l=r(97215);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,l.m6)((0,n.W)(t))}let o=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"},43652:function(e,t,r){r.d(t,{SearchProvider:function(){return s},A:function(){return c}});var n=r(75376),l=r(32486),a=r(10952);let o=()=>{let[e,t]=(0,l.useState)(""),[r,n]=(0,l.useState)([]),[o,i]=(0,l.useState)(!1),[s,c]=(0,l.useState)(null),d=(0,l.useCallback)(async()=>{if(!e.trim()){n([]);return}i(!0),c(null);try{var t,r;let l=localStorage.getItem("token");if(!l)throw Error("No token found");let o=await (0,a.Gl)("/help-center/articles/search?title=".concat(encodeURIComponent(e)),l);if((null==o?void 0:o.status)===200&&(null===(t=o.data)||void 0===t?void 0:t.status)==="success"){let e=(null===(r=o.data)||void 0===r?void 0:r.data)||[];n(e)}else throw Error("Failed to fetch search results")}catch(e){c(e instanceof Error?e.message:"An error occurred")}finally{i(!1)}},[e]);return{query:e,results:r,isLoading:o,error:s,searchData:d,setQuery:t,setResults:n}},i=(0,l.createContext)(void 0),s=e=>{let{children:t}=e,r=o();return(0,n.jsx)(i.Provider,{value:r,children:t})},c=()=>{let e=(0,l.useContext)(i);if(void 0===e)throw Error("useSearchContext must be used within a SearchProvider");return e}},66236:function(e,t,r){var n=r(75376);r(32486);var l=r(16669),a=r(51888);t.Z=e=>{let{items:t}=e;return(0,n.jsx)("nav",{className:"flex py-5","aria-label":"Breadcrumb",children:(0,n.jsxs)("ol",{className:"flex items-center space-x-1 text-sm text-gray-600 text-[10px] md:text-[13px]",children:[(0,n.jsx)("li",{children:(0,n.jsx)(l.default,{href:"/help",className:"hover:text-[#8860F8]",children:"All Articles"})}),t.map((e,r)=>(0,n.jsx)("li",{children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)(a.Z,{className:"h-4 w-4 mx-2 text-gray-400"}),(0,n.jsx)(l.default,{href:e.href,className:r===t.length-1?"text-[#8860F8]":"hover:text-[#8860F8]",children:e.label})]})},r))]})})}},4125:function(e,t,r){var n=r(75376),l=r(32486),a=r(43652),o=r(11492),i=r(25575),s=r(19660),c=r(16669),d=r(37523),u=r(75309),f=r(71753);t.Z=()=>{let{query:e,results:t,isLoading:r,error:h,searchData:v,setQuery:p,setResults:m}=(0,a.A)(),[x,g]=(0,l.useState)(1),y=3*x,b=t.slice(y-3,y),j=()=>{m([])};return(0,n.jsxs)("div",{className:"relative w-full",children:[(0,n.jsx)("form",{onSubmit:e=>{e.preventDefault(),v()},className:"mt-4 mb-20 flex justify-center",children:(0,n.jsxs)("div",{className:"md:w-[618px] flex gap-3 p-[5px] rounded-[10px] md:border md:border-[#ADAEAF] md:border-opacity-45",children:[(0,n.jsx)(i.I,{type:"text",value:e,placeholder:"Search for articles",className:"placeholder:text-[#8B8C8D] md:border-none",onChange:e=>p(e.target.value),"aria-label":"Search"}),(0,n.jsxs)("div",{className:"flex gap-4 items-center",children:[""!=e?(0,n.jsx)(f.Z,{color:"#8055F8",className:"hover:cursor-pointer",onClick:()=>{p(""),m([])}}):null,(0,n.jsx)(o.z,{type:"submit",className:"bg-[#8055F8] font-normal text-[#F9FAFB] rounded-[8px] hover:bg-opacity-80",children:"Search"})]})]})}),r&&(0,n.jsx)("div",{children:"Searching..."}),h&&(0,n.jsxs)("div",{children:["Error: ",h]}),b.length>0&&(0,n.jsxs)("div",{className:"absolute top-full left-0 right-0 z-[9999] bg-[#FAFAFA] max-h-[450px] rounded-md mt-8 border border-[#8055F8]",children:[b.map(e=>(0,n.jsx)(c.default,{href:"/help/".concat(e.category_id,"/").concat(e.article_id),onClick:j,children:(0,n.jsx)(s.bZ,{className:"hover:bg-[#ffffff] rounded-none  border-b border-t-0 border-x-0 bg-[#FAFAFA] p-[15px] py-6",children:(0,n.jsx)("div",{className:"flex gap-4 items-center leading-[140%]",children:(0,n.jsxs)("div",{children:[(0,n.jsx)(s.Cd,{className:"text-[16px] font-bold",children:e.title}),(0,n.jsx)(s.X,{className:"text-[16px]",children:e.content})]})})})},e.id)),(0,n.jsx)("div",{className:"my-8",children:(0,n.jsx)(d.Z,{totalPosts:t.length,postsPerPage:3,currentPage:x,setCurrentPage:g})}),(0,n.jsx)("div",{className:"my-8",children:(0,n.jsx)(u.Z,{totalPosts:t.length,postsPerPage:3,currentPage:x,setCurrentPage:g})})]})]})}},37523:function(e,t,r){r.d(t,{Z:function(){return x}});var n=r(75376),l=r(32486),a=r(53532),o=r(51888),i=r(64632),s=r(58983),c=r(11492);let d=e=>{let{className:t,...r}=e;return(0,n.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,s.cn)("mx-auto flex w-full justify-center",t),...r})};d.displayName="Pagination";let u=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,n.jsx)("ul",{ref:t,className:(0,s.cn)("flex flex-row items-center gap-1",r),...l})});u.displayName="PaginationContent";let f=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,n.jsx)("li",{ref:t,className:(0,s.cn)("",r),...l})});f.displayName="PaginationItem";let h=e=>{let{className:t,isActive:r,size:l="icon",...a}=e;return(0,n.jsx)("a",{"aria-current":r?"page":void 0,className:(0,s.cn)((0,c.d)({variant:r?"outline":"ghost",size:l}),t),...a})};h.displayName="PaginationLink";let v=e=>{let{className:t,...r}=e;return(0,n.jsxs)(h,{"aria-label":"Go to previous page",size:"default",className:(0,s.cn)("gap-1 pl-2.5",t),...r,children:[(0,n.jsx)(a.Z,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:"Previous"})]})};v.displayName="PaginationPrevious";let p=e=>{let{className:t,...r}=e;return(0,n.jsxs)(h,{"aria-label":"Go to next page",size:"default",className:(0,s.cn)("gap-1 pr-2.5",t),...r,children:[(0,n.jsx)("span",{children:"Next"}),(0,n.jsx)(o.Z,{className:"h-4 w-4"})]})};p.displayName="PaginationNext";let m=e=>{let{className:t,...r}=e;return(0,n.jsxs)("span",{"aria-hidden":!0,className:(0,s.cn)("flex h-9 w-9 items-center justify-center",t),...r,children:[(0,n.jsx)(i.Z,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"sr-only",children:"More pages"})]})};function x(e){let{totalPosts:t,postsPerPage:r,currentPage:l,setCurrentPage:a}=e,o=[];for(let e=1;e<=Math.ceil(t/r);e++)o.push(e);let i=Math.floor(2.5),s=o.slice(Math.max(0,l-1-i),Math.min(l-1+i+1,o.length));return(0,n.jsx)("div",{className:"hidden md:flex md:justify-center",children:(0,n.jsx)(d,{children:(0,n.jsxs)(u,{children:[(0,n.jsx)(f,{className:"cursor-pointer",children:(0,n.jsx)(v,{onClick:()=>{l>1&&a(l-1)}})}),(()=>{let e=s.map((e,t)=>(0,n.jsx)(f,{className:l===e?"bg-neutral-100 rounded-md":"cursor-pointer",children:(0,n.jsx)(h,{onClick:()=>a(e),children:e})},t));return s[0]>1&&e.unshift((0,n.jsx)(m,{onClick:()=>a(s[0]-1)},"ellipsis-start")),s[s.length-1]<o.length&&e.push((0,n.jsx)(m,{onClick:()=>a(s[s.length-1]+1)},"ellipsis-end")),e})(),(0,n.jsx)(f,{className:"cursor-pointer",children:(0,n.jsx)(p,{onClick:()=>{l<o.length&&a(l+1)}})})]})})})}m.displayName="PaginationEllipsis"},75309:function(e,t,r){var n=r(75376);r(32486),t.Z=e=>{let{totalPosts:t,postsPerPage:r,setCurrentPage:l,currentPage:a}=e,o=[];for(let e=1;e<=Math.ceil(t/r);e++)o.push(e);return(0,n.jsx)("div",{className:"flex flex-wrap justify-center mt-[1rem] md:hidden",children:o.map(e=>(0,n.jsx)("button",{onClick:()=>l(e),className:"".concat(e===a?"bg-gradient-to-b from-[#8860F8] to-[#7141F8] text-[#fff]":""," w-[38px] h-[38px] text-[16px] my-0 mx-[6px] cursor-pointer bg-[#E3D9FE] border border-[#7141F8] transition-all duration-300 ease-in rounded-md"),children:e},e))})}},65593:function(e,t,r){var n=r(75376);r(32486);var l=r(73003);t.Z=()=>(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen ",children:(0,n.jsx)(l.Z,{width:"32",height:"32",color:"#8055F8"})})},10952:function(e,t,r){r.d(t,{Gl:function(){return i},an:function(){return c},jx:function(){return d},xo:function(){return s}});var n=r(20818),l=r(13352),a=r(18648);let o=a.env.NEXT_PUBLIC_BASE_URL;a.env.NEXT_PUBLIC_INTEGRATION_URL;let i=async(e,t)=>{try{return await n.Z.get(o+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var r,l,a;return((null==e?void 0:null===(r=e.response)||void 0===r?void 0:r.status)===401||(null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(l=a.data)||void 0===l?void 0:l.status_code)===401)&&(localStorage.clear(),window.location.href="/auth/login"),e}},s=async(e,t,r)=>{try{return await n.Z.post(o+e,t,{headers:{Authorization:"Bearer ".concat(r),"Content-Type":"application/json"}})}catch(e){var a,i,s,c,d;return l.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(a=i.data)||void 0===a?void 0:a.message),(null==e?void 0:null===(s=e.response)||void 0===s?void 0:s.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(c=d.data)||void 0===c?void 0:c.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},c=async(e,t,r)=>{try{return await n.Z.put(o+e,t,{headers:{Authorization:"Bearer ".concat(r),"Content-Type":"application/json"}})}catch(e){var a,i,s,c,d;return l.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(a=i.data)||void 0===a?void 0:a.message),(null==e?void 0:null===(s=e.response)||void 0===s?void 0:s.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(c=d.data)||void 0===c?void 0:c.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},d=async(e,t)=>{try{return await n.Z.delete(o+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var r,a,i,s,c;return l.Z.error(null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(r=a.data)||void 0===r?void 0:r.message),(null==e?void 0:null===(i=e.response)||void 0===i?void 0:i.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(s=c.data)||void 0===s?void 0:s.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}}},29626:function(e,t,r){r.d(t,{F:function(){return a},e:function(){return o}});var n=r(32486);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let r=!1,n=e.map(e=>{let n=l(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():l(e[t],null)}}}}function o(...e){return n.useCallback(a(...e),e)}},91007:function(e,t,r){r.d(t,{Z8:function(){return o},g7:function(){return i},sA:function(){return c}});var n=r(32486),l=r(29626),a=r(75376);function o(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...a}=e;if(n.isValidElement(r)){let e,o;let i=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,s=function(e,t){let r={...t};for(let n in t){let l=e[n],a=t[n];/^on[A-Z]/.test(n)?l&&a?r[n]=(...e)=>{let t=a(...e);return l(...e),t}:l&&(r[n]=l):"style"===n?r[n]={...l,...a}:"className"===n&&(r[n]=[l,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==n.Fragment&&(s.ref=t?(0,l.F)(t,i):i),n.cloneElement(r,s)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:l,...o}=e,i=n.Children.toArray(l),s=i.find(d);if(s){let e=s.props.children,l=i.map(t=>t!==s?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,a.jsx)(t,{...o,ref:r,children:l})});return r.displayName=`${e}.Slot`,r}var i=o("Slot"),s=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=s,t}function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}},53447:function(e,t,r){r.d(t,{j:function(){return o}});var n=r(89824);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.W,o=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:o,defaultVariants:i}=t,s=Object.keys(o).map(e=>{let t=null==r?void 0:r[e],n=null==i?void 0:i[e];if(null===t)return null;let a=l(t)||l(n);return o[e][a]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return a(e,s,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...l}=t;return Object.entries(l).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...c}[t]):({...i,...c})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}}]);