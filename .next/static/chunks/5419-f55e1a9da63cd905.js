"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5419],{36221:function(e,t,l){l.d(t,{r:function(){return o}});var i=l(75376),n=l(32486),a=l(46962);function o(e){let{textToCopy:t,tooltipText:l="Copied",children:o}=e,[u,d]=(0,n.useState)(!1),s=async()=>{try{await navigator.clipboard.writeText(t),d(!0),setTimeout(()=>d(!1),2e3)}catch(e){console.error("Failed to copy text:",e)}};return(0,i.jsx)(a.pn,{children:(0,i.jsxs)(a.u,{open:u,children:[(0,i.jsx)(a.aJ,{asChild:!0,children:(0,i.jsx)("div",{onClick:s,className:"cursor-pointer inline-flex",children:o})}),(0,i.jsx)(a._v,{side:"top",className:"bg-black text-white",children:l})]})})}},71240:function(e,t,l){l.d(t,{Z:function(){return O}});var i=l(75376),n=l(32486),a=l(72742),o=l(22641),u=l(49107),d=l(28904),s=l(82310),D=l(72593),r=l(59632),c=l(76892),v=l(22397),E=l(41102),p=l(75148),h=l(35602),g=l(9274),m=l(60601),f=l(40497),x=l(79624),y=l(39713),_=l(40708),b=l(97220),j=l(86418),A=l(47411),C=l(92287),N=l(65470),k=l(16006),T=l(25575),w=l(11492),S=l(99402),I=l(89863),L=l(73003),R=l(97712),P=l(74178),U=l(409),Z=l.n(U),F=l(60971),z=l.n(F),M=e=>{let{state:t}=(0,n.useContext)(b.R);return{handleTyping:z()(l=>{if(e){var i,n;null==e||e.publish({user:{id:null==t?void 0:null===(i=t.user)||void 0===i?void 0:i.id,username:null==t?void 0:null===(n=t.user)||void 0===n?void 0:n.username},typing:l,type:"typing"})}},50)}};let B={":-)":"\uD83D\uDE0A",":)":"\uD83D\uDE0A","(:":"\uD83D\uDE0A","=)":"\uD83D\uDE0A",":-D":"\uD83D\uDE00",":D":"\uD83D\uDE00",xD:"\uD83D\uDE06",XD:"\uD83D\uDE06",":-(":"\uD83D\uDE1E",":(":"\uD83D\uDE1E","):":"\uD83D\uDE1E","=(":"\uD83D\uDE1E",";-)":"\uD83D\uDE09",";)":"\uD83D\uDE09",";]":"\uD83D\uDE09","*-)":"\uD83D\uDE09",":-P":"\uD83D\uDE1B",":P":"\uD83D\uDE1B",":-p":"\uD83D\uDE1B",":p":"\uD83D\uDE1B",":-o":"\uD83D\uDE2E",":o":"\uD83D\uDE2E",":-O":"\uD83D\uDE2E",":O":"\uD83D\uDE2E",":-*":"\uD83D\uDE18",":*":"\uD83D\uDE18",":-x":"\uD83D\uDE18",":x":"\uD83D\uDE18",":'(":"\uD83D\uDE22",":'‑(":"\uD83D\uDE22",":'‑)":"\uD83D\uDE02",":'D":"\uD83D\uDE02",":-/":"\uD83D\uDE15",":/":"\uD83D\uDE15",":-\\":"\uD83D\uDE15",":\\":"\uD83D\uDE15",":-|":"\uD83D\uDE10",":|":"\uD83D\uDE10",":-]":"\uD83D\uDE10",":]":"\uD83D\uDE10","<3":"❤️","</3":"\uD83D\uDC94","<\\3":"\uD83D\uDC94","♥":"❤️",":3":"\uD83D\uDE3A",":-3":"\uD83D\uDE3A",":>":"\uD83D\uDE3A",":^)":"\uD83D\uDE04","O:)":"\uD83D\uDE07","O:-)":"\uD83D\uDE07","0:)":"\uD83D\uDE07","0:-)":"\uD83D\uDE07",">:(":"\uD83D\uDE20",">:-(":"\uD83D\uDE20","D:<":"\uD83D\uDE20",D8:"\uD83D\uDE31","D-:<":"\uD83D\uDE20","D':":"\uD83D\uDE31",">:O":"\uD83D\uDE21",">:o":"\uD83D\uDE21",":-$":"\uD83E\uDD10",":$":"\uD83E\uDD10",":-#":"\uD83E\uDD10",":#":"\uD83E\uDD10",":-X":"\uD83E\uDD10",":X":"\uD83E\uDD10",":-!":"\uD83E\uDD10","B-)":"\uD83D\uDE0E","B)":"\uD83D\uDE0E","8-)":"\uD83D\uDE0E","8)":"\uD83D\uDE0E",":-B":"\uD83D\uDE0E",":-b":"\uD83D\uDE0E","|-)":"\uD83D\uDE34","|)":"\uD83D\uDE34","-_-":"\uD83D\uDE11","=_=":"\uD83D\uDE11",">.<":"\uD83D\uDE23",">_>":"\uD83D\uDE12","<_<":"\uD83D\uDE12","-.-":"\uD83D\uDE12",u_u:"\uD83D\uDE14","^_^":"\uD83D\uDE0A","^.^":"\uD83D\uDE0A","^-^":"\uD83D\uDE0A",":‑]":"\uD83D\uDE42","=]":"\uD83D\uDE42",":}":"\uD83D\uDE42",":-}":"\uD83D\uDE42",":')":"\uD83D\uDE02",":‑')":"\uD83D\uDE02",":‑))":"\uD83D\uDE02",":’-D":"\uD83D\uDE02",":L":"\uD83D\uDE15",":S":"\uD83D\uDE16",":Z":"\uD83D\uDE36",":T":"\uD83D\uDE24","D:":"\uD83D\uDE27",DX:"\uD83D\uDE27",QQ:"\uD83D\uDE2D",T_T:"\uD83D\uDE2D","T-T":"\uD83D\uDE2D",TT:"\uD83D\uDE2D",":-@":"\uD83D\uDE21",":@":"\uD83D\uDE21",">:D":"\uD83D\uDE08",">;]":"\uD83D\uDE08",">:)":"\uD83D\uDE08",">:3":"\uD83D\uDE08","<(^_^)>":"\uD83E\uDD17","<(o_o<)":"\uD83E\uDD17","(>o_o)>":"\uD83E\uDD17","(>'-')>":"\uD83E\uDD17","<('-'<)":"\uD83E\uDD17","ヽ(•‿•)ノ":"\uD83D\uDE0A","(╯\xb0□\xb0）╯︵ ┻━┻":"\uD83D\uDE21","┬─┬ ノ( ゜-゜ノ)":"\uD83D\uDE0C","(☞ﾟヮﾟ)☞":"\uD83D\uDC49","☜(ﾟヮﾟ☜)":"\uD83D\uDC48","(づ｡◕‿‿◕｡)づ":"\uD83E\uDD17","(~_^)":"\uD83D\uDE09","(^_−)☆":"\uD83D\uDE09","(^_^)":"\uD83D\uDE0A","(^.^)":"\uD83D\uDE0A","(-_-)":"\uD83D\uDE34","(\xac_\xac)":"\uD83D\uDE12","(ಠ_ಠ)":"\uD83D\uDE11","(ʘ‿ʘ)":"\uD83D\uDE33","(ಥ﹏ಥ)":"\uD83D\uDE2D","(\xac‿\xac)":"\uD83D\uDE0F","(ง •̀_•́)ง":"\uD83D\uDCAA"};var O=e=>{let{subscription:t,sendMessage:l,show:U=!0}=e,{state:F,dispatch:z}=(0,n.useContext)(b.R),O=(0,A.useParams)().id,G=(0,C.AE)(),[X,H]=(0,n.useState)(!1),[Y,$]=(0,n.useState)(""),[K,q]=(0,n.useState)(""),[J,Q]=(0,n.useState)(!1),[V,W]=(0,n.useState)(!0),ee=(0,n.useRef)(null),[et,el]=(0,n.useState)([]),[ei,en]=(0,n.useState)([]),[ea,eo]=(0,n.useState)([]),{handleTyping:eu}=M(t);(0,n.useEffect)(()=>()=>{et.forEach(e=>URL.revokeObjectURL(e.preview))},[et]);let{editor:ed,isEmpty:es}=(0,_.Z)(e=>{let t={id:Date.now()+Math.random(),file:e,type:"image",preview:URL.createObjectURL(e)};el(e=>[...e,t]),eo(e=>[...e,t.id]);let l=new FormData;l.append("files",e),(0,j.x9)("/files/upload-files",l).then(e=>{var t;let l=null==e?void 0:null===(t=e.data)||void 0===t?void 0:t.data[0];l&&ed&&en(e=>[...e,l])}).catch(e=>{console.error("Image paste upload failed",e)}).finally(()=>{eo(e=>e.filter(e=>e!==t.id))})}),eD=async e=>{let t=e.target.files;if(!t)return;let l=Array.from(t).map(e=>{let t=e.type.split("/")[0];return{id:Date.now()+Math.random(),file:e,type:t,preview:"image"===t||"video"===t?URL.createObjectURL(e):null}});for(let e of(el(e=>[...e,...l]),l)){eo(t=>[...t,e.id]);let t=new FormData;t.append("files",e.file);try{var i;let e=await (0,j.x9)("/files/upload-files",t);(null==e?void 0:null===(i=e.data)||void 0===i?void 0:i.data)&&en(t=>[...t,...e.data.data])}catch(e){console.error("Upload failed",e)}finally{eo(t=>t.filter(t=>t!==e.id))}}},er=e=>{el(t=>t.filter(l=>l!==t[e])),en(t=>t.filter((t,l)=>l!==e))},ec=e=>{let t=e;for(let e in B){let l=RegExp(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"g");t=t.replace(l,B[e])}return t},ev=async e=>{if(e.preventDefault(),!ed)return;let i=ed.getHTML(),n=ed.getText().trim(),a=n.length>0,o=ei.length>0;if(!a&&!o)return;let u=ec(n),d=Z().shortnameToUnicode(u);i=i.replace(n,d),t?(ed.commands.clearContent(),el([]),en([]),l(O,G,i,ei),z({type:R.a.CLEAR_MENTIONS}),eu(!1)):console.log("No connection detected")};return(0,n.useEffect)(()=>{ed&&ed.commands.focus()},[ed]),(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)("div",{onClick:()=>ed&&ed.commands.focus(),className:"bg-white border rounded-xl mx-3 md:mx-5 border-[#E6EAEF] overflow-hidden ".concat((null==F?void 0:F.reply)?"right-[520px]":"right-0"," ").concat((null==ed?void 0:ed.isFocused)?"border-primary-400":"border-gray-200"),children:[V&&(0,i.jsxs)("div",{className:"border-b border-[#E6EAEF] flex items-center gap-2 bg-[#F9FAFB] pl-3 pr-4 py-[5px]",children:[(0,i.jsx)("button",{onClick:()=>null==ed?void 0:ed.chain().focus().toggleBold().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==ed?void 0:ed.isActive("bold"))?"bg-gray-200 font-semibold text-black":""),children:(0,i.jsx)(a.Z,{size:18,color:(null==ed?void 0:ed.isActive("bold"))?"#444444":"#CACACA"})}),(0,i.jsx)("button",{onClick:()=>null==ed?void 0:ed.chain().focus().toggleItalic().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==ed?void 0:ed.isActive("italic"))?"bg-gray-200 font-semibold text-black":""),children:(0,i.jsx)(o.Z,{size:18,color:(null==ed?void 0:ed.isActive("italic"))?"#444444":"#CACACA"})}),(0,i.jsx)("button",{onClick:()=>null==ed?void 0:ed.chain().focus().toggleStrike().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==ed?void 0:ed.isActive("strike"))?"bg-gray-200 font-semibold text-black":""),children:(0,i.jsx)(u.Z,{size:18,color:(null==ed?void 0:ed.isActive("strike"))?"#444444":"#CACACA"})}),(0,i.jsx)("div",{className:"w-px h-5 bg-[#E6EAEF]"}),(0,i.jsxs)(k.Vq,{open:X,onOpenChange:H,children:[(0,i.jsx)(k.hg,{asChild:!0,children:(0,i.jsx)("button",{onClick:()=>H(!0),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==ed?void 0:ed.isActive("link"))?"bg-gray-200 font-semibold text-black":""),children:(0,i.jsx)(d.Z,{size:18,color:(null==ed?void 0:ed.isActive("link"))?"#444444":"#CACACA"})})}),(0,i.jsxs)(k.cZ,{className:"w-full max-w-md",children:[(0,i.jsx)(k.fK,{children:(0,i.jsx)(k.$N,{className:"font-semibold",children:"Add link"})}),(0,i.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium",children:"Text"}),(0,i.jsx)(T.I,{value:Y,onChange:e=>$(e.target.value),placeholder:"Enter link text"}),(0,i.jsx)("label",{className:"text-sm font-medium mt-2",children:"Link"}),(0,i.jsx)(T.I,{value:K,onChange:e=>q(e.target.value),placeholder:"Enter URL",type:"url"})]}),(0,i.jsxs)(k.cN,{className:"mt-4 flex justify-end gap-2",children:[(0,i.jsx)(w.z,{variant:"outline",onClick:()=>H(!1),children:"Cancel"}),(0,i.jsx)(w.z,{onClick:()=>{Y&&K&&(null==ed||ed.chain().focus().insertContent('<a href="'.concat(K,'" target="_blank" rel="noopener noreferrer">').concat(Y,"</a>")).run(),H(!1),$(""),q(""))},disabled:!Y||!K,className:"bg-blue-500 text-white px-10",children:"Save"})]})]})]}),(0,i.jsx)("button",{onClick:()=>null==ed?void 0:ed.chain().focus().toggleOrderedList().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==ed?void 0:ed.isActive("orderedList"))?"bg-gray-200 font-semibold text-black":""),children:(0,i.jsx)(s.Z,{size:18,color:(null==ed?void 0:ed.isActive("orderedList"))?"#444444":"#CACACA"})}),(0,i.jsx)("button",{onClick:()=>null==ed?void 0:ed.chain().focus().toggleBulletList().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==ed?void 0:ed.isActive("bulletList"))?"bg-gray-200 font-semibold text-black":""),children:(0,i.jsx)(D.Z,{size:18,color:(null==ed?void 0:ed.isActive("bulletList"))?"#444444":"#CACACA"})}),(0,i.jsx)("div",{className:"w-px h-5 bg-[#E6EAEF]"}),(0,i.jsx)("button",{onClick:()=>null==ed?void 0:ed.chain().focus().toggleCode().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==ed?void 0:ed.isActive("code"))?"bg-gray-200 font-semibold text-black":""),children:(0,i.jsx)(r.Z,{size:18,color:(null==ed?void 0:ed.isActive("code"))?"#444444":"#CACACA"})})]}),(0,i.jsxs)("div",{className:"md:flex-1 relative px-3",children:[(0,i.jsx)(N.kg,{editor:ed,className:"py-2 rounded-md flex flex-row overflow-auto",onKeyDown:e=>{"Enter"===e.key&&(e.shiftKey?(e.preventDefault(),null==ed||ed.commands.enter()):(e.preventDefault(),ev(e)))}}),(0,i.jsx)("div",{className:"flex gap-3 ".concat((null==et?void 0:et.length)>0?"mt-3":""),children:null==et?void 0:et.map((e,t)=>(0,i.jsxs)("div",{className:"relative w-[70px] h-[70px]",children:["image"===e.type?(0,i.jsx)(y.default,{src:e.preview,alt:"Uploaded ".concat(t),width:70,height:70,className:"w-[70px] h-[70px] rounded-md object-cover border border-primary-400 cursor-pointer"}):"application"===e.type?(0,i.jsx)("div",{className:"w-[70px] h-[70px] flex items-center justify-center border border-primary-500 rounded-md bg-gray-100",children:(0,i.jsxs)("a",{href:URL.createObjectURL(e.file),target:"_blank",rel:"noopener noreferrer",className:"flex flex-col items-center",children:[(0,i.jsx)(c.Z,{size:24,color:"#606060"}),(0,i.jsx)("span",{className:"text-xs text-blue-500 mt-1",children:"PDF"})]})}):null,(0,i.jsx)("button",{onClick:()=>er(t),className:"absolute -top-1 -right-2 p-1 bg-gray-500 text-white rounded-full w-5 h-5 text-xs flex items-center justify-center",children:(0,i.jsx)(v.Z,{size:14})}),ea.includes(null==e?void 0:e.id)&&(0,i.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-50",children:(0,i.jsx)(L.Z,{})})]},t))})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 py-2 pl-3 pr-4",children:[(0,i.jsxs)("button",{onClick:()=>{var e;return null===(e=ee.current)||void 0===e?void 0:e.click()},className:"p-1.5 hover:bg-gray-100 rounded-full bg-[#F2F4F7]",children:[(0,i.jsx)("input",{type:"file",ref:ee,style:{display:"none"},onChange:eD,accept:"image/*, application/pdf",multiple:!0}),(0,i.jsx)(E.Z,{size:18,color:"#606060"})]}),(0,i.jsx)("button",{title:"show formatting",onClick:()=>W(e=>!e),className:"p-1.5 hover:bg-gray-100 rounded text-[#606060] underline",children:"Aa"}),(0,i.jsx)("div",{className:"relative",children:(0,i.jsxs)(P.J2,{open:J,onOpenChange:Q,children:[(0,i.jsx)(P.xo,{asChild:!0,children:(0,i.jsx)("button",{onClick:e=>e.stopPropagation(),className:"p-1.5 hover:bg-gray-100 rounded",children:(0,i.jsx)(p.Z,{size:18,color:"#606060"})})}),(0,i.jsx)(P.yk,{className:"p-0 w-full max-w-xs",children:(0,i.jsx)(S.Z,{data:I,onEmojiSelect:e=>{ed?(ed.chain().focus().insertContent(null==e?void 0:e.native).run(),Q(!1)):console.log("editor not available")}})})]})}),(0,i.jsx)("button",{onClick:()=>{null==ed||ed.chain().focus().insertContent("@").run()},className:"p-1.5 hover:bg-gray-100 rounded",children:(0,i.jsx)(h.Z,{size:18,color:"#606060"})}),U&&(0,i.jsxs)(n.Fragment,{children:[(0,i.jsx)("div",{className:"w-px h-5 bg-[#E6EAEF] hidden sm:flex"}),(0,i.jsx)("button",{className:"p-1.5 hover:bg-gray-100 rounded hidden sm:flex",children:(0,i.jsx)(g.Z,{size:18,color:"#606060"})}),(0,i.jsx)("button",{className:"p-1.5 hover:bg-gray-100 rounded hidden sm:flex",children:(0,i.jsx)(m.Z,{size:18,color:"#606060"})}),(0,i.jsx)("div",{className:"w-px h-5 bg-[#E6EAEF]"}),(0,i.jsx)("button",{className:"p-1.5 hover:bg-gray-100 rounded hidden sm:flex",children:(0,i.jsx)(f.Z,{size:18,color:"#606060",className:"[&>path]:stroke-[2.5]"})})]})]}),(0,i.jsx)("div",{className:"flex items-center gap-1 py-2 pl-3 pr-4",children:(0,i.jsx)("button",{type:"submit",className:"p-1.5 hover:bg-gray-100 rounded size-8 flex items-center justify-center",onClick:ev,disabled:es&&(null==et?void 0:et.length)===0,children:(0,i.jsx)(x.Z,{color:es&&(null==et?void 0:et.length)===0?"#999":"black"})})})]})]})})}},11648:function(e,t,l){var i=l(47411),n=l(32486),a=l(97712),o=l(97220),u=l(86418);t.Z=()=>{let e=(0,i.useParams)().id,{state:t,dispatch:l}=(0,n.useContext)(o.R),d=localStorage.getItem("token")||"",[s,D]=(0,n.useState)(1),[r,c]=(0,n.useState)(!0),[v,E]=(0,n.useState)(!0),p=async function(){let i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;try{let d=await (0,u.Gl)("/dms/channels/".concat(e,"/threads?page=").concat(i,"&limit=50"));if((null==d?void 0:d.status)===200||(null==d?void 0:d.status)===201){var n,o;let e=Array.isArray(null===(n=d.data)||void 0===n?void 0:n.data)?null===(o=d.data)||void 0===o?void 0:o.data:[],u=(null==t?void 0:t.threads)||[];l({type:a.a.CHATS,payload:{newThreads:[...u,...e],newPage:i}}),c([...u,...e].length>50)}E(!1)}catch(e){console.error("Error fetching threads:",e),c(!1)}finally{D(i)}};return(0,n.useEffect)(()=>{e&&d&&p(1).finally(()=>l({type:a.a.MESSAGE_LOADING,payload:!1}))},[e,d,l]),{fetchMoreData:()=>{r&&p(s+1)},hasMore:r,loading:v}}},69518:function(e,t,l){l.d(t,{Z:function(){return r}});var i=l(75376),n=l(32486),a=l(97220),o=l(20818),u=l(53465),d=l(97712),s=l(47411),D=l(18648);function r(){let e=(0,s.useParams)().id,{dispatch:t}=(0,n.useContext)(a.R),l=D.env.NEXT_PUBLIC_CONNECT_URL,r=async()=>{let e=localStorage.getItem("token")||"";return(await o.Z.get("".concat(D.env.NEXT_PUBLIC_BASE_URL,"/token/connection"),{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}})).data.data.token},c=async e=>{let t=localStorage.getItem("token")||"";return(await o.Z.post("".concat(D.env.NEXT_PUBLIC_BASE_URL,"/token/subscription"),{channel:e},{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})).data.data.token};return(0,n.useEffect)(()=>{if(e){let i=new u.qX(l,{getToken:r,debug:!0});i.on("connect",()=>{console.log("Connected to Centrifuge")}),i.on("disconnect",()=>{console.log("Disconnected from Centrifuge")});let n=async()=>c(e),a=i.newSubscription(e,{getToken:n});return a.on("publication",e=>{var l,i,n,a,o,u,s,D,r,c,v,E,p,h,g,m,f,x,y,_;let b=null==e?void 0:e.data;if(t({type:d.a.AGENT_STATE,payload:null==e?void 0:e.data}),(null==e?void 0:null===(l=e.data)||void 0===l?void 0:l.type)==="message"&&t({type:d.a.CHATS,payload:{newMessage:e.data,isRealTime:!0}}),(null==b?void 0:b.section)==="channels_section"&&(null==b?void 0:b.notification_type)==="reply_count_change"){let l=null==e?void 0:null===(i=e.data)||void 0===i?void 0:i.data,a=null==e?void 0:null===(n=e.data)||void 0===n?void 0:n.update_change;t({type:d.a.UPDATE_DM_MESSAGE_THREAD,payload:{threadId:l.thread_id,reply:l,updates:a}})}if((null==b?void 0:b.section)==="reply_message"&&(null==b?void 0:b.notification_type)==="deleted"){let l=null==e?void 0:null===(a=e.data)||void 0===a?void 0:a.modification_ids,i=null==e?void 0:null===(o=e.data)||void 0===o?void 0:o.update_change;t({type:d.a.DELETE_DM_THREAD_REPLY,payload:{threadId:l.thread_id,messageId:null==l?void 0:l.message_id,updates:i}})}if((null==b?void 0:b.section)==="thread_message"&&(null==b?void 0:b.notification_type)==="deleted"){let l=null==e?void 0:null===(u=e.data)||void 0===u?void 0:u.modification_ids;t({type:d.a.DELETE_DM_MESSAGE,payload:{threadId:l.thread_id}})}if((null==b?void 0:b.section)==="thread_message"&&(null==b?void 0:b.notification_type)==="updated"){let l=null==e?void 0:null===(s=e.data)||void 0===s?void 0:s.data;t({type:d.a.EDIT_DM_MESSAGE,payload:{threadId:l.thread_id,newMessageData:l}})}if((null==b?void 0:b.section)==="reply_message"&&(null==b?void 0:b.notification_type)==="updated"){let l=null==e?void 0:null===(D=e.data)||void 0===D?void 0:D.data,i=null==e?void 0:null===(c=e.data)||void 0===c?void 0:null===(r=c.modification_ids)||void 0===r?void 0:r.message_id;t({type:d.a.EDIT_REPLY_MESSAGE,payload:{threadId:i,newMessageData:l}})}if((null==b?void 0:b.section)==="thread_message"&&(null==b?void 0:b.notification_type)==="pinned_message_event"){let l=null==e?void 0:null===(v=e.data)||void 0===v?void 0:v.modification_ids,i=null==e?void 0:null===(E=e.data)||void 0===E?void 0:E.pinned_details;t({type:d.a.UPDATE_DM_PIN,payload:{threadId:l.thread_id,is_pin:!0,details:i}})}if((null==b?void 0:b.section)==="reply_message"&&(null==b?void 0:b.notification_type)==="pinned_message_event"){let l=null==e?void 0:null===(p=e.data)||void 0===p?void 0:p.modification_ids,i=null==e?void 0:null===(h=e.data)||void 0===h?void 0:h.pinned_details;t({type:d.a.UPDATE_REPLY_PIN,payload:{threadId:l.message_id,is_pin:!0,details:i}})}if((null==b?void 0:b.section)==="thread_message"&&(null==b?void 0:b.notification_type)==="unpinned_message_event"){let l=null==e?void 0:null===(g=e.data)||void 0===g?void 0:g.modification_ids;t({type:d.a.UPDATE_DM_PIN,payload:{threadId:l.thread_id,is_pin:!1}})}if((null==b?void 0:b.section)==="reply_message"&&(null==b?void 0:b.notification_type)==="unpinned_message_event"){let l=null==e?void 0:null===(m=e.data)||void 0===m?void 0:m.modification_ids;t({type:d.a.UPDATE_REPLY_PIN,payload:{threadId:l.message_id,is_pin:!1}})}if((null==b?void 0:b.section)==="thread_message"&&(null==b?void 0:b.notification_type)==="reaction_event"){let l=null==e?void 0:null===(f=e.data)||void 0===f?void 0:f.modification_ids,i=null==e?void 0:null===(x=e.data)||void 0===x?void 0:x.reactions;t({type:d.a.UPDATE_DM_REACTIONS,payload:{threadId:l.thread_id,reactions:i}})}if((null==b?void 0:b.section)==="reply_message"&&(null==b?void 0:b.notification_type)==="reaction_event"){let l=null==e?void 0:null===(y=e.data)||void 0===y?void 0:y.modification_ids,i=null==e?void 0:null===(_=e.data)||void 0===_?void 0:_.reactions;t({type:d.a.UPDATE_REPLY_REACTIONS,payload:{messageId:l.message_id,reactions:i}})}}),a.on("error",e=>{console.error("Subscription error: ".concat(e.message))}),i.connect(),a.subscribe(),t({type:d.a.CHAT_SUBSCRIPTION,payload:a}),()=>{a.unsubscribe(),i.disconnect()}}},[e,l,t]),(0,i.jsx)(i.Fragment,{})}}}]);