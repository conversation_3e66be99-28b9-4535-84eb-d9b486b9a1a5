"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5164],{40461:function(e,s,t){t.d(s,{Z:function(){return eo}});var l=t(75376),a=t(32486),i=t(97220),n=t(97712),r=t(71240),o=t(22397),d=t(3371),c=t(6769),x=t(95578),u=t(47720),p=t(39713),m=t(56792),h=t(45838),v=t(74178),g=t(99402),j=t(89863),b=t(43806),y=t(5426),f=t(48324),N=t(2336),w=t(81356),C=t(37847),_=t(23633),Z=t(82301),k=t(21920),A=t(64632),E=t(16006),z=t(11492),S=t(72738),L=t.n(S),R=t(47411),P=t(86418),D=t(73003),I=e=>{let{open:s,setOpen:t}=e,{state:n}=(0,a.useContext)(i.R),{threadReply:r}=n,d=(0,R.useParams)().id,[c,x]=(0,a.useState)(!1),u=async()=>{x(!0);let e=await (0,P.jx)("/channels/".concat(d,"/messages/").concat(null==r?void 0:r.id));((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201)&&t(!1),x(!1)};return(0,l.jsx)(E.Vq,{open:s,onOpenChange:t,children:(0,l.jsxs)(E.cZ,{className:"sm:max-w-[550px] rounded-lg p-0 overflow-hidden",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-b border-gray-200",children:[(0,l.jsx)(E.$N,{className:"text-lg font-semibold",children:"Delete message"}),(0,l.jsx)("button",{onClick:()=>t(!1),className:"text-gray-500 hover:text-gray-700 transition","aria-label":"Close",children:(0,l.jsx)(o.Z,{className:"w-5 h-5"})})]}),(0,l.jsx)("div",{className:"px-6 pt-4 text-sm text-gray-700",children:"Are you sure you want to delete this message? This cannot be undone."}),(0,l.jsx)("div",{className:"px-6 py-4 max-h-[400px] overflow-auto",children:(0,l.jsxs)("div",{className:"border border-gray-200 rounded p-3 flex gap-3 items-start bg-white",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded flex items-center justify-center text-white text-sm font-bold",children:(0,l.jsx)(p.default,{src:(null==r?void 0:r.avatar_url)?null==r?void 0:r.avatar_url:(null==r?void 0:r.user_type)=="user"||(null==r?void 0:r.user_type)===""?null===m.Z||void 0===m.Z?void 0:m.Z.user:null===m.Z||void 0===m.Z?void 0:m.Z.bot,alt:"avatar",width:32,height:32,className:"rounded"})}),(0,l.jsxs)("div",{className:"flex flex-col gap-[2px] text-sm w-full",children:[(0,l.jsxs)("div",{className:"flex gap-2 items-baseline",children:[(0,l.jsx)("span",{className:"font-bold text-gray-800",children:null==r?void 0:r.username}),(0,l.jsx)("span",{className:"text-xs text-gray-500",children:L()(null==r?void 0:r.created_at).calendar()})]}),(0,l.jsx)(h.Z,{item:r})]})]})}),(0,l.jsxs)(E.cN,{className:"px-6 pb-4 pt-2",children:[(0,l.jsx)(z.z,{variant:"outline",className:"rounded-md text-sm px-4 py-2 border border-gray-300",onClick:()=>t(!1),children:"Cancel"}),(0,l.jsxs)(z.z,{className:"bg-rose-600 hover:bg-rose-700 text-white text-sm px-4 py-2 rounded-md shadow-sm",onClick:u,children:["Delete ",c&&(0,l.jsx)(D.Z,{})]})]})]})})},T=e=>{let{open:s,setOpen:t}=e,{state:n}=(0,a.useContext)(i.R),{threadReply:r}=n,d=(0,R.useParams)().id,[c,x]=(0,a.useState)(!1),u=async()=>{x(!0),await (0,P.i1)("/channels/pin/".concat(d,"/message/").concat(null==r?void 0:r.id)),t(!1),x(!1)};return(0,l.jsx)(E.Vq,{open:s,onOpenChange:t,children:(0,l.jsxs)(E.cZ,{className:"sm:max-w-[550px] rounded-lg p-0 overflow-hidden",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between px-4 sm:px-6 py-4 border-b border-gray-200",children:[(0,l.jsx)(E.$N,{className:"text-lg font-semibold",children:"Remove pinned item"}),(0,l.jsx)("button",{onClick:()=>t(!1),className:"text-gray-500 hover:text-gray-700 transition","aria-label":"Close",children:(0,l.jsx)(o.Z,{className:"w-5 h-5"})})]}),(0,l.jsx)("div",{className:"px-4 sm:px-6 pt-4 text-sm text-gray-700",children:"Are you sure you want to remove this pinned item."}),(0,l.jsx)("div",{className:"px-4 sm:px-6 py-4 max-h-[400px] overflow-auto",children:(0,l.jsxs)("div",{className:"border border-gray-200 rounded p-3 flex gap-3 items-start bg-white",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded flex items-center justify-center text-white text-sm font-bold",children:(0,l.jsx)(p.default,{src:(null==r?void 0:r.avatar_url)?null==r?void 0:r.avatar_url:(null==r?void 0:r.user_type)=="user"||(null==r?void 0:r.user_type)===""?null===m.Z||void 0===m.Z?void 0:m.Z.user:null===m.Z||void 0===m.Z?void 0:m.Z.bot,alt:"avatar",width:32,height:32,className:"rounded"})}),(0,l.jsxs)("div",{className:"flex flex-col gap-[2px] text-sm w-full",children:[(0,l.jsxs)("div",{className:"flex gap-2 items-baseline",children:[(0,l.jsx)("span",{className:"font-bold text-gray-800",children:null==r?void 0:r.username}),(0,l.jsx)("span",{className:"text-xs text-gray-500",children:L()(null==r?void 0:r.created_at).calendar()})]}),(0,l.jsx)(h.Z,{item:r})]})]})}),(0,l.jsxs)(E.cN,{className:"mb-5 px-4 sm:px-6 pb-4 pt-2",children:[(0,l.jsx)(z.z,{variant:"outline",className:"rounded-md text-sm px-4 py-2 border border-gray-300",onClick:()=>t(!1),children:"Cancel"}),(0,l.jsxs)(z.z,{className:"mb-5 bg-rose-600 hover:bg-rose-700 text-white text-sm px-4 py-2 rounded-md shadow-sm",onClick:u,children:["Remove pinned item ",c&&(0,l.jsx)(D.Z,{})]})]})]})})};let O=e=>{let{item:s}=e,[t,r]=(0,a.useState)(!1),[o,d]=(0,a.useState)(!1),{state:c,dispatch:x}=(0,a.useContext)(i.R),{user:p}=c,m=(0,R.useParams)().id,[h,g]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=document.body;return t?e.classList.add("overflow-hidden"):e.classList.remove("overflow-hidden"),()=>{e.classList.remove("overflow-hidden")}},[t]);let j=async()=>{r(!1);let e={thread_id:s.thread_id,message_id:null==s?void 0:s.id};await (0,P.Q_)("/channels/pin/".concat(m,"/message"),e)};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)(v.J2,{open:t,onOpenChange:r,children:[(0,l.jsx)(v.xo,{asChild:!0,children:(0,l.jsx)("button",{onClick:e=>e.stopPropagation(),className:"py-[7px] px-[10px] hover:bg-gray-200 rounded","aria-label":"More options",children:(0,l.jsx)(y.Z,{size:18,className:"text-[#667085]"})})}),(0,l.jsx)(v.yk,{className:"max-width-[350px] p-0 bg-white border border-gray-200 rounded-md shadow-lg",align:"end",children:(0,l.jsxs)(b.E.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},transition:{duration:.1},className:"popover-content",children:[(0,l.jsx)("div",{className:"group flex items-center justify-between px-4 py-1 my-3 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(f.Z,{size:16}),(0,l.jsx)("span",{children:"Turn off notifications for replies"})]})}),(0,l.jsx)("hr",{}),(0,l.jsxs)("div",{className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(u.Z,{size:16}),(0,l.jsx)("span",{children:"Mark unread"})]}),(0,l.jsx)("span",{className:"ml-4 text-xs text-gray-400 group-hover:text-white",children:"U"})]}),(0,l.jsx)("hr",{}),(0,l.jsxs)("div",{className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(N.Z,{size:16}),(0,l.jsx)("span",{children:"Remind me about this"})]}),(0,l.jsx)("span",{className:"ml-4 text-xs text-gray-400 group-hover:text-white",children:">"})]}),(0,l.jsx)("hr",{}),(0,l.jsxs)("div",{className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(w.Z,{size:16}),(0,l.jsx)("span",{children:"Copy link"})]}),(0,l.jsx)("span",{className:"ml-4 text-xs text-gray-400 group-hover:text-white",children:"L"})]}),(0,l.jsx)("hr",{}),s.is_pinned?(0,l.jsxs)("div",{onClick:e=>{e.stopPropagation(),x({type:n.a.THREAD_REPLY,payload:s}),g(!0),r(!1)},className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(C.Z,{size:16}),(0,l.jsx)("span",{children:"Un-pin from channel"})]}),(0,l.jsx)("span",{className:"ml-4 text-xs text-gray-400 group-hover:text-white",children:"P"})]}):(0,l.jsxs)("div",{onClick:j,className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(C.Z,{size:16}),(0,l.jsx)("span",{children:"Pin to channel"})]}),(0,l.jsx)("span",{className:"ml-4 text-xs text-gray-400 group-hover:text-white",children:"P"})]}),(0,l.jsx)("hr",{}),(0,l.jsx)("div",{className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(_.Z,{size:16}),(0,l.jsx)("span",{children:"Start a huddle in thread..."})]})}),(0,l.jsx)("hr",{}),(null==p?void 0:p.user_id)===(null==s?void 0:s.user_id)&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{onClick:()=>{x({type:n.a.THREAD_REPLY,payload:s}),x({type:n.a.IS_EDIT_REPLY,payload:!0})},className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(Z.Z,{size:16}),(0,l.jsx)("span",{children:"Edit message"})]}),(0,l.jsx)("span",{className:"ml-4 text-xs text-gray-400 group-hover:text-white",children:"E"})]}),(0,l.jsx)("hr",{}),(0,l.jsxs)("div",{onClick:()=>{x({type:n.a.THREAD_REPLY,payload:s}),d(!0)},className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-red-500 hover:bg-red-500 hover:text-white cursor-pointer",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(k.Z,{size:16}),(0,l.jsx)("span",{children:"Delete message..."})]}),(0,l.jsx)("span",{className:"ml-4 text-xs group-hover:text-white",children:"delete"})]}),(0,l.jsx)("hr",{})]}),(0,l.jsx)("div",{className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(A.Z,{size:16}),(0,l.jsx)("span",{children:"More message shortcuts..."})]})})]})})]}),t&&(0,l.jsx)("div",{className:"fixed inset-0 bg-black/1 z-50",onClick:()=>r(!1),"aria-hidden":"true"}),(0,l.jsx)(I,{open:o,setOpen:d}),(0,l.jsx)(T,{open:h,setOpen:g})]})};var F=e=>{let{item:s}=e;return(0,l.jsx)(O,{item:s})},M=t(46962),B=t(82288),U=e=>{var s,t;let{item:r,shouldShowAvatar:o,setPopupId:d,popupId:b}=e,[y,f]=(0,a.useState)(!1),[N,w]=(0,a.useState)(!1),{state:C,dispatch:_}=(0,a.useContext)(i.R),{bookmarks:Z,user:k}=C,A=(0,R.useParams)().id,[E,z]=(0,a.useState)([]),S=(0,R.usePathname)(),L=null==Z?void 0:Z.some(e=>e.id===r.id),D=async e=>{f(!1);let s={thread_id:null==r?void 0:r.thread_id,message_id:null==r?void 0:r.id,type:"reply",reaction:e.native};await (0,P.xo)("/reactions/".concat(A),s),f(!1),w(!1)},I=async e=>{f(!1);let s={thread_id:null==r?void 0:r.thread_id,message_id:null==r?void 0:r.id,type:"reply",reaction:e};await (0,P.xo)("/reactions/".concat(A),s)},T=async()=>{let e=[...Z,{id:r.id}];_({type:n.a.BOOKMARKS,payload:e});let s=localStorage.getItem("orgId")||"",t={channels_id:A,message_id:r.id,thread_id:r.thread_id};await (0,P.Q_)("/organisations/".concat(s,"/message/save"),t)},O=async()=>{let e=Z.filter(e=>e.id!==r.id);_({type:n.a.BOOKMARKS,payload:e});let s=localStorage.getItem("orgId")||"";await (0,P.i1)("/organisations/".concat(s,"/saved/message/").concat(r.id))},U=async e=>{let s=await (0,P.Gl)("/reactions/".concat(e,"/thread/").concat(null==r?void 0:r.thread_id));if((null==s?void 0:s.status)===200||(null==s?void 0:s.status)===201){var t,l;z(null==s?void 0:null===(l=s.data)||void 0===l?void 0:null===(t=l.data)||void 0===t?void 0:t.usernames)}};return(0,l.jsxs)("div",{className:"h-[100%] relative group py-1 transition-colors flex items-start px-3  ".concat(r.is_pinned?"bg-yellow-50":L?"bg-primary-50":"hover:bg-gray-50"),children:[(0,l.jsx)("div",{className:"w-10 mr-2 flex items-center justify-center",children:o?(0,l.jsx)("div",{className:"relative size-9",children:(0,l.jsx)(p.default,{src:(null==r?void 0:r.avatar_url)?null==r?void 0:r.avatar_url:(null==r?void 0:r.user_type)=="user"||(null==r?void 0:r.user_type)===""?null===m.Z||void 0===m.Z?void 0:m.Z.user:null===m.Z||void 0===m.Z?void 0:m.Z.bot,alt:"avatar",width:100,height:100,objectFit:"cover",className:"rounded-[7px] border size-9 object-cover"})}):(0,l.jsx)("span",{className:"text-xs text-[#98A2B3] mt-1 opacity-0 group-hover:opacity-100 transition-opacity",children:new Date(null==r?void 0:r.created_at).toLocaleTimeString([],{hour:"numeric",minute:"2-digit",hour12:!0}).replace(/ am| pm/,"")})}),(0,l.jsxs)("div",{children:[o&&(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("span",{className:"font-bold text-[15px] text-[#1D2939]",children:(null==r?void 0:r.username)||(null==r?void 0:r.email)}),(0,l.jsx)("span",{className:"text-xs text-[#98A2B3]",children:new Date(null==r?void 0:r.created_at).toLocaleTimeString([],{hour:"numeric",minute:"2-digit",hour12:!0}).replace(/ AM| PM/,"")})]}),(0,l.jsx)("div",{className:"relative flex items-start justify-between",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(h.Z,{item:r}),(0,l.jsx)("span",{className:"text-[9px] text-neutral-500",children:(null==r?void 0:r.edited)?"(edited)":""})]})}),(0,l.jsxs)("div",{className:"flex flex-wrap items-center gap-2 rounded-md mt-1",children:[null==r?void 0:null===(s=r.reactions)||void 0===s?void 0:s.map((e,s)=>{let t=null==k?void 0:k.username,a=(E||[]).map(e=>e===t?"you":e),i="";if(0===a.length)i=" ";else if(1===a.length)i=a[0];else{let e=a[a.length-1],s=a.slice(0,-1).join(", ");i="".concat(s," and ").concat(e)}return(0,l.jsx)(M.pn,{children:(0,l.jsxs)(M.u,{children:[(0,l.jsx)(M.aJ,{asChild:!0,children:(0,l.jsxs)("div",{onMouseEnter:()=>U(e.reaction_id),onClick:s=>{I(e.reaction),s.stopPropagation()},className:"bg-primary-50 text-[13px] cursor-pointer text-blue-100 border border-blue-400 flex items-center justify-center h-[27px] py-1 px-3 rounded-2xl",children:[null==e?void 0:e.reaction," ",null==e?void 0:e.reaction_count]})}),(0,l.jsxs)(M._v,{className:"bg-black text-white p-2 rounded-md text-sm",children:[(0,l.jsx)(B.Ce,{className:"fill-black"}),(0,l.jsx)("div",{className:"text-5xl mx-auto text-center bg-white rounded-lg flex items-center justify-center p-2 w-[70px] mb-2",children:e.reaction}),i&&(0,l.jsxs)("span",{children:[i," reacted with ",null==e?void 0:e.reaction]})]})]})},s)}),(0,l.jsxs)(v.J2,{open:N,onOpenChange:w,children:[(0,l.jsx)(M.pn,{delayDuration:0,children:(0,l.jsxs)(M.u,{children:[(0,l.jsx)(M.aJ,{asChild:!0,children:(0,l.jsx)(v.xo,{asChild:!0,children:(null==r?void 0:null===(t=r.reactions)||void 0===t?void 0:t.length)>0&&(0,l.jsx)("div",{className:"bg-primary-50 text-[13px] cursor-pointer text-blue-100 h-[27px] flex items-center justify-center py-1 px-3 rounded-full border hover:border-blue-400",onClick:e=>e.stopPropagation(),children:(0,l.jsx)(c.Z,{size:16})})})}),(0,l.jsxs)(M._v,{className:"bg-black text-white p-2 rounded-md text-sm",children:[(0,l.jsx)(B.Ce,{className:"fill-black"}),(0,l.jsx)("span",{children:"Add reaction..."})]})]})}),(0,l.jsx)(v.yk,{className:"p-0 w-full max-w-xs",align:"end",children:(0,l.jsx)(g.Z,{data:j,onEmojiSelect:D})})]})]}),(0,l.jsxs)("div",{className:"hidden lg:flex opacity-0 group-hover:opacity-100 transition-opacity flex items-center ml-4 absolute right-5 -top-6 z-10 bg-white shadow-md rounded-[8px] border border-[#E6EAEF] p-[2px]",children:[!(null==S?void 0:S.includes("/agents"))&&(0,l.jsxs)(v.J2,{open:y,onOpenChange:f,children:[(0,l.jsx)(v.xo,{asChild:!0,children:(0,l.jsx)("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,l.jsx)(c.Z,{size:18,className:"text-[#667085]"})})}),(0,l.jsx)(v.yk,{className:"p-0",align:"end",avoidCollisions:!0,collisionPadding:80,children:(0,l.jsx)(g.Z,{data:j,onEmojiSelect:D})})]}),L?(0,l.jsx)("button",{onClick:O,className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,l.jsx)(x.Z,{size:18,className:"text-primary-500"})}):(0,l.jsx)("button",{onClick:T,className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,l.jsx)(u.Z,{size:18,className:"text-[#667085]"})}),(0,l.jsx)(F,{item:r})]}),b===r.id&&(0,l.jsxs)("div",{onClick:()=>{d(e=>e===r.id?null:r.id)},className:"flex lg:hidden group-hover:opacity-100 transition-opacity flex items-center ml-4 absolute right-5 -top-6 z-10 bg-white shadow-md rounded-[8px] border border-[#E6EAEF] p-[2px]",children:[!(null==S?void 0:S.includes("/agents"))&&(0,l.jsxs)(v.J2,{open:y,onOpenChange:f,children:[(0,l.jsx)(v.xo,{asChild:!0,children:(0,l.jsx)("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,l.jsx)(c.Z,{size:18,className:"text-[#667085]"})})}),(0,l.jsx)(v.yk,{className:"p-0",align:"end",avoidCollisions:!0,collisionPadding:80,children:(0,l.jsx)(g.Z,{data:j,onEmojiSelect:D})})]}),L?(0,l.jsx)("button",{onClick:O,className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,l.jsx)(x.Z,{size:18,className:"text-primary-500"})}):(0,l.jsx)("button",{onClick:T,className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,l.jsx)(u.Z,{size:18,className:"text-[#667085]"})}),(0,l.jsx)(F,{item:r})]})]})]})},Y=t(35575),K=t(72742),J=t(22641),q=t(49107),H=t(28904),V=t(82310),X=t(72593),$=t(59632),G=t(75148),Q=t(40708),W=t(65470),ee=t(25575),es=e=>{let{subscription:s,sendMessage:t}=e,{editor:r}=(0,Q.Z)(s),{state:o,dispatch:d}=(0,a.useContext)(i.R),[c,x]=(0,a.useState)(!1),[u,p]=(0,a.useState)(""),[m,h]=(0,a.useState)(""),[b,y]=(0,a.useState)(!1),[f,N]=(0,a.useState)(!0),w=async e=>{e.preventDefault();let l=null==r?void 0:r.getHTML();if(null==l?void 0:l.replace(/<[^>]+>/g,"").trim()){if(s){var a;t(l),null==r||null===(a=r.commands)||void 0===a||a.clearContent()}else console.log("No connection detected")}};return(0,a.useEffect)(()=>{if(r){var e;null==r||r.commands.setContent(null==o?void 0:null===(e=o.threadReply)||void 0===e?void 0:e.message),r.commands.focus()}},[r]),(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)("div",{className:"bg-white border rounded border-[#E6EAEF] w-full ".concat((null==o?void 0:o.reply)?"right-[520px]":"right-0"),children:[f&&(0,l.jsxs)("div",{className:"border-b border-[#E6EAEF] flex items-center gap-2 mb-2 bg-[#F9FAFB] pl-3 pr-4 py-[5px]",children:[(0,l.jsx)("button",{onClick:()=>null==r?void 0:r.chain().focus().toggleBold().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==r?void 0:r.isActive("bold"))?"bg-gray-200 font-semibold text-black":""),children:(0,l.jsx)(K.Z,{size:18,color:(null==r?void 0:r.isActive("bold"))?"#444444":"#CACACA"})}),(0,l.jsx)("button",{onClick:()=>null==r?void 0:r.chain().focus().toggleItalic().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==r?void 0:r.isActive("italic"))?"bg-gray-200 font-semibold text-black":""),children:(0,l.jsx)(J.Z,{size:18,color:(null==r?void 0:r.isActive("italic"))?"#444444":"#CACACA"})}),(0,l.jsx)("button",{onClick:()=>null==r?void 0:r.chain().focus().toggleStrike().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==r?void 0:r.isActive("strike"))?"bg-gray-200 font-semibold text-black":""),children:(0,l.jsx)(q.Z,{size:18,color:(null==r?void 0:r.isActive("strike"))?"#444444":"#CACACA"})}),(0,l.jsx)("div",{className:"w-px h-5 bg-[#E6EAEF]"}),(0,l.jsxs)(E.Vq,{open:c,onOpenChange:x,children:[(0,l.jsx)(E.hg,{asChild:!0,children:(0,l.jsx)("button",{onClick:()=>x(!0),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==r?void 0:r.isActive("link"))?"bg-gray-200 font-semibold text-black":""),children:(0,l.jsx)(H.Z,{size:18,color:(null==r?void 0:r.isActive("link"))?"#444444":"#CACACA"})})}),(0,l.jsxs)(E.cZ,{className:"w-full max-w-md",children:[(0,l.jsx)(E.fK,{children:(0,l.jsx)(E.$N,{className:"font-semibold",children:"Add link"})}),(0,l.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"Text"}),(0,l.jsx)(ee.I,{value:u,onChange:e=>p(e.target.value),placeholder:"Enter link text"}),(0,l.jsx)("label",{className:"text-sm font-medium mt-2",children:"Link"}),(0,l.jsx)(ee.I,{value:m,onChange:e=>h(e.target.value),placeholder:"Enter URL",type:"url"})]}),(0,l.jsxs)(E.cN,{className:"mt-4 flex justify-end gap-2",children:[(0,l.jsx)(z.z,{variant:"outline",onClick:()=>x(!1),children:"Cancel"}),(0,l.jsx)(z.z,{onClick:()=>{u&&m&&(null==r||r.chain().focus().insertContent('<a href="'.concat(m,'" target="_blank" rel="noopener noreferrer">').concat(u,"</a>")).run(),x(!1),p(""),h(""))},disabled:!u||!m,className:"bg-blue-500 text-white px-10",children:"Save"})]})]})]}),(0,l.jsx)("button",{onClick:()=>null==r?void 0:r.chain().focus().toggleOrderedList().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==r?void 0:r.isActive("orderedList"))?"bg-gray-200 font-semibold text-black":""),children:(0,l.jsx)(V.Z,{size:18,color:(null==r?void 0:r.isActive("orderedList"))?"#444444":"#CACACA"})}),(0,l.jsx)("button",{onClick:()=>null==r?void 0:r.chain().focus().toggleBulletList().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==r?void 0:r.isActive("bulletList"))?"bg-gray-200 font-semibold text-black":""),children:(0,l.jsx)(X.Z,{size:18,color:(null==r?void 0:r.isActive("bulletList"))?"#444444":"#CACACA"})}),(0,l.jsx)("div",{className:"w-px h-5 bg-[#E6EAEF]"}),(0,l.jsx)("button",{onClick:()=>null==r?void 0:r.chain().focus().toggleCode().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==r?void 0:r.isActive("code"))?"bg-gray-200 font-semibold text-black":""),children:(0,l.jsx)($.Z,{size:18,color:(null==r?void 0:r.isActive("code"))?"#444444":"#CACACA"})})]}),(0,l.jsx)("div",{className:"flex-1 relative px-3",children:(0,l.jsx)(W.kg,{editor:r,className:"py-2 rounded-md flex flex-row overflow-auto",onKeyDown:e=>{"Enter"===e.key&&(e.shiftKey?(e.preventDefault(),null==r||r.commands.enter()):(e.preventDefault(),w(e)))}})}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2 py-2 pl-3 pr-4",children:[(0,l.jsx)("button",{title:"show formatting",onClick:()=>N(e=>!e),className:"p-1.5 hover:bg-gray-100 rounded text-[#606060] underline",children:"Aa"}),(0,l.jsx)("div",{className:"relative",children:(0,l.jsxs)(v.J2,{open:b,onOpenChange:y,children:[(0,l.jsx)(v.xo,{asChild:!0,children:(0,l.jsx)("button",{className:"p-1.5 hover:bg-gray-100 rounded",children:(0,l.jsx)(G.Z,{size:18,color:"#606060"})})}),(0,l.jsx)(v.yk,{className:"p-0 w-full max-w-xs",children:(0,l.jsx)(g.Z,{data:j,onEmojiSelect:e=>{r?(r.chain().focus().insertContent(null==e?void 0:e.native).run(),y(!1)):console.log("editor not available")}})})]})})]}),(0,l.jsxs)("div",{className:"flex items-center gap-1 py-2 pl-3 pr-4",children:[(0,l.jsx)("button",{className:"bg-gray-100 rounded py-2 px-4 text-xs",onClick:()=>d({type:n.a.IS_EDIT_REPLY,payload:!1}),children:"Cancel"}),(0,l.jsx)("button",{onClick:w,className:"bg-blue-500 rounded py-2 px-4 text-white text-xs",children:"Save"})]})]})]})})},et=t(14776),el=e=>{let{fetchMoreData:s,hasMore:t}=e,{state:r,dispatch:o}=(0,a.useContext)(i.R),{replies:x,user:v,thread:g,threadReply:j,isEditReply:b,bookmarks:f}=r,N=(0,R.useParams)().id,[w,_]=(0,a.useState)(null),Z=async e=>{let s={content:e,message_id:null==j?void 0:j.id,thread_id:null==j?void 0:j.thread_id};await (0,P.an)("/channels/".concat(N,"/messages"),s),o({type:n.a.IS_EDIT_REPLY,payload:!1})};return(0,a.useEffect)(()=>{let e=localStorage.getItem("orgId")||"";(async()=>{let s=await (0,P.Gl)("/organisations/".concat(e,"/saved/message"));if((null==s?void 0:s.status)===200||(null==s?void 0:s.status)===201){var t,l;let e=null==s?void 0:null===(l=s.data)||void 0===l?void 0:null===(t=l.data)||void 0===t?void 0:t.map(e=>"thread"===e.type?{id:e.id,thread_id:e.thread_id}:{id:e.id});o({type:n.a.BOOKMARKS,payload:e})}})()},[]),(0,l.jsx)("div",{id:"scrollableDivs",style:{overflowY:"auto",display:"flex",flexDirection:"column-reverse"},className:"w-full pb-[30px]",children:(0,l.jsxs)(d.Z,{dataLength:null==x?void 0:x.length,next:s,hasMore:t,loader:(null==x?void 0:x.length)!==0&&(0,l.jsx)("h4",{className:"my-5 text-xs text-center",children:"Loading threads..."}),style:{display:"flex",flexDirection:"column-reverse",overflowY:"scroll"},scrollableTarget:"scrollableDivs",inverse:!0,children:[null==x?void 0:x.map((e,s)=>{var t,i;let n=x[s+1],o=!n||n.user_id!==e.user_id,d=null==f?void 0:f.some(s=>s.id===e.id);return(0,l.jsx)(a.Fragment,{children:(0,l.jsx)(l.Fragment,{children:b&&(null==j?void 0:j.id)===(null==e?void 0:e.id)?(0,l.jsxs)("div",{className:"flex mb-5 gap-2 mt-2 z-10 bg-white px-2 py-3 bg-blue-50 w-full",children:[(0,l.jsx)("div",{className:"size-9 mb-2 w-[50px] h-[30px]",children:(0,l.jsx)(p.default,{src:(null==e?void 0:e.avatar_url)?null==e?void 0:e.avatar_url:(null==e?void 0:e.user_type)=="user"||(null==e?void 0:e.user_type)===""?null===m.Z||void 0===m.Z?void 0:m.Z.user:null===m.Z||void 0===m.Z?void 0:m.Z.bot,alt:"avatar",width:30,height:30,className:"rounded-[7px] border size-9 object-cover"})}),(0,l.jsx)(es,{subscription:null==r?void 0:r.replySubscription,sendMessage:Z})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(U,{item:e,shouldShowAvatar:o,setPopupId:_,popupId:w}),e.is_pinned?(0,l.jsxs)("div",{className:"flex items-center gap-2 bg-yellow-50 pl-10 text-[13px] font-semibold text-blue-100 pt-2",children:[(0,l.jsx)(C.Z,{size:13,className:"text-[#667085] mt-[3px]"}),"Pinned by"," ",(null==v?void 0:v.email)===(null==e?void 0:null===(t=e.pinned_details)||void 0===t?void 0:t.email)?"you":null==e?void 0:null===(i=e.pinned_details)||void 0===i?void 0:i.username]}):d?(0,l.jsxs)("div",{className:"flex items-center gap-2 bg-primary-50 pl-10 text-[13px] font-bold text-blue-100 pt-2",children:[(0,l.jsx)(et.$8n,{fontSize:13,className:"text-[#667085]"}),"Saved for Later"]}):null]})})},s)}),!t&&(0,l.jsxs)("div",{className:"px-3 my-4 mb-8",children:[(0,l.jsxs)("div",{className:"flex",children:[(0,l.jsx)(p.default,{src:(null==g?void 0:g.avatar_url)?null==g?void 0:g.avatar_url:(null==g?void 0:g.user_type)==="user"?null===m.Z||void 0===m.Z?void 0:m.Z.user:null===m.Z||void 0===m.Z?void 0:m.Z.bot,alt:"avatar",width:100,height:100,className:"rounded-[7px] border size-9 mr-2 object-cover"}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"w-full flex items-center gap-2",children:[(0,l.jsx)("span",{className:"font-bold text-[15px] text-[#1D2939]",children:(null==g?void 0:g.username)||(null==g?void 0:g.email)}),(0,l.jsx)("span",{className:"text-xs text-[#98A2B3]",children:new Date(null==g?void 0:g.created_at).toLocaleTimeString([],{hour:"numeric",minute:"2-digit",hour12:!0}).replace(/ AM| PM/,"")})]}),(0,l.jsx)("div",{className:"relative flex items-start justify-between",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(h.Z,{item:g}),(0,l.jsx)("span",{className:"text-[9px] text-neutral-500",children:(null==g?void 0:g.edited)?"(edited)":""})]})}),(0,l.jsxs)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity flex items-center ml-4 absolute right-5 -top-6 z-20 bg-white shadow-md rounded-[8px] border border-[#E6EAEF] p-[2px]",children:[(0,l.jsx)("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,l.jsx)(c.Z,{size:18,className:"text-[#667085]"})}),(0,l.jsx)("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,l.jsx)(Y.Z,{size:18,className:"text-[#667085]"})}),(0,l.jsx)("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,l.jsx)(u.Z,{size:18,className:"text-[#667085]"})}),(0,l.jsx)("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,l.jsx)(y.Z,{size:18,className:"text-[#667085]"})})]})]})]}),(null==x?void 0:x.length)>0&&(0,l.jsxs)("div",{className:"flex items-center my-3 mt-10",children:[(0,l.jsx)("hr",{className:"flex-grow border-t border-gray-200"}),(0,l.jsxs)("span",{className:"text-xs text-gray-500 mx-2 whitespace-nowrap",children:[null==x?void 0:x.length," ",(null==x?void 0:x.length)===1?"reply":"replies"]}),(0,l.jsx)("hr",{className:"flex-grow border-t border-gray-200"})]})]})]})})},ea=t(20818),ei=t(53465),en=t(18648);function er(){var e;let{state:s,dispatch:t}=(0,a.useContext)(i.R),r=en.env.NEXT_PUBLIC_CONNECT_URL,o=async()=>{let e=localStorage.getItem("token")||"";return(await ea.Z.get("".concat(en.env.NEXT_PUBLIC_BASE_URL,"/token/connection"),{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}})).data.data.token},d=async e=>{let s=localStorage.getItem("token")||"";return(await ea.Z.post("".concat(en.env.NEXT_PUBLIC_BASE_URL,"/token/subscription"),{channel:e},{headers:{Authorization:"Bearer ".concat(s),"Content-Type":"application/json"}})).data.data.token};return(0,a.useEffect)(()=>{var e,l;if(null==s?void 0:null===(e=s.thread)||void 0===e?void 0:e.thread_id){let e=new ei.qX(r,{getToken:o,debug:!0});e.on("connect",()=>{console.log("Connected to Centrifuge")}),e.on("disconnect",()=>{console.log("Disconnected from Centrifuge")});let a=async()=>{var e;return d(null==s?void 0:null===(e=s.thread)||void 0===e?void 0:e.thread_id)},i=e.newSubscription(null==s?void 0:null===(l=s.thread)||void 0===l?void 0:l.thread_id,{getToken:a});return i.on("subscribed",()=>{t({type:n.a.REPLY_SUBSCRIPTION,payload:i})}),i.on("publication",e=>{var s;console.log("Reply connection",null==e?void 0:e.data),(null==e?void 0:null===(s=e.data)||void 0===s?void 0:s.type)==="message"&&t({type:n.a.REPLIES,payload:{newMessage:e.data,isRealTime:!0}})}),i.on("error",e=>{console.error("Subscription error: ".concat(e.message))}),e.connect(),i.subscribe(),()=>{i.unsubscribe(),e.disconnect()}}},[r,t,null==s?void 0:null===(e=s.thread)||void 0===e?void 0:e.thread_id]),(0,l.jsx)(l.Fragment,{})}var eo=e=>{let{handleSendMessage:s,fetchMoreData:t,hasMore:d}=e,{state:c,dispatch:x}=(0,a.useContext)(i.R),u=(0,R.usePathname)();(0,a.useEffect)(()=>{p()},[u]);let p=()=>{x({type:n.a.REPLY,payload:!1})};return(0,l.jsxs)("div",{className:"relative z-40 h-[calc(100vh-80px)] w-full overflow-auto",children:[(0,l.jsx)(er,{}),(0,l.jsxs)("div",{className:"relative flex items-center justify-between min-h-[70px] px-5 border-b font-bold textbase lg:text-lg",children:["Thread",(0,l.jsx)("button",{onClick:p,className:"text-[#344054] p-1 border border-input rounded-[0.3125rem]",children:(0,l.jsx)(o.Z,{className:"size-5 text-[#344054]"})})]}),(0,l.jsx)(el,{fetchMoreData:t,hasMore:d}),(0,l.jsx)(r.Z,{subscription:null==c?void 0:c.replySubscription,sendMessage:s,show:!1})]})}},74258:function(e,s,t){t.d(s,{C:function(){return r}});var l=t(75376);t(32486);var a=t(53447),i=t(58983);let n=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function r(e){let{className:s,variant:t,...a}=e;return(0,l.jsx)("div",{className:(0,i.cn)(n({variant:t}),s),...a})}}}]);