(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6859],{79316:function(e){"use strict";var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,l=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},o=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var r,i=t.call(e,"constructor"),l=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!i&&!l)return!1;for(r in e);return void 0===r||t.call(e,r)},a=function(e,t){r&&"__proto__"===t.name?r(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},u=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;if(i)return i(e,n).value}return e[n]};e.exports=function e(){var t,n,r,i,s,c,f=arguments[0],p=1,h=arguments.length,d=!1;for("boolean"==typeof f&&(d=f,f=arguments[1]||{},p=2),(null==f||"object"!=typeof f&&"function"!=typeof f)&&(f={});p<h;++p)if(t=arguments[p],null!=t)for(n in t)r=u(f,n),f!==(i=u(t,n))&&(d&&i&&(o(i)||(s=l(i)))?(s?(s=!1,c=r&&l(r)?r:[]):c=r&&o(r)?r:{},a(f,{name:n,newValue:e(d,c,i)})):void 0!==i&&a(f,{name:n,newValue:i}));return f}},6251:function(e){var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,i=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,l=/^:\s*/,o=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,a=/^[;\s]*/,u=/^\s+|\s+$/g;function s(e){return e?e.replace(u,""):""}e.exports=function(e,u){if("string"!=typeof e)throw TypeError("First argument must be a string");if(!e)return[];u=u||{};var c=1,f=1;function p(e){var t=e.match(n);t&&(c+=t.length);var r=e.lastIndexOf("\n");f=~r?e.length-r:f+e.length}function h(){var e={line:c,column:f};return function(t){return t.position=new d(e),y(r),t}}function d(e){this.start=e,this.end={line:c,column:f},this.source=u.source}d.prototype.content=e;var m=[];function g(t){var n=Error(u.source+":"+c+":"+f+": "+t);if(n.reason=t,n.filename=u.source,n.line=c,n.column=f,n.source=e,u.silent)m.push(n);else throw n}function y(t){var n=t.exec(e);if(n){var r=n[0];return p(r),e=e.slice(r.length),n}}function k(e){var t;for(e=e||[];t=x();)!1!==t&&e.push(t);return e}function x(){var t=h();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;""!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return g("End of comment missing");var r=e.slice(2,n-2);return f+=2,p(r),e=e.slice(n),f+=2,t({type:"comment",comment:r})}}return y(r),function(){var e,n=[];for(k(n);e=function(){var e=h(),n=y(i);if(n){if(x(),!y(l))return g("property missing ':'");var r=y(o),u=e({type:"declaration",property:s(n[0].replace(t,"")),value:r?s(r[0].replace(t,"")):""});return y(a),u}}();)!1!==e&&(n.push(e),k(n));return n}()}},41074:function(e,t,n){"use strict";var r=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(n(90346)),i=n(4018);function l(e,t){var n={};return e&&"string"==typeof e&&(0,r.default)(e,function(e,r){e&&r&&(n[(0,i.camelCase)(e,t)]=r)}),n}l.default=l,e.exports=l},4018:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelCase=void 0;var n=/^--[a-zA-Z0-9_-]+$/,r=/-([a-z])/g,i=/^[^-]+$/,l=/^-(webkit|moz|ms|o|khtml)-/,o=/^-(ms)-/,a=function(e,t){return t.toUpperCase()},u=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){var s;return(void 0===t&&(t={}),!(s=e)||i.test(s)||n.test(s))?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(o,u):e.replace(l,u)).replace(r,a))}},90346:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,i.default)(e),l="function"==typeof t;return r.forEach(function(e){if("declaration"===e.type){var r=e.property,i=e.value;l?t(r,i,e):i&&((n=n||{})[r]=i)}}),n};var i=r(n(6251))},47465:function(e,t,n){"use strict";n.d(t,{ZP:function(){return p}});let r="object"==typeof self?self:globalThis,i=(e,t)=>{let n=(t,n)=>(e.set(n,t),t),i=l=>{if(e.has(l))return e.get(l);let[o,a]=t[l];switch(o){case 0:case -1:return n(a,l);case 1:{let e=n([],l);for(let t of a)e.push(i(t));return e}case 2:{let e=n({},l);for(let[t,n]of a)e[i(t)]=i(n);return e}case 3:return n(new Date(a),l);case 4:{let{source:e,flags:t}=a;return n(new RegExp(e,t),l)}case 5:{let e=n(new Map,l);for(let[t,n]of a)e.set(i(t),i(n));return e}case 6:{let e=n(new Set,l);for(let t of a)e.add(i(t));return e}case 7:{let{name:e,message:t}=a;return n(new r[e](t),l)}case 8:return n(BigInt(a),l);case"BigInt":return n(Object(BigInt(a)),l);case"ArrayBuffer":return n(new Uint8Array(a).buffer,a);case"DataView":{let{buffer:e}=new Uint8Array(a);return n(new DataView(e),a)}}return n(new r[o](a),l)};return i},l=e=>i(new Map,e)(0),{toString:o}={},{keys:a}=Object,u=e=>{let t=typeof e;if("object"!==t||!e)return[0,t];let n=o.call(e).slice(8,-1);switch(n){case"Array":return[1,""];case"Object":return[2,""];case"Date":return[3,""];case"RegExp":return[4,""];case"Map":return[5,""];case"Set":return[6,""];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},s=([e,t])=>0===e&&("function"===t||"symbol"===t),c=(e,t,n,r)=>{let i=(e,t)=>{let i=r.push(e)-1;return n.set(t,i),i},l=r=>{if(n.has(r))return n.get(r);let[o,c]=u(r);switch(o){case 0:{let t=r;switch(c){case"bigint":o=8,t=r.toString();break;case"function":case"symbol":if(e)throw TypeError("unable to serialize "+c);t=null;break;case"undefined":return i([-1],r)}return i([o,t],r)}case 1:{if(c){let e=r;return"DataView"===c?e=new Uint8Array(r.buffer):"ArrayBuffer"===c&&(e=new Uint8Array(r)),i([c,[...e]],r)}let e=[],t=i([o,e],r);for(let t of r)e.push(l(t));return t}case 2:{if(c)switch(c){case"BigInt":return i([c,r.toString()],r);case"Boolean":case"Number":case"String":return i([c,r.valueOf()],r)}if(t&&"toJSON"in r)return l(r.toJSON());let n=[],f=i([o,n],r);for(let t of a(r))(e||!s(u(r[t])))&&n.push([l(t),l(r[t])]);return f}case 3:return i([o,r.toISOString()],r);case 4:{let{source:e,flags:t}=r;return i([o,{source:e,flags:t}],r)}case 5:{let t=[],n=i([o,t],r);for(let[n,i]of r)(e||!(s(u(n))||s(u(i))))&&t.push([l(n),l(i)]);return n}case 6:{let t=[],n=i([o,t],r);for(let n of r)(e||!s(u(n)))&&t.push(l(n));return n}}let{message:f}=r;return i([o,{name:c,message:f}],r)};return l},f=(e,{json:t,lossy:n}={})=>{let r=[];return c(!(t||n),!!t,new Map,r)(e),r};var p="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?l(f(e,t)):structuredClone(e):(e,t)=>l(f(e,t))},77170:function(e,t,n){"use strict";function r(e){let t=[],n=String(e||""),r=n.indexOf(","),i=0,l=!1;for(;!l;){-1===r&&(r=n.length,l=!0);let e=n.slice(i,r).trim();(e||!l)&&t.push(e),i=r+1,r=n.indexOf(",",i)}return t}function i(e,t){let n=t||{};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}n.d(t,{P:function(){return i},Q:function(){return r}})},51786:function(e,t,n){"use strict";function r(){}function i(){}n.d(t,{ok:function(){return r},t1:function(){return i}})},172:function(e,t,n){"use strict";n.d(t,{B:function(){return i}});let r={};function i(e,t){let n=t||r;return l(e,"boolean"!=typeof n.includeImageAlt||n.includeImageAlt,"boolean"!=typeof n.includeHtml||n.includeHtml)}function l(e,t,n){if(e&&"object"==typeof e){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return o(e.children,t,n)}return Array.isArray(e)?o(e,t,n):""}function o(e,t,n){let r=[],i=-1;for(;++i<e.length;)r[i]=l(e[i],t,n);return r.join("")}},49283:function(e,t,n){"use strict";n.d(t,{w:function(){return l}});var r=n(6604),i=n(52289);let l={partial:!0,tokenize:function(e,t,n){return function(t){return(0,i.xz)(t)?(0,r.f)(e,l,"linePrefix")(t):l(t)};function l(e){return null===e||(0,i.Ch)(e)?t(e):n(e)}}}},6604:function(e,t,n){"use strict";n.d(t,{f:function(){return i}});var r=n(52289);function i(e,t,n,i){let l=i?i-1:Number.POSITIVE_INFINITY,o=0;return function(i){return(0,r.xz)(i)?(e.enter(n),function i(a){return(0,r.xz)(a)&&o++<l?(e.consume(a),i):(e.exit(n),t(a))}(i)):t(i)}}},52289:function(e,t,n){"use strict";n.d(t,{AF:function(){return u},Av:function(){return o},B8:function(){return d},Ch:function(){return c},H$:function(){return i},Xh:function(){return h},jv:function(){return r},n9:function(){return l},pY:function(){return a},sR:function(){return s},xz:function(){return p},z3:function(){return f}});let r=m(/[A-Za-z]/),i=m(/[\dA-Za-z]/),l=m(/[#-'*+\--9=?A-Z^-~]/);function o(e){return null!==e&&(e<32||127===e)}let a=m(/\d/),u=m(/[\dA-Fa-f]/),s=m(/[!-/:-@[-`{-~]/);function c(e){return null!==e&&e<-2}function f(e){return null!==e&&(e<0||32===e)}function p(e){return -2===e||-1===e||32===e}let h=m(/\p{P}|\p{S}/u),d=m(/\s/);function m(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}},83139:function(e,t,n){"use strict";function r(e,t,n,r){let i;let l=e.length,o=0;if(t=t<0?-t>l?0:l+t:t>l?l:t,n=n>0?n:0,r.length<1e4)(i=Array.from(r)).unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);o<r.length;)(i=r.slice(o,o+1e4)).unshift(t,0),e.splice(...i),o+=1e4,t+=1e4}function i(e,t){return e.length>0?(r(e,e.length,0,t),e):t}n.d(t,{V:function(){return i},d:function(){return r}})},63921:function(e,t,n){"use strict";n.d(t,{r:function(){return i}});var r=n(52289);function i(e){return null===e||(0,r.z3)(e)||(0,r.B8)(e)?1:(0,r.Xh)(e)?2:void 0}},20631:function(e,t,n){"use strict";n.d(t,{W:function(){return l}});var r=n(83139);let i={}.hasOwnProperty;function l(e){let t={},n=-1;for(;++n<e.length;)!function(e,t){let n;for(n in t){let l;let o=(i.call(e,n)?e[n]:void 0)||(e[n]={}),a=t[n];if(a)for(l in a){i.call(o,l)||(o[l]=[]);let e=a[l];!function(e,t){let n=-1,i=[];for(;++n<t.length;)("after"===t[n].add?e:i).push(t[n]);(0,r.d)(e,0,0,i)}(o[l],Array.isArray(e)?e:e?[e]:[])}}}(t,e[n]);return t}},90800:function(e,t,n){"use strict";function r(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}n.d(t,{d:function(){return r}})},10741:function(e,t,n){"use strict";function r(e,t,n){let r=[],i=-1;for(;++i<e.length;){let l=e[i].resolveAll;l&&!r.includes(l)&&(t=l(t,n),r.push(l))}return t}n.d(t,{C:function(){return r}})},30531:function(e,t,n){"use strict";n.d(t,{dy:function(){return y},YP:function(){return k}});class r{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}function i(e,t){let n={},i={};for(let t of e)Object.assign(n,t.property),Object.assign(i,t.normal);return new r(n,i,t)}r.prototype.normal={},r.prototype.property={},r.prototype.space=void 0;var l=n(75911),o=n(90041);function a(e){let t={},n={};for(let[r,i]of Object.entries(e.properties)){let a=new o.I(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(a.mustUseProperty=!0),t[r]=a,n[(0,l.F)(r)]=r,n[(0,l.F)(a.attribute)]=r}return new r(t,n,e.space)}var u=n(4758);let s=a({properties:{ariaActiveDescendant:null,ariaAtomic:u.booleanish,ariaAutoComplete:null,ariaBusy:u.booleanish,ariaChecked:u.booleanish,ariaColCount:u.number,ariaColIndex:u.number,ariaColSpan:u.number,ariaControls:u.spaceSeparated,ariaCurrent:null,ariaDescribedBy:u.spaceSeparated,ariaDetails:null,ariaDisabled:u.booleanish,ariaDropEffect:u.spaceSeparated,ariaErrorMessage:null,ariaExpanded:u.booleanish,ariaFlowTo:u.spaceSeparated,ariaGrabbed:u.booleanish,ariaHasPopup:null,ariaHidden:u.booleanish,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:u.spaceSeparated,ariaLevel:u.number,ariaLive:null,ariaModal:u.booleanish,ariaMultiLine:u.booleanish,ariaMultiSelectable:u.booleanish,ariaOrientation:null,ariaOwns:u.spaceSeparated,ariaPlaceholder:null,ariaPosInSet:u.number,ariaPressed:u.booleanish,ariaReadOnly:u.booleanish,ariaRelevant:null,ariaRequired:u.booleanish,ariaRoleDescription:u.spaceSeparated,ariaRowCount:u.number,ariaRowIndex:u.number,ariaRowSpan:u.number,ariaSelected:u.booleanish,ariaSetSize:u.number,ariaSort:null,ariaValueMax:u.number,ariaValueMin:u.number,ariaValueNow:u.number,ariaValueText:null,role:null},transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase()});function c(e,t){return t in e?e[t]:t}function f(e,t){return c(e,t.toLowerCase())}let p=a({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:u.commaSeparated,acceptCharset:u.spaceSeparated,accessKey:u.spaceSeparated,action:null,allow:null,allowFullScreen:u.boolean,allowPaymentRequest:u.boolean,allowUserMedia:u.boolean,alt:null,as:null,async:u.boolean,autoCapitalize:null,autoComplete:u.spaceSeparated,autoFocus:u.boolean,autoPlay:u.boolean,blocking:u.spaceSeparated,capture:null,charSet:null,checked:u.boolean,cite:null,className:u.spaceSeparated,cols:u.number,colSpan:null,content:null,contentEditable:u.booleanish,controls:u.boolean,controlsList:u.spaceSeparated,coords:u.number|u.commaSeparated,crossOrigin:null,data:null,dateTime:null,decoding:null,default:u.boolean,defer:u.boolean,dir:null,dirName:null,disabled:u.boolean,download:u.overloadedBoolean,draggable:u.booleanish,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:u.boolean,formTarget:null,headers:u.spaceSeparated,height:u.number,hidden:u.overloadedBoolean,high:u.number,href:null,hrefLang:null,htmlFor:u.spaceSeparated,httpEquiv:u.spaceSeparated,id:null,imageSizes:null,imageSrcSet:null,inert:u.boolean,inputMode:null,integrity:null,is:null,isMap:u.boolean,itemId:null,itemProp:u.spaceSeparated,itemRef:u.spaceSeparated,itemScope:u.boolean,itemType:u.spaceSeparated,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:u.boolean,low:u.number,manifest:null,max:null,maxLength:u.number,media:null,method:null,min:null,minLength:u.number,multiple:u.boolean,muted:u.boolean,name:null,nonce:null,noModule:u.boolean,noValidate:u.boolean,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:u.boolean,optimum:u.number,pattern:null,ping:u.spaceSeparated,placeholder:null,playsInline:u.boolean,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:u.boolean,referrerPolicy:null,rel:u.spaceSeparated,required:u.boolean,reversed:u.boolean,rows:u.number,rowSpan:u.number,sandbox:u.spaceSeparated,scope:null,scoped:u.boolean,seamless:u.boolean,selected:u.boolean,shadowRootClonable:u.boolean,shadowRootDelegatesFocus:u.boolean,shadowRootMode:null,shape:null,size:u.number,sizes:null,slot:null,span:u.number,spellCheck:u.booleanish,src:null,srcDoc:null,srcLang:null,srcSet:null,start:u.number,step:null,style:null,tabIndex:u.number,target:null,title:null,translate:null,type:null,typeMustMatch:u.boolean,useMap:null,value:u.booleanish,width:u.number,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:u.spaceSeparated,axis:null,background:null,bgColor:null,border:u.number,borderColor:null,bottomMargin:u.number,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:u.boolean,declare:u.boolean,event:null,face:null,frame:null,frameBorder:null,hSpace:u.number,leftMargin:u.number,link:null,longDesc:null,lowSrc:null,marginHeight:u.number,marginWidth:u.number,noResize:u.boolean,noHref:u.boolean,noShade:u.boolean,noWrap:u.boolean,object:null,profile:null,prompt:null,rev:null,rightMargin:u.number,rules:null,scheme:null,scrolling:u.booleanish,standby:null,summary:null,text:null,topMargin:u.number,valueType:null,version:null,vAlign:null,vLink:null,vSpace:u.number,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:u.boolean,disableRemotePlayback:u.boolean,prefix:null,property:null,results:u.number,security:null,unselectable:null},space:"html",transform:f}),h=a({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:u.commaOrSpaceSeparated,accentHeight:u.number,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:u.number,amplitude:u.number,arabicForm:null,ascent:u.number,attributeName:null,attributeType:null,azimuth:u.number,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:u.number,by:null,calcMode:null,capHeight:u.number,className:u.spaceSeparated,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:u.number,diffuseConstant:u.number,direction:null,display:null,dur:null,divisor:u.number,dominantBaseline:null,download:u.boolean,dx:null,dy:null,edgeMode:null,editable:null,elevation:u.number,enableBackground:null,end:null,event:null,exponent:u.number,externalResourcesRequired:null,fill:null,fillOpacity:u.number,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:u.commaSeparated,g2:u.commaSeparated,glyphName:u.commaSeparated,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:u.number,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:u.number,horizOriginX:u.number,horizOriginY:u.number,id:null,ideographic:u.number,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:u.number,k:u.number,k1:u.number,k2:u.number,k3:u.number,k4:u.number,kernelMatrix:u.commaOrSpaceSeparated,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:u.number,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:u.number,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:u.number,overlineThickness:u.number,paintOrder:null,panose1:null,path:null,pathLength:u.number,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:u.spaceSeparated,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:u.number,pointsAtY:u.number,pointsAtZ:u.number,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:u.commaOrSpaceSeparated,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:u.commaOrSpaceSeparated,rev:u.commaOrSpaceSeparated,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:u.commaOrSpaceSeparated,requiredFeatures:u.commaOrSpaceSeparated,requiredFonts:u.commaOrSpaceSeparated,requiredFormats:u.commaOrSpaceSeparated,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:u.number,specularExponent:u.number,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:u.number,strikethroughThickness:u.number,string:null,stroke:null,strokeDashArray:u.commaOrSpaceSeparated,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:u.number,strokeOpacity:u.number,strokeWidth:null,style:null,surfaceScale:u.number,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:u.commaOrSpaceSeparated,tabIndex:u.number,tableValues:null,target:null,targetX:u.number,targetY:u.number,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:u.commaOrSpaceSeparated,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:u.number,underlineThickness:u.number,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:u.number,values:null,vAlphabetic:u.number,vMathematical:u.number,vectorEffect:null,vHanging:u.number,vIdeographic:u.number,version:null,vertAdvY:u.number,vertOriginX:u.number,vertOriginY:u.number,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:u.number,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:c}),d=a({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase()}),m=a({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:f}),g=a({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase()}),y=i([s,p,d,m,g],"html"),k=i([s,h,d,m,g],"svg")},72481:function(e,t,n){"use strict";n.d(t,{s:function(){return s}});var r=n(90041),i=n(25601),l=n(75911);let o=/[A-Z]/g,a=/-[a-z]/g,u=/^data[-\w.:]+$/i;function s(e,t){let n=(0,l.F)(t),s=t,p=i.k;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&u.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(a,f);s="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!a.test(e)){let n=e.replace(o,c);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}p=r.I}return new p(s,t)}function c(e){return"-"+e.toLowerCase()}function f(e){return e.charAt(1).toUpperCase()}},75911:function(e,t,n){"use strict";function r(e){return e.toLowerCase()}n.d(t,{F:function(){return r}})},90041:function(e,t,n){"use strict";n.d(t,{I:function(){return o}});var r=n(25601),i=n(4758);let l=Object.keys(i);class o extends r.k{constructor(e,t,n,r){var o,a;let u=-1;if(super(e,t),r&&(this.space=r),"number"==typeof n)for(;++u<l.length;){let e=l[u];o=l[u],(a=(n&i[e])===i[e])&&(this[o]=a)}}}o.prototype.defined=!0},25601:function(e,t,n){"use strict";n.d(t,{k:function(){return r}});class r{constructor(e,t){this.attribute=t,this.property=e}}r.prototype.attribute="",r.prototype.booleanish=!1,r.prototype.boolean=!1,r.prototype.commaOrSpaceSeparated=!1,r.prototype.commaSeparated=!1,r.prototype.defined=!1,r.prototype.mustUseProperty=!1,r.prototype.number=!1,r.prototype.overloadedBoolean=!1,r.prototype.property="",r.prototype.spaceSeparated=!1,r.prototype.space=void 0},4758:function(e,t,n){"use strict";n.r(t),n.d(t,{boolean:function(){return i},booleanish:function(){return l},commaOrSpaceSeparated:function(){return c},commaSeparated:function(){return s},number:function(){return a},overloadedBoolean:function(){return o},spaceSeparated:function(){return u}});let r=0,i=f(),l=f(),o=f(),a=f(),u=f(),s=f(),c=f();function f(){return 2**++r}},36727:function(e,t,n){"use strict";n.d(t,{UG:function(){return t_}});var r={};n.r(r),n.d(r,{attentionMarkers:function(){return eY},contentInitial:function(){return eH},disable:function(){return eK},document:function(){return eB},flow:function(){return eU},flowInitial:function(){return eV},insideSpan:function(){return eW},string:function(){return eq},text:function(){return e$}});var i=n(51786),l=n(77170);let o=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,a=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,u={};function s(e,t){return((t||u).jsx?a:o).test(e)}let c=/[ \t\n\f\r]/g;function f(e){return""===e.replace(c,"")}var p=n(30531),h=n(72481);let d={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var m=n(60624),g=n(41074),y=n(95510);function k(e){return e&&"object"==typeof e?"position"in e||"type"in e?b(e.position):"start"in e||"end"in e?b(e):"line"in e||"column"in e?x(e):"":""}function x(e){return v(e&&e.line)+":"+v(e&&e.column)}function b(e){return x(e&&e.start)+"-"+x(e&&e.end)}function v(e){return e&&"number"==typeof e?e:1}class S extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let r="",i={},l=!1;if(t&&(i="line"in t&&"column"in t?{place:t}:"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?r=e:!i.cause&&e&&(l=!0,r=e.message,i.cause=e),!i.ruleId&&!i.source&&"string"==typeof n){let e=n.indexOf(":");-1===e?i.ruleId=n:(i.source=n.slice(0,e),i.ruleId=n.slice(e+1))}if(!i.place&&i.ancestors&&i.ancestors){let e=i.ancestors[i.ancestors.length-1];e&&(i.place=e.position)}let o=i.place&&"start"in i.place?i.place.start:i.place;this.ancestors=i.ancestors||void 0,this.cause=i.cause||void 0,this.column=o?o.column:void 0,this.fatal=void 0,this.file="",this.message=r,this.line=o?o.line:void 0,this.name=k(i.place)||"1:1",this.place=i.place||void 0,this.reason=this.message,this.ruleId=i.ruleId||void 0,this.source=i.source||void 0,this.stack=l&&i.cause&&"string"==typeof i.cause.stack?i.cause.stack:"",this.actual=void 0,this.expected=void 0,this.note=void 0,this.url=void 0}}S.prototype.file="",S.prototype.name="",S.prototype.reason="",S.prototype.message="",S.prototype.stack="",S.prototype.column=void 0,S.prototype.line=void 0,S.prototype.ancestors=void 0,S.prototype.cause=void 0,S.prototype.fatal=void 0,S.prototype.place=void 0,S.prototype.ruleId=void 0,S.prototype.source=void 0;let w={}.hasOwnProperty,C=new Map,E=/[A-Z]/g,I=new Set(["table","tbody","thead","tfoot","tr"]),z=new Set(["td","th"]),T="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function P(e,t,n){return"element"===t.type?function(e,t,n){let r=e.schema,i=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(i=p.YP,e.schema=i),e.ancestors.push(t);let o=L(e,t.tagName,!1),a=function(e,t){let n,r;let i={};for(r in t.properties)if("children"!==r&&w.call(t.properties,r)){let o=function(e,t,n){let r=(0,h.s)(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?(0,l.P)(n):(0,m.P)(n)),"style"===r.property){let t="object"==typeof n?n:function(e,t){try{return g(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};let t=new S("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:n,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw t.file=e.filePath||void 0,t.url=T+"#cannot-parse-style-attribute",t}}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){let t;let n={};for(t in e)w.call(e,t)&&(n[function(e){let t=e.replace(E,M);return"ms-"===t.slice(0,3)&&(t="-"+t),t}(t)]=e[t]);return n}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?d[r.property]||r.property:r.attribute,n]}}(e,r,t.properties[r]);if(o){let[r,l]=o;e.tableCellAlignToStyle&&"align"===r&&"string"==typeof l&&z.has(t.tagName)?n=l:i[r]=l}}return n&&((i.style||(i.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=n),i}(e,t),u=O(e,t);return I.has(t.tagName)&&(u=u.filter(function(e){return"string"!=typeof e||!("object"==typeof e?"text"===e.type&&f(e.value):f(e))})),A(e,a,o,t),D(a,u),e.ancestors.pop(),e.schema=r,e.create(t,o,a,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){let n=t.data.estree.body[0];return(0,i.ok)("ExpressionStatement"===n.type),e.evaluater.evaluateExpression(n.expression)}F(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){let r=e.schema,l=r;"svg"===t.name&&"html"===r.space&&(l=p.YP,e.schema=l),e.ancestors.push(t);let o=null===t.name?e.Fragment:L(e,t.name,!0),a=function(e,t){let n={};for(let r of t.attributes)if("mdxJsxExpressionAttribute"===r.type){if(r.data&&r.data.estree&&e.evaluater){let t=r.data.estree.body[0];(0,i.ok)("ExpressionStatement"===t.type);let l=t.expression;(0,i.ok)("ObjectExpression"===l.type);let o=l.properties[0];(0,i.ok)("SpreadElement"===o.type),Object.assign(n,e.evaluater.evaluateExpression(o.argument))}else F(e,t.position)}else{let l;let o=r.name;if(r.value&&"object"==typeof r.value){if(r.value.data&&r.value.data.estree&&e.evaluater){let t=r.value.data.estree.body[0];(0,i.ok)("ExpressionStatement"===t.type),l=e.evaluater.evaluateExpression(t.expression)}else F(e,t.position)}else l=null===r.value||r.value;n[o]=l}return n}(e,t),u=O(e,t);return A(e,a,o,t),D(a,u),e.ancestors.pop(),e.schema=r,e.create(t,o,a,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);F(e,t.position)}(e,t):"root"===t.type?function(e,t,n){let r={};return D(r,O(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?t.value:void 0}function A(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function D(e,t){if(t.length>0){let n=t.length>1?t:t[0];n&&(e.children=n)}}function O(e,t){let n=[],r=-1,i=e.passKeys?new Map:C;for(;++r<t.children.length;){let l;let o=t.children[r];if(e.passKeys){let e="element"===o.type?o.tagName:"mdxJsxFlowElement"===o.type||"mdxJsxTextElement"===o.type?o.name:void 0;if(e){let t=i.get(e)||0;l=e+"-"+t,i.set(e,t+1)}}let a=P(e,o,l);void 0!==a&&n.push(a)}return n}function L(e,t,n){let r;if(n){if(t.includes(".")){let e;let n=t.split("."),l=-1;for(;++l<n.length;){let t=s(n[l])?{type:"Identifier",name:n[l]}:{type:"Literal",value:n[l]};e=e?{type:"MemberExpression",object:e,property:t,computed:!!(l&&"Literal"===t.type),optional:!1}:t}(0,i.ok)(e,"always a result"),r=e}else r=s(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t}}else r={type:"Literal",value:t};if("Literal"===r.type){let t=r.value;return w.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);F(e)}function F(e,t){let n=new S("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=T+"#cannot-handle-mdx-estrees-without-createevaluater",n}function M(e){return"-"+e.toLowerCase()}let R={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};var _=n(75376);n(32486);var N=n(172),j=n(83139);class B{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){let n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){this.setCursor(Math.trunc(e));let r=this.right.splice(this.right.length-(t||0),Number.POSITIVE_INFINITY);return n&&H(this.left,n),r.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),H(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),H(this.right,e.reverse())}setCursor(e){if(e!==this.left.length&&(!(e>this.left.length)||0!==this.right.length)&&(!(e<0)||0!==this.left.length)){if(e<this.left.length){let t=this.left.splice(e,Number.POSITIVE_INFINITY);H(this.right,t.reverse())}else{let t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);H(this.left,t.reverse())}}}}function H(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function V(e){let t,n,r,i,l,o,a;let u={},s=-1,c=new B(e);for(;++s<c.length;){for(;(s in u);)s=u[s];if(t=c.get(s),s&&"chunkFlow"===t[1].type&&"listItemPrefix"===c.get(s-1)[1].type&&((r=0)<(o=t[1]._tokenizer.events).length&&"lineEndingBlank"===o[r][1].type&&(r+=2),r<o.length&&"content"===o[r][1].type))for(;++r<o.length&&"content"!==o[r][1].type;)"chunkText"===o[r][1].type&&(o[r][1]._isInFirstContentOfListItem=!0,r++);if("enter"===t[0])t[1].contentType&&(Object.assign(u,function(e,t){let n,r;let i=e.get(t)[1],l=e.get(t)[2],o=t-1,a=[],u=i._tokenizer;!u&&(u=l.parser[i.contentType](i.start),i._contentTypeTextTrailing&&(u._contentTypeTextTrailing=!0));let s=u.events,c=[],f={},p=-1,h=i,d=0,m=0,g=[0];for(;h;){for(;e.get(++o)[1]!==h;);a.push(o),!h._tokenizer&&(n=l.sliceStream(h),h.next||n.push(null),r&&u.defineSkip(h.start),h._isInFirstContentOfListItem&&(u._gfmTasklistFirstContentOfListItem=!0),u.write(n),h._isInFirstContentOfListItem&&(u._gfmTasklistFirstContentOfListItem=void 0)),r=h,h=h.next}for(h=i;++p<s.length;)"exit"===s[p][0]&&"enter"===s[p-1][0]&&s[p][1].type===s[p-1][1].type&&s[p][1].start.line!==s[p][1].end.line&&(m=p+1,g.push(m),h._tokenizer=void 0,h.previous=void 0,h=h.next);for(u.events=[],h?(h._tokenizer=void 0,h.previous=void 0):g.pop(),p=g.length;p--;){let t=s.slice(g[p],g[p+1]),n=a.pop();c.push([n,n+t.length-1]),e.splice(n,2,t)}for(c.reverse(),p=-1;++p<c.length;)f[d+c[p][0]]=d+c[p][1],d+=c[p][1]-c[p][0]-1;return f}(c,s)),s=u[s],a=!0);else if(t[1]._container){for(r=s,n=void 0;r--;)if("lineEnding"===(i=c.get(r))[1].type||"lineEndingBlank"===i[1].type)"enter"===i[0]&&(n&&(c.get(n)[1].type="lineEndingBlank"),i[1].type="lineEnding",n=r);else if("linePrefix"===i[1].type||"listItemIndent"===i[1].type);else break;n&&(t[1].end={...c.get(n)[1].start},(l=c.slice(n,s)).unshift(t),c.splice(n,s-n+1,l))}}return(0,j.d)(e,0,Number.POSITIVE_INFINITY,c.slice(0)),!a}var U=n(20631),q=n(6604),$=n(52289);let W={tokenize:function(e){let t;let n=e.attempt(this.parser.constructs.contentInitial,function(t){if(null===t){e.consume(t);return}return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,q.f)(e,n,"linePrefix")},function(n){return e.enter("paragraph"),function n(r){let i=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=i),t=i,function t(r){if(null===r){e.exit("chunkText"),e.exit("paragraph"),e.consume(r);return}return(0,$.Ch)(r)?(e.consume(r),e.exit("chunkText"),n):(e.consume(r),t)}(r)}(n)});return n}},Y={tokenize:function(e){let t,n,r;let i=this,l=[],o=0;return a;function a(t){if(o<l.length){let n=l[o];return i.containerState=n[1],e.attempt(n[0].continuation,u,s)(t)}return s(t)}function u(e){if(o++,i.containerState._closeFlow){let n;i.containerState._closeFlow=void 0,t&&y();let r=i.events.length,l=r;for(;l--;)if("exit"===i.events[l][0]&&"chunkFlow"===i.events[l][1].type){n=i.events[l][1].end;break}g(o);let a=r;for(;a<i.events.length;)i.events[a][1].end={...n},a++;return(0,j.d)(i.events,l+1,0,i.events.slice(r)),i.events.length=a,s(e)}return a(e)}function s(n){if(o===l.length){if(!t)return p(n);if(t.currentConstruct&&t.currentConstruct.concrete)return d(n);i.interrupt=!!(t.currentConstruct&&!t._gfmTableDynamicInterruptHack)}return i.containerState={},e.check(K,c,f)(n)}function c(e){return t&&y(),g(o),p(e)}function f(e){return i.parser.lazy[i.now().line]=o!==l.length,r=i.now().offset,d(e)}function p(t){return i.containerState={},e.attempt(K,h,d)(t)}function h(e){return o++,l.push([i.currentConstruct,i.containerState]),p(e)}function d(r){if(null===r){t&&y(),g(0),e.consume(r);return}return t=t||i.parser.flow(i.now()),e.enter("chunkFlow",{_tokenizer:t,contentType:"flow",previous:n}),function t(n){if(null===n){m(e.exit("chunkFlow"),!0),g(0),e.consume(n);return}return(0,$.Ch)(n)?(e.consume(n),m(e.exit("chunkFlow")),o=0,i.interrupt=void 0,a):(e.consume(n),t)}(r)}function m(e,l){let a=i.sliceStream(e);if(l&&a.push(null),e.previous=n,n&&(n.next=e),n=e,t.defineSkip(e.start),t.write(a),i.parser.lazy[e.start.line]){let e,n,l=t.events.length;for(;l--;)if(t.events[l][1].start.offset<r&&(!t.events[l][1].end||t.events[l][1].end.offset>r))return;let a=i.events.length,u=a;for(;u--;)if("exit"===i.events[u][0]&&"chunkFlow"===i.events[u][1].type){if(e){n=i.events[u][1].end;break}e=!0}for(g(o),l=a;l<i.events.length;)i.events[l][1].end={...n},l++;(0,j.d)(i.events,u+1,0,i.events.slice(a)),i.events.length=l}}function g(t){let n=l.length;for(;n-- >t;){let t=l[n];i.containerState=t[1],t[0].exit.call(i,e)}l.length=t}function y(){t.write([null]),n=void 0,t=void 0,i.containerState._closeFlow=void 0}}},K={tokenize:function(e,t,n){return(0,q.f)(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}};var Q=n(49283);let X={resolve:function(e){return V(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){return null===t?i(t):(0,$.Ch)(t)?e.check(Z,l,i)(t):(e.consume(t),r)}function i(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function l(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}}},Z={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,q.f)(e,i,"linePrefix")};function i(i){if(null===i||(0,$.Ch)(i))return n(i);let l=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&l&&"linePrefix"===l[1].type&&l[2].sliceSerialize(l[1],!0).length>=4?t(i):e.interrupt(r.parser.constructs.flow,n,t)(i)}}},J={tokenize:function(e){let t=this,n=e.attempt(Q.w,function(r){if(null===r){e.consume(r);return}return e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n},e.attempt(this.parser.constructs.flowInitial,r,(0,q.f)(e,e.attempt(this.parser.constructs.flow,r,e.attempt(X,r)),"linePrefix")));return n;function r(r){if(null===r){e.consume(r);return}return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n}}},G={resolveAll:er()},ee=en("string"),et=en("text");function en(e){return{resolveAll:er("text"===e?ei:void 0),tokenize:function(t){let n=this,r=this.parser.constructs[e],i=t.attempt(r,l,o);return l;function l(e){return u(e)?i(e):o(e)}function o(e){if(null===e){t.consume(e);return}return t.enter("data"),t.consume(e),a}function a(e){return u(e)?(t.exit("data"),i(e)):(t.consume(e),a)}function u(e){if(null===e)return!0;let t=r[e],i=-1;if(t)for(;++i<t.length;){let e=t[i];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function er(e){return function(t,n){let r,i=-1;for(;++i<=t.length;)void 0===r?t[i]&&"data"===t[i][1].type&&(r=i,i++):t[i]&&"data"===t[i][1].type||(i!==r+2&&(t[r][1].end=t[i-1][1].end,t.splice(r+2,i-r-2),i=r+2),r=void 0);return e?e(t,n):t}}function ei(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){let r;let i=e[n-1][1],l=t.sliceStream(i),o=l.length,a=-1,u=0;for(;o--;){let e=l[o];if("string"==typeof e){for(a=e.length;32===e.charCodeAt(a-1);)u++,a--;if(a)break;a=-1}else if(-2===e)r=!0,u++;else if(-1===e);else{o++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(u=0),u){let l={type:n===e.length||r||u<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:o?a:i.start._bufferIndex+a,_index:i.start._index+o,line:i.end.line,column:i.end.column-u,offset:i.end.offset-u},end:{...i.end}};i.end={...l.start},i.start.offset===i.end.offset?Object.assign(i,l):(e.splice(n,0,["enter",l,t],["exit",l,t]),n+=2)}n++}return e}let el={name:"thematicBreak",tokenize:function(e,t,n){let r,i=0;return function(l){return e.enter("thematicBreak"),r=l,function l(o){return o===r?(e.enter("thematicBreakSequence"),function t(n){return n===r?(e.consume(n),i++,t):(e.exit("thematicBreakSequence"),(0,$.xz)(n)?(0,q.f)(e,l,"whitespace")(n):l(n))}(o)):i>=3&&(null===o||(0,$.Ch)(o))?(e.exit("thematicBreak"),t(o)):n(o)}(l)}}},eo={continuation:{tokenize:function(e,t,n){let r=this;return r.containerState._closeFlow=void 0,e.check(Q.w,function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,(0,q.f)(e,t,"listItemIndent",r.containerState.size+1)(n)},function(n){return r.containerState.furtherBlankLines||!(0,$.xz)(n)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,i(n)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(eu,t,i)(n))});function i(i){return r.containerState._closeFlow=!0,r.interrupt=void 0,(0,q.f)(e,e.attempt(eo,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(i)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){let r=this,i=r.events[r.events.length-1],l=i&&"linePrefix"===i[1].type?i[2].sliceSerialize(i[1],!0).length:0,o=0;return function(t){let i=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===i?!r.containerState.marker||t===r.containerState.marker:(0,$.pY)(t)){if(r.containerState.type||(r.containerState.type=i,e.enter(i,{_container:!0})),"listUnordered"===i)return e.enter("listItemPrefix"),42===t||45===t?e.check(el,n,a)(t):a(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),function t(i){return(0,$.pY)(i)&&++o<10?(e.consume(i),t):(!r.interrupt||o<2)&&(r.containerState.marker?i===r.containerState.marker:41===i||46===i)?(e.exit("listItemValue"),a(i)):n(i)}(t)}return n(t)};function a(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(Q.w,r.interrupt?n:u,e.attempt(ea,c,s))}function u(e){return r.containerState.initialBlankLine=!0,l++,c(e)}function s(t){return(0,$.xz)(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),c):n(t)}function c(n){return r.containerState.size=l+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},ea={partial:!0,tokenize:function(e,t,n){let r=this;return(0,q.f)(e,function(e){let i=r.events[r.events.length-1];return!(0,$.xz)(e)&&i&&"listItemPrefixWhitespace"===i[1].type?t(e):n(e)},"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},eu={partial:!0,tokenize:function(e,t,n){let r=this;return(0,q.f)(e,function(e){let i=r.events[r.events.length-1];return i&&"listItemIndent"===i[1].type&&i[2].sliceSerialize(i[1],!0).length===r.containerState.size?t(e):n(e)},"listItemIndent",r.containerState.size+1)}},es={continuation:{tokenize:function(e,t,n){let r=this;return function(t){return(0,$.xz)(t)?(0,q.f)(e,i,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):i(t)};function i(r){return e.attempt(es,t,n)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){let r=this;return function(t){if(62===t){let n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),i}return n(t)};function i(n){return(0,$.xz)(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}};function ec(e,t,n,r,i,l,o,a,u){let s=u||Number.POSITIVE_INFINITY,c=0;return function(t){return 60===t?(e.enter(r),e.enter(i),e.enter(l),e.consume(t),e.exit(l),f):null===t||32===t||41===t||(0,$.Av)(t)?n(t):(e.enter(r),e.enter(o),e.enter(a),e.enter("chunkString",{contentType:"string"}),d(t))};function f(n){return 62===n?(e.enter(l),e.consume(n),e.exit(l),e.exit(i),e.exit(r),t):(e.enter(a),e.enter("chunkString",{contentType:"string"}),p(n))}function p(t){return 62===t?(e.exit("chunkString"),e.exit(a),f(t)):null===t||60===t||(0,$.Ch)(t)?n(t):(e.consume(t),92===t?h:p)}function h(t){return 60===t||62===t||92===t?(e.consume(t),p):p(t)}function d(i){return!c&&(null===i||41===i||(0,$.z3)(i))?(e.exit("chunkString"),e.exit(a),e.exit(o),e.exit(r),t(i)):c<s&&40===i?(e.consume(i),c++,d):41===i?(e.consume(i),c--,d):null===i||32===i||40===i||(0,$.Av)(i)?n(i):(e.consume(i),92===i?m:d)}function m(t){return 40===t||41===t||92===t?(e.consume(t),d):d(t)}}function ef(e,t,n,r,i,l){let o;let a=this,u=0;return function(t){return e.enter(r),e.enter(i),e.consume(t),e.exit(i),e.enter(l),s};function s(f){return u>999||null===f||91===f||93===f&&!o||94===f&&!u&&"_hiddenFootnoteSupport"in a.parser.constructs?n(f):93===f?(e.exit(l),e.enter(i),e.consume(f),e.exit(i),e.exit(r),t):(0,$.Ch)(f)?(e.enter("lineEnding"),e.consume(f),e.exit("lineEnding"),s):(e.enter("chunkString",{contentType:"string"}),c(f))}function c(t){return null===t||91===t||93===t||(0,$.Ch)(t)||u++>999?(e.exit("chunkString"),s(t)):(e.consume(t),o||(o=!(0,$.xz)(t)),92===t?f:c)}function f(t){return 91===t||92===t||93===t?(e.consume(t),u++,c):c(t)}}function ep(e,t,n,r,i,l){let o;return function(t){return 34===t||39===t||40===t?(e.enter(r),e.enter(i),e.consume(t),e.exit(i),o=40===t?41:t,a):n(t)};function a(n){return n===o?(e.enter(i),e.consume(n),e.exit(i),e.exit(r),t):(e.enter(l),u(n))}function u(t){return t===o?(e.exit(l),a(o)):null===t?n(t):(0,$.Ch)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,q.f)(e,u,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),s(t))}function s(t){return t===o||null===t||(0,$.Ch)(t)?(e.exit("chunkString"),u(t)):(e.consume(t),92===t?c:s)}function c(t){return t===o||92===t?(e.consume(t),s):s(t)}}function eh(e,t){let n;return function r(i){return(0,$.Ch)(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),n=!0,r):(0,$.xz)(i)?(0,q.f)(e,r,n?"linePrefix":"lineSuffix")(i):t(i)}}var ed=n(90800);let em={partial:!0,tokenize:function(e,t,n){return function(t){return(0,$.z3)(t)?eh(e,r)(t):n(t)};function r(t){return ep(e,i,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function i(t){return(0,$.xz)(t)?(0,q.f)(e,l,"whitespace")(t):l(t)}function l(e){return null===e||(0,$.Ch)(e)?t(e):n(e)}}},eg={name:"codeIndented",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("codeIndented"),(0,q.f)(e,i,"linePrefix",5)(t)};function i(t){let i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?function t(n){return null===n?l(n):(0,$.Ch)(n)?e.attempt(ey,t,l)(n):(e.enter("codeFlowValue"),function n(r){return null===r||(0,$.Ch)(r)?(e.exit("codeFlowValue"),t(r)):(e.consume(r),n)}(n))}(t):n(t)}function l(n){return e.exit("codeIndented"),t(n)}}},ey={partial:!0,tokenize:function(e,t,n){let r=this;return i;function i(t){return r.parser.lazy[r.now().line]?n(t):(0,$.Ch)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):(0,q.f)(e,l,"linePrefix",5)(t)}function l(e){let l=r.events[r.events.length-1];return l&&"linePrefix"===l[1].type&&l[2].sliceSerialize(l[1],!0).length>=4?t(e):(0,$.Ch)(e)?i(e):n(e)}}},ek={name:"setextUnderline",resolveTo:function(e,t){let n,r,i,l=e.length;for(;l--;)if("enter"===e[l][0]){if("content"===e[l][1].type){n=l;break}"paragraph"===e[l][1].type&&(r=l)}else"content"===e[l][1].type&&e.splice(l,1),i||"definition"!==e[l][1].type||(i=l);let o={type:"setextHeading",start:{...e[n][1].start},end:{...e[e.length-1][1].end}};return e[r][1].type="setextHeadingText",i?(e.splice(r,0,["enter",o,t]),e.splice(i+1,0,["exit",e[n][1],t]),e[n][1].end={...e[i][1].end}):e[n][1]=o,e.push(["exit",o,t]),e},tokenize:function(e,t,n){let r;let i=this;return function(t){let o,a=i.events.length;for(;a--;)if("lineEnding"!==i.events[a][1].type&&"linePrefix"!==i.events[a][1].type&&"content"!==i.events[a][1].type){o="paragraph"===i.events[a][1].type;break}return!i.parser.lazy[i.now().line]&&(i.interrupt||o)?(e.enter("setextHeadingLine"),r=t,e.enter("setextHeadingLineSequence"),function t(n){return n===r?(e.consume(n),t):(e.exit("setextHeadingLineSequence"),(0,$.xz)(n)?(0,q.f)(e,l,"lineSuffix")(n):l(n))}(t)):n(t)};function l(r){return null===r||(0,$.Ch)(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}},ex=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],eb=["pre","script","style","textarea"],ev={partial:!0,tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(Q.w,t,n)}}},eS={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return(0,$.Ch)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):n(t)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},ew={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return null===t?n(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},eC={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){let r;let i=this,l={partial:!0,tokenize:function(e,t,n){let l=0;return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o};function o(t){return e.enter("codeFencedFence"),(0,$.xz)(t)?(0,q.f)(e,u,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):u(t)}function u(t){return t===r?(e.enter("codeFencedFenceSequence"),function t(i){return i===r?(l++,e.consume(i),t):l>=a?(e.exit("codeFencedFenceSequence"),(0,$.xz)(i)?(0,q.f)(e,s,"whitespace")(i):s(i)):n(i)}(t)):n(t)}function s(r){return null===r||(0,$.Ch)(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}},o=0,a=0;return function(t){return function(t){let l=i.events[i.events.length-1];return o=l&&"linePrefix"===l[1].type?l[2].sliceSerialize(l[1],!0).length:0,r=t,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),function t(i){return i===r?(a++,e.consume(i),t):a<3?n(i):(e.exit("codeFencedFenceSequence"),(0,$.xz)(i)?(0,q.f)(e,u,"whitespace")(i):u(i))}(t)}(t)};function u(l){return null===l||(0,$.Ch)(l)?(e.exit("codeFencedFence"),i.interrupt?t(l):e.check(ew,c,d)(l)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||(0,$.Ch)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),u(i)):(0,$.xz)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),(0,q.f)(e,s,"whitespace")(i)):96===i&&i===r?n(i):(e.consume(i),t)}(l))}function s(t){return null===t||(0,$.Ch)(t)?u(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||(0,$.Ch)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),u(i)):96===i&&i===r?n(i):(e.consume(i),t)}(t))}function c(t){return e.attempt(l,d,f)(t)}function f(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),p}function p(t){return o>0&&(0,$.xz)(t)?(0,q.f)(e,h,"linePrefix",o+1)(t):h(t)}function h(t){return null===t||(0,$.Ch)(t)?e.check(ew,c,d)(t):(e.enter("codeFlowValue"),function t(n){return null===n||(0,$.Ch)(n)?(e.exit("codeFlowValue"),h(n)):(e.consume(n),t)}(t))}function d(n){return e.exit("codeFenced"),t(n)}}},eE=document.createElement("i");function eI(e){let t="&"+e+";";eE.innerHTML=t;let n=eE.textContent;return(59!==n.charCodeAt(n.length-1)||"semi"===e)&&n!==t&&n}let ez={name:"characterReference",tokenize:function(e,t,n){let r,i;let l=this,o=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),a};function a(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),u):(e.enter("characterReferenceValue"),r=31,i=$.H$,s(t))}function u(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),r=6,i=$.AF,s):(e.enter("characterReferenceValue"),r=7,i=$.pY,s(t))}function s(a){if(59===a&&o){let r=e.exit("characterReferenceValue");return i!==$.H$||eI(l.sliceSerialize(r))?(e.enter("characterReferenceMarker"),e.consume(a),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(a)}return i(a)&&o++<r?(e.consume(a),s):n(a)}}},eT={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){return(0,$.sR)(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}},eP={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),(0,q.f)(e,t,"linePrefix")}}};var eA=n(10741);let eD={name:"labelEnd",resolveAll:function(e){let t=-1,n=[];for(;++t<e.length;){let r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){let e="labelImage"===r.type?4:2;r.type="data",t+=e}}return e.length!==n.length&&(0,j.d)(e,0,e.length,n),e},resolveTo:function(e,t){let n,r,i,l,o=e.length,a=0;for(;o--;)if(n=e[o][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[o][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(i){if("enter"===e[o][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=o,"labelLink"!==n.type)){a=2;break}}else"labelEnd"===n.type&&(i=o);let u={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},s={type:"label",start:{...e[r][1].start},end:{...e[i][1].end}},c={type:"labelText",start:{...e[r+a+2][1].end},end:{...e[i-2][1].start}};return l=[["enter",u,t],["enter",s,t]],l=(0,j.V)(l,e.slice(r+1,r+a+3)),l=(0,j.V)(l,[["enter",c,t]]),l=(0,j.V)(l,(0,eA.C)(t.parser.constructs.insideSpan.null,e.slice(r+a+4,i-3),t)),l=(0,j.V)(l,[["exit",c,t],e[i-2],e[i-1],["exit",s,t]]),l=(0,j.V)(l,e.slice(i+1)),l=(0,j.V)(l,[["exit",u,t]]),(0,j.d)(e,r,e.length,l),e},tokenize:function(e,t,n){let r,i;let l=this,o=l.events.length;for(;o--;)if(("labelImage"===l.events[o][1].type||"labelLink"===l.events[o][1].type)&&!l.events[o][1]._balanced){r=l.events[o][1];break}return function(t){return r?r._inactive?c(t):(i=l.parser.defined.includes((0,ed.d)(l.sliceSerialize({start:r.end,end:l.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),a):n(t)};function a(t){return 40===t?e.attempt(eO,s,i?s:c)(t):91===t?e.attempt(eL,s,i?u:c)(t):i?s(t):c(t)}function u(t){return e.attempt(eF,s,c)(t)}function s(e){return t(e)}function c(e){return r._balanced=!0,n(e)}}},eO={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),r};function r(t){return(0,$.z3)(t)?eh(e,i)(t):i(t)}function i(t){return 41===t?s(t):ec(e,l,o,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function l(t){return(0,$.z3)(t)?eh(e,a)(t):s(t)}function o(e){return n(e)}function a(t){return 34===t||39===t||40===t?ep(e,u,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):s(t)}function u(t){return(0,$.z3)(t)?eh(e,s)(t):s(t)}function s(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},eL={tokenize:function(e,t,n){let r=this;return function(t){return ef.call(r,e,i,l,"reference","referenceMarker","referenceString")(t)};function i(e){return r.parser.defined.includes((0,ed.d)(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function l(e){return n(e)}}},eF={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}},eM={name:"labelStartImage",resolveAll:eD.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),i};function i(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),l):n(t)}function l(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};var eR=n(63921);let e_={name:"attention",resolveAll:function(e,t){let n,r,i,l,o,a,u,s,c=-1;for(;++c<e.length;)if("enter"===e[c][0]&&"attentionSequence"===e[c][1].type&&e[c][1]._close){for(n=c;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[c][1]).charCodeAt(0)){if((e[n][1]._close||e[c][1]._open)&&(e[c][1].end.offset-e[c][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[c][1].end.offset-e[c][1].start.offset)%3))continue;a=e[n][1].end.offset-e[n][1].start.offset>1&&e[c][1].end.offset-e[c][1].start.offset>1?2:1;let f={...e[n][1].end},p={...e[c][1].start};eN(f,-a),eN(p,a),l={type:a>1?"strongSequence":"emphasisSequence",start:f,end:{...e[n][1].end}},o={type:a>1?"strongSequence":"emphasisSequence",start:{...e[c][1].start},end:p},i={type:a>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[c][1].start}},r={type:a>1?"strong":"emphasis",start:{...l.start},end:{...o.end}},e[n][1].end={...l.start},e[c][1].start={...o.end},u=[],e[n][1].end.offset-e[n][1].start.offset&&(u=(0,j.V)(u,[["enter",e[n][1],t],["exit",e[n][1],t]])),u=(0,j.V)(u,[["enter",r,t],["enter",l,t],["exit",l,t],["enter",i,t]]),u=(0,j.V)(u,(0,eA.C)(t.parser.constructs.insideSpan.null,e.slice(n+1,c),t)),u=(0,j.V)(u,[["exit",i,t],["enter",o,t],["exit",o,t],["exit",r,t]]),e[c][1].end.offset-e[c][1].start.offset?(s=2,u=(0,j.V)(u,[["enter",e[c][1],t],["exit",e[c][1],t]])):s=0,(0,j.d)(e,n-1,c-n+3,u),c=n+u.length-s-2;break}}for(c=-1;++c<e.length;)"attentionSequence"===e[c][1].type&&(e[c][1].type="data");return e},tokenize:function(e,t){let n;let r=this.parser.constructs.attentionMarkers.null,i=this.previous,l=(0,eR.r)(i);return function(o){return n=o,e.enter("attentionSequence"),function o(a){if(a===n)return e.consume(a),o;let u=e.exit("attentionSequence"),s=(0,eR.r)(a),c=!s||2===s&&l||r.includes(a),f=!l||2===l&&s||r.includes(i);return u._open=!!(42===n?c:c&&(l||!f)),u._close=!!(42===n?f:f&&(s||!c)),t(a)}(o)}}};function eN(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}let ej={name:"labelStartLink",resolveAll:eD.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),i};function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}},eB={42:eo,43:eo,45:eo,48:eo,49:eo,50:eo,51:eo,52:eo,53:eo,54:eo,55:eo,56:eo,57:eo,62:es},eH={91:{name:"definition",tokenize:function(e,t,n){let r;let i=this;return function(t){return e.enter("definition"),ef.call(i,e,l,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(t)};function l(t){return(r=(0,ed.d)(i.sliceSerialize(i.events[i.events.length-1][1]).slice(1,-1)),58===t)?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),o):n(t)}function o(t){return(0,$.z3)(t)?eh(e,a)(t):a(t)}function a(t){return ec(e,u,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function u(t){return e.attempt(em,s,s)(t)}function s(t){return(0,$.xz)(t)?(0,q.f)(e,c,"whitespace")(t):c(t)}function c(l){return null===l||(0,$.Ch)(l)?(e.exit("definition"),i.parser.defined.push(r),t(l)):n(l)}}}},eV={[-2]:eg,[-1]:eg,32:eg},eU={35:{name:"headingAtx",resolve:function(e,t){let n,r,i=e.length-2,l=3;return"whitespace"===e[3][1].type&&(l+=2),i-2>l&&"whitespace"===e[i][1].type&&(i-=2),"atxHeadingSequence"===e[i][1].type&&(l===i-1||i-4>l&&"whitespace"===e[i-2][1].type)&&(i-=l+1===i?2:4),i>l&&(n={type:"atxHeadingText",start:e[l][1].start,end:e[i][1].end},r={type:"chunkText",start:e[l][1].start,end:e[i][1].end,contentType:"text"},(0,j.d)(e,l,i-l+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]])),e},tokenize:function(e,t,n){let r=0;return function(i){return e.enter("atxHeading"),e.enter("atxHeadingSequence"),function i(l){return 35===l&&r++<6?(e.consume(l),i):null===l||(0,$.z3)(l)?(e.exit("atxHeadingSequence"),function n(r){return 35===r?(e.enter("atxHeadingSequence"),function t(r){return 35===r?(e.consume(r),t):(e.exit("atxHeadingSequence"),n(r))}(r)):null===r||(0,$.Ch)(r)?(e.exit("atxHeading"),t(r)):(0,$.xz)(r)?(0,q.f)(e,n,"whitespace")(r):(e.enter("atxHeadingText"),function t(r){return null===r||35===r||(0,$.z3)(r)?(e.exit("atxHeadingText"),n(r)):(e.consume(r),t)}(r))}(l)):n(l)}(i)}}},42:el,45:[ek,el],60:{concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););return t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e},tokenize:function(e,t,n){let r,i,l,o,a;let u=this;return function(t){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(t),s};function s(o){return 33===o?(e.consume(o),c):47===o?(e.consume(o),i=!0,h):63===o?(e.consume(o),r=3,u.interrupt?t:O):(0,$.jv)(o)?(e.consume(o),l=String.fromCharCode(o),d):n(o)}function c(i){return 45===i?(e.consume(i),r=2,f):91===i?(e.consume(i),r=5,o=0,p):(0,$.jv)(i)?(e.consume(i),r=4,u.interrupt?t:O):n(i)}function f(r){return 45===r?(e.consume(r),u.interrupt?t:O):n(r)}function p(r){let i="CDATA[";return r===i.charCodeAt(o++)?(e.consume(r),o===i.length)?u.interrupt?t:C:p:n(r)}function h(t){return(0,$.jv)(t)?(e.consume(t),l=String.fromCharCode(t),d):n(t)}function d(o){if(null===o||47===o||62===o||(0,$.z3)(o)){let a=47===o,s=l.toLowerCase();return!a&&!i&&eb.includes(s)?(r=1,u.interrupt?t(o):C(o)):ex.includes(l.toLowerCase())?(r=6,a)?(e.consume(o),m):u.interrupt?t(o):C(o):(r=7,u.interrupt&&!u.parser.lazy[u.now().line]?n(o):i?function t(n){return(0,$.xz)(n)?(e.consume(n),t):S(n)}(o):g(o))}return 45===o||(0,$.H$)(o)?(e.consume(o),l+=String.fromCharCode(o),d):n(o)}function m(r){return 62===r?(e.consume(r),u.interrupt?t:C):n(r)}function g(t){return 47===t?(e.consume(t),S):58===t||95===t||(0,$.jv)(t)?(e.consume(t),y):(0,$.xz)(t)?(e.consume(t),g):S(t)}function y(t){return 45===t||46===t||58===t||95===t||(0,$.H$)(t)?(e.consume(t),y):k(t)}function k(t){return 61===t?(e.consume(t),x):(0,$.xz)(t)?(e.consume(t),k):g(t)}function x(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),a=t,b):(0,$.xz)(t)?(e.consume(t),x):function t(n){return null===n||34===n||39===n||47===n||60===n||61===n||62===n||96===n||(0,$.z3)(n)?k(n):(e.consume(n),t)}(t)}function b(t){return t===a?(e.consume(t),a=null,v):null===t||(0,$.Ch)(t)?n(t):(e.consume(t),b)}function v(e){return 47===e||62===e||(0,$.xz)(e)?g(e):n(e)}function S(t){return 62===t?(e.consume(t),w):n(t)}function w(t){return null===t||(0,$.Ch)(t)?C(t):(0,$.xz)(t)?(e.consume(t),w):n(t)}function C(t){return 45===t&&2===r?(e.consume(t),T):60===t&&1===r?(e.consume(t),P):62===t&&4===r?(e.consume(t),L):63===t&&3===r?(e.consume(t),O):93===t&&5===r?(e.consume(t),D):(0,$.Ch)(t)&&(6===r||7===r)?(e.exit("htmlFlowData"),e.check(ev,F,E)(t)):null===t||(0,$.Ch)(t)?(e.exit("htmlFlowData"),E(t)):(e.consume(t),C)}function E(t){return e.check(eS,I,F)(t)}function I(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),z}function z(t){return null===t||(0,$.Ch)(t)?E(t):(e.enter("htmlFlowData"),C(t))}function T(t){return 45===t?(e.consume(t),O):C(t)}function P(t){return 47===t?(e.consume(t),l="",A):C(t)}function A(t){if(62===t){let n=l.toLowerCase();return eb.includes(n)?(e.consume(t),L):C(t)}return(0,$.jv)(t)&&l.length<8?(e.consume(t),l+=String.fromCharCode(t),A):C(t)}function D(t){return 93===t?(e.consume(t),O):C(t)}function O(t){return 62===t?(e.consume(t),L):45===t&&2===r?(e.consume(t),O):C(t)}function L(t){return null===t||(0,$.Ch)(t)?(e.exit("htmlFlowData"),F(t)):(e.consume(t),L)}function F(n){return e.exit("htmlFlow"),t(n)}}},61:ek,95:el,96:eC,126:eC},eq={38:ez,92:eT},e$={[-5]:eP,[-4]:eP,[-3]:eP,33:eM,38:ez,42:e_,60:[{name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),i};function i(t){return(0,$.jv)(t)?(e.consume(t),l):64===t?n(t):a(t)}function l(t){return 43===t||45===t||46===t||(0,$.H$)(t)?(r=1,function t(n){return 58===n?(e.consume(n),r=0,o):(43===n||45===n||46===n||(0,$.H$)(n))&&r++<32?(e.consume(n),t):(r=0,a(n))}(t)):a(t)}function o(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||(0,$.Av)(r)?n(r):(e.consume(r),o)}function a(t){return 64===t?(e.consume(t),u):(0,$.n9)(t)?(e.consume(t),a):n(t)}function u(i){return(0,$.H$)(i)?function i(l){return 46===l?(e.consume(l),r=0,u):62===l?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(l),e.exit("autolinkMarker"),e.exit("autolink"),t):function t(l){if((45===l||(0,$.H$)(l))&&r++<63){let n=45===l?t:i;return e.consume(l),n}return n(l)}(l)}(i):n(i)}}},{name:"htmlText",tokenize:function(e,t,n){let r,i,l;let o=this;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),a};function a(t){return 33===t?(e.consume(t),u):47===t?(e.consume(t),b):63===t?(e.consume(t),k):(0,$.jv)(t)?(e.consume(t),S):n(t)}function u(t){return 45===t?(e.consume(t),s):91===t?(e.consume(t),i=0,h):(0,$.jv)(t)?(e.consume(t),y):n(t)}function s(t){return 45===t?(e.consume(t),p):n(t)}function c(t){return null===t?n(t):45===t?(e.consume(t),f):(0,$.Ch)(t)?(l=c,A(t)):(e.consume(t),c)}function f(t){return 45===t?(e.consume(t),p):c(t)}function p(e){return 62===e?P(e):45===e?f(e):c(e)}function h(t){let r="CDATA[";return t===r.charCodeAt(i++)?(e.consume(t),i===r.length?d:h):n(t)}function d(t){return null===t?n(t):93===t?(e.consume(t),m):(0,$.Ch)(t)?(l=d,A(t)):(e.consume(t),d)}function m(t){return 93===t?(e.consume(t),g):d(t)}function g(t){return 62===t?P(t):93===t?(e.consume(t),g):d(t)}function y(t){return null===t||62===t?P(t):(0,$.Ch)(t)?(l=y,A(t)):(e.consume(t),y)}function k(t){return null===t?n(t):63===t?(e.consume(t),x):(0,$.Ch)(t)?(l=k,A(t)):(e.consume(t),k)}function x(e){return 62===e?P(e):k(e)}function b(t){return(0,$.jv)(t)?(e.consume(t),v):n(t)}function v(t){return 45===t||(0,$.H$)(t)?(e.consume(t),v):function t(n){return(0,$.Ch)(n)?(l=t,A(n)):(0,$.xz)(n)?(e.consume(n),t):P(n)}(t)}function S(t){return 45===t||(0,$.H$)(t)?(e.consume(t),S):47===t||62===t||(0,$.z3)(t)?w(t):n(t)}function w(t){return 47===t?(e.consume(t),P):58===t||95===t||(0,$.jv)(t)?(e.consume(t),C):(0,$.Ch)(t)?(l=w,A(t)):(0,$.xz)(t)?(e.consume(t),w):P(t)}function C(t){return 45===t||46===t||58===t||95===t||(0,$.H$)(t)?(e.consume(t),C):function t(n){return 61===n?(e.consume(n),E):(0,$.Ch)(n)?(l=t,A(n)):(0,$.xz)(n)?(e.consume(n),t):w(n)}(t)}function E(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),r=t,I):(0,$.Ch)(t)?(l=E,A(t)):(0,$.xz)(t)?(e.consume(t),E):(e.consume(t),z)}function I(t){return t===r?(e.consume(t),r=void 0,T):null===t?n(t):(0,$.Ch)(t)?(l=I,A(t)):(e.consume(t),I)}function z(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||(0,$.z3)(t)?w(t):(e.consume(t),z)}function T(e){return 47===e||62===e||(0,$.z3)(e)?w(e):n(e)}function P(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function A(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),D}function D(t){return(0,$.xz)(t)?(0,q.f)(e,O,"linePrefix",o.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):O(t)}function O(t){return e.enter("htmlTextData"),l(t)}}}],91:ej,92:[{name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),r};function r(r){return(0,$.Ch)(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}},eT],93:eD,95:e_,96:{name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,r=e.length-4,i=3;if(("lineEnding"===e[3][1].type||"space"===e[i][1].type)&&("lineEnding"===e[r][1].type||"space"===e[r][1].type)){for(t=i;++t<r;)if("codeTextData"===e[t][1].type){e[i][1].type="codeTextPadding",e[r][1].type="codeTextPadding",i+=2,r-=2;break}}for(t=i-1,r++;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):(t===r||"lineEnding"===e[t][1].type)&&(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let r,i,l=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),function t(n){return 96===n?(e.consume(n),l++,t):(e.exit("codeTextSequence"),o(n))}(t)};function o(u){return null===u?n(u):32===u?(e.enter("space"),e.consume(u),e.exit("space"),o):96===u?(i=e.enter("codeTextSequence"),r=0,function n(o){return 96===o?(e.consume(o),r++,n):r===l?(e.exit("codeTextSequence"),e.exit("codeText"),t(o)):(i.type="codeTextData",a(o))}(u)):(0,$.Ch)(u)?(e.enter("lineEnding"),e.consume(u),e.exit("lineEnding"),o):(e.enter("codeTextData"),a(u))}function a(t){return null===t||32===t||96===t||(0,$.Ch)(t)?(e.exit("codeTextData"),o(t)):(e.consume(t),a)}}}},eW={null:[e_,G]},eY={null:[42,95]},eK={null:[]},eQ=/[\0\t\n\r]/g;function eX(e,t){let n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(65535&n)==65535||(65535&n)==65534||n>1114111?"�":String.fromCodePoint(n)}let eZ=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function eJ(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){let e=n.charCodeAt(1),t=120===e||88===e;return eX(n.slice(t?2:1),t?16:10)}return eI(n)||e}let eG={}.hasOwnProperty;function e0(e){return{line:e.line,column:e.column,offset:e.offset}}function e1(e,t){if(e)throw Error("Cannot close `"+e.type+"` ("+k({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+k({start:t.start,end:t.end})+") is open");throw Error("Cannot close document, a token (`"+t.type+"`, "+k({start:t.start,end:t.end})+") is still open")}function e2(e){let t=this;t.parser=function(n){var i,l;let o,a,u,s;return"string"!=typeof(i={...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})&&(l=i,i=void 0),(function(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:r(y),autolinkProtocol:s,autolinkEmail:s,atxHeading:r(d),blockQuote:r(function(){return{type:"blockquote",children:[]}}),characterEscape:s,characterReference:s,codeFenced:r(h),codeFencedFenceInfo:i,codeFencedFenceMeta:i,codeIndented:r(h,i),codeText:r(function(){return{type:"inlineCode",value:""}},i),codeTextData:s,data:s,codeFlowValue:s,definition:r(function(){return{type:"definition",identifier:"",label:null,title:null,url:""}}),definitionDestinationString:i,definitionLabelString:i,definitionTitleString:i,emphasis:r(function(){return{type:"emphasis",children:[]}}),hardBreakEscape:r(m),hardBreakTrailing:r(m),htmlFlow:r(g,i),htmlFlowData:s,htmlText:r(g,i),htmlTextData:s,image:r(function(){return{type:"image",title:null,url:"",alt:null}}),label:i,link:r(y),listItem:r(function(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}),listItemValue:function(e){this.data.expectingFirstListItemValue&&(this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0)},listOrdered:r(x,function(){this.data.expectingFirstListItemValue=!0}),listUnordered:r(x),paragraph:r(function(){return{type:"paragraph",children:[]}}),reference:function(){this.data.referenceType="collapsed"},referenceString:i,resourceDestinationString:i,resourceTitleString:i,setextHeading:r(d),strong:r(function(){return{type:"strong",children:[]}}),thematicBreak:r(function(){return{type:"thematicBreak"}})},exit:{atxHeading:o(),atxHeadingSequence:function(e){let t=this.stack[this.stack.length-1];if(!t.depth){let n=this.sliceSerialize(e).length;t.depth=n}},autolink:o(),autolinkEmail:function(e){c.call(this,e),this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)},autolinkProtocol:function(e){c.call(this,e),this.stack[this.stack.length-1].url=this.sliceSerialize(e)},blockQuote:o(),characterEscapeValue:c,characterReferenceMarkerHexadecimal:p,characterReferenceMarkerNumeric:p,characterReferenceValue:function(e){let t;let n=this.sliceSerialize(e),r=this.data.characterReferenceType;r?(t=eX(n,"characterReferenceMarkerNumeric"===r?10:16),this.data.characterReferenceType=void 0):t=eI(n);let i=this.stack[this.stack.length-1];i.value+=t},characterReference:function(e){this.stack.pop().position.end=e0(e.end)},codeFenced:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}),codeFencedFence:function(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)},codeFencedFenceInfo:function(){let e=this.resume();this.stack[this.stack.length-1].lang=e},codeFencedFenceMeta:function(){let e=this.resume();this.stack[this.stack.length-1].meta=e},codeFlowValue:c,codeIndented:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}),codeText:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),codeTextData:c,data:c,definition:o(),definitionDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},definitionLabelString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,ed.d)(this.sliceSerialize(e)).toLowerCase()},definitionTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},emphasis:o(),hardBreakEscape:o(f),hardBreakTrailing:o(f),htmlFlow:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlFlowData:c,htmlText:o(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlTextData:c,image:o(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),label:function(){let e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];if(this.data.inReference=!0,"link"===n.type){let t=e.children;n.children=t}else n.alt=t},labelText:function(e){let t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=t.replace(eZ,eJ),n.identifier=(0,ed.d)(t).toLowerCase()},lineEnding:function(e){let n=this.stack[this.stack.length-1];if(this.data.atHardBreak){n.children[n.children.length-1].position.end=e0(e.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(s.call(this,e),c.call(this,e))},link:o(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),listItem:o(),listOrdered:o(),listUnordered:o(),paragraph:o(),referenceString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,ed.d)(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"},resourceDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},resourceTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},resource:function(){this.data.inReference=void 0},setextHeading:o(function(){this.data.setextHeadingSlurpLineEnding=void 0}),setextHeadingLineSequence:function(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2},setextHeadingText:function(){this.data.setextHeadingSlurpLineEnding=!0},strong:o(),thematicBreak:o()}};(function e(t,n){let r=-1;for(;++r<n.length;){let i=n[r];Array.isArray(i)?e(t,i):function(e,t){let n;for(n in t)if(eG.call(t,n))switch(n){case"canContainEols":{let r=t[n];r&&e[n].push(...r);break}case"transforms":{let r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{let r=t[n];r&&Object.assign(e[n],r)}}}(t,i)}})(t,(e||{}).mdastExtensions||[]);let n={};return function(e){let r={type:"root",children:[]},o={stack:[r],tokenStack:[],config:t,enter:l,exit:a,buffer:i,resume:u,data:n},s=[],c=-1;for(;++c<e.length;)("listOrdered"===e[c][1].type||"listUnordered"===e[c][1].type)&&("enter"===e[c][0]?s.push(c):c=function(e,t,n){let r,i,l,o,a=t-1,u=-1,s=!1;for(;++a<=n;){let t=e[a];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?u++:u--,o=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||o||u||l||(l=a),o=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:o=void 0}if(!u&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===u&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let o=a;for(i=void 0;o--;){let t=e[o];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;i&&(e[i][1].type="lineEndingBlank",s=!0),t[1].type="lineEnding",i=o}else if("linePrefix"===t[1].type||"blockQuotePrefix"===t[1].type||"blockQuotePrefixWhitespace"===t[1].type||"blockQuoteMarker"===t[1].type||"listItemIndent"===t[1].type);else break}l&&(!i||l<i)&&(r._spread=!0),r.end=Object.assign({},i?e[i][1].start:t[1].end),e.splice(i||a,0,["exit",r,t[2]]),a++,n++}if("listItemPrefix"===t[1].type){let i={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=i,e.splice(a,0,["enter",i,t[2]]),a++,n++,l=void 0,o=!0}}}return e[t][1]._spread=s,n}(e,s.pop(),c));for(c=-1;++c<e.length;){let n=t[e[c][0]];eG.call(n,e[c][1].type)&&n[e[c][1].type].call(Object.assign({sliceSerialize:e[c][2].sliceSerialize},o),e[c][1])}if(o.tokenStack.length>0){let e=o.tokenStack[o.tokenStack.length-1];(e[1]||e1).call(o,void 0,e[0])}for(r.position={start:e0(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:e0(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},c=-1;++c<t.transforms.length;)r=t.transforms[c](r)||r;return r};function r(e,t){return function(n){l.call(this,e(n),n),t&&t.call(this,n)}}function i(){this.stack.push({type:"fragment",children:[]})}function l(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:e0(t.start),end:void 0}}function o(e){return function(t){e&&e.call(this,t),a.call(this,t)}}function a(e,t){let n=this.stack.pop(),r=this.tokenStack.pop();if(r)r[0].type!==e.type&&(t?t.call(this,e,r[0]):(r[1]||e1).call(this,e,r[0]));else throw Error("Cannot close `"+e.type+"` ("+k({start:e.start,end:e.end})+"): it’s not open");n.position.end=e0(e.end)}function u(){return(0,N.B)(this.stack.pop())}function s(e){let t=this.stack[this.stack.length-1].children,n=t[t.length-1];n&&"text"===n.type||((n={type:"text",value:""}).position={start:e0(e.start),end:void 0},t.push(n)),this.stack.push(n)}function c(e){let t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=e0(e.end)}function f(){this.data.atHardBreak=!0}function p(e){this.data.characterReferenceType=e.type}function h(){return{type:"code",lang:null,meta:null,value:""}}function d(){return{type:"heading",depth:0,children:[]}}function m(){return{type:"break"}}function g(){return{type:"html",value:""}}function y(){return{type:"link",title:null,url:"",children:[]}}function x(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}})(l)(function(e){for(;!V(e););return e}((function(e){let t={constructs:(0,U.W)([r,...(e||{}).extensions||[]]),content:n(W),defined:[],document:n(Y),flow:n(J),lazy:{},string:n(ee),text:n(et)};return t;function n(e){return function(n){return function(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},i={},l=[],o=[],a=[],u={attempt:d(function(e,t){m(e,t.from)}),check:d(h),consume:function(e){(0,$.Ch)(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,g()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===o[r._index].length&&(r._bufferIndex=-1,r._index++)),s.previous=e},enter:function(e,t){let n=t||{};return n.type=e,n.start=p(),s.events.push(["enter",n,s]),a.push(n),n},exit:function(e){let t=a.pop();return t.end=p(),s.events.push(["exit",t,s]),t},interrupt:d(h,{interrupt:!0})},s={code:null,containerState:{},defineSkip:function(e){i[e.line]=e.column,g()},events:[],now:p,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n,r=-1,i=[];for(;++r<e.length;){let l;let o=e[r];if("string"==typeof o)l=o;else switch(o){case -5:l="\r";break;case -4:l="\n";break;case -3:l="\r\n";break;case -2:l=t?" ":"	";break;case -1:if(!t&&n)continue;l=" ";break;default:l=String.fromCharCode(o)}n=-2===o,i.push(l)}return i.join("")}(f(e),t)},sliceStream:f,write:function(e){return(o=(0,j.V)(o,e),function(){let e;for(;r._index<o.length;){let n=o[r._index];if("string"==typeof n)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<n.length;){var t;t=n.charCodeAt(r._bufferIndex),c=c(t)}else c=c(n)}}(),null!==o[o.length-1])?[]:(m(t,0),s.events=(0,eA.C)(l,s.events,s),s.events)}},c=t.tokenize.call(s,u);return t.resolveAll&&l.push(t),s;function f(e){return function(e,t){let n;let r=t.start._index,i=t.start._bufferIndex,l=t.end._index,o=t.end._bufferIndex;if(r===l)n=[e[r].slice(i,o)];else{if(n=e.slice(r,l),i>-1){let e=n[0];"string"==typeof e?n[0]=e.slice(i):n.shift()}o>0&&n.push(e[l].slice(0,o))}return n}(o,e)}function p(){let{_bufferIndex:e,_index:t,line:n,column:i,offset:l}=r;return{_bufferIndex:e,_index:t,line:n,column:i,offset:l}}function h(e,t){t.restore()}function d(e,t){return function(n,i,l){let o,c,f,h;return Array.isArray(n)?d(n):"tokenize"in n?d([n]):function(e){let t=null!==e&&n[e],r=null!==e&&n.null;return d([...Array.isArray(t)?t:t?[t]:[],...Array.isArray(r)?r:r?[r]:[]])(e)};function d(e){return(o=e,c=0,0===e.length)?l:m(e[c])}function m(e){return function(n){return(h=function(){let e=p(),t=s.previous,n=s.currentConstruct,i=s.events.length,l=Array.from(a);return{from:i,restore:function(){r=e,s.previous=t,s.currentConstruct=n,s.events.length=i,a=l,g()}}}(),f=e,e.partial||(s.currentConstruct=e),e.name&&s.parser.constructs.disable.null.includes(e.name))?k(n):e.tokenize.call(t?Object.assign(Object.create(s),t):s,u,y,k)(n)}}function y(t){return e(f,h),i}function k(e){return(h.restore(),++c<o.length)?m(o[c]):l}}}function m(e,t){e.resolveAll&&!l.includes(e)&&l.push(e),e.resolve&&(0,j.d)(s.events,t,s.events.length-t,e.resolve(s.events.slice(t),s)),e.resolveTo&&(s.events=e.resolveTo(s.events,s))}function g(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}(t,e,n)}}})(l).document().write((a=1,u="",s=!0,function(e,t,n){let r,i,l,c,f;let p=[];for(e=u+("string"==typeof e?e.toString():new TextDecoder(t||void 0).decode(e)),l=0,u="",s&&(65279===e.charCodeAt(0)&&l++,s=void 0);l<e.length;){if(eQ.lastIndex=l,c=(r=eQ.exec(e))&&void 0!==r.index?r.index:e.length,f=e.charCodeAt(c),!r){u=e.slice(l);break}if(10===f&&l===c&&o)p.push(-3),o=void 0;else switch(o&&(p.push(-5),o=void 0),l<c&&(p.push(e.slice(l,c)),a+=c-l),f){case 0:p.push(65533),a++;break;case 9:for(i=4*Math.ceil(a/4),p.push(-2);a++<i;)p.push(-1);break;case 10:p.push(-4),a=1;break;default:o=!0,a=1}l=c+1}return n&&(o&&p.push(-5),u&&p.push(u),p.push(null)),p})(n,i,!0))))}}var e4=n(47465);function e3(e){let t=[],n=-1,r=0,i=0;for(;++n<e.length;){let l=e.charCodeAt(n),o="";if(37===l&&(0,$.H$)(e.charCodeAt(n+1))&&(0,$.H$)(e.charCodeAt(n+2)))i=2;else if(l<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(l))||(o=String.fromCharCode(l));else if(l>55295&&l<57344){let t=e.charCodeAt(n+1);l<56320&&t>56319&&t<57344?(o=String.fromCharCode(l,t),i=1):o="�"}else o=String.fromCharCode(l);o&&(t.push(e.slice(r,n),encodeURIComponent(o)),r=n+i+1,o=""),i&&(n+=i,i=0)}return t.join("")+e.slice(r)}function e9(e,t){let n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function e5(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}var e6=n(94794);function e7(e,t){let n=t.referenceType,r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];let i=e.all(t),l=i[0];l&&"text"===l.type?l.value="["+l.value:i.unshift({type:"text",value:"["});let o=i[i.length-1];return o&&"text"===o.type?o.value+=r:i.push({type:"text",value:r}),i}function e8(e){let t=e.spread;return null==t?e.children.length>1:t}function te(e,t,n){let r=0,i=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(i-1);for(;9===t||32===t;)i--,t=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}let tt={blockquote:function(e,t){let n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){let n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){let n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let i={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(i.data={meta:t.meta}),e.patch(t,i),i={type:"element",tagName:"pre",properties:{},children:[i=e.applyData(t,i)]},e.patch(t,i),i},delete:function(e,t){let n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){let n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){let n;let r="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",i=String(t.identifier).toUpperCase(),l=e3(i.toLowerCase()),o=e.footnoteOrder.indexOf(i),a=e.footnoteCounts.get(i);void 0===a?(a=0,e.footnoteOrder.push(i),n=e.footnoteOrder.length):n=o+1,a+=1,e.footnoteCounts.set(i,a);let u={type:"element",tagName:"a",properties:{href:"#"+r+"fn-"+l,id:r+"fnref-"+l+(a>1?"-"+a:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(n)}]};e.patch(t,u);let s={type:"element",tagName:"sup",properties:{},children:[u]};return e.patch(t,s),e.applyData(t,s)},heading:function(e,t){let n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){let n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return e7(e,t);let i={src:e3(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(i.title=r.title);let l={type:"element",tagName:"img",properties:i,children:[]};return e.patch(t,l),e.applyData(t,l)},image:function(e,t){let n={src:e3(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){let n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);let r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return e7(e,t);let i={href:e3(r.url||"")};null!==r.title&&void 0!==r.title&&(i.title=r.title);let l={type:"element",tagName:"a",properties:i,children:e.all(t)};return e.patch(t,l),e.applyData(t,l)},link:function(e,t){let n={href:e3(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){let r=e.all(t),i=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;let n=e.children,r=-1;for(;!t&&++r<n.length;)t=e8(n[r])}return t}(n):e8(t),l={},o=[];if("boolean"==typeof t.checked){let e;let n=r[0];n&&"element"===n.type&&"p"===n.tagName?e=n:(e={type:"element",tagName:"p",properties:{},children:[]},r.unshift(e)),e.children.length>0&&e.children.unshift({type:"text",value:" "}),e.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),l.className=["task-list-item"]}let a=-1;for(;++a<r.length;){let e=r[a];(i||0!==a||"element"!==e.type||"p"!==e.tagName)&&o.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||i?o.push(e):o.push(...e.children)}let u=r[r.length-1];u&&(i||"element"!==u.type||"p"!==u.tagName)&&o.push({type:"text",value:"\n"});let s={type:"element",tagName:"li",properties:l,children:o};return e.patch(t,s),e.applyData(t,s)},list:function(e,t){let n={},r=e.all(t),i=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++i<r.length;){let e=r[i];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let l={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,l),e.applyData(t,l)},paragraph:function(e,t){let n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){let n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){let n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){let n=e.all(t),r=n.shift(),i=[];if(r){let n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),i.push(n)}if(n.length>0){let r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},l=(0,y.Pk)(t.children[1]),o=(0,y.rb)(t.children[t.children.length-1]);l&&o&&(r.position={start:l,end:o}),i.push(r)}let l={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(t,l),e.applyData(t,l)},tableCell:function(e,t){let n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){let r=n?n.children:void 0,i=0===(r?r.indexOf(t):1)?"th":"td",l=n&&"table"===n.type?n.align:void 0,o=l?l.length:t.children.length,a=-1,u=[];for(;++a<o;){let n=t.children[a],r={},o=l?l[a]:void 0;o&&(r.align=o);let s={type:"element",tagName:i,properties:r,children:[]};n&&(s.children=e.all(n),e.patch(n,s),s=e.applyData(n,s)),u.push(s)}let s={type:"element",tagName:"tr",properties:{},children:e.wrap(u,!0)};return e.patch(t,s),e.applyData(t,s)},text:function(e,t){let n={type:"text",value:function(e){let t=String(e),n=/\r?\n|\r/g,r=n.exec(t),i=0,l=[];for(;r;)l.push(te(t.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=n.exec(t);return l.push(te(t.slice(i),i>0,!1)),l.join("")}(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){let n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:tn,yaml:tn,definition:tn,footnoteDefinition:tn};function tn(){}let tr={}.hasOwnProperty,ti={};function tl(e,t){e.position&&(t.position=(0,y.FK)(e))}function to(e,t){let n=t;if(e&&e.data){let t=e.data.hName,r=e.data.hChildren,i=e.data.hProperties;"string"==typeof t&&("element"===n.type?n.tagName=t:n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}),"element"===n.type&&i&&Object.assign(n.properties,(0,e4.ZP)(i)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function ta(e,t){let n=[],r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function tu(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function ts(e,t){let n=function(e,t){let n=t||ti,r=new Map,i=new Map,l={all:function(e){let t=[];if("children"in e){let n=e.children,r=-1;for(;++r<n.length;){let i=l.one(n[r],e);if(i){if(r&&"break"===n[r-1].type&&(Array.isArray(i)||"text"!==i.type||(i.value=tu(i.value)),!Array.isArray(i)&&"element"===i.type)){let e=i.children[0];e&&"text"===e.type&&(e.value=tu(e.value))}Array.isArray(i)?t.push(...i):t.push(i)}}}return t},applyData:to,definitionById:r,footnoteById:i,footnoteCounts:new Map,footnoteOrder:[],handlers:{...tt,...n.handlers},one:function(e,t){let n=e.type,r=l.handlers[n];if(tr.call(l.handlers,n)&&r)return r(l,e,t);if(l.options.passThrough&&l.options.passThrough.includes(n)){if("children"in e){let{children:t,...n}=e,r=(0,e4.ZP)(n);return r.children=l.all(e),r}return(0,e4.ZP)(e)}return(l.options.unknownHandler||function(e,t){let n=t.data||{},r="value"in t&&!(tr.call(n,"hProperties")||tr.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)})(l,e,t)},options:n,patch:tl,wrap:ta};return(0,e6.Vn)(e,function(e){if("definition"===e.type||"footnoteDefinition"===e.type){let t="definition"===e.type?r:i,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}}),l}(e,t),r=n.one(e,void 0),l=function(e){let t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||e9,r=e.options.footnoteBackLabel||e5,i=e.options.footnoteLabel||"Footnotes",l=e.options.footnoteLabelTagName||"h2",o=e.options.footnoteLabelProperties||{className:["sr-only"]},a=[],u=-1;for(;++u<e.footnoteOrder.length;){let i=e.footnoteById.get(e.footnoteOrder[u]);if(!i)continue;let l=e.all(i),o=String(i.identifier).toUpperCase(),s=e3(o.toLowerCase()),c=0,f=[],p=e.footnoteCounts.get(o);for(;void 0!==p&&++c<=p;){f.length>0&&f.push({type:"text",value:" "});let e="string"==typeof n?n:n(u,c);"string"==typeof e&&(e={type:"text",value:e}),f.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+s+(c>1?"-"+c:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(u,c),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}let h=l[l.length-1];if(h&&"element"===h.type&&"p"===h.tagName){let e=h.children[h.children.length-1];e&&"text"===e.type?e.value+=" ":h.children.push({type:"text",value:" "}),h.children.push(...f)}else l.push(...f);let d={type:"element",tagName:"li",properties:{id:t+"fn-"+s},children:e.wrap(l,!0)};e.patch(i,d),a.push(d)}if(0!==a.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:l,properties:{...(0,e4.ZP)(o),id:"footnote-label"},children:[{type:"text",value:i}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(a,!0)},{type:"text",value:"\n"}]}}(n),o=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return l&&((0,i.ok)("children"in o),o.children.push({type:"text",value:"\n"},l)),o}function tc(e,t){return e&&"run"in e?async function(n,r){let i=ts(n,{file:r,...t});await e.run(i,r)}:function(n,r){return ts(n,{file:r,...e||t})}}function tf(e){if(e)throw e}var tp=n(79316);function th(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}let td={basename:function(e,t){let n;if(void 0!==t&&"string"!=typeof t)throw TypeError('"ext" argument must be a string');tm(e);let r=0,i=-1,l=e.length;if(void 0===t||0===t.length||t.length>e.length){for(;l--;)if(47===e.codePointAt(l)){if(n){r=l+1;break}}else i<0&&(n=!0,i=l+1);return i<0?"":e.slice(r,i)}if(t===e)return"";let o=-1,a=t.length-1;for(;l--;)if(47===e.codePointAt(l)){if(n){r=l+1;break}}else o<0&&(n=!0,o=l+1),a>-1&&(e.codePointAt(l)===t.codePointAt(a--)?a<0&&(i=l):(a=-1,i=o));return r===i?i=o:i<0&&(i=e.length),e.slice(r,i)},dirname:function(e){let t;if(tm(e),0===e.length)return".";let n=-1,r=e.length;for(;--r;)if(47===e.codePointAt(r)){if(t){n=r;break}}else t||(t=!0);return n<0?47===e.codePointAt(0)?"/":".":1===n&&47===e.codePointAt(0)?"//":e.slice(0,n)},extname:function(e){let t;tm(e);let n=e.length,r=-1,i=0,l=-1,o=0;for(;n--;){let a=e.codePointAt(n);if(47===a){if(t){i=n+1;break}continue}r<0&&(t=!0,r=n+1),46===a?l<0?l=n:1!==o&&(o=1):l>-1&&(o=-1)}return l<0||r<0||0===o||1===o&&l===r-1&&l===i+1?"":e.slice(l,r)},join:function(...e){let t,n=-1;for(;++n<e.length;)tm(e[n]),e[n]&&(t=void 0===t?e[n]:t+"/"+e[n]);return void 0===t?".":function(e){tm(e);let t=47===e.codePointAt(0),n=function(e,t){let n,r,i="",l=0,o=-1,a=0,u=-1;for(;++u<=e.length;){if(u<e.length)n=e.codePointAt(u);else if(47===n)break;else n=47;if(47===n){if(o===u-1||1===a);else if(o!==u-1&&2===a){if(i.length<2||2!==l||46!==i.codePointAt(i.length-1)||46!==i.codePointAt(i.length-2)){if(i.length>2){if((r=i.lastIndexOf("/"))!==i.length-1){r<0?(i="",l=0):l=(i=i.slice(0,r)).length-1-i.lastIndexOf("/"),o=u,a=0;continue}}else if(i.length>0){i="",l=0,o=u,a=0;continue}}t&&(i=i.length>0?i+"/..":"..",l=2)}else i.length>0?i+="/"+e.slice(o+1,u):i=e.slice(o+1,u),l=u-o-1;o=u,a=0}else 46===n&&a>-1?a++:a=-1}return i}(e,!t);return 0!==n.length||t||(n="."),n.length>0&&47===e.codePointAt(e.length-1)&&(n+="/"),t?"/"+n:n}(t)},sep:"/"};function tm(e){if("string"!=typeof e)throw TypeError("Path must be a string. Received "+JSON.stringify(e))}let tg={cwd:function(){return"/"}};function ty(e){return!!(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}let tk=["history","path","basename","stem","extname","dirname"];class tx{constructor(e){let t,n;t=e?ty(e)?{path:e}:"string"==typeof e||e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e?{value:e}:e:{},this.cwd="cwd"in t?"":tg.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<tk.length;){let e=tk[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)tk.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?td.basename(this.path):void 0}set basename(e){tv(e,"basename"),tb(e,"basename"),this.path=td.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?td.dirname(this.path):void 0}set dirname(e){tS(this.basename,"dirname"),this.path=td.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?td.extname(this.path):void 0}set extname(e){if(tb(e,"extname"),tS(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw Error("`extname` must start with `.`");if(e.includes(".",1))throw Error("`extname` cannot contain multiple dots")}this.path=td.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){ty(e)&&(e=function(e){if("string"==typeof e)e=new URL(e);else if(!ty(e)){let t=TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if("file:"!==e.protocol){let e=TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return function(e){if(""!==e.hostname){let e=TypeError('File URL host must be "localhost" or empty on darwin');throw e.code="ERR_INVALID_FILE_URL_HOST",e}let t=e.pathname,n=-1;for(;++n<t.length;)if(37===t.codePointAt(n)&&50===t.codePointAt(n+1)){let e=t.codePointAt(n+2);if(70===e||102===e){let e=TypeError("File URL path must not include encoded / characters");throw e.code="ERR_INVALID_FILE_URL_PATH",e}}return decodeURIComponent(t)}(e)}(e)),tv(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?td.basename(this.path,this.extname):void 0}set stem(e){tv(e,"stem"),tb(e,"stem"),this.path=td.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){let r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){let r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){let r=new S(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){return void 0===this.value?"":"string"==typeof this.value?this.value:new TextDecoder(e||void 0).decode(this.value)}}function tb(e,t){if(e&&e.includes(td.sep))throw Error("`"+t+"` cannot be a path: did not expect `"+td.sep+"`")}function tv(e,t){if(!e)throw Error("`"+t+"` cannot be empty")}function tS(e,t){if(!e)throw Error("Setting `"+t+"` requires `path` to be set too")}let tw=function(e){let t=this.constructor.prototype,n=t[e],r=function(){return n.apply(r,arguments)};return Object.setPrototypeOf(r,t),r},tC={}.hasOwnProperty;class tE extends tw{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=function(){let e=[],t={run:function(...t){let n=-1,r=t.pop();if("function"!=typeof r)throw TypeError("Expected function as last argument, not "+r);(function i(l,...o){let a=e[++n],u=-1;if(l){r(l);return}for(;++u<t.length;)(null===o[u]||void 0===o[u])&&(o[u]=t[u]);t=o,a?(function(e,t){let n;return function(...t){let l;let o=e.length>t.length;o&&t.push(r);try{l=e.apply(this,t)}catch(e){if(o&&n)throw e;return r(e)}o||(l&&l.then&&"function"==typeof l.then?l.then(i,r):l instanceof Error?r(l):i(l))};function r(e,...i){n||(n=!0,t(e,...i))}function i(e){r(null,e)}})(a,i)(...o):r(null,...o)})(null,...t)},use:function(n){if("function"!=typeof n)throw TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}()}copy(){let e=new tE,t=-1;for(;++t<this.attachers.length;){let n=this.attachers[t];e.use(...n)}return e.data(tp(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2==arguments.length?(tP("data",this.frozen),this.namespace[e]=t,this):tC.call(this.namespace,e)&&this.namespace[e]||void 0:e?(tP("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;for(;++this.freezeIndex<this.attachers.length;){let[e,...t]=this.attachers[this.freezeIndex];if(!1===t[0])continue;!0===t[0]&&(t[0]=void 0);let n=e.call(this,...t);"function"==typeof n&&this.transformers.use(n)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();let t=tO(e),n=this.parser||this.Parser;return tz("parse",n),n(String(t),t)}process(e,t){let n=this;return this.freeze(),tz("process",this.parser||this.Parser),tT("process",this.compiler||this.Compiler),t?r(void 0,t):new Promise(r);function r(r,l){let o=tO(e),a=n.parse(o);function u(e,n){e||!n?l(e):r?r(n):((0,i.ok)(t,"`done` is defined if `resolve` is not"),t(void 0,n))}n.run(a,o,function(e,t,r){if(e||!t||!r)return u(e);let i=n.stringify(t,r);"string"==typeof i||i&&"object"==typeof i&&"byteLength"in i&&"byteOffset"in i?r.value=i:r.result=i,u(e,r)})}}processSync(e){let t,n=!1;return this.freeze(),tz("processSync",this.parser||this.Parser),tT("processSync",this.compiler||this.Compiler),this.process(e,function(e,r){n=!0,tf(e),t=r}),tD("processSync","process",n),(0,i.ok)(t,"we either bailed on an error or have a tree"),t}run(e,t,n){tA(e),this.freeze();let r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?l(void 0,n):new Promise(l);function l(l,o){(0,i.ok)("function"!=typeof t,"`file` can’t be a `done` anymore, we checked");let a=tO(t);r.run(e,a,function(t,r,a){let u=r||e;t?o(t):l?l(u):((0,i.ok)(n,"`done` is defined if `resolve` is not"),n(void 0,u,a))})}}runSync(e,t){let n,r=!1;return this.run(e,t,function(e,t){tf(e),n=t,r=!0}),tD("runSync","run",r),(0,i.ok)(n,"we either bailed on an error or have a tree"),n}stringify(e,t){this.freeze();let n=tO(t),r=this.compiler||this.Compiler;return tT("stringify",r),tA(e),r(e,n)}use(e,...t){let n=this.attachers,r=this.namespace;if(tP("use",this.frozen),null==e);else if("function"==typeof e)o(e,t);else if("object"==typeof e)Array.isArray(e)?l(e):i(e);else throw TypeError("Expected usable value, not `"+e+"`");return this;function i(e){if(!("plugins"in e)&&!("settings"in e))throw Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");l(e.plugins),e.settings&&(r.settings=tp(!0,r.settings,e.settings))}function l(e){let t=-1;if(null==e);else if(Array.isArray(e))for(;++t<e.length;)!function(e){if("function"==typeof e)o(e,[]);else if("object"==typeof e){if(Array.isArray(e)){let[t,...n]=e;o(t,n)}else i(e)}else throw TypeError("Expected usable value, not `"+e+"`")}(e[t]);else throw TypeError("Expected a list of plugins, not `"+e+"`")}function o(e,t){let r=-1,i=-1;for(;++r<n.length;)if(n[r][0]===e){i=r;break}if(-1===i)n.push([e,...t]);else if(t.length>0){let[r,...l]=t,o=n[i][1];th(o)&&th(r)&&(r=tp(!0,o,r)),n[i]=[e,r,...l]}}}}let tI=new tE().freeze();function tz(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `parser`")}function tT(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `compiler`")}function tP(e,t){if(t)throw Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function tA(e){if(!th(e)||"string"!=typeof e.type)throw TypeError("Expected node, got `"+e+"`")}function tD(e,t,n){if(!n)throw Error("`"+e+"` finished async. Use `"+t+"` instead")}function tO(e){return e&&"object"==typeof e&&"message"in e&&"messages"in e?e:new tx(e)}let tL=[],tF={allowDangerousHtml:!0},tM=/^(https?|ircs?|mailto|xmpp)$/i,tR=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function t_(e){let t=function(e){let t=e.rehypePlugins||tL,n=e.remarkPlugins||tL,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...tF}:tF;return tI().use(e2).use(n).use(tc,r).use(t)}(e),n=function(e){let t=e.children||"",n=new tx;return"string"==typeof t?n.value=t:(0,i.t1)("Unexpected value `"+t+"` for `children` prop, expected `string`"),n}(e);return function(e,t){let n=t.allowedElements,r=t.allowElement,l=t.components,o=t.disallowedElements,a=t.skipHtml,u=t.unwrapDisallowed,s=t.urlTransform||tN;for(let e of tR)Object.hasOwn(t,e.from)&&(0,i.t1)("Unexpected `"+e.from+"` prop, "+(e.to?"use `"+e.to+"` instead":"remove it")+" (see <https://github.com/remarkjs/react-markdown/blob/main/changelog.md#"+e.id+"> for more info)");return n&&o&&(0,i.t1)("Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other"),t.className&&(e={type:"element",tagName:"div",properties:{className:t.className},children:"root"===e.type?e.children:[e]}),(0,e6.Vn)(e,function(e,t,i){if("raw"===e.type&&i&&"number"==typeof t)return a?i.children.splice(t,1):i.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in R)if(Object.hasOwn(R,t)&&Object.hasOwn(e.properties,t)){let n=e.properties[t],r=R[t];(null===r||r.includes(e.tagName))&&(e.properties[t]=s(String(n||""),t,e))}}if("element"===e.type){let l=n?!n.includes(e.tagName):!!o&&o.includes(e.tagName);if(!l&&r&&"number"==typeof t&&(l=!r(e,t,i)),l&&i&&"number"==typeof t)return u&&e.children?i.children.splice(t,1,...e.children):i.children.splice(t,1),t}}),function(e,t){var n,r,i;let l;if(!t||void 0===t.Fragment)throw TypeError("Expected `Fragment` in options");let o=t.filePath||void 0;if(t.development){if("function"!=typeof t.jsxDEV)throw TypeError("Expected `jsxDEV` in options when `development: true`");n=t.jsxDEV,l=function(e,t,r,i){let l=Array.isArray(r.children),a=(0,y.Pk)(e);return n(t,r,i,l,{columnNumber:a?a.column-1:void 0,fileName:o,lineNumber:a?a.line:void 0},void 0)}}else{if("function"!=typeof t.jsx)throw TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw TypeError("Expected `jsxs` in production options");r=t.jsx,i=t.jsxs,l=function(e,t,n,l){let o=Array.isArray(n.children)?i:r;return l?o(t,n,l):o(t,n)}}let a={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:l,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:o,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?p.YP:p.dy,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},u=P(a,e,void 0);return u&&"string"!=typeof u?u:a.create(e,a.Fragment,{children:u||void 0},void 0)}(e,{Fragment:_.Fragment,components:l,ignoreInvalidStyle:!0,jsx:_.jsx,jsxs:_.jsxs,passKeys:!0,passNode:!0})}(t.runSync(t.parse(n),n),e)}function tN(e){let t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),i=e.indexOf("/");return -1===t||-1!==i&&t>i||-1!==n&&t>n||-1!==r&&t>r||tM.test(e.slice(0,t))?e:""}},19154:function(e,t,n){"use strict";function r(e,t){let n=String(e);if("string"!=typeof t)throw TypeError("Expected character");let r=0,i=n.indexOf(t);for(;-1!==i;)r++,i=n.indexOf(t,i+t.length);return r}n.d(t,{Z:function(){return eF}});var i=n(51786),l=n(52289),o=n(8111),a=n(5522);let u="phrasing",s=["autolink","link","image","label"];function c(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function f(e){this.config.enter.autolinkProtocol.call(this,e)}function p(e){this.config.exit.autolinkProtocol.call(this,e)}function h(e){this.config.exit.data.call(this,e);let t=this.stack[this.stack.length-1];(0,i.ok)("link"===t.type),t.url="http://"+this.sliceSerialize(e)}function d(e){this.config.exit.autolinkEmail.call(this,e)}function m(e){this.exit(e)}function g(e){!function(e,t,n){let r=(0,a.O)((n||{}).ignore||[]),i=function(e){let t=[];if(!Array.isArray(e))throw TypeError("Expected find and replace tuple or list of tuples");let n=!e[0]||Array.isArray(e[0])?e:[e],r=-1;for(;++r<n.length;){var i;let e=n[r];t.push(["string"==typeof(i=e[0])?RegExp(function(e){if("string"!=typeof e)throw TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(i),"g"):i,function(e){return"function"==typeof e?e:function(){return e}}(e[1])])}return t}(t),l=-1;for(;++l<i.length;)(0,o.S4)(e,"text",u);function u(e,t){let n,o=-1;for(;++o<t.length;){let e=t[o],i=n?n.children:void 0;if(r(e,i?i.indexOf(e):void 0,n))return;n=e}if(n)return function(e,t){let n=t[t.length-1],r=i[l][0],o=i[l][1],a=0,u=n.children.indexOf(e),s=!1,c=[];r.lastIndex=0;let f=r.exec(e.value);for(;f;){let n=f.index,i={index:f.index,input:f.input,stack:[...t,e]},l=o(...f,i);if("string"==typeof l&&(l=l.length>0?{type:"text",value:l}:void 0),!1===l?r.lastIndex=n+1:(a!==n&&c.push({type:"text",value:e.value.slice(a,n)}),Array.isArray(l)?c.push(...l):l&&c.push(l),a=n+f[0].length,s=!0),!r.global)break;f=r.exec(e.value)}return s?(a<e.value.length&&c.push({type:"text",value:e.value.slice(a)}),n.children.splice(u,1,...c)):c=[e],u+c.length}(e,t)}}(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,y],[/(?<=^|\s|\p{P}|\p{S})([-.\w+]+)@([-\w]+(?:\.[-\w]+)+)/gu,k]],{ignore:["link","linkReference"]})}function y(e,t,n,i,l){let o="";if(!x(l)||(/^w/i.test(t)&&(n=t+n,t="",o="http://"),!function(e){let t=e.split(".");return!(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))}(n)))return!1;let a=function(e){let t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],i=n.indexOf(")"),l=r(e,"("),o=r(e,")");for(;-1!==i&&l>o;)e+=n.slice(0,i+1),i=(n=n.slice(i+1)).indexOf(")"),o++;return[e,n]}(n+i);if(!a[0])return!1;let u={type:"link",title:null,url:o+t+a[0],children:[{type:"text",value:t+a[0]}]};return a[1]?[u,{type:"text",value:a[1]}]:u}function k(e,t,n,r){return!(!x(r,!0)||/[-\d_]$/.test(n))&&{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function x(e,t){let n=e.input.charCodeAt(e.index-1);return(0===e.index||(0,l.B8)(n)||(0,l.Xh)(n))&&(!t||47!==n)}var b=n(90800);function v(){this.buffer()}function S(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function w(){this.buffer()}function C(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function E(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,i.ok)("footnoteReference"===n.type),n.identifier=(0,b.d)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function I(e){this.exit(e)}function z(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,i.ok)("footnoteDefinition"===n.type),n.identifier=(0,b.d)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function T(e){this.exit(e)}function P(e,t,n,r){let i=n.createTracker(r),l=i.move("[^"),o=n.enter("footnoteReference"),a=n.enter("reference");return l+=i.move(n.safe(n.associationId(e),{after:"]",before:l})),a(),o(),l+=i.move("]")}function A(e,t,n){return 0===t?e:D(e,t,n)}function D(e,t,n){return(n?"":"    ")+e}P.peek=function(){return"["};let O=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];function L(e){this.enter({type:"delete",children:[]},e)}function F(e){this.exit(e)}function M(e,t,n,r){let i=n.createTracker(r),l=n.enter("strikethrough"),o=i.move("~~");return o+=n.containerPhrasing(e,{...i.current(),before:o,after:"~"})+i.move("~~"),l(),o}function R(e){return e.length}function _(e){let t="string"==typeof e?e.codePointAt(0):0;return 67===t||99===t?99:76===t||108===t?108:82===t||114===t?114:0}M.peek=function(){return"~"};var N=n(63921);n(94794);var j=n(172);function B(e,t,n){let r=e.value||"",i="`",l=-1;for(;RegExp("(^|[^`])"+i+"([^`]|$)").test(r);)i+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++l<n.unsafe.length;){let e;let t=n.unsafe[l],i=n.compilePattern(t);if(t.atBreak)for(;e=i.exec(r);){let t=e.index;10===r.charCodeAt(t)&&13===r.charCodeAt(t-1)&&t--,r=r.slice(0,t)+" "+r.slice(e.index+1)}}return i+r+i}B.peek=function(){return"`"},(0,a.O)(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);let H=function(e,t,n,r){let i=function(e){let t=e.options.listItemIndent||"one";if("tab"!==t&&"one"!==t&&"mixed"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}(n),l=n.bulletCurrent||function(e){let t=e.options.bullet||"*";if("*"!==t&&"+"!==t&&"-"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}(n);t&&"list"===t.type&&t.ordered&&(l=("number"==typeof t.start&&t.start>-1?t.start:1)+(!1===n.options.incrementListMarker?0:t.children.indexOf(e))+l);let o=l.length+1;("tab"===i||"mixed"===i&&(t&&"list"===t.type&&t.spread||e.spread))&&(o=4*Math.ceil(o/4));let a=n.createTracker(r);a.move(l+" ".repeat(o-l.length)),a.shift(o);let u=n.enter("listItem"),s=n.indentLines(n.containerFlow(e,a.current()),function(e,t,n){return t?(n?"":" ".repeat(o))+e:(n?l:l+" ".repeat(o-l.length))+e});return u(),s};function V(e){let t=e._align;(0,i.ok)(t,"expected `_align` on table"),this.enter({type:"table",align:t.map(function(e){return"none"===e?null:e}),children:[]},e),this.data.inTable=!0}function U(e){this.exit(e),this.data.inTable=void 0}function q(e){this.enter({type:"tableRow",children:[]},e)}function $(e){this.exit(e)}function W(e){this.enter({type:"tableCell",children:[]},e)}function Y(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,K));let n=this.stack[this.stack.length-1];(0,i.ok)("inlineCode"===n.type),n.value=t,this.exit(e)}function K(e,t){return"|"===t?t:e}function Q(e){let t=this.stack[this.stack.length-2];(0,i.ok)("listItem"===t.type),t.checked="taskListCheckValueChecked"===e.type}function X(e){let t=this.stack[this.stack.length-2];if(t&&"listItem"===t.type&&"boolean"==typeof t.checked){let e=this.stack[this.stack.length-1];(0,i.ok)("paragraph"===e.type);let n=e.children[0];if(n&&"text"===n.type){let r;let i=t.children,l=-1;for(;++l<i.length;){let e=i[l];if("paragraph"===e.type){r=e;break}}r===e&&(n.value=n.value.slice(1),0===n.value.length?e.children.shift():e.position&&n.position&&"number"==typeof n.position.start.offset&&(n.position.start.column++,n.position.start.offset++,e.position.start=Object.assign({},n.position.start)))}}this.exit(e)}function Z(e,t,n,r){let i=e.children[0],l="boolean"==typeof e.checked&&i&&"paragraph"===i.type,o="["+(e.checked?"x":" ")+"] ",a=n.createTracker(r);l&&a.move(o);let u=H(e,t,n,{...r,...a.current()});return l&&(u=u.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,function(e){return e+o})),u}var J=n(20631);let G={tokenize:function(e,t,n){let r=0;return function t(l){return(87===l||119===l)&&r<3?(r++,e.consume(l),t):46===l&&3===r?(e.consume(l),i):n(l)};function i(e){return null===e?n(e):t(e)}},partial:!0},ee={tokenize:function(e,t,n){let r,i,o;return a;function a(t){return 46===t||95===t?e.check(en,s,u)(t):null===t||(0,l.z3)(t)||(0,l.B8)(t)||45!==t&&(0,l.Xh)(t)?s(t):(o=!0,e.consume(t),a)}function u(t){return 95===t?r=!0:(i=r,r=void 0),e.consume(t),a}function s(e){return i||r||!o?n(e):t(e)}},partial:!0},et={tokenize:function(e,t){let n=0,r=0;return i;function i(a){return 40===a?(n++,e.consume(a),i):41===a&&r<n?o(a):33===a||34===a||38===a||39===a||41===a||42===a||44===a||46===a||58===a||59===a||60===a||63===a||93===a||95===a||126===a?e.check(en,t,o)(a):null===a||(0,l.z3)(a)||(0,l.B8)(a)?t(a):(e.consume(a),i)}function o(t){return 41===t&&r++,e.consume(t),i}},partial:!0},en={tokenize:function(e,t,n){return r;function r(a){return 33===a||34===a||39===a||41===a||42===a||44===a||46===a||58===a||59===a||63===a||95===a||126===a?(e.consume(a),r):38===a?(e.consume(a),o):93===a?(e.consume(a),i):60===a||null===a||(0,l.z3)(a)||(0,l.B8)(a)?t(a):n(a)}function i(e){return null===e||40===e||91===e||(0,l.z3)(e)||(0,l.B8)(e)?t(e):r(e)}function o(t){return(0,l.jv)(t)?function t(i){return 59===i?(e.consume(i),r):(0,l.jv)(i)?(e.consume(i),t):n(i)}(t):n(t)}},partial:!0},er={tokenize:function(e,t,n){return function(t){return e.consume(t),r};function r(e){return(0,l.H$)(e)?n(e):t(e)}},partial:!0},ei={name:"wwwAutolink",tokenize:function(e,t,n){let r=this;return function(t){return 87!==t&&119!==t||!es.call(r,r.previous)||eh(r.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(G,e.attempt(ee,e.attempt(et,i),n),n)(t))};function i(n){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(n)}},previous:es},el={name:"protocolAutolink",tokenize:function(e,t,n){let r=this,i="",o=!1;return function(t){return(72===t||104===t)&&ec.call(r,r.previous)&&!eh(r.events)?(e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),i+=String.fromCodePoint(t),e.consume(t),a):n(t)};function a(t){if((0,l.jv)(t)&&i.length<5)return i+=String.fromCodePoint(t),e.consume(t),a;if(58===t){let n=i.toLowerCase();if("http"===n||"https"===n)return e.consume(t),u}return n(t)}function u(t){return 47===t?(e.consume(t),o)?s:(o=!0,u):n(t)}function s(t){return null===t||(0,l.Av)(t)||(0,l.z3)(t)||(0,l.B8)(t)||(0,l.Xh)(t)?n(t):e.attempt(ee,e.attempt(et,c),n)(t)}function c(n){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(n)}},previous:ec},eo={name:"emailAutolink",tokenize:function(e,t,n){let r,i;let o=this;return function(t){return!ep(t)||!ef.call(o,o.previous)||eh(o.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),function t(r){return ep(r)?(e.consume(r),t):64===r?(e.consume(r),a):n(r)}(t))};function a(t){return 46===t?e.check(er,s,u)(t):45===t||95===t||(0,l.H$)(t)?(i=!0,e.consume(t),a):s(t)}function u(t){return e.consume(t),r=!0,a}function s(a){return i&&r&&(0,l.jv)(o.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(a)):n(a)}},previous:ef},ea={},eu=48;for(;eu<123;)ea[eu]=eo,58==++eu?eu=65:91===eu&&(eu=97);function es(e){return null===e||40===e||42===e||95===e||91===e||93===e||126===e||(0,l.z3)(e)}function ec(e){return!(0,l.jv)(e)}function ef(e){return!(47===e||ep(e))}function ep(e){return 43===e||45===e||46===e||95===e||(0,l.H$)(e)}function eh(e){let t=e.length,n=!1;for(;t--;){let r=e[t][1];if(("labelLink"===r.type||"labelImage"===r.type)&&!r._balanced){n=!0;break}if(r._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}ea[43]=eo,ea[45]=eo,ea[46]=eo,ea[95]=eo,ea[72]=[eo,el],ea[104]=[eo,el],ea[87]=[eo,ei],ea[119]=[eo,ei];var ed=n(49283),em=n(6604);let eg={tokenize:function(e,t,n){let r=this;return(0,em.f)(e,function(e){let i=r.events[r.events.length-1];return i&&"gfmFootnoteDefinitionIndent"===i[1].type&&4===i[2].sliceSerialize(i[1],!0).length?t(e):n(e)},"gfmFootnoteDefinitionIndent",5)},partial:!0};function ey(e,t,n){let r;let i=this,l=i.events.length,o=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]);for(;l--;){let e=i.events[l][1];if("labelImage"===e.type){r=e;break}if("gfmFootnoteCall"===e.type||"labelLink"===e.type||"label"===e.type||"image"===e.type||"link"===e.type)break}return function(l){if(!r||!r._balanced)return n(l);let a=(0,b.d)(i.sliceSerialize({start:r.end,end:i.now()}));return 94===a.codePointAt(0)&&o.includes(a.slice(1))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(l),e.exit("gfmFootnoteCallLabelMarker"),t(l)):n(l)}}function ek(e,t){let n=e.length;for(;n--;)if("labelImage"===e[n][1].type&&"enter"===e[n][0]){e[n][1];break}e[n+1][1].type="data",e[n+3][1].type="gfmFootnoteCallLabelMarker";let r={type:"gfmFootnoteCall",start:Object.assign({},e[n+3][1].start),end:Object.assign({},e[e.length-1][1].end)},i={type:"gfmFootnoteCallMarker",start:Object.assign({},e[n+3][1].end),end:Object.assign({},e[n+3][1].end)};i.end.column++,i.end.offset++,i.end._bufferIndex++;let l={type:"gfmFootnoteCallString",start:Object.assign({},i.end),end:Object.assign({},e[e.length-1][1].start)},o={type:"chunkString",contentType:"string",start:Object.assign({},l.start),end:Object.assign({},l.end)},a=[e[n+1],e[n+2],["enter",r,t],e[n+3],e[n+4],["enter",i,t],["exit",i,t],["enter",l,t],["enter",o,t],["exit",o,t],["exit",l,t],e[e.length-2],e[e.length-1],["exit",r,t]];return e.splice(n,e.length-n+1,...a),e}function ex(e,t,n){let r;let i=this,o=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]),a=0;return function(t){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(t),e.exit("gfmFootnoteCallLabelMarker"),u};function u(t){return 94!==t?n(t):(e.enter("gfmFootnoteCallMarker"),e.consume(t),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",s)}function s(u){if(a>999||93===u&&!r||null===u||91===u||(0,l.z3)(u))return n(u);if(93===u){e.exit("chunkString");let r=e.exit("gfmFootnoteCallString");return o.includes((0,b.d)(i.sliceSerialize(r)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(u),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(u)}return(0,l.z3)(u)||(r=!0),a++,e.consume(u),92===u?c:s}function c(t){return 91===t||92===t||93===t?(e.consume(t),a++,s):s(t)}}function eb(e,t,n){let r,i;let o=this,a=o.parser.gfmFootnotes||(o.parser.gfmFootnotes=[]),u=0;return function(t){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),s};function s(t){return 94===t?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",c):n(t)}function c(t){if(u>999||93===t&&!i||null===t||91===t||(0,l.z3)(t))return n(t);if(93===t){e.exit("chunkString");let n=e.exit("gfmFootnoteDefinitionLabelString");return r=(0,b.d)(o.sliceSerialize(n)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),p}return(0,l.z3)(t)||(i=!0),u++,e.consume(t),92===t?f:c}function f(t){return 91===t||92===t||93===t?(e.consume(t),u++,c):c(t)}function p(t){return 58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),a.includes(r)||a.push(r),(0,em.f)(e,h,"gfmFootnoteDefinitionWhitespace")):n(t)}function h(e){return t(e)}}function ev(e,t,n){return e.check(ed.w,t,e.attempt(eg,t,n))}function eS(e){e.exit("gfmFootnoteDefinition")}var ew=n(83139),eC=n(10741);class eE{constructor(){this.map=[]}add(e,t,n){!function(e,t,n,r){let i=0;if(0!==n||0!==r.length){for(;i<e.map.length;){if(e.map[i][0]===t){e.map[i][1]+=n,e.map[i][2].push(...r);return}i+=1}e.map.push([t,n,r])}}(this,e,t,n)}consume(e){if(this.map.sort(function(e,t){return e[0]-t[0]}),0===this.map.length)return;let t=this.map.length,n=[];for(;t>0;)t-=1,n.push(e.slice(this.map[t][0]+this.map[t][1]),this.map[t][2]),e.length=this.map[t][0];n.push(e.slice()),e.length=0;let r=n.pop();for(;r;){for(let t of r)e.push(t);r=n.pop()}this.map.length=0}}function eI(e,t,n){let r;let i=this,o=0,a=0;return function(e){let t=i.events.length-1;for(;t>-1;){let e=i.events[t][1].type;if("lineEnding"===e||"linePrefix"===e)t--;else break}let r=t>-1?i.events[t][1].type:null,l="tableHead"===r||"tableRow"===r?x:u;return l===x&&i.parser.lazy[i.now().line]?n(e):l(e)};function u(t){return e.enter("tableHead"),e.enter("tableRow"),124===t||(r=!0,a+=1),s(t)}function s(t){return null===t?n(t):(0,l.Ch)(t)?a>1?(a=0,i.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),p):n(t):(0,l.xz)(t)?(0,em.f)(e,s,"whitespace")(t):(a+=1,r&&(r=!1,o+=1),124===t)?(e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),r=!0,s):(e.enter("data"),c(t))}function c(t){return null===t||124===t||(0,l.z3)(t)?(e.exit("data"),s(t)):(e.consume(t),92===t?f:c)}function f(t){return 92===t||124===t?(e.consume(t),c):c(t)}function p(t){return(i.interrupt=!1,i.parser.lazy[i.now().line])?n(t):(e.enter("tableDelimiterRow"),r=!1,(0,l.xz)(t))?(0,em.f)(e,h,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):h(t)}function h(t){return 45===t||58===t?m(t):124===t?(r=!0,e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),d):n(t)}function d(t){return(0,l.xz)(t)?(0,em.f)(e,m,"whitespace")(t):m(t)}function m(t){return 58===t?(a+=1,r=!0,e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),g):45===t?(a+=1,g(t)):null===t||(0,l.Ch)(t)?k(t):n(t)}function g(t){return 45===t?(e.enter("tableDelimiterFiller"),function t(n){return 45===n?(e.consume(n),t):58===n?(r=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(n),e.exit("tableDelimiterMarker"),y):(e.exit("tableDelimiterFiller"),y(n))}(t)):n(t)}function y(t){return(0,l.xz)(t)?(0,em.f)(e,k,"whitespace")(t):k(t)}function k(i){return 124===i?h(i):null===i||(0,l.Ch)(i)?r&&o===a?(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(i)):n(i):n(i)}function x(t){return e.enter("tableRow"),b(t)}function b(n){return 124===n?(e.enter("tableCellDivider"),e.consume(n),e.exit("tableCellDivider"),b):null===n||(0,l.Ch)(n)?(e.exit("tableRow"),t(n)):(0,l.xz)(n)?(0,em.f)(e,b,"whitespace")(n):(e.enter("data"),v(n))}function v(t){return null===t||124===t||(0,l.z3)(t)?(e.exit("data"),b(t)):(e.consume(t),92===t?S:v)}function S(t){return 92===t||124===t?(e.consume(t),v):v(t)}}function ez(e,t){let n,r,i,l=-1,o=!0,a=0,u=[0,0,0,0],s=[0,0,0,0],c=!1,f=0,p=new eE;for(;++l<e.length;){let h=e[l],d=h[1];"enter"===h[0]?"tableHead"===d.type?(c=!1,0!==f&&(eP(p,t,f,n,r),r=void 0,f=0),n={type:"table",start:Object.assign({},d.start),end:Object.assign({},d.end)},p.add(l,0,[["enter",n,t]])):"tableRow"===d.type||"tableDelimiterRow"===d.type?(o=!0,i=void 0,u=[0,0,0,0],s=[0,l+1,0,0],c&&(c=!1,r={type:"tableBody",start:Object.assign({},d.start),end:Object.assign({},d.end)},p.add(l,0,[["enter",r,t]])),a="tableDelimiterRow"===d.type?2:r?3:1):a&&("data"===d.type||"tableDelimiterMarker"===d.type||"tableDelimiterFiller"===d.type)?(o=!1,0===s[2]&&(0!==u[1]&&(s[0]=s[1],i=eT(p,t,u,a,void 0,i),u=[0,0,0,0]),s[2]=l)):"tableCellDivider"===d.type&&(o?o=!1:(0!==u[1]&&(s[0]=s[1],i=eT(p,t,u,a,void 0,i)),s=[(u=s)[1],l,0,0])):"tableHead"===d.type?(c=!0,f=l):"tableRow"===d.type||"tableDelimiterRow"===d.type?(f=l,0!==u[1]?(s[0]=s[1],i=eT(p,t,u,a,l,i)):0!==s[1]&&(i=eT(p,t,s,a,l,i)),a=0):a&&("data"===d.type||"tableDelimiterMarker"===d.type||"tableDelimiterFiller"===d.type)&&(s[3]=l)}for(0!==f&&eP(p,t,f,n,r),p.consume(t.events),l=-1;++l<t.events.length;){let e=t.events[l];"enter"===e[0]&&"table"===e[1].type&&(e[1]._align=function(e,t){let n=!1,r=[];for(;t<e.length;){let i=e[t];if(n){if("enter"===i[0])"tableContent"===i[1].type&&r.push("tableDelimiterMarker"===e[t+1][1].type?"left":"none");else if("tableContent"===i[1].type){if("tableDelimiterMarker"===e[t-1][1].type){let e=r.length-1;r[e]="left"===r[e]?"center":"right"}}else if("tableDelimiterRow"===i[1].type)break}else"enter"===i[0]&&"tableDelimiterRow"===i[1].type&&(n=!0);t+=1}return r}(t.events,l))}return e}function eT(e,t,n,r,i,l){0!==n[0]&&(l.end=Object.assign({},eA(t.events,n[0])),e.add(n[0],0,[["exit",l,t]]));let o=eA(t.events,n[1]);if(l={type:1===r?"tableHeader":2===r?"tableDelimiter":"tableData",start:Object.assign({},o),end:Object.assign({},o)},e.add(n[1],0,[["enter",l,t]]),0!==n[2]){let i=eA(t.events,n[2]),l=eA(t.events,n[3]),o={type:"tableContent",start:Object.assign({},i),end:Object.assign({},l)};if(e.add(n[2],0,[["enter",o,t]]),2!==r){let r=t.events[n[2]],i=t.events[n[3]];if(r[1].end=Object.assign({},i[1].end),r[1].type="chunkText",r[1].contentType="text",n[3]>n[2]+1){let t=n[2]+1,r=n[3]-n[2]-1;e.add(t,r,[])}}e.add(n[3]+1,0,[["exit",o,t]])}return void 0!==i&&(l.end=Object.assign({},eA(t.events,i)),e.add(i,0,[["exit",l,t]]),l=void 0),l}function eP(e,t,n,r,i){let l=[],o=eA(t.events,n);i&&(i.end=Object.assign({},o),l.push(["exit",i,t])),r.end=Object.assign({},o),l.push(["exit",r,t]),e.add(n+1,0,l)}function eA(e,t){let n=e[t],r="enter"===n[0]?"start":"end";return n[1][r]}let eD={name:"tasklistCheck",tokenize:function(e,t,n){let r=this;return function(t){return null===r.previous&&r._gfmTasklistFirstContentOfListItem?(e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),i):n(t)};function i(t){return(0,l.z3)(t)?(e.enter("taskListCheckValueUnchecked"),e.consume(t),e.exit("taskListCheckValueUnchecked"),o):88===t||120===t?(e.enter("taskListCheckValueChecked"),e.consume(t),e.exit("taskListCheckValueChecked"),o):n(t)}function o(t){return 93===t?(e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),a):n(t)}function a(r){return(0,l.Ch)(r)?t(r):(0,l.xz)(r)?e.check({tokenize:eO},t,n)(r):n(r)}}};function eO(e,t,n){return(0,em.f)(e,function(e){return null===e?n(e):t(e)},"whitespace")}let eL={};function eF(e){let t;let n=e||eL,r=this.data(),i=r.micromarkExtensions||(r.micromarkExtensions=[]),l=r.fromMarkdownExtensions||(r.fromMarkdownExtensions=[]),o=r.toMarkdownExtensions||(r.toMarkdownExtensions=[]);i.push((0,J.W)([{text:ea},{document:{91:{name:"gfmFootnoteDefinition",tokenize:eb,continuation:{tokenize:ev},exit:eS}},text:{91:{name:"gfmFootnoteCall",tokenize:ex},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:ey,resolveTo:ek}}},function(e){let t=(e||{}).singleTilde,n={name:"strikethrough",tokenize:function(e,n,r){let i=this.previous,l=this.events,o=0;return function(a){return 126===i&&"characterEscape"!==l[l.length-1][1].type?r(a):(e.enter("strikethroughSequenceTemporary"),function l(a){let u=(0,N.r)(i);if(126===a)return o>1?r(a):(e.consume(a),o++,l);if(o<2&&!t)return r(a);let s=e.exit("strikethroughSequenceTemporary"),c=(0,N.r)(a);return s._open=!c||2===c&&!!u,s._close=!u||2===u&&!!c,n(a)}(a))}},resolveAll:function(e,t){let n=-1;for(;++n<e.length;)if("enter"===e[n][0]&&"strikethroughSequenceTemporary"===e[n][1].type&&e[n][1]._close){let r=n;for(;r--;)if("exit"===e[r][0]&&"strikethroughSequenceTemporary"===e[r][1].type&&e[r][1]._open&&e[n][1].end.offset-e[n][1].start.offset==e[r][1].end.offset-e[r][1].start.offset){e[n][1].type="strikethroughSequence",e[r][1].type="strikethroughSequence";let i={type:"strikethrough",start:Object.assign({},e[r][1].start),end:Object.assign({},e[n][1].end)},l={type:"strikethroughText",start:Object.assign({},e[r][1].end),end:Object.assign({},e[n][1].start)},o=[["enter",i,t],["enter",e[r][1],t],["exit",e[r][1],t],["enter",l,t]],a=t.parser.constructs.insideSpan.null;a&&(0,ew.d)(o,o.length,0,(0,eC.C)(a,e.slice(r+1,n),t)),(0,ew.d)(o,o.length,0,[["exit",l,t],["enter",e[n][1],t],["exit",e[n][1],t],["exit",i,t]]),(0,ew.d)(e,r-1,n-r+3,o),n=r+o.length-2;break}}for(n=-1;++n<e.length;)"strikethroughSequenceTemporary"===e[n][1].type&&(e[n][1].type="data");return e}};return null==t&&(t=!0),{text:{126:n},insideSpan:{null:[n]},attentionMarkers:{null:[126]}}}(n),{flow:{null:{name:"table",tokenize:eI,resolveAll:ez}}},{text:{91:eD}}])),l.push([{transforms:[g],enter:{literalAutolink:c,literalAutolinkEmail:f,literalAutolinkHttp:f,literalAutolinkWww:f},exit:{literalAutolink:m,literalAutolinkEmail:d,literalAutolinkHttp:p,literalAutolinkWww:h}},{enter:{gfmFootnoteCallString:v,gfmFootnoteCall:S,gfmFootnoteDefinitionLabelString:w,gfmFootnoteDefinition:C},exit:{gfmFootnoteCallString:E,gfmFootnoteCall:I,gfmFootnoteDefinitionLabelString:z,gfmFootnoteDefinition:T}},{canContainEols:["delete"],enter:{strikethrough:L},exit:{strikethrough:F}},{enter:{table:V,tableData:W,tableHeader:W,tableRow:q},exit:{codeText:Y,table:U,tableData:$,tableHeader:$,tableRow:$}},{exit:{taskListCheckValueChecked:Q,taskListCheckValueUnchecked:Q,paragraph:X}}]),o.push({extensions:[{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:u,notInConstruct:s},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:u,notInConstruct:s},{character:":",before:"[ps]",after:"\\/",inConstruct:u,notInConstruct:s}]},(t=!1,n&&n.firstLineBlank&&(t=!0),{handlers:{footnoteDefinition:function(e,n,r,i){let l=r.createTracker(i),o=l.move("[^"),a=r.enter("footnoteDefinition"),u=r.enter("label");return o+=l.move(r.safe(r.associationId(e),{before:o,after:"]"})),u(),o+=l.move("]:"),e.children&&e.children.length>0&&(l.shift(4),o+=l.move((t?"\n":" ")+r.indentLines(r.containerFlow(e,l.current()),t?D:A))),a(),o},footnoteReference:P},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]}),{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:O}],handlers:{delete:M}},function(e){let t=e||{},n=t.tableCellPadding,r=t.tablePipeAlign,i=t.stringLength,l=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:"\n",inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:function(e,t,n){let r=B(e,t,n);return n.stack.includes("tableCell")&&(r=r.replace(/\|/g,"\\$&")),r},table:function(e,t,n,r){return a(function(e,t,n){let r=e.children,i=-1,l=[],o=t.enter("table");for(;++i<r.length;)l[i]=u(r[i],t,n);return o(),l}(e,n,r),e.align)},tableCell:o,tableRow:function(e,t,n,r){let i=a([u(e,n,r)]);return i.slice(0,i.indexOf("\n"))}}};function o(e,t,n,r){let i=n.enter("tableCell"),o=n.enter("phrasing"),a=n.containerPhrasing(e,{...r,before:l,after:l});return o(),i(),a}function a(e,t){return function(e,t){let n=t||{},r=(n.align||[]).concat(),i=n.stringLength||R,l=[],o=[],a=[],u=[],s=0,c=-1;for(;++c<e.length;){let t=[],r=[],l=-1;for(e[c].length>s&&(s=e[c].length);++l<e[c].length;){var f;let o=null==(f=e[c][l])?"":String(f);if(!1!==n.alignDelimiters){let e=i(o);r[l]=e,(void 0===u[l]||e>u[l])&&(u[l]=e)}t.push(o)}o[c]=t,a[c]=r}let p=-1;if("object"==typeof r&&"length"in r)for(;++p<s;)l[p]=_(r[p]);else{let e=_(r);for(;++p<s;)l[p]=e}p=-1;let h=[],d=[];for(;++p<s;){let e=l[p],t="",r="";99===e?(t=":",r=":"):108===e?t=":":114===e&&(r=":");let i=!1===n.alignDelimiters?1:Math.max(1,u[p]-t.length-r.length),o=t+"-".repeat(i)+r;!1!==n.alignDelimiters&&((i=t.length+i+r.length)>u[p]&&(u[p]=i),d[p]=i),h[p]=o}o.splice(1,0,h),a.splice(1,0,d),c=-1;let m=[];for(;++c<o.length;){let e=o[c],t=a[c];p=-1;let r=[];for(;++p<s;){let i=e[p]||"",o="",a="";if(!1!==n.alignDelimiters){let e=u[p]-(t[p]||0),n=l[p];114===n?o=" ".repeat(e):99===n?e%2?(o=" ".repeat(e/2+.5),a=" ".repeat(e/2-.5)):a=o=" ".repeat(e/2):a=" ".repeat(e)}!1===n.delimiterStart||p||r.push("|"),!1!==n.padding&&!(!1===n.alignDelimiters&&""===i)&&(!1!==n.delimiterStart||p)&&r.push(" "),!1!==n.alignDelimiters&&r.push(o),r.push(i),!1!==n.alignDelimiters&&r.push(a),!1!==n.padding&&r.push(" "),(!1!==n.delimiterEnd||p!==s-1)&&r.push("|")}m.push(!1===n.delimiterEnd?r.join("").replace(/ +$/,""):r.join(""))}return m.join("\n")}(e,{align:t,alignDelimiters:r,padding:n,stringLength:i})}function u(e,t,n){let r=e.children,i=-1,l=[],a=t.enter("tableRow");for(;++i<r.length;)l[i]=o(r[i],e,t,n);return a(),l}}(n),{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:Z}}]})}},60624:function(e,t,n){"use strict";function r(e){let t=String(e||"").trim();return t?t.split(/[ \t\n\r\f]+/g):[]}function i(e){return e.join(" ").trim()}n.d(t,{P:function(){return i},Q:function(){return r}})},5522:function(e,t,n){"use strict";n.d(t,{O:function(){return r}});let r=function(e){if(null==e)return l;if("function"==typeof e)return i(e);if("object"==typeof e)return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=r(e[n]);return i(function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1})}(e):i(function(t){let n;for(n in e)if(t[n]!==e[n])return!1;return!0});if("string"==typeof e)return i(function(t){return t&&t.type===e});throw Error("Expected function, string, or object as test")};function i(e){return function(t,n,r){var i;return!!(null!==(i=t)&&"object"==typeof i&&"type"in i&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function l(){return!0}},95510:function(e,t,n){"use strict";n.d(t,{FK:function(){return o},Pk:function(){return i},rb:function(){return r}});let r=l("end"),i=l("start");function l(e){return function(t){let n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function o(e){let t=i(e),n=r(e);if(t&&n)return{start:t,end:n}}},8111:function(e,t,n){"use strict";n.d(t,{BK:function(){return l},S4:function(){return o}});var r=n(5522);let i=[],l=!1;function o(e,t,n,o){let a;"function"==typeof t&&"function"!=typeof n?(o=n,n=t):a=t;let u=(0,r.O)(a),s=o?-1:1;(function e(r,a,c){let f=r&&"object"==typeof r?r:{};if("string"==typeof f.type){let e="string"==typeof f.tagName?f.tagName:"string"==typeof f.name?f.name:void 0;Object.defineProperty(p,"name",{value:"node ("+r.type+(e?"<"+e+">":"")+")"})}return p;function p(){var f;let p,h,d,m=i;if((!t||u(r,a,c[c.length-1]||void 0))&&(m=Array.isArray(f=n(r,c))?f:"number"==typeof f?[!0,f]:null==f?i:[f])[0]===l)return m;if("children"in r&&r.children&&r.children&&"skip"!==m[0])for(h=(o?r.children.length:-1)+s,d=c.concat(r);h>-1&&h<r.children.length;){if((p=e(r.children[h],h,d)())[0]===l)return p;h="number"==typeof p[1]?p[1]:h+s}return m}})(e,void 0,[])()}},94794:function(e,t,n){"use strict";n.d(t,{Vn:function(){return i}});var r=n(8111);function i(e,t,n,i){let l,o,a;"function"==typeof t&&"function"!=typeof n?(o=void 0,a=t,l=n):(o=t,a=n,l=i),(0,r.S4)(e,o,function(e,t){let n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return a(e,r,n)},l)}}}]);