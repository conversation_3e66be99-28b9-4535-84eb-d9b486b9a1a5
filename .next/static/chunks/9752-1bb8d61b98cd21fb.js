(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9752],{60353:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("ArrowBigRight",[["path",{d:"M6 9h6V5l7 7-7 7v-4H6V9z",key:"7fvt9c"}]])},35602:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("AtSign",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M16 8v5a3 3 0 0 0 6 0v-1a10 10 0 1 0-4 8",key:"7n84p3"}]])},48324:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("BellOff",[["path",{d:"M8.7 3A6 6 0 0 1 18 8a21.3 21.3 0 0 0 .6 5",key:"o7mx20"}],["path",{d:"M17 17H3s3-2 3-9a4.67 4.67 0 0 1 .3-1.7",key:"16f1lm"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},72742:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Bold",[["path",{d:"M6 12h9a4 4 0 0 1 0 8H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h7a4 4 0 0 1 0 8",key:"mg9rjx"}]])},95578:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("BookmarkCheck",[["path",{d:"m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2Z",key:"169p4p"}],["path",{d:"m9 10 2 2 4-4",key:"1gnqz4"}]])},2336:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},59632:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},811:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},5426:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},64632:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},10676:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},35575:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Forward",[["polyline",{points:"15 17 20 12 15 7",key:"1w3sku"}],["path",{d:"M4 18v-2a4 4 0 0 1 4-4h12",key:"jmiej9"}]])},41102:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},22641:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Italic",[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]])},28904:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Link2",[["path",{d:"M9 17H7A5 5 0 0 1 7 7h2",key:"8i5ue5"}],["path",{d:"M15 7h2a5 5 0 1 1 0 10h-2",key:"1b9ql8"}],["line",{x1:"8",x2:"16",y1:"12",y2:"12",key:"1jonct"}]])},81356:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},82310:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("ListOrdered",[["line",{x1:"10",x2:"21",y1:"6",y2:"6",key:"76qw6h"}],["line",{x1:"10",x2:"21",y1:"12",y2:"12",key:"16nom4"}],["line",{x1:"10",x2:"21",y1:"18",y2:"18",key:"u3jurt"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]])},72593:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])},94912:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("MessageCircleMore",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}],["path",{d:"M8 12h.01",key:"czm47f"}],["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 12h.01",key:"1l6xoz"}]])},60601:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]])},59503:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])},18208:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},20384:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},6769:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("SmilePlus",[["path",{d:"M22 11v1a10 10 0 1 1-9-10",key:"ew0xw9"}],["path",{d:"M8 14s1.5 2 4 2 4-2 4-2",key:"1y1vjs"}],["line",{x1:"9",x2:"9.01",y1:"9",y2:"9",key:"yxxnd0"}],["line",{x1:"15",x2:"15.01",y1:"9",y2:"9",key:"1p4y9e"}],["path",{d:"M16 5h6",key:"1vod17"}],["path",{d:"M19 2v6",key:"4bpg5p"}]])},82301:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},40497:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("SquareSlash",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["line",{x1:"9",x2:"15",y1:"15",y2:"9",key:"1dfufj"}]])},49107:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Strikethrough",[["path",{d:"M16 4H9a3 3 0 0 0-2.83 4",key:"43sutm"}],["path",{d:"M14 12a4 4 0 0 1 0 8H6",key:"nlfj13"}],["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}]])},21920:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},23633:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},9274:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])},26833:function(){var e,t,n;t=/(?:"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n])*')/,(e=Prism).languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:RegExp("@[\\w-](?:"+/[^;{\s"']|\s+(?!\s)/.source+"|"+t.source+")*?"+/(?:;|(?=\s*\{))/.source),inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+t.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+t.source+"$"),alias:"url"}}},selector:{pattern:RegExp("(^|[{}\\s])[^{}\\s](?:[^{};\"'\\s]|\\s+(?![\\s{])|"+t.source+")*(?=\\s*\\{)"),lookbehind:!0},string:{pattern:t,greedy:!0},property:{pattern:/(^|[^-\w\xA0-\uFFFF])(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,lookbehind:!0},important:/!important\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\()/i,lookbehind:!0},punctuation:/[(){};:,]/},e.languages.css.atrule.inside.rest=e.languages.css,(n=e.languages.markup)&&(n.tag.addInlined("style","css"),n.tag.addAttribute("style","css"))},27973:function(){Prism.languages.javascript=Prism.languages.extend("clike",{"class-name":[Prism.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:constructor|prototype))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\})\s*)catch\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|assert(?=\s*\{)|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\s*(?:\{|$))|for|from(?=\s*(?:['"]|$))|function|(?:get|set)(?=\s*(?:[#\[$\w\xA0-\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:{pattern:RegExp(/(^|[^\w$])/.source+"(?:"+(/NaN|Infinity/.source+"|"+/0[bB][01]+(?:_[01]+)*n?/.source+"|"+/0[oO][0-7]+(?:_[0-7]+)*n?/.source+"|"+/0[xX][\dA-Fa-f]+(?:_[\dA-Fa-f]+)*n?/.source+"|")+/\d+(?:_\d+)*n/.source+"|"+/(?:\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\.\d+(?:_\d+)*)(?:[Ee][+-]?\d+(?:_\d+)*)?/.source+")"+/(?![\w$])/.source),lookbehind:!0},operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),Prism.languages.javascript["class-name"][0].pattern=/(\b(?:class|extends|implements|instanceof|interface|new)\s+)[\w.\\]+/,Prism.languages.insertBefore("javascript","keyword",{regex:{pattern:RegExp(/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)/.source+/\//.source+"(?:"+/(?:\[(?:[^\]\\\r\n]|\\.)*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}/.source+"|"+/(?:\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.)*\])*\])*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source+")"+/(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/.source),lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:Prism.languages.regex},"regex-delimiter":/^\/|\/$/,"regex-flags":/^[a-z]+$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:Prism.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),Prism.languages.insertBefore("javascript","string",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:"comment"},"template-string":{pattern:/`(?:\\[\s\S]|\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}|(?!\$\{)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\$\{|\}$/,alias:"punctuation"},rest:Prism.languages.javascript}},string:/[\s\S]+/}},"string-property":{pattern:/((?:^|[,{])[ \t]*)(["'])(?:\\(?:\r\n|[\s\S])|(?!\2)[^\\\r\n])*\2(?=\s*:)/m,lookbehind:!0,greedy:!0,alias:"property"}}),Prism.languages.insertBefore("javascript","operator",{"literal-property":{pattern:/((?:^|[,{])[ \t]*)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*:)/m,lookbehind:!0,alias:"property"}}),Prism.languages.markup&&(Prism.languages.markup.tag.addInlined("script","javascript"),Prism.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,"javascript")),Prism.languages.js=Prism.languages.javascript},69588:function(){Prism.languages.markup={comment:{pattern:/<!--(?:(?!<!--)[\s\S])*?-->/,greedy:!0},prolog:{pattern:/<\?[\s\S]+?\?>/,greedy:!0},doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(^[^\[]*\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/i,name:/[^\s<>'"]+/}},cdata:{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,greedy:!0},tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"special-attr":[],"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},{pattern:/^(\s*)["']|["']$/,lookbehind:!0}]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},Prism.languages.markup.tag.inside["attr-value"].inside.entity=Prism.languages.markup.entity,Prism.languages.markup.doctype.inside["internal-subset"].inside=Prism.languages.markup,Prism.hooks.add("wrap",function(e){"entity"===e.type&&(e.attributes.title=e.content.replace(/&amp;/,"&"))}),Object.defineProperty(Prism.languages.markup.tag,"addInlined",{value:function(e,t){var n={};n["language-"+t]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:Prism.languages[t]},n.cdata=/^<!\[CDATA\[|\]\]>$/i;var r={"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:n}};r["language-"+t]={pattern:/[\s\S]+/,inside:Prism.languages[t]};var s={};s[e]={pattern:RegExp(/(<__[^>]*>)(?:<!\[CDATA\[(?:[^\]]|\](?!\]>))*\]\]>|(?!<!\[CDATA\[)[\s\S])*?(?=<\/__>)/.source.replace(/__/g,function(){return e}),"i"),lookbehind:!0,greedy:!0,inside:r},Prism.languages.insertBefore("markup","cdata",s)}}),Object.defineProperty(Prism.languages.markup.tag,"addAttribute",{value:function(e,t){Prism.languages.markup.tag.inside["special-attr"].push({pattern:RegExp(/(^|["'\s])/.source+"(?:"+e+")"+/\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))/.source,"i"),lookbehind:!0,inside:{"attr-name":/^[^\s=]+/,"attr-value":{pattern:/=[\s\S]+/,inside:{value:{pattern:/(^=\s*(["']|(?!["'])))\S[\s\S]*(?=\2$)/,lookbehind:!0,alias:[t,"language-"+t],inside:Prism.languages[t]},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}}}})}}),Prism.languages.html=Prism.languages.markup,Prism.languages.mathml=Prism.languages.markup,Prism.languages.svg=Prism.languages.markup,Prism.languages.xml=Prism.languages.extend("markup",{}),Prism.languages.ssml=Prism.languages.xml,Prism.languages.atom=Prism.languages.xml,Prism.languages.rss=Prism.languages.xml},6845:function(){Prism.languages.python={comment:{pattern:/(^|[^\\])#.*/,lookbehind:!0,greedy:!0},"string-interpolation":{pattern:/(?:f|fr|rf)(?:("""|''')[\s\S]*?\1|("|')(?:\\.|(?!\2)[^\\\r\n])*\2)/i,greedy:!0,inside:{interpolation:{pattern:/((?:^|[^{])(?:\{\{)*)\{(?!\{)(?:[^{}]|\{(?!\{)(?:[^{}]|\{(?!\{)(?:[^{}])+\})+\})+\}/,lookbehind:!0,inside:{"format-spec":{pattern:/(:)[^:(){}]+(?=\}$)/,lookbehind:!0},"conversion-option":{pattern:/![sra](?=[:}]$)/,alias:"punctuation"},rest:null}},string:/[\s\S]+/}},"triple-quoted-string":{pattern:/(?:[rub]|br|rb)?("""|''')[\s\S]*?\1/i,greedy:!0,alias:"string"},string:{pattern:/(?:[rub]|br|rb)?("|')(?:\\.|(?!\1)[^\\\r\n])*\1/i,greedy:!0},function:{pattern:/((?:^|\s)def[ \t]+)[a-zA-Z_]\w*(?=\s*\()/g,lookbehind:!0},"class-name":{pattern:/(\bclass\s+)\w+/i,lookbehind:!0},decorator:{pattern:/(^[\t ]*)@\w+(?:\.\w+)*/m,lookbehind:!0,alias:["annotation","punctuation"],inside:{punctuation:/\./}},keyword:/\b(?:_(?=\s*:)|and|as|assert|async|await|break|case|class|continue|def|del|elif|else|except|exec|finally|for|from|global|if|import|in|is|lambda|match|nonlocal|not|or|pass|print|raise|return|try|while|with|yield)\b/,builtin:/\b(?:__import__|abs|all|any|apply|ascii|basestring|bin|bool|buffer|bytearray|bytes|callable|chr|classmethod|cmp|coerce|compile|complex|delattr|dict|dir|divmod|enumerate|eval|execfile|file|filter|float|format|frozenset|getattr|globals|hasattr|hash|help|hex|id|input|int|intern|isinstance|issubclass|iter|len|list|locals|long|map|max|memoryview|min|next|object|oct|open|ord|pow|property|range|raw_input|reduce|reload|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|unichr|unicode|vars|xrange|zip)\b/,boolean:/\b(?:False|None|True)\b/,number:/\b0(?:b(?:_?[01])+|o(?:_?[0-7])+|x(?:_?[a-f0-9])+)\b|(?:\b\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\B\.\d+(?:_\d+)*)(?:e[+-]?\d+(?:_\d+)*)?j?(?!\w)/i,operator:/[-+%=]=?|!=|:=|\*\*?=?|\/\/?=?|<[<=>]?|>[=>]?|[&|^~]/,punctuation:/[{}[\];(),.:]/},Prism.languages.python["string-interpolation"].inside.interpolation.inside.rest=Prism.languages.python,Prism.languages.py=Prism.languages.python},13259:function(e,t,n){/**
 * Prism: Lightweight, robust, elegant syntax highlighting
 *
 * @license MIT <https://opensource.org/licenses/MIT>
 * <AUTHOR> Verou <https://lea.verou.me>
 * @namespace
 * @public
 */var r,s,i=function(e){var t=/(?:^|\s)lang(?:uage)?-([\w-]+)(?=\s|$)/i,n=0,r={},s={manual:e.Prism&&e.Prism.manual,disableWorkerMessageHandler:e.Prism&&e.Prism.disableWorkerMessageHandler,util:{encode:function e(t){return t instanceof i?new i(t.type,e(t.content),t.alias):Array.isArray(t)?t.map(e):t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++n}),e.__id},clone:function e(t,n){var r,i;switch(n=n||{},s.util.type(t)){case"Object":if(n[i=s.util.objId(t)])return n[i];for(var a in r={},n[i]=r,t)t.hasOwnProperty(a)&&(r[a]=e(t[a],n));return r;case"Array":if(n[i=s.util.objId(t)])return n[i];return r=[],n[i]=r,t.forEach(function(t,s){r[s]=e(t,n)}),r;default:return t}},getLanguage:function(e){for(;e;){var n=t.exec(e.className);if(n)return n[1].toLowerCase();e=e.parentElement}return"none"},setLanguage:function(e,n){e.className=e.className.replace(RegExp(t,"gi"),""),e.classList.add("language-"+n)},currentScript:function(){if("undefined"==typeof document)return null;if(document.currentScript&&"SCRIPT"===document.currentScript.tagName)return document.currentScript;try{throw Error()}catch(r){var e=(/at [^(\r\n]*\((.*):[^:]+:[^:]+\)$/i.exec(r.stack)||[])[1];if(e){var t=document.getElementsByTagName("script");for(var n in t)if(t[n].src==e)return t[n]}return null}},isActive:function(e,t,n){for(var r="no-"+t;e;){var s=e.classList;if(s.contains(t))return!0;if(s.contains(r))return!1;e=e.parentElement}return!!n}},languages:{plain:r,plaintext:r,text:r,txt:r,extend:function(e,t){var n=s.util.clone(s.languages[e]);for(var r in t)n[r]=t[r];return n},insertBefore:function(e,t,n,r){var i=(r=r||s.languages)[e],a={};for(var o in i)if(i.hasOwnProperty(o)){if(o==t)for(var l in n)n.hasOwnProperty(l)&&(a[l]=n[l]);n.hasOwnProperty(o)||(a[o]=i[o])}var c=r[e];return r[e]=a,s.languages.DFS(s.languages,function(t,n){n===c&&t!=e&&(this[t]=a)}),a},DFS:function e(t,n,r,i){i=i||{};var a=s.util.objId;for(var o in t)if(t.hasOwnProperty(o)){n.call(t,o,t[o],r||o);var l=t[o],c=s.util.type(l);"Object"!==c||i[a(l)]?"Array"!==c||i[a(l)]||(i[a(l)]=!0,e(l,n,o,i)):(i[a(l)]=!0,e(l,n,null,i))}}},plugins:{},highlightAll:function(e,t){s.highlightAllUnder(document,e,t)},highlightAllUnder:function(e,t,n){var r={callback:n,container:e,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};s.hooks.run("before-highlightall",r),r.elements=Array.prototype.slice.apply(r.container.querySelectorAll(r.selector)),s.hooks.run("before-all-elements-highlight",r);for(var i,a=0;i=r.elements[a++];)s.highlightElement(i,!0===t,r.callback)},highlightElement:function(t,n,r){var i=s.util.getLanguage(t),a=s.languages[i];s.util.setLanguage(t,i);var o=t.parentElement;o&&"pre"===o.nodeName.toLowerCase()&&s.util.setLanguage(o,i);var l=t.textContent,c={element:t,language:i,grammar:a,code:l};function u(e){c.highlightedCode=e,s.hooks.run("before-insert",c),c.element.innerHTML=c.highlightedCode,s.hooks.run("after-highlight",c),s.hooks.run("complete",c),r&&r.call(c.element)}if(s.hooks.run("before-sanity-check",c),(o=c.element.parentElement)&&"pre"===o.nodeName.toLowerCase()&&!o.hasAttribute("tabindex")&&o.setAttribute("tabindex","0"),!c.code){s.hooks.run("complete",c),r&&r.call(c.element);return}if(s.hooks.run("before-highlight",c),!c.grammar){u(s.util.encode(c.code));return}if(n&&e.Worker){var h=new Worker(s.filename);h.onmessage=function(e){u(e.data)},h.postMessage(JSON.stringify({language:c.language,code:c.code,immediateClose:!0}))}else u(s.highlight(c.code,c.grammar,c.language))},highlight:function(e,t,n){var r={code:e,grammar:t,language:n};if(s.hooks.run("before-tokenize",r),!r.grammar)throw Error('The language "'+r.language+'" has no grammar.');return r.tokens=s.tokenize(r.code,r.grammar),s.hooks.run("after-tokenize",r),i.stringify(s.util.encode(r.tokens),r.language)},tokenize:function(e,t){var n=t.rest;if(n){for(var r in n)t[r]=n[r];delete t.rest}var c=new o;return l(c,c.head,e),function e(t,n,r,o,c,u){for(var h in r)if(r.hasOwnProperty(h)&&r[h]){var d=r[h];d=Array.isArray(d)?d:[d];for(var p=0;p<d.length;++p){if(u&&u.cause==h+","+p)return;var f=d[p],m=f.inside,g=!!f.lookbehind,E=!!f.greedy,T=f.alias;if(E&&!f.pattern.global){var A=f.pattern.toString().match(/[imsuy]*$/)[0];f.pattern=RegExp(f.pattern.source,A+"g")}for(var _=f.pattern||f,S=o.next,y=c;S!==n.tail&&(!u||!(y>=u.reach));y+=S.value.length,S=S.next){var k,C=S.value;if(n.length>t.length)return;if(!(C instanceof i)){var N=1;if(E){if(!(k=a(_,y,t,g))||k.index>=t.length)break;var I=k.index,b=k.index+k[0].length,O=y;for(O+=S.value.length;I>=O;)O+=(S=S.next).value.length;if(O-=S.value.length,y=O,S.value instanceof i)continue;for(var D=S;D!==n.tail&&(O<b||"string"==typeof D.value);D=D.next)N++,O+=D.value.length;N--,C=t.slice(y,O),k.index-=y}else if(!(k=a(_,0,C,g)))continue;var I=k.index,R=k[0],M=C.slice(0,I),v=C.slice(I+R.length),L=y+C.length;u&&L>u.reach&&(u.reach=L);var w=S.prev;if(M&&(w=l(n,w,M),y+=M.length),function(e,t,n){for(var r=t.next,s=0;s<n&&r!==e.tail;s++)r=r.next;t.next=r,r.prev=t,e.length-=s}(n,w,N),S=l(n,w,new i(h,m?s.tokenize(R,m):R,T,R)),v&&l(n,S,v),N>1){var P={cause:h+","+p,reach:L};e(t,n,r,S.prev,y,P),u&&P.reach>u.reach&&(u.reach=P.reach)}}}}}}(e,c,t,c.head,0),function(e){for(var t=[],n=e.head.next;n!==e.tail;)t.push(n.value),n=n.next;return t}(c)},hooks:{all:{},add:function(e,t){var n=s.hooks.all;n[e]=n[e]||[],n[e].push(t)},run:function(e,t){var n=s.hooks.all[e];if(n&&n.length)for(var r,i=0;r=n[i++];)r(t)}},Token:i};function i(e,t,n,r){this.type=e,this.content=t,this.alias=n,this.length=0|(r||"").length}function a(e,t,n,r){e.lastIndex=t;var s=e.exec(n);if(s&&r&&s[1]){var i=s[1].length;s.index+=i,s[0]=s[0].slice(i)}return s}function o(){var e={value:null,prev:null,next:null},t={value:null,prev:e,next:null};e.next=t,this.head=e,this.tail=t,this.length=0}function l(e,t,n){var r=t.next,s={value:n,prev:t,next:r};return t.next=s,r.prev=s,e.length++,s}if(e.Prism=s,i.stringify=function e(t,n){if("string"==typeof t)return t;if(Array.isArray(t)){var r="";return t.forEach(function(t){r+=e(t,n)}),r}var i={type:t.type,content:e(t.content,n),tag:"span",classes:["token",t.type],attributes:{},language:n},a=t.alias;a&&(Array.isArray(a)?Array.prototype.push.apply(i.classes,a):i.classes.push(a)),s.hooks.run("wrap",i);var o="";for(var l in i.attributes)o+=" "+l+'="'+(i.attributes[l]||"").replace(/"/g,"&quot;")+'"';return"<"+i.tag+' class="'+i.classes.join(" ")+'"'+o+">"+i.content+"</"+i.tag+">"},!e.document)return e.addEventListener&&(s.disableWorkerMessageHandler||e.addEventListener("message",function(t){var n=JSON.parse(t.data),r=n.language,i=n.code,a=n.immediateClose;e.postMessage(s.highlight(i,s.languages[r],r)),a&&e.close()},!1)),s;var c=s.util.currentScript();function u(){s.manual||s.highlightAll()}if(c&&(s.filename=c.src,c.hasAttribute("data-manual")&&(s.manual=!0)),!s.manual){var h=document.readyState;"loading"===h||"interactive"===h&&c&&c.defer?document.addEventListener("DOMContentLoaded",u):window.requestAnimationFrame?window.requestAnimationFrame(u):window.setTimeout(u,16)}return s}("undefined"!=typeof window?window:"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?self:{});e.exports&&(e.exports=i),void 0!==n.g&&(n.g.Prism=i),i.languages.markup={comment:{pattern:/<!--(?:(?!<!--)[\s\S])*?-->/,greedy:!0},prolog:{pattern:/<\?[\s\S]+?\?>/,greedy:!0},doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(^[^\[]*\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/i,name:/[^\s<>'"]+/}},cdata:{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,greedy:!0},tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"special-attr":[],"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},{pattern:/^(\s*)["']|["']$/,lookbehind:!0}]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},i.languages.markup.tag.inside["attr-value"].inside.entity=i.languages.markup.entity,i.languages.markup.doctype.inside["internal-subset"].inside=i.languages.markup,i.hooks.add("wrap",function(e){"entity"===e.type&&(e.attributes.title=e.content.replace(/&amp;/,"&"))}),Object.defineProperty(i.languages.markup.tag,"addInlined",{value:function(e,t){var n={};n["language-"+t]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:i.languages[t]},n.cdata=/^<!\[CDATA\[|\]\]>$/i;var r={"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:n}};r["language-"+t]={pattern:/[\s\S]+/,inside:i.languages[t]};var s={};s[e]={pattern:RegExp(/(<__[^>]*>)(?:<!\[CDATA\[(?:[^\]]|\](?!\]>))*\]\]>|(?!<!\[CDATA\[)[\s\S])*?(?=<\/__>)/.source.replace(/__/g,function(){return e}),"i"),lookbehind:!0,greedy:!0,inside:r},i.languages.insertBefore("markup","cdata",s)}}),Object.defineProperty(i.languages.markup.tag,"addAttribute",{value:function(e,t){i.languages.markup.tag.inside["special-attr"].push({pattern:RegExp(/(^|["'\s])/.source+"(?:"+e+")"+/\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))/.source,"i"),lookbehind:!0,inside:{"attr-name":/^[^\s=]+/,"attr-value":{pattern:/=[\s\S]+/,inside:{value:{pattern:/(^=\s*(["']|(?!["'])))\S[\s\S]*(?=\2$)/,lookbehind:!0,alias:[t,"language-"+t],inside:i.languages[t]},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}}}})}}),i.languages.html=i.languages.markup,i.languages.mathml=i.languages.markup,i.languages.svg=i.languages.markup,i.languages.xml=i.languages.extend("markup",{}),i.languages.ssml=i.languages.xml,i.languages.atom=i.languages.xml,i.languages.rss=i.languages.xml,r=/(?:"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n])*')/,i.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:RegExp("@[\\w-](?:"+/[^;{\s"']|\s+(?!\s)/.source+"|"+r.source+")*?"+/(?:;|(?=\s*\{))/.source),inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+r.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+r.source+"$"),alias:"url"}}},selector:{pattern:RegExp("(^|[{}\\s])[^{}\\s](?:[^{};\"'\\s]|\\s+(?![\\s{])|"+r.source+")*(?=\\s*\\{)"),lookbehind:!0},string:{pattern:r,greedy:!0},property:{pattern:/(^|[^-\w\xA0-\uFFFF])(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,lookbehind:!0},important:/!important\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\()/i,lookbehind:!0},punctuation:/[(){};:,]/},i.languages.css.atrule.inside.rest=i.languages.css,(s=i.languages.markup)&&(s.tag.addInlined("style","css"),s.tag.addAttribute("style","css")),i.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|trait)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\b/,boolean:/\b(?:false|true)\b/,function:/\b\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/},i.languages.javascript=i.languages.extend("clike",{"class-name":[i.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:constructor|prototype))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\})\s*)catch\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|assert(?=\s*\{)|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\s*(?:\{|$))|for|from(?=\s*(?:['"]|$))|function|(?:get|set)(?=\s*(?:[#\[$\w\xA0-\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:{pattern:RegExp(/(^|[^\w$])/.source+"(?:"+(/NaN|Infinity/.source+"|"+/0[bB][01]+(?:_[01]+)*n?/.source+"|"+/0[oO][0-7]+(?:_[0-7]+)*n?/.source+"|"+/0[xX][\dA-Fa-f]+(?:_[\dA-Fa-f]+)*n?/.source+"|")+/\d+(?:_\d+)*n/.source+"|"+/(?:\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\.\d+(?:_\d+)*)(?:[Ee][+-]?\d+(?:_\d+)*)?/.source+")"+/(?![\w$])/.source),lookbehind:!0},operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),i.languages.javascript["class-name"][0].pattern=/(\b(?:class|extends|implements|instanceof|interface|new)\s+)[\w.\\]+/,i.languages.insertBefore("javascript","keyword",{regex:{pattern:RegExp(/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)/.source+/\//.source+"(?:"+/(?:\[(?:[^\]\\\r\n]|\\.)*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}/.source+"|"+/(?:\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.)*\])*\])*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source+")"+/(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/.source),lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:i.languages.regex},"regex-delimiter":/^\/|\/$/,"regex-flags":/^[a-z]+$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:i.languages.javascript},{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,lookbehind:!0,inside:i.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:i.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:i.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),i.languages.insertBefore("javascript","string",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:"comment"},"template-string":{pattern:/`(?:\\[\s\S]|\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}|(?!\$\{)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\$\{|\}$/,alias:"punctuation"},rest:i.languages.javascript}},string:/[\s\S]+/}},"string-property":{pattern:/((?:^|[,{])[ \t]*)(["'])(?:\\(?:\r\n|[\s\S])|(?!\2)[^\\\r\n])*\2(?=\s*:)/m,lookbehind:!0,greedy:!0,alias:"property"}}),i.languages.insertBefore("javascript","operator",{"literal-property":{pattern:/((?:^|[,{])[ \t]*)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*:)/m,lookbehind:!0,alias:"property"}}),i.languages.markup&&(i.languages.markup.tag.addInlined("script","javascript"),i.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,"javascript")),i.languages.js=i.languages.javascript,function(){if(void 0!==i&&"undefined"!=typeof document){Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector);var e={js:"javascript",py:"python",rb:"ruby",ps1:"powershell",psm1:"powershell",sh:"bash",bat:"batch",h:"c",tex:"latex"},t="data-src-status",n="loading",r="loaded",s="pre[data-src]:not(["+t+'="'+r+'"]):not(['+t+'="'+n+'"])';i.hooks.add("before-highlightall",function(e){e.selector+=", "+s}),i.hooks.add("before-sanity-check",function(a){var o=a.element;if(o.matches(s)){a.code="",o.setAttribute(t,n);var l,c,u,h=o.appendChild(document.createElement("CODE"));h.textContent="Loading…";var d=o.getAttribute("data-src"),p=a.language;if("none"===p){var f=(/\.(\w+)$/.exec(d)||[,"none"])[1];p=e[f]||f}i.util.setLanguage(h,p),i.util.setLanguage(o,p);var m=i.plugins.autoloader;m&&m.loadLanguages(p),l=function(e){o.setAttribute(t,r);var n=function(e){var t=/^\s*(\d+)\s*(?:(,)\s*(?:(\d+)\s*)?)?$/.exec(e||"");if(t){var n=Number(t[1]),r=t[2],s=t[3];return r?s?[n,Number(s)]:[n,void 0]:[n,n]}}(o.getAttribute("data-range"));if(n){var s=e.split(/\r\n?|\n/g),a=n[0],l=null==n[1]?s.length:n[1];a<0&&(a+=s.length),a=Math.max(0,Math.min(a-1,s.length)),l<0&&(l+=s.length),l=Math.max(0,Math.min(l,s.length)),e=s.slice(a,l).join("\n"),o.hasAttribute("data-start")||o.setAttribute("data-start",String(a+1))}h.textContent=e,i.highlightElement(h)},c=function(e){o.setAttribute(t,"failed"),h.textContent=e},(u=new XMLHttpRequest).open("GET",d,!0),u.onreadystatechange=function(){4==u.readyState&&(u.status<400&&u.responseText?l(u.responseText):u.status>=400?c("✖ Error "+u.status+" while fetching file: "+u.statusText):c("✖ Error: File does not exist or is empty"))},u.send(null)}}),i.plugins.fileHighlight={highlight:function(e){for(var t,n=(e||document).querySelectorAll(s),r=0;t=n[r++];)i.highlightElement(t)}};var a=!1;i.fileHighlight=function(){a||(console.warn("Prism.fileHighlight is deprecated. Use `Prism.plugins.fileHighlight.highlight` instead."),a=!0),i.plugins.fileHighlight.highlight.apply(this,arguments)}}}()},3371:function(e,t,n){"use strict";var r=n(32486),s=function(e,t){return(s=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var s in t=arguments[n])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e}).apply(this,arguments)},a={Pixel:"Pixel",Percent:"Percent"},o={unit:a.Percent,value:.8};function l(e){return"number"==typeof e?{unit:a.Percent,value:100*e}:"string"==typeof e?e.match(/^(\d*(\.\d+)?)px$/)?{unit:a.Pixel,value:parseFloat(e)}:e.match(/^(\d*(\.\d+)?)%$/)?{unit:a.Percent,value:parseFloat(e)}:(console.warn('scrollThreshold format is invalid. Valid formats: "120px", "50%"...'),o):(console.warn("scrollThreshold should be string or number"),o)}var c=function(e){function t(t){var n=e.call(this,t)||this;return n.lastScrollTop=0,n.actionTriggered=!1,n.startY=0,n.currentY=0,n.dragging=!1,n.maxPullDownDistance=0,n.getScrollableTarget=function(){return n.props.scrollableTarget instanceof HTMLElement?n.props.scrollableTarget:"string"==typeof n.props.scrollableTarget?document.getElementById(n.props.scrollableTarget):(null===n.props.scrollableTarget&&console.warn("You are trying to pass scrollableTarget but it is null. This might\n        happen because the element may not have been added to DOM yet.\n        See https://github.com/ankeetmaini/react-infinite-scroll-component/issues/59 for more info.\n      "),null)},n.onStart=function(e){!n.lastScrollTop&&(n.dragging=!0,e instanceof MouseEvent?n.startY=e.pageY:e instanceof TouchEvent&&(n.startY=e.touches[0].pageY),n.currentY=n.startY,n._infScroll&&(n._infScroll.style.willChange="transform",n._infScroll.style.transition="transform 0.2s cubic-bezier(0,0,0.31,1)"))},n.onMove=function(e){n.dragging&&(e instanceof MouseEvent?n.currentY=e.pageY:e instanceof TouchEvent&&(n.currentY=e.touches[0].pageY),n.currentY<n.startY||(n.currentY-n.startY>=Number(n.props.pullDownToRefreshThreshold)&&n.setState({pullToRefreshThresholdBreached:!0}),n.currentY-n.startY>1.5*n.maxPullDownDistance||!n._infScroll||(n._infScroll.style.overflow="visible",n._infScroll.style.transform="translate3d(0px, "+(n.currentY-n.startY)+"px, 0px)")))},n.onEnd=function(){n.startY=0,n.currentY=0,n.dragging=!1,n.state.pullToRefreshThresholdBreached&&(n.props.refreshFunction&&n.props.refreshFunction(),n.setState({pullToRefreshThresholdBreached:!1})),requestAnimationFrame(function(){n._infScroll&&(n._infScroll.style.overflow="auto",n._infScroll.style.transform="none",n._infScroll.style.willChange="unset")})},n.onScrollListener=function(e){"function"==typeof n.props.onScroll&&setTimeout(function(){return n.props.onScroll&&n.props.onScroll(e)},0);var t=n.props.height||n._scrollableNode?e.target:document.documentElement.scrollTop?document.documentElement:document.body;n.actionTriggered||((n.props.inverse?n.isElementAtTop(t,n.props.scrollThreshold):n.isElementAtBottom(t,n.props.scrollThreshold))&&n.props.hasMore&&(n.actionTriggered=!0,n.setState({showLoader:!0}),n.props.next&&n.props.next()),n.lastScrollTop=t.scrollTop)},n.state={showLoader:!1,pullToRefreshThresholdBreached:!1,prevDataLength:t.dataLength},n.throttledOnScrollListener=(function(e,t,n,r){var s,i=!1,a=0;function o(){s&&clearTimeout(s)}function l(){var l=this,c=Date.now()-a,u=arguments;function h(){a=Date.now(),n.apply(l,u)}i||(r&&!s&&h(),o(),void 0===r&&c>e?h():!0!==t&&(s=setTimeout(r?function(){s=void 0}:h,void 0===r?e-c:e)))}return"boolean"!=typeof t&&(r=n,n=t,t=void 0),l.cancel=function(){o(),i=!0},l})(150,n.onScrollListener).bind(n),n.onStart=n.onStart.bind(n),n.onMove=n.onMove.bind(n),n.onEnd=n.onEnd.bind(n),n}return!function(e,t){function n(){this.constructor=e}s(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}(t,e),t.prototype.componentDidMount=function(){if(void 0===this.props.dataLength)throw Error('mandatory prop "dataLength" is missing. The prop is needed when loading more content. Check README.md for usage');if(this._scrollableNode=this.getScrollableTarget(),this.el=this.props.height?this._infScroll:this._scrollableNode||window,this.el&&this.el.addEventListener("scroll",this.throttledOnScrollListener),"number"==typeof this.props.initialScrollY&&this.el&&this.el instanceof HTMLElement&&this.el.scrollHeight>this.props.initialScrollY&&this.el.scrollTo(0,this.props.initialScrollY),this.props.pullDownToRefresh&&this.el&&(this.el.addEventListener("touchstart",this.onStart),this.el.addEventListener("touchmove",this.onMove),this.el.addEventListener("touchend",this.onEnd),this.el.addEventListener("mousedown",this.onStart),this.el.addEventListener("mousemove",this.onMove),this.el.addEventListener("mouseup",this.onEnd),this.maxPullDownDistance=this._pullDown&&this._pullDown.firstChild&&this._pullDown.firstChild.getBoundingClientRect().height||0,this.forceUpdate(),"function"!=typeof this.props.refreshFunction))throw Error('Mandatory prop "refreshFunction" missing.\n          Pull Down To Refresh functionality will not work\n          as expected. Check README.md for usage\'')},t.prototype.componentWillUnmount=function(){this.el&&(this.el.removeEventListener("scroll",this.throttledOnScrollListener),this.props.pullDownToRefresh&&(this.el.removeEventListener("touchstart",this.onStart),this.el.removeEventListener("touchmove",this.onMove),this.el.removeEventListener("touchend",this.onEnd),this.el.removeEventListener("mousedown",this.onStart),this.el.removeEventListener("mousemove",this.onMove),this.el.removeEventListener("mouseup",this.onEnd)))},t.prototype.componentDidUpdate=function(e){this.props.dataLength!==e.dataLength&&(this.actionTriggered=!1,this.setState({showLoader:!1}))},t.getDerivedStateFromProps=function(e,t){return e.dataLength!==t.prevDataLength?i(i({},t),{prevDataLength:e.dataLength}):null},t.prototype.isElementAtTop=function(e,t){void 0===t&&(t=.8);var n=e===document.body||e===document.documentElement?window.screen.availHeight:e.clientHeight,r=l(t);return r.unit===a.Pixel?e.scrollTop<=r.value+n-e.scrollHeight+1:e.scrollTop<=r.value/100+n-e.scrollHeight+1},t.prototype.isElementAtBottom=function(e,t){void 0===t&&(t=.8);var n=e===document.body||e===document.documentElement?window.screen.availHeight:e.clientHeight,r=l(t);return r.unit===a.Pixel?e.scrollTop+n>=e.scrollHeight-r.value:e.scrollTop+n>=r.value/100*e.scrollHeight},t.prototype.render=function(){var e=this,t=i({height:this.props.height||"auto",overflow:"auto",WebkitOverflowScrolling:"touch"},this.props.style),n=this.props.hasChildren||!!(this.props.children&&this.props.children instanceof Array&&this.props.children.length),s=this.props.pullDownToRefresh&&this.props.height?{overflow:"auto"}:{};return r.createElement("div",{style:s,className:"infinite-scroll-component__outerdiv"},r.createElement("div",{className:"infinite-scroll-component "+(this.props.className||""),ref:function(t){return e._infScroll=t},style:t},this.props.pullDownToRefresh&&r.createElement("div",{style:{position:"relative"},ref:function(t){return e._pullDown=t}},r.createElement("div",{style:{position:"absolute",left:0,right:0,top:-1*this.maxPullDownDistance}},this.state.pullToRefreshThresholdBreached?this.props.releaseToRefreshContent:this.props.pullDownToRefreshContent)),this.props.children,!this.state.showLoader&&!n&&this.props.hasMore&&this.props.loader,this.state.showLoader&&this.props.hasMore&&this.props.loader,!this.props.hasMore&&this.props.endMessage))},t}(r.Component);t.Z=c},17695:function(e,t,n){"use strict";n.d(t,{WU:function(){return c}});var r=["second","minute","hour","day","week","month","year"],s=["秒","分钟","小时","天","周","个月","年"],i={},a=function(e,t){i[e]=t},o=[60,60,24,7,365/7/12,12];function l(e){return e instanceof Date?e:new Date(!isNaN(e)||/^\d+$/.test(e)?parseInt(e):e=(e||"").trim().replace(/\.\d+/,"").replace(/-/,"/").replace(/-/,"/").replace(/(\d)T(\d)/,"$1 $2").replace(/Z/," UTC").replace(/([+-]\d\d):?(\d\d)/," $1$2"))}var c=function(e,t,n){var r;return function(e,t){for(var n=e<0?1:0,r=e=Math.abs(e),s=0;e>=o[s]&&s<o.length;s++)e/=o[s];return s*=2,(e=Math.floor(e))>(0===s?9:1)&&(s+=1),t(e,s,r)[n].replace("%s",e.toString())}((+((r=n&&n.relativeDate)?l(r):new Date)-+l(e))/1e3,i[t]||i.en_US)};a("en_US",function(e,t){if(0===t)return["just now","right now"];var n=r[Math.floor(t/2)];return e>1&&(n+="s"),[e+" "+n+" ago","in "+e+" "+n]}),a("zh_CN",function(e,t){if(0===t)return["刚刚","片刻后"];var n=s[~~(t/2)];return[e+" "+n+"前",e+" "+n+"后"]})},3694:function(){},8051:function(e,t,n){"use strict";n.d(t,{ZP:function(){return o},dn:function(){return o}});var r=n(81816),s=n(28201);let i=/^```([a-z]+)?[\s\n]$/,a=/^~~~([a-z]+)?[\s\n]$/,o=r.NB.create({name:"codeBlock",addOptions:()=>({languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}),content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:e=>{var t;let{languageClassPrefix:n}=this.options;return[...(null===(t=e.firstElementChild)||void 0===t?void 0:t.classList)||[]].filter(e=>e.startsWith(n)).map(e=>e.replace(n,""))[0]||null},rendered:!1}}},parseHTML:()=>[{tag:"pre",preserveWhitespace:"full"}],renderHTML({node:e,HTMLAttributes:t}){return["pre",(0,r.P1)(this.options.HTMLAttributes,t),["code",{class:e.attrs.language?this.options.languageClassPrefix+e.attrs.language:null},0]]},addCommands(){return{setCodeBlock:e=>({commands:t})=>t.setNode(this.name,e),toggleCodeBlock:e=>({commands:t})=>t.toggleNode(this.name,"paragraph",e)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{let{empty:e,$anchor:t}=this.editor.state.selection,n=1===t.pos;return!!e&&t.parent.type.name===this.name&&(!!n||!t.parent.textContent.length)&&this.editor.commands.clearNodes()},Enter:({editor:e})=>{if(!this.options.exitOnTripleEnter)return!1;let{state:t}=e,{selection:n}=t,{$from:r,empty:s}=n;if(!s||r.parent.type!==this.type)return!1;let i=r.parentOffset===r.parent.nodeSize-2,a=r.parent.textContent.endsWith("\n\n");return!!i&&!!a&&e.chain().command(({tr:e})=>(e.delete(r.pos-2,r.pos),!0)).exitCode().run()},ArrowDown:({editor:e})=>{if(!this.options.exitOnArrowDown)return!1;let{state:t}=e,{selection:n,doc:r}=t,{$from:i,empty:a}=n;if(!a||i.parent.type!==this.type||i.parentOffset!==i.parent.nodeSize-2)return!1;let o=i.after();return void 0!==o&&(r.nodeAt(o)?e.commands.command(({tr:e})=>(e.setSelection(s.Y1.near(r.resolve(o))),!0)):e.commands.exitCode())}}},addInputRules(){return[(0,r.zK)({find:i,type:this.type,getAttributes:e=>({language:e[1]})}),(0,r.zK)({find:a,type:this.type,getAttributes:e=>({language:e[1]})})]},addProseMirrorPlugins(){return[new s.Sy({key:new s.H$("codeBlockVSCodeHandler"),props:{handlePaste:(e,t)=>{if(!t.clipboardData||this.editor.isActive(this.type.name))return!1;let n=t.clipboardData.getData("text/plain"),r=t.clipboardData.getData("vscode-editor-data"),i=r?JSON.parse(r):void 0,a=null==i?void 0:i.mode;if(!n||!a)return!1;let{tr:o,schema:l}=e.state,c=l.text(n.replace(/\r\n?/g,"\n"));return o.replaceSelectionWith(this.type.create({language:a},c)),o.selection.$from.parent.type!==this.type&&o.setSelection(s.Bs.near(o.doc.resolve(Math.max(0,o.selection.from-2)))),o.setMeta("paste",!0),e.dispatch(o),!0}}})]}})},35844:function(e,t,n){"use strict";n.d(t,{EK:function(){return a},ZP:function(){return a}});var r=n(81816);let s=/(^|[^`])`([^`]+)`(?!`)/,i=/(^|[^`])`([^`]+)`(?!`)/g,a=r.vc.create({name:"code",addOptions:()=>({HTMLAttributes:{}}),excludes:"_",code:!0,exitable:!0,parseHTML:()=>[{tag:"code"}],renderHTML({HTMLAttributes:e}){return["code",(0,r.P1)(this.options.HTMLAttributes,e),0]},addCommands(){return{setCode:()=>({commands:e})=>e.setMark(this.name),toggleCode:()=>({commands:e})=>e.toggleMark(this.name),unsetCode:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[(0,r.Cf)({find:s,type:this.type})]},addPasteRules(){return[(0,r.K9)({find:i,type:this.type})]}})},47641:function(e,t,n){"use strict";n.d(t,{ZP:function(){return eq}});var r=n(81816);let s="numeric",i="ascii",a="alpha",o="asciinumeric",l="alphanumeric",c="domain",u="emoji",h="whitespace";function d(e,t,n){for(let r in t[s]&&(t[o]=!0,t[l]=!0),t[i]&&(t[o]=!0,t[a]=!0),t[o]&&(t[l]=!0),t[a]&&(t[l]=!0),t[l]&&(t[c]=!0),t[u]&&(t[c]=!0),t){let t=(r in n||(n[r]=[]),n[r]);0>t.indexOf(e)&&t.push(e)}}function p(e=null){this.j={},this.jr=[],this.jd=null,this.t=e}p.groups={},p.prototype={accepts(){return!!this.t},go(e){let t=this.j[e];if(t)return t;for(let t=0;t<this.jr.length;t++){let n=this.jr[t][0],r=this.jr[t][1];if(r&&n.test(e))return r}return this.jd},has(e,t=!1){return t?e in this.j:!!this.go(e)},ta(e,t,n,r){for(let s=0;s<e.length;s++)this.tt(e[s],t,n,r)},tr(e,t,n,r){let s;return r=r||p.groups,t&&t.j?s=t:(s=new p(t),n&&r&&d(t,n,r)),this.jr.push([e,s]),s},ts(e,t,n,r){let s=this,i=e.length;if(!i)return s;for(let t=0;t<i-1;t++)s=s.tt(e[t]);return s.tt(e[i-1],t,n,r)},tt(e,t,n,r){if(r=r||p.groups,t&&t.j)return this.j[e]=t,t;let s,i=this.go(e);return i?(Object.assign((s=new p).j,i.j),s.jr.push.apply(s.jr,i.jr),s.jd=i.jd,s.t=i.t):s=new p,t&&(r&&(s.t&&"string"==typeof s.t?d(t,Object.assign(function(e,t){let n={};for(let r in t)t[r].indexOf(e)>=0&&(n[r]=!0);return n}(s.t,r),n),r):n&&d(t,n,r)),s.t=t),this.j[e]=s,s}};let f=(e,t,n,r,s)=>e.ta(t,n,r,s),m=(e,t,n,r,s)=>e.tr(t,n,r,s),g=(e,t,n,r,s)=>e.ts(t,n,r,s),E=(e,t,n,r,s)=>e.tt(t,n,r,s),T="WORD",A="UWORD",_="ASCIINUMERICAL",S="ALPHANUMERICAL",y="LOCALHOST",k="UTLD",C="SCHEME",N="SLASH_SCHEME",I="OPENBRACE",b="CLOSEBRACE",O="OPENBRACKET",D="CLOSEBRACKET",R="OPENPAREN",M="CLOSEPAREN",v="OPENANGLEBRACKET",L="CLOSEANGLEBRACKET",w="FULLWIDTHLEFTPAREN",P="FULLWIDTHRIGHTPAREN",x="LEFTCORNERBRACKET",F="RIGHTCORNERBRACKET",B="LEFTWHITECORNERBRACKET",H="RIGHTWHITECORNERBRACKET",U="FULLWIDTHLESSTHAN",G="FULLWIDTHGREATERTHAN",Y="AMPERSAND",z="APOSTROPHE",W="ASTERISK",q="BACKSLASH",$="BACKTICK",j="CARET",V="COLON",K="COMMA",Q="DOLLAR",X="EQUALS",J="EXCLAMATION",Z="HYPHEN",ee="PERCENT",et="PIPE",en="PLUS",er="POUND",es="QUERY",ei="QUOTE",ea="FULLWIDTHMIDDLEDOT",eo="SEMI",el="SLASH",ec="TILDE",eu="UNDERSCORE",eh="EMOJI";var ed=Object.freeze({__proto__:null,ALPHANUMERICAL:S,AMPERSAND:Y,APOSTROPHE:z,ASCIINUMERICAL:_,ASTERISK:W,AT:"AT",BACKSLASH:q,BACKTICK:$,CARET:j,CLOSEANGLEBRACKET:L,CLOSEBRACE:b,CLOSEBRACKET:D,CLOSEPAREN:M,COLON:V,COMMA:K,DOLLAR:Q,DOT:"DOT",EMOJI:eh,EQUALS:X,EXCLAMATION:J,FULLWIDTHGREATERTHAN:G,FULLWIDTHLEFTPAREN:w,FULLWIDTHLESSTHAN:U,FULLWIDTHMIDDLEDOT:ea,FULLWIDTHRIGHTPAREN:P,HYPHEN:Z,LEFTCORNERBRACKET:x,LEFTWHITECORNERBRACKET:B,LOCALHOST:y,NL:"NL",NUM:"NUM",OPENANGLEBRACKET:v,OPENBRACE:I,OPENBRACKET:O,OPENPAREN:R,PERCENT:ee,PIPE:et,PLUS:en,POUND:er,QUERY:es,QUOTE:ei,RIGHTCORNERBRACKET:F,RIGHTWHITECORNERBRACKET:H,SCHEME:C,SEMI:eo,SLASH:el,SLASH_SCHEME:N,SYM:"SYM",TILDE:ec,TLD:"TLD",UNDERSCORE:eu,UTLD:k,UWORD:A,WORD:T,WS:"WS"});let ep=/[a-z]/,ef=/\p{L}/u,em=/\p{Emoji}/u,eg=/\d/,eE=/\s/,eT=null,eA=null;function e_(e,t){let n=function(e){let t=[],n=e.length,r=0;for(;r<n;){let s,i=e.charCodeAt(r),a=i<55296||i>56319||r+1===n||(s=e.charCodeAt(r+1))<56320||s>57343?e[r]:e.slice(r,r+2);t.push(a),r+=a.length}return t}(t.replace(/[A-Z]/g,e=>e.toLowerCase())),r=n.length,s=[],i=0,a=0;for(;a<r;){let o=e,l=null,c=0,u=null,h=-1,d=-1;for(;a<r&&(l=o.go(n[a]));)(o=l).accepts()?(h=0,d=0,u=o):h>=0&&(h+=n[a].length,d++),c+=n[a].length,i+=n[a].length,a++;i-=h,a-=d,c-=h,s.push({t:u.t,v:t.slice(i-c,i),s:i-c,e:i})}return s}function eS(e,t,n,r,s){let i;let a=t.length;for(let n=0;n<a-1;n++){let a=t[n];e.j[a]?i=e.j[a]:((i=new p(r)).jr=s.slice(),e.j[a]=i),e=i}return(i=new p(n)).jr=s.slice(),e.j[t[a-1]]=i,i}function ey(e){let t=[],n=[],r=0;for(;r<e.length;){let s=0;for(;"0123456789".indexOf(e[r+s])>=0;)s++;if(s>0){t.push(n.join(""));for(let t=parseInt(e.substring(r,r+s),10);t>0;t--)n.pop();r+=s}else n.push(e[r]),r++}return t}let ek={defaultProtocol:"http",events:null,format:eN,formatHref:eN,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function eC(e,t=null){let n=Object.assign({},ek);e&&(n=Object.assign(n,e instanceof eC?e.o:e));let r=n.ignoreTags,s=[];for(let e=0;e<r.length;e++)s.push(r[e].toUpperCase());this.o=n,t&&(this.defaultRender=t),this.ignoreTags=s}function eN(e){return e}function eI(e,t){this.t="token",this.v=e,this.tk=t}function eb(e,t){class n extends eI{constructor(t,n){super(t,n),this.t=e}}for(let e in t)n.prototype[e]=t[e];return n.t=e,n}eC.prototype={o:ek,ignoreTags:[],defaultRender:e=>e,check(e){return this.get("validate",e.toString(),e)},get(e,t,n){let r=null!=t,s=this.o[e];return s&&("object"==typeof s?"function"==typeof(s=n.t in s?s[n.t]:ek[e])&&r&&(s=s(t,n)):"function"==typeof s&&r&&(s=s(t,n.t,n))),s},getObj(e,t,n){let r=this.o[e];return"function"==typeof r&&null!=t&&(r=r(t,n.t,n)),r},render(e){let t=e.render(this);return(this.get("render",null,e)||this.defaultRender)(t,e.t,e)}},eI.prototype={isLink:!1,toString(){return this.v},toHref(e){return this.toString()},toFormattedString(e){let t=this.toString(),n=e.get("truncate",t,this),r=e.get("format",t,this);return n&&r.length>n?r.substring(0,n)+"…":r},toFormattedHref(e){return e.get("formatHref",this.toHref(e.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(e=ek.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(e),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(e){return{type:this.t,value:this.toFormattedString(e),isLink:this.isLink,href:this.toFormattedHref(e),start:this.startIndex(),end:this.endIndex()}},validate(e){return e.get("validate",this.toString(),this)},render(e){let t=this.toHref(e.get("defaultProtocol")),n=e.get("formatHref",t,this),r=e.get("tagName",t,this),s=this.toFormattedString(e),i={},a=e.get("className",t,this),o=e.get("target",t,this),l=e.get("rel",t,this),c=e.getObj("attributes",t,this),u=e.getObj("events",t,this);return i.href=n,a&&(i.class=a),o&&(i.target=o),l&&(i.rel=l),c&&Object.assign(i,c),{tagName:r,attributes:i,content:s,eventListeners:u}}};let eO=eb("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),eD=eb("text"),eR=eb("nl"),eM=eb("url",{isLink:!0,toHref(e=ek.defaultProtocol){return this.hasProtocol()?this.v:`${e}://${this.v}`},hasProtocol(){let e=this.tk;return e.length>=2&&e[0].t!==y&&e[1].t===V}}),ev=e=>new p(e);function eL(e,t,n){let r=n[0].s,s=n[n.length-1].e;return new e(t.slice(r,s),n)}let ew="undefined"!=typeof console&&console&&console.warn||(()=>{}),eP={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function ex(e,t=!1){if(eP.initialized&&ew(`linkifyjs: already initialized - will not register custom scheme "${e}" until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(e))throw Error(`linkifyjs: incorrect scheme format.
1. Must only contain digits, lowercase ASCII letters or "-"
2. Cannot start or end with "-"
3. "-" cannot repeat`);eP.customSchemes.push([e,t])}function eF(e){return eP.initialized||function(){eP.scanner=function(e=[]){let t={};p.groups=t;let n=new p;null==eT&&(eT=ey("aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0axi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5m\xf6gensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2")),null==eA&&(eA=ey("ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2")),E(n,"'",z),E(n,"{",I),E(n,"}",b),E(n,"[",O),E(n,"]",D),E(n,"(",R),E(n,")",M),E(n,"<",v),E(n,">",L),E(n,"（",w),E(n,"）",P),E(n,"「",x),E(n,"」",F),E(n,"『",B),E(n,"』",H),E(n,"＜",U),E(n,"＞",G),E(n,"&",Y),E(n,"*",W),E(n,"@","AT"),E(n,"`",$),E(n,"^",j),E(n,":",V),E(n,",",K),E(n,"$",Q),E(n,".","DOT"),E(n,"=",X),E(n,"!",J),E(n,"-",Z),E(n,"%",ee),E(n,"|",et),E(n,"+",en),E(n,"#",er),E(n,"?",es),E(n,'"',ei),E(n,"/",el),E(n,";",eo),E(n,"~",ec),E(n,"_",eu),E(n,"\\",q),E(n,"・",ea);let r=m(n,eg,"NUM",{[s]:!0});m(r,eg,r);let f=m(r,ep,_,{[o]:!0}),e_=m(r,ef,S,{[l]:!0}),ek=m(n,ep,T,{[i]:!0});m(ek,eg,f),m(ek,ep,ek),m(f,eg,f),m(f,ep,f);let eC=m(n,ef,A,{[a]:!0});m(eC,ep),m(eC,eg,e_),m(eC,ef,eC),m(e_,eg,e_),m(e_,ep),m(e_,ef,e_);let eN=E(n,"\n","NL",{[h]:!0}),eI=E(n,"\r","WS",{[h]:!0}),eb=m(n,eE,"WS",{[h]:!0});E(n,"￼",eb),E(eI,"\n",eN),E(eI,"￼",eb),m(eI,eE,eb),E(eb,"\r"),E(eb,"\n"),m(eb,eE,eb),E(eb,"￼",eb);let eO=m(n,em,eh,{[u]:!0});E(eO,"#"),m(eO,em,eO),E(eO,"️",eO);let eD=E(eO,"‍");E(eD,"#"),m(eD,em,eO);let eR=[[ep,ek],[eg,f]],eM=[[ep,null],[ef,eC],[eg,e_]];for(let e=0;e<eT.length;e++)eS(n,eT[e],"TLD",T,eR);for(let e=0;e<eA.length;e++)eS(n,eA[e],k,A,eM);d("TLD",{tld:!0,ascii:!0},t),d(k,{utld:!0,alpha:!0},t),eS(n,"file",C,T,eR),eS(n,"mailto",C,T,eR),eS(n,"http",N,T,eR),eS(n,"https",N,T,eR),eS(n,"ftp",N,T,eR),eS(n,"ftps",N,T,eR),d(C,{scheme:!0,ascii:!0},t),d(N,{slashscheme:!0,ascii:!0},t),e=e.sort((e,t)=>e[0]>t[0]?1:-1);for(let t=0;t<e.length;t++){let r=e[t][0],a=e[t][1]?{scheme:!0}:{slashscheme:!0};r.indexOf("-")>=0?a[c]=!0:ep.test(r)?eg.test(r)?a[o]=!0:a[i]=!0:a[s]=!0,g(n,r,r,a)}return g(n,"localhost",y,{ascii:!0}),n.jd=new p("SYM"),{start:n,tokens:Object.assign({groups:t},ed)}}(eP.customSchemes);for(let e=0;e<eP.tokenQueue.length;e++)eP.tokenQueue[e][1]({scanner:eP.scanner});eP.parser=function({groups:e}){let t=e.domain.concat([Y,W,"AT",q,$,j,Q,X,Z,"NUM",ee,et,en,er,el,"SYM",ec,eu]),n=[z,V,K,"DOT",J,ee,es,ei,eo,v,L,I,b,D,O,R,M,w,P,x,F,B,H,U,G],r=[Y,z,W,q,$,j,Q,X,Z,I,b,ee,et,en,er,es,el,"SYM",ec,eu],s=ev(),i=E(s,ec);f(i,r,i),f(i,e.domain,i);let a=ev(),o=ev(),l=ev();f(s,e.domain,a),f(s,e.scheme,o),f(s,e.slashscheme,l),f(a,r,i),f(a,e.domain,a);let c=E(a,"AT");E(i,"AT",c),E(o,"AT",c),E(l,"AT",c);let u=E(i,"DOT");f(u,r,i),f(u,e.domain,i);let h=ev();f(c,e.domain,h),f(h,e.domain,h);let d=E(h,"DOT");f(d,e.domain,h);let p=ev(eO);f(d,e.tld,p),f(d,e.utld,p),E(c,y,p);let m=E(h,Z);E(m,Z,m),f(m,e.domain,h),f(p,e.domain,h),E(p,"DOT",d),E(p,Z,m),f(E(p,V),e.numeric,eO);let g=E(a,Z),T=E(a,"DOT");E(g,Z,g),f(g,e.domain,a),f(T,r,i),f(T,e.domain,a);let A=ev(eM);f(T,e.tld,A),f(T,e.utld,A),f(A,e.domain,a),f(A,r,i),E(A,"DOT",T),E(A,Z,g),E(A,"AT",c);let _=E(A,V),S=ev(eM);f(_,e.numeric,S);let k=ev(eM),C=ev();f(k,t,k),f(k,n,C),f(C,t,k),f(C,n,C),E(A,el,k),E(S,el,k);let N=E(o,V),ea=E(l,V),eh=E(ea,el),ep=E(eh,el);f(o,e.domain,a),E(o,"DOT",T),E(o,Z,g),f(l,e.domain,a),E(l,"DOT",T),E(l,Z,g),f(N,e.domain,k),E(N,el,k),E(N,es,k),f(ep,e.domain,k),f(ep,t,k),E(ep,el,k);let ef=[[I,b],[O,D],[R,M],[v,L],[w,P],[x,F],[B,H],[U,G]];for(let e=0;e<ef.length;e++){let[r,s]=ef[e],i=E(k,r);E(C,r,i),E(i,s,k);let a=ev(eM);f(i,t,a);let o=ev();f(i,n),f(a,t,a),f(a,n,o),f(o,t,a),f(o,n,o),E(a,s,k),E(o,s,k)}return E(s,y,A),E(s,"NL",eR),{start:s,tokens:ed}}(eP.scanner.tokens);for(let e=0;e<eP.pluginQueue.length;e++)eP.pluginQueue[e][1]({scanner:eP.scanner,parser:eP.parser});eP.initialized=!0}(),function(e,t,n){let r=n.length,s=0,i=[],a=[];for(;s<r;){let o=e,l=null,c=null,u=0,h=null,d=-1;for(;s<r&&!(l=o.go(n[s].t));)a.push(n[s++]);for(;s<r&&(c=l||o.go(n[s].t));)l=null,(o=c).accepts()?(d=0,h=o):d>=0&&d++,s++,u++;if(d<0)(s-=u)<r&&(a.push(n[s]),s++);else{a.length>0&&(i.push(eL(eD,t,a)),a=[]),s-=d,u-=d;let e=h.t,r=n.slice(s-u,s);i.push(eL(e,t,r))}}return a.length>0&&i.push(eL(eD,t,a)),i}(eP.parser.start,e,e_(eP.scanner.start,e))}function eB(e,t=null,n=null){if(t&&"object"==typeof t){if(n)throw Error(`linkifyjs: Invalid link type ${t}; must be a string`);n=t,t=null}let r=new eC(n),s=eF(e),i=[];for(let e=0;e<s.length;e++){let n=s[e];n.isLink&&(!t||n.t===t)&&r.check(n)&&i.push(n.toFormattedObject(r))}return i}eF.scan=e_;var eH=n(28201);let eU="[\0- \xa0 ᠎ -\u2029 　]",eG=new RegExp(eU),eY=RegExp(`${eU}$`),ez=RegExp(eU,"g");function eW(e,t){let n=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return t&&t.forEach(e=>{let t="string"==typeof e?e:e.scheme;t&&n.push(t)}),!e||e.replace(ez,"").match(RegExp(`^(?:(?:${n.join("|")}):|[^a-z]|[a-z0-9+.-]+(?:[^a-z+.-:]|$))`,"i"))}let eq=r.vc.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate,console.warn("The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.")),this.options.protocols.forEach(e=>{if("string"==typeof e){ex(e);return}ex(e.scheme,e.optionalSlashes)})},onDestroy(){p.groups={},eP.scanner=null,eP.parser=null,eP.tokenQueue=[],eP.pluginQueue=[],eP.customSchemes=[],eP.initialized=!1},inclusive(){return this.options.autolink},addOptions:()=>({openOnClick:!0,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(e,t)=>!!eW(e,t.protocols),validate:e=>!!e,shouldAutoLink:e=>!!e}),addAttributes(){return{href:{default:null,parseHTML:e=>e.getAttribute("href")},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:e=>{let t=e.getAttribute("href");return!!(t&&this.options.isAllowedUri(t,{defaultValidate:e=>!!eW(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol}))&&null}}]},renderHTML({HTMLAttributes:e}){return this.options.isAllowedUri(e.href,{defaultValidate:e=>!!eW(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",(0,r.P1)(this.options.HTMLAttributes,e),0]:["a",(0,r.P1)(this.options.HTMLAttributes,{...e,href:""}),0]},addCommands(){return{setLink:e=>({chain:t})=>{let{href:n}=e;return!!this.options.isAllowedUri(n,{defaultValidate:e=>!!eW(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&t().setMark(this.name,e).setMeta("preventAutolink",!0).run()},toggleLink:e=>({chain:t})=>{let{href:n}=e;return!!this.options.isAllowedUri(n,{defaultValidate:e=>!!eW(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&t().toggleMark(this.name,e,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()},unsetLink:()=>({chain:e})=>e().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[(0,r.K9)({find:e=>{let t=[];if(e){let{protocols:n,defaultProtocol:r}=this.options,s=eB(e).filter(e=>e.isLink&&this.options.isAllowedUri(e.value,{defaultValidate:e=>!!eW(e,n),protocols:n,defaultProtocol:r}));s.length&&s.forEach(e=>t.push({text:e.value,data:{href:e.href},index:e.start}))}return t},type:this.type,getAttributes:e=>{var t;return{href:null===(t=e.data)||void 0===t?void 0:t.href}}})]},addProseMirrorPlugins(){var e,t,n;let s=[],{protocols:i,defaultProtocol:a}=this.options;return this.options.autolink&&s.push((e={type:this.type,defaultProtocol:this.options.defaultProtocol,validate:e=>this.options.isAllowedUri(e,{defaultValidate:e=>!!eW(e,i),protocols:i,defaultProtocol:a}),shouldAutoLink:this.options.shouldAutoLink},new eH.Sy({key:new eH.H$("autolink"),appendTransaction:(t,n,s)=>{let i=t.some(e=>e.docChanged)&&!n.doc.eq(s.doc),a=t.some(e=>e.getMeta("preventAutolink"));if(!i||a)return;let{tr:o}=s,l=(0,r.XP)(n.doc,[...t]);if((0,r.QC)(l).forEach(({newRange:t})=>{let n,i;let a=(0,r.b5)(s.doc,t,e=>e.isTextblock);if(a.length>1)n=a[0],i=s.doc.textBetween(n.pos,n.pos+n.node.nodeSize,void 0," ");else if(a.length){let e=s.doc.textBetween(t.from,t.to," "," ");if(!eY.test(e))return;n=a[0],i=s.doc.textBetween(n.pos,t.to,void 0," ")}if(n&&i){let t=i.split(eG).filter(Boolean);if(t.length<=0)return!1;let a=t[t.length-1],l=n.pos+i.lastIndexOf(a);if(!a)return!1;let c=eF(a).map(t=>t.toObject(e.defaultProtocol));if(!(1===c.length?c[0].isLink:3===c.length&&!!c[1].isLink&&["()","[]"].includes(c[0].value+c[2].value)))return!1;c.filter(e=>e.isLink).map(e=>({...e,from:l+e.start+1,to:l+e.end+1})).filter(e=>!s.schema.marks.code||!s.doc.rangeHasMark(e.from,e.to,s.schema.marks.code)).filter(t=>e.validate(t.value)).filter(t=>e.shouldAutoLink(t.value)).forEach(t=>{(0,r.tI)(t.from,t.to,s.doc).some(t=>t.mark.type===e.type)||o.addMark(t.from,t.to,e.type.create({href:t.href}))})}}),o.steps.length)return o}}))),!0===this.options.openOnClick&&s.push((t={type:this.type},new eH.Sy({key:new eH.H$("handleClickLink"),props:{handleClick:(e,n,s)=>{var i,a;if(0!==s.button||!e.editable)return!1;let o=s.target,l=[];for(;"DIV"!==o.nodeName;)l.push(o),o=o.parentNode;if(!l.find(e=>"A"===e.nodeName))return!1;let c=(0,r.u9)(e.state,t.type.name),u=s.target,h=null!==(i=null==u?void 0:u.href)&&void 0!==i?i:c.href,d=null!==(a=null==u?void 0:u.target)&&void 0!==a?a:c.target;return!!u&&!!h&&(window.open(h,d),!0)}}}))),this.options.linkOnPaste&&s.push((n={editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type},new eH.Sy({key:new eH.H$("handlePasteLink"),props:{handlePaste:(e,t,r)=>{let{state:s}=e,{selection:i}=s,{empty:a}=i;if(a)return!1;let o="";r.content.forEach(e=>{o+=e.textContent});let l=eB(o,{defaultProtocol:n.defaultProtocol}).find(e=>e.isLink&&e.value===o);return!!o&&!!l&&n.editor.commands.setMark(n.type,{href:l.href})}}}))),s}})},91296:function(e,t,n){"use strict";n.d(t,{Z:function(){return d}});var r=n(81816),s=n(77247),i=n(28201),a=n(51058);function o(e){var t;let{char:n,allowSpaces:s,allowToIncludeChar:i,allowedPrefixes:a,startOfLine:o,$position:l}=e,c=s&&!i,u=(0,r.Ov)(n),h=RegExp(`\\s${u}$`),d=o?"^":"",p=i?"":u,f=c?RegExp(`${d}${u}.*?(?=\\s${p}|$)`,"gm"):RegExp(`${d}(?:^)?${u}[^\\s${p}]*`,"gm"),m=(null==(t=l.nodeBefore)?void 0:t.isText)&&l.nodeBefore.text;if(!m)return null;let g=l.pos-m.length,E=Array.from(m.matchAll(f)).pop();if(!E||void 0===E.input||void 0===E.index)return null;let T=E.input.slice(Math.max(0,E.index-1),E.index),A=RegExp(`^[${null==a?void 0:a.join("")}\0]?$`).test(T);if(null!==a&&!A)return null;let _=g+E.index,S=_+E[0].length;return(c&&h.test(m.slice(S-1,S+1))&&(E[0]+=" ",S+=1),_<l.pos&&S>=l.pos)?{range:{from:_,to:S},query:E[0].slice(n.length),text:E[0]}:null}var l=new i.H$("suggestion"),c=function({pluginKey:e=l,editor:t,char:n="@",allowSpaces:r=!1,allowToIncludeChar:s=!1,allowedPrefixes:c=[" "],startOfLine:u=!1,decorationTag:h="span",decorationClass:d="suggestion",decorationContent:p="",decorationEmptyClass:f="is-empty",command:m=()=>null,items:g=()=>[],render:E=()=>({}),allow:T=()=>!0,findSuggestionMatch:A=o}){let _;let S=null==E?void 0:E(),y=new i.Sy({key:e,view(){return{update:async(e,n)=>{var r,s,i,a,o,l,c;let u=null==(r=this.key)?void 0:r.getState(n),h=null==(s=this.key)?void 0:s.getState(e.state),d=u.active&&h.active&&u.range.from!==h.range.from,p=!u.active&&h.active,f=u.active&&!h.active,E=!p&&!f&&u.query!==h.query,T=p||d&&E,A=E||d,y=f||d&&E;if(!T&&!A&&!y)return;let k=y&&!T?u:h,C=e.dom.querySelector(`[data-decoration-id="${k.decorationId}"]`);_={editor:t,range:k.range,query:k.query,text:k.text,items:[],command:e=>m({editor:t,range:k.range,props:e}),decorationNode:C,clientRect:C?()=>{var n;let{decorationId:r}=null==(n=this.key)?void 0:n.getState(t.state),s=e.dom.querySelector(`[data-decoration-id="${r}"]`);return(null==s?void 0:s.getBoundingClientRect())||null}:null},T&&(null==(i=null==S?void 0:S.onBeforeStart)||i.call(S,_)),A&&(null==(a=null==S?void 0:S.onBeforeUpdate)||a.call(S,_)),(A||T)&&(_.items=await g({editor:t,query:k.query})),y&&(null==(o=null==S?void 0:S.onExit)||o.call(S,_)),A&&(null==(l=null==S?void 0:S.onUpdate)||l.call(S,_)),T&&(null==(c=null==S?void 0:S.onStart)||c.call(S,_))},destroy:()=>{var e;_&&(null==(e=null==S?void 0:S.onExit)||e.call(S,_))}}},state:{init:()=>({active:!1,range:{from:0,to:0},query:null,text:null,composing:!1}),apply(e,i,a,o){let{isEditable:l}=t,{composing:h}=t.view,{selection:d}=e,{empty:p,from:f}=d,m={...i};if(m.composing=h,l&&(p||t.view.composing)){(f<i.range.from||f>i.range.to)&&!h&&!i.composing&&(m.active=!1);let e=A({char:n,allowSpaces:r,allowToIncludeChar:s,allowedPrefixes:c,startOfLine:u,$position:d.$from}),a=`id_${Math.floor(***********Math.random())}`;e&&T({editor:t,state:o,range:e.range,isActive:i.active})?(m.active=!0,m.decorationId=i.decorationId?i.decorationId:a,m.range=e.range,m.query=e.query,m.text=e.text):m.active=!1}else m.active=!1;return m.active||(m.decorationId=null,m.range={from:0,to:0},m.query=null,m.text=null),m}},props:{handleKeyDown(e,t){var n;let{active:r,range:s}=y.getState(e.state);return!!r&&((null==(n=null==S?void 0:S.onKeyDown)?void 0:n.call(S,{view:e,event:t,range:s}))||!1)},decorations(e){let{active:t,range:n,decorationId:r,query:s}=y.getState(e);if(!t)return null;let i=!(null==s?void 0:s.length),o=[d];return i&&o.push(f),a.EH.create(e.doc,[a.p.inline(n.from,n.to,{nodeName:h,class:o.join(" "),"data-decoration-id":r,"data-decoration-content":p})])}}});return y};function u(e){return(e.options.suggestions.length?e.options.suggestions:[e.options.suggestion]).map(t=>(function({editor:e,overrideSuggestionOptions:t,extensionName:n,char:r="@"}){return{editor:e,char:r,pluginKey:new i.H$,command:({editor:e,range:t,props:s})=>{var i,a,o;let l=e.view.state.selection.$to.nodeAfter;(null===(i=null==l?void 0:l.text)||void 0===i?void 0:i.startsWith(" "))&&(t.to+=1),e.chain().focus().insertContentAt(t,[{type:n,attrs:{...s,mentionSuggestionChar:r}},{type:"text",text:" "}]).run(),null===(o=null===(a=e.view.dom.ownerDocument.defaultView)||void 0===a?void 0:a.getSelection())||void 0===o||o.collapseToEnd()},allow:({state:e,range:t})=>{let r=e.doc.resolve(t.from),s=e.schema.nodes[n];return!!r.parent.type.contentMatch.matchType(s)},...t}})({editor:e.editor,overrideSuggestionOptions:t,extensionName:e.name,char:t.char}))}function h(e,t){let n=u(e);return n.find(e=>e.char===t)||(n.length?n[0]:null)}let d=r.NB.create({name:"mention",priority:101,addOptions:()=>({HTMLAttributes:{},renderText({node:e,suggestion:t}){var n,r;return`${null!==(n=null==t?void 0:t.char)&&void 0!==n?n:"@"}${null!==(r=e.attrs.label)&&void 0!==r?r:e.attrs.id}`},deleteTriggerWithBackspace:!1,renderHTML({options:e,node:t,suggestion:n}){var s,i;return["span",(0,r.P1)(this.HTMLAttributes,e.HTMLAttributes),`${null!==(s=null==n?void 0:n.char)&&void 0!==s?s:"@"}${null!==(i=t.attrs.label)&&void 0!==i?i:t.attrs.id}`]},suggestions:[],suggestion:{}}),group:"inline",inline:!0,selectable:!1,atom:!0,addAttributes:()=>({id:{default:null,parseHTML:e=>e.getAttribute("data-id"),renderHTML:e=>e.id?{"data-id":e.id}:{}},label:{default:null,parseHTML:e=>e.getAttribute("data-label"),renderHTML:e=>e.label?{"data-label":e.label}:{}},mentionSuggestionChar:{default:"@",parseHTML:e=>e.getAttribute("data-mention-suggestion-char"),renderHTML:e=>({"data-mention-suggestion-char":e.mentionSuggestionChar})}}),parseHTML(){return[{tag:`span[data-type="${this.name}"]`}]},renderHTML({node:e,HTMLAttributes:t}){let n=h(this,e.attrs.mentionSuggestionChar);if(void 0!==this.options.renderLabel)return console.warn("renderLabel is deprecated use renderText and renderHTML instead"),["span",(0,r.P1)({"data-type":this.name},this.options.HTMLAttributes,t),this.options.renderLabel({options:this.options,node:e,suggestion:n})];let s={...this.options};s.HTMLAttributes=(0,r.P1)({"data-type":this.name},this.options.HTMLAttributes,t);let i=this.options.renderHTML({options:s,node:e,suggestion:n});return"string"==typeof i?["span",(0,r.P1)({"data-type":this.name},this.options.HTMLAttributes,t),i]:i},renderText({node:e}){let t={options:this.options,node:e,suggestion:h(this,e.attrs.mentionSuggestionChar)};return void 0!==this.options.renderLabel?(console.warn("renderLabel is deprecated use renderText and renderHTML instead"),this.options.renderLabel(t)):this.options.renderText(t)},addKeyboardShortcuts(){return{Backspace:()=>this.editor.commands.command(({tr:e,state:t})=>{let n=!1,{selection:r}=t,{empty:i,anchor:a}=r;if(!i)return!1;t.doc.nodesBetween(a-1,a,(t,r)=>{if(t.type.name===this.name)return n=!0,e.insertText(this.options.deleteTriggerWithBackspace?"":this.options.suggestion.char||"",r,r+t.nodeSize),!1});let o=new s.NB,l=0;return t.doc.nodesBetween(a-1,a,(e,t)=>{if(e.type.name===this.name)return n=!0,o=e,l=t,!1}),n&&e.insertText(this.options.deleteTriggerWithBackspace?"":o.attrs.mentionSuggestionChar,l,l+o.nodeSize),n})}},addProseMirrorPlugins(){return u(this).map(c)}})},47233:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});var r=n(81816),s=n(28201),i=n(51058);let a=r.hj.create({name:"placeholder",addOptions:()=>({emptyEditorClass:"is-editor-empty",emptyNodeClass:"is-empty",placeholder:"Write something …",showOnlyWhenEditable:!0,showOnlyCurrent:!0,includeChildren:!1}),addProseMirrorPlugins(){return[new s.Sy({key:new s.H$("placeholder"),props:{decorations:({doc:e,selection:t})=>{let n=this.editor.isEditable||!this.options.showOnlyWhenEditable,{anchor:s}=t,a=[];if(!n)return null;let o=this.editor.isEmpty;return e.descendants((e,t)=>{let n=s>=t&&s<=t+e.nodeSize,l=!e.isLeaf&&(0,r.bR)(e);if((n||!this.options.showOnlyCurrent)&&l){let r=[this.options.emptyNodeClass];o&&r.push(this.options.emptyEditorClass);let s=i.p.node(t,t+e.nodeSize,{class:r.join(" "),"data-placeholder":"function"==typeof this.options.placeholder?this.options.placeholder({editor:this.editor,node:e,pos:t,hasAnchor:n}):this.options.placeholder});a.push(s)}return this.options.includeChildren}),i.EH.create(e,a)}}})]}})},65470:function(e,t,n){"use strict";n.d(t,{jE:function(){return I},kg:function(){return g}});var r,s,i,a=n(32486),o=n(54087),l=n(81816),c={exports:{}},u={};c.exports=function(){if(s)return u;s=1;var e="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},t=a.useState,n=a.useEffect,r=a.useLayoutEffect,i=a.useDebugValue;function o(t){var n=t.getSnapshot;t=t.value;try{var r=n();return!e(t,r)}catch(e){return!0}}var l="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,s){var a=s(),l=t({inst:{value:a,getSnapshot:s}}),c=l[0].inst,u=l[1];return r(function(){c.value=a,c.getSnapshot=s,o(c)&&u({inst:c})},[e,a,s]),n(function(){return o(c)&&u({inst:c}),e(function(){o(c)&&u({inst:c})})},[e]),i(a),a};return u.useSyncExternalStore=void 0!==a.useSyncExternalStore?a.useSyncExternalStore:l,u}();var h=c.exports;let d=(...e)=>t=>{e.forEach(e=>{"function"==typeof e?e(t):e&&(e.current=t)})},p=({contentComponent:e})=>{let t=h.useSyncExternalStore(e.subscribe,e.getSnapshot,e.getServerSnapshot);return a.createElement(a.Fragment,null,Object.values(t))};class f extends a.Component{constructor(e){var t;super(e),this.editorContentRef=a.createRef(),this.initialized=!1,this.state={hasContentComponentInitialized:!!(null===(t=e.editor)||void 0===t?void 0:t.contentComponent)}}componentDidMount(){this.init()}componentDidUpdate(){this.init()}init(){let e=this.props.editor;if(e&&!e.isDestroyed&&e.options.element){if(e.contentComponent)return;let t=this.editorContentRef.current;t.append(...e.options.element.childNodes),e.setOptions({element:t}),e.contentComponent=function(){let e=new Set,t={};return{subscribe:t=>(e.add(t),()=>{e.delete(t)}),getSnapshot:()=>t,getServerSnapshot:()=>t,setRenderer(n,r){t={...t,[n]:o.createPortal(r.reactElement,r.element,n)},e.forEach(e=>e())},removeRenderer(n){let r={...t};delete r[n],t=r,e.forEach(e=>e())}}}(),this.state.hasContentComponentInitialized||(this.unsubscribeToContentComponent=e.contentComponent.subscribe(()=>{this.setState(e=>e.hasContentComponentInitialized?e:{hasContentComponentInitialized:!0}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent()})),e.createNodeViews(),this.initialized=!0}}componentWillUnmount(){let e=this.props.editor;if(!e||(this.initialized=!1,e.isDestroyed||e.view.setProps({nodeViews:{}}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent(),e.contentComponent=null,!e.options.element.firstChild))return;let t=document.createElement("div");t.append(...e.options.element.childNodes),e.setOptions({element:t})}render(){let{editor:e,innerRef:t,...n}=this.props;return a.createElement(a.Fragment,null,a.createElement("div",{ref:d(t,this.editorContentRef),...n}),(null==e?void 0:e.contentComponent)&&a.createElement(p,{contentComponent:e.contentComponent}))}}let m=(0,a.forwardRef)((e,t)=>{let n=a.useMemo(()=>Math.floor(***********Math.random()).toString(),[e.editor]);return a.createElement(f,{key:n,innerRef:t,...e})}),g=a.memo(m);var E=(r=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;if(Array.isArray(t)){if((r=t.length)!=n.length)return!1;for(s=r;0!=s--;)if(!e(t[s],n[s]))return!1;return!0}if(t instanceof Map&&n instanceof Map){if(t.size!==n.size)return!1;for(s of t.entries())if(!n.has(s[0]))return!1;for(s of t.entries())if(!e(s[1],n.get(s[0])))return!1;return!0}if(t instanceof Set&&n instanceof Set){if(t.size!==n.size)return!1;for(s of t.entries())if(!n.has(s[0]))return!1;return!0}if(ArrayBuffer.isView(t)&&ArrayBuffer.isView(n)){if((r=t.length)!=n.length)return!1;for(s=r;0!=s--;)if(t[s]!==n[s])return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((r=(i=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(s=r;0!=s--;)if(!Object.prototype.hasOwnProperty.call(n,i[s]))return!1;for(s=r;0!=s--;){var r,s,i,a=i[s];if(("_owner"!==a||!t.$$typeof)&&!e(t[a],n[a]))return!1}return!0}return t!=t&&n!=n}).__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r,T={exports:{}},A={};T.exports=function(){if(i)return A;i=1;var e="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},t=h.useSyncExternalStore,n=a.useRef,r=a.useEffect,s=a.useMemo,o=a.useDebugValue;return A.useSyncExternalStoreWithSelector=function(i,a,l,c,u){var h=n(null);if(null===h.current){var d={hasValue:!1,value:null};h.current=d}else d=h.current;var p=t(i,(h=s(function(){function t(t){if(!s){if(s=!0,n=t,t=c(t),void 0!==u&&d.hasValue){var i=d.value;if(u(i,t))return r=i}return r=t}if(i=r,e(n,t))return i;var a=c(t);return void 0!==u&&u(i,a)?i:(n=t,r=a)}var n,r,s=!1,i=void 0===l?null:l;return[function(){return t(a())},null===i?void 0:function(){return t(i())}]},[a,l,c,u]))[0],h[1]);return r(function(){d.hasValue=!0,d.value=p},[p]),o(p),p},A}();var _=T.exports;let S="undefined"!=typeof window?a.useLayoutEffect:a.useEffect;class y{constructor(e){this.transactionNumber=0,this.lastTransactionNumber=0,this.subscribers=new Set,this.editor=e,this.lastSnapshot={editor:e,transactionNumber:0},this.getSnapshot=this.getSnapshot.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.watch=this.watch.bind(this),this.subscribe=this.subscribe.bind(this)}getSnapshot(){return this.transactionNumber===this.lastTransactionNumber||(this.lastTransactionNumber=this.transactionNumber,this.lastSnapshot={editor:this.editor,transactionNumber:this.transactionNumber}),this.lastSnapshot}getServerSnapshot(){return{editor:null,transactionNumber:0}}subscribe(e){return this.subscribers.add(e),()=>{this.subscribers.delete(e)}}watch(e){if(this.editor=e,this.editor){let e=()=>{this.transactionNumber+=1,this.subscribers.forEach(e=>e())},t=this.editor;return t.on("transaction",e),()=>{t.off("transaction",e)}}}}let k="undefined"==typeof window,C=k||!!("undefined"!=typeof window&&window.next);class N{constructor(e){this.editor=null,this.subscriptions=new Set,this.isComponentMounted=!1,this.previousDeps=null,this.instanceId="",this.options=e,this.subscriptions=new Set,this.setEditor(this.getInitialEditor()),this.scheduleDestroy(),this.getEditor=this.getEditor.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.subscribe=this.subscribe.bind(this),this.refreshEditorInstance=this.refreshEditorInstance.bind(this),this.scheduleDestroy=this.scheduleDestroy.bind(this),this.onRender=this.onRender.bind(this),this.createEditor=this.createEditor.bind(this)}setEditor(e){this.editor=e,this.instanceId=Math.random().toString(36).slice(2,9),this.subscriptions.forEach(e=>e())}getInitialEditor(){return void 0===this.options.current.immediatelyRender?k||C?null:this.createEditor():(this.options.current.immediatelyRender,this.options.current.immediatelyRender?this.createEditor():null)}createEditor(){let e={...this.options.current,onBeforeCreate:(...e)=>{var t,n;return null===(n=(t=this.options.current).onBeforeCreate)||void 0===n?void 0:n.call(t,...e)},onBlur:(...e)=>{var t,n;return null===(n=(t=this.options.current).onBlur)||void 0===n?void 0:n.call(t,...e)},onCreate:(...e)=>{var t,n;return null===(n=(t=this.options.current).onCreate)||void 0===n?void 0:n.call(t,...e)},onDestroy:(...e)=>{var t,n;return null===(n=(t=this.options.current).onDestroy)||void 0===n?void 0:n.call(t,...e)},onFocus:(...e)=>{var t,n;return null===(n=(t=this.options.current).onFocus)||void 0===n?void 0:n.call(t,...e)},onSelectionUpdate:(...e)=>{var t,n;return null===(n=(t=this.options.current).onSelectionUpdate)||void 0===n?void 0:n.call(t,...e)},onTransaction:(...e)=>{var t,n;return null===(n=(t=this.options.current).onTransaction)||void 0===n?void 0:n.call(t,...e)},onUpdate:(...e)=>{var t,n;return null===(n=(t=this.options.current).onUpdate)||void 0===n?void 0:n.call(t,...e)},onContentError:(...e)=>{var t,n;return null===(n=(t=this.options.current).onContentError)||void 0===n?void 0:n.call(t,...e)},onDrop:(...e)=>{var t,n;return null===(n=(t=this.options.current).onDrop)||void 0===n?void 0:n.call(t,...e)},onPaste:(...e)=>{var t,n;return null===(n=(t=this.options.current).onPaste)||void 0===n?void 0:n.call(t,...e)}};return new l.ML(e)}getEditor(){return this.editor}getServerSnapshot(){return null}subscribe(e){return this.subscriptions.add(e),()=>{this.subscriptions.delete(e)}}static compareOptions(e,t){return Object.keys(e).every(n=>!!["onCreate","onBeforeCreate","onDestroy","onUpdate","onTransaction","onFocus","onBlur","onSelectionUpdate","onContentError","onDrop","onPaste"].includes(n)||("extensions"===n&&e.extensions&&t.extensions?e.extensions.length===t.extensions.length&&e.extensions.every((e,n)=>{var r;return e===(null===(r=t.extensions)||void 0===r?void 0:r[n])}):e[n]===t[n]))}onRender(e){return()=>(this.isComponentMounted=!0,clearTimeout(this.scheduledDestructionTimeout),this.editor&&!this.editor.isDestroyed&&0===e.length?N.compareOptions(this.options.current,this.editor.options)||this.editor.setOptions({...this.options.current,editable:this.editor.isEditable}):this.refreshEditorInstance(e),()=>{this.isComponentMounted=!1,this.scheduleDestroy()})}refreshEditorInstance(e){if(this.editor&&!this.editor.isDestroyed){if(null===this.previousDeps){this.previousDeps=e;return}if(this.previousDeps.length===e.length&&this.previousDeps.every((t,n)=>t===e[n]))return}this.editor&&!this.editor.isDestroyed&&this.editor.destroy(),this.setEditor(this.createEditor()),this.previousDeps=e}scheduleDestroy(){let e=this.instanceId,t=this.editor;this.scheduledDestructionTimeout=setTimeout(()=>{if(this.isComponentMounted&&this.instanceId===e){t&&t.setOptions(this.options.current);return}t&&!t.isDestroyed&&(t.destroy(),this.instanceId===e&&this.setEditor(null))},1)}}function I(e={},t=[]){let n=(0,a.useRef)(e);n.current=e;let[r]=(0,a.useState)(()=>new N(n)),s=h.useSyncExternalStore(r.subscribe,r.getEditor,r.getServerSnapshot);return(0,a.useDebugValue)(s),(0,a.useEffect)(r.onRender(t)),!function(e){var t;let[n]=(0,a.useState)(()=>new y(e.editor)),r=_.useSyncExternalStoreWithSelector(n.subscribe,n.getSnapshot,n.getServerSnapshot,e.selector,null!==(t=e.equalityFn)&&void 0!==t?t:E);S(()=>n.watch(e.editor),[e.editor,n]),(0,a.useDebugValue)(r)}({editor:s,selector:({transactionNumber:t})=>!1===e.shouldRerenderOnTransaction?null:e.immediatelyRender&&0===t?0:t+1}),s}(0,a.createContext)({editor:null}).Consumer;let b=(0,a.createContext)({onDragStart:void 0}),O=()=>(0,a.useContext)(b);a.forwardRef((e,t)=>{let{onDragStart:n}=O(),r=e.as||"div";return a.createElement(r,{...e,ref:t,"data-node-view-wrapper":"",onDragStart:n,style:{whiteSpace:"normal",...e.style}})})},63141:function(e,t,n){"use strict";n.d(t,{Z:function(){return ep}});var r=n(81816);let s=/^\s*>\s$/,i=r.NB.create({name:"blockquote",addOptions:()=>({HTMLAttributes:{}}),content:"block+",group:"block",defining:!0,parseHTML:()=>[{tag:"blockquote"}],renderHTML({HTMLAttributes:e}){return["blockquote",(0,r.P1)(this.options.HTMLAttributes,e),0]},addCommands(){return{setBlockquote:()=>({commands:e})=>e.wrapIn(this.name),toggleBlockquote:()=>({commands:e})=>e.toggleWrap(this.name),unsetBlockquote:()=>({commands:e})=>e.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[(0,r.S0)({find:s,type:this.type})]}}),a=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,o=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,l=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,c=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,u=r.vc.create({name:"bold",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:e=>"normal"!==e.style.fontWeight&&null},{style:"font-weight=400",clearMark:e=>e.type.name===this.name},{style:"font-weight",getAttrs:e=>/^(bold(er)?|[5-9]\d{2,})$/.test(e)&&null}]},renderHTML({HTMLAttributes:e}){return["strong",(0,r.P1)(this.options.HTMLAttributes,e),0]},addCommands(){return{setBold:()=>({commands:e})=>e.setMark(this.name),toggleBold:()=>({commands:e})=>e.toggleMark(this.name),unsetBold:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[(0,r.Cf)({find:a,type:this.type}),(0,r.Cf)({find:l,type:this.type})]},addPasteRules(){return[(0,r.K9)({find:o,type:this.type}),(0,r.K9)({find:c,type:this.type})]}}),h="textStyle",d=/^\s*([-+*])\s$/,p=r.NB.create({name:"bulletList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML:()=>[{tag:"ul"}],renderHTML({HTMLAttributes:e}){return["ul",(0,r.P1)(this.options.HTMLAttributes,e),0]},addCommands(){return{toggleBulletList:()=>({commands:e,chain:t})=>this.options.keepAttributes?t().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(h)).run():e.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let e=(0,r.S0)({find:d,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(e=(0,r.S0)({find:d,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(h),editor:this.editor})),[e]}});var f=n(35844),m=n(8051);let g=r.NB.create({name:"doc",topNode:!0,content:"block+"});var E=n(28201),T=n(67120);class A{constructor(e,t){var n;this.editorView=e,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=null!==(n=t.width)&&void 0!==n?n:1,this.color=!1===t.color?void 0:t.color||"black",this.class=t.class,this.handlers=["dragover","dragend","drop","dragleave"].map(t=>{let n=e=>{this[t](e)};return e.dom.addEventListener(t,n),{name:t,handler:n}})}destroy(){this.handlers.forEach(({name:e,handler:t})=>this.editorView.dom.removeEventListener(e,t))}update(e,t){null!=this.cursorPos&&t.doc!=e.state.doc&&(this.cursorPos>e.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(e){e!=this.cursorPos&&(this.cursorPos=e,null==e?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let e,t,n=this.editorView.state.doc.resolve(this.cursorPos),r=!n.parent.inlineContent,s,i=this.editorView.dom,a=i.getBoundingClientRect(),o=a.width/i.offsetWidth,l=a.height/i.offsetHeight;if(r){let e=n.nodeBefore,t=n.nodeAfter;if(e||t){let n=this.editorView.nodeDOM(this.cursorPos-(e?e.nodeSize:0));if(n){let r=n.getBoundingClientRect(),i=e?r.bottom:r.top;e&&t&&(i=(i+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2);let a=this.width/2*l;s={left:r.left,right:r.right,top:i-a,bottom:i+a}}}}if(!s){let e=this.editorView.coordsAtPos(this.cursorPos),t=this.width/2*o;s={left:e.left-t,right:e.left+t,top:e.top,bottom:e.bottom}}let c=this.editorView.dom.offsetParent;if(!this.element&&(this.element=c.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",r),this.element.classList.toggle("prosemirror-dropcursor-inline",!r),c&&(c!=document.body||"static"!=getComputedStyle(c).position)){let n=c.getBoundingClientRect(),r=n.width/c.offsetWidth,s=n.height/c.offsetHeight;e=n.left-c.scrollLeft*r,t=n.top-c.scrollTop*s}else e=-pageXOffset,t=-pageYOffset;this.element.style.left=(s.left-e)/o+"px",this.element.style.top=(s.top-t)/l+"px",this.element.style.width=(s.right-s.left)/o+"px",this.element.style.height=(s.bottom-s.top)/l+"px"}scheduleRemoval(e){clearTimeout(this.timeout),this.timeout=setTimeout(()=>this.setCursor(null),e)}dragover(e){if(!this.editorView.editable)return;let t=this.editorView.posAtCoords({left:e.clientX,top:e.clientY}),n=t&&t.inside>=0&&this.editorView.state.doc.nodeAt(t.inside),r=n&&n.type.spec.disableDropCursor,s="function"==typeof r?r(this.editorView,t,e):r;if(t&&!s){let e=t.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let t=(0,T.nj)(this.editorView.state.doc,e,this.editorView.dragging.slice);null!=t&&(e=t)}this.setCursor(e),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(e){this.editorView.dom.contains(e.relatedTarget)||this.setCursor(null)}}let _=r.hj.create({name:"dropCursor",addOptions:()=>({color:"currentColor",width:1,class:void 0}),addProseMirrorPlugins(){return[function(e={}){return new E.Sy({view:t=>new A(t,e)})}(this.options)]}});var S=n(99007),y=n(77247),k=n(51058);class C extends E.Y1{constructor(e){super(e,e)}map(e,t){let n=e.resolve(t.map(this.head));return C.valid(n)?new C(n):E.Y1.near(n)}content(){return y.p2.empty}eq(e){return e instanceof C&&e.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(e,t){if("number"!=typeof t.pos)throw RangeError("Invalid input for GapCursor.fromJSON");return new C(e.resolve(t.pos))}getBookmark(){return new N(this.anchor)}static valid(e){let t=e.parent;if(t.isTextblock||!function(e){for(let t=e.depth;t>=0;t--){let n=e.index(t),r=e.node(t);if(0==n){if(r.type.spec.isolating)return!0;continue}for(let e=r.child(n-1);;e=e.lastChild){if(0==e.childCount&&!e.inlineContent||e.isAtom||e.type.spec.isolating)return!0;if(e.inlineContent)return!1}}return!0}(e)||!function(e){for(let t=e.depth;t>=0;t--){let n=e.indexAfter(t),r=e.node(t);if(n==r.childCount){if(r.type.spec.isolating)return!0;continue}for(let e=r.child(n);;e=e.firstChild){if(0==e.childCount&&!e.inlineContent||e.isAtom||e.type.spec.isolating)return!0;if(e.inlineContent)return!1}}return!0}(e))return!1;let n=t.type.spec.allowGapCursor;if(null!=n)return n;let r=t.contentMatchAt(e.index()).defaultType;return r&&r.isTextblock}static findGapCursorFrom(e,t,n=!1){e:for(;;){if(!n&&C.valid(e))return e;let r=e.pos,s=null;for(let n=e.depth;;n--){let i=e.node(n);if(t>0?e.indexAfter(n)<i.childCount:e.index(n)>0){s=i.child(t>0?e.indexAfter(n):e.index(n)-1);break}if(0==n)return null;r+=t;let a=e.doc.resolve(r);if(C.valid(a))return a}for(;;){let i=t>0?s.firstChild:s.lastChild;if(!i){if(s.isAtom&&!s.isText&&!E.qv.isSelectable(s)){e=e.doc.resolve(r+s.nodeSize*t),n=!1;continue e}break}s=i,r+=t;let a=e.doc.resolve(r);if(C.valid(a))return a}return null}}}C.prototype.visible=!1,C.findFrom=C.findGapCursorFrom,E.Y1.jsonID("gapcursor",C);class N{constructor(e){this.pos=e}map(e){return new N(e.map(this.pos))}resolve(e){let t=e.resolve(this.pos);return C.valid(t)?new C(t):E.Y1.near(t)}}let I=(0,S.$)({ArrowLeft:b("horiz",-1),ArrowRight:b("horiz",1),ArrowUp:b("vert",-1),ArrowDown:b("vert",1)});function b(e,t){let n="vert"==e?t>0?"down":"up":t>0?"right":"left";return function(e,r,s){let i=e.selection,a=t>0?i.$to:i.$from,o=i.empty;if(i instanceof E.Bs){if(!s.endOfTextblock(n)||0==a.depth)return!1;o=!1,a=e.doc.resolve(t>0?a.after():a.before())}let l=C.findGapCursorFrom(a,t,o);return!!l&&(r&&r(e.tr.setSelection(new C(l))),!0)}}function O(e,t,n){if(!e||!e.editable)return!1;let r=e.state.doc.resolve(t);if(!C.valid(r))return!1;let s=e.posAtCoords({left:n.clientX,top:n.clientY});return!(s&&s.inside>-1&&E.qv.isSelectable(e.state.doc.nodeAt(s.inside)))&&(e.dispatch(e.state.tr.setSelection(new C(r))),!0)}function D(e,t){if("insertCompositionText"!=t.inputType||!(e.state.selection instanceof C))return!1;let{$from:n}=e.state.selection,r=n.parent.contentMatchAt(n.index()).findWrapping(e.state.schema.nodes.text);if(!r)return!1;let s=y.HY.empty;for(let e=r.length-1;e>=0;e--)s=y.HY.from(r[e].createAndFill(null,s));let i=e.state.tr.replace(n.pos,n.pos,new y.p2(s,0,0));return i.setSelection(E.Bs.near(i.doc.resolve(n.pos+1))),e.dispatch(i),!1}function R(e){if(!(e.selection instanceof C))return null;let t=document.createElement("div");return t.className="ProseMirror-gapcursor",k.EH.create(e.doc,[k.p.widget(e.selection.head,t,{key:"gapcursor"})])}let M=r.hj.create({name:"gapCursor",addProseMirrorPlugins:()=>[new E.Sy({props:{decorations:R,createSelectionBetween:(e,t,n)=>t.pos==n.pos&&C.valid(n)?new C(n):null,handleClick:O,handleKeyDown:I,handleDOMEvents:{beforeinput:D}}})],extendNodeSchema(e){var t;let n={name:e.name,options:e.options,storage:e.storage};return{allowGapCursor:null!==(t=(0,r.nU)((0,r.Nl)(e,"allowGapCursor",n)))&&void 0!==t?t:null}}}),v=r.NB.create({name:"hardBreak",addOptions:()=>({keepMarks:!0,HTMLAttributes:{}}),inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML:()=>[{tag:"br"}],renderHTML({HTMLAttributes:e}){return["br",(0,r.P1)(this.options.HTMLAttributes,e)]},renderText:()=>"\n",addCommands(){return{setHardBreak:()=>({commands:e,chain:t,state:n,editor:r})=>e.first([()=>e.exitCode(),()=>e.command(()=>{let{selection:e,storedMarks:s}=n;if(e.$from.parent.type.spec.isolating)return!1;let{keepMarks:i}=this.options,{splittableMarks:a}=r.extensionManager,o=s||e.$to.parentOffset&&e.$from.marks();return t().insertContent({type:this.name}).command(({tr:e,dispatch:t})=>{if(t&&o&&i){let t=o.filter(e=>a.includes(e.type.name));e.ensureMarks(t)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),L=r.NB.create({name:"heading",addOptions:()=>({levels:[1,2,3,4,5,6],HTMLAttributes:{}}),content:"inline*",group:"block",defining:!0,addAttributes:()=>({level:{default:1,rendered:!1}}),parseHTML(){return this.options.levels.map(e=>({tag:`h${e}`,attrs:{level:e}}))},renderHTML({node:e,HTMLAttributes:t}){let n=this.options.levels.includes(e.attrs.level)?e.attrs.level:this.options.levels[0];return[`h${n}`,(0,r.P1)(this.options.HTMLAttributes,t),0]},addCommands(){return{setHeading:e=>({commands:t})=>!!this.options.levels.includes(e.level)&&t.setNode(this.name,e),toggleHeading:e=>({commands:t})=>!!this.options.levels.includes(e.level)&&t.toggleNode(this.name,"paragraph",e)}},addKeyboardShortcuts(){return this.options.levels.reduce((e,t)=>({...e,[`Mod-Alt-${t}`]:()=>this.editor.commands.toggleHeading({level:t})}),{})},addInputRules(){return this.options.levels.map(e=>(0,r.zK)({find:RegExp(`^(#{${Math.min(...this.options.levels)},${e}})\\s$`),type:this.type,getAttributes:{level:e}}))}});var w=function(){};w.prototype.append=function(e){return e.length?(e=w.from(e),!this.length&&e||e.length<200&&this.leafAppend(e)||this.length<200&&e.leafPrepend(this)||this.appendInner(e)):this},w.prototype.prepend=function(e){return e.length?w.from(e).append(this):this},w.prototype.appendInner=function(e){return new x(this,e)},w.prototype.slice=function(e,t){return(void 0===e&&(e=0),void 0===t&&(t=this.length),e>=t)?w.empty:this.sliceInner(Math.max(0,e),Math.min(this.length,t))},w.prototype.get=function(e){if(!(e<0)&&!(e>=this.length))return this.getInner(e)},w.prototype.forEach=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=this.length),t<=n?this.forEachInner(e,t,n,0):this.forEachInvertedInner(e,t,n,0)},w.prototype.map=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=this.length);var r=[];return this.forEach(function(t,n){return r.push(e(t,n))},t,n),r},w.from=function(e){return e instanceof w?e:e&&e.length?new P(e):w.empty};var P=function(e){function t(t){e.call(this),this.values=t}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var n={length:{configurable:!0},depth:{configurable:!0}};return t.prototype.flatten=function(){return this.values},t.prototype.sliceInner=function(e,n){return 0==e&&n==this.length?this:new t(this.values.slice(e,n))},t.prototype.getInner=function(e){return this.values[e]},t.prototype.forEachInner=function(e,t,n,r){for(var s=t;s<n;s++)if(!1===e(this.values[s],r+s))return!1},t.prototype.forEachInvertedInner=function(e,t,n,r){for(var s=t-1;s>=n;s--)if(!1===e(this.values[s],r+s))return!1},t.prototype.leafAppend=function(e){if(this.length+e.length<=200)return new t(this.values.concat(e.flatten()))},t.prototype.leafPrepend=function(e){if(this.length+e.length<=200)return new t(e.flatten().concat(this.values))},n.length.get=function(){return this.values.length},n.depth.get=function(){return 0},Object.defineProperties(t.prototype,n),t}(w);w.empty=new P([]);var x=function(e){function t(t,n){e.call(this),this.left=t,this.right=n,this.length=t.length+n.length,this.depth=Math.max(t.depth,n.depth)+1}return e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t,t.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},t.prototype.getInner=function(e){return e<this.left.length?this.left.get(e):this.right.get(e-this.left.length)},t.prototype.forEachInner=function(e,t,n,r){var s=this.left.length;if(t<s&&!1===this.left.forEachInner(e,t,Math.min(n,s),r)||n>s&&!1===this.right.forEachInner(e,Math.max(t-s,0),Math.min(this.length,n)-s,r+s))return!1},t.prototype.forEachInvertedInner=function(e,t,n,r){var s=this.left.length;if(t>s&&!1===this.right.forEachInvertedInner(e,t-s,Math.max(n,s)-s,r+s)||n<s&&!1===this.left.forEachInvertedInner(e,Math.min(t,s),n,r))return!1},t.prototype.sliceInner=function(e,t){if(0==e&&t==this.length)return this;var n=this.left.length;return t<=n?this.left.slice(e,t):e>=n?this.right.slice(e-n,t-n):this.left.slice(e,n).append(this.right.slice(0,t-n))},t.prototype.leafAppend=function(e){var n=this.right.leafAppend(e);if(n)return new t(this.left,n)},t.prototype.leafPrepend=function(e){var n=this.left.leafPrepend(e);if(n)return new t(n,this.right)},t.prototype.appendInner=function(e){return this.left.depth>=Math.max(this.right.depth,e.depth)+1?new t(this.left,new t(this.right,e)):new t(this,e)},t}(w);class F{constructor(e,t){this.items=e,this.eventCount=t}popEvent(e,t){let n,r,s,i;if(0==this.eventCount)return null;let a=this.items.length;for(;;a--)if(this.items.get(a-1).selection){--a;break}t&&(r=(n=this.remapping(a,this.items.length)).maps.length);let o=e.tr,l=[],c=[];return this.items.forEach((e,t)=>{if(!e.step){n||(r=(n=this.remapping(a,t+1)).maps.length),r--,c.push(e);return}if(n){c.push(new B(e.map));let t=e.step.map(n.slice(r)),s;t&&o.maybeStep(t).doc&&(s=o.mapping.maps[o.mapping.maps.length-1],l.push(new B(s,void 0,void 0,l.length+c.length))),r--,s&&n.appendMap(s,r)}else o.maybeStep(e.step);if(e.selection)return s=n?e.selection.map(n.slice(r)):e.selection,i=new F(this.items.slice(0,a).append(c.reverse().concat(l)),this.eventCount-1),!1},this.items.length,0),{remaining:i,transform:o,selection:s}}addTransform(e,t,n,r){var s,i;let a,o=[],l=this.eventCount,c=this.items,u=!r&&c.length?c.get(c.length-1):null;for(let n=0;n<e.steps.length;n++){let s=e.steps[n].invert(e.docs[n]),i=new B(e.mapping.maps[n],s,t),a;(a=u&&u.merge(i))&&(i=a,n?o.pop():c=c.slice(0,c.length-1)),o.push(i),t&&(l++,t=void 0),r||(u=i)}let h=l-n.depth;return h>U&&(s=c,i=h,s.forEach((e,t)=>{if(e.selection&&0==i--)return a=t,!1}),c=s.slice(a),l-=h),new F(c.append(o),l)}remapping(e,t){let n=new T.vs;return this.items.forEach((t,r)=>{let s=null!=t.mirrorOffset&&r-t.mirrorOffset>=e?n.maps.length-t.mirrorOffset:void 0;n.appendMap(t.map,s)},e,t),n}addMaps(e){return 0==this.eventCount?this:new F(this.items.append(e.map(e=>new B(e))),this.eventCount)}rebased(e,t){if(!this.eventCount)return this;let n=[],r=Math.max(0,this.items.length-t),s=e.mapping,i=e.steps.length,a=this.eventCount;this.items.forEach(e=>{e.selection&&a--},r);let o=t;this.items.forEach(t=>{let r=s.getMirror(--o);if(null==r)return;i=Math.min(i,r);let l=s.maps[r];if(t.step){let i=e.steps[r].invert(e.docs[r]),c=t.selection&&t.selection.map(s.slice(o+1,r));c&&a++,n.push(new B(l,i,c))}else n.push(new B(l))},r);let l=[];for(let e=t;e<i;e++)l.push(new B(s.maps[e]));let c=new F(this.items.slice(0,r).append(l).append(n),a);return c.emptyItemCount()>500&&(c=c.compress(this.items.length-n.length)),c}emptyItemCount(){let e=0;return this.items.forEach(t=>{!t.step&&e++}),e}compress(e=this.items.length){let t=this.remapping(0,e),n=t.maps.length,r=[],s=0;return this.items.forEach((i,a)=>{if(a>=e)r.push(i),i.selection&&s++;else if(i.step){let e=i.step.map(t.slice(n)),a=e&&e.getMap();if(n--,a&&t.appendMap(a,n),e){let o=i.selection&&i.selection.map(t.slice(n));o&&s++;let l=new B(a.invert(),e,o),c,u=r.length-1;(c=r.length&&r[u].merge(l))?r[u]=c:r.push(l)}}else i.map&&n--},this.items.length,0),new F(w.from(r.reverse()),s)}}F.empty=new F(w.empty,0);class B{constructor(e,t,n,r){this.map=e,this.step=t,this.selection=n,this.mirrorOffset=r}merge(e){if(this.step&&e.step&&!e.selection){let t=e.step.merge(this.step);if(t)return new B(t.getMap().invert(),t,this.selection)}}}class H{constructor(e,t,n,r,s){this.done=e,this.undone=t,this.prevRanges=n,this.prevTime=r,this.prevComposition=s}}let U=20;function G(e){let t=[];for(let n=e.length-1;n>=0&&0==t.length;n--)e[n].forEach((e,n,r,s)=>t.push(r,s));return t}function Y(e,t){if(!e)return null;let n=[];for(let r=0;r<e.length;r+=2){let s=t.map(e[r],1),i=t.map(e[r+1],-1);s<=i&&n.push(s,i)}return n}let z=!1,W=null;function q(e){let t=e.plugins;if(W!=t){z=!1,W=t;for(let e=0;e<t.length;e++)if(t[e].spec.historyPreserveItems){z=!0;break}}return z}let $=new E.H$("history"),j=new E.H$("closeHistory");function V(e,t){return(n,r)=>{let s=$.getState(n);if(!s||0==(e?s.undone:s.done).eventCount)return!1;if(r){let i=function(e,t,n){let r=q(t),s=$.get(t).spec.config,i=(n?e.undone:e.done).popEvent(t,r);if(!i)return null;let a=i.selection.resolve(i.transform.doc),o=(n?e.done:e.undone).addTransform(i.transform,t.selection.getBookmark(),s,r),l=new H(n?o:i.remaining,n?i.remaining:o,null,0,-1);return i.transform.setSelection(a).setMeta($,{redo:n,historyState:l})}(s,n,e);i&&r(t?i.scrollIntoView():i)}return!0}}let K=V(!1,!0),Q=V(!0,!0);V(!1,!1),V(!0,!1);let X=r.hj.create({name:"history",addOptions:()=>({depth:100,newGroupDelay:500}),addCommands:()=>({undo:()=>({state:e,dispatch:t})=>K(e,t),redo:()=>({state:e,dispatch:t})=>Q(e,t)}),addProseMirrorPlugins(){return[function(e={}){return e={depth:e.depth||100,newGroupDelay:e.newGroupDelay||500},new E.Sy({key:$,state:{init:()=>new H(F.empty,F.empty,null,0,-1),apply:(t,n,r)=>(function(e,t,n,r){let s=n.getMeta($),i;if(s)return s.historyState;n.getMeta(j)&&(e=new H(e.done,e.undone,null,0,-1));let a=n.getMeta("appendedTransaction");if(0==n.steps.length)return e;if(a&&a.getMeta($))return a.getMeta($).redo?new H(e.done.addTransform(n,void 0,r,q(t)),e.undone,G(n.mapping.maps),e.prevTime,e.prevComposition):new H(e.done,e.undone.addTransform(n,void 0,r,q(t)),null,e.prevTime,e.prevComposition);if(!1===n.getMeta("addToHistory")||a&&!1===a.getMeta("addToHistory"))return(i=n.getMeta("rebased"))?new H(e.done.rebased(n,i),e.undone.rebased(n,i),Y(e.prevRanges,n.mapping),e.prevTime,e.prevComposition):new H(e.done.addMaps(n.mapping.maps),e.undone.addMaps(n.mapping.maps),Y(e.prevRanges,n.mapping),e.prevTime,e.prevComposition);{let s=n.getMeta("composition"),i=0==e.prevTime||!a&&e.prevComposition!=s&&(e.prevTime<(n.time||0)-r.newGroupDelay||!function(e,t){if(!t)return!1;if(!e.docChanged)return!0;let n=!1;return e.mapping.maps[0].forEach((e,r)=>{for(let s=0;s<t.length;s+=2)e<=t[s+1]&&r>=t[s]&&(n=!0)}),n}(n,e.prevRanges)),o=a?Y(e.prevRanges,n.mapping):G(n.mapping.maps);return new H(e.done.addTransform(n,i?t.selection.getBookmark():void 0,r,q(t)),F.empty,o,n.time,null==s?e.prevComposition:s)}})(n,r,t,e)},config:e,props:{handleDOMEvents:{beforeinput(e,t){let n=t.inputType,r="historyUndo"==n?K:"historyRedo"==n?Q:null;return!!r&&(t.preventDefault(),r(e.state,e.dispatch))}}}})}(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),J=r.NB.create({name:"horizontalRule",addOptions:()=>({HTMLAttributes:{}}),group:"block",parseHTML:()=>[{tag:"hr"}],renderHTML({HTMLAttributes:e}){return["hr",(0,r.P1)(this.options.HTMLAttributes,e)]},addCommands(){return{setHorizontalRule:()=>({chain:e,state:t})=>{if(!(0,r.FZ)(t,t.schema.nodes[this.name]))return!1;let{selection:n}=t,{$from:s,$to:i}=n,a=e();return 0===s.parentOffset?a.insertContentAt({from:Math.max(s.pos-1,0),to:i.pos},{type:this.name}):(0,r.EG)(n)?a.insertContentAt(i.pos,{type:this.name}):a.insertContent({type:this.name}),a.command(({tr:e,dispatch:t})=>{var n;if(t){let{$to:t}=e.selection,r=t.end();if(t.nodeAfter)t.nodeAfter.isTextblock?e.setSelection(E.Bs.create(e.doc,t.pos+1)):t.nodeAfter.isBlock?e.setSelection(E.qv.create(e.doc,t.pos)):e.setSelection(E.Bs.create(e.doc,t.pos));else{let s=null===(n=t.parent.type.contentMatch.defaultType)||void 0===n?void 0:n.create();s&&(e.insert(r,s),e.setSelection(E.Bs.create(e.doc,r+1)))}e.scrollIntoView()}return!0}).run()}}},addInputRules(){return[(0,r.x2)({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}}),Z=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,ee=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,et=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,en=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,er=r.vc.create({name:"italic",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:e=>"normal"!==e.style.fontStyle&&null},{style:"font-style=normal",clearMark:e=>e.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:e}){return["em",(0,r.P1)(this.options.HTMLAttributes,e),0]},addCommands(){return{setItalic:()=>({commands:e})=>e.setMark(this.name),toggleItalic:()=>({commands:e})=>e.toggleMark(this.name),unsetItalic:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[(0,r.Cf)({find:Z,type:this.type}),(0,r.Cf)({find:et,type:this.type})]},addPasteRules(){return[(0,r.K9)({find:ee,type:this.type}),(0,r.K9)({find:en,type:this.type})]}}),es=r.NB.create({name:"listItem",addOptions:()=>({HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}),content:"paragraph block*",defining:!0,parseHTML:()=>[{tag:"li"}],renderHTML({HTMLAttributes:e}){return["li",(0,r.P1)(this.options.HTMLAttributes,e),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),ei="textStyle",ea=/^(\d+)\.\s$/,eo=r.NB.create({name:"orderedList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes:()=>({start:{default:1,parseHTML:e=>e.hasAttribute("start")?parseInt(e.getAttribute("start")||"",10):1},type:{default:null,parseHTML:e=>e.getAttribute("type")}}),parseHTML:()=>[{tag:"ol"}],renderHTML({HTMLAttributes:e}){let{start:t,...n}=e;return 1===t?["ol",(0,r.P1)(this.options.HTMLAttributes,n),0]:["ol",(0,r.P1)(this.options.HTMLAttributes,e),0]},addCommands(){return{toggleOrderedList:()=>({commands:e,chain:t})=>this.options.keepAttributes?t().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(ei)).run():e.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let e=(0,r.S0)({find:ea,type:this.type,getAttributes:e=>({start:+e[1]}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(e=(0,r.S0)({find:ea,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:e=>({start:+e[1],...this.editor.getAttributes(ei)}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1],editor:this.editor})),[e]}}),el=r.NB.create({name:"paragraph",priority:1e3,addOptions:()=>({HTMLAttributes:{}}),group:"block",content:"inline*",parseHTML:()=>[{tag:"p"}],renderHTML({HTMLAttributes:e}){return["p",(0,r.P1)(this.options.HTMLAttributes,e),0]},addCommands(){return{setParagraph:()=>({commands:e})=>e.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),ec=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,eu=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,eh=r.vc.create({name:"strike",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:e=>!!e.includes("line-through")&&{}}],renderHTML({HTMLAttributes:e}){return["s",(0,r.P1)(this.options.HTMLAttributes,e),0]},addCommands(){return{setStrike:()=>({commands:e})=>e.setMark(this.name),toggleStrike:()=>({commands:e})=>e.toggleMark(this.name),unsetStrike:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[(0,r.Cf)({find:ec,type:this.type})]},addPasteRules(){return[(0,r.K9)({find:eu,type:this.type})]}}),ed=r.NB.create({name:"text",group:"inline"}),ep=r.hj.create({name:"starterKit",addExtensions(){let e=[];return!1!==this.options.bold&&e.push(u.configure(this.options.bold)),!1!==this.options.blockquote&&e.push(i.configure(this.options.blockquote)),!1!==this.options.bulletList&&e.push(p.configure(this.options.bulletList)),!1!==this.options.code&&e.push(f.EK.configure(this.options.code)),!1!==this.options.codeBlock&&e.push(m.dn.configure(this.options.codeBlock)),!1!==this.options.document&&e.push(g.configure(this.options.document)),!1!==this.options.dropcursor&&e.push(_.configure(this.options.dropcursor)),!1!==this.options.gapcursor&&e.push(M.configure(this.options.gapcursor)),!1!==this.options.hardBreak&&e.push(v.configure(this.options.hardBreak)),!1!==this.options.heading&&e.push(L.configure(this.options.heading)),!1!==this.options.history&&e.push(X.configure(this.options.history)),!1!==this.options.horizontalRule&&e.push(J.configure(this.options.horizontalRule)),!1!==this.options.italic&&e.push(er.configure(this.options.italic)),!1!==this.options.listItem&&e.push(es.configure(this.options.listItem)),!1!==this.options.orderedList&&e.push(eo.configure(this.options.orderedList)),!1!==this.options.paragraph&&e.push(el.configure(this.options.paragraph)),!1!==this.options.strike&&e.push(eh.configure(this.options.strike)),!1!==this.options.text&&e.push(ed.configure(this.options.text)),e}})},56830:function(e,t,n){"use strict";function r(e,t){return e instanceof Date?new e.constructor(t):new Date(t)}n.d(t,{L:function(){return r}})},82994:function(e,t,n){"use strict";n.d(t,{y:function(){return s}});var r=n(56830);function s(e){return(0,r.L)(e,Date.now())}},19704:function(e,t,n){"use strict";n.d(t,{WU:function(){return B}});let r={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function s(e){return function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}let i={date:s({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:s({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:s({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},a={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function o(e){return(t,n)=>{let r;if("formatting"===((null==n?void 0:n.context)?String(n.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,s=(null==n?void 0:n.width)?String(n.width):t;r=e.formattingValues[s]||e.formattingValues[t]}else{let t=e.defaultWidth,s=(null==n?void 0:n.width)?String(n.width):e.defaultWidth;r=e.values[s]||e.values[t]}return r[e.argumentCallback?e.argumentCallback(t):t]}}function l(e){return function(t){let n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=r.width,i=s&&e.matchPatterns[s]||e.matchPatterns[e.defaultMatchWidth],a=t.match(i);if(!a)return null;let o=a[0],l=s&&e.parsePatterns[s]||e.parsePatterns[e.defaultParseWidth],c=Array.isArray(l)?function(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n}(l,e=>e.test(o)):function(e,t){for(let n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n}(l,e=>e.test(o));return n=e.valueCallback?e.valueCallback(c):c,{value:n=r.valueCallback?r.valueCallback(n):n,rest:t.slice(o.length)}}}let c={code:"en-US",formatDistance:(e,t,n)=>{let s;let i=r[e];return(s="string"==typeof i?i:1===t?i.one:i.other.replace("{{count}}",t.toString()),null==n?void 0:n.addSuffix)?n.comparison&&n.comparison>0?"in "+s:s+" ago":s},formatLong:i,formatRelative:(e,t,n,r)=>a[e],localize:{ordinalNumber:(e,t)=>{let n=Number(e),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:o({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:o({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:o({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:o({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:o({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:(h={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)},function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.match(h.matchPattern);if(!n)return null;let r=n[0],s=e.match(h.parsePattern);if(!s)return null;let i=h.valueCallback?h.valueCallback(s[0]):s[0];return{value:i=t.valueCallback?t.valueCallback(i):i,rest:e.slice(r.length)}}),era:l({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:l({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:l({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:l({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:l({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},u={};var h,d=n(85453),p=n(28999);function f(e){let t=(0,p.Q)(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}var m=n(56830);function g(e,t){var n,r,s,i,a,o,l,c;let h=null!==(c=null!==(l=null!==(o=null!==(a=null==t?void 0:t.weekStartsOn)&&void 0!==a?a:null==t?void 0:null===(r=t.locale)||void 0===r?void 0:null===(n=r.options)||void 0===n?void 0:n.weekStartsOn)&&void 0!==o?o:u.weekStartsOn)&&void 0!==l?l:null===(i=u.locale)||void 0===i?void 0:null===(s=i.options)||void 0===s?void 0:s.weekStartsOn)&&void 0!==c?c:0,d=(0,p.Q)(e),f=d.getDay();return d.setDate(d.getDate()-((f<h?7:0)+f-h)),d.setHours(0,0,0,0),d}function E(e){return g(e,{weekStartsOn:1})}function T(e){let t=(0,p.Q)(e),n=t.getFullYear(),r=(0,m.L)(e,0);r.setFullYear(n+1,0,4),r.setHours(0,0,0,0);let s=E(r),i=(0,m.L)(e,0);i.setFullYear(n,0,4),i.setHours(0,0,0,0);let a=E(i);return t.getTime()>=s.getTime()?n+1:t.getTime()>=a.getTime()?n:n-1}function A(e,t){var n,r,s,i,a,o,l,c;let h=(0,p.Q)(e),d=h.getFullYear(),f=null!==(c=null!==(l=null!==(o=null!==(a=null==t?void 0:t.firstWeekContainsDate)&&void 0!==a?a:null==t?void 0:null===(r=t.locale)||void 0===r?void 0:null===(n=r.options)||void 0===n?void 0:n.firstWeekContainsDate)&&void 0!==o?o:u.firstWeekContainsDate)&&void 0!==l?l:null===(i=u.locale)||void 0===i?void 0:null===(s=i.options)||void 0===s?void 0:s.firstWeekContainsDate)&&void 0!==c?c:1,E=(0,m.L)(e,0);E.setFullYear(d+1,0,f),E.setHours(0,0,0,0);let T=g(E,t),A=(0,m.L)(e,0);A.setFullYear(d,0,f),A.setHours(0,0,0,0);let _=g(A,t);return h.getTime()>=T.getTime()?d+1:h.getTime()>=_.getTime()?d:d-1}function _(e,t){let n=Math.abs(e).toString().padStart(t,"0");return(e<0?"-":"")+n}let S={y(e,t){let n=e.getFullYear(),r=n>0?n:1-n;return _("yy"===t?r%100:r,t.length)},M(e,t){let n=e.getMonth();return"M"===t?String(n+1):_(n+1,2)},d:(e,t)=>_(e.getDate(),t.length),a(e,t){let n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return"am"===n?"a.m.":"p.m."}},h:(e,t)=>_(e.getHours()%12||12,t.length),H:(e,t)=>_(e.getHours(),t.length),m:(e,t)=>_(e.getMinutes(),t.length),s:(e,t)=>_(e.getSeconds(),t.length),S(e,t){let n=t.length;return _(Math.trunc(e.getMilliseconds()*Math.pow(10,n-3)),t.length)}},y={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},k={G:function(e,t,n){let r=e.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});default:return n.era(r,{width:"wide"})}},y:function(e,t,n){if("yo"===t){let t=e.getFullYear();return n.ordinalNumber(t>0?t:1-t,{unit:"year"})}return S.y(e,t)},Y:function(e,t,n,r){let s=A(e,r),i=s>0?s:1-s;return"YY"===t?_(i%100,2):"Yo"===t?n.ordinalNumber(i,{unit:"year"}):_(i,t.length)},R:function(e,t){return _(T(e),t.length)},u:function(e,t){return _(e.getFullYear(),t.length)},Q:function(e,t,n){let r=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return _(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(e,t,n){let r=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return _(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(e,t,n){let r=e.getMonth();switch(t){case"M":case"MM":return S.M(e,t);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(e,t,n){let r=e.getMonth();switch(t){case"L":return String(r+1);case"LL":return _(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(e,t,n,r){let s=function(e,t){let n=(0,p.Q)(e);return Math.round((+g(n,t)-+function(e,t){var n,r,s,i,a,o,l,c;let h=null!==(c=null!==(l=null!==(o=null!==(a=null==t?void 0:t.firstWeekContainsDate)&&void 0!==a?a:null==t?void 0:null===(r=t.locale)||void 0===r?void 0:null===(n=r.options)||void 0===n?void 0:n.firstWeekContainsDate)&&void 0!==o?o:u.firstWeekContainsDate)&&void 0!==l?l:null===(i=u.locale)||void 0===i?void 0:null===(s=i.options)||void 0===s?void 0:s.firstWeekContainsDate)&&void 0!==c?c:1,d=A(e,t),p=(0,m.L)(e,0);return p.setFullYear(d,0,h),p.setHours(0,0,0,0),g(p,t)}(n,t))/6048e5)+1}(e,r);return"wo"===t?n.ordinalNumber(s,{unit:"week"}):_(s,t.length)},I:function(e,t,n){let r=function(e){let t=(0,p.Q)(e);return Math.round((+E(t)-+function(e){let t=T(e),n=(0,m.L)(e,0);return n.setFullYear(t,0,4),n.setHours(0,0,0,0),E(n)}(t))/6048e5)+1}(e);return"Io"===t?n.ordinalNumber(r,{unit:"week"}):_(r,t.length)},d:function(e,t,n){return"do"===t?n.ordinalNumber(e.getDate(),{unit:"date"}):S.d(e,t)},D:function(e,t,n){let r=function(e){let t=(0,p.Q)(e);return function(e,t){let n=(0,d.b)(e),r=(0,d.b)(t);return Math.round((+n-f(n)-(+r-f(r)))/864e5)}(t,function(e){let t=(0,p.Q)(e),n=(0,m.L)(e,0);return n.setFullYear(t.getFullYear(),0,1),n.setHours(0,0,0,0),n}(t))+1}(e);return"Do"===t?n.ordinalNumber(r,{unit:"dayOfYear"}):_(r,t.length)},E:function(e,t,n){let r=e.getDay();switch(t){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(e,t,n,r){let s=e.getDay(),i=(s-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(i);case"ee":return _(i,2);case"eo":return n.ordinalNumber(i,{unit:"day"});case"eee":return n.day(s,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(s,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(s,{width:"short",context:"formatting"});default:return n.day(s,{width:"wide",context:"formatting"})}},c:function(e,t,n,r){let s=e.getDay(),i=(s-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(i);case"cc":return _(i,t.length);case"co":return n.ordinalNumber(i,{unit:"day"});case"ccc":return n.day(s,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(s,{width:"narrow",context:"standalone"});case"cccccc":return n.day(s,{width:"short",context:"standalone"});default:return n.day(s,{width:"wide",context:"standalone"})}},i:function(e,t,n){let r=e.getDay(),s=0===r?7:r;switch(t){case"i":return String(s);case"ii":return _(s,t.length);case"io":return n.ordinalNumber(s,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(e,t,n){let r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(e,t,n){let r;let s=e.getHours();switch(r=12===s?y.noon:0===s?y.midnight:s/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},B:function(e,t,n){let r;let s=e.getHours();switch(r=s>=17?y.evening:s>=12?y.afternoon:s>=4?y.morning:y.night,t){case"B":case"BB":case"BBB":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},h:function(e,t,n){if("ho"===t){let t=e.getHours()%12;return 0===t&&(t=12),n.ordinalNumber(t,{unit:"hour"})}return S.h(e,t)},H:function(e,t,n){return"Ho"===t?n.ordinalNumber(e.getHours(),{unit:"hour"}):S.H(e,t)},K:function(e,t,n){let r=e.getHours()%12;return"Ko"===t?n.ordinalNumber(r,{unit:"hour"}):_(r,t.length)},k:function(e,t,n){let r=e.getHours();return(0===r&&(r=24),"ko"===t)?n.ordinalNumber(r,{unit:"hour"}):_(r,t.length)},m:function(e,t,n){return"mo"===t?n.ordinalNumber(e.getMinutes(),{unit:"minute"}):S.m(e,t)},s:function(e,t,n){return"so"===t?n.ordinalNumber(e.getSeconds(),{unit:"second"}):S.s(e,t)},S:function(e,t){return S.S(e,t)},X:function(e,t,n){let r=e.getTimezoneOffset();if(0===r)return"Z";switch(t){case"X":return N(r);case"XXXX":case"XX":return I(r);default:return I(r,":")}},x:function(e,t,n){let r=e.getTimezoneOffset();switch(t){case"x":return N(r);case"xxxx":case"xx":return I(r);default:return I(r,":")}},O:function(e,t,n){let r=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+C(r,":");default:return"GMT"+I(r,":")}},z:function(e,t,n){let r=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+C(r,":");default:return"GMT"+I(r,":")}},t:function(e,t,n){return _(Math.trunc(e.getTime()/1e3),t.length)},T:function(e,t,n){return _(e.getTime(),t.length)}};function C(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=e>0?"-":"+",r=Math.abs(e),s=Math.trunc(r/60),i=r%60;return 0===i?n+String(s):n+String(s)+t+_(i,2)}function N(e,t){return e%60==0?(e>0?"-":"+")+_(Math.abs(e)/60,2):I(e,t)}function I(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=Math.abs(e);return(e>0?"-":"+")+_(Math.trunc(n/60),2)+t+_(n%60,2)}let b=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},O=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},D={p:O,P:(e,t)=>{let n;let r=e.match(/(P+)(p+)?/)||[],s=r[1],i=r[2];if(!i)return b(e,t);switch(s){case"P":n=t.dateTime({width:"short"});break;case"PP":n=t.dateTime({width:"medium"});break;case"PPP":n=t.dateTime({width:"long"});break;default:n=t.dateTime({width:"full"})}return n.replace("{{date}}",b(s,t)).replace("{{time}}",O(i,t))}},R=/^D+$/,M=/^Y+$/,v=["D","DD","YY","YYYY"],L=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,w=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,P=/^'([^]*?)'?$/,x=/''/g,F=/[a-zA-Z]/;function B(e,t,n){var r,s,i,a,o,l,h,d,f,m,g,E,T,A,_,S,y,C;let N=null!==(m=null!==(f=null==n?void 0:n.locale)&&void 0!==f?f:u.locale)&&void 0!==m?m:c,I=null!==(A=null!==(T=null!==(E=null!==(g=null==n?void 0:n.firstWeekContainsDate)&&void 0!==g?g:null==n?void 0:null===(s=n.locale)||void 0===s?void 0:null===(r=s.options)||void 0===r?void 0:r.firstWeekContainsDate)&&void 0!==E?E:u.firstWeekContainsDate)&&void 0!==T?T:null===(a=u.locale)||void 0===a?void 0:null===(i=a.options)||void 0===i?void 0:i.firstWeekContainsDate)&&void 0!==A?A:1,b=null!==(C=null!==(y=null!==(S=null!==(_=null==n?void 0:n.weekStartsOn)&&void 0!==_?_:null==n?void 0:null===(l=n.locale)||void 0===l?void 0:null===(o=l.options)||void 0===o?void 0:o.weekStartsOn)&&void 0!==S?S:u.weekStartsOn)&&void 0!==y?y:null===(d=u.locale)||void 0===d?void 0:null===(h=d.options)||void 0===h?void 0:h.weekStartsOn)&&void 0!==C?C:0,O=(0,p.Q)(e);if(!((O instanceof Date||"object"==typeof O&&"[object Date]"===Object.prototype.toString.call(O)||"number"==typeof O)&&!isNaN(Number((0,p.Q)(O)))))throw RangeError("Invalid time value");let B=t.match(w).map(e=>{let t=e[0];return"p"===t||"P"===t?(0,D[t])(e,N.formatLong):e}).join("").match(L).map(e=>{if("''"===e)return{isToken:!1,value:"'"};let t=e[0];if("'"===t)return{isToken:!1,value:function(e){let t=e.match(P);return t?t[1].replace(x,"'"):e}(e)};if(k[t])return{isToken:!0,value:e};if(t.match(F))throw RangeError("Format string contains an unescaped latin alphabet character `"+t+"`");return{isToken:!1,value:e}});N.localize.preprocessor&&(B=N.localize.preprocessor(O,B));let H={firstWeekContainsDate:I,weekStartsOn:b,locale:N};return B.map(r=>{if(!r.isToken)return r.value;let s=r.value;return(!(null==n?void 0:n.useAdditionalWeekYearTokens)&&M.test(s)||!(null==n?void 0:n.useAdditionalDayOfYearTokens)&&R.test(s))&&function(e,t,n){let r=function(e,t,n){let r="Y"===e[0]?"years":"days of the month";return"Use `".concat(e.toLowerCase(),"` instead of `").concat(e,"` (in `").concat(t,"`) for formatting ").concat(r," to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md")}(e,t,n);if(console.warn(r),v.includes(e))throw RangeError(r)}(s,t,String(e)),(0,k[s[0]])(O,s,N.localize,H)}).join("")}},64691:function(e,t,n){"use strict";n.d(t,{K:function(){return s}});var r=n(85453);function s(e,t){return+(0,r.b)(e)==+(0,r.b)(t)}},77947:function(e,t,n){"use strict";n.d(t,{z:function(){return i}});var r=n(82994),s=n(64691);function i(e){return(0,s.K)(e,(0,r.y)(e))}},51462:function(e,t,n){"use strict";n.d(t,{g:function(){return o}});var r=n(82994),s=n(64691),i=n(28999),a=n(56830);function o(e){return(0,s.K)(e,function(e,t){let n=(0,i.Q)(e);return isNaN(-1)?(0,a.L)(e,NaN):(n.setDate(n.getDate()+t),n)}((0,r.y)(e),-1))}},85453:function(e,t,n){"use strict";n.d(t,{b:function(){return s}});var r=n(28999);function s(e){let t=(0,r.Q)(e);return t.setHours(0,0,0,0),t}},28999:function(e,t,n){"use strict";function r(e){let t=Object.prototype.toString.call(e);return e instanceof Date||"object"==typeof e&&"[object Date]"===t?new e.constructor(+e):new Date("number"==typeof e||"[object Number]"===t||"string"==typeof e||"[object String]"===t?e:NaN)}n.d(t,{Q:function(){return r}})},29227:function(e,t,n){"use strict";n.d(t,{Ig:function(){return _},MI:function(){return A},N0:function(){return b},NE:function(){return a},QK:function(){return y},U9:function(){return R},X0:function(){return I},Y_:function(){return g},_M:function(){return p},al:function(){return l},cR:function(){return u},mD:function(){return N},nw:function(){return M},o:function(){return E},rr:function(){return c},uJ:function(){return L},uo:function(){return C},xb:function(){return S},ym:function(){return v}});var r=n(67120),s=n(77247),i=n(28201);let a=(e,t)=>!e.selection.empty&&(t&&t(e.tr.deleteSelection().scrollIntoView()),!0);function o(e,t){let{$cursor:n}=e.selection;return n&&(t?t.endOfTextblock("backward",e):!(n.parentOffset>0))?n:null}let l=(e,t,n)=>{let a=o(e,n);if(!a)return!1;let l=f(a);if(!l){let n=a.blockRange(),s=n&&(0,r.k9)(n);return null!=s&&(t&&t(e.tr.lift(n,s).scrollIntoView()),!0)}let c=l.nodeBefore;if(O(e,l,t,-1))return!0;if(0==a.parent.content.size&&(d(c,"end")||i.qv.isSelectable(c)))for(let n=a.depth;;n--){let o=(0,r.dR)(e.doc,a.before(n),a.after(n),s.p2.empty);if(o&&o.slice.size<o.to-o.from){if(t){let n=e.tr.step(o);n.setSelection(d(c,"end")?i.Y1.findFrom(n.doc.resolve(n.mapping.map(l.pos,-1)),-1):i.qv.create(n.doc,l.pos-c.nodeSize)),t(n.scrollIntoView())}return!0}if(1==n||a.node(n-1).childCount>1)break}return!!c.isAtom&&l.depth==a.depth-1&&(t&&t(e.tr.delete(l.pos-c.nodeSize,l.pos).scrollIntoView()),!0)},c=(e,t,n)=>{let r=o(e,n);if(!r)return!1;let s=f(r);return!!s&&h(e,s,t)},u=(e,t,n)=>{let r=m(e,n);if(!r)return!1;let s=T(r);return!!s&&h(e,s,t)};function h(e,t,n){let a=t.nodeBefore,o=t.pos-1;for(;!a.isTextblock;o--){if(a.type.spec.isolating)return!1;let e=a.lastChild;if(!e)return!1;a=e}let l=t.nodeAfter,c=t.pos+1;for(;!l.isTextblock;c++){if(l.type.spec.isolating)return!1;let e=l.firstChild;if(!e)return!1;l=e}let u=(0,r.dR)(e.doc,o,c,s.p2.empty);if(!u||u.from!=o||u instanceof r.Pu&&u.slice.size>=c-o)return!1;if(n){let t=e.tr.step(u);t.setSelection(i.Bs.create(t.doc,o)),n(t.scrollIntoView())}return!0}function d(e,t,n=!1){for(let r=e;r;r="start"==t?r.firstChild:r.lastChild){if(r.isTextblock)return!0;if(n&&1!=r.childCount)break}return!1}let p=(e,t,n)=>{let{$head:r,empty:s}=e.selection,a=r;if(!s)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("backward",e):r.parentOffset>0)return!1;a=f(r)}let o=a&&a.nodeBefore;return!!(o&&i.qv.isSelectable(o))&&(t&&t(e.tr.setSelection(i.qv.create(e.doc,a.pos-o.nodeSize)).scrollIntoView()),!0)};function f(e){if(!e.parent.type.spec.isolating)for(let t=e.depth-1;t>=0;t--){if(e.index(t)>0)return e.doc.resolve(e.before(t+1));if(e.node(t).type.spec.isolating)break}return null}function m(e,t){let{$cursor:n}=e.selection;return n&&(t?t.endOfTextblock("forward",e):!(n.parentOffset<n.parent.content.size))?n:null}let g=(e,t,n)=>{let a=m(e,n);if(!a)return!1;let o=T(a);if(!o)return!1;let l=o.nodeAfter;if(O(e,o,t,1))return!0;if(0==a.parent.content.size&&(d(l,"start")||i.qv.isSelectable(l))){let n=(0,r.dR)(e.doc,a.before(),a.after(),s.p2.empty);if(n&&n.slice.size<n.to-n.from){if(t){let r=e.tr.step(n);r.setSelection(d(l,"start")?i.Y1.findFrom(r.doc.resolve(r.mapping.map(o.pos)),1):i.qv.create(r.doc,r.mapping.map(o.pos))),t(r.scrollIntoView())}return!0}}return!!l.isAtom&&o.depth==a.depth-1&&(t&&t(e.tr.delete(o.pos,o.pos+l.nodeSize).scrollIntoView()),!0)},E=(e,t,n)=>{let{$head:r,empty:s}=e.selection,a=r;if(!s)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("forward",e):r.parentOffset<r.parent.content.size)return!1;a=T(r)}let o=a&&a.nodeAfter;return!!(o&&i.qv.isSelectable(o))&&(t&&t(e.tr.setSelection(i.qv.create(e.doc,a.pos)).scrollIntoView()),!0)};function T(e){if(!e.parent.type.spec.isolating)for(let t=e.depth-1;t>=0;t--){let n=e.node(t);if(e.index(t)+1<n.childCount)return e.doc.resolve(e.after(t+1));if(n.type.spec.isolating)break}return null}let A=(e,t)=>{let n=e.selection,s=n instanceof i.qv,a;if(s){if(n.node.isTextblock||!(0,r.Mn)(e.doc,n.from))return!1;a=n.from}else if(null==(a=(0,r.GJ)(e.doc,n.from,-1)))return!1;if(t){let n=e.tr.join(a);s&&n.setSelection(i.qv.create(n.doc,a-e.doc.resolve(a).nodeBefore.nodeSize)),t(n.scrollIntoView())}return!0},_=(e,t)=>{let n=e.selection,s;if(n instanceof i.qv){if(n.node.isTextblock||!(0,r.Mn)(e.doc,n.to))return!1;s=n.to}else if(null==(s=(0,r.GJ)(e.doc,n.to,1)))return!1;return t&&t(e.tr.join(s).scrollIntoView()),!0},S=(e,t)=>{let{$from:n,$to:s}=e.selection,i=n.blockRange(s),a=i&&(0,r.k9)(i);return null!=a&&(t&&t(e.tr.lift(i,a).scrollIntoView()),!0)},y=(e,t)=>{let{$head:n,$anchor:r}=e.selection;return!!(n.parent.type.spec.code&&n.sameParent(r))&&(t&&t(e.tr.insertText("\n").scrollIntoView()),!0)};function k(e){for(let t=0;t<e.edgeCount;t++){let{type:n}=e.edge(t);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}let C=(e,t)=>{let{$head:n,$anchor:r}=e.selection;if(!n.parent.type.spec.code||!n.sameParent(r))return!1;let s=n.node(-1),a=n.indexAfter(-1),o=k(s.contentMatchAt(a));if(!o||!s.canReplaceWith(a,a,o))return!1;if(t){let r=n.after(),s=e.tr.replaceWith(r,r,o.createAndFill());s.setSelection(i.Y1.near(s.doc.resolve(r),1)),t(s.scrollIntoView())}return!0},N=(e,t)=>{let n=e.selection,{$from:r,$to:s}=n;if(n instanceof i.C1||r.parent.inlineContent||s.parent.inlineContent)return!1;let a=k(s.parent.contentMatchAt(s.indexAfter()));if(!a||!a.isTextblock)return!1;if(t){let n=(!r.parentOffset&&s.index()<s.parent.childCount?r:s).pos,o=e.tr.insert(n,a.createAndFill());o.setSelection(i.Bs.create(o.doc,n+1)),t(o.scrollIntoView())}return!0},I=(e,t)=>{let{$cursor:n}=e.selection;if(!n||n.parent.content.size)return!1;if(n.depth>1&&n.after()!=n.end(-1)){let s=n.before();if((0,r.Ax)(e.doc,s))return t&&t(e.tr.split(s).scrollIntoView()),!0}let s=n.blockRange(),i=s&&(0,r.k9)(s);return null!=i&&(t&&t(e.tr.lift(s,i).scrollIntoView()),!0)},b=(e,t)=>{let{$from:n,to:r}=e.selection,s,a=n.sharedDepth(r);return 0!=a&&(s=n.before(a),t&&t(e.tr.setSelection(i.qv.create(e.doc,s))),!0)};function O(e,t,n,a){let o,l,c,u=t.nodeBefore,h=t.nodeAfter,p,f,m=u.type.spec.isolating||h.type.spec.isolating;if(!m&&(o=t.nodeBefore,l=t.nodeAfter,c=t.index(),o&&l&&o.type.compatibleContent(l.type)&&(!o.content.size&&t.parent.canReplace(c-1,c)?(n&&n(e.tr.delete(t.pos-o.nodeSize,t.pos).scrollIntoView()),!0):!!(t.parent.canReplace(c,c+1)&&(l.isTextblock||(0,r.Mn)(e.doc,t.pos)))&&(n&&n(e.tr.join(t.pos).scrollIntoView()),!0))))return!0;let g=!m&&t.parent.canReplace(t.index(),t.index()+1);if(g&&(p=(f=u.contentMatchAt(u.childCount)).findWrapping(h.type))&&f.matchType(p[0]||h.type).validEnd){if(n){let i=t.pos+h.nodeSize,a=s.HY.empty;for(let e=p.length-1;e>=0;e--)a=s.HY.from(p[e].create(null,a));a=s.HY.from(u.copy(a));let o=e.tr.step(new r.FC(t.pos-1,i,t.pos,i,new s.p2(a,1,0),p.length,!0)),l=o.doc.resolve(i+2*p.length);l.nodeAfter&&l.nodeAfter.type==u.type&&(0,r.Mn)(o.doc,l.pos)&&o.join(l.pos),n(o.scrollIntoView())}return!0}let E=h.type.spec.isolating||a>0&&m?null:i.Y1.findFrom(t,1),T=E&&E.$from.blockRange(E.$to),A=T&&(0,r.k9)(T);if(null!=A&&A>=t.depth)return n&&n(e.tr.lift(T,A).scrollIntoView()),!0;if(g&&d(h,"start",!0)&&d(u,"end")){let i=u,a=[];for(;a.push(i),!i.isTextblock;)i=i.lastChild;let o=h,l=1;for(;!o.isTextblock;o=o.firstChild)l++;if(i.canReplace(i.childCount,i.childCount,o.content)){if(n){let i=s.HY.empty;for(let e=a.length-1;e>=0;e--)i=s.HY.from(a[e].copy(i));n(e.tr.step(new r.FC(t.pos-a.length,t.pos+h.nodeSize,t.pos+l,t.pos+h.nodeSize-l,new s.p2(i,a.length,0),0,!0)).scrollIntoView())}return!0}}return!1}function D(e){return function(t,n){let r=t.selection,s=e<0?r.$from:r.$to,a=s.depth;for(;s.node(a).isInline;){if(!a)return!1;a--}return!!s.node(a).isTextblock&&(n&&n(t.tr.setSelection(i.Bs.create(t.doc,e<0?s.start(a):s.end(a)))),!0)}}let R=D(-1),M=D(1);function v(e,t=null){return function(n,s){let{$from:i,$to:a}=n.selection,o=i.blockRange(a),l=o&&(0,r.nd)(o,e,t);return!!l&&(s&&s(n.tr.wrap(o,l).scrollIntoView()),!0)}}function L(e,t=null){return function(n,r){let s=!1;for(let r=0;r<n.selection.ranges.length&&!s;r++){let{$from:{pos:i},$to:{pos:a}}=n.selection.ranges[r];n.doc.nodesBetween(i,a,(r,i)=>{if(s)return!1;if(!(!r.isTextblock||r.hasMarkup(e,t))){if(r.type==e)s=!0;else{let t=n.doc.resolve(i),r=t.index();s=t.parent.canReplaceWith(r,r+1,e)}}})}if(!s)return!1;if(r){let s=n.tr;for(let r=0;r<n.selection.ranges.length;r++){let{$from:{pos:i},$to:{pos:a}}=n.selection.ranges[r];s.setBlockType(i,a,e,t)}r(s.scrollIntoView())}return!0}}function w(...e){return function(t,n,r){for(let s=0;s<e.length;s++)if(e[s](t,n,r))return!0;return!1}}let P=w(a,l,p),x=w(a,g,E),F={Enter:w(y,N,I,(e,t)=>{let{$from:n,$to:s}=e.selection;if(e.selection instanceof i.qv&&e.selection.node.isBlock)return!!(n.parentOffset&&(0,r.Ax)(e.doc,n.pos))&&(t&&t(e.tr.split(n.pos).scrollIntoView()),!0);if(!n.depth)return!1;let a=[],o,l,c=!1,u=!1;for(let e=n.depth;;e--){if(n.node(e).isBlock){let t;c=n.end(e)==n.pos+(n.depth-e),u=n.start(e)==n.pos-(n.depth-e),l=k(n.node(e-1).contentMatchAt(n.indexAfter(e-1))),a.unshift(t||(c&&l?{type:l}:null)),o=e;break}if(1==e)return!1;a.unshift(null)}let h=e.tr;(e.selection instanceof i.Bs||e.selection instanceof i.C1)&&h.deleteSelection();let d=h.mapping.map(n.pos),p=(0,r.Ax)(h.doc,d,a.length,a);if(p||(a[0]=l?{type:l}:null,p=(0,r.Ax)(h.doc,d,a.length,a)),!p)return!1;if(h.split(d,a.length,a),!c&&u&&n.node(o).type!=l){let e=h.mapping.map(n.before(o)),t=h.doc.resolve(e);l&&n.node(o-1).canReplaceWith(t.index(),t.index()+1,l)&&h.setNodeMarkup(h.mapping.map(n.before(o)),l)}return t&&t(h.scrollIntoView()),!0}),"Mod-Enter":C,Backspace:P,"Mod-Backspace":P,"Shift-Backspace":P,Delete:x,"Mod-Delete":x,"Mod-a":(e,t)=>(t&&t(e.tr.setSelection(new i.C1(e.doc))),!0)},B={"Ctrl-h":F.Backspace,"Alt-Backspace":F["Mod-Backspace"],"Ctrl-d":F.Delete,"Ctrl-Alt-Backspace":F["Mod-Delete"],"Alt-Delete":F["Mod-Delete"],"Alt-d":F["Mod-Delete"],"Ctrl-a":R,"Ctrl-e":M};for(let e in F)B[e]=F[e];"undefined"!=typeof navigator?/Mac|iP(hone|[oa]d)/.test(navigator.platform):"undefined"!=typeof os&&os.platform&&os.platform()},99007:function(e,t,n){"use strict";n.d(t,{$:function(){return f},h:function(){return p}});for(var r={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},s={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},i="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),a="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),o=0;o<10;o++)r[48+o]=r[96+o]=String(o);for(var o=1;o<=24;o++)r[o+111]="F"+o;for(var o=65;o<=90;o++)r[o]=String.fromCharCode(o+32),s[o]=String.fromCharCode(o);for(var l in r)s.hasOwnProperty(l)||(s[l]=r[l]);var c=n(28201);let u="undefined"!=typeof navigator&&/Mac|iP(hone|[oa]d)/.test(navigator.platform),h="undefined"!=typeof navigator&&/Win/.test(navigator.platform);function d(e,t,n=!0){return t.altKey&&(e="Alt-"+e),t.ctrlKey&&(e="Ctrl-"+e),t.metaKey&&(e="Meta-"+e),n&&t.shiftKey&&(e="Shift-"+e),e}function p(e){return new c.Sy({props:{handleKeyDown:f(e)}})}function f(e){let t=function(e){let t=Object.create(null);for(let n in e)t[function(e){let t,n,r,s,i=e.split(/-(?!$)/),a=i[i.length-1];"Space"==a&&(a=" ");for(let e=0;e<i.length-1;e++){let a=i[e];if(/^(cmd|meta|m)$/i.test(a))s=!0;else if(/^a(lt)?$/i.test(a))t=!0;else if(/^(c|ctrl|control)$/i.test(a))n=!0;else if(/^s(hift)?$/i.test(a))r=!0;else if(/^mod$/i.test(a))u?s=!0:n=!0;else throw Error("Unrecognized modifier name: "+a)}return t&&(a="Alt-"+a),n&&(a="Ctrl-"+a),s&&(a="Meta-"+a),r&&(a="Shift-"+a),a}(n)]=e[n];return t}(e);return function(e,n){var o;let l=("Esc"==(o=!(i&&n.metaKey&&n.shiftKey&&!n.ctrlKey&&!n.altKey||a&&n.shiftKey&&n.key&&1==n.key.length||"Unidentified"==n.key)&&n.key||(n.shiftKey?s:r)[n.keyCode]||n.key||"Unidentified")&&(o="Escape"),"Del"==o&&(o="Delete"),"Left"==o&&(o="ArrowLeft"),"Up"==o&&(o="ArrowUp"),"Right"==o&&(o="ArrowRight"),"Down"==o&&(o="ArrowDown"),o),c,u=t[d(l,n)];if(u&&u(e.state,e.dispatch,e))return!0;if(1==l.length&&" "!=l){if(n.shiftKey){let r=t[d(l,n,!1)];if(r&&r(e.state,e.dispatch,e))return!0}if((n.altKey||n.metaKey||n.ctrlKey)&&!(h&&n.ctrlKey&&n.altKey)&&(c=r[n.keyCode])&&c!=l){let r=t[d(c,n)];if(r&&r(e.state,e.dispatch,e))return!0}}return!1}}},77247:function(e,t,n){"use strict";function r(e){this.content=e}n.d(t,{aw:function(){return U},PW:function(){return K},HY:function(){return s},vc:function(){return l},ZU:function(){return F},NB:function(){return k},Ts:function(){return S},e4:function(){return c},V_:function(){return B},p2:function(){return u}}),r.prototype={constructor:r,find:function(e){for(var t=0;t<this.content.length;t+=2)if(this.content[t]===e)return t;return -1},get:function(e){var t=this.find(e);return -1==t?void 0:this.content[t+1]},update:function(e,t,n){var s=n&&n!=e?this.remove(n):this,i=s.find(e),a=s.content.slice();return -1==i?a.push(n||e,t):(a[i+1]=t,n&&(a[i]=n)),new r(a)},remove:function(e){var t=this.find(e);if(-1==t)return this;var n=this.content.slice();return n.splice(t,2),new r(n)},addToStart:function(e,t){return new r([e,t].concat(this.remove(e).content))},addToEnd:function(e,t){var n=this.remove(e).content.slice();return n.push(e,t),new r(n)},addBefore:function(e,t,n){var s=this.remove(t),i=s.content.slice(),a=s.find(e);return i.splice(-1==a?i.length:a,0,t,n),new r(i)},forEach:function(e){for(var t=0;t<this.content.length;t+=2)e(this.content[t],this.content[t+1])},prepend:function(e){return(e=r.from(e)).size?new r(e.content.concat(this.subtract(e).content)):this},append:function(e){return(e=r.from(e)).size?new r(this.subtract(e).content.concat(e.content)):this},subtract:function(e){var t=this;e=r.from(e);for(var n=0;n<e.content.length;n+=2)t=t.remove(e.content[n]);return t},toObject:function(){var e={};return this.forEach(function(t,n){e[t]=n}),e},get size(){return this.content.length>>1}},r.from=function(e){if(e instanceof r)return e;var t=[];if(e)for(var n in e)t.push(n,e[n]);return new r(t)};class s{constructor(e,t){if(this.content=e,this.size=t||0,null==t)for(let t=0;t<e.length;t++)this.size+=e[t].nodeSize}nodesBetween(e,t,n,r=0,s){for(let i=0,a=0;a<t;i++){let o=this.content[i],l=a+o.nodeSize;if(l>e&&!1!==n(o,r+a,s||null,i)&&o.content.size){let s=a+1;o.nodesBetween(Math.max(0,e-s),Math.min(o.content.size,t-s),n,r+s)}a=l}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,t,n,r){let s="",i=!0;return this.nodesBetween(e,t,(a,o)=>{let l=a.isText?a.text.slice(Math.max(e,o)-o,t-o):a.isLeaf?r?"function"==typeof r?r(a):r:a.type.spec.leafText?a.type.spec.leafText(a):"":"";a.isBlock&&(a.isLeaf&&l||a.isTextblock)&&n&&(i?i=!1:s+=n),s+=l},0),s}append(e){if(!e.size)return this;if(!this.size)return e;let t=this.lastChild,n=e.firstChild,r=this.content.slice(),i=0;for(t.isText&&t.sameMarkup(n)&&(r[r.length-1]=t.withText(t.text+n.text),i=1);i<e.content.length;i++)r.push(e.content[i]);return new s(r,this.size+e.size)}cut(e,t=this.size){if(0==e&&t==this.size)return this;let n=[],r=0;if(t>e)for(let s=0,i=0;i<t;s++){let a=this.content[s],o=i+a.nodeSize;o>e&&((i<e||o>t)&&(a=a.isText?a.cut(Math.max(0,e-i),Math.min(a.text.length,t-i)):a.cut(Math.max(0,e-i-1),Math.min(a.content.size,t-i-1))),n.push(a),r+=a.nodeSize),i=o}return new s(n,r)}cutByIndex(e,t){return e==t?s.empty:0==e&&t==this.content.length?this:new s(this.content.slice(e,t))}replaceChild(e,t){let n=this.content[e];if(n==t)return this;let r=this.content.slice(),i=this.size+t.nodeSize-n.nodeSize;return r[e]=t,new s(r,i)}addToStart(e){return new s([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new s(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let t=this.content[e];if(!t)throw RangeError("Index "+e+" out of range for "+this);return t}maybeChild(e){return this.content[e]||null}forEach(e){for(let t=0,n=0;t<this.content.length;t++){let r=this.content[t];e(r,n,t),n+=r.nodeSize}}findDiffStart(e,t=0){return function e(t,n,r){for(let s=0;;s++){if(s==t.childCount||s==n.childCount)return t.childCount==n.childCount?null:r;let i=t.child(s),a=n.child(s);if(i==a){r+=i.nodeSize;continue}if(!i.sameMarkup(a))return r;if(i.isText&&i.text!=a.text){for(let e=0;i.text[e]==a.text[e];e++)r++;return r}if(i.content.size||a.content.size){let t=e(i.content,a.content,r+1);if(null!=t)return t}r+=i.nodeSize}}(this,e,t)}findDiffEnd(e,t=this.size,n=e.size){return function e(t,n,r,s){for(let i=t.childCount,a=n.childCount;;){if(0==i||0==a)return i==a?null:{a:r,b:s};let o=t.child(--i),l=n.child(--a),c=o.nodeSize;if(o==l){r-=c,s-=c;continue}if(!o.sameMarkup(l))return{a:r,b:s};if(o.isText&&o.text!=l.text){let e=0,t=Math.min(o.text.length,l.text.length);for(;e<t&&o.text[o.text.length-e-1]==l.text[l.text.length-e-1];)e++,r--,s--;return{a:r,b:s}}if(o.content.size||l.content.size){let t=e(o.content,l.content,r-1,s-1);if(t)return t}r-=c,s-=c}}(this,e,t,n)}findIndex(e){if(0==e)return a(0,e);if(e==this.size)return a(this.content.length,e);if(e>this.size||e<0)throw RangeError(`Position ${e} outside of fragment (${this})`);for(let t=0,n=0;;t++){let r=n+this.child(t).nodeSize;if(r>=e){if(r==e)return a(t+1,r);return a(t,n)}n=r}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(e=>e.toJSON()):null}static fromJSON(e,t){if(!t)return s.empty;if(!Array.isArray(t))throw RangeError("Invalid input for Fragment.fromJSON");return new s(t.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return s.empty;let t,n=0;for(let r=0;r<e.length;r++){let s=e[r];n+=s.nodeSize,r&&s.isText&&e[r-1].sameMarkup(s)?(t||(t=e.slice(0,r)),t[t.length-1]=s.withText(t[t.length-1].text+s.text)):t&&t.push(s)}return new s(t||e,n)}static from(e){if(!e)return s.empty;if(e instanceof s)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new s([e],e.nodeSize);throw RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}s.empty=new s([],0);let i={index:0,offset:0};function a(e,t){return i.index=e,i.offset=t,i}function o(e,t){if(e===t)return!0;if(!(e&&"object"==typeof e)||!(t&&"object"==typeof t))return!1;let n=Array.isArray(e);if(Array.isArray(t)!=n)return!1;if(n){if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!o(e[n],t[n]))return!1}else{for(let n in e)if(!(n in t)||!o(e[n],t[n]))return!1;for(let n in t)if(!(n in e))return!1}return!0}class l{constructor(e,t){this.type=e,this.attrs=t}addToSet(e){let t,n=!1;for(let r=0;r<e.length;r++){let s=e[r];if(this.eq(s))return e;if(this.type.excludes(s.type))t||(t=e.slice(0,r));else{if(s.type.excludes(this.type))return e;!n&&s.type.rank>this.type.rank&&(t||(t=e.slice(0,r)),t.push(this),n=!0),t&&t.push(s)}}return t||(t=e.slice()),n||t.push(this),t}removeFromSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return e.slice(0,t).concat(e.slice(t+1));return e}isInSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return!0;return!1}eq(e){return this==e||this.type==e.type&&o(this.attrs,e.attrs)}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return e}static fromJSON(e,t){if(!t)throw RangeError("Invalid input for Mark.fromJSON");let n=e.marks[t.type];if(!n)throw RangeError(`There is no mark type ${t.type} in this schema`);let r=n.create(t.attrs);return n.checkAttrs(r.attrs),r}static sameSet(e,t){if(e==t)return!0;if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].eq(t[n]))return!1;return!0}static setFrom(e){if(!e||Array.isArray(e)&&0==e.length)return l.none;if(e instanceof l)return[e];let t=e.slice();return t.sort((e,t)=>e.type.rank-t.type.rank),t}}l.none=[];class c extends Error{}class u{constructor(e,t,n){this.content=e,this.openStart=t,this.openEnd=n}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,t){let n=function e(t,n,r,s){let{index:i,offset:a}=t.findIndex(n),o=t.maybeChild(i);if(a==n||o.isText)return s&&!s.canReplace(i,i,r)?null:t.cut(0,n).append(r).append(t.cut(n));let l=e(o.content,n-a-1,r);return l&&t.replaceChild(i,o.copy(l))}(this.content,e+this.openStart,t);return n&&new u(n,this.openStart,this.openEnd)}removeBetween(e,t){return new u(function e(t,n,r){let{index:s,offset:i}=t.findIndex(n),a=t.maybeChild(s),{index:o,offset:l}=t.findIndex(r);if(i==n||a.isText){if(l!=r&&!t.child(o).isText)throw RangeError("Removing non-flat range");return t.cut(0,n).append(t.cut(r))}if(s!=o)throw RangeError("Removing non-flat range");return t.replaceChild(s,a.copy(e(a.content,n-i-1,r-i-1)))}(this.content,e+this.openStart,t+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,t){if(!t)return u.empty;let n=t.openStart||0,r=t.openEnd||0;if("number"!=typeof n||"number"!=typeof r)throw RangeError("Invalid input for Slice.fromJSON");return new u(s.fromJSON(e,t.content),n,r)}static maxOpen(e,t=!0){let n=0,r=0;for(let r=e.firstChild;r&&!r.isLeaf&&(t||!r.type.spec.isolating);r=r.firstChild)n++;for(let n=e.lastChild;n&&!n.isLeaf&&(t||!n.type.spec.isolating);n=n.lastChild)r++;return new u(e,n,r)}}function h(e,t){if(!t.type.compatibleContent(e.type))throw new c("Cannot join "+t.type.name+" onto "+e.type.name)}function d(e,t,n){let r=e.node(n);return h(r,t.node(n)),r}function p(e,t){let n=t.length-1;n>=0&&e.isText&&e.sameMarkup(t[n])?t[n]=e.withText(t[n].text+e.text):t.push(e)}function f(e,t,n,r){let s=(t||e).node(n),i=0,a=t?t.index(n):s.childCount;e&&(i=e.index(n),e.depth>n?i++:e.textOffset&&(p(e.nodeAfter,r),i++));for(let e=i;e<a;e++)p(s.child(e),r);t&&t.depth==n&&t.textOffset&&p(t.nodeBefore,r)}function m(e,t){return e.type.checkContent(t),e.copy(t)}function g(e,t,n){let r=[];return f(null,e,n,r),e.depth>n&&p(m(d(e,t,n+1),g(e,t,n+1)),r),f(t,null,n,r),new s(r)}u.empty=new u(s.empty,0,0);class E{constructor(e,t,n){this.pos=e,this.path=t,this.parentOffset=n,this.depth=t.length/3-1}resolveDepth(e){return null==e?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[3*this.resolveDepth(e)]}index(e){return this.path[3*this.resolveDepth(e)+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e!=this.depth||this.textOffset?1:0)}start(e){return 0==(e=this.resolveDepth(e))?0:this.path[3*e-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(!(e=this.resolveDepth(e)))throw RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]}after(e){if(!(e=this.resolveDepth(e)))throw RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]+this.path[3*e].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;let n=this.pos-this.path[this.path.length-1],r=e.child(t);return n?e.child(t).cut(n):r}get nodeBefore(){let e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):0==e?null:this.parent.child(e-1)}posAtIndex(e,t){t=this.resolveDepth(t);let n=this.path[3*t],r=0==t?0:this.path[3*t-1]+1;for(let t=0;t<e;t++)r+=n.child(t).nodeSize;return r}marks(){let e=this.parent,t=this.index();if(0==e.content.size)return l.none;if(this.textOffset)return e.child(t).marks;let n=e.maybeChild(t-1),r=e.maybeChild(t);if(!n){let e=n;n=r,r=e}let s=n.marks;for(var i=0;i<s.length;i++)!1!==s[i].type.spec.inclusive||r&&s[i].isInSet(r.marks)||(s=s[i--].removeFromSet(s));return s}marksAcross(e){let t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;let n=t.marks,r=e.parent.maybeChild(e.index());for(var s=0;s<n.length;s++)!1!==n[s].type.spec.inclusive||r&&n[s].isInSet(r.marks)||(n=n[s--].removeFromSet(n));return n}sharedDepth(e){for(let t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}blockRange(e=this,t){if(e.pos<this.pos)return e.blockRange(this);for(let n=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);n>=0;n--)if(e.pos<=this.end(n)&&(!t||t(this.node(n))))return new S(this,e,n);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}static resolve(e,t){if(!(t>=0&&t<=e.content.size))throw RangeError("Position "+t+" out of range");let n=[],r=0,s=t;for(let t=e;;){let{index:e,offset:i}=t.content.findIndex(s),a=s-i;if(n.push(t,e,r+i),!a||(t=t.child(e)).isText)break;s=a-1,r+=i+1}return new E(t,n,s)}static resolveCached(e,t){let n=_.get(e);if(n)for(let e=0;e<n.elts.length;e++){let r=n.elts[e];if(r.pos==t)return r}else _.set(e,n=new T);let r=n.elts[n.i]=E.resolve(e,t);return n.i=(n.i+1)%A,r}}class T{constructor(){this.elts=[],this.i=0}}let A=12,_=new WeakMap;class S{constructor(e,t,n){this.$from=e,this.$to=t,this.depth=n}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}let y=Object.create(null);class k{constructor(e,t,n,r=l.none){this.type=e,this.attrs=t,this.marks=r,this.content=n||s.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,t,n,r=0){this.content.nodesBetween(e,t,n,r,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,t,n,r){return this.content.textBetween(e,t,n,r)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,t,n){return this.type==e&&o(this.attrs,t||e.defaultAttrs||y)&&l.sameSet(this.marks,n||l.none)}copy(e=null){return e==this.content?this:new k(this.type,this.attrs,e,this.marks)}mark(e){return e==this.marks?this:new k(this.type,this.attrs,this.content,e)}cut(e,t=this.content.size){return 0==e&&t==this.content.size?this:this.copy(this.content.cut(e,t))}slice(e,t=this.content.size,n=!1){if(e==t)return u.empty;let r=this.resolve(e),s=this.resolve(t),i=n?0:r.sharedDepth(t),a=r.start(i);return new u(r.node(i).content.cut(r.pos-a,s.pos-a),r.depth-i,s.depth-i)}replace(e,t,n){return function(e,t,n){if(n.openStart>e.depth)throw new c("Inserted content deeper than insertion position");if(e.depth-n.openStart!=t.depth-n.openEnd)throw new c("Inconsistent open depths");return function e(t,n,r,i){let a=t.index(i),o=t.node(i);if(a==n.index(i)&&i<t.depth-r.openStart){let s=e(t,n,r,i+1);return o.copy(o.content.replaceChild(a,s))}if(!r.content.size)return m(o,g(t,n,i));if(r.openStart||r.openEnd||t.depth!=i||n.depth!=i){let{start:e,end:a}=function(e,t){let n=t.depth-e.openStart,r=t.node(n).copy(e.content);for(let e=n-1;e>=0;e--)r=t.node(e).copy(s.from(r));return{start:r.resolveNoCache(e.openStart+n),end:r.resolveNoCache(r.content.size-e.openEnd-n)}}(r,t);return m(o,function e(t,n,r,i,a){let o=t.depth>a&&d(t,n,a+1),l=i.depth>a&&d(r,i,a+1),c=[];return f(null,t,a,c),o&&l&&n.index(a)==r.index(a)?(h(o,l),p(m(o,e(t,n,r,i,a+1)),c)):(o&&p(m(o,g(t,n,a+1)),c),f(n,r,a,c),l&&p(m(l,g(r,i,a+1)),c)),f(i,null,a,c),new s(c)}(t,e,a,n,i))}{let e=t.parent,s=e.content;return m(e,s.cut(0,t.parentOffset).append(r.content).append(s.cut(n.parentOffset)))}}(e,t,n,0)}(this.resolve(e),this.resolve(t),n)}nodeAt(e){for(let t=this;;){let{index:n,offset:r}=t.content.findIndex(e);if(!(t=t.maybeChild(n)))return null;if(r==e||t.isText)return t;e-=r+1}}childAfter(e){let{index:t,offset:n}=this.content.findIndex(e);return{node:this.content.maybeChild(t),index:t,offset:n}}childBefore(e){if(0==e)return{node:null,index:0,offset:0};let{index:t,offset:n}=this.content.findIndex(e);if(n<e)return{node:this.content.child(t),index:t,offset:n};let r=this.content.child(t-1);return{node:r,index:t-1,offset:n-r.nodeSize}}resolve(e){return E.resolveCached(this,e)}resolveNoCache(e){return E.resolve(this,e)}rangeHasMark(e,t,n){let r=!1;return t>e&&this.nodesBetween(e,t,e=>(n.isInSet(e.marks)&&(r=!0),!r)),r}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),N(this.marks,e)}contentMatchAt(e){let t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw Error("Called contentMatchAt on a node with invalid content");return t}canReplace(e,t,n=s.empty,r=0,i=n.childCount){let a=this.contentMatchAt(e).matchFragment(n,r,i),o=a&&a.matchFragment(this.content,t);if(!o||!o.validEnd)return!1;for(let e=r;e<i;e++)if(!this.type.allowsMarks(n.child(e).marks))return!1;return!0}canReplaceWith(e,t,n,r){if(r&&!this.type.allowsMarks(r))return!1;let s=this.contentMatchAt(e).matchType(n),i=s&&s.matchFragment(this.content,t);return!!i&&i.validEnd}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let e=l.none;for(let t=0;t<this.marks.length;t++){let n=this.marks[t];n.type.checkAttrs(n.attrs),e=n.addToSet(e)}if(!l.sameSet(e,this.marks))throw RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(e=>e.type.name)}`);this.content.forEach(e=>e.check())}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map(e=>e.toJSON())),e}static fromJSON(e,t){let n;if(!t)throw RangeError("Invalid input for Node.fromJSON");if(t.marks){if(!Array.isArray(t.marks))throw RangeError("Invalid mark data for Node.fromJSON");n=t.marks.map(e.markFromJSON)}if("text"==t.type){if("string"!=typeof t.text)throw RangeError("Invalid text node in JSON");return e.text(t.text,n)}let r=s.fromJSON(e,t.content),i=e.nodeType(t.type).create(t.attrs,r,n);return i.type.checkAttrs(i.attrs),i}}k.prototype.text=void 0;class C extends k{constructor(e,t,n,r){if(super(e,t,null,r),!n)throw RangeError("Empty text nodes are not allowed");this.text=n}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):N(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,t){return this.text.slice(e,t)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new C(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new C(this.type,this.attrs,e,this.marks)}cut(e=0,t=this.text.length){return 0==e&&t==this.text.length?this:this.withText(this.text.slice(e,t))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}}function N(e,t){for(let n=e.length-1;n>=0;n--)t=e[n].type.name+"("+t+")";return t}class I{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,t){var n;let r,s=new b(e,t);if(null==s.next)return I.empty;let i=function e(t){let n=[];do n.push(function(t){let n=[];do n.push(function(t){let n=function(t){if(t.eat("(")){let n=e(t);return t.eat(")")||t.err("Missing closing paren"),n}if(/\W/.test(t.next))t.err("Unexpected token '"+t.next+"'");else{let e=(function(e,t){let n=e.nodeTypes,r=n[t];if(r)return[r];let s=[];for(let e in n){let r=n[e];r.isInGroup(t)&&s.push(r)}return 0==s.length&&e.err("No node type or group '"+t+"' found"),s})(t,t.next).map(e=>(null==t.inline?t.inline=e.isInline:t.inline!=e.isInline&&t.err("Mixing inline and block content"),{type:"name",value:e}));return t.pos++,1==e.length?e[0]:{type:"choice",exprs:e}}}(t);for(;;)if(t.eat("+"))n={type:"plus",expr:n};else if(t.eat("*"))n={type:"star",expr:n};else if(t.eat("?"))n={type:"opt",expr:n};else if(t.eat("{"))n=function(e,t){let n=O(e),r=n;return e.eat(",")&&(r="}"!=e.next?O(e):-1),e.eat("}")||e.err("Unclosed braced range"),{type:"range",min:n,max:r,expr:t}}(t,n);else break;return n}(t));while(t.next&&")"!=t.next&&"|"!=t.next);return 1==n.length?n[0]:{type:"seq",exprs:n}}(t));while(t.eat("|"));return 1==n.length?n[0]:{type:"choice",exprs:n}}(s);s.next&&s.err("Unexpected trailing text");let a=(n=function(e){let t=[[]];return s(function e(t,i){if("choice"==t.type)return t.exprs.reduce((t,n)=>t.concat(e(n,i)),[]);if("seq"==t.type)for(let r=0;;r++){let a=e(t.exprs[r],i);if(r==t.exprs.length-1)return a;s(a,i=n())}else if("star"==t.type){let a=n();return r(i,a),s(e(t.expr,a),a),[r(a)]}else if("plus"==t.type){let a=n();return s(e(t.expr,i),a),s(e(t.expr,a),a),[r(a)]}else if("opt"==t.type)return[r(i)].concat(e(t.expr,i));else if("range"==t.type){let a=i;for(let r=0;r<t.min;r++){let r=n();s(e(t.expr,a),r),a=r}if(-1==t.max)s(e(t.expr,a),a);else for(let i=t.min;i<t.max;i++){let i=n();r(a,i),s(e(t.expr,a),i),a=i}return[r(a)]}else if("name"==t.type)return[r(i,void 0,t.value)];else throw Error("Unknown expr type")}(e,0),n()),t;function n(){return t.push([])-1}function r(e,n,r){let s={term:r,to:n};return t[e].push(s),s}function s(e,t){e.forEach(e=>e.to=t)}}(i),r=Object.create(null),function e(t){let s=[];t.forEach(e=>{n[e].forEach(({term:e,to:t})=>{let r;if(e){for(let t=0;t<s.length;t++)s[t][0]==e&&(r=s[t][1]);R(n,t).forEach(t=>{r||s.push([e,r=[]]),-1==r.indexOf(t)&&r.push(t)})}})});let i=r[t.join(",")]=new I(t.indexOf(n.length-1)>-1);for(let t=0;t<s.length;t++){let n=s[t][1].sort(D);i.next.push({type:s[t][0],next:r[n.join(",")]||e(n)})}return i}(R(n,0)));return function(e,t){for(let n=0,r=[e];n<r.length;n++){let e=r[n],s=!e.validEnd,i=[];for(let t=0;t<e.next.length;t++){let{type:n,next:a}=e.next[t];i.push(n.name),s&&!(n.isText||n.hasRequiredAttrs())&&(s=!1),-1==r.indexOf(a)&&r.push(a)}s&&t.err("Only non-generatable nodes ("+i.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}(a,s),a}matchType(e){for(let t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}matchFragment(e,t=0,n=e.childCount){let r=this;for(let s=t;r&&s<n;s++)r=r.matchType(e.child(s).type);return r}get inlineContent(){return 0!=this.next.length&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:t}=this.next[e];if(!(t.isText||t.hasRequiredAttrs()))return t}return null}compatible(e){for(let t=0;t<this.next.length;t++)for(let n=0;n<e.next.length;n++)if(this.next[t].type==e.next[n].type)return!0;return!1}fillBefore(e,t=!1,n=0){let r=[this];return function i(a,o){let l=a.matchFragment(e,n);if(l&&(!t||l.validEnd))return s.from(o.map(e=>e.createAndFill()));for(let e=0;e<a.next.length;e++){let{type:t,next:n}=a.next[e];if(!(t.isText||t.hasRequiredAttrs())&&-1==r.indexOf(n)){r.push(n);let e=i(n,o.concat(t));if(e)return e}}return null}(this,[])}findWrapping(e){for(let t=0;t<this.wrapCache.length;t+=2)if(this.wrapCache[t]==e)return this.wrapCache[t+1];let t=this.computeWrapping(e);return this.wrapCache.push(e,t),t}computeWrapping(e){let t=Object.create(null),n=[{match:this,type:null,via:null}];for(;n.length;){let r=n.shift(),s=r.match;if(s.matchType(e)){let e=[];for(let t=r;t.type;t=t.via)e.push(t.type);return e.reverse()}for(let e=0;e<s.next.length;e++){let{type:i,next:a}=s.next[e];i.isLeaf||i.hasRequiredAttrs()||i.name in t||r.type&&!a.validEnd||(n.push({match:i.contentMatch,type:i,via:r}),t[i.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];return!function t(n){e.push(n);for(let r=0;r<n.next.length;r++)-1==e.indexOf(n.next[r].next)&&t(n.next[r].next)}(this),e.map((t,n)=>{let r=n+(t.validEnd?"*":" ")+" ";for(let n=0;n<t.next.length;n++)r+=(n?", ":"")+t.next[n].type.name+"->"+e.indexOf(t.next[n].next);return r}).join("\n")}}I.empty=new I(!0);class b{constructor(e,t){this.string=e,this.nodeTypes=t,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),""==this.tokens[this.tokens.length-1]&&this.tokens.pop(),""==this.tokens[0]&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw SyntaxError(e+" (in content expression '"+this.string+"')")}}function O(e){/\D/.test(e.next)&&e.err("Expected number, got '"+e.next+"'");let t=Number(e.next);return e.pos++,t}function D(e,t){return t-e}function R(e,t){let n=[];return function t(r){let s=e[r];if(1==s.length&&!s[0].term)return t(s[0].to);n.push(r);for(let e=0;e<s.length;e++){let{term:r,to:i}=s[e];r||-1!=n.indexOf(i)||t(i)}}(t),n.sort(D)}function M(e){let t=Object.create(null);for(let n in e){let r=e[n];if(!r.hasDefault)return null;t[n]=r.default}return t}function v(e,t){let n=Object.create(null);for(let r in e){let s=t&&t[r];if(void 0===s){let t=e[r];if(t.hasDefault)s=t.default;else throw RangeError("No value supplied for attribute "+r)}n[r]=s}return n}function L(e,t,n,r){for(let r in t)if(!(r in e))throw RangeError(`Unsupported attribute ${r} for ${n} of type ${r}`);for(let n in e){let r=e[n];r.validate&&r.validate(t[n])}}function w(e,t){let n=Object.create(null);if(t)for(let r in t)n[r]=new x(e,r,t[r]);return n}class P{constructor(e,t,n){this.name=e,this.schema=t,this.spec=n,this.markSet=null,this.groups=n.group?n.group.split(" "):[],this.attrs=w(e,n.attrs),this.defaultAttrs=M(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(n.inline||"text"==e),this.isText="text"==e}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==I.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(e){return this.groups.indexOf(e)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:v(this.attrs,e)}create(e=null,t,n){if(this.isText)throw Error("NodeType.create can't construct text nodes");return new k(this,this.computeAttrs(e),s.from(t),l.setFrom(n))}createChecked(e=null,t,n){return t=s.from(t),this.checkContent(t),new k(this,this.computeAttrs(e),t,l.setFrom(n))}createAndFill(e=null,t,n){if(e=this.computeAttrs(e),(t=s.from(t)).size){let e=this.contentMatch.fillBefore(t);if(!e)return null;t=e.append(t)}let r=this.contentMatch.matchFragment(t),i=r&&r.fillBefore(s.empty,!0);return i?new k(this,e,t.append(i),l.setFrom(n)):null}validContent(e){let t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(let t=0;t<e.childCount;t++)if(!this.allowsMarks(e.child(t).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}checkAttrs(e){L(this.attrs,e,"node",this.name)}allowsMarkType(e){return null==this.markSet||this.markSet.indexOf(e)>-1}allowsMarks(e){if(null==this.markSet)return!0;for(let t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}allowedMarks(e){let t;if(null==this.markSet)return e;for(let n=0;n<e.length;n++)this.allowsMarkType(e[n].type)?t&&t.push(e[n]):t||(t=e.slice(0,n));return t?t.length?t:l.none:e}static compile(e,t){let n=Object.create(null);e.forEach((e,r)=>n[e]=new P(e,t,r));let r=t.spec.topNode||"doc";if(!n[r])throw RangeError("Schema is missing its top node type ('"+r+"')");if(!n.text)throw RangeError("Every schema needs a 'text' type");for(let e in n.text.attrs)throw RangeError("The text node type should not have attributes");return n}}class x{constructor(e,t,n){let r;this.hasDefault=Object.prototype.hasOwnProperty.call(n,"default"),this.default=n.default,this.validate="string"==typeof n.validate?(r=n.validate.split("|"),n=>{let s=null===n?"null":typeof n;if(0>r.indexOf(s))throw RangeError(`Expected value of type ${r} for attribute ${t} on type ${e}, got ${s}`)}):n.validate}get isRequired(){return!this.hasDefault}}class F{constructor(e,t,n,r){this.name=e,this.rank=t,this.schema=n,this.spec=r,this.attrs=w(e,r.attrs),this.excluded=null;let s=M(this.attrs);this.instance=s?new l(this,s):null}create(e=null){return!e&&this.instance?this.instance:new l(this,v(this.attrs,e))}static compile(e,t){let n=Object.create(null),r=0;return e.forEach((e,s)=>n[e]=new F(e,r++,t,s)),n}removeFromSet(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}isInSet(e){for(let t=0;t<e.length;t++)if(e[t].type==this)return e[t]}checkAttrs(e){L(this.attrs,e,"mark",this.name)}excludes(e){return this.excluded.indexOf(e)>-1}}class B{constructor(e){this.linebreakReplacement=null,this.cached=Object.create(null);let t=this.spec={};for(let n in e)t[n]=e[n];t.nodes=r.from(e.nodes),t.marks=r.from(e.marks||{}),this.nodes=P.compile(this.spec.nodes,this),this.marks=F.compile(this.spec.marks,this);let n=Object.create(null);for(let e in this.nodes){if(e in this.marks)throw RangeError(e+" can not be both a node and a mark");let t=this.nodes[e],r=t.spec.content||"",s=t.spec.marks;if(t.contentMatch=n[r]||(n[r]=I.parse(r,this.nodes)),t.inlineContent=t.contentMatch.inlineContent,t.spec.linebreakReplacement){if(this.linebreakReplacement)throw RangeError("Multiple linebreak nodes defined");if(!t.isInline||!t.isLeaf)throw RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=t}t.markSet="_"==s?null:s?H(this,s.split(" ")):""!=s&&t.inlineContent?null:[]}for(let e in this.marks){let t=this.marks[e],n=t.spec.excludes;t.excluded=null==n?[t]:""==n?[]:H(this,n.split(" "))}this.nodeFromJSON=e=>k.fromJSON(this,e),this.markFromJSON=e=>l.fromJSON(this,e),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,t=null,n,r){if("string"==typeof e)e=this.nodeType(e);else if(e instanceof P){if(e.schema!=this)throw RangeError("Node type from different schema used ("+e.name+")")}else throw RangeError("Invalid node type: "+e);return e.createChecked(t,n,r)}text(e,t){let n=this.nodes.text;return new C(n,n.defaultAttrs,e,l.setFrom(t))}mark(e,t){return"string"==typeof e&&(e=this.marks[e]),e.create(t)}nodeType(e){let t=this.nodes[e];if(!t)throw RangeError("Unknown node type: "+e);return t}}function H(e,t){let n=[];for(let r=0;r<t.length;r++){let s=t[r],i=e.marks[s],a=i;if(i)n.push(i);else for(let t in e.marks){let r=e.marks[t];("_"==s||r.spec.group&&r.spec.group.split(" ").indexOf(s)>-1)&&n.push(a=r)}if(!a)throw SyntaxError("Unknown mark type: '"+t[r]+"'")}return n}class U{constructor(e,t){this.schema=e,this.rules=t,this.tags=[],this.styles=[];let n=this.matchedStyles=[];t.forEach(e=>{if(null!=e.tag)this.tags.push(e);else if(null!=e.style){let t=/[^=]*/.exec(e.style)[0];0>n.indexOf(t)&&n.push(t),this.styles.push(e)}}),this.normalizeLists=!this.tags.some(t=>{if(!/^(ul|ol)\b/.test(t.tag)||!t.node)return!1;let n=e.nodes[t.node];return n.contentMatch.matchType(n)})}parse(e,t={}){let n=new $(this,t,!1);return n.addAll(e,l.none,t.from,t.to),n.finish()}parseSlice(e,t={}){let n=new $(this,t,!0);return n.addAll(e,l.none,t.from,t.to),u.maxOpen(n.finish())}matchTag(e,t,n){for(let s=n?this.tags.indexOf(n)+1:0;s<this.tags.length;s++){var r;let n=this.tags[s];if(r=n.tag,(e.matches||e.msMatchesSelector||e.webkitMatchesSelector||e.mozMatchesSelector).call(e,r)&&(void 0===n.namespace||e.namespaceURI==n.namespace)&&(!n.context||t.matchesContext(n.context))){if(n.getAttrs){let t=n.getAttrs(e);if(!1===t)continue;n.attrs=t||void 0}return n}}}matchStyle(e,t,n,r){for(let s=r?this.styles.indexOf(r)+1:0;s<this.styles.length;s++){let r=this.styles[s],i=r.style;if(0==i.indexOf(e)&&(!r.context||n.matchesContext(r.context))&&(!(i.length>e.length)||61==i.charCodeAt(e.length)&&i.slice(e.length+1)==t)){if(r.getAttrs){let e=r.getAttrs(t);if(!1===e)continue;r.attrs=e||void 0}return r}}}static schemaRules(e){let t=[];function n(e){let n=null==e.priority?50:e.priority,r=0;for(;r<t.length;r++){let e=t[r];if((null==e.priority?50:e.priority)<n)break}t.splice(r,0,e)}for(let t in e.marks){let r=e.marks[t].spec.parseDOM;r&&r.forEach(e=>{n(e=j(e)),e.mark||e.ignore||e.clearMark||(e.mark=t)})}for(let t in e.nodes){let r=e.nodes[t].spec.parseDOM;r&&r.forEach(e=>{n(e=j(e)),e.node||e.ignore||e.mark||(e.node=t)})}return t}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new U(e,U.schemaRules(e)))}}let G={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},Y={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},z={ol:!0,ul:!0};function W(e,t,n){return null!=t?(t?1:0)|("full"===t?2:0):e&&"pre"==e.whitespace?3:-5&n}class q{constructor(e,t,n,r,s,i){this.type=e,this.attrs=t,this.marks=n,this.solid=r,this.options=i,this.content=[],this.activeMarks=l.none,this.match=s||(4&i?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let t=this.type.contentMatch.fillBefore(s.from(e));if(t)this.match=this.type.contentMatch.matchFragment(t);else{let t=this.type.contentMatch,n;return(n=t.findWrapping(e.type))?(this.match=t,n):null}}return this.match.findWrapping(e.type)}finish(e){if(!(1&this.options)){let e=this.content[this.content.length-1],t;e&&e.isText&&(t=/[ \t\r\n\u000c]+$/.exec(e.text))&&(e.text.length==t[0].length?this.content.pop():this.content[this.content.length-1]=e.withText(e.text.slice(0,e.text.length-t[0].length)))}let t=s.from(this.content);return!e&&this.match&&(t=t.append(this.match.fillBefore(s.empty,!0))),this.type?this.type.create(this.attrs,t,this.marks):t}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!G.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}}class ${constructor(e,t,n){this.parser=e,this.options=t,this.isOpen=n,this.open=0,this.localPreserveWS=!1;let r=t.topNode,s,i=W(null,t.preserveWhitespace,0)|(n?4:0);s=r?new q(r.type,r.attrs,l.none,!0,t.topMatch||r.type.contentMatch,i):n?new q(null,null,l.none,!0,null,i):new q(e.schema.topNodeType,null,l.none,!0,null,i),this.nodes=[s],this.find=t.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e,t){3==e.nodeType?this.addTextNode(e,t):1==e.nodeType&&this.addElement(e,t)}addTextNode(e,t){let n=e.nodeValue,r=this.top,s=2&r.options?"full":this.localPreserveWS||(1&r.options)>0;if("full"===s||r.inlineContext(e)||/[^ \t\r\n\u000c]/.test(n)){if(s)n="full"!==s?n.replace(/\r?\n|\r/g," "):n.replace(/\r\n?/g,"\n");else if(n=n.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(n)&&this.open==this.nodes.length-1){let t=r.content[r.content.length-1],s=e.previousSibling;(!t||s&&"BR"==s.nodeName||t.isText&&/[ \t\r\n\u000c]$/.test(t.text))&&(n=n.slice(1))}n&&this.insertNode(this.parser.schema.text(n),t,!/\S/.test(n)),this.findInText(e)}else this.findInside(e)}addElement(e,t,n){let r=this.localPreserveWS,s=this.top;("PRE"==e.tagName||/pre/.test(e.style&&e.style.whiteSpace))&&(this.localPreserveWS=!0);let i=e.nodeName.toLowerCase(),a;z.hasOwnProperty(i)&&this.parser.normalizeLists&&function(e){for(let t=e.firstChild,n=null;t;t=t.nextSibling){let e=1==t.nodeType?t.nodeName.toLowerCase():null;e&&z.hasOwnProperty(e)&&n?(n.appendChild(t),t=n):"li"==e?n=t:e&&(n=null)}}(e);let o=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(a=this.parser.matchTag(e,this,n));t:if(o?o.ignore:Y.hasOwnProperty(i))this.findInside(e),this.ignoreFallback(e,t);else if(!o||o.skip||o.closeParent){o&&o.closeParent?this.open=Math.max(0,this.open-1):o&&o.skip.nodeType&&(e=o.skip);let n,r=this.needsBlock;if(G.hasOwnProperty(i))s.content.length&&s.content[0].isInline&&this.open&&(this.open--,s=this.top),n=!0,s.type||(this.needsBlock=!0);else if(!e.firstChild){this.leafFallback(e,t);break t}let a=o&&o.skip?t:this.readStyles(e,t);a&&this.addAll(e,a),n&&this.sync(s),this.needsBlock=r}else{let n=this.readStyles(e,t);n&&this.addElementByRule(e,o,n,!1===o.consuming?a:void 0)}this.localPreserveWS=r}leafFallback(e,t){"BR"==e.nodeName&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode("\n"),t)}ignoreFallback(e,t){"BR"!=e.nodeName||this.top.type&&this.top.type.inlineContent||this.findPlace(this.parser.schema.text("-"),t,!0)}readStyles(e,t){let n=e.style;if(n&&n.length)for(let e=0;e<this.parser.matchedStyles.length;e++){let r=this.parser.matchedStyles[e],s=n.getPropertyValue(r);if(s)for(let e;;){let n=this.parser.matchStyle(r,s,this,e);if(!n)break;if(n.ignore)return null;if(t=n.clearMark?t.filter(e=>!n.clearMark(e)):t.concat(this.parser.schema.marks[n.mark].create(n.attrs)),!1===n.consuming)e=n;else break}}return t}addElementByRule(e,t,n,r){let s,i;if(t.node){if((i=this.parser.schema.nodes[t.node]).isLeaf)this.insertNode(i.create(t.attrs),n,"BR"==e.nodeName)||this.leafFallback(e,n);else{let e=this.enter(i,t.attrs||null,n,t.preserveWhitespace);e&&(s=!0,n=e)}}else{let e=this.parser.schema.marks[t.mark];n=n.concat(e.create(t.attrs))}let a=this.top;if(i&&i.isLeaf)this.findInside(e);else if(r)this.addElement(e,n,r);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach(e=>this.insertNode(e,n,!1));else{let r=e;"string"==typeof t.contentElement?r=e.querySelector(t.contentElement):"function"==typeof t.contentElement?r=t.contentElement(e):t.contentElement&&(r=t.contentElement),this.findAround(e,r,!0),this.addAll(r,n),this.findAround(e,r,!1)}s&&this.sync(a)&&this.open--}addAll(e,t,n,r){let s=n||0;for(let i=n?e.childNodes[n]:e.firstChild,a=null==r?null:e.childNodes[r];i!=a;i=i.nextSibling,++s)this.findAtPoint(e,s),this.addDOM(i,t);this.findAtPoint(e,s)}findPlace(e,t,n){let r,s;for(let t=this.open,i=0;t>=0;t--){let a=this.nodes[t],o=a.findWrapping(e);if(o&&(!r||r.length>o.length+i)&&(r=o,s=a,!o.length))break;if(a.solid){if(n)break;i+=2}}if(!r)return null;this.sync(s);for(let e=0;e<r.length;e++)t=this.enterInner(r[e],null,t,!1);return t}insertNode(e,t,n){if(e.isInline&&this.needsBlock&&!this.top.type){let e=this.textblockFromContext();e&&(t=this.enterInner(e,null,t))}let r=this.findPlace(e,t,n);if(r){this.closeExtra();let t=this.top;t.match&&(t.match=t.match.matchType(e.type));let n=l.none;for(let s of r.concat(e.marks))(t.type?t.type.allowsMarkType(s.type):V(s.type,e.type))&&(n=s.addToSet(n));return t.content.push(e.mark(n)),!0}return!1}enter(e,t,n,r){let s=this.findPlace(e.create(t),n,!1);return s&&(s=this.enterInner(e,t,n,!0,r)),s}enterInner(e,t,n,r=!1,s){this.closeExtra();let i=this.top;i.match=i.match&&i.match.matchType(e);let a=W(e,s,i.options);4&i.options&&0==i.content.length&&(a|=4);let o=l.none;return n=n.filter(t=>(i.type?!i.type.allowsMarkType(t.type):!V(t.type,e))||(o=t.addToSet(o),!1)),this.nodes.push(new q(e,t,o,r,null,a)),this.open++,n}closeExtra(e=!1){let t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!!(this.isOpen||this.options.topOpen))}sync(e){for(let t=this.open;t>=0;t--){if(this.nodes[t]==e)return this.open=t,!0;this.localPreserveWS&&(this.nodes[t].options|=1)}return!1}get currentPos(){this.closeExtra();let e=0;for(let t=this.open;t>=0;t--){let n=this.nodes[t].content;for(let t=n.length-1;t>=0;t--)e+=n[t].nodeSize;t&&e++}return e}findAtPoint(e,t){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].node==e&&this.find[n].offset==t&&(this.find[n].pos=this.currentPos)}findInside(e){if(this.find)for(let t=0;t<this.find.length;t++)null==this.find[t].pos&&1==e.nodeType&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}findAround(e,t,n){if(e!=t&&this.find)for(let r=0;r<this.find.length;r++)null==this.find[r].pos&&1==e.nodeType&&e.contains(this.find[r].node)&&t.compareDocumentPosition(this.find[r].node)&(n?2:4)&&(this.find[r].pos=this.currentPos)}findInText(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let t=e.split("/"),n=this.options.context,r=!this.isOpen&&(!n||n.parent.type==this.nodes[0].type),s=-(n?n.depth+1:0)+(r?0:1),i=(e,a)=>{for(;e>=0;e--){let o=t[e];if(""==o){if(e==t.length-1||0==e)continue;for(;a>=s;a--)if(i(e-1,a))return!0;return!1}{let e=a>0||0==a&&r?this.nodes[a].type:n&&a>=s?n.node(a-s).type:null;if(!e||e.name!=o&&!e.isInGroup(o))return!1;a--}}return!0};return i(t.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let t=e.depth;t>=0;t--){let n=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(let e in this.parser.schema.nodes){let t=this.parser.schema.nodes[e];if(t.isTextblock&&t.defaultAttrs)return t}}}function j(e){let t={};for(let n in e)t[n]=e[n];return t}function V(e,t){let n=t.schema.nodes;for(let r in n){let s=n[r];if(!s.allowsMarkType(e))continue;let i=[],a=e=>{i.push(e);for(let n=0;n<e.edgeCount;n++){let{type:r,next:s}=e.edge(n);if(r==t||0>i.indexOf(s)&&a(s))return!0}};if(a(s.contentMatch))return!0}}class K{constructor(e,t){this.nodes=e,this.marks=t}serializeFragment(e,t={},n){n||(n=X(t).createDocumentFragment());let r=n,s=[];return e.forEach(e=>{if(s.length||e.marks.length){let n=0,i=0;for(;n<s.length&&i<e.marks.length;){let t=e.marks[i];if(!this.marks[t.type.name]){i++;continue}if(!t.eq(s[n][0])||!1===t.type.spec.spanning)break;n++,i++}for(;n<s.length;)r=s.pop()[1];for(;i<e.marks.length;){let n=e.marks[i++],a=this.serializeMark(n,e.isInline,t);a&&(s.push([n,r]),r.appendChild(a.dom),r=a.contentDOM||a.dom)}}r.appendChild(this.serializeNodeInner(e,t))}),n}serializeNodeInner(e,t){let{dom:n,contentDOM:r}=Z(X(t),this.nodes[e.type.name](e),null,e.attrs);if(r){if(e.isLeaf)throw RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,t,r)}return n}serializeNode(e,t={}){let n=this.serializeNodeInner(e,t);for(let r=e.marks.length-1;r>=0;r--){let s=this.serializeMark(e.marks[r],e.isInline,t);s&&((s.contentDOM||s.dom).appendChild(n),n=s.dom)}return n}serializeMark(e,t,n={}){let r=this.marks[e.type.name];return r&&Z(X(n),r(e,t),null,e.attrs)}static renderSpec(e,t,n=null,r){return Z(e,t,n,r)}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new K(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let t=Q(e.nodes);return t.text||(t.text=e=>e.text),t}static marksFromSchema(e){return Q(e.marks)}}function Q(e){let t={};for(let n in e){let r=e[n].spec.toDOM;r&&(t[n]=r)}return t}function X(e){return e.document||window.document}let J=new WeakMap;function Z(e,t,n,r){let s,i,a;if("string"==typeof t)return{dom:e.createTextNode(t)};if(null!=t.nodeType)return{dom:t};if(t.dom&&null!=t.dom.nodeType)return t;let o=t[0],l;if("string"!=typeof o)throw RangeError("Invalid array passed to renderSpec");if(r&&(void 0===(i=J.get(r))&&J.set(r,(a=null,function e(t){if(t&&"object"==typeof t){if(Array.isArray(t)){if("string"==typeof t[0])a||(a=[]),a.push(t);else for(let n=0;n<t.length;n++)e(t[n])}else for(let n in t)e(t[n])}}(r),i=a)),l=i)&&l.indexOf(t)>-1)throw RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let c=o.indexOf(" ");c>0&&(n=o.slice(0,c),o=o.slice(c+1));let u=n?e.createElementNS(n,o):e.createElement(o),h=t[1],d=1;if(h&&"object"==typeof h&&null==h.nodeType&&!Array.isArray(h)){for(let e in d=2,h)if(null!=h[e]){let t=e.indexOf(" ");t>0?u.setAttributeNS(e.slice(0,t),e.slice(t+1),h[e]):"style"==e&&u.style?u.style.cssText=h[e]:u.setAttribute(e,h[e])}}for(let i=d;i<t.length;i++){let a=t[i];if(0===a){if(i<t.length-1||i>d)throw RangeError("Content hole must be the only child of its parent node");return{dom:u,contentDOM:u}}{let{dom:t,contentDOM:i}=Z(e,a,n,r);if(u.appendChild(t),i){if(s)throw RangeError("Multiple content holes");s=i}}}return{dom:u,contentDOM:s}}},4646:function(e,t,n){"use strict";n.d(t,{IB:function(){return a},KI:function(){return i},bw:function(){return o}});var r=n(67120),s=n(77247);function i(e,t=null){return function(n,i){let{$from:a,$to:o}=n.selection,l=a.blockRange(o);if(!l)return!1;let c=i?n.tr:null;return!!function(e,t,n,i=null){let a=!1,o=t,l=t.$from.doc;if(t.depth>=2&&t.$from.node(t.depth-1).type.compatibleContent(n)&&0==t.startIndex){if(0==t.$from.index(t.depth-1))return!1;let e=l.resolve(t.start-2);o=new s.Ts(e,e,t.depth),t.endIndex<t.parent.childCount&&(t=new s.Ts(t.$from,l.resolve(t.$to.end(t.depth)),t.depth)),a=!0}let c=(0,r.nd)(o,n,i,t);return!!c&&(e&&function(e,t,n,i,a){let o=s.HY.empty;for(let e=n.length-1;e>=0;e--)o=s.HY.from(n[e].type.create(n[e].attrs,o));e.step(new r.FC(t.start-(i?2:0),t.end,t.start,t.end,new s.p2(o,0,0),n.length,!0));let l=0;for(let e=0;e<n.length;e++)n[e].type==a&&(l=e+1);let c=n.length-l,u=t.start+n.length-(i?2:0),h=t.parent;for(let n=t.startIndex,s=t.endIndex,i=!0;n<s;n++,i=!1)!i&&(0,r.Ax)(e.doc,u,c)&&(e.split(u,c),u+=2*c),u+=h.child(n).nodeSize}(e,t,c,a,n),!0)}(c,l,e,t)&&(i&&i(c.scrollIntoView()),!0)}}function a(e){return function(t,n){let{$from:i,$to:a}=t.selection,o=i.blockRange(a,t=>t.childCount>0&&t.firstChild.type==e);return!!o&&(!n||(i.node(o.depth-1).type==e?function(e,t,n,i){let a=e.tr,o=i.end,l=i.$to.end(i.depth);o<l&&(a.step(new r.FC(o-1,l,o,l,new s.p2(s.HY.from(n.create(null,i.parent.copy())),1,0),1,!0)),i=new s.Ts(a.doc.resolve(i.$from.pos),a.doc.resolve(l),i.depth));let c=(0,r.k9)(i);if(null==c)return!1;a.lift(i,c);let u=a.doc.resolve(a.mapping.map(o,-1)-1);return(0,r.Mn)(a.doc,u.pos)&&u.nodeBefore.type==u.nodeAfter.type&&a.join(u.pos),t(a.scrollIntoView()),!0}(t,n,e,o):function(e,t,n){let i=e.tr,a=n.parent;for(let e=n.end,t=n.endIndex-1,r=n.startIndex;t>r;t--)e-=a.child(t).nodeSize,i.delete(e-1,e+1);let o=i.doc.resolve(n.start),l=o.nodeAfter;if(i.mapping.map(n.end)!=n.start+o.nodeAfter.nodeSize)return!1;let c=0==n.startIndex,u=n.endIndex==a.childCount,h=o.node(-1),d=o.index(-1);if(!h.canReplace(d+(c?0:1),d+1,l.content.append(u?s.HY.empty:s.HY.from(a))))return!1;let p=o.pos,f=p+l.nodeSize;return i.step(new r.FC(p-(c?1:0),f+(u?1:0),p+1,f-1,new s.p2((c?s.HY.empty:s.HY.from(a.copy(s.HY.empty))).append(u?s.HY.empty:s.HY.from(a.copy(s.HY.empty))),c?0:1,u?0:1),c?0:1)),t(i.scrollIntoView()),!0}(t,n,o)))}}function o(e){return function(t,n){let{$from:i,$to:a}=t.selection,o=i.blockRange(a,t=>t.childCount>0&&t.firstChild.type==e);if(!o)return!1;let l=o.startIndex;if(0==l)return!1;let c=o.parent,u=c.child(l-1);if(u.type!=e)return!1;if(n){let i=u.lastChild&&u.lastChild.type==c.type,a=s.HY.from(i?e.create():null),l=new s.p2(s.HY.from(e.create(null,s.HY.from(c.type.create(null,a)))),i?3:1,0),h=o.start,d=o.end;n(t.tr.step(new r.FC(h-(i?3:1),d,h,d,l,1,!0)).scrollIntoView())}return!0}}},28201:function(e,t,n){"use strict";n.d(t,{Bs:function(){return u},C1:function(){return f},H$:function(){return b},Sy:function(){return C},Y1:function(){return a},qv:function(){return d},yy:function(){return k}});var r=n(77247),s=n(67120);let i=Object.create(null);class a{constructor(e,t,n){this.$anchor=e,this.$head=t,this.ranges=n||[new o(e.min(t),e.max(t))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,t=r.p2.empty){let n=t.content.lastChild,s=null;for(let e=0;e<t.openEnd;e++)s=n,n=n.lastChild;let i=e.steps.length,a=this.ranges;for(let o=0;o<a.length;o++){let{$from:l,$to:c}=a[o],u=e.mapping.slice(i);e.replaceRange(u.map(l.pos),u.map(c.pos),o?r.p2.empty:t),0==o&&E(e,i,(n?n.isInline:s&&s.isTextblock)?-1:1)}}replaceWith(e,t){let n=e.steps.length,r=this.ranges;for(let s=0;s<r.length;s++){let{$from:i,$to:a}=r[s],o=e.mapping.slice(n),l=o.map(i.pos),c=o.map(a.pos);s?e.deleteRange(l,c):(e.replaceRangeWith(l,c,t),E(e,n,t.isInline?-1:1))}}static findFrom(e,t,n=!1){let r=e.parent.inlineContent?new u(e):g(e.node(0),e.parent,e.pos,e.index(),t,n);if(r)return r;for(let r=e.depth-1;r>=0;r--){let s=t<0?g(e.node(0),e.node(r),e.before(r+1),e.index(r),t,n):g(e.node(0),e.node(r),e.after(r+1),e.index(r)+1,t,n);if(s)return s}return null}static near(e,t=1){return this.findFrom(e,t)||this.findFrom(e,-t)||new f(e.node(0))}static atStart(e){return g(e,e,0,0,1)||new f(e)}static atEnd(e){return g(e,e,e.content.size,e.childCount,-1)||new f(e)}static fromJSON(e,t){if(!t||!t.type)throw RangeError("Invalid input for Selection.fromJSON");let n=i[t.type];if(!n)throw RangeError(`No selection type ${t.type} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in i)throw RangeError("Duplicate use of selection JSON ID "+e);return i[e]=t,t.prototype.jsonID=e,t}getBookmark(){return u.between(this.$anchor,this.$head).getBookmark()}}a.prototype.visible=!0;class o{constructor(e,t){this.$from=e,this.$to=t}}let l=!1;function c(e){l||e.parent.inlineContent||(l=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+e.parent.type.name+")"))}class u extends a{constructor(e,t=e){c(e),c(t),super(e,t)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,t){let n=e.resolve(t.map(this.head));if(!n.parent.inlineContent)return a.near(n);let r=e.resolve(t.map(this.anchor));return new u(r.parent.inlineContent?r:n,n)}replace(e,t=r.p2.empty){if(super.replace(e,t),t==r.p2.empty){let t=this.$from.marksAcross(this.$to);t&&e.ensureMarks(t)}}eq(e){return e instanceof u&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new h(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,t){if("number"!=typeof t.anchor||"number"!=typeof t.head)throw RangeError("Invalid input for TextSelection.fromJSON");return new u(e.resolve(t.anchor),e.resolve(t.head))}static create(e,t,n=t){let r=e.resolve(t);return new this(r,n==t?r:e.resolve(n))}static between(e,t,n){let r=e.pos-t.pos;if((!n||r)&&(n=r>=0?1:-1),!t.parent.inlineContent){let e=a.findFrom(t,n,!0)||a.findFrom(t,-n,!0);if(!e)return a.near(t,n);t=e.$head}return e.parent.inlineContent||(0==r?e=t:(e=(a.findFrom(e,-n,!0)||a.findFrom(e,n,!0)).$anchor).pos<t.pos==r<0||(e=t)),new u(e,t)}}a.jsonID("text",u);class h{constructor(e,t){this.anchor=e,this.head=t}map(e){return new h(e.map(this.anchor),e.map(this.head))}resolve(e){return u.between(e.resolve(this.anchor),e.resolve(this.head))}}class d extends a{constructor(e){let t=e.nodeAfter;super(e,e.node(0).resolve(e.pos+t.nodeSize)),this.node=t}map(e,t){let{deleted:n,pos:r}=t.mapResult(this.anchor),s=e.resolve(r);return n?a.near(s):new d(s)}content(){return new r.p2(r.HY.from(this.node),0,0)}eq(e){return e instanceof d&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new p(this.anchor)}static fromJSON(e,t){if("number"!=typeof t.anchor)throw RangeError("Invalid input for NodeSelection.fromJSON");return new d(e.resolve(t.anchor))}static create(e,t){return new d(e.resolve(t))}static isSelectable(e){return!e.isText&&!1!==e.type.spec.selectable}}d.prototype.visible=!1,a.jsonID("node",d);class p{constructor(e){this.anchor=e}map(e){let{deleted:t,pos:n}=e.mapResult(this.anchor);return t?new h(n,n):new p(n)}resolve(e){let t=e.resolve(this.anchor),n=t.nodeAfter;return n&&d.isSelectable(n)?new d(t):a.near(t)}}class f extends a{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,t=r.p2.empty){if(t==r.p2.empty){e.delete(0,e.doc.content.size);let t=a.atStart(e.doc);t.eq(e.selection)||e.setSelection(t)}else super.replace(e,t)}toJSON(){return{type:"all"}}static fromJSON(e){return new f(e)}map(e){return new f(e)}eq(e){return e instanceof f}getBookmark(){return m}}a.jsonID("all",f);let m={map(){return this},resolve:e=>new f(e)};function g(e,t,n,r,s,i=!1){if(t.inlineContent)return u.create(e,n);for(let a=r-(s>0?0:1);s>0?a<t.childCount:a>=0;a+=s){let r=t.child(a);if(r.isAtom){if(!i&&d.isSelectable(r))return d.create(e,n-(s<0?r.nodeSize:0))}else{let t=g(e,r,n+s,s<0?r.childCount:0,s,i);if(t)return t}n+=r.nodeSize*s}return null}function E(e,t,n){let r,i=e.steps.length-1;if(i<t)return;let o=e.steps[i];(o instanceof s.Pu||o instanceof s.FC)&&(e.mapping.maps[i].forEach((e,t,n,s)=>{null==r&&(r=s)}),e.setSelection(a.near(e.doc.resolve(r),n)))}class T extends s.wx{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=(1|this.updated)&-3,this.storedMarks=null,this}get selectionSet(){return(1&this.updated)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=2,this}ensureMarks(e){return r.vc.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(2&this.updated)>0}addStep(e,t){super.addStep(e,t),this.updated=-3&this.updated,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,t=!0){let n=this.selection;return t&&(e=e.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||r.vc.none))),n.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,t,n){let r=this.doc.type.schema;if(null==t)return e?this.replaceSelectionWith(r.text(e),!0):this.deleteSelection();{if(null==n&&(n=t),n=null==n?t:n,!e)return this.deleteRange(t,n);let s=this.storedMarks;if(!s){let e=this.doc.resolve(t);s=n==t?e.marks():e.marksAcross(this.doc.resolve(n))}return this.replaceRangeWith(t,n,r.text(e,s)),this.selection.empty||this.setSelection(a.near(this.selection.$to)),this}}setMeta(e,t){return this.meta["string"==typeof e?e:e.key]=t,this}getMeta(e){return this.meta["string"==typeof e?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=4,this}get scrolledIntoView(){return(4&this.updated)>0}}function A(e,t){return t&&e?e.bind(t):e}class _{constructor(e,t,n){this.name=e,this.init=A(t.init,n),this.apply=A(t.apply,n)}}let S=[new _("doc",{init:e=>e.doc||e.schema.topNodeType.createAndFill(),apply:e=>e.doc}),new _("selection",{init:(e,t)=>e.selection||a.atStart(t.doc),apply:e=>e.selection}),new _("storedMarks",{init:e=>e.storedMarks||null,apply:(e,t,n,r)=>r.selection.$cursor?e.storedMarks:null}),new _("scrollToSelection",{init:()=>0,apply:(e,t)=>e.scrolledIntoView?t+1:t})];class y{constructor(e,t){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=S.slice(),t&&t.forEach(e=>{if(this.pluginsByKey[e.key])throw RangeError("Adding different instances of a keyed plugin ("+e.key+")");this.plugins.push(e),this.pluginsByKey[e.key]=e,e.spec.state&&this.fields.push(new _(e.key,e.spec.state,e))})}}class k{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,t=-1){for(let n=0;n<this.config.plugins.length;n++)if(n!=t){let t=this.config.plugins[n];if(t.spec.filterTransaction&&!t.spec.filterTransaction.call(t,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let t=[e],n=this.applyInner(e),r=null;for(;;){let s=!1;for(let i=0;i<this.config.plugins.length;i++){let a=this.config.plugins[i];if(a.spec.appendTransaction){let o=r?r[i].n:0,l=r?r[i].state:this,c=o<t.length&&a.spec.appendTransaction.call(a,o?t.slice(o):t,l,n);if(c&&n.filterTransaction(c,i)){if(c.setMeta("appendedTransaction",e),!r){r=[];for(let e=0;e<this.config.plugins.length;e++)r.push(e<i?{state:n,n:t.length}:{state:this,n:0})}t.push(c),n=n.applyInner(c),s=!0}r&&(r[i]={state:n,n:t.length})}}if(!s)return{state:n,transactions:t}}}applyInner(e){if(!e.before.eq(this.doc))throw RangeError("Applying a mismatched transaction");let t=new k(this.config),n=this.config.fields;for(let r=0;r<n.length;r++){let s=n[r];t[s.name]=s.apply(e,this[s.name],this,t)}return t}get tr(){return new T(this)}static create(e){let t=new y(e.doc?e.doc.type.schema:e.schema,e.plugins),n=new k(t);for(let r=0;r<t.fields.length;r++)n[t.fields[r].name]=t.fields[r].init(e,n);return n}reconfigure(e){let t=new y(this.schema,e.plugins),n=t.fields,r=new k(t);for(let t=0;t<n.length;t++){let s=n[t].name;r[s]=this.hasOwnProperty(s)?this[s]:n[t].init(e,r)}return r}toJSON(e){let t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map(e=>e.toJSON())),e&&"object"==typeof e)for(let n in e){if("doc"==n||"selection"==n)throw RangeError("The JSON fields `doc` and `selection` are reserved");let r=e[n],s=r.spec.state;s&&s.toJSON&&(t[n]=s.toJSON.call(r,this[r.key]))}return t}static fromJSON(e,t,n){if(!t)throw RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw RangeError("Required config field 'schema' missing");let s=new y(e.schema,e.plugins),i=new k(s);return s.fields.forEach(s=>{if("doc"==s.name)i.doc=r.NB.fromJSON(e.schema,t.doc);else if("selection"==s.name)i.selection=a.fromJSON(i.doc,t.selection);else if("storedMarks"==s.name)t.storedMarks&&(i.storedMarks=t.storedMarks.map(e.schema.markFromJSON));else{if(n)for(let r in n){let a=n[r],o=a.spec.state;if(a.key==s.name&&o&&o.fromJSON&&Object.prototype.hasOwnProperty.call(t,r)){i[s.name]=o.fromJSON.call(a,e,t[r],i);return}}i[s.name]=s.init(e,i)}}),i}}class C{constructor(e){this.spec=e,this.props={},e.props&&function e(t,n,r){for(let s in t){let i=t[s];i instanceof Function?i=i.bind(n):"handleDOMEvents"==s&&(i=e(i,n,{})),r[s]=i}return r}(e.props,this,this.props),this.key=e.key?e.key.key:I("plugin")}getState(e){return e[this.key]}}let N=Object.create(null);function I(e){return e in N?e+"$"+ ++N[e]:(N[e]=0,e+"$")}class b{constructor(e="key"){this.key=I(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}}},67120:function(e,t,n){"use strict";n.d(t,{Ax:function(){return C},FC:function(){return g},GJ:function(){return b},Mn:function(){return N},Pu:function(){return m},dR:function(){return D},k9:function(){return A},nd:function(){return _},nj:function(){return O},vs:function(){return a},wx:function(){return U}});var r=n(77247);class s{constructor(e,t,n){this.pos=e,this.delInfo=t,this.recover=n}get deleted(){return(8&this.delInfo)>0}get deletedBefore(){return(5&this.delInfo)>0}get deletedAfter(){return(6&this.delInfo)>0}get deletedAcross(){return(4&this.delInfo)>0}}class i{constructor(e,t=!1){if(this.ranges=e,this.inverted=t,!e.length&&i.empty)return i.empty}recover(e){let t=0,n=65535&e;if(!this.inverted)for(let e=0;e<n;e++)t+=this.ranges[3*e+2]-this.ranges[3*e+1];return this.ranges[3*n]+t+(e-(65535&e))/65536}mapResult(e,t=1){return this._map(e,t,!1)}map(e,t=1){return this._map(e,t,!0)}_map(e,t,n){let r=0,i=this.inverted?2:1,a=this.inverted?1:2;for(let o=0;o<this.ranges.length;o+=3){let l=this.ranges[o]-(this.inverted?r:0);if(l>e)break;let c=this.ranges[o+i],u=this.ranges[o+a],h=l+c;if(e<=h){let i=c?e==l?-1:e==h?1:t:t,a=l+r+(i<0?0:u);if(n)return a;let d=e==(t<0?l:h)?null:o/3+(e-l)*65536,p=e==l?2:e==h?1:4;return(t<0?e!=l:e!=h)&&(p|=8),new s(a,p,d)}r+=u-c}return n?e+r:new s(e+r,0,null)}touches(e,t){let n=0,r=65535&t,s=this.inverted?2:1,i=this.inverted?1:2;for(let t=0;t<this.ranges.length;t+=3){let a=this.ranges[t]-(this.inverted?n:0);if(a>e)break;let o=this.ranges[t+s];if(e<=a+o&&t==3*r)return!0;n+=this.ranges[t+i]-o}return!1}forEach(e){let t=this.inverted?2:1,n=this.inverted?1:2;for(let r=0,s=0;r<this.ranges.length;r+=3){let i=this.ranges[r],a=i-(this.inverted?s:0),o=i+(this.inverted?0:s),l=this.ranges[r+t],c=this.ranges[r+n];e(a,a+l,o,o+c),s+=c-l}}invert(){return new i(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return 0==e?i.empty:new i(e<0?[0,-e,0]:[0,0,e])}}i.empty=new i([]);class a{constructor(e,t,n=0,r=e?e.length:0){this.mirror=t,this.from=n,this.to=r,this._maps=e||[],this.ownData=!(e||t)}get maps(){return this._maps}slice(e=0,t=this.maps.length){return new a(this._maps,this.mirror,e,t)}appendMap(e,t){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(e),null!=t&&this.setMirror(this._maps.length-1,t)}appendMapping(e){for(let t=0,n=this._maps.length;t<e._maps.length;t++){let r=e.getMirror(t);this.appendMap(e._maps[t],null!=r&&r<t?n+r:void 0)}}getMirror(e){if(this.mirror){for(let t=0;t<this.mirror.length;t++)if(this.mirror[t]==e)return this.mirror[t+(t%2?-1:1)]}}setMirror(e,t){this.mirror||(this.mirror=[]),this.mirror.push(e,t)}appendMappingInverted(e){for(let t=e.maps.length-1,n=this._maps.length+e._maps.length;t>=0;t--){let r=e.getMirror(t);this.appendMap(e._maps[t].invert(),null!=r&&r>t?n-r-1:void 0)}}invert(){let e=new a;return e.appendMappingInverted(this),e}map(e,t=1){if(this.mirror)return this._map(e,t,!0);for(let n=this.from;n<this.to;n++)e=this._maps[n].map(e,t);return e}mapResult(e,t=1){return this._map(e,t,!1)}_map(e,t,n){let r=0;for(let n=this.from;n<this.to;n++){let s=this._maps[n].mapResult(e,t);if(null!=s.recover){let t=this.getMirror(n);if(null!=t&&t>n&&t<this.to){n=t,e=this._maps[t].recover(s.recover);continue}}r|=s.delInfo,e=s.pos}return n?e:new s(e,r,null)}}let o=Object.create(null);class l{getMap(){return i.empty}merge(e){return null}static fromJSON(e,t){if(!t||!t.stepType)throw RangeError("Invalid input for Step.fromJSON");let n=o[t.stepType];if(!n)throw RangeError(`No step type ${t.stepType} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in o)throw RangeError("Duplicate use of step JSON ID "+e);return o[e]=t,t.prototype.jsonID=e,t}}class c{constructor(e,t){this.doc=e,this.failed=t}static ok(e){return new c(e,null)}static fail(e){return new c(null,e)}static fromReplace(e,t,n,s){try{return c.ok(e.replace(t,n,s))}catch(e){if(e instanceof r.e4)return c.fail(e.message);throw e}}}function u(e,t,n){let s=[];for(let r=0;r<e.childCount;r++){let i=e.child(r);i.content.size&&(i=i.copy(u(i.content,t,i))),i.isInline&&(i=t(i,n,r)),s.push(i)}return r.HY.fromArray(s)}class h extends l{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=e.resolve(this.from),s=n.node(n.sharedDepth(this.to)),i=new r.p2(u(t.content,(e,t)=>e.isAtom&&t.type.allowsMarkType(this.mark.type)?e.mark(this.mark.addToSet(e.marks)):e,s),t.openStart,t.openEnd);return c.fromReplace(e,this.from,this.to,i)}invert(){return new d(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new h(t.pos,n.pos,this.mark)}merge(e){return e instanceof h&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new h(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw RangeError("Invalid input for AddMarkStep.fromJSON");return new h(t.from,t.to,e.markFromJSON(t.mark))}}l.jsonID("addMark",h);class d extends l{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=new r.p2(u(t.content,e=>e.mark(this.mark.removeFromSet(e.marks)),e),t.openStart,t.openEnd);return c.fromReplace(e,this.from,this.to,n)}invert(){return new h(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new d(t.pos,n.pos,this.mark)}merge(e){return e instanceof d&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new d(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw RangeError("Invalid input for RemoveMarkStep.fromJSON");return new d(t.from,t.to,e.markFromJSON(t.mark))}}l.jsonID("removeMark",d);class p extends l{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return c.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.addToSet(t.marks));return c.fromReplace(e,this.pos,this.pos+1,new r.p2(r.HY.from(n),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);if(t){let e=this.mark.addToSet(t.marks);if(e.length==t.marks.length){for(let n=0;n<t.marks.length;n++)if(!t.marks[n].isInSet(e))return new p(this.pos,t.marks[n]);return new p(this.pos,this.mark)}}return new f(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new p(t.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if("number"!=typeof t.pos)throw RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new p(t.pos,e.markFromJSON(t.mark))}}l.jsonID("addNodeMark",p);class f extends l{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return c.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.removeFromSet(t.marks));return c.fromReplace(e,this.pos,this.pos+1,new r.p2(r.HY.from(n),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);return t&&this.mark.isInSet(t.marks)?new p(this.pos,this.mark):this}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new f(t.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if("number"!=typeof t.pos)throw RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new f(t.pos,e.markFromJSON(t.mark))}}l.jsonID("removeNodeMark",f);class m extends l{constructor(e,t,n,r=!1){super(),this.from=e,this.to=t,this.slice=n,this.structure=r}apply(e){return this.structure&&E(e,this.from,this.to)?c.fail("Structure replace would overwrite content"):c.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new i([this.from,this.to-this.from,this.slice.size])}invert(e){return new m(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deletedAcross&&n.deletedAcross?null:new m(t.pos,Math.max(t.pos,n.pos),this.slice,this.structure)}merge(e){if(!(e instanceof m)||e.structure||this.structure)return null;if(this.from+this.slice.size!=e.from||this.slice.openEnd||e.slice.openStart){if(e.to!=this.from||this.slice.openStart||e.slice.openEnd)return null;{let t=this.slice.size+e.slice.size==0?r.p2.empty:new r.p2(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new m(e.from,this.to,t,this.structure)}}{let t=this.slice.size+e.slice.size==0?r.p2.empty:new r.p2(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new m(this.from,this.to+(e.to-e.from),t,this.structure)}}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw RangeError("Invalid input for ReplaceStep.fromJSON");return new m(t.from,t.to,r.p2.fromJSON(e,t.slice),!!t.structure)}}l.jsonID("replace",m);class g extends l{constructor(e,t,n,r,s,i,a=!1){super(),this.from=e,this.to=t,this.gapFrom=n,this.gapTo=r,this.slice=s,this.insert=i,this.structure=a}apply(e){if(this.structure&&(E(e,this.from,this.gapFrom)||E(e,this.gapTo,this.to)))return c.fail("Structure gap-replace would overwrite content");let t=e.slice(this.gapFrom,this.gapTo);if(t.openStart||t.openEnd)return c.fail("Gap is not a flat range");let n=this.slice.insertAt(this.insert,t.content);return n?c.fromReplace(e,this.from,this.to,n):c.fail("Content does not fit in gap")}getMap(){return new i([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let t=this.gapTo-this.gapFrom;return new g(this.from,this.from+this.slice.size+t,this.from+this.insert,this.from+this.insert+t,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1),r=this.from==this.gapFrom?t.pos:e.map(this.gapFrom,-1),s=this.to==this.gapTo?n.pos:e.map(this.gapTo,1);return t.deletedAcross&&n.deletedAcross||r<t.pos||s>n.pos?null:new g(t.pos,n.pos,r,s,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to||"number"!=typeof t.gapFrom||"number"!=typeof t.gapTo||"number"!=typeof t.insert)throw RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new g(t.from,t.to,t.gapFrom,t.gapTo,r.p2.fromJSON(e,t.slice),t.insert,!!t.structure)}}function E(e,t,n){let r=e.resolve(t),s=n-t,i=r.depth;for(;s>0&&i>0&&r.indexAfter(i)==r.node(i).childCount;)i--,s--;if(s>0){let e=r.node(i).maybeChild(r.indexAfter(i));for(;s>0;){if(!e||e.isLeaf)return!0;e=e.firstChild,s--}}return!1}function T(e,t,n,s=n.contentMatch,i=!0){let a=e.doc.nodeAt(t),o=[],l=t+1;for(let t=0;t<a.childCount;t++){let c=a.child(t),u=l+c.nodeSize,h=s.matchType(c.type);if(h){s=h;for(let t=0;t<c.marks.length;t++)n.allowsMarkType(c.marks[t].type)||e.step(new d(l,u,c.marks[t]));if(i&&c.isText&&"pre"!=n.whitespace){let e,t=/\r?\n|\r/g,s;for(;e=t.exec(c.text);)s||(s=new r.p2(r.HY.from(n.schema.text(" ",n.allowedMarks(c.marks))),0,0)),o.push(new m(l+e.index,l+e.index+e[0].length,s))}}else o.push(new m(l,u,r.p2.empty));l=u}if(!s.validEnd){let t=s.fillBefore(r.HY.empty,!0);e.replace(l,l,new r.p2(t,0,0))}for(let t=o.length-1;t>=0;t--)e.step(o[t])}function A(e){let t=e.parent.content.cutByIndex(e.startIndex,e.endIndex);for(let n=e.depth;;--n){let r=e.$from.node(n),s=e.$from.index(n),i=e.$to.indexAfter(n);if(n<e.depth&&r.canReplace(s,i,t))return n;if(0==n||r.type.spec.isolating||!((0==s||r.canReplace(s,r.childCount))&&(i==r.childCount||r.canReplace(0,i))))break}return null}function _(e,t,n=null,r=e){let s=function(e,t){let{parent:n,startIndex:r,endIndex:s}=e,i=n.contentMatchAt(r).findWrapping(t);if(!i)return null;let a=i.length?i[0]:t;return n.canReplaceWith(r,s,a)?i:null}(e,t),i=s&&function(e,t){let{parent:n,startIndex:r,endIndex:s}=e,i=n.child(r),a=t.contentMatch.findWrapping(i.type);if(!a)return null;let o=(a.length?a[a.length-1]:t).contentMatch;for(let e=r;o&&e<s;e++)o=o.matchType(n.child(e).type);return o&&o.validEnd?a:null}(r,t);return i?s.map(S).concat({type:t,attrs:n}).concat(i.map(S)):null}function S(e){return{type:e,attrs:null}}function y(e,t,n,r){t.forEach((s,i)=>{if(s.isText){let a,o=/\r?\n|\r/g;for(;a=o.exec(s.text);){let s=e.mapping.slice(r).map(n+1+i+a.index);e.replaceWith(s,s+1,t.type.schema.linebreakReplacement.create())}}})}function k(e,t,n,r){t.forEach((s,i)=>{if(s.type==s.type.schema.linebreakReplacement){let s=e.mapping.slice(r).map(n+1+i);e.replaceWith(s,s+1,t.type.schema.text("\n"))}})}function C(e,t,n=1,r){let s=e.resolve(t),i=s.depth-n,a=r&&r[r.length-1]||s.parent;if(i<0||s.parent.type.spec.isolating||!s.parent.canReplace(s.index(),s.parent.childCount)||!a.type.validContent(s.parent.content.cutByIndex(s.index(),s.parent.childCount)))return!1;for(let e=s.depth-1,t=n-2;e>i;e--,t--){let n=s.node(e),i=s.index(e);if(n.type.spec.isolating)return!1;let a=n.content.cutByIndex(i,n.childCount),o=r&&r[t+1];o&&(a=a.replaceChild(0,o.type.create(o.attrs)));let l=r&&r[t]||n;if(!n.canReplace(i+1,n.childCount)||!l.type.validContent(a))return!1}let o=s.indexAfter(i),l=r&&r[0];return s.node(i).canReplaceWith(o,o,l?l.type:s.node(i+1).type)}function N(e,t){let n=e.resolve(t),r=n.index();return I(n.nodeBefore,n.nodeAfter)&&n.parent.canReplace(r,r+1)}function I(e,t){return!!(e&&t&&!e.isLeaf&&function(e,t){t.content.size||e.type.compatibleContent(t.type);let n=e.contentMatchAt(e.childCount),{linebreakReplacement:r}=e.type.schema;for(let s=0;s<t.childCount;s++){let i=t.child(s),a=i.type==r?e.type.schema.nodes.text:i.type;if(!(n=n.matchType(a))||!e.type.allowsMarks(i.marks))return!1}return n.validEnd}(e,t))}function b(e,t,n=-1){let r=e.resolve(t);for(let e=r.depth;;e--){let s,i,a=r.index(e);if(e==r.depth?(s=r.nodeBefore,i=r.nodeAfter):n>0?(s=r.node(e+1),a++,i=r.node(e).maybeChild(a)):(s=r.node(e).maybeChild(a-1),i=r.node(e+1)),s&&!s.isTextblock&&I(s,i)&&r.node(e).canReplace(a,a+1))return t;if(0==e)break;t=n<0?r.before(e):r.after(e)}}function O(e,t,n){let r=e.resolve(t);if(!n.content.size)return t;let s=n.content;for(let e=0;e<n.openStart;e++)s=s.firstChild.content;for(let e=1;e<=(0==n.openStart&&n.size?2:1);e++)for(let t=r.depth;t>=0;t--){let n=t==r.depth?0:r.pos<=(r.start(t+1)+r.end(t+1))/2?-1:1,i=r.index(t)+(n>0?1:0),a=r.node(t),o=!1;if(1==e)o=a.canReplace(i,i,s);else{let e=a.contentMatchAt(i).findWrapping(s.firstChild.type);o=e&&a.canReplaceWith(i,i,e[0])}if(o)return 0==n?r.pos:n<0?r.before(t+1):r.after(t+1)}return null}function D(e,t,n=t,s=r.p2.empty){if(t==n&&!s.size)return null;let i=e.resolve(t),a=e.resolve(n);return R(i,a,s)?new m(t,n,s):new M(i,a,s).fit()}function R(e,t,n){return!n.openStart&&!n.openEnd&&e.start()==t.start()&&e.parent.canReplace(e.index(),t.index(),n.content)}l.jsonID("replaceAround",g);class M{constructor(e,t,n){this.$from=e,this.$to=t,this.unplaced=n,this.frontier=[],this.placed=r.HY.empty;for(let t=0;t<=e.depth;t++){let n=e.node(t);this.frontier.push({type:n.type,match:n.contentMatchAt(e.indexAfter(t))})}for(let t=e.depth;t>0;t--)this.placed=r.HY.from(e.node(t).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let e=this.findFittable();e?this.placeNodes(e):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),t=this.placed.size-this.depth-this.$from.depth,n=this.$from,s=this.close(e<0?this.$to:n.doc.resolve(e));if(!s)return null;let i=this.placed,a=n.depth,o=s.depth;for(;a&&o&&1==i.childCount;)i=i.firstChild.content,a--,o--;let l=new r.p2(i,a,o);return e>-1?new g(n.pos,e,this.$to.pos,this.$to.end(),l,t):l.size||n.pos!=this.$to.pos?new m(n.pos,s.pos,l):null}findFittable(){let e=this.unplaced.openStart;for(let t=this.unplaced.content,n=0,r=this.unplaced.openEnd;n<e;n++){let s=t.firstChild;if(t.childCount>1&&(r=0),s.type.spec.isolating&&r<=n){e=n;break}t=s.content}for(let t=1;t<=2;t++)for(let n=1==t?e:this.unplaced.openStart;n>=0;n--){let e=null,s=(n?(e=w(this.unplaced.content,n-1).firstChild).content:this.unplaced.content).firstChild;for(let i=this.depth;i>=0;i--){let{type:a,match:o}=this.frontier[i],l,c=null;if(1==t&&(s?o.matchType(s.type)||(c=o.fillBefore(r.HY.from(s),!1)):e&&a.compatibleContent(e.type)))return{sliceDepth:n,frontierDepth:i,parent:e,inject:c};if(2==t&&s&&(l=o.findWrapping(s.type)))return{sliceDepth:n,frontierDepth:i,parent:e,wrap:l};if(e&&o.matchType(e.type))break}}}openMore(){let{content:e,openStart:t,openEnd:n}=this.unplaced,s=w(e,t);return!!s.childCount&&!s.firstChild.isLeaf&&(this.unplaced=new r.p2(e,t+1,Math.max(n,s.size+t>=e.size-n?t+1:0)),!0)}dropNode(){let{content:e,openStart:t,openEnd:n}=this.unplaced,s=w(e,t);if(s.childCount<=1&&t>0){let i=e.size-t<=t+s.size;this.unplaced=new r.p2(v(e,t-1,1),t-1,i?t-1:n)}else this.unplaced=new r.p2(v(e,t,1),t,n)}placeNodes({sliceDepth:e,frontierDepth:t,parent:n,inject:s,wrap:i}){for(;this.depth>t;)this.closeFrontierNode();if(i)for(let e=0;e<i.length;e++)this.openFrontierNode(i[e]);let a=this.unplaced,o=n?n.content:a.content,l=a.openStart-e,c=0,u=[],{match:h,type:d}=this.frontier[t];if(s){for(let e=0;e<s.childCount;e++)u.push(s.child(e));h=h.matchFragment(s)}let p=o.size+e-(a.content.size-a.openEnd);for(;c<o.childCount;){let e=o.child(c),t=h.matchType(e.type);if(!t)break;(++c>1||0==l||e.content.size)&&(h=t,u.push(function e(t,n,s){if(n<=0)return t;let i=t.content;return n>1&&(i=i.replaceChild(0,e(i.firstChild,n-1,1==i.childCount?s-1:0))),n>0&&(i=t.type.contentMatch.fillBefore(i).append(i),s<=0&&(i=i.append(t.type.contentMatch.matchFragment(i).fillBefore(r.HY.empty,!0)))),t.copy(i)}(e.mark(d.allowedMarks(e.marks)),1==c?l:0,c==o.childCount?p:-1)))}let f=c==o.childCount;f||(p=-1),this.placed=L(this.placed,t,r.HY.from(u)),this.frontier[t].match=h,f&&p<0&&n&&n.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let e=0,t=o;e<p;e++){let e=t.lastChild;this.frontier.push({type:e.type,match:e.contentMatchAt(e.childCount)}),t=e.content}this.unplaced=f?0==e?r.p2.empty:new r.p2(v(a.content,e-1,1),e-1,p<0?a.openEnd:e-1):new r.p2(v(a.content,e,c),a.openStart,a.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return -1;let e=this.frontier[this.depth],t;if(!e.type.isTextblock||!P(this.$to,this.$to.depth,e.type,e.match,!1)||this.$to.depth==this.depth&&(t=this.findCloseLevel(this.$to))&&t.depth==this.depth)return -1;let{depth:n}=this.$to,r=this.$to.after(n);for(;n>1&&r==this.$to.end(--n);)++r;return r}findCloseLevel(e){n:for(let t=Math.min(this.depth,e.depth);t>=0;t--){let{match:n,type:r}=this.frontier[t],s=t<e.depth&&e.end(t+1)==e.pos+(e.depth-(t+1)),i=P(e,t,r,n,s);if(i){for(let n=t-1;n>=0;n--){let{match:t,type:r}=this.frontier[n],s=P(e,n,r,t,!0);if(!s||s.childCount)continue n}return{depth:t,fit:i,move:s?e.doc.resolve(e.after(t+1)):e}}}}close(e){let t=this.findCloseLevel(e);if(!t)return null;for(;this.depth>t.depth;)this.closeFrontierNode();t.fit.childCount&&(this.placed=L(this.placed,t.depth,t.fit)),e=t.move;for(let n=t.depth+1;n<=e.depth;n++){let t=e.node(n),r=t.type.contentMatch.fillBefore(t.content,!0,e.index(n));this.openFrontierNode(t.type,t.attrs,r)}return e}openFrontierNode(e,t=null,n){let s=this.frontier[this.depth];s.match=s.match.matchType(e),this.placed=L(this.placed,this.depth,r.HY.from(e.create(t,n))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let e=this.frontier.pop().match.fillBefore(r.HY.empty,!0);e.childCount&&(this.placed=L(this.placed,this.frontier.length,e))}}function v(e,t,n){return 0==t?e.cutByIndex(n,e.childCount):e.replaceChild(0,e.firstChild.copy(v(e.firstChild.content,t-1,n)))}function L(e,t,n){return 0==t?e.append(n):e.replaceChild(e.childCount-1,e.lastChild.copy(L(e.lastChild.content,t-1,n)))}function w(e,t){for(let n=0;n<t;n++)e=e.firstChild.content;return e}function P(e,t,n,r,s){let i=e.node(t),a=s?e.indexAfter(t):e.index(t);if(a==i.childCount&&!n.compatibleContent(i.type))return null;let o=r.fillBefore(i.content,!0,a);return o&&!function(e,t,n){for(let r=n;r<t.childCount;r++)if(!e.allowsMarks(t.child(r).marks))return!0;return!1}(n,i.content,a)?o:null}function x(e,t){let n=[],r=Math.min(e.depth,t.depth);for(let s=r;s>=0;s--){let r=e.start(s);if(r<e.pos-(e.depth-s)||t.end(s)>t.pos+(t.depth-s)||e.node(s).type.spec.isolating||t.node(s).type.spec.isolating)break;(r==t.start(s)||s==e.depth&&s==t.depth&&e.parent.inlineContent&&t.parent.inlineContent&&s&&t.start(s-1)==r-1)&&n.push(s)}return n}class F extends l{constructor(e,t,n){super(),this.pos=e,this.attr=t,this.value=n}apply(e){let t=e.nodeAt(this.pos);if(!t)return c.fail("No node at attribute step's position");let n=Object.create(null);for(let e in t.attrs)n[e]=t.attrs[e];n[this.attr]=this.value;let s=t.type.create(n,null,t.marks);return c.fromReplace(e,this.pos,this.pos+1,new r.p2(r.HY.from(s),0,t.isLeaf?0:1))}getMap(){return i.empty}invert(e){return new F(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new F(t.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,t){if("number"!=typeof t.pos||"string"!=typeof t.attr)throw RangeError("Invalid input for AttrStep.fromJSON");return new F(t.pos,t.attr,t.value)}}l.jsonID("attr",F);class B extends l{constructor(e,t){super(),this.attr=e,this.value=t}apply(e){let t=Object.create(null);for(let n in e.attrs)t[n]=e.attrs[n];t[this.attr]=this.value;let n=e.type.create(t,e.content,e.marks);return c.ok(n)}getMap(){return i.empty}invert(e){return new B(this.attr,e.attrs[this.attr])}map(e){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(e,t){if("string"!=typeof t.attr)throw RangeError("Invalid input for DocAttrStep.fromJSON");return new B(t.attr,t.value)}}l.jsonID("docAttr",B);let H=class extends Error{};(H=function e(t){let n=Error.call(this,t);return n.__proto__=e.prototype,n}).prototype=Object.create(Error.prototype),H.prototype.constructor=H,H.prototype.name="TransformError";class U{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new a}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let t=this.maybeStep(e);if(t.failed)throw new H(t.failed);return this}maybeStep(e){let t=e.apply(this.doc);return t.failed||this.addStep(e,t.doc),t}get docChanged(){return this.steps.length>0}addStep(e,t){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=t}replace(e,t=e,n=r.p2.empty){let s=D(this.doc,e,t,n);return s&&this.step(s),this}replaceWith(e,t,n){return this.replace(e,t,new r.p2(r.HY.from(n),0,0))}delete(e,t){return this.replace(e,t,r.p2.empty)}insert(e,t){return this.replaceWith(e,e,t)}replaceRange(e,t,n){return!function(e,t,n,s){if(!s.size)return e.deleteRange(t,n);let i=e.doc.resolve(t),a=e.doc.resolve(n);if(R(i,a,s))return e.step(new m(t,n,s));let o=x(i,e.doc.resolve(n));0==o[o.length-1]&&o.pop();let l=-(i.depth+1);o.unshift(l);for(let e=i.depth,t=i.pos-1;e>0;e--,t--){let n=i.node(e).type.spec;if(n.defining||n.definingAsContext||n.isolating)break;o.indexOf(e)>-1?l=e:i.before(e)==t&&o.splice(1,0,-e)}let c=o.indexOf(l),u=[],h=s.openStart;for(let e=s.content,t=0;;t++){let n=e.firstChild;if(u.push(n),t==s.openStart)break;e=n.content}for(let e=h-1;e>=0;e--){var d;let t=u[e],n=(d=t.type).spec.defining||d.spec.definingForContent;if(n&&!t.sameMarkup(i.node(Math.abs(l)-1)))h=e;else if(n||!t.type.isTextblock)break}for(let t=s.openStart;t>=0;t--){let l=(t+h+1)%(s.openStart+1),d=u[l];if(d)for(let t=0;t<o.length;t++){let u=o[(t+c)%o.length],h=!0;u<0&&(h=!1,u=-u);let p=i.node(u-1),f=i.index(u-1);if(p.canReplaceWith(f,f,d.type,d.marks))return e.replace(i.before(u),h?a.after(u):n,new r.p2(function e(t,n,s,i,a){if(n<s){let r=t.firstChild;t=t.replaceChild(0,r.copy(e(r.content,n+1,s,i,r)))}if(n>i){let e=a.contentMatchAt(0),n=e.fillBefore(t).append(t);t=n.append(e.matchFragment(n).fillBefore(r.HY.empty,!0))}return t}(s.content,0,s.openStart,l),l,s.openEnd))}}let p=e.steps.length;for(let r=o.length-1;r>=0&&(e.replace(t,n,s),!(e.steps.length>p));r--){let e=o[r];e<0||(t=i.before(e),n=a.after(e))}}(this,e,t,n),this}replaceRangeWith(e,t,n){return!function(e,t,n,s){if(!s.isInline&&t==n&&e.doc.resolve(t).parent.content.size){let r=function(e,t,n){let r=e.resolve(t);if(r.parent.canReplaceWith(r.index(),r.index(),n))return t;if(0==r.parentOffset)for(let e=r.depth-1;e>=0;e--){let t=r.index(e);if(r.node(e).canReplaceWith(t,t,n))return r.before(e+1);if(t>0)return null}if(r.parentOffset==r.parent.content.size)for(let e=r.depth-1;e>=0;e--){let t=r.indexAfter(e);if(r.node(e).canReplaceWith(t,t,n))return r.after(e+1);if(t<r.node(e).childCount)break}return null}(e.doc,t,s.type);null!=r&&(t=n=r)}e.replaceRange(t,n,new r.p2(r.HY.from(s),0,0))}(this,e,t,n),this}deleteRange(e,t){return!function(e,t,n){let r=e.doc.resolve(t),s=e.doc.resolve(n),i=x(r,s);for(let t=0;t<i.length;t++){let n=i[t],a=t==i.length-1;if(a&&0==n||r.node(n).type.contentMatch.validEnd)return e.delete(r.start(n),s.end(n));if(n>0&&(a||r.node(n-1).canReplace(r.index(n-1),s.indexAfter(n-1))))return e.delete(r.before(n),s.after(n))}for(let i=1;i<=r.depth&&i<=s.depth;i++)if(t-r.start(i)==r.depth-i&&n>r.end(i)&&s.end(i)-n!=s.depth-i&&r.start(i-1)==s.start(i-1)&&r.node(i-1).canReplace(r.index(i-1),s.index(i-1)))return e.delete(r.before(i),n);e.delete(t,n)}(this,e,t),this}lift(e,t){return!function(e,t,n){let{$from:s,$to:i,depth:a}=t,o=s.before(a+1),l=i.after(a+1),c=o,u=l,h=r.HY.empty,d=0;for(let e=a,t=!1;e>n;e--)t||s.index(e)>0?(t=!0,h=r.HY.from(s.node(e).copy(h)),d++):c--;let p=r.HY.empty,f=0;for(let e=a,t=!1;e>n;e--)t||i.after(e+1)<i.end(e)?(t=!0,p=r.HY.from(i.node(e).copy(p)),f++):u++;e.step(new g(c,u,o,l,new r.p2(h.append(p),d,f),h.size-d,!0))}(this,e,t),this}join(e,t=1){return!function(e,t,n){let s=null,{linebreakReplacement:i}=e.doc.type.schema,a=e.doc.resolve(t-n),o=a.node().type;if(i&&o.inlineContent){let e="pre"==o.whitespace,t=!!o.contentMatch.matchType(i);e&&!t?s=!1:!e&&t&&(s=!0)}let l=e.steps.length;if(!1===s){let r=e.doc.resolve(t+n);k(e,r.node(),r.before(),l)}o.inlineContent&&T(e,t+n-1,o,a.node().contentMatchAt(a.index()),null==s);let c=e.mapping.slice(l),u=c.map(t-n);if(e.step(new m(u,c.map(t+n,-1),r.p2.empty,!0)),!0===s){let t=e.doc.resolve(u);y(e,t.node(),t.before(),e.steps.length)}}(this,e,t),this}wrap(e,t){return!function(e,t,n){let s=r.HY.empty;for(let e=n.length-1;e>=0;e--){if(s.size){let t=n[e].type.contentMatch.matchFragment(s);if(!t||!t.validEnd)throw RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}s=r.HY.from(n[e].type.create(n[e].attrs,s))}let i=t.start,a=t.end;e.step(new g(i,a,i,a,new r.p2(s,0,0),n.length,!0))}(this,e,t),this}setBlockType(e,t=e,n,s=null){return!function(e,t,n,s,i){if(!s.isTextblock)throw RangeError("Type given to setBlockType should be a textblock");let a=e.steps.length;e.doc.nodesBetween(t,n,(t,n)=>{var o,l;let c,u,h="function"==typeof i?i(t):i;if(t.isTextblock&&!t.hasMarkup(s,h)&&(o=e.doc,l=e.mapping.slice(a).map(n),u=(c=o.resolve(l)).index(),c.parent.canReplaceWith(u,u+1,s))){let i=null;if(s.schema.linebreakReplacement){let e="pre"==s.whitespace,t=!!s.contentMatch.matchType(s.schema.linebreakReplacement);e&&!t?i=!1:!e&&t&&(i=!0)}!1===i&&k(e,t,n,a),T(e,e.mapping.slice(a).map(n,1),s,void 0,null===i);let o=e.mapping.slice(a),l=o.map(n,1),c=o.map(n+t.nodeSize,1);return e.step(new g(l,c,l+1,c-1,new r.p2(r.HY.from(s.create(h,null,t.marks)),0,0),1,!0)),!0===i&&y(e,t,n,a),!1}})}(this,e,t,n,s),this}setNodeMarkup(e,t,n=null,s){return!function(e,t,n,s,i){let a=e.doc.nodeAt(t);if(!a)throw RangeError("No node at given position");n||(n=a.type);let o=n.create(s,null,i||a.marks);if(a.isLeaf)return e.replaceWith(t,t+a.nodeSize,o);if(!n.validContent(a.content))throw RangeError("Invalid content for node type "+n.name);e.step(new g(t,t+a.nodeSize,t+1,t+a.nodeSize-1,new r.p2(r.HY.from(o),0,0),1,!0))}(this,e,t,n,s),this}setNodeAttribute(e,t,n){return this.step(new F(e,t,n)),this}setDocAttribute(e,t){return this.step(new B(e,t)),this}addNodeMark(e,t){return this.step(new p(e,t)),this}removeNodeMark(e,t){let n=this.doc.nodeAt(e);if(!n)throw RangeError("No node at position "+e);if(t instanceof r.vc)t.isInSet(n.marks)&&this.step(new f(e,t));else{let r=n.marks,s,i=[];for(;s=t.isInSet(r);)i.push(new f(e,s)),r=s.removeFromSet(r);for(let e=i.length-1;e>=0;e--)this.step(i[e])}return this}split(e,t=1,n){return!function(e,t,n=1,s){let i=e.doc.resolve(t),a=r.HY.empty,o=r.HY.empty;for(let e=i.depth,t=i.depth-n,l=n-1;e>t;e--,l--){a=r.HY.from(i.node(e).copy(a));let t=s&&s[l];o=r.HY.from(t?t.type.create(t.attrs,o):i.node(e).copy(o))}e.step(new m(t,t,new r.p2(a.append(o),n,n),!0))}(this,e,t,n),this}addMark(e,t,n){var r;let s,i,a,o;return r=this,a=[],o=[],r.doc.nodesBetween(e,t,(r,l,c)=>{if(!r.isInline)return;let u=r.marks;if(!n.isInSet(u)&&c.type.allowsMarkType(n.type)){let c=Math.max(l,e),p=Math.min(l+r.nodeSize,t),f=n.addToSet(u);for(let e=0;e<u.length;e++)u[e].isInSet(f)||(s&&s.to==c&&s.mark.eq(u[e])?s.to=p:a.push(s=new d(c,p,u[e])));i&&i.to==c?i.to=p:o.push(i=new h(c,p,n))}}),a.forEach(e=>r.step(e)),o.forEach(e=>r.step(e)),this}removeMark(e,t,n){var s;let i,a;return s=this,i=[],a=0,s.doc.nodesBetween(e,t,(s,o)=>{if(!s.isInline)return;a++;let l=null;if(n instanceof r.ZU){let e=s.marks,t;for(;t=n.isInSet(e);)(l||(l=[])).push(t),e=t.removeFromSet(e)}else n?n.isInSet(s.marks)&&(l=[n]):l=s.marks;if(l&&l.length){let n=Math.min(o+s.nodeSize,t);for(let t=0;t<l.length;t++){let r=l[t],s;for(let e=0;e<i.length;e++){let t=i[e];t.step==a-1&&r.eq(i[e].style)&&(s=t)}s?(s.to=n,s.step=a):i.push({style:r,from:Math.max(o,e),to:n,step:a})}}}),i.forEach(e=>s.step(new d(e.from,e.to,e.style))),this}clearIncompatible(e,t,n){return T(this,e,t,n),this}}},27199:function(e,t,n){"use strict";n.d(t,{Z:function(){return nD}});var r,s,i,a,o,l,c,u,h,d,p,f,m,g,E,T,A,_,S,y,k,C,N,I,b,O,D,R,M,v,L={};n.r(L),n.d(L,{boolean:function(){return ea},booleanish:function(){return eo},commaOrSpaceSeparated:function(){return ed},commaSeparated:function(){return eh},number:function(){return ec},overloadedBoolean:function(){return el},spaceSeparated:function(){return eu}});var w=n(47465),P=n(51786),x=n(30531),F=n(77170);let B=/[#.]/g;var H=n(72481),U=n(75911),G=n(60624);function Y(e,t,n){let r=n?function(e){let t=new Map;for(let n of e)t.set(n.toLowerCase(),n);return t}(n):void 0;return function(n,s,...i){let a;if(null==n)a={type:"root",children:[]},i.unshift(s);else{let o=(a=function(e,t){let n,r;let s=e||"",i={},a=0;for(;a<s.length;){B.lastIndex=a;let e=B.exec(s),t=s.slice(a,e?e.index:s.length);t&&(n?"#"===n?i.id=t:Array.isArray(i.className)?i.className.push(t):i.className=[t]:r=t,a+=t.length),e&&(n=e[0],a++)}return{type:"element",tagName:r||t||"div",properties:i,children:[]}}(n,t)).tagName.toLowerCase(),l=r?r.get(o):void 0;if(a.tagName=l||o,function(e){if(null===e||"object"!=typeof e||Array.isArray(e))return!0;if("string"!=typeof e.type)return!1;for(let t of Object.keys(e)){let n=e[t];if(n&&"object"==typeof n){if(!Array.isArray(n))return!0;for(let e of n)if("number"!=typeof e&&"string"!=typeof e)return!0}}return!!("children"in e&&Array.isArray(e.children))}(s))i.unshift(s);else for(let[t,n]of Object.entries(s))!function(e,t,n,r){let s;let i=(0,H.s)(e,n);if(null!=r){if("number"==typeof r){if(Number.isNaN(r))return;s=r}else s="boolean"==typeof r?r:"string"==typeof r?i.spaceSeparated?(0,G.Q)(r):i.commaSeparated?(0,F.Q)(r):i.commaOrSpaceSeparated?(0,G.Q)((0,F.Q)(r).join(" ")):z(i,i.property,r):Array.isArray(r)?[...r]:"style"===i.property?function(e){let t=[];for(let[n,r]of Object.entries(e))t.push([n,r].join(": "));return t.join("; ")}(r):String(r);if(Array.isArray(s)){let e=[];for(let t of s)e.push(z(i,i.property,t));s=e}"className"===i.property&&Array.isArray(t.className)&&(s=t.className.concat(s)),t[i.property]=s}}(e,a.properties,t,n)}for(let e of i)!function e(t,n){if(null==n);else if("number"==typeof n||"string"==typeof n)t.push({type:"text",value:String(n)});else if(Array.isArray(n))for(let r of n)e(t,r);else if("object"==typeof n&&"type"in n)"root"===n.type?e(t,n.children):t.push(n);else throw Error("Expected node, nodes, or string, got `"+n+"`")}(a.children,e);return"element"===a.type&&"template"===a.tagName&&(a.content={type:"root",children:a.children},a.children=[]),a}}function z(e,t,n){if("string"==typeof n){if(e.number&&n&&!Number.isNaN(Number(n)))return Number(n);if((e.boolean||e.overloadedBoolean)&&(""===n||(0,U.F)(n)===(0,U.F)(t)))return!0}return n}let W=Y(x.dy,"div"),q=Y(x.YP,"g",["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","solidColor","textArea","textPath"]);function $(e,t){let n=e.indexOf("\r",t),r=e.indexOf("\n",t);return -1===r?n:-1===n||n+1===r?r:n<r?n:r}let j={html:"http://www.w3.org/1999/xhtml",mathml:"http://www.w3.org/1998/Math/MathML",svg:"http://www.w3.org/2000/svg",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"},V={}.hasOwnProperty,K=Object.prototype;function Q(e,t){let n;switch(t.nodeName){case"#comment":return n={type:"comment",value:t.data},J(e,t,n),n;case"#document":case"#document-fragment":{let r="mode"in t&&("quirks"===t.mode||"limited-quirks"===t.mode);if(n={type:"root",children:X(e,t.childNodes),data:{quirksMode:r}},e.file&&e.location){let t=String(e.file),r=function(e){let t=String(e),n=[];return{toOffset:function(e){if(e&&"number"==typeof e.line&&"number"==typeof e.column&&!Number.isNaN(e.line)&&!Number.isNaN(e.column)){for(;n.length<e.line;){let e=n[n.length-1],r=$(t,e),s=-1===r?t.length+1:r+1;if(e===s)break;n.push(s)}let r=(e.line>1?n[e.line-2]:0)+e.column-1;if(r<n[e.line-1])return r}},toPoint:function(e){if("number"==typeof e&&e>-1&&e<=t.length){let r=0;for(;;){let s=n[r];if(void 0===s){let e=$(t,n[r-1]);s=-1===e?t.length+1:e+1,n[r]=s}if(s>e)return{line:r+1,column:e-(r>0?n[r-1]:0)+1,offset:e};r++}}}}}(t),s=r.toPoint(0),i=r.toPoint(t.length);(0,P.ok)(s,"expected `start`"),(0,P.ok)(i,"expected `end`"),n.position={start:s,end:i}}return n}case"#documentType":return J(e,t,n={type:"doctype"}),n;case"#text":return n={type:"text",value:t.value},J(e,t,n),n;default:return function(e,t){let n=e.schema;e.schema=t.namespaceURI===j.svg?x.YP:x.dy;let r=-1,s={};for(;++r<t.attrs.length;){let e=t.attrs[r],n=(e.prefix?e.prefix+":":"")+e.name;V.call(K,n)||(s[n]=e.value)}let i=("svg"===e.schema.space?q:W)(t.tagName,s,X(e,t.childNodes));if(J(e,t,i),"template"===i.tagName){let n=t.sourceCodeLocation,r=n&&n.startTag&&Z(n.startTag),s=n&&n.endTag&&Z(n.endTag),a=Q(e,t.content);r&&s&&e.file&&(a.position={start:r.end,end:s.start}),i.content=a}return e.schema=n,i}(e,t)}}function X(e,t){let n=-1,r=[];for(;++n<t.length;){let s=Q(e,t[n]);r.push(s)}return r}function J(e,t,n){if("sourceCodeLocation"in t&&t.sourceCodeLocation&&e.file){let r=function(e,t,n){let r=Z(n);if("element"===t.type){let s=t.children[t.children.length-1];if(r&&!n.endTag&&s&&s.position&&s.position.end&&(r.end=Object.assign({},s.position.end)),e.verbose){let r;let s={};if(n.attrs)for(r in n.attrs)V.call(n.attrs,r)&&(s[(0,H.s)(e.schema,r).property]=Z(n.attrs[r]));(0,P.ok)(n.startTag,"a start tag should exist");let i=Z(n.startTag),a=n.endTag?Z(n.endTag):void 0,o={opening:i};a&&(o.closing=a),o.properties=s,t.data={position:o}}}return r}(e,n,t.sourceCodeLocation);r&&(e.location=!0,n.position=r)}}function Z(e){let t=ee({line:e.startLine,column:e.startCol,offset:e.startOffset}),n=ee({line:e.endLine,column:e.endCol,offset:e.endOffset});return t||n?{start:t,end:n}:void 0}function ee(e){return e.line&&e.column?e:void 0}class et{constructor(e,t,n){this.property=e,this.normal=t,n&&(this.space=n)}}function en(e,t){let n={},r={},s=-1;for(;++s<e.length;)Object.assign(n,e[s].property),Object.assign(r,e[s].normal);return new et(n,r,t)}function er(e){return e.toLowerCase()}et.prototype.property={},et.prototype.normal={},et.prototype.space=null;class es{constructor(e,t){this.property=e,this.attribute=t}}es.prototype.space=null,es.prototype.boolean=!1,es.prototype.booleanish=!1,es.prototype.overloadedBoolean=!1,es.prototype.number=!1,es.prototype.commaSeparated=!1,es.prototype.spaceSeparated=!1,es.prototype.commaOrSpaceSeparated=!1,es.prototype.mustUseProperty=!1,es.prototype.defined=!1;let ei=0,ea=ep(),eo=ep(),el=ep(),ec=ep(),eu=ep(),eh=ep(),ed=ep();function ep(){return 2**++ei}let ef=Object.keys(L);class em extends es{constructor(e,t,n,r){var s,i;let a=-1;if(super(e,t),r&&(this.space=r),"number"==typeof n)for(;++a<ef.length;){let e=ef[a];s=ef[a],(i=(n&L[e])===L[e])&&(this[s]=i)}}}em.prototype.defined=!0;let eg={}.hasOwnProperty;function eE(e){let t;let n={},r={};for(t in e.properties)if(eg.call(e.properties,t)){let s=e.properties[t],i=new em(t,e.transform(e.attributes||{},t),s,e.space);e.mustUseProperty&&e.mustUseProperty.includes(t)&&(i.mustUseProperty=!0),n[t]=i,r[er(t)]=t,r[er(i.attribute)]=t}return new et(n,r,e.space)}let eT=eE({space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase(),properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null}}),eA=eE({space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase(),properties:{xmlLang:null,xmlBase:null,xmlSpace:null}});function e_(e,t){return t in e?e[t]:t}function eS(e,t){return e_(e,t.toLowerCase())}let ey=eE({space:"xmlns",attributes:{xmlnsxlink:"xmlns:xlink"},transform:eS,properties:{xmlns:null,xmlnsXLink:null}}),ek=eE({transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase(),properties:{ariaActiveDescendant:null,ariaAtomic:eo,ariaAutoComplete:null,ariaBusy:eo,ariaChecked:eo,ariaColCount:ec,ariaColIndex:ec,ariaColSpan:ec,ariaControls:eu,ariaCurrent:null,ariaDescribedBy:eu,ariaDetails:null,ariaDisabled:eo,ariaDropEffect:eu,ariaErrorMessage:null,ariaExpanded:eo,ariaFlowTo:eu,ariaGrabbed:eo,ariaHasPopup:null,ariaHidden:eo,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:eu,ariaLevel:ec,ariaLive:null,ariaModal:eo,ariaMultiLine:eo,ariaMultiSelectable:eo,ariaOrientation:null,ariaOwns:eu,ariaPlaceholder:null,ariaPosInSet:ec,ariaPressed:eo,ariaReadOnly:eo,ariaRelevant:null,ariaRequired:eo,ariaRoleDescription:eu,ariaRowCount:ec,ariaRowIndex:ec,ariaRowSpan:ec,ariaSelected:eo,ariaSetSize:ec,ariaSort:null,ariaValueMax:ec,ariaValueMin:ec,ariaValueNow:ec,ariaValueText:null,role:null}}),eC=eE({space:"html",attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},transform:eS,mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:eh,acceptCharset:eu,accessKey:eu,action:null,allow:null,allowFullScreen:ea,allowPaymentRequest:ea,allowUserMedia:ea,alt:null,as:null,async:ea,autoCapitalize:null,autoComplete:eu,autoFocus:ea,autoPlay:ea,blocking:eu,capture:null,charSet:null,checked:ea,cite:null,className:eu,cols:ec,colSpan:null,content:null,contentEditable:eo,controls:ea,controlsList:eu,coords:ec|eh,crossOrigin:null,data:null,dateTime:null,decoding:null,default:ea,defer:ea,dir:null,dirName:null,disabled:ea,download:el,draggable:eo,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:ea,formTarget:null,headers:eu,height:ec,hidden:ea,high:ec,href:null,hrefLang:null,htmlFor:eu,httpEquiv:eu,id:null,imageSizes:null,imageSrcSet:null,inert:ea,inputMode:null,integrity:null,is:null,isMap:ea,itemId:null,itemProp:eu,itemRef:eu,itemScope:ea,itemType:eu,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:ea,low:ec,manifest:null,max:null,maxLength:ec,media:null,method:null,min:null,minLength:ec,multiple:ea,muted:ea,name:null,nonce:null,noModule:ea,noValidate:ea,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:ea,optimum:ec,pattern:null,ping:eu,placeholder:null,playsInline:ea,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:ea,referrerPolicy:null,rel:eu,required:ea,reversed:ea,rows:ec,rowSpan:ec,sandbox:eu,scope:null,scoped:ea,seamless:ea,selected:ea,shadowRootClonable:ea,shadowRootDelegatesFocus:ea,shadowRootMode:null,shape:null,size:ec,sizes:null,slot:null,span:ec,spellCheck:eo,src:null,srcDoc:null,srcLang:null,srcSet:null,start:ec,step:null,style:null,tabIndex:ec,target:null,title:null,translate:null,type:null,typeMustMatch:ea,useMap:null,value:eo,width:ec,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:eu,axis:null,background:null,bgColor:null,border:ec,borderColor:null,bottomMargin:ec,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:ea,declare:ea,event:null,face:null,frame:null,frameBorder:null,hSpace:ec,leftMargin:ec,link:null,longDesc:null,lowSrc:null,marginHeight:ec,marginWidth:ec,noResize:ea,noHref:ea,noShade:ea,noWrap:ea,object:null,profile:null,prompt:null,rev:null,rightMargin:ec,rules:null,scheme:null,scrolling:eo,standby:null,summary:null,text:null,topMargin:ec,valueType:null,version:null,vAlign:null,vLink:null,vSpace:ec,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:ea,disableRemotePlayback:ea,prefix:null,property:null,results:ec,security:null,unselectable:null}}),eN=eE({space:"svg",attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},transform:e_,properties:{about:ed,accentHeight:ec,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:ec,amplitude:ec,arabicForm:null,ascent:ec,attributeName:null,attributeType:null,azimuth:ec,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:ec,by:null,calcMode:null,capHeight:ec,className:eu,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:ec,diffuseConstant:ec,direction:null,display:null,dur:null,divisor:ec,dominantBaseline:null,download:ea,dx:null,dy:null,edgeMode:null,editable:null,elevation:ec,enableBackground:null,end:null,event:null,exponent:ec,externalResourcesRequired:null,fill:null,fillOpacity:ec,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:eh,g2:eh,glyphName:eh,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:ec,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:ec,horizOriginX:ec,horizOriginY:ec,id:null,ideographic:ec,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:ec,k:ec,k1:ec,k2:ec,k3:ec,k4:ec,kernelMatrix:ed,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:ec,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:ec,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:ec,overlineThickness:ec,paintOrder:null,panose1:null,path:null,pathLength:ec,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:eu,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:ec,pointsAtY:ec,pointsAtZ:ec,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:ed,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:ed,rev:ed,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:ed,requiredFeatures:ed,requiredFonts:ed,requiredFormats:ed,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:ec,specularExponent:ec,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:ec,strikethroughThickness:ec,string:null,stroke:null,strokeDashArray:ed,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:ec,strokeOpacity:ec,strokeWidth:null,style:null,surfaceScale:ec,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:ed,tabIndex:ec,tableValues:null,target:null,targetX:ec,targetY:ec,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:ed,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:ec,underlineThickness:ec,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:ec,values:null,vAlphabetic:ec,vMathematical:ec,vectorEffect:null,vHanging:ec,vIdeographic:ec,version:null,vertAdvY:ec,vertOriginX:ec,vertOriginY:ec,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:ec,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null}}),eI=en([eA,eT,ey,ek,eC],"html"),eb=en([eA,eT,ey,ek,eN],"svg"),eO=/^data[-\w.:]+$/i,eD=/-[a-z]/g,eR=/[A-Z]/g;function eM(e){return"-"+e.toLowerCase()}function ev(e){return e.charAt(1).toUpperCase()}let eL={}.hasOwnProperty;function ew(e,t){let n=t||{};function r(t,...n){let s=r.invalid,i=r.handlers;if(t&&eL.call(t,e)){let n=String(t[e]);s=eL.call(i,n)?i[n]:r.unknown}if(s)return s.call(this,t,...n)}return r.handlers=n.handlers||{},r.invalid=n.invalid,r.unknown=n.unknown,r}let eP={}.hasOwnProperty,ex=ew("type",{handlers:{root:function(e,t){let n={nodeName:"#document",mode:(e.data||{}).quirksMode?"quirks":"no-quirks",childNodes:[]};return n.childNodes=eF(e.children,n,t),eB(e,n),n},element:function(e,t){let n;let r=t;"element"===e.type&&"svg"===e.tagName.toLowerCase()&&"html"===t.space&&(r=eb);let s=[];if(e.properties){for(n in e.properties)if("children"!==n&&eP.call(e.properties,n)){let t=function(e,t,n){let r=function(e,t){let n=er(t),r=t,s=es;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&eO.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(eD,ev);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!eD.test(e)){let n=e.replace(eR,eM);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}s=em}return new s(r,t)}(e,t);if(!1===n||null==n||"number"==typeof n&&Number.isNaN(n)||!n&&r.boolean)return;Array.isArray(n)&&(n=r.commaSeparated?(0,F.P)(n):(0,G.P)(n));let s={name:r.attribute,value:!0===n?"":String(n)};if(r.space&&"html"!==r.space&&"svg"!==r.space){let e=s.name.indexOf(":");e<0?s.prefix="":(s.name=s.name.slice(e+1),s.prefix=r.attribute.slice(0,e)),s.namespace=j[r.space]}return s}(r,n,e.properties[n]);t&&s.push(t)}}let i=r.space;(0,P.ok)(i);let a={nodeName:e.tagName,tagName:e.tagName,attrs:s,namespaceURI:j[i],childNodes:[],parentNode:null};return a.childNodes=eF(e.children,a,r),eB(e,a),"template"===e.tagName&&e.content&&(a.content=function(e,t){let n={nodeName:"#document-fragment",childNodes:[]};return n.childNodes=eF(e.children,n,t),eB(e,n),n}(e.content,r)),a},text:function(e){let t={nodeName:"#text",value:e.value,parentNode:null};return eB(e,t),t},comment:function(e){let t={nodeName:"#comment",data:e.value,parentNode:null};return eB(e,t),t},doctype:function(e){let t={nodeName:"#documentType",name:"html",publicId:"",systemId:"",parentNode:null};return eB(e,t),t}}});function eF(e,t,n){let r=-1,s=[];if(e)for(;++r<e.length;){let i=ex(e[r],n);i.parentNode=t,s.push(i)}return s}function eB(e,t){let n=e.position;n&&n.start&&n.end&&((0,P.ok)("number"==typeof n.start.offset),(0,P.ok)("number"==typeof n.end.offset),t.sourceCodeLocation={startLine:n.start.line,startCol:n.start.column,startOffset:n.start.offset,endLine:n.end.line,endCol:n.end.column,endOffset:n.end.offset})}let eH=["area","base","basefont","bgsound","br","col","command","embed","frame","hr","image","img","input","keygen","link","meta","param","source","track","wbr"],eU=new Set([65534,65535,131070,131071,196606,196607,262142,262143,327678,327679,393214,393215,458750,458751,524286,524287,589822,589823,655358,655359,720894,720895,786430,786431,851966,851967,917502,917503,983038,983039,1048574,1048575,1114110,1114111]);(r=T||(T={}))[r.EOF=-1]="EOF",r[r.NULL=0]="NULL",r[r.TABULATION=9]="TABULATION",r[r.CARRIAGE_RETURN=13]="CARRIAGE_RETURN",r[r.LINE_FEED=10]="LINE_FEED",r[r.FORM_FEED=12]="FORM_FEED",r[r.SPACE=32]="SPACE",r[r.EXCLAMATION_MARK=33]="EXCLAMATION_MARK",r[r.QUOTATION_MARK=34]="QUOTATION_MARK",r[r.AMPERSAND=38]="AMPERSAND",r[r.APOSTROPHE=39]="APOSTROPHE",r[r.HYPHEN_MINUS=45]="HYPHEN_MINUS",r[r.SOLIDUS=47]="SOLIDUS",r[r.DIGIT_0=48]="DIGIT_0",r[r.DIGIT_9=57]="DIGIT_9",r[r.SEMICOLON=59]="SEMICOLON",r[r.LESS_THAN_SIGN=60]="LESS_THAN_SIGN",r[r.EQUALS_SIGN=61]="EQUALS_SIGN",r[r.GREATER_THAN_SIGN=62]="GREATER_THAN_SIGN",r[r.QUESTION_MARK=63]="QUESTION_MARK",r[r.LATIN_CAPITAL_A=65]="LATIN_CAPITAL_A",r[r.LATIN_CAPITAL_Z=90]="LATIN_CAPITAL_Z",r[r.RIGHT_SQUARE_BRACKET=93]="RIGHT_SQUARE_BRACKET",r[r.GRAVE_ACCENT=96]="GRAVE_ACCENT",r[r.LATIN_SMALL_A=97]="LATIN_SMALL_A",r[r.LATIN_SMALL_Z=122]="LATIN_SMALL_Z";let eG={DASH_DASH:"--",CDATA_START:"[CDATA[",DOCTYPE:"doctype",SCRIPT:"script",PUBLIC:"public",SYSTEM:"system"};function eY(e){return e>=55296&&e<=57343}function ez(e){return 32!==e&&10!==e&&13!==e&&9!==e&&12!==e&&e>=1&&e<=31||e>=127&&e<=159}function eW(e){return e>=64976&&e<=65007||eU.has(e)}(s=A||(A={})).controlCharacterInInputStream="control-character-in-input-stream",s.noncharacterInInputStream="noncharacter-in-input-stream",s.surrogateInInputStream="surrogate-in-input-stream",s.nonVoidHtmlElementStartTagWithTrailingSolidus="non-void-html-element-start-tag-with-trailing-solidus",s.endTagWithAttributes="end-tag-with-attributes",s.endTagWithTrailingSolidus="end-tag-with-trailing-solidus",s.unexpectedSolidusInTag="unexpected-solidus-in-tag",s.unexpectedNullCharacter="unexpected-null-character",s.unexpectedQuestionMarkInsteadOfTagName="unexpected-question-mark-instead-of-tag-name",s.invalidFirstCharacterOfTagName="invalid-first-character-of-tag-name",s.unexpectedEqualsSignBeforeAttributeName="unexpected-equals-sign-before-attribute-name",s.missingEndTagName="missing-end-tag-name",s.unexpectedCharacterInAttributeName="unexpected-character-in-attribute-name",s.unknownNamedCharacterReference="unknown-named-character-reference",s.missingSemicolonAfterCharacterReference="missing-semicolon-after-character-reference",s.unexpectedCharacterAfterDoctypeSystemIdentifier="unexpected-character-after-doctype-system-identifier",s.unexpectedCharacterInUnquotedAttributeValue="unexpected-character-in-unquoted-attribute-value",s.eofBeforeTagName="eof-before-tag-name",s.eofInTag="eof-in-tag",s.missingAttributeValue="missing-attribute-value",s.missingWhitespaceBetweenAttributes="missing-whitespace-between-attributes",s.missingWhitespaceAfterDoctypePublicKeyword="missing-whitespace-after-doctype-public-keyword",s.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers="missing-whitespace-between-doctype-public-and-system-identifiers",s.missingWhitespaceAfterDoctypeSystemKeyword="missing-whitespace-after-doctype-system-keyword",s.missingQuoteBeforeDoctypePublicIdentifier="missing-quote-before-doctype-public-identifier",s.missingQuoteBeforeDoctypeSystemIdentifier="missing-quote-before-doctype-system-identifier",s.missingDoctypePublicIdentifier="missing-doctype-public-identifier",s.missingDoctypeSystemIdentifier="missing-doctype-system-identifier",s.abruptDoctypePublicIdentifier="abrupt-doctype-public-identifier",s.abruptDoctypeSystemIdentifier="abrupt-doctype-system-identifier",s.cdataInHtmlContent="cdata-in-html-content",s.incorrectlyOpenedComment="incorrectly-opened-comment",s.eofInScriptHtmlCommentLikeText="eof-in-script-html-comment-like-text",s.eofInDoctype="eof-in-doctype",s.nestedComment="nested-comment",s.abruptClosingOfEmptyComment="abrupt-closing-of-empty-comment",s.eofInComment="eof-in-comment",s.incorrectlyClosedComment="incorrectly-closed-comment",s.eofInCdata="eof-in-cdata",s.absenceOfDigitsInNumericCharacterReference="absence-of-digits-in-numeric-character-reference",s.nullCharacterReference="null-character-reference",s.surrogateCharacterReference="surrogate-character-reference",s.characterReferenceOutsideUnicodeRange="character-reference-outside-unicode-range",s.controlCharacterReference="control-character-reference",s.noncharacterCharacterReference="noncharacter-character-reference",s.missingWhitespaceBeforeDoctypeName="missing-whitespace-before-doctype-name",s.missingDoctypeName="missing-doctype-name",s.invalidCharacterSequenceAfterDoctypeName="invalid-character-sequence-after-doctype-name",s.duplicateAttribute="duplicate-attribute",s.nonConformingDoctype="non-conforming-doctype",s.missingDoctype="missing-doctype",s.misplacedDoctype="misplaced-doctype",s.endTagWithoutMatchingOpenElement="end-tag-without-matching-open-element",s.closingOfElementWithOpenChildElements="closing-of-element-with-open-child-elements",s.disallowedContentInNoscriptInHead="disallowed-content-in-noscript-in-head",s.openElementsLeftAfterEof="open-elements-left-after-eof",s.abandonedHeadElementChild="abandoned-head-element-child",s.misplacedStartTagForHeadElement="misplaced-start-tag-for-head-element",s.nestedNoscriptInHead="nested-noscript-in-head",s.eofInElementThatCanContainOnlyText="eof-in-element-that-can-contain-only-text";class eq{constructor(e){this.handler=e,this.html="",this.pos=-1,this.lastGapPos=-2,this.gapStack=[],this.skipNextNewLine=!1,this.lastChunkWritten=!1,this.endOfChunkHit=!1,this.bufferWaterline=65536,this.isEol=!1,this.lineStartPos=0,this.droppedBufferSize=0,this.line=1,this.lastErrOffset=-1}get col(){return this.pos-this.lineStartPos+Number(this.lastGapPos!==this.pos)}get offset(){return this.droppedBufferSize+this.pos}getError(e,t){let{line:n,col:r,offset:s}=this,i=r+t,a=s+t;return{code:e,startLine:n,endLine:n,startCol:i,endCol:i,startOffset:a,endOffset:a}}_err(e){this.handler.onParseError&&this.lastErrOffset!==this.offset&&(this.lastErrOffset=this.offset,this.handler.onParseError(this.getError(e,0)))}_addGap(){this.gapStack.push(this.lastGapPos),this.lastGapPos=this.pos}_processSurrogate(e){if(this.pos!==this.html.length-1){let t=this.html.charCodeAt(this.pos+1);if(t>=56320&&t<=57343)return this.pos++,this._addGap(),(e-55296)*1024+9216+t}else if(!this.lastChunkWritten)return this.endOfChunkHit=!0,T.EOF;return this._err(A.surrogateInInputStream),e}willDropParsedChunk(){return this.pos>this.bufferWaterline}dropParsedChunk(){this.willDropParsedChunk()&&(this.html=this.html.substring(this.pos),this.lineStartPos-=this.pos,this.droppedBufferSize+=this.pos,this.pos=0,this.lastGapPos=-2,this.gapStack.length=0)}write(e,t){this.html.length>0?this.html+=e:this.html=e,this.endOfChunkHit=!1,this.lastChunkWritten=t}insertHtmlAtCurrentPos(e){this.html=this.html.substring(0,this.pos+1)+e+this.html.substring(this.pos+1),this.endOfChunkHit=!1}startsWith(e,t){if(this.pos+e.length>this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,!1;if(t)return this.html.startsWith(e,this.pos);for(let t=0;t<e.length;t++)if((32|this.html.charCodeAt(this.pos+t))!==e.charCodeAt(t))return!1;return!0}peek(e){let t=this.pos+e;if(t>=this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,T.EOF;let n=this.html.charCodeAt(t);return n===T.CARRIAGE_RETURN?T.LINE_FEED:n}advance(){if(this.pos++,this.isEol&&(this.isEol=!1,this.line++,this.lineStartPos=this.pos),this.pos>=this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,T.EOF;let e=this.html.charCodeAt(this.pos);return e===T.CARRIAGE_RETURN?(this.isEol=!0,this.skipNextNewLine=!0,T.LINE_FEED):e===T.LINE_FEED&&(this.isEol=!0,this.skipNextNewLine)?(this.line--,this.skipNextNewLine=!1,this._addGap(),this.advance()):(this.skipNextNewLine=!1,eY(e)&&(e=this._processSurrogate(e)),null===this.handler.onParseError||e>31&&e<127||e===T.LINE_FEED||e===T.CARRIAGE_RETURN||e>159&&e<64976||this._checkForProblematicCharacters(e),e)}_checkForProblematicCharacters(e){ez(e)?this._err(A.controlCharacterInInputStream):eW(e)&&this._err(A.noncharacterInInputStream)}retreat(e){for(this.pos-=e;this.pos<this.lastGapPos;)this.lastGapPos=this.gapStack.pop(),this.pos--;this.isEol=!1}}function e$(e,t){for(let n=e.attrs.length-1;n>=0;n--)if(e.attrs[n].name===t)return e.attrs[n].value;return null}(i=_||(_={}))[i.CHARACTER=0]="CHARACTER",i[i.NULL_CHARACTER=1]="NULL_CHARACTER",i[i.WHITESPACE_CHARACTER=2]="WHITESPACE_CHARACTER",i[i.START_TAG=3]="START_TAG",i[i.END_TAG=4]="END_TAG",i[i.COMMENT=5]="COMMENT",i[i.DOCTYPE=6]="DOCTYPE",i[i.EOF=7]="EOF",i[i.HIBERNATION=8]="HIBERNATION";let ej=new Uint16Array('ᵁ<\xd5ıʊҝջאٵ۞ޢߖࠏ੊ઑඡ๭༉༦჊ረዡᐕᒝᓃᓟᔥ\0\0\0\0\0\0ᕫᛍᦍᰒᷝ὾⁠↰⊍⏀⏻⑂⠤⤒ⴈ⹈⿎〖㊺㘹㞬㣾㨨㩱㫠㬮ࠀEMabcfglmnoprstu\\bfms\x7f\x84\x8b\x90\x95\x98\xa6\xb3\xb9\xc8\xcflig耻\xc6䃆P耻&䀦cute耻\xc1䃁reve;䄂Āiyx}rc耻\xc2䃂;䐐r;쀀\ud835\udd04rave耻\xc0䃀pha;䎑acr;䄀d;橓Āgp\x9d\xa1on;䄄f;쀀\ud835\udd38plyFunction;恡ing耻\xc5䃅Ācs\xbe\xc3r;쀀\ud835\udc9cign;扔ilde耻\xc3䃃ml耻\xc4䃄Ѐaceforsu\xe5\xfb\xfeėĜĢħĪĀcr\xea\xf2kslash;或Ŷ\xf6\xf8;櫧ed;挆y;䐑ƀcrtąċĔause;戵noullis;愬a;䎒r;쀀\ud835\udd05pf;쀀\ud835\udd39eve;䋘c\xf2ēmpeq;扎܀HOacdefhilorsuōőŖƀƞƢƵƷƺǜȕɳɸɾcy;䐧PY耻\xa9䂩ƀcpyŝŢźute;䄆Ā;iŧŨ拒talDifferentialD;慅leys;愭ȀaeioƉƎƔƘron;䄌dil耻\xc7䃇rc;䄈nint;戰ot;䄊ĀdnƧƭilla;䂸terDot;䂷\xf2ſi;䎧rcleȀDMPTǇǋǑǖot;抙inus;抖lus;投imes;抗oĀcsǢǸkwiseContourIntegral;戲eCurlyĀDQȃȏoubleQuote;思uote;怙ȀlnpuȞȨɇɕonĀ;eȥȦ户;橴ƀgitȯȶȺruent;扡nt;戯ourIntegral;戮ĀfrɌɎ;愂oduct;成nterClockwiseContourIntegral;戳oss;樯cr;쀀\ud835\udc9epĀ;Cʄʅ拓ap;才րDJSZacefiosʠʬʰʴʸˋ˗ˡ˦̳ҍĀ;oŹʥtrahd;椑cy;䐂cy;䐅cy;䐏ƀgrsʿ˄ˇger;怡r;憡hv;櫤Āayː˕ron;䄎;䐔lĀ;t˝˞戇a;䎔r;쀀\ud835\udd07Āaf˫̧Ācm˰̢riticalȀADGT̖̜̀̆cute;䂴oŴ̋̍;䋙bleAcute;䋝rave;䁠ilde;䋜ond;拄ferentialD;慆Ѱ̽\0\0\0͔͂\0Ѕf;쀀\ud835\udd3bƀ;DE͈͉͍䂨ot;惜qual;扐blèCDLRUVͣͲ΂ϏϢϸontourIntegra\xecȹoɴ͹\0\0ͻ\xbb͉nArrow;懓Āeo·ΤftƀARTΐΖΡrrow;懐ightArrow;懔e\xe5ˊngĀLRΫτeftĀARγιrrow;柸ightArrow;柺ightArrow;柹ightĀATϘϞrrow;懒ee;抨pɁϩ\0\0ϯrrow;懑ownArrow;懕erticalBar;戥ǹABLRTaВЪаўѿͼrrowƀ;BUНОТ憓ar;椓pArrow;懵reve;䌑eft˒к\0ц\0ѐightVector;楐eeVector;楞ectorĀ;Bљњ憽ar;楖ightǔѧ\0ѱeeVector;楟ectorĀ;BѺѻ懁ar;楗eeĀ;A҆҇护rrow;憧ĀctҒҗr;쀀\ud835\udc9frok;䄐ࠀNTacdfglmopqstuxҽӀӄӋӞӢӧӮӵԡԯԶՒ՝ՠեG;䅊H耻\xd0䃐cute耻\xc9䃉ƀaiyӒӗӜron;䄚rc耻\xca䃊;䐭ot;䄖r;쀀\ud835\udd08rave耻\xc8䃈ement;戈ĀapӺӾcr;䄒tyɓԆ\0\0ԒmallSquare;旻erySmallSquare;斫ĀgpԦԪon;䄘f;쀀\ud835\udd3csilon;䎕uĀaiԼՉlĀ;TՂՃ橵ilde;扂librium;懌Āci՗՚r;愰m;橳a;䎗ml耻\xcb䃋Āipժկsts;戃onentialE;慇ʀcfiosօֈ֍ֲ׌y;䐤r;쀀\ud835\udd09lledɓ֗\0\0֣mallSquare;旼erySmallSquare;斪Ͱֺ\0ֿ\0\0ׄf;쀀\ud835\udd3dAll;戀riertrf;愱c\xf2׋؀JTabcdfgorstר׬ׯ׺؀ؒؖ؛؝أ٬ٲcy;䐃耻>䀾mmaĀ;d׷׸䎓;䏜reve;䄞ƀeiy؇،ؐdil;䄢rc;䄜;䐓ot;䄠r;쀀\ud835\udd0a;拙pf;쀀\ud835\udd3eeater̀EFGLSTصلَٖٛ٦qualĀ;Lؾؿ扥ess;招ullEqual;执reater;檢ess;扷lantEqual;橾ilde;扳cr;쀀\ud835\udca2;扫ЀAacfiosuڅڋږڛڞڪھۊRDcy;䐪Āctڐڔek;䋇;䁞irc;䄤r;愌lbertSpace;愋ǰگ\0ڲf;愍izontalLine;攀Āctۃۅ\xf2کrok;䄦mpńېۘownHum\xf0įqual;扏܀EJOacdfgmnostuۺ۾܃܇܎ܚܞܡܨ݄ݸދޏޕcy;䐕lig;䄲cy;䐁cute耻\xcd䃍Āiyܓܘrc耻\xce䃎;䐘ot;䄰r;愑rave耻\xcc䃌ƀ;apܠܯܿĀcgܴܷr;䄪inaryI;慈lie\xf3ϝǴ݉\0ݢĀ;eݍݎ戬Āgrݓݘral;戫section;拂isibleĀCTݬݲomma;恣imes;恢ƀgptݿރވon;䄮f;쀀\ud835\udd40a;䎙cr;愐ilde;䄨ǫޚ\0ޞcy;䐆l耻\xcf䃏ʀcfosuެ޷޼߂ߐĀiyޱ޵rc;䄴;䐙r;쀀\ud835\udd0dpf;쀀\ud835\udd41ǣ߇\0ߌr;쀀\ud835\udca5rcy;䐈kcy;䐄΀HJacfosߤߨ߽߬߱ࠂࠈcy;䐥cy;䐌ppa;䎚Āey߶߻dil;䄶;䐚r;쀀\ud835\udd0epf;쀀\ud835\udd42cr;쀀\ud835\udca6րJTaceflmostࠥࠩࠬࡐࡣ঳সে্਷ੇcy;䐉耻<䀼ʀcmnpr࠷࠼ࡁࡄࡍute;䄹bda;䎛g;柪lacetrf;愒r;憞ƀaeyࡗ࡜ࡡron;䄽dil;䄻;䐛Āfsࡨ॰tԀACDFRTUVarࡾࢩࢱࣦ࣠ࣼयज़ΐ४Ānrࢃ࢏gleBracket;柨rowƀ;BR࢙࢚࢞憐ar;懤ightArrow;懆eiling;挈oǵࢷ\0ࣃbleBracket;柦nǔࣈ\0࣒eeVector;楡ectorĀ;Bࣛࣜ懃ar;楙loor;挊ightĀAV࣯ࣵrrow;憔ector;楎Āerँगeƀ;AVउऊऐ抣rrow;憤ector;楚iangleƀ;BEतथऩ抲ar;槏qual;抴pƀDTVषूौownVector;楑eeVector;楠ectorĀ;Bॖॗ憿ar;楘ectorĀ;B॥०憼ar;楒ight\xe1Μs̀EFGLSTॾঋকঝঢভqualGreater;拚ullEqual;扦reater;扶ess;檡lantEqual;橽ilde;扲r;쀀\ud835\udd0fĀ;eঽা拘ftarrow;懚idot;䄿ƀnpw৔ਖਛgȀLRlr৞৷ਂਐeftĀAR০৬rrow;柵ightArrow;柷ightArrow;柶eftĀarγਊight\xe1οight\xe1ϊf;쀀\ud835\udd43erĀLRਢਬeftArrow;憙ightArrow;憘ƀchtਾੀੂ\xf2ࡌ;憰rok;䅁;扪Ѐacefiosuਗ਼੝੠੷੼અઋ઎p;椅y;䐜Ādl੥੯iumSpace;恟lintrf;愳r;쀀\ud835\udd10nusPlus;戓pf;쀀\ud835\udd44c\xf2੶;䎜ҀJacefostuણધભીଔଙඑ඗ඞcy;䐊cute;䅃ƀaey઴હાron;䅇dil;䅅;䐝ƀgswે૰଎ativeƀMTV૓૟૨ediumSpace;怋hiĀcn૦૘\xeb૙eryThi\xee૙tedĀGL૸ଆreaterGreate\xf2ٳessLes\xf3ੈLine;䀊r;쀀\ud835\udd11ȀBnptଢନଷ଺reak;恠BreakingSpace;䂠f;愕ڀ;CDEGHLNPRSTV୕ୖ୪୼஡௫ఄ౞಄ದ೘ൡඅ櫬Āou୛୤ngruent;扢pCap;扭oubleVerticalBar;戦ƀlqxஃஊ஛ement;戉ualĀ;Tஒஓ扠ilde;쀀≂̸ists;戄reater΀;EFGLSTஶஷ஽௉௓௘௥扯qual;扱ullEqual;쀀≧̸reater;쀀≫̸ess;批lantEqual;쀀⩾̸ilde;扵umpń௲௽ownHump;쀀≎̸qual;쀀≏̸eĀfsఊధtTriangleƀ;BEచఛడ拪ar;쀀⧏̸qual;括s̀;EGLSTవశ఼ౄోౘ扮qual;扰reater;扸ess;쀀≪̸lantEqual;쀀⩽̸ilde;扴estedĀGL౨౹reaterGreater;쀀⪢̸essLess;쀀⪡̸recedesƀ;ESಒಓಛ技qual;쀀⪯̸lantEqual;拠ĀeiಫಹverseElement;戌ghtTriangleƀ;BEೋೌ೒拫ar;쀀⧐̸qual;拭ĀquೝഌuareSuĀbp೨೹setĀ;E೰ೳ쀀⊏̸qual;拢ersetĀ;Eഃആ쀀⊐̸qual;拣ƀbcpഓതൎsetĀ;Eഛഞ쀀⊂⃒qual;抈ceedsȀ;ESTലള഻െ抁qual;쀀⪰̸lantEqual;拡ilde;쀀≿̸ersetĀ;E൘൛쀀⊃⃒qual;抉ildeȀ;EFT൮൯൵ൿ扁qual;扄ullEqual;扇ilde;扉erticalBar;戤cr;쀀\ud835\udca9ilde耻\xd1䃑;䎝܀Eacdfgmoprstuvලෂ෉෕ෛ෠෧෼ขภยา฿ไlig;䅒cute耻\xd3䃓Āiy෎ීrc耻\xd4䃔;䐞blac;䅐r;쀀\ud835\udd12rave耻\xd2䃒ƀaei෮ෲ෶cr;䅌ga;䎩cron;䎟pf;쀀\ud835\udd46enCurlyĀDQฎบoubleQuote;怜uote;怘;橔Āclวฬr;쀀\ud835\udcaaash耻\xd8䃘iŬื฼de耻\xd5䃕es;樷ml耻\xd6䃖erĀBP๋๠Āar๐๓r;怾acĀek๚๜;揞et;掴arenthesis;揜Ҁacfhilors๿ງຊຏຒດຝະ໼rtialD;戂y;䐟r;쀀\ud835\udd13i;䎦;䎠usMinus;䂱Āipຢອncareplan\xe5ڝf;愙Ȁ;eio຺ູ໠໤檻cedesȀ;EST່້໏໚扺qual;檯lantEqual;扼ilde;找me;怳Ādp໩໮uct;戏ortionĀ;aȥ໹l;戝Āci༁༆r;쀀\ud835\udcab;䎨ȀUfos༑༖༛༟OT耻"䀢r;쀀\ud835\udd14pf;愚cr;쀀\ud835\udcac؀BEacefhiorsu༾གྷཇའཱིྦྷྪྭ႖ႩႴႾarr;椐G耻\xae䂮ƀcnrཎནབute;䅔g;柫rĀ;tཛྷཝ憠l;椖ƀaeyཧཬཱron;䅘dil;䅖;䐠Ā;vླྀཹ愜erseĀEUྂྙĀlq྇ྎement;戋uilibrium;懋pEquilibrium;楯r\xbbཹo;䎡ghtЀACDFTUVa࿁࿫࿳ဢဨၛႇϘĀnr࿆࿒gleBracket;柩rowƀ;BL࿜࿝࿡憒ar;懥eftArrow;懄eiling;按oǵ࿹\0စbleBracket;柧nǔည\0နeeVector;楝ectorĀ;Bဝသ懂ar;楕loor;挋Āerိ၃eƀ;AVဵံြ抢rrow;憦ector;楛iangleƀ;BEၐၑၕ抳ar;槐qual;抵pƀDTVၣၮၸownVector;楏eeVector;楜ectorĀ;Bႂႃ憾ar;楔ectorĀ;B႑႒懀ar;楓Āpuႛ႞f;愝ndImplies;楰ightarrow;懛ĀchႹႼr;愛;憱leDelayed;槴ڀHOacfhimoqstuფჱჷჽᄙᄞᅑᅖᅡᅧᆵᆻᆿĀCcჩხHcy;䐩y;䐨FTcy;䐬cute;䅚ʀ;aeiyᄈᄉᄎᄓᄗ檼ron;䅠dil;䅞rc;䅜;䐡r;쀀\ud835\udd16ortȀDLRUᄪᄴᄾᅉownArrow\xbbОeftArrow\xbb࢚ightArrow\xbb࿝pArrow;憑gma;䎣allCircle;战pf;쀀\ud835\udd4aɲᅭ\0\0ᅰt;戚areȀ;ISUᅻᅼᆉᆯ斡ntersection;抓uĀbpᆏᆞsetĀ;Eᆗᆘ抏qual;抑ersetĀ;Eᆨᆩ抐qual;抒nion;抔cr;쀀\ud835\udcaear;拆ȀbcmpᇈᇛሉላĀ;sᇍᇎ拐etĀ;Eᇍᇕqual;抆ĀchᇠህeedsȀ;ESTᇭᇮᇴᇿ扻qual;檰lantEqual;扽ilde;承Th\xe1ྌ;我ƀ;esሒሓሣ拑rsetĀ;Eሜም抃qual;抇et\xbbሓրHRSacfhiorsሾቄ቉ቕ቞ቱቶኟዂወዑORN耻\xde䃞ADE;愢ĀHc቎ቒcy;䐋y;䐦Ābuቚቜ;䀉;䎤ƀaeyብቪቯron;䅤dil;䅢;䐢r;쀀\ud835\udd17Āeiቻ኉ǲኀ\0ኇefore;戴a;䎘Ācn኎ኘkSpace;쀀  Space;怉ldeȀ;EFTካኬኲኼ戼qual;扃ullEqual;扅ilde;扈pf;쀀\ud835\udd4bipleDot;惛Āctዖዛr;쀀\ud835\udcafrok;䅦ૡዷጎጚጦ\0ጬጱ\0\0\0\0\0ጸጽ፷ᎅ\0᏿ᐄᐊᐐĀcrዻጁute耻\xda䃚rĀ;oጇገ憟cir;楉rǣጓ\0጖y;䐎ve;䅬Āiyጞጣrc耻\xdb䃛;䐣blac;䅰r;쀀\ud835\udd18rave耻\xd9䃙acr;䅪Ādiፁ፩erĀBPፈ፝Āarፍፐr;䁟acĀekፗፙ;揟et;掵arenthesis;揝onĀ;P፰፱拃lus;抎Āgp፻፿on;䅲f;쀀\ud835\udd4cЀADETadps᎕ᎮᎸᏄϨᏒᏗᏳrrowƀ;BDᅐᎠᎤar;椒ownArrow;懅ownArrow;憕quilibrium;楮eeĀ;AᏋᏌ报rrow;憥own\xe1ϳerĀLRᏞᏨeftArrow;憖ightArrow;憗iĀ;lᏹᏺ䏒on;䎥ing;䅮cr;쀀\ud835\udcb0ilde;䅨ml耻\xdc䃜ҀDbcdefosvᐧᐬᐰᐳᐾᒅᒊᒐᒖash;披ar;櫫y;䐒ashĀ;lᐻᐼ抩;櫦Āerᑃᑅ;拁ƀbtyᑌᑐᑺar;怖Ā;iᑏᑕcalȀBLSTᑡᑥᑪᑴar;戣ine;䁼eparator;杘ilde;所ThinSpace;怊r;쀀\ud835\udd19pf;쀀\ud835\udd4dcr;쀀\ud835\udcb1dash;抪ʀcefosᒧᒬᒱᒶᒼirc;䅴dge;拀r;쀀\ud835\udd1apf;쀀\ud835\udd4ecr;쀀\ud835\udcb2Ȁfiosᓋᓐᓒᓘr;쀀\ud835\udd1b;䎞pf;쀀\ud835\udd4fcr;쀀\ud835\udcb3ҀAIUacfosuᓱᓵᓹᓽᔄᔏᔔᔚᔠcy;䐯cy;䐇cy;䐮cute耻\xdd䃝Āiyᔉᔍrc;䅶;䐫r;쀀\ud835\udd1cpf;쀀\ud835\udd50cr;쀀\ud835\udcb4ml;䅸ЀHacdefosᔵᔹᔿᕋᕏᕝᕠᕤcy;䐖cute;䅹Āayᕄᕉron;䅽;䐗ot;䅻ǲᕔ\0ᕛoWidt\xe8૙a;䎖r;愨pf;愤cr;쀀\ud835\udcb5௡ᖃᖊᖐ\0ᖰᖶᖿ\0\0\0\0ᗆᗛᗫᙟ᙭\0ᚕ᚛ᚲᚹ\0ᚾcute耻\xe1䃡reve;䄃̀;Ediuyᖜᖝᖡᖣᖨᖭ戾;쀀∾̳;房rc耻\xe2䃢te肻\xb4̆;䐰lig耻\xe6䃦Ā;r\xb2ᖺ;쀀\ud835\udd1erave耻\xe0䃠ĀepᗊᗖĀfpᗏᗔsym;愵\xe8ᗓha;䎱ĀapᗟcĀclᗤᗧr;䄁g;樿ɤᗰ\0\0ᘊʀ;adsvᗺᗻᗿᘁᘇ戧nd;橕;橜lope;橘;橚΀;elmrszᘘᘙᘛᘞᘿᙏᙙ戠;榤e\xbbᘙsdĀ;aᘥᘦ戡ѡᘰᘲᘴᘶᘸᘺᘼᘾ;榨;榩;榪;榫;榬;榭;榮;榯tĀ;vᙅᙆ戟bĀ;dᙌᙍ抾;榝Āptᙔᙗh;戢\xbb\xb9arr;捼Āgpᙣᙧon;䄅f;쀀\ud835\udd52΀;Eaeiop዁ᙻᙽᚂᚄᚇᚊ;橰cir;橯;扊d;手s;䀧roxĀ;e዁ᚒ\xf1ᚃing耻\xe5䃥ƀctyᚡᚦᚨr;쀀\ud835\udcb6;䀪mpĀ;e዁ᚯ\xf1ʈilde耻\xe3䃣ml耻\xe4䃤Āciᛂᛈonin\xf4ɲnt;樑ࠀNabcdefiklnoprsu᛭ᛱᜰ᜼ᝃᝈ᝸᝽០៦ᠹᡐᜍ᤽᥈ᥰot;櫭Ācrᛶ᜞kȀcepsᜀᜅᜍᜓong;扌psilon;䏶rime;怵imĀ;e᜚᜛戽q;拍Ŷᜢᜦee;抽edĀ;gᜬᜭ挅e\xbbᜭrkĀ;t፜᜷brk;掶Āoyᜁᝁ;䐱quo;怞ʀcmprtᝓ᝛ᝡᝤᝨausĀ;eĊĉptyv;榰s\xe9ᜌno\xf5ēƀahwᝯ᝱ᝳ;䎲;愶een;扬r;쀀\ud835\udd1fg΀costuvwឍឝឳេ៕៛៞ƀaiuបពរ\xf0ݠrc;旯p\xbb፱ƀdptឤឨឭot;樀lus;樁imes;樂ɱឹ\0\0ើcup;樆ar;昅riangleĀdu៍្own;施p;斳plus;樄e\xe5ᑄ\xe5ᒭarow;植ƀako៭ᠦᠵĀcn៲ᠣkƀlst៺֫᠂ozenge;槫riangleȀ;dlr᠒᠓᠘᠝斴own;斾eft;旂ight;斸k;搣Ʊᠫ\0ᠳƲᠯ\0ᠱ;斒;斑4;斓ck;斈ĀeoᠾᡍĀ;qᡃᡆ쀀=⃥uiv;쀀≡⃥t;挐Ȁptwxᡙᡞᡧᡬf;쀀\ud835\udd53Ā;tᏋᡣom\xbbᏌtie;拈؀DHUVbdhmptuvᢅᢖᢪᢻᣗᣛᣬ᣿ᤅᤊᤐᤡȀLRlrᢎᢐᢒᢔ;敗;敔;敖;敓ʀ;DUduᢡᢢᢤᢦᢨ敐;敦;敩;敤;敧ȀLRlrᢳᢵᢷᢹ;敝;敚;敜;教΀;HLRhlrᣊᣋᣍᣏᣑᣓᣕ救;敬;散;敠;敫;敢;敟ox;槉ȀLRlrᣤᣦᣨᣪ;敕;敒;攐;攌ʀ;DUduڽ᣷᣹᣻᣽;敥;敨;攬;攴inus;抟lus;択imes;抠ȀLRlrᤙᤛᤝ᤟;敛;敘;攘;攔΀;HLRhlrᤰᤱᤳᤵᤷ᤻᤹攂;敪;敡;敞;攼;攤;攜Āevģ᥂bar耻\xa6䂦Ȁceioᥑᥖᥚᥠr;쀀\ud835\udcb7mi;恏mĀ;e᜚᜜lƀ;bhᥨᥩᥫ䁜;槅sub;柈Ŭᥴ᥾lĀ;e᥹᥺怢t\xbb᥺pƀ;Eeįᦅᦇ;檮Ā;qۜۛೡᦧ\0᧨ᨑᨕᨲ\0ᨷᩐ\0\0᪴\0\0᫁\0\0ᬡᬮ᭍᭒\0᯽\0ᰌƀcpr᦭ᦲ᧝ute;䄇̀;abcdsᦿᧀᧄ᧊᧕᧙戩nd;橄rcup;橉Āau᧏᧒p;橋p;橇ot;橀;쀀∩︀Āeo᧢᧥t;恁\xeeړȀaeiu᧰᧻ᨁᨅǰ᧵\0᧸s;橍on;䄍dil耻\xe7䃧rc;䄉psĀ;sᨌᨍ橌m;橐ot;䄋ƀdmnᨛᨠᨦil肻\xb8ƭptyv;榲t脀\xa2;eᨭᨮ䂢r\xe4Ʋr;쀀\ud835\udd20ƀceiᨽᩀᩍy;䑇ckĀ;mᩇᩈ朓ark\xbbᩈ;䏇r΀;Ecefms᩟᩠ᩢᩫ᪤᪪᪮旋;槃ƀ;elᩩᩪᩭ䋆q;扗eɡᩴ\0\0᪈rrowĀlr᩼᪁eft;憺ight;憻ʀRSacd᪒᪔᪖᪚᪟\xbbཇ;擈st;抛irc;抚ash;抝nint;樐id;櫯cir;槂ubsĀ;u᪻᪼晣it\xbb᪼ˬ᫇᫔᫺\0ᬊonĀ;eᫍᫎ䀺Ā;q\xc7\xc6ɭ᫙\0\0᫢aĀ;t᫞᫟䀬;䁀ƀ;fl᫨᫩᫫戁\xeeᅠeĀmx᫱᫶ent\xbb᫩e\xf3ɍǧ᫾\0ᬇĀ;dኻᬂot;橭n\xf4Ɇƀfryᬐᬔᬗ;쀀\ud835\udd54o\xe4ɔ脀\xa9;sŕᬝr;愗Āaoᬥᬩrr;憵ss;朗Ācuᬲᬷr;쀀\ud835\udcb8Ābpᬼ᭄Ā;eᭁᭂ櫏;櫑Ā;eᭉᭊ櫐;櫒dot;拯΀delprvw᭠᭬᭷ᮂᮬᯔ᯹arrĀlr᭨᭪;椸;椵ɰ᭲\0\0᭵r;拞c;拟arrĀ;p᭿ᮀ憶;椽̀;bcdosᮏᮐᮖᮡᮥᮨ截rcap;橈Āauᮛᮞp;橆p;橊ot;抍r;橅;쀀∪︀Ȁalrv᮵ᮿᯞᯣrrĀ;mᮼᮽ憷;椼yƀevwᯇᯔᯘqɰᯎ\0\0ᯒre\xe3᭳u\xe3᭵ee;拎edge;拏en耻\xa4䂤earrowĀlrᯮ᯳eft\xbbᮀight\xbbᮽe\xe4ᯝĀciᰁᰇonin\xf4Ƿnt;戱lcty;挭ঀAHabcdefhijlorstuwz᰸᰻᰿ᱝᱩᱵᲊᲞᲬᲷ᳻᳿ᴍᵻᶑᶫᶻ᷆᷍r\xf2΁ar;楥Ȁglrs᱈ᱍ᱒᱔ger;怠eth;愸\xf2ᄳhĀ;vᱚᱛ怐\xbbऊūᱡᱧarow;椏a\xe3̕Āayᱮᱳron;䄏;䐴ƀ;ao̲ᱼᲄĀgrʿᲁr;懊tseq;橷ƀglmᲑᲔᲘ耻\xb0䂰ta;䎴ptyv;榱ĀirᲣᲨsht;楿;쀀\ud835\udd21arĀlrᲳᲵ\xbbࣜ\xbbသʀaegsv᳂͸᳖᳜᳠mƀ;oș᳊᳔ndĀ;ș᳑uit;晦amma;䏝in;拲ƀ;io᳧᳨᳸䃷de脀\xf7;o᳧ᳰntimes;拇n\xf8᳷cy;䑒cɯᴆ\0\0ᴊrn;挞op;挍ʀlptuwᴘᴝᴢᵉᵕlar;䀤f;쀀\ud835\udd55ʀ;emps̋ᴭᴷᴽᵂqĀ;d͒ᴳot;扑inus;戸lus;戔quare;抡blebarwedg\xe5\xfanƀadhᄮᵝᵧownarrow\xf3ᲃarpoonĀlrᵲᵶef\xf4Ჴigh\xf4ᲶŢᵿᶅkaro\xf7གɯᶊ\0\0ᶎrn;挟op;挌ƀcotᶘᶣᶦĀryᶝᶡ;쀀\ud835\udcb9;䑕l;槶rok;䄑Ādrᶰᶴot;拱iĀ;fᶺ᠖斿Āah᷀᷃r\xf2Щa\xf2ྦangle;榦Āci᷒ᷕy;䑟grarr;柿ऀDacdefglmnopqrstuxḁḉḙḸոḼṉṡṾấắẽỡἪἷὄ὎὚ĀDoḆᴴo\xf4ᲉĀcsḎḔute耻\xe9䃩ter;橮ȀaioyḢḧḱḶron;䄛rĀ;cḭḮ扖耻\xea䃪lon;払;䑍ot;䄗ĀDrṁṅot;扒;쀀\ud835\udd22ƀ;rsṐṑṗ檚ave耻\xe8䃨Ā;dṜṝ檖ot;檘Ȁ;ilsṪṫṲṴ檙nters;揧;愓Ā;dṹṺ檕ot;檗ƀapsẅẉẗcr;䄓tyƀ;svẒẓẕ戅et\xbbẓpĀ1;ẝẤĳạả;怄;怅怃ĀgsẪẬ;䅋p;怂ĀgpẴẸon;䄙f;쀀\ud835\udd56ƀalsỄỎỒrĀ;sỊị拕l;槣us;橱iƀ;lvỚớở䎵on\xbbớ;䏵ȀcsuvỪỳἋἣĀioữḱrc\xbbḮɩỹ\0\0ỻ\xedՈantĀglἂἆtr\xbbṝess\xbbṺƀaeiἒ἖Ἒls;䀽st;扟vĀ;DȵἠD;橸parsl;槥ĀDaἯἳot;打rr;楱ƀcdiἾὁỸr;愯o\xf4͒ĀahὉὋ;䎷耻\xf0䃰Āmrὓὗl耻\xeb䃫o;悬ƀcipὡὤὧl;䀡s\xf4ծĀeoὬὴctatio\xeeՙnential\xe5չৡᾒ\0ᾞ\0ᾡᾧ\0\0ῆῌ\0ΐ\0ῦῪ \0 ⁚llingdotse\xf1Ṅy;䑄male;晀ƀilrᾭᾳ῁lig;耀ﬃɩᾹ\0\0᾽g;耀ﬀig;耀ﬄ;쀀\ud835\udd23lig;耀ﬁlig;쀀fjƀaltῙ῜ῡt;晭ig;耀ﬂns;斱of;䆒ǰ΅\0ῳf;쀀\ud835\udd57ĀakֿῷĀ;vῼ´拔;櫙artint;樍Āao‌⁕Ācs‑⁒α‚‰‸⁅⁈\0⁐β•‥‧‪‬\0‮耻\xbd䂽;慓耻\xbc䂼;慕;慙;慛Ƴ‴\0‶;慔;慖ʴ‾⁁\0\0⁃耻\xbe䂾;慗;慜5;慘ƶ⁌\0⁎;慚;慝8;慞l;恄wn;挢cr;쀀\ud835\udcbbࢀEabcdefgijlnorstv₂₉₟₥₰₴⃰⃵⃺⃿℃ℒℸ̗ℾ⅒↞Ā;lٍ₇;檌ƀcmpₐₕ₝ute;䇵maĀ;dₜ᳚䎳;檆reve;䄟Āiy₪₮rc;䄝;䐳ot;䄡Ȁ;lqsؾق₽⃉ƀ;qsؾٌ⃄lan\xf4٥Ȁ;cdl٥⃒⃥⃕c;檩otĀ;o⃜⃝檀Ā;l⃢⃣檂;檄Ā;e⃪⃭쀀⋛︀s;檔r;쀀\ud835\udd24Ā;gٳ؛mel;愷cy;䑓Ȁ;Eajٚℌℎℐ;檒;檥;檤ȀEaesℛℝ℩ℴ;扩pĀ;p℣ℤ檊rox\xbbℤĀ;q℮ℯ檈Ā;q℮ℛim;拧pf;쀀\ud835\udd58Āci⅃ⅆr;愊mƀ;el٫ⅎ⅐;檎;檐茀>;cdlqr׮ⅠⅪⅮⅳⅹĀciⅥⅧ;檧r;橺ot;拗Par;榕uest;橼ʀadelsↄⅪ←ٖ↛ǰ↉\0↎pro\xf8₞r;楸qĀlqؿ↖les\xf3₈i\xed٫Āen↣↭rtneqq;쀀≩︀\xc5↪ԀAabcefkosy⇄⇇⇱⇵⇺∘∝∯≨≽r\xf2ΠȀilmr⇐⇔⇗⇛rs\xf0ᒄf\xbb․il\xf4کĀdr⇠⇤cy;䑊ƀ;cwࣴ⇫⇯ir;楈;憭ar;意irc;䄥ƀalr∁∎∓rtsĀ;u∉∊晥it\xbb∊lip;怦con;抹r;쀀\ud835\udd25sĀew∣∩arow;椥arow;椦ʀamopr∺∾≃≞≣rr;懿tht;戻kĀlr≉≓eftarrow;憩ightarrow;憪f;쀀\ud835\udd59bar;怕ƀclt≯≴≸r;쀀\ud835\udcbdas\xe8⇴rok;䄧Ābp⊂⊇ull;恃hen\xbbᱛૡ⊣\0⊪\0⊸⋅⋎\0⋕⋳\0\0⋸⌢⍧⍢⍿\0⎆⎪⎴cute耻\xed䃭ƀ;iyݱ⊰⊵rc耻\xee䃮;䐸Ācx⊼⊿y;䐵cl耻\xa1䂡ĀfrΟ⋉;쀀\ud835\udd26rave耻\xec䃬Ȁ;inoܾ⋝⋩⋮Āin⋢⋦nt;樌t;戭fin;槜ta;愩lig;䄳ƀaop⋾⌚⌝ƀcgt⌅⌈⌗r;䄫ƀelpܟ⌏⌓in\xe5ގar\xf4ܠh;䄱f;抷ed;䆵ʀ;cfotӴ⌬⌱⌽⍁are;愅inĀ;t⌸⌹戞ie;槝do\xf4⌙ʀ;celpݗ⍌⍐⍛⍡al;抺Āgr⍕⍙er\xf3ᕣ\xe3⍍arhk;樗rod;樼Ȁcgpt⍯⍲⍶⍻y;䑑on;䄯f;쀀\ud835\udd5aa;䎹uest耻\xbf䂿Āci⎊⎏r;쀀\ud835\udcbenʀ;EdsvӴ⎛⎝⎡ӳ;拹ot;拵Ā;v⎦⎧拴;拳Ā;iݷ⎮lde;䄩ǫ⎸\0⎼cy;䑖l耻\xef䃯̀cfmosu⏌⏗⏜⏡⏧⏵Āiy⏑⏕rc;䄵;䐹r;쀀\ud835\udd27ath;䈷pf;쀀\ud835\udd5bǣ⏬\0⏱r;쀀\ud835\udcbfrcy;䑘kcy;䑔Ѐacfghjos␋␖␢␧␭␱␵␻ppaĀ;v␓␔䎺;䏰Āey␛␠dil;䄷;䐺r;쀀\ud835\udd28reen;䄸cy;䑅cy;䑜pf;쀀\ud835\udd5ccr;쀀\ud835\udcc0஀ABEHabcdefghjlmnoprstuv⑰⒁⒆⒍⒑┎┽╚▀♎♞♥♹♽⚚⚲⛘❝❨➋⟀⠁⠒ƀart⑷⑺⑼r\xf2৆\xf2Εail;椛arr;椎Ā;gঔ⒋;檋ar;楢ॣ⒥\0⒪\0⒱\0\0\0\0\0⒵Ⓔ\0ⓆⓈⓍ\0⓹ute;䄺mptyv;榴ra\xeeࡌbda;䎻gƀ;dlࢎⓁⓃ;榑\xe5ࢎ;檅uo耻\xab䂫rЀ;bfhlpst࢙ⓞⓦⓩ⓫⓮⓱⓵Ā;f࢝ⓣs;椟s;椝\xeb≒p;憫l;椹im;楳l;憢ƀ;ae⓿─┄檫il;椙Ā;s┉┊檭;쀀⪭︀ƀabr┕┙┝rr;椌rk;杲Āak┢┬cĀek┨┪;䁻;䁛Āes┱┳;榋lĀdu┹┻;榏;榍Ȁaeuy╆╋╖╘ron;䄾Ādi═╔il;䄼\xecࢰ\xe2┩;䐻Ȁcqrs╣╦╭╽a;椶uoĀ;rนᝆĀdu╲╷har;楧shar;楋h;憲ʀ;fgqs▋▌উ◳◿扤tʀahlrt▘▤▷◂◨rrowĀ;t࢙□a\xe9⓶arpoonĀdu▯▴own\xbbњp\xbb०eftarrows;懇ightƀahs◍◖◞rrowĀ;sࣴࢧarpoon\xf3྘quigarro\xf7⇰hreetimes;拋ƀ;qs▋ও◺lan\xf4বʀ;cdgsব☊☍☝☨c;檨otĀ;o☔☕橿Ā;r☚☛檁;檃Ā;e☢☥쀀⋚︀s;檓ʀadegs☳☹☽♉♋ppro\xf8Ⓠot;拖qĀgq♃♅\xf4উgt\xf2⒌\xf4ছi\xedলƀilr♕࣡♚sht;楼;쀀\ud835\udd29Ā;Eজ♣;檑š♩♶rĀdu▲♮Ā;l॥♳;楪lk;斄cy;䑙ʀ;achtੈ⚈⚋⚑⚖r\xf2◁orne\xf2ᴈard;楫ri;旺Āio⚟⚤dot;䅀ustĀ;a⚬⚭掰che\xbb⚭ȀEaes⚻⚽⛉⛔;扨pĀ;p⛃⛄檉rox\xbb⛄Ā;q⛎⛏檇Ā;q⛎⚻im;拦Ѐabnoptwz⛩⛴⛷✚✯❁❇❐Ānr⛮⛱g;柬r;懽r\xebࣁgƀlmr⛿✍✔eftĀar০✇ight\xe1৲apsto;柼ight\xe1৽parrowĀlr✥✩ef\xf4⓭ight;憬ƀafl✶✹✽r;榅;쀀\ud835\udd5dus;樭imes;樴š❋❏st;戗\xe1ፎƀ;ef❗❘᠀旊nge\xbb❘arĀ;l❤❥䀨t;榓ʀachmt❳❶❼➅➇r\xf2ࢨorne\xf2ᶌarĀ;d྘➃;業;怎ri;抿̀achiqt➘➝ੀ➢➮➻quo;怹r;쀀\ud835\udcc1mƀ;egল➪➬;檍;檏Ābu┪➳oĀ;rฟ➹;怚rok;䅂萀<;cdhilqrࠫ⟒☹⟜⟠⟥⟪⟰Āci⟗⟙;檦r;橹re\xe5◲mes;拉arr;楶uest;橻ĀPi⟵⟹ar;榖ƀ;ef⠀भ᠛旃rĀdu⠇⠍shar;楊har;楦Āen⠗⠡rtneqq;쀀≨︀\xc5⠞܀Dacdefhilnopsu⡀⡅⢂⢎⢓⢠⢥⢨⣚⣢⣤ઃ⣳⤂Dot;戺Ȁclpr⡎⡒⡣⡽r耻\xaf䂯Āet⡗⡙;時Ā;e⡞⡟朠se\xbb⡟Ā;sျ⡨toȀ;dluျ⡳⡷⡻ow\xeeҌef\xf4ए\xf0Ꮡker;斮Āoy⢇⢌mma;権;䐼ash;怔asuredangle\xbbᘦr;쀀\ud835\udd2ao;愧ƀcdn⢯⢴⣉ro耻\xb5䂵Ȁ;acdᑤ⢽⣀⣄s\xf4ᚧir;櫰ot肻\xb7Ƶusƀ;bd⣒ᤃ⣓戒Ā;uᴼ⣘;横ţ⣞⣡p;櫛\xf2−\xf0ઁĀdp⣩⣮els;抧f;쀀\ud835\udd5eĀct⣸⣽r;쀀\ud835\udcc2pos\xbbᖝƀ;lm⤉⤊⤍䎼timap;抸ఀGLRVabcdefghijlmoprstuvw⥂⥓⥾⦉⦘⧚⧩⨕⨚⩘⩝⪃⪕⪤⪨⬄⬇⭄⭿⮮ⰴⱧⱼ⳩Āgt⥇⥋;쀀⋙̸Ā;v⥐௏쀀≫⃒ƀelt⥚⥲⥶ftĀar⥡⥧rrow;懍ightarrow;懎;쀀⋘̸Ā;v⥻ే쀀≪⃒ightarrow;懏ĀDd⦎⦓ash;抯ash;抮ʀbcnpt⦣⦧⦬⦱⧌la\xbb˞ute;䅄g;쀀∠⃒ʀ;Eiop඄⦼⧀⧅⧈;쀀⩰̸d;쀀≋̸s;䅉ro\xf8඄urĀ;a⧓⧔普lĀ;s⧓ସǳ⧟\0⧣p肻\xa0ଷmpĀ;e௹ఀʀaeouy⧴⧾⨃⨐⨓ǰ⧹\0⧻;橃on;䅈dil;䅆ngĀ;dൾ⨊ot;쀀⩭̸p;橂;䐽ash;怓΀;Aadqsxஒ⨩⨭⨻⩁⩅⩐rr;懗rĀhr⨳⨶k;椤Ā;oᏲᏰot;쀀≐̸ui\xf6ୣĀei⩊⩎ar;椨\xed஘istĀ;s஠டr;쀀\ud835\udd2bȀEest௅⩦⩹⩼ƀ;qs஼⩭௡ƀ;qs஼௅⩴lan\xf4௢i\xed௪Ā;rஶ⪁\xbbஷƀAap⪊⪍⪑r\xf2⥱rr;憮ar;櫲ƀ;svྍ⪜ྌĀ;d⪡⪢拼;拺cy;䑚΀AEadest⪷⪺⪾⫂⫅⫶⫹r\xf2⥦;쀀≦̸rr;憚r;急Ȁ;fqs఻⫎⫣⫯tĀar⫔⫙rro\xf7⫁ightarro\xf7⪐ƀ;qs఻⪺⫪lan\xf4ౕĀ;sౕ⫴\xbbశi\xedౝĀ;rవ⫾iĀ;eచథi\xe4ඐĀpt⬌⬑f;쀀\ud835\udd5f膀\xac;in⬙⬚⬶䂬nȀ;Edvஉ⬤⬨⬮;쀀⋹̸ot;쀀⋵̸ǡஉ⬳⬵;拷;拶iĀ;vಸ⬼ǡಸ⭁⭃;拾;拽ƀaor⭋⭣⭩rȀ;ast୻⭕⭚⭟lle\xec୻l;쀀⫽⃥;쀀∂̸lint;樔ƀ;ceಒ⭰⭳u\xe5ಥĀ;cಘ⭸Ā;eಒ⭽\xf1ಘȀAait⮈⮋⮝⮧r\xf2⦈rrƀ;cw⮔⮕⮙憛;쀀⤳̸;쀀↝̸ghtarrow\xbb⮕riĀ;eೋೖ΀chimpqu⮽⯍⯙⬄୸⯤⯯Ȁ;cerല⯆ഷ⯉u\xe5൅;쀀\ud835\udcc3ortɭ⬅\0\0⯖ar\xe1⭖mĀ;e൮⯟Ā;q൴൳suĀbp⯫⯭\xe5೸\xe5ഋƀbcp⯶ⰑⰙȀ;Ees⯿ⰀഢⰄ抄;쀀⫅̸etĀ;eഛⰋqĀ;qണⰀcĀ;eലⰗ\xf1സȀ;EesⰢⰣൟⰧ抅;쀀⫆̸etĀ;e൘ⰮqĀ;qൠⰣȀgilrⰽⰿⱅⱇ\xecௗlde耻\xf1䃱\xe7ృiangleĀlrⱒⱜeftĀ;eచⱚ\xf1దightĀ;eೋⱥ\xf1೗Ā;mⱬⱭ䎽ƀ;esⱴⱵⱹ䀣ro;愖p;怇ҀDHadgilrsⲏⲔⲙⲞⲣⲰⲶⳓⳣash;抭arr;椄p;쀀≍⃒ash;抬ĀetⲨⲬ;쀀≥⃒;쀀>⃒nfin;槞ƀAetⲽⳁⳅrr;椂;쀀≤⃒Ā;rⳊⳍ쀀<⃒ie;쀀⊴⃒ĀAtⳘⳜrr;椃rie;쀀⊵⃒im;쀀∼⃒ƀAan⳰⳴ⴂrr;懖rĀhr⳺⳽k;椣Ā;oᏧᏥear;椧ቓ᪕\0\0\0\0\0\0\0\0\0\0\0\0\0ⴭ\0ⴸⵈⵠⵥ⵲ⶄᬇ\0\0ⶍⶫ\0ⷈⷎ\0ⷜ⸙⸫⸾⹃Ācsⴱ᪗ute耻\xf3䃳ĀiyⴼⵅrĀ;c᪞ⵂ耻\xf4䃴;䐾ʀabios᪠ⵒⵗǈⵚlac;䅑v;樸old;榼lig;䅓Ācr⵩⵭ir;榿;쀀\ud835\udd2cͯ⵹\0\0⵼\0ⶂn;䋛ave耻\xf2䃲;槁Ābmⶈ෴ar;榵Ȁacitⶕ⶘ⶥⶨr\xf2᪀Āir⶝ⶠr;榾oss;榻n\xe5๒;槀ƀaeiⶱⶵⶹcr;䅍ga;䏉ƀcdnⷀⷅǍron;䎿;榶pf;쀀\ud835\udd60ƀaelⷔ⷗ǒr;榷rp;榹΀;adiosvⷪⷫⷮ⸈⸍⸐⸖戨r\xf2᪆Ȁ;efmⷷⷸ⸂⸅橝rĀ;oⷾⷿ愴f\xbbⷿ耻\xaa䂪耻\xba䂺gof;抶r;橖lope;橗;橛ƀclo⸟⸡⸧\xf2⸁ash耻\xf8䃸l;折iŬⸯ⸴de耻\xf5䃵esĀ;aǛ⸺s;樶ml耻\xf6䃶bar;挽ૡ⹞\0⹽\0⺀⺝\0⺢⺹\0\0⻋ຜ\0⼓\0\0⼫⾼\0⿈rȀ;astЃ⹧⹲຅脀\xb6;l⹭⹮䂶le\xecЃɩ⹸\0\0⹻m;櫳;櫽y;䐿rʀcimpt⺋⺏⺓ᡥ⺗nt;䀥od;䀮il;怰enk;怱r;쀀\ud835\udd2dƀimo⺨⺰⺴Ā;v⺭⺮䏆;䏕ma\xf4੶ne;明ƀ;tv⺿⻀⻈䏀chfork\xbb´;䏖Āau⻏⻟nĀck⻕⻝kĀ;h⇴⻛;愎\xf6⇴sҀ;abcdemst⻳⻴ᤈ⻹⻽⼄⼆⼊⼎䀫cir;樣ir;樢Āouᵀ⼂;樥;橲n肻\xb1ຝim;樦wo;樧ƀipu⼙⼠⼥ntint;樕f;쀀\ud835\udd61nd耻\xa3䂣Ԁ;Eaceinosu່⼿⽁⽄⽇⾁⾉⾒⽾⾶;檳p;檷u\xe5໙Ā;c໎⽌̀;acens່⽙⽟⽦⽨⽾ppro\xf8⽃urlye\xf1໙\xf1໎ƀaes⽯⽶⽺pprox;檹qq;檵im;拨i\xedໟmeĀ;s⾈ຮ怲ƀEas⽸⾐⽺\xf0⽵ƀdfp໬⾙⾯ƀals⾠⾥⾪lar;挮ine;挒urf;挓Ā;t໻⾴\xef໻rel;抰Āci⿀⿅r;쀀\ud835\udcc5;䏈ncsp;怈̀fiopsu⿚⋢⿟⿥⿫⿱r;쀀\ud835\udd2epf;쀀\ud835\udd62rime;恗cr;쀀\ud835\udcc6ƀaeo⿸〉〓tĀei⿾々rnion\xf3ڰnt;樖stĀ;e【】䀿\xf1Ἑ\xf4༔઀ABHabcdefhilmnoprstux぀けさすムㄎㄫㅇㅢㅲㆎ㈆㈕㈤㈩㉘㉮㉲㊐㊰㊷ƀartぇおがr\xf2Ⴓ\xf2ϝail;検ar\xf2ᱥar;楤΀cdenqrtとふへみわゔヌĀeuねぱ;쀀∽̱te;䅕i\xe3ᅮmptyv;榳gȀ;del࿑らるろ;榒;榥\xe5࿑uo耻\xbb䂻rր;abcfhlpstw࿜ガクシスゼゾダッデナp;極Ā;f࿠ゴs;椠;椳s;椞\xeb≝\xf0✮l;楅im;楴l;憣;憝Āaiパフil;椚oĀ;nホボ戶al\xf3༞ƀabrョリヮr\xf2៥rk;杳ĀakンヽcĀekヹ・;䁽;䁝Āes㄂㄄;榌lĀduㄊㄌ;榎;榐Ȁaeuyㄗㄜㄧㄩron;䅙Ādiㄡㄥil;䅗\xec࿲\xe2ヺ;䑀Ȁclqsㄴㄷㄽㅄa;椷dhar;楩uoĀ;rȎȍh;憳ƀacgㅎㅟངlȀ;ipsླྀㅘㅛႜn\xe5Ⴛar\xf4ྩt;断ƀilrㅩဣㅮsht;楽;쀀\ud835\udd2fĀaoㅷㆆrĀduㅽㅿ\xbbѻĀ;l႑ㆄ;楬Ā;vㆋㆌ䏁;䏱ƀgns㆕ㇹㇼht̀ahlrstㆤㆰ㇂㇘㇤㇮rrowĀ;t࿜ㆭa\xe9トarpoonĀduㆻㆿow\xeeㅾp\xbb႒eftĀah㇊㇐rrow\xf3࿪arpoon\xf3Ցightarrows;應quigarro\xf7ニhreetimes;拌g;䋚ingdotse\xf1ἲƀahm㈍㈐㈓r\xf2࿪a\xf2Ց;怏oustĀ;a㈞㈟掱che\xbb㈟mid;櫮Ȁabpt㈲㈽㉀㉒Ānr㈷㈺g;柭r;懾r\xebဃƀafl㉇㉊㉎r;榆;쀀\ud835\udd63us;樮imes;樵Āap㉝㉧rĀ;g㉣㉤䀩t;榔olint;樒ar\xf2㇣Ȁachq㉻㊀Ⴜ㊅quo;怺r;쀀\ud835\udcc7Ābu・㊊oĀ;rȔȓƀhir㊗㊛㊠re\xe5ㇸmes;拊iȀ;efl㊪ၙᠡ㊫方tri;槎luhar;楨;愞ൡ㋕㋛㋟㌬㌸㍱\0㍺㎤\0\0㏬㏰\0㐨㑈㑚㒭㒱㓊㓱\0㘖\0\0㘳cute;䅛qu\xef➺Ԁ;Eaceinpsyᇭ㋳㋵㋿㌂㌋㌏㌟㌦㌩;檴ǰ㋺\0㋼;檸on;䅡u\xe5ᇾĀ;dᇳ㌇il;䅟rc;䅝ƀEas㌖㌘㌛;檶p;檺im;择olint;樓i\xedሄ;䑁otƀ;be㌴ᵇ㌵担;橦΀Aacmstx㍆㍊㍗㍛㍞㍣㍭rr;懘rĀhr㍐㍒\xeb∨Ā;oਸ਼਴t耻\xa7䂧i;䀻war;椩mĀin㍩\xf0nu\xf3\xf1t;朶rĀ;o㍶⁕쀀\ud835\udd30Ȁacoy㎂㎆㎑㎠rp;景Āhy㎋㎏cy;䑉;䑈rtɭ㎙\0\0㎜i\xe4ᑤara\xec⹯耻\xad䂭Āgm㎨㎴maƀ;fv㎱㎲㎲䏃;䏂Ѐ;deglnprካ㏅㏉㏎㏖㏞㏡㏦ot;橪Ā;q኱ኰĀ;E㏓㏔檞;檠Ā;E㏛㏜檝;檟e;扆lus;樤arr;楲ar\xf2ᄽȀaeit㏸㐈㐏㐗Āls㏽㐄lsetm\xe9㍪hp;樳parsl;槤Ādlᑣ㐔e;挣Ā;e㐜㐝檪Ā;s㐢㐣檬;쀀⪬︀ƀflp㐮㐳㑂tcy;䑌Ā;b㐸㐹䀯Ā;a㐾㐿槄r;挿f;쀀\ud835\udd64aĀdr㑍ЂesĀ;u㑔㑕晠it\xbb㑕ƀcsu㑠㑹㒟Āau㑥㑯pĀ;sᆈ㑫;쀀⊓︀pĀ;sᆴ㑵;쀀⊔︀uĀbp㑿㒏ƀ;esᆗᆜ㒆etĀ;eᆗ㒍\xf1ᆝƀ;esᆨᆭ㒖etĀ;eᆨ㒝\xf1ᆮƀ;afᅻ㒦ְrť㒫ֱ\xbbᅼar\xf2ᅈȀcemt㒹㒾㓂㓅r;쀀\ud835\udcc8tm\xee\xf1i\xec㐕ar\xe6ᆾĀar㓎㓕rĀ;f㓔ឿ昆Āan㓚㓭ightĀep㓣㓪psilo\xeeỠh\xe9⺯s\xbb⡒ʀbcmnp㓻㕞ሉ㖋㖎Ҁ;Edemnprs㔎㔏㔑㔕㔞㔣㔬㔱㔶抂;櫅ot;檽Ā;dᇚ㔚ot;櫃ult;櫁ĀEe㔨㔪;櫋;把lus;檿arr;楹ƀeiu㔽㕒㕕tƀ;en㔎㕅㕋qĀ;qᇚ㔏eqĀ;q㔫㔨m;櫇Ābp㕚㕜;櫕;櫓c̀;acensᇭ㕬㕲㕹㕻㌦ppro\xf8㋺urlye\xf1ᇾ\xf1ᇳƀaes㖂㖈㌛ppro\xf8㌚q\xf1㌗g;晪ڀ123;Edehlmnps㖩㖬㖯ሜ㖲㖴㗀㗉㗕㗚㗟㗨㗭耻\xb9䂹耻\xb2䂲耻\xb3䂳;櫆Āos㖹㖼t;檾ub;櫘Ā;dሢ㗅ot;櫄sĀou㗏㗒l;柉b;櫗arr;楻ult;櫂ĀEe㗤㗦;櫌;抋lus;櫀ƀeiu㗴㘉㘌tƀ;enሜ㗼㘂qĀ;qሢ㖲eqĀ;q㗧㗤m;櫈Ābp㘑㘓;櫔;櫖ƀAan㘜㘠㘭rr;懙rĀhr㘦㘨\xeb∮Ā;oਫ਩war;椪lig耻\xdf䃟௡㙑㙝㙠ዎ㙳㙹\0㙾㛂\0\0\0\0\0㛛㜃\0㜉㝬\0\0\0㞇ɲ㙖\0\0㙛get;挖;䏄r\xeb๟ƀaey㙦㙫㙰ron;䅥dil;䅣;䑂lrec;挕r;쀀\ud835\udd31Ȁeiko㚆㚝㚵㚼ǲ㚋\0㚑eĀ4fኄኁaƀ;sv㚘㚙㚛䎸ym;䏑Ācn㚢㚲kĀas㚨㚮ppro\xf8዁im\xbbኬs\xf0ኞĀas㚺㚮\xf0዁rn耻\xfe䃾Ǭ̟㛆⋧es膀\xd7;bd㛏㛐㛘䃗Ā;aᤏ㛕r;樱;樰ƀeps㛡㛣㜀\xe1⩍Ȁ;bcf҆㛬㛰㛴ot;挶ir;櫱Ā;o㛹㛼쀀\ud835\udd65rk;櫚\xe1㍢rime;怴ƀaip㜏㜒㝤d\xe5ቈ΀adempst㜡㝍㝀㝑㝗㝜㝟ngleʀ;dlqr㜰㜱㜶㝀㝂斵own\xbbᶻeftĀ;e⠀㜾\xf1म;扜ightĀ;e㊪㝋\xf1ၚot;旬inus;樺lus;樹b;槍ime;樻ezium;揢ƀcht㝲㝽㞁Āry㝷㝻;쀀\ud835\udcc9;䑆cy;䑛rok;䅧Āio㞋㞎x\xf4᝷headĀlr㞗㞠eftarro\xf7ࡏightarrow\xbbཝऀAHabcdfghlmoprstuw㟐㟓㟗㟤㟰㟼㠎㠜㠣㠴㡑㡝㡫㢩㣌㣒㣪㣶r\xf2ϭar;楣Ācr㟜㟢ute耻\xfa䃺\xf2ᅐrǣ㟪\0㟭y;䑞ve;䅭Āiy㟵㟺rc耻\xfb䃻;䑃ƀabh㠃㠆㠋r\xf2Ꭽlac;䅱a\xf2ᏃĀir㠓㠘sht;楾;쀀\ud835\udd32rave耻\xf9䃹š㠧㠱rĀlr㠬㠮\xbbॗ\xbbႃlk;斀Āct㠹㡍ɯ㠿\0\0㡊rnĀ;e㡅㡆挜r\xbb㡆op;挏ri;旸Āal㡖㡚cr;䅫肻\xa8͉Āgp㡢㡦on;䅳f;쀀\ud835\udd66̀adhlsuᅋ㡸㡽፲㢑㢠own\xe1ᎳarpoonĀlr㢈㢌ef\xf4㠭igh\xf4㠯iƀ;hl㢙㢚㢜䏅\xbbᏺon\xbb㢚parrows;懈ƀcit㢰㣄㣈ɯ㢶\0\0㣁rnĀ;e㢼㢽挝r\xbb㢽op;挎ng;䅯ri;旹cr;쀀\ud835\udccaƀdir㣙㣝㣢ot;拰lde;䅩iĀ;f㜰㣨\xbb᠓Āam㣯㣲r\xf2㢨l耻\xfc䃼angle;榧ހABDacdeflnoprsz㤜㤟㤩㤭㦵㦸㦽㧟㧤㧨㧳㧹㧽㨁㨠r\xf2ϷarĀ;v㤦㤧櫨;櫩as\xe8ϡĀnr㤲㤷grt;榜΀eknprst㓣㥆㥋㥒㥝㥤㦖app\xe1␕othin\xe7ẖƀhir㓫⻈㥙op\xf4⾵Ā;hᎷ㥢\xefㆍĀiu㥩㥭gm\xe1㎳Ābp㥲㦄setneqĀ;q㥽㦀쀀⊊︀;쀀⫋︀setneqĀ;q㦏㦒쀀⊋︀;쀀⫌︀Āhr㦛㦟et\xe1㚜iangleĀlr㦪㦯eft\xbbथight\xbbၑy;䐲ash\xbbံƀelr㧄㧒㧗ƀ;beⷪ㧋㧏ar;抻q;扚lip;拮Ābt㧜ᑨa\xf2ᑩr;쀀\ud835\udd33tr\xe9㦮suĀbp㧯㧱\xbbജ\xbb൙pf;쀀\ud835\udd67ro\xf0໻tr\xe9㦴Ācu㨆㨋r;쀀\ud835\udccbĀbp㨐㨘nĀEe㦀㨖\xbb㥾nĀEe㦒㨞\xbb㦐igzag;榚΀cefoprs㨶㨻㩖㩛㩔㩡㩪irc;䅵Ādi㩀㩑Ābg㩅㩉ar;機eĀ;qᗺ㩏;扙erp;愘r;쀀\ud835\udd34pf;쀀\ud835\udd68Ā;eᑹ㩦at\xe8ᑹcr;쀀\ud835\udcccૣណ㪇\0㪋\0㪐㪛\0\0㪝㪨㪫㪯\0\0㫃㫎\0㫘ៜ៟tr\xe9៑r;쀀\ud835\udd35ĀAa㪔㪗r\xf2σr\xf2৶;䎾ĀAa㪡㪤r\xf2θr\xf2৫a\xf0✓is;拻ƀdptឤ㪵㪾Āfl㪺ឩ;쀀\ud835\udd69im\xe5ឲĀAa㫇㫊r\xf2ώr\xf2ਁĀcq㫒ីr;쀀\ud835\udccdĀpt៖㫜r\xe9។Ѐacefiosu㫰㫽㬈㬌㬑㬕㬛㬡cĀuy㫶㫻te耻\xfd䃽;䑏Āiy㬂㬆rc;䅷;䑋n耻\xa5䂥r;쀀\ud835\udd36cy;䑗pf;쀀\ud835\udd6acr;쀀\ud835\udcceĀcm㬦㬩y;䑎l耻\xff䃿Ԁacdefhiosw㭂㭈㭔㭘㭤㭩㭭㭴㭺㮀cute;䅺Āay㭍㭒ron;䅾;䐷ot;䅼Āet㭝㭡tr\xe6ᕟa;䎶r;쀀\ud835\udd37cy;䐶grarr;懝pf;쀀\ud835\udd6bcr;쀀\ud835\udccfĀjn㮅㮇;怍j;怌'.split("").map(e=>e.charCodeAt(0))),eV=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]);function eK(e){return e>=S.ZERO&&e<=S.NINE}(a=S||(S={}))[a.NUM=35]="NUM",a[a.SEMI=59]="SEMI",a[a.EQUALS=61]="EQUALS",a[a.ZERO=48]="ZERO",a[a.NINE=57]="NINE",a[a.LOWER_A=97]="LOWER_A",a[a.LOWER_F=102]="LOWER_F",a[a.LOWER_X=120]="LOWER_X",a[a.LOWER_Z=122]="LOWER_Z",a[a.UPPER_A=65]="UPPER_A",a[a.UPPER_F=70]="UPPER_F",a[a.UPPER_Z=90]="UPPER_Z",(o=y||(y={}))[o.VALUE_LENGTH=49152]="VALUE_LENGTH",o[o.BRANCH_LENGTH=16256]="BRANCH_LENGTH",o[o.JUMP_TABLE=127]="JUMP_TABLE",(l=k||(k={}))[l.EntityStart=0]="EntityStart",l[l.NumericStart=1]="NumericStart",l[l.NumericDecimal=2]="NumericDecimal",l[l.NumericHex=3]="NumericHex",l[l.NamedEntity=4]="NamedEntity",(c=C||(C={}))[c.Legacy=0]="Legacy",c[c.Strict=1]="Strict",c[c.Attribute=2]="Attribute";class eQ{constructor(e,t,n){this.decodeTree=e,this.emitCodePoint=t,this.errors=n,this.state=k.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=C.Strict}startEntity(e){this.decodeMode=e,this.state=k.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(e,t){switch(this.state){case k.EntityStart:if(e.charCodeAt(t)===S.NUM)return this.state=k.NumericStart,this.consumed+=1,this.stateNumericStart(e,t+1);return this.state=k.NamedEntity,this.stateNamedEntity(e,t);case k.NumericStart:return this.stateNumericStart(e,t);case k.NumericDecimal:return this.stateNumericDecimal(e,t);case k.NumericHex:return this.stateNumericHex(e,t);case k.NamedEntity:return this.stateNamedEntity(e,t)}}stateNumericStart(e,t){return t>=e.length?-1:(32|e.charCodeAt(t))===S.LOWER_X?(this.state=k.NumericHex,this.consumed+=1,this.stateNumericHex(e,t+1)):(this.state=k.NumericDecimal,this.stateNumericDecimal(e,t))}addToNumericResult(e,t,n,r){if(t!==n){let s=n-t;this.result=this.result*Math.pow(r,s)+Number.parseInt(e.substr(t,s),r),this.consumed+=s}}stateNumericHex(e,t){let n=t;for(;t<e.length;){var r;let s=e.charCodeAt(t);if(!eK(s)&&(!((r=s)>=S.UPPER_A)||!(r<=S.UPPER_F))&&(!(r>=S.LOWER_A)||!(r<=S.LOWER_F)))return this.addToNumericResult(e,n,t,16),this.emitNumericEntity(s,3);t+=1}return this.addToNumericResult(e,n,t,16),-1}stateNumericDecimal(e,t){let n=t;for(;t<e.length;){let r=e.charCodeAt(t);if(!eK(r))return this.addToNumericResult(e,n,t,10),this.emitNumericEntity(r,2);t+=1}return this.addToNumericResult(e,n,t,10),-1}emitNumericEntity(e,t){var n,r,s;if(this.consumed<=t)return null===(n=this.errors)||void 0===n||n.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(e===S.SEMI)this.consumed+=1;else if(this.decodeMode===C.Strict)return 0;return this.emitCodePoint((r=this.result)>=55296&&r<=57343||r>1114111?65533:null!==(s=eV.get(r))&&void 0!==s?s:r,this.consumed),this.errors&&(e!==S.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(e,t){let{decodeTree:n}=this,r=n[this.treeIndex],s=(r&y.VALUE_LENGTH)>>14;for(;t<e.length;t++,this.excess++){let i=e.charCodeAt(t);if(this.treeIndex=function(e,t,n,r){let s=(t&y.BRANCH_LENGTH)>>7,i=t&y.JUMP_TABLE;if(0===s)return 0!==i&&r===i?n:-1;if(i){let t=r-i;return t<0||t>=s?-1:e[n+t]-1}let a=n,o=a+s-1;for(;a<=o;){let t=a+o>>>1,n=e[t];if(n<r)a=t+1;else{if(!(n>r))return e[t+s];o=t-1}}return -1}(n,r,this.treeIndex+Math.max(1,s),i),this.treeIndex<0)return 0===this.result||this.decodeMode===C.Attribute&&(0===s||function(e){var t;return e===S.EQUALS||(t=e)>=S.UPPER_A&&t<=S.UPPER_Z||t>=S.LOWER_A&&t<=S.LOWER_Z||eK(t)}(i))?0:this.emitNotTerminatedNamedEntity();if(0!=(s=((r=n[this.treeIndex])&y.VALUE_LENGTH)>>14)){if(i===S.SEMI)return this.emitNamedEntityData(this.treeIndex,s,this.consumed+this.excess);this.decodeMode!==C.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return -1}emitNotTerminatedNamedEntity(){var e;let{result:t,decodeTree:n}=this,r=(n[t]&y.VALUE_LENGTH)>>14;return this.emitNamedEntityData(t,r,this.consumed),null===(e=this.errors)||void 0===e||e.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(e,t,n){let{decodeTree:r}=this;return this.emitCodePoint(1===t?r[e]&~y.VALUE_LENGTH:r[e+1],n),3===t&&this.emitCodePoint(r[e+2],n),n}end(){var e;switch(this.state){case k.NamedEntity:return 0!==this.result&&(this.decodeMode!==C.Attribute||this.result===this.treeIndex)?this.emitNotTerminatedNamedEntity():0;case k.NumericDecimal:return this.emitNumericEntity(0,2);case k.NumericHex:return this.emitNumericEntity(0,3);case k.NumericStart:return null===(e=this.errors)||void 0===e||e.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case k.EntityStart:return 0}}}(u=N||(N={})).HTML="http://www.w3.org/1999/xhtml",u.MATHML="http://www.w3.org/1998/Math/MathML",u.SVG="http://www.w3.org/2000/svg",u.XLINK="http://www.w3.org/1999/xlink",u.XML="http://www.w3.org/XML/1998/namespace",u.XMLNS="http://www.w3.org/2000/xmlns/",(h=I||(I={})).TYPE="type",h.ACTION="action",h.ENCODING="encoding",h.PROMPT="prompt",h.NAME="name",h.COLOR="color",h.FACE="face",h.SIZE="size",(d=b||(b={})).NO_QUIRKS="no-quirks",d.QUIRKS="quirks",d.LIMITED_QUIRKS="limited-quirks",(p=O||(O={})).A="a",p.ADDRESS="address",p.ANNOTATION_XML="annotation-xml",p.APPLET="applet",p.AREA="area",p.ARTICLE="article",p.ASIDE="aside",p.B="b",p.BASE="base",p.BASEFONT="basefont",p.BGSOUND="bgsound",p.BIG="big",p.BLOCKQUOTE="blockquote",p.BODY="body",p.BR="br",p.BUTTON="button",p.CAPTION="caption",p.CENTER="center",p.CODE="code",p.COL="col",p.COLGROUP="colgroup",p.DD="dd",p.DESC="desc",p.DETAILS="details",p.DIALOG="dialog",p.DIR="dir",p.DIV="div",p.DL="dl",p.DT="dt",p.EM="em",p.EMBED="embed",p.FIELDSET="fieldset",p.FIGCAPTION="figcaption",p.FIGURE="figure",p.FONT="font",p.FOOTER="footer",p.FOREIGN_OBJECT="foreignObject",p.FORM="form",p.FRAME="frame",p.FRAMESET="frameset",p.H1="h1",p.H2="h2",p.H3="h3",p.H4="h4",p.H5="h5",p.H6="h6",p.HEAD="head",p.HEADER="header",p.HGROUP="hgroup",p.HR="hr",p.HTML="html",p.I="i",p.IMG="img",p.IMAGE="image",p.INPUT="input",p.IFRAME="iframe",p.KEYGEN="keygen",p.LABEL="label",p.LI="li",p.LINK="link",p.LISTING="listing",p.MAIN="main",p.MALIGNMARK="malignmark",p.MARQUEE="marquee",p.MATH="math",p.MENU="menu",p.META="meta",p.MGLYPH="mglyph",p.MI="mi",p.MO="mo",p.MN="mn",p.MS="ms",p.MTEXT="mtext",p.NAV="nav",p.NOBR="nobr",p.NOFRAMES="noframes",p.NOEMBED="noembed",p.NOSCRIPT="noscript",p.OBJECT="object",p.OL="ol",p.OPTGROUP="optgroup",p.OPTION="option",p.P="p",p.PARAM="param",p.PLAINTEXT="plaintext",p.PRE="pre",p.RB="rb",p.RP="rp",p.RT="rt",p.RTC="rtc",p.RUBY="ruby",p.S="s",p.SCRIPT="script",p.SEARCH="search",p.SECTION="section",p.SELECT="select",p.SOURCE="source",p.SMALL="small",p.SPAN="span",p.STRIKE="strike",p.STRONG="strong",p.STYLE="style",p.SUB="sub",p.SUMMARY="summary",p.SUP="sup",p.TABLE="table",p.TBODY="tbody",p.TEMPLATE="template",p.TEXTAREA="textarea",p.TFOOT="tfoot",p.TD="td",p.TH="th",p.THEAD="thead",p.TITLE="title",p.TR="tr",p.TRACK="track",p.TT="tt",p.U="u",p.UL="ul",p.SVG="svg",p.VAR="var",p.WBR="wbr",p.XMP="xmp",(f=D||(D={}))[f.UNKNOWN=0]="UNKNOWN",f[f.A=1]="A",f[f.ADDRESS=2]="ADDRESS",f[f.ANNOTATION_XML=3]="ANNOTATION_XML",f[f.APPLET=4]="APPLET",f[f.AREA=5]="AREA",f[f.ARTICLE=6]="ARTICLE",f[f.ASIDE=7]="ASIDE",f[f.B=8]="B",f[f.BASE=9]="BASE",f[f.BASEFONT=10]="BASEFONT",f[f.BGSOUND=11]="BGSOUND",f[f.BIG=12]="BIG",f[f.BLOCKQUOTE=13]="BLOCKQUOTE",f[f.BODY=14]="BODY",f[f.BR=15]="BR",f[f.BUTTON=16]="BUTTON",f[f.CAPTION=17]="CAPTION",f[f.CENTER=18]="CENTER",f[f.CODE=19]="CODE",f[f.COL=20]="COL",f[f.COLGROUP=21]="COLGROUP",f[f.DD=22]="DD",f[f.DESC=23]="DESC",f[f.DETAILS=24]="DETAILS",f[f.DIALOG=25]="DIALOG",f[f.DIR=26]="DIR",f[f.DIV=27]="DIV",f[f.DL=28]="DL",f[f.DT=29]="DT",f[f.EM=30]="EM",f[f.EMBED=31]="EMBED",f[f.FIELDSET=32]="FIELDSET",f[f.FIGCAPTION=33]="FIGCAPTION",f[f.FIGURE=34]="FIGURE",f[f.FONT=35]="FONT",f[f.FOOTER=36]="FOOTER",f[f.FOREIGN_OBJECT=37]="FOREIGN_OBJECT",f[f.FORM=38]="FORM",f[f.FRAME=39]="FRAME",f[f.FRAMESET=40]="FRAMESET",f[f.H1=41]="H1",f[f.H2=42]="H2",f[f.H3=43]="H3",f[f.H4=44]="H4",f[f.H5=45]="H5",f[f.H6=46]="H6",f[f.HEAD=47]="HEAD",f[f.HEADER=48]="HEADER",f[f.HGROUP=49]="HGROUP",f[f.HR=50]="HR",f[f.HTML=51]="HTML",f[f.I=52]="I",f[f.IMG=53]="IMG",f[f.IMAGE=54]="IMAGE",f[f.INPUT=55]="INPUT",f[f.IFRAME=56]="IFRAME",f[f.KEYGEN=57]="KEYGEN",f[f.LABEL=58]="LABEL",f[f.LI=59]="LI",f[f.LINK=60]="LINK",f[f.LISTING=61]="LISTING",f[f.MAIN=62]="MAIN",f[f.MALIGNMARK=63]="MALIGNMARK",f[f.MARQUEE=64]="MARQUEE",f[f.MATH=65]="MATH",f[f.MENU=66]="MENU",f[f.META=67]="META",f[f.MGLYPH=68]="MGLYPH",f[f.MI=69]="MI",f[f.MO=70]="MO",f[f.MN=71]="MN",f[f.MS=72]="MS",f[f.MTEXT=73]="MTEXT",f[f.NAV=74]="NAV",f[f.NOBR=75]="NOBR",f[f.NOFRAMES=76]="NOFRAMES",f[f.NOEMBED=77]="NOEMBED",f[f.NOSCRIPT=78]="NOSCRIPT",f[f.OBJECT=79]="OBJECT",f[f.OL=80]="OL",f[f.OPTGROUP=81]="OPTGROUP",f[f.OPTION=82]="OPTION",f[f.P=83]="P",f[f.PARAM=84]="PARAM",f[f.PLAINTEXT=85]="PLAINTEXT",f[f.PRE=86]="PRE",f[f.RB=87]="RB",f[f.RP=88]="RP",f[f.RT=89]="RT",f[f.RTC=90]="RTC",f[f.RUBY=91]="RUBY",f[f.S=92]="S",f[f.SCRIPT=93]="SCRIPT",f[f.SEARCH=94]="SEARCH",f[f.SECTION=95]="SECTION",f[f.SELECT=96]="SELECT",f[f.SOURCE=97]="SOURCE",f[f.SMALL=98]="SMALL",f[f.SPAN=99]="SPAN",f[f.STRIKE=100]="STRIKE",f[f.STRONG=101]="STRONG",f[f.STYLE=102]="STYLE",f[f.SUB=103]="SUB",f[f.SUMMARY=104]="SUMMARY",f[f.SUP=105]="SUP",f[f.TABLE=106]="TABLE",f[f.TBODY=107]="TBODY",f[f.TEMPLATE=108]="TEMPLATE",f[f.TEXTAREA=109]="TEXTAREA",f[f.TFOOT=110]="TFOOT",f[f.TD=111]="TD",f[f.TH=112]="TH",f[f.THEAD=113]="THEAD",f[f.TITLE=114]="TITLE",f[f.TR=115]="TR",f[f.TRACK=116]="TRACK",f[f.TT=117]="TT",f[f.U=118]="U",f[f.UL=119]="UL",f[f.SVG=120]="SVG",f[f.VAR=121]="VAR",f[f.WBR=122]="WBR",f[f.XMP=123]="XMP";let eX=new Map([[O.A,D.A],[O.ADDRESS,D.ADDRESS],[O.ANNOTATION_XML,D.ANNOTATION_XML],[O.APPLET,D.APPLET],[O.AREA,D.AREA],[O.ARTICLE,D.ARTICLE],[O.ASIDE,D.ASIDE],[O.B,D.B],[O.BASE,D.BASE],[O.BASEFONT,D.BASEFONT],[O.BGSOUND,D.BGSOUND],[O.BIG,D.BIG],[O.BLOCKQUOTE,D.BLOCKQUOTE],[O.BODY,D.BODY],[O.BR,D.BR],[O.BUTTON,D.BUTTON],[O.CAPTION,D.CAPTION],[O.CENTER,D.CENTER],[O.CODE,D.CODE],[O.COL,D.COL],[O.COLGROUP,D.COLGROUP],[O.DD,D.DD],[O.DESC,D.DESC],[O.DETAILS,D.DETAILS],[O.DIALOG,D.DIALOG],[O.DIR,D.DIR],[O.DIV,D.DIV],[O.DL,D.DL],[O.DT,D.DT],[O.EM,D.EM],[O.EMBED,D.EMBED],[O.FIELDSET,D.FIELDSET],[O.FIGCAPTION,D.FIGCAPTION],[O.FIGURE,D.FIGURE],[O.FONT,D.FONT],[O.FOOTER,D.FOOTER],[O.FOREIGN_OBJECT,D.FOREIGN_OBJECT],[O.FORM,D.FORM],[O.FRAME,D.FRAME],[O.FRAMESET,D.FRAMESET],[O.H1,D.H1],[O.H2,D.H2],[O.H3,D.H3],[O.H4,D.H4],[O.H5,D.H5],[O.H6,D.H6],[O.HEAD,D.HEAD],[O.HEADER,D.HEADER],[O.HGROUP,D.HGROUP],[O.HR,D.HR],[O.HTML,D.HTML],[O.I,D.I],[O.IMG,D.IMG],[O.IMAGE,D.IMAGE],[O.INPUT,D.INPUT],[O.IFRAME,D.IFRAME],[O.KEYGEN,D.KEYGEN],[O.LABEL,D.LABEL],[O.LI,D.LI],[O.LINK,D.LINK],[O.LISTING,D.LISTING],[O.MAIN,D.MAIN],[O.MALIGNMARK,D.MALIGNMARK],[O.MARQUEE,D.MARQUEE],[O.MATH,D.MATH],[O.MENU,D.MENU],[O.META,D.META],[O.MGLYPH,D.MGLYPH],[O.MI,D.MI],[O.MO,D.MO],[O.MN,D.MN],[O.MS,D.MS],[O.MTEXT,D.MTEXT],[O.NAV,D.NAV],[O.NOBR,D.NOBR],[O.NOFRAMES,D.NOFRAMES],[O.NOEMBED,D.NOEMBED],[O.NOSCRIPT,D.NOSCRIPT],[O.OBJECT,D.OBJECT],[O.OL,D.OL],[O.OPTGROUP,D.OPTGROUP],[O.OPTION,D.OPTION],[O.P,D.P],[O.PARAM,D.PARAM],[O.PLAINTEXT,D.PLAINTEXT],[O.PRE,D.PRE],[O.RB,D.RB],[O.RP,D.RP],[O.RT,D.RT],[O.RTC,D.RTC],[O.RUBY,D.RUBY],[O.S,D.S],[O.SCRIPT,D.SCRIPT],[O.SEARCH,D.SEARCH],[O.SECTION,D.SECTION],[O.SELECT,D.SELECT],[O.SOURCE,D.SOURCE],[O.SMALL,D.SMALL],[O.SPAN,D.SPAN],[O.STRIKE,D.STRIKE],[O.STRONG,D.STRONG],[O.STYLE,D.STYLE],[O.SUB,D.SUB],[O.SUMMARY,D.SUMMARY],[O.SUP,D.SUP],[O.TABLE,D.TABLE],[O.TBODY,D.TBODY],[O.TEMPLATE,D.TEMPLATE],[O.TEXTAREA,D.TEXTAREA],[O.TFOOT,D.TFOOT],[O.TD,D.TD],[O.TH,D.TH],[O.THEAD,D.THEAD],[O.TITLE,D.TITLE],[O.TR,D.TR],[O.TRACK,D.TRACK],[O.TT,D.TT],[O.U,D.U],[O.UL,D.UL],[O.SVG,D.SVG],[O.VAR,D.VAR],[O.WBR,D.WBR],[O.XMP,D.XMP]]);function eJ(e){var t;return null!==(t=eX.get(e))&&void 0!==t?t:D.UNKNOWN}let eZ=D,e0={[N.HTML]:new Set([eZ.ADDRESS,eZ.APPLET,eZ.AREA,eZ.ARTICLE,eZ.ASIDE,eZ.BASE,eZ.BASEFONT,eZ.BGSOUND,eZ.BLOCKQUOTE,eZ.BODY,eZ.BR,eZ.BUTTON,eZ.CAPTION,eZ.CENTER,eZ.COL,eZ.COLGROUP,eZ.DD,eZ.DETAILS,eZ.DIR,eZ.DIV,eZ.DL,eZ.DT,eZ.EMBED,eZ.FIELDSET,eZ.FIGCAPTION,eZ.FIGURE,eZ.FOOTER,eZ.FORM,eZ.FRAME,eZ.FRAMESET,eZ.H1,eZ.H2,eZ.H3,eZ.H4,eZ.H5,eZ.H6,eZ.HEAD,eZ.HEADER,eZ.HGROUP,eZ.HR,eZ.HTML,eZ.IFRAME,eZ.IMG,eZ.INPUT,eZ.LI,eZ.LINK,eZ.LISTING,eZ.MAIN,eZ.MARQUEE,eZ.MENU,eZ.META,eZ.NAV,eZ.NOEMBED,eZ.NOFRAMES,eZ.NOSCRIPT,eZ.OBJECT,eZ.OL,eZ.P,eZ.PARAM,eZ.PLAINTEXT,eZ.PRE,eZ.SCRIPT,eZ.SECTION,eZ.SELECT,eZ.SOURCE,eZ.STYLE,eZ.SUMMARY,eZ.TABLE,eZ.TBODY,eZ.TD,eZ.TEMPLATE,eZ.TEXTAREA,eZ.TFOOT,eZ.TH,eZ.THEAD,eZ.TITLE,eZ.TR,eZ.TRACK,eZ.UL,eZ.WBR,eZ.XMP]),[N.MATHML]:new Set([eZ.MI,eZ.MO,eZ.MN,eZ.MS,eZ.MTEXT,eZ.ANNOTATION_XML]),[N.SVG]:new Set([eZ.TITLE,eZ.FOREIGN_OBJECT,eZ.DESC]),[N.XLINK]:new Set,[N.XML]:new Set,[N.XMLNS]:new Set},e1=new Set([eZ.H1,eZ.H2,eZ.H3,eZ.H4,eZ.H5,eZ.H6]);O.STYLE,O.SCRIPT,O.XMP,O.IFRAME,O.NOEMBED,O.NOFRAMES,O.PLAINTEXT,(m=R||(R={}))[m.DATA=0]="DATA",m[m.RCDATA=1]="RCDATA",m[m.RAWTEXT=2]="RAWTEXT",m[m.SCRIPT_DATA=3]="SCRIPT_DATA",m[m.PLAINTEXT=4]="PLAINTEXT",m[m.TAG_OPEN=5]="TAG_OPEN",m[m.END_TAG_OPEN=6]="END_TAG_OPEN",m[m.TAG_NAME=7]="TAG_NAME",m[m.RCDATA_LESS_THAN_SIGN=8]="RCDATA_LESS_THAN_SIGN",m[m.RCDATA_END_TAG_OPEN=9]="RCDATA_END_TAG_OPEN",m[m.RCDATA_END_TAG_NAME=10]="RCDATA_END_TAG_NAME",m[m.RAWTEXT_LESS_THAN_SIGN=11]="RAWTEXT_LESS_THAN_SIGN",m[m.RAWTEXT_END_TAG_OPEN=12]="RAWTEXT_END_TAG_OPEN",m[m.RAWTEXT_END_TAG_NAME=13]="RAWTEXT_END_TAG_NAME",m[m.SCRIPT_DATA_LESS_THAN_SIGN=14]="SCRIPT_DATA_LESS_THAN_SIGN",m[m.SCRIPT_DATA_END_TAG_OPEN=15]="SCRIPT_DATA_END_TAG_OPEN",m[m.SCRIPT_DATA_END_TAG_NAME=16]="SCRIPT_DATA_END_TAG_NAME",m[m.SCRIPT_DATA_ESCAPE_START=17]="SCRIPT_DATA_ESCAPE_START",m[m.SCRIPT_DATA_ESCAPE_START_DASH=18]="SCRIPT_DATA_ESCAPE_START_DASH",m[m.SCRIPT_DATA_ESCAPED=19]="SCRIPT_DATA_ESCAPED",m[m.SCRIPT_DATA_ESCAPED_DASH=20]="SCRIPT_DATA_ESCAPED_DASH",m[m.SCRIPT_DATA_ESCAPED_DASH_DASH=21]="SCRIPT_DATA_ESCAPED_DASH_DASH",m[m.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN=22]="SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN",m[m.SCRIPT_DATA_ESCAPED_END_TAG_OPEN=23]="SCRIPT_DATA_ESCAPED_END_TAG_OPEN",m[m.SCRIPT_DATA_ESCAPED_END_TAG_NAME=24]="SCRIPT_DATA_ESCAPED_END_TAG_NAME",m[m.SCRIPT_DATA_DOUBLE_ESCAPE_START=25]="SCRIPT_DATA_DOUBLE_ESCAPE_START",m[m.SCRIPT_DATA_DOUBLE_ESCAPED=26]="SCRIPT_DATA_DOUBLE_ESCAPED",m[m.SCRIPT_DATA_DOUBLE_ESCAPED_DASH=27]="SCRIPT_DATA_DOUBLE_ESCAPED_DASH",m[m.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH=28]="SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH",m[m.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN=29]="SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN",m[m.SCRIPT_DATA_DOUBLE_ESCAPE_END=30]="SCRIPT_DATA_DOUBLE_ESCAPE_END",m[m.BEFORE_ATTRIBUTE_NAME=31]="BEFORE_ATTRIBUTE_NAME",m[m.ATTRIBUTE_NAME=32]="ATTRIBUTE_NAME",m[m.AFTER_ATTRIBUTE_NAME=33]="AFTER_ATTRIBUTE_NAME",m[m.BEFORE_ATTRIBUTE_VALUE=34]="BEFORE_ATTRIBUTE_VALUE",m[m.ATTRIBUTE_VALUE_DOUBLE_QUOTED=35]="ATTRIBUTE_VALUE_DOUBLE_QUOTED",m[m.ATTRIBUTE_VALUE_SINGLE_QUOTED=36]="ATTRIBUTE_VALUE_SINGLE_QUOTED",m[m.ATTRIBUTE_VALUE_UNQUOTED=37]="ATTRIBUTE_VALUE_UNQUOTED",m[m.AFTER_ATTRIBUTE_VALUE_QUOTED=38]="AFTER_ATTRIBUTE_VALUE_QUOTED",m[m.SELF_CLOSING_START_TAG=39]="SELF_CLOSING_START_TAG",m[m.BOGUS_COMMENT=40]="BOGUS_COMMENT",m[m.MARKUP_DECLARATION_OPEN=41]="MARKUP_DECLARATION_OPEN",m[m.COMMENT_START=42]="COMMENT_START",m[m.COMMENT_START_DASH=43]="COMMENT_START_DASH",m[m.COMMENT=44]="COMMENT",m[m.COMMENT_LESS_THAN_SIGN=45]="COMMENT_LESS_THAN_SIGN",m[m.COMMENT_LESS_THAN_SIGN_BANG=46]="COMMENT_LESS_THAN_SIGN_BANG",m[m.COMMENT_LESS_THAN_SIGN_BANG_DASH=47]="COMMENT_LESS_THAN_SIGN_BANG_DASH",m[m.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH=48]="COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH",m[m.COMMENT_END_DASH=49]="COMMENT_END_DASH",m[m.COMMENT_END=50]="COMMENT_END",m[m.COMMENT_END_BANG=51]="COMMENT_END_BANG",m[m.DOCTYPE=52]="DOCTYPE",m[m.BEFORE_DOCTYPE_NAME=53]="BEFORE_DOCTYPE_NAME",m[m.DOCTYPE_NAME=54]="DOCTYPE_NAME",m[m.AFTER_DOCTYPE_NAME=55]="AFTER_DOCTYPE_NAME",m[m.AFTER_DOCTYPE_PUBLIC_KEYWORD=56]="AFTER_DOCTYPE_PUBLIC_KEYWORD",m[m.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER=57]="BEFORE_DOCTYPE_PUBLIC_IDENTIFIER",m[m.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED=58]="DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED",m[m.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED=59]="DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED",m[m.AFTER_DOCTYPE_PUBLIC_IDENTIFIER=60]="AFTER_DOCTYPE_PUBLIC_IDENTIFIER",m[m.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS=61]="BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS",m[m.AFTER_DOCTYPE_SYSTEM_KEYWORD=62]="AFTER_DOCTYPE_SYSTEM_KEYWORD",m[m.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER=63]="BEFORE_DOCTYPE_SYSTEM_IDENTIFIER",m[m.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED=64]="DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED",m[m.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED=65]="DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED",m[m.AFTER_DOCTYPE_SYSTEM_IDENTIFIER=66]="AFTER_DOCTYPE_SYSTEM_IDENTIFIER",m[m.BOGUS_DOCTYPE=67]="BOGUS_DOCTYPE",m[m.CDATA_SECTION=68]="CDATA_SECTION",m[m.CDATA_SECTION_BRACKET=69]="CDATA_SECTION_BRACKET",m[m.CDATA_SECTION_END=70]="CDATA_SECTION_END",m[m.CHARACTER_REFERENCE=71]="CHARACTER_REFERENCE",m[m.AMBIGUOUS_AMPERSAND=72]="AMBIGUOUS_AMPERSAND";let e2={DATA:R.DATA,RCDATA:R.RCDATA,RAWTEXT:R.RAWTEXT,SCRIPT_DATA:R.SCRIPT_DATA,PLAINTEXT:R.PLAINTEXT,CDATA_SECTION:R.CDATA_SECTION};function e3(e){return e>=T.LATIN_CAPITAL_A&&e<=T.LATIN_CAPITAL_Z}function e5(e){return e>=T.LATIN_SMALL_A&&e<=T.LATIN_SMALL_Z||e3(e)}function e4(e){return e5(e)||e>=T.DIGIT_0&&e<=T.DIGIT_9}function e8(e){return e===T.SPACE||e===T.LINE_FEED||e===T.TABULATION||e===T.FORM_FEED}function e6(e){return e8(e)||e===T.SOLIDUS||e===T.GREATER_THAN_SIGN}class e9{constructor(e,t){this.options=e,this.handler=t,this.paused=!1,this.inLoop=!1,this.inForeignNode=!1,this.lastStartTagName="",this.active=!1,this.state=R.DATA,this.returnState=R.DATA,this.entityStartPos=0,this.consumedAfterSnapshot=-1,this.currentCharacterToken=null,this.currentToken=null,this.currentAttr={name:"",value:""},this.preprocessor=new eq(t),this.currentLocation=this.getCurrentLocation(-1),this.entityDecoder=new eQ(ej,(e,t)=>{this.preprocessor.pos=this.entityStartPos+t-1,this._flushCodePointConsumedAsCharacterReference(e)},t.onParseError?{missingSemicolonAfterCharacterReference:()=>{this._err(A.missingSemicolonAfterCharacterReference,1)},absenceOfDigitsInNumericCharacterReference:e=>{this._err(A.absenceOfDigitsInNumericCharacterReference,this.entityStartPos-this.preprocessor.pos+e)},validateNumericCharacterReference:e=>{var t;let n=(t=e)===T.NULL?A.nullCharacterReference:t>1114111?A.characterReferenceOutsideUnicodeRange:eY(t)?A.surrogateCharacterReference:eW(t)?A.noncharacterCharacterReference:ez(t)||t===T.CARRIAGE_RETURN?A.controlCharacterReference:null;n&&this._err(n,1)}}:void 0)}_err(e,t=0){var n,r;null===(r=(n=this.handler).onParseError)||void 0===r||r.call(n,this.preprocessor.getError(e,t))}getCurrentLocation(e){return this.options.sourceCodeLocationInfo?{startLine:this.preprocessor.line,startCol:this.preprocessor.col-e,startOffset:this.preprocessor.offset-e,endLine:-1,endCol:-1,endOffset:-1}:null}_runParsingLoop(){if(!this.inLoop){for(this.inLoop=!0;this.active&&!this.paused;){this.consumedAfterSnapshot=0;let e=this._consume();this._ensureHibernation()||this._callState(e)}this.inLoop=!1}}pause(){this.paused=!0}resume(e){if(!this.paused)throw Error("Parser was already resumed");this.paused=!1,this.inLoop||(this._runParsingLoop(),this.paused||null==e||e())}write(e,t,n){this.active=!0,this.preprocessor.write(e,t),this._runParsingLoop(),this.paused||null==n||n()}insertHtmlAtCurrentPos(e){this.active=!0,this.preprocessor.insertHtmlAtCurrentPos(e),this._runParsingLoop()}_ensureHibernation(){return!!this.preprocessor.endOfChunkHit&&(this.preprocessor.retreat(this.consumedAfterSnapshot),this.consumedAfterSnapshot=0,this.active=!1,!0)}_consume(){return this.consumedAfterSnapshot++,this.preprocessor.advance()}_advanceBy(e){this.consumedAfterSnapshot+=e;for(let t=0;t<e;t++)this.preprocessor.advance()}_consumeSequenceIfMatch(e,t){return!!this.preprocessor.startsWith(e,t)&&(this._advanceBy(e.length-1),!0)}_createStartTagToken(){this.currentToken={type:_.START_TAG,tagName:"",tagID:D.UNKNOWN,selfClosing:!1,ackSelfClosing:!1,attrs:[],location:this.getCurrentLocation(1)}}_createEndTagToken(){this.currentToken={type:_.END_TAG,tagName:"",tagID:D.UNKNOWN,selfClosing:!1,ackSelfClosing:!1,attrs:[],location:this.getCurrentLocation(2)}}_createCommentToken(e){this.currentToken={type:_.COMMENT,data:"",location:this.getCurrentLocation(e)}}_createDoctypeToken(e){this.currentToken={type:_.DOCTYPE,name:e,forceQuirks:!1,publicId:null,systemId:null,location:this.currentLocation}}_createCharacterToken(e,t){this.currentCharacterToken={type:e,chars:t,location:this.currentLocation}}_createAttr(e){this.currentAttr={name:e,value:""},this.currentLocation=this.getCurrentLocation(0)}_leaveAttrName(){var e,t;let n=this.currentToken;null===e$(n,this.currentAttr.name)?(n.attrs.push(this.currentAttr),n.location&&this.currentLocation&&((null!==(e=(t=n.location).attrs)&&void 0!==e?e:t.attrs=Object.create(null))[this.currentAttr.name]=this.currentLocation,this._leaveAttrValue())):this._err(A.duplicateAttribute)}_leaveAttrValue(){this.currentLocation&&(this.currentLocation.endLine=this.preprocessor.line,this.currentLocation.endCol=this.preprocessor.col,this.currentLocation.endOffset=this.preprocessor.offset)}prepareToken(e){this._emitCurrentCharacterToken(e.location),this.currentToken=null,e.location&&(e.location.endLine=this.preprocessor.line,e.location.endCol=this.preprocessor.col+1,e.location.endOffset=this.preprocessor.offset+1),this.currentLocation=this.getCurrentLocation(-1)}emitCurrentTagToken(){let e=this.currentToken;this.prepareToken(e),e.tagID=eJ(e.tagName),e.type===_.START_TAG?(this.lastStartTagName=e.tagName,this.handler.onStartTag(e)):(e.attrs.length>0&&this._err(A.endTagWithAttributes),e.selfClosing&&this._err(A.endTagWithTrailingSolidus),this.handler.onEndTag(e)),this.preprocessor.dropParsedChunk()}emitCurrentComment(e){this.prepareToken(e),this.handler.onComment(e),this.preprocessor.dropParsedChunk()}emitCurrentDoctype(e){this.prepareToken(e),this.handler.onDoctype(e),this.preprocessor.dropParsedChunk()}_emitCurrentCharacterToken(e){if(this.currentCharacterToken){switch(e&&this.currentCharacterToken.location&&(this.currentCharacterToken.location.endLine=e.startLine,this.currentCharacterToken.location.endCol=e.startCol,this.currentCharacterToken.location.endOffset=e.startOffset),this.currentCharacterToken.type){case _.CHARACTER:this.handler.onCharacter(this.currentCharacterToken);break;case _.NULL_CHARACTER:this.handler.onNullCharacter(this.currentCharacterToken);break;case _.WHITESPACE_CHARACTER:this.handler.onWhitespaceCharacter(this.currentCharacterToken)}this.currentCharacterToken=null}}_emitEOFToken(){let e=this.getCurrentLocation(0);e&&(e.endLine=e.startLine,e.endCol=e.startCol,e.endOffset=e.startOffset),this._emitCurrentCharacterToken(e),this.handler.onEof({type:_.EOF,location:e}),this.active=!1}_appendCharToCurrentCharacterToken(e,t){if(this.currentCharacterToken){if(this.currentCharacterToken.type===e){this.currentCharacterToken.chars+=t;return}this.currentLocation=this.getCurrentLocation(0),this._emitCurrentCharacterToken(this.currentLocation),this.preprocessor.dropParsedChunk()}this._createCharacterToken(e,t)}_emitCodePoint(e){let t=e8(e)?_.WHITESPACE_CHARACTER:e===T.NULL?_.NULL_CHARACTER:_.CHARACTER;this._appendCharToCurrentCharacterToken(t,String.fromCodePoint(e))}_emitChars(e){this._appendCharToCurrentCharacterToken(_.CHARACTER,e)}_startCharacterReference(){this.returnState=this.state,this.state=R.CHARACTER_REFERENCE,this.entityStartPos=this.preprocessor.pos,this.entityDecoder.startEntity(this._isCharacterReferenceInAttribute()?C.Attribute:C.Legacy)}_isCharacterReferenceInAttribute(){return this.returnState===R.ATTRIBUTE_VALUE_DOUBLE_QUOTED||this.returnState===R.ATTRIBUTE_VALUE_SINGLE_QUOTED||this.returnState===R.ATTRIBUTE_VALUE_UNQUOTED}_flushCodePointConsumedAsCharacterReference(e){this._isCharacterReferenceInAttribute()?this.currentAttr.value+=String.fromCodePoint(e):this._emitCodePoint(e)}_callState(e){switch(this.state){case R.DATA:this._stateData(e);break;case R.RCDATA:this._stateRcdata(e);break;case R.RAWTEXT:this._stateRawtext(e);break;case R.SCRIPT_DATA:this._stateScriptData(e);break;case R.PLAINTEXT:this._statePlaintext(e);break;case R.TAG_OPEN:this._stateTagOpen(e);break;case R.END_TAG_OPEN:this._stateEndTagOpen(e);break;case R.TAG_NAME:this._stateTagName(e);break;case R.RCDATA_LESS_THAN_SIGN:this._stateRcdataLessThanSign(e);break;case R.RCDATA_END_TAG_OPEN:this._stateRcdataEndTagOpen(e);break;case R.RCDATA_END_TAG_NAME:this._stateRcdataEndTagName(e);break;case R.RAWTEXT_LESS_THAN_SIGN:this._stateRawtextLessThanSign(e);break;case R.RAWTEXT_END_TAG_OPEN:this._stateRawtextEndTagOpen(e);break;case R.RAWTEXT_END_TAG_NAME:this._stateRawtextEndTagName(e);break;case R.SCRIPT_DATA_LESS_THAN_SIGN:this._stateScriptDataLessThanSign(e);break;case R.SCRIPT_DATA_END_TAG_OPEN:this._stateScriptDataEndTagOpen(e);break;case R.SCRIPT_DATA_END_TAG_NAME:this._stateScriptDataEndTagName(e);break;case R.SCRIPT_DATA_ESCAPE_START:this._stateScriptDataEscapeStart(e);break;case R.SCRIPT_DATA_ESCAPE_START_DASH:this._stateScriptDataEscapeStartDash(e);break;case R.SCRIPT_DATA_ESCAPED:this._stateScriptDataEscaped(e);break;case R.SCRIPT_DATA_ESCAPED_DASH:this._stateScriptDataEscapedDash(e);break;case R.SCRIPT_DATA_ESCAPED_DASH_DASH:this._stateScriptDataEscapedDashDash(e);break;case R.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN:this._stateScriptDataEscapedLessThanSign(e);break;case R.SCRIPT_DATA_ESCAPED_END_TAG_OPEN:this._stateScriptDataEscapedEndTagOpen(e);break;case R.SCRIPT_DATA_ESCAPED_END_TAG_NAME:this._stateScriptDataEscapedEndTagName(e);break;case R.SCRIPT_DATA_DOUBLE_ESCAPE_START:this._stateScriptDataDoubleEscapeStart(e);break;case R.SCRIPT_DATA_DOUBLE_ESCAPED:this._stateScriptDataDoubleEscaped(e);break;case R.SCRIPT_DATA_DOUBLE_ESCAPED_DASH:this._stateScriptDataDoubleEscapedDash(e);break;case R.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH:this._stateScriptDataDoubleEscapedDashDash(e);break;case R.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN:this._stateScriptDataDoubleEscapedLessThanSign(e);break;case R.SCRIPT_DATA_DOUBLE_ESCAPE_END:this._stateScriptDataDoubleEscapeEnd(e);break;case R.BEFORE_ATTRIBUTE_NAME:this._stateBeforeAttributeName(e);break;case R.ATTRIBUTE_NAME:this._stateAttributeName(e);break;case R.AFTER_ATTRIBUTE_NAME:this._stateAfterAttributeName(e);break;case R.BEFORE_ATTRIBUTE_VALUE:this._stateBeforeAttributeValue(e);break;case R.ATTRIBUTE_VALUE_DOUBLE_QUOTED:this._stateAttributeValueDoubleQuoted(e);break;case R.ATTRIBUTE_VALUE_SINGLE_QUOTED:this._stateAttributeValueSingleQuoted(e);break;case R.ATTRIBUTE_VALUE_UNQUOTED:this._stateAttributeValueUnquoted(e);break;case R.AFTER_ATTRIBUTE_VALUE_QUOTED:this._stateAfterAttributeValueQuoted(e);break;case R.SELF_CLOSING_START_TAG:this._stateSelfClosingStartTag(e);break;case R.BOGUS_COMMENT:this._stateBogusComment(e);break;case R.MARKUP_DECLARATION_OPEN:this._stateMarkupDeclarationOpen(e);break;case R.COMMENT_START:this._stateCommentStart(e);break;case R.COMMENT_START_DASH:this._stateCommentStartDash(e);break;case R.COMMENT:this._stateComment(e);break;case R.COMMENT_LESS_THAN_SIGN:this._stateCommentLessThanSign(e);break;case R.COMMENT_LESS_THAN_SIGN_BANG:this._stateCommentLessThanSignBang(e);break;case R.COMMENT_LESS_THAN_SIGN_BANG_DASH:this._stateCommentLessThanSignBangDash(e);break;case R.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH:this._stateCommentLessThanSignBangDashDash(e);break;case R.COMMENT_END_DASH:this._stateCommentEndDash(e);break;case R.COMMENT_END:this._stateCommentEnd(e);break;case R.COMMENT_END_BANG:this._stateCommentEndBang(e);break;case R.DOCTYPE:this._stateDoctype(e);break;case R.BEFORE_DOCTYPE_NAME:this._stateBeforeDoctypeName(e);break;case R.DOCTYPE_NAME:this._stateDoctypeName(e);break;case R.AFTER_DOCTYPE_NAME:this._stateAfterDoctypeName(e);break;case R.AFTER_DOCTYPE_PUBLIC_KEYWORD:this._stateAfterDoctypePublicKeyword(e);break;case R.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER:this._stateBeforeDoctypePublicIdentifier(e);break;case R.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED:this._stateDoctypePublicIdentifierDoubleQuoted(e);break;case R.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED:this._stateDoctypePublicIdentifierSingleQuoted(e);break;case R.AFTER_DOCTYPE_PUBLIC_IDENTIFIER:this._stateAfterDoctypePublicIdentifier(e);break;case R.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS:this._stateBetweenDoctypePublicAndSystemIdentifiers(e);break;case R.AFTER_DOCTYPE_SYSTEM_KEYWORD:this._stateAfterDoctypeSystemKeyword(e);break;case R.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER:this._stateBeforeDoctypeSystemIdentifier(e);break;case R.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED:this._stateDoctypeSystemIdentifierDoubleQuoted(e);break;case R.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED:this._stateDoctypeSystemIdentifierSingleQuoted(e);break;case R.AFTER_DOCTYPE_SYSTEM_IDENTIFIER:this._stateAfterDoctypeSystemIdentifier(e);break;case R.BOGUS_DOCTYPE:this._stateBogusDoctype(e);break;case R.CDATA_SECTION:this._stateCdataSection(e);break;case R.CDATA_SECTION_BRACKET:this._stateCdataSectionBracket(e);break;case R.CDATA_SECTION_END:this._stateCdataSectionEnd(e);break;case R.CHARACTER_REFERENCE:this._stateCharacterReference();break;case R.AMBIGUOUS_AMPERSAND:this._stateAmbiguousAmpersand(e);break;default:throw Error("Unknown state")}}_stateData(e){switch(e){case T.LESS_THAN_SIGN:this.state=R.TAG_OPEN;break;case T.AMPERSAND:this._startCharacterReference();break;case T.NULL:this._err(A.unexpectedNullCharacter),this._emitCodePoint(e);break;case T.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateRcdata(e){switch(e){case T.AMPERSAND:this._startCharacterReference();break;case T.LESS_THAN_SIGN:this.state=R.RCDATA_LESS_THAN_SIGN;break;case T.NULL:this._err(A.unexpectedNullCharacter),this._emitChars("�");break;case T.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateRawtext(e){switch(e){case T.LESS_THAN_SIGN:this.state=R.RAWTEXT_LESS_THAN_SIGN;break;case T.NULL:this._err(A.unexpectedNullCharacter),this._emitChars("�");break;case T.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateScriptData(e){switch(e){case T.LESS_THAN_SIGN:this.state=R.SCRIPT_DATA_LESS_THAN_SIGN;break;case T.NULL:this._err(A.unexpectedNullCharacter),this._emitChars("�");break;case T.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_statePlaintext(e){switch(e){case T.NULL:this._err(A.unexpectedNullCharacter),this._emitChars("�");break;case T.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateTagOpen(e){if(e5(e))this._createStartTagToken(),this.state=R.TAG_NAME,this._stateTagName(e);else switch(e){case T.EXCLAMATION_MARK:this.state=R.MARKUP_DECLARATION_OPEN;break;case T.SOLIDUS:this.state=R.END_TAG_OPEN;break;case T.QUESTION_MARK:this._err(A.unexpectedQuestionMarkInsteadOfTagName),this._createCommentToken(1),this.state=R.BOGUS_COMMENT,this._stateBogusComment(e);break;case T.EOF:this._err(A.eofBeforeTagName),this._emitChars("<"),this._emitEOFToken();break;default:this._err(A.invalidFirstCharacterOfTagName),this._emitChars("<"),this.state=R.DATA,this._stateData(e)}}_stateEndTagOpen(e){if(e5(e))this._createEndTagToken(),this.state=R.TAG_NAME,this._stateTagName(e);else switch(e){case T.GREATER_THAN_SIGN:this._err(A.missingEndTagName),this.state=R.DATA;break;case T.EOF:this._err(A.eofBeforeTagName),this._emitChars("</"),this._emitEOFToken();break;default:this._err(A.invalidFirstCharacterOfTagName),this._createCommentToken(2),this.state=R.BOGUS_COMMENT,this._stateBogusComment(e)}}_stateTagName(e){let t=this.currentToken;switch(e){case T.SPACE:case T.LINE_FEED:case T.TABULATION:case T.FORM_FEED:this.state=R.BEFORE_ATTRIBUTE_NAME;break;case T.SOLIDUS:this.state=R.SELF_CLOSING_START_TAG;break;case T.GREATER_THAN_SIGN:this.state=R.DATA,this.emitCurrentTagToken();break;case T.NULL:this._err(A.unexpectedNullCharacter),t.tagName+="�";break;case T.EOF:this._err(A.eofInTag),this._emitEOFToken();break;default:t.tagName+=String.fromCodePoint(e3(e)?e+32:e)}}_stateRcdataLessThanSign(e){e===T.SOLIDUS?this.state=R.RCDATA_END_TAG_OPEN:(this._emitChars("<"),this.state=R.RCDATA,this._stateRcdata(e))}_stateRcdataEndTagOpen(e){e5(e)?(this.state=R.RCDATA_END_TAG_NAME,this._stateRcdataEndTagName(e)):(this._emitChars("</"),this.state=R.RCDATA,this._stateRcdata(e))}handleSpecialEndTag(e){if(!this.preprocessor.startsWith(this.lastStartTagName,!1))return!this._ensureHibernation();switch(this._createEndTagToken(),this.currentToken.tagName=this.lastStartTagName,this.preprocessor.peek(this.lastStartTagName.length)){case T.SPACE:case T.LINE_FEED:case T.TABULATION:case T.FORM_FEED:return this._advanceBy(this.lastStartTagName.length),this.state=R.BEFORE_ATTRIBUTE_NAME,!1;case T.SOLIDUS:return this._advanceBy(this.lastStartTagName.length),this.state=R.SELF_CLOSING_START_TAG,!1;case T.GREATER_THAN_SIGN:return this._advanceBy(this.lastStartTagName.length),this.emitCurrentTagToken(),this.state=R.DATA,!1;default:return!this._ensureHibernation()}}_stateRcdataEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=R.RCDATA,this._stateRcdata(e))}_stateRawtextLessThanSign(e){e===T.SOLIDUS?this.state=R.RAWTEXT_END_TAG_OPEN:(this._emitChars("<"),this.state=R.RAWTEXT,this._stateRawtext(e))}_stateRawtextEndTagOpen(e){e5(e)?(this.state=R.RAWTEXT_END_TAG_NAME,this._stateRawtextEndTagName(e)):(this._emitChars("</"),this.state=R.RAWTEXT,this._stateRawtext(e))}_stateRawtextEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=R.RAWTEXT,this._stateRawtext(e))}_stateScriptDataLessThanSign(e){switch(e){case T.SOLIDUS:this.state=R.SCRIPT_DATA_END_TAG_OPEN;break;case T.EXCLAMATION_MARK:this.state=R.SCRIPT_DATA_ESCAPE_START,this._emitChars("<!");break;default:this._emitChars("<"),this.state=R.SCRIPT_DATA,this._stateScriptData(e)}}_stateScriptDataEndTagOpen(e){e5(e)?(this.state=R.SCRIPT_DATA_END_TAG_NAME,this._stateScriptDataEndTagName(e)):(this._emitChars("</"),this.state=R.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=R.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEscapeStart(e){e===T.HYPHEN_MINUS?(this.state=R.SCRIPT_DATA_ESCAPE_START_DASH,this._emitChars("-")):(this.state=R.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEscapeStartDash(e){e===T.HYPHEN_MINUS?(this.state=R.SCRIPT_DATA_ESCAPED_DASH_DASH,this._emitChars("-")):(this.state=R.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEscaped(e){switch(e){case T.HYPHEN_MINUS:this.state=R.SCRIPT_DATA_ESCAPED_DASH,this._emitChars("-");break;case T.LESS_THAN_SIGN:this.state=R.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break;case T.NULL:this._err(A.unexpectedNullCharacter),this._emitChars("�");break;case T.EOF:this._err(A.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateScriptDataEscapedDash(e){switch(e){case T.HYPHEN_MINUS:this.state=R.SCRIPT_DATA_ESCAPED_DASH_DASH,this._emitChars("-");break;case T.LESS_THAN_SIGN:this.state=R.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break;case T.NULL:this._err(A.unexpectedNullCharacter),this.state=R.SCRIPT_DATA_ESCAPED,this._emitChars("�");break;case T.EOF:this._err(A.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=R.SCRIPT_DATA_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataEscapedDashDash(e){switch(e){case T.HYPHEN_MINUS:this._emitChars("-");break;case T.LESS_THAN_SIGN:this.state=R.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break;case T.GREATER_THAN_SIGN:this.state=R.SCRIPT_DATA,this._emitChars(">");break;case T.NULL:this._err(A.unexpectedNullCharacter),this.state=R.SCRIPT_DATA_ESCAPED,this._emitChars("�");break;case T.EOF:this._err(A.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=R.SCRIPT_DATA_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataEscapedLessThanSign(e){e===T.SOLIDUS?this.state=R.SCRIPT_DATA_ESCAPED_END_TAG_OPEN:e5(e)?(this._emitChars("<"),this.state=R.SCRIPT_DATA_DOUBLE_ESCAPE_START,this._stateScriptDataDoubleEscapeStart(e)):(this._emitChars("<"),this.state=R.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataEscapedEndTagOpen(e){e5(e)?(this.state=R.SCRIPT_DATA_ESCAPED_END_TAG_NAME,this._stateScriptDataEscapedEndTagName(e)):(this._emitChars("</"),this.state=R.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataEscapedEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=R.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataDoubleEscapeStart(e){if(this.preprocessor.startsWith(eG.SCRIPT,!1)&&e6(this.preprocessor.peek(eG.SCRIPT.length))){this._emitCodePoint(e);for(let e=0;e<eG.SCRIPT.length;e++)this._emitCodePoint(this._consume());this.state=R.SCRIPT_DATA_DOUBLE_ESCAPED}else this._ensureHibernation()||(this.state=R.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataDoubleEscaped(e){switch(e){case T.HYPHEN_MINUS:this.state=R.SCRIPT_DATA_DOUBLE_ESCAPED_DASH,this._emitChars("-");break;case T.LESS_THAN_SIGN:this.state=R.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break;case T.NULL:this._err(A.unexpectedNullCharacter),this._emitChars("�");break;case T.EOF:this._err(A.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateScriptDataDoubleEscapedDash(e){switch(e){case T.HYPHEN_MINUS:this.state=R.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH,this._emitChars("-");break;case T.LESS_THAN_SIGN:this.state=R.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break;case T.NULL:this._err(A.unexpectedNullCharacter),this.state=R.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitChars("�");break;case T.EOF:this._err(A.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=R.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataDoubleEscapedDashDash(e){switch(e){case T.HYPHEN_MINUS:this._emitChars("-");break;case T.LESS_THAN_SIGN:this.state=R.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break;case T.GREATER_THAN_SIGN:this.state=R.SCRIPT_DATA,this._emitChars(">");break;case T.NULL:this._err(A.unexpectedNullCharacter),this.state=R.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitChars("�");break;case T.EOF:this._err(A.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=R.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataDoubleEscapedLessThanSign(e){e===T.SOLIDUS?(this.state=R.SCRIPT_DATA_DOUBLE_ESCAPE_END,this._emitChars("/")):(this.state=R.SCRIPT_DATA_DOUBLE_ESCAPED,this._stateScriptDataDoubleEscaped(e))}_stateScriptDataDoubleEscapeEnd(e){if(this.preprocessor.startsWith(eG.SCRIPT,!1)&&e6(this.preprocessor.peek(eG.SCRIPT.length))){this._emitCodePoint(e);for(let e=0;e<eG.SCRIPT.length;e++)this._emitCodePoint(this._consume());this.state=R.SCRIPT_DATA_ESCAPED}else this._ensureHibernation()||(this.state=R.SCRIPT_DATA_DOUBLE_ESCAPED,this._stateScriptDataDoubleEscaped(e))}_stateBeforeAttributeName(e){switch(e){case T.SPACE:case T.LINE_FEED:case T.TABULATION:case T.FORM_FEED:break;case T.SOLIDUS:case T.GREATER_THAN_SIGN:case T.EOF:this.state=R.AFTER_ATTRIBUTE_NAME,this._stateAfterAttributeName(e);break;case T.EQUALS_SIGN:this._err(A.unexpectedEqualsSignBeforeAttributeName),this._createAttr("="),this.state=R.ATTRIBUTE_NAME;break;default:this._createAttr(""),this.state=R.ATTRIBUTE_NAME,this._stateAttributeName(e)}}_stateAttributeName(e){switch(e){case T.SPACE:case T.LINE_FEED:case T.TABULATION:case T.FORM_FEED:case T.SOLIDUS:case T.GREATER_THAN_SIGN:case T.EOF:this._leaveAttrName(),this.state=R.AFTER_ATTRIBUTE_NAME,this._stateAfterAttributeName(e);break;case T.EQUALS_SIGN:this._leaveAttrName(),this.state=R.BEFORE_ATTRIBUTE_VALUE;break;case T.QUOTATION_MARK:case T.APOSTROPHE:case T.LESS_THAN_SIGN:this._err(A.unexpectedCharacterInAttributeName),this.currentAttr.name+=String.fromCodePoint(e);break;case T.NULL:this._err(A.unexpectedNullCharacter),this.currentAttr.name+="�";break;default:this.currentAttr.name+=String.fromCodePoint(e3(e)?e+32:e)}}_stateAfterAttributeName(e){switch(e){case T.SPACE:case T.LINE_FEED:case T.TABULATION:case T.FORM_FEED:break;case T.SOLIDUS:this.state=R.SELF_CLOSING_START_TAG;break;case T.EQUALS_SIGN:this.state=R.BEFORE_ATTRIBUTE_VALUE;break;case T.GREATER_THAN_SIGN:this.state=R.DATA,this.emitCurrentTagToken();break;case T.EOF:this._err(A.eofInTag),this._emitEOFToken();break;default:this._createAttr(""),this.state=R.ATTRIBUTE_NAME,this._stateAttributeName(e)}}_stateBeforeAttributeValue(e){switch(e){case T.SPACE:case T.LINE_FEED:case T.TABULATION:case T.FORM_FEED:break;case T.QUOTATION_MARK:this.state=R.ATTRIBUTE_VALUE_DOUBLE_QUOTED;break;case T.APOSTROPHE:this.state=R.ATTRIBUTE_VALUE_SINGLE_QUOTED;break;case T.GREATER_THAN_SIGN:this._err(A.missingAttributeValue),this.state=R.DATA,this.emitCurrentTagToken();break;default:this.state=R.ATTRIBUTE_VALUE_UNQUOTED,this._stateAttributeValueUnquoted(e)}}_stateAttributeValueDoubleQuoted(e){switch(e){case T.QUOTATION_MARK:this.state=R.AFTER_ATTRIBUTE_VALUE_QUOTED;break;case T.AMPERSAND:this._startCharacterReference();break;case T.NULL:this._err(A.unexpectedNullCharacter),this.currentAttr.value+="�";break;case T.EOF:this._err(A.eofInTag),this._emitEOFToken();break;default:this.currentAttr.value+=String.fromCodePoint(e)}}_stateAttributeValueSingleQuoted(e){switch(e){case T.APOSTROPHE:this.state=R.AFTER_ATTRIBUTE_VALUE_QUOTED;break;case T.AMPERSAND:this._startCharacterReference();break;case T.NULL:this._err(A.unexpectedNullCharacter),this.currentAttr.value+="�";break;case T.EOF:this._err(A.eofInTag),this._emitEOFToken();break;default:this.currentAttr.value+=String.fromCodePoint(e)}}_stateAttributeValueUnquoted(e){switch(e){case T.SPACE:case T.LINE_FEED:case T.TABULATION:case T.FORM_FEED:this._leaveAttrValue(),this.state=R.BEFORE_ATTRIBUTE_NAME;break;case T.AMPERSAND:this._startCharacterReference();break;case T.GREATER_THAN_SIGN:this._leaveAttrValue(),this.state=R.DATA,this.emitCurrentTagToken();break;case T.NULL:this._err(A.unexpectedNullCharacter),this.currentAttr.value+="�";break;case T.QUOTATION_MARK:case T.APOSTROPHE:case T.LESS_THAN_SIGN:case T.EQUALS_SIGN:case T.GRAVE_ACCENT:this._err(A.unexpectedCharacterInUnquotedAttributeValue),this.currentAttr.value+=String.fromCodePoint(e);break;case T.EOF:this._err(A.eofInTag),this._emitEOFToken();break;default:this.currentAttr.value+=String.fromCodePoint(e)}}_stateAfterAttributeValueQuoted(e){switch(e){case T.SPACE:case T.LINE_FEED:case T.TABULATION:case T.FORM_FEED:this._leaveAttrValue(),this.state=R.BEFORE_ATTRIBUTE_NAME;break;case T.SOLIDUS:this._leaveAttrValue(),this.state=R.SELF_CLOSING_START_TAG;break;case T.GREATER_THAN_SIGN:this._leaveAttrValue(),this.state=R.DATA,this.emitCurrentTagToken();break;case T.EOF:this._err(A.eofInTag),this._emitEOFToken();break;default:this._err(A.missingWhitespaceBetweenAttributes),this.state=R.BEFORE_ATTRIBUTE_NAME,this._stateBeforeAttributeName(e)}}_stateSelfClosingStartTag(e){switch(e){case T.GREATER_THAN_SIGN:this.currentToken.selfClosing=!0,this.state=R.DATA,this.emitCurrentTagToken();break;case T.EOF:this._err(A.eofInTag),this._emitEOFToken();break;default:this._err(A.unexpectedSolidusInTag),this.state=R.BEFORE_ATTRIBUTE_NAME,this._stateBeforeAttributeName(e)}}_stateBogusComment(e){let t=this.currentToken;switch(e){case T.GREATER_THAN_SIGN:this.state=R.DATA,this.emitCurrentComment(t);break;case T.EOF:this.emitCurrentComment(t),this._emitEOFToken();break;case T.NULL:this._err(A.unexpectedNullCharacter),t.data+="�";break;default:t.data+=String.fromCodePoint(e)}}_stateMarkupDeclarationOpen(e){this._consumeSequenceIfMatch(eG.DASH_DASH,!0)?(this._createCommentToken(eG.DASH_DASH.length+1),this.state=R.COMMENT_START):this._consumeSequenceIfMatch(eG.DOCTYPE,!1)?(this.currentLocation=this.getCurrentLocation(eG.DOCTYPE.length+1),this.state=R.DOCTYPE):this._consumeSequenceIfMatch(eG.CDATA_START,!0)?this.inForeignNode?this.state=R.CDATA_SECTION:(this._err(A.cdataInHtmlContent),this._createCommentToken(eG.CDATA_START.length+1),this.currentToken.data="[CDATA[",this.state=R.BOGUS_COMMENT):this._ensureHibernation()||(this._err(A.incorrectlyOpenedComment),this._createCommentToken(2),this.state=R.BOGUS_COMMENT,this._stateBogusComment(e))}_stateCommentStart(e){switch(e){case T.HYPHEN_MINUS:this.state=R.COMMENT_START_DASH;break;case T.GREATER_THAN_SIGN:{this._err(A.abruptClosingOfEmptyComment),this.state=R.DATA;let e=this.currentToken;this.emitCurrentComment(e);break}default:this.state=R.COMMENT,this._stateComment(e)}}_stateCommentStartDash(e){let t=this.currentToken;switch(e){case T.HYPHEN_MINUS:this.state=R.COMMENT_END;break;case T.GREATER_THAN_SIGN:this._err(A.abruptClosingOfEmptyComment),this.state=R.DATA,this.emitCurrentComment(t);break;case T.EOF:this._err(A.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="-",this.state=R.COMMENT,this._stateComment(e)}}_stateComment(e){let t=this.currentToken;switch(e){case T.HYPHEN_MINUS:this.state=R.COMMENT_END_DASH;break;case T.LESS_THAN_SIGN:t.data+="<",this.state=R.COMMENT_LESS_THAN_SIGN;break;case T.NULL:this._err(A.unexpectedNullCharacter),t.data+="�";break;case T.EOF:this._err(A.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+=String.fromCodePoint(e)}}_stateCommentLessThanSign(e){let t=this.currentToken;switch(e){case T.EXCLAMATION_MARK:t.data+="!",this.state=R.COMMENT_LESS_THAN_SIGN_BANG;break;case T.LESS_THAN_SIGN:t.data+="<";break;default:this.state=R.COMMENT,this._stateComment(e)}}_stateCommentLessThanSignBang(e){e===T.HYPHEN_MINUS?this.state=R.COMMENT_LESS_THAN_SIGN_BANG_DASH:(this.state=R.COMMENT,this._stateComment(e))}_stateCommentLessThanSignBangDash(e){e===T.HYPHEN_MINUS?this.state=R.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH:(this.state=R.COMMENT_END_DASH,this._stateCommentEndDash(e))}_stateCommentLessThanSignBangDashDash(e){e!==T.GREATER_THAN_SIGN&&e!==T.EOF&&this._err(A.nestedComment),this.state=R.COMMENT_END,this._stateCommentEnd(e)}_stateCommentEndDash(e){let t=this.currentToken;switch(e){case T.HYPHEN_MINUS:this.state=R.COMMENT_END;break;case T.EOF:this._err(A.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="-",this.state=R.COMMENT,this._stateComment(e)}}_stateCommentEnd(e){let t=this.currentToken;switch(e){case T.GREATER_THAN_SIGN:this.state=R.DATA,this.emitCurrentComment(t);break;case T.EXCLAMATION_MARK:this.state=R.COMMENT_END_BANG;break;case T.HYPHEN_MINUS:t.data+="-";break;case T.EOF:this._err(A.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="--",this.state=R.COMMENT,this._stateComment(e)}}_stateCommentEndBang(e){let t=this.currentToken;switch(e){case T.HYPHEN_MINUS:t.data+="--!",this.state=R.COMMENT_END_DASH;break;case T.GREATER_THAN_SIGN:this._err(A.incorrectlyClosedComment),this.state=R.DATA,this.emitCurrentComment(t);break;case T.EOF:this._err(A.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="--!",this.state=R.COMMENT,this._stateComment(e)}}_stateDoctype(e){switch(e){case T.SPACE:case T.LINE_FEED:case T.TABULATION:case T.FORM_FEED:this.state=R.BEFORE_DOCTYPE_NAME;break;case T.GREATER_THAN_SIGN:this.state=R.BEFORE_DOCTYPE_NAME,this._stateBeforeDoctypeName(e);break;case T.EOF:{this._err(A.eofInDoctype),this._createDoctypeToken(null);let e=this.currentToken;e.forceQuirks=!0,this.emitCurrentDoctype(e),this._emitEOFToken();break}default:this._err(A.missingWhitespaceBeforeDoctypeName),this.state=R.BEFORE_DOCTYPE_NAME,this._stateBeforeDoctypeName(e)}}_stateBeforeDoctypeName(e){if(e3(e))this._createDoctypeToken(String.fromCharCode(e+32)),this.state=R.DOCTYPE_NAME;else switch(e){case T.SPACE:case T.LINE_FEED:case T.TABULATION:case T.FORM_FEED:break;case T.NULL:this._err(A.unexpectedNullCharacter),this._createDoctypeToken("�"),this.state=R.DOCTYPE_NAME;break;case T.GREATER_THAN_SIGN:{this._err(A.missingDoctypeName),this._createDoctypeToken(null);let e=this.currentToken;e.forceQuirks=!0,this.emitCurrentDoctype(e),this.state=R.DATA;break}case T.EOF:{this._err(A.eofInDoctype),this._createDoctypeToken(null);let e=this.currentToken;e.forceQuirks=!0,this.emitCurrentDoctype(e),this._emitEOFToken();break}default:this._createDoctypeToken(String.fromCodePoint(e)),this.state=R.DOCTYPE_NAME}}_stateDoctypeName(e){let t=this.currentToken;switch(e){case T.SPACE:case T.LINE_FEED:case T.TABULATION:case T.FORM_FEED:this.state=R.AFTER_DOCTYPE_NAME;break;case T.GREATER_THAN_SIGN:this.state=R.DATA,this.emitCurrentDoctype(t);break;case T.NULL:this._err(A.unexpectedNullCharacter),t.name+="�";break;case T.EOF:this._err(A.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.name+=String.fromCodePoint(e3(e)?e+32:e)}}_stateAfterDoctypeName(e){let t=this.currentToken;switch(e){case T.SPACE:case T.LINE_FEED:case T.TABULATION:case T.FORM_FEED:break;case T.GREATER_THAN_SIGN:this.state=R.DATA,this.emitCurrentDoctype(t);break;case T.EOF:this._err(A.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._consumeSequenceIfMatch(eG.PUBLIC,!1)?this.state=R.AFTER_DOCTYPE_PUBLIC_KEYWORD:this._consumeSequenceIfMatch(eG.SYSTEM,!1)?this.state=R.AFTER_DOCTYPE_SYSTEM_KEYWORD:this._ensureHibernation()||(this._err(A.invalidCharacterSequenceAfterDoctypeName),t.forceQuirks=!0,this.state=R.BOGUS_DOCTYPE,this._stateBogusDoctype(e))}}_stateAfterDoctypePublicKeyword(e){let t=this.currentToken;switch(e){case T.SPACE:case T.LINE_FEED:case T.TABULATION:case T.FORM_FEED:this.state=R.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER;break;case T.QUOTATION_MARK:this._err(A.missingWhitespaceAfterDoctypePublicKeyword),t.publicId="",this.state=R.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED;break;case T.APOSTROPHE:this._err(A.missingWhitespaceAfterDoctypePublicKeyword),t.publicId="",this.state=R.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED;break;case T.GREATER_THAN_SIGN:this._err(A.missingDoctypePublicIdentifier),t.forceQuirks=!0,this.state=R.DATA,this.emitCurrentDoctype(t);break;case T.EOF:this._err(A.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(A.missingQuoteBeforeDoctypePublicIdentifier),t.forceQuirks=!0,this.state=R.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBeforeDoctypePublicIdentifier(e){let t=this.currentToken;switch(e){case T.SPACE:case T.LINE_FEED:case T.TABULATION:case T.FORM_FEED:break;case T.QUOTATION_MARK:t.publicId="",this.state=R.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED;break;case T.APOSTROPHE:t.publicId="",this.state=R.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED;break;case T.GREATER_THAN_SIGN:this._err(A.missingDoctypePublicIdentifier),t.forceQuirks=!0,this.state=R.DATA,this.emitCurrentDoctype(t);break;case T.EOF:this._err(A.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(A.missingQuoteBeforeDoctypePublicIdentifier),t.forceQuirks=!0,this.state=R.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateDoctypePublicIdentifierDoubleQuoted(e){let t=this.currentToken;switch(e){case T.QUOTATION_MARK:this.state=R.AFTER_DOCTYPE_PUBLIC_IDENTIFIER;break;case T.NULL:this._err(A.unexpectedNullCharacter),t.publicId+="�";break;case T.GREATER_THAN_SIGN:this._err(A.abruptDoctypePublicIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=R.DATA;break;case T.EOF:this._err(A.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.publicId+=String.fromCodePoint(e)}}_stateDoctypePublicIdentifierSingleQuoted(e){let t=this.currentToken;switch(e){case T.APOSTROPHE:this.state=R.AFTER_DOCTYPE_PUBLIC_IDENTIFIER;break;case T.NULL:this._err(A.unexpectedNullCharacter),t.publicId+="�";break;case T.GREATER_THAN_SIGN:this._err(A.abruptDoctypePublicIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=R.DATA;break;case T.EOF:this._err(A.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.publicId+=String.fromCodePoint(e)}}_stateAfterDoctypePublicIdentifier(e){let t=this.currentToken;switch(e){case T.SPACE:case T.LINE_FEED:case T.TABULATION:case T.FORM_FEED:this.state=R.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS;break;case T.GREATER_THAN_SIGN:this.state=R.DATA,this.emitCurrentDoctype(t);break;case T.QUOTATION_MARK:this._err(A.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers),t.systemId="",this.state=R.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case T.APOSTROPHE:this._err(A.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers),t.systemId="",this.state=R.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case T.EOF:this._err(A.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(A.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=R.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBetweenDoctypePublicAndSystemIdentifiers(e){let t=this.currentToken;switch(e){case T.SPACE:case T.LINE_FEED:case T.TABULATION:case T.FORM_FEED:break;case T.GREATER_THAN_SIGN:this.emitCurrentDoctype(t),this.state=R.DATA;break;case T.QUOTATION_MARK:t.systemId="",this.state=R.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case T.APOSTROPHE:t.systemId="",this.state=R.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case T.EOF:this._err(A.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(A.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=R.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateAfterDoctypeSystemKeyword(e){let t=this.currentToken;switch(e){case T.SPACE:case T.LINE_FEED:case T.TABULATION:case T.FORM_FEED:this.state=R.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER;break;case T.QUOTATION_MARK:this._err(A.missingWhitespaceAfterDoctypeSystemKeyword),t.systemId="",this.state=R.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case T.APOSTROPHE:this._err(A.missingWhitespaceAfterDoctypeSystemKeyword),t.systemId="",this.state=R.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case T.GREATER_THAN_SIGN:this._err(A.missingDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=R.DATA,this.emitCurrentDoctype(t);break;case T.EOF:this._err(A.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(A.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=R.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBeforeDoctypeSystemIdentifier(e){let t=this.currentToken;switch(e){case T.SPACE:case T.LINE_FEED:case T.TABULATION:case T.FORM_FEED:break;case T.QUOTATION_MARK:t.systemId="",this.state=R.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case T.APOSTROPHE:t.systemId="",this.state=R.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case T.GREATER_THAN_SIGN:this._err(A.missingDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=R.DATA,this.emitCurrentDoctype(t);break;case T.EOF:this._err(A.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(A.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=R.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateDoctypeSystemIdentifierDoubleQuoted(e){let t=this.currentToken;switch(e){case T.QUOTATION_MARK:this.state=R.AFTER_DOCTYPE_SYSTEM_IDENTIFIER;break;case T.NULL:this._err(A.unexpectedNullCharacter),t.systemId+="�";break;case T.GREATER_THAN_SIGN:this._err(A.abruptDoctypeSystemIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=R.DATA;break;case T.EOF:this._err(A.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.systemId+=String.fromCodePoint(e)}}_stateDoctypeSystemIdentifierSingleQuoted(e){let t=this.currentToken;switch(e){case T.APOSTROPHE:this.state=R.AFTER_DOCTYPE_SYSTEM_IDENTIFIER;break;case T.NULL:this._err(A.unexpectedNullCharacter),t.systemId+="�";break;case T.GREATER_THAN_SIGN:this._err(A.abruptDoctypeSystemIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=R.DATA;break;case T.EOF:this._err(A.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.systemId+=String.fromCodePoint(e)}}_stateAfterDoctypeSystemIdentifier(e){let t=this.currentToken;switch(e){case T.SPACE:case T.LINE_FEED:case T.TABULATION:case T.FORM_FEED:break;case T.GREATER_THAN_SIGN:this.emitCurrentDoctype(t),this.state=R.DATA;break;case T.EOF:this._err(A.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(A.unexpectedCharacterAfterDoctypeSystemIdentifier),this.state=R.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBogusDoctype(e){let t=this.currentToken;switch(e){case T.GREATER_THAN_SIGN:this.emitCurrentDoctype(t),this.state=R.DATA;break;case T.NULL:this._err(A.unexpectedNullCharacter);break;case T.EOF:this.emitCurrentDoctype(t),this._emitEOFToken()}}_stateCdataSection(e){switch(e){case T.RIGHT_SQUARE_BRACKET:this.state=R.CDATA_SECTION_BRACKET;break;case T.EOF:this._err(A.eofInCdata),this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateCdataSectionBracket(e){e===T.RIGHT_SQUARE_BRACKET?this.state=R.CDATA_SECTION_END:(this._emitChars("]"),this.state=R.CDATA_SECTION,this._stateCdataSection(e))}_stateCdataSectionEnd(e){switch(e){case T.GREATER_THAN_SIGN:this.state=R.DATA;break;case T.RIGHT_SQUARE_BRACKET:this._emitChars("]");break;default:this._emitChars("]]"),this.state=R.CDATA_SECTION,this._stateCdataSection(e)}}_stateCharacterReference(){let e=this.entityDecoder.write(this.preprocessor.html,this.preprocessor.pos);if(e<0){if(this.preprocessor.lastChunkWritten)e=this.entityDecoder.end();else{this.active=!1,this.preprocessor.pos=this.preprocessor.html.length-1,this.consumedAfterSnapshot=0,this.preprocessor.endOfChunkHit=!0;return}}0===e?(this.preprocessor.pos=this.entityStartPos,this._flushCodePointConsumedAsCharacterReference(T.AMPERSAND),this.state=!this._isCharacterReferenceInAttribute()&&e4(this.preprocessor.peek(1))?R.AMBIGUOUS_AMPERSAND:this.returnState):this.state=this.returnState}_stateAmbiguousAmpersand(e){e4(e)?this._flushCodePointConsumedAsCharacterReference(e):(e===T.SEMICOLON&&this._err(A.unknownNamedCharacterReference),this.state=this.returnState,this._callState(e))}}let e7=new Set([D.DD,D.DT,D.LI,D.OPTGROUP,D.OPTION,D.P,D.RB,D.RP,D.RT,D.RTC]),te=new Set([...e7,D.CAPTION,D.COLGROUP,D.TBODY,D.TD,D.TFOOT,D.TH,D.THEAD,D.TR]),tt=new Set([D.APPLET,D.CAPTION,D.HTML,D.MARQUEE,D.OBJECT,D.TABLE,D.TD,D.TEMPLATE,D.TH]),tn=new Set([...tt,D.OL,D.UL]),tr=new Set([...tt,D.BUTTON]),ts=new Set([D.ANNOTATION_XML,D.MI,D.MN,D.MO,D.MS,D.MTEXT]),ti=new Set([D.DESC,D.FOREIGN_OBJECT,D.TITLE]),ta=new Set([D.TR,D.TEMPLATE,D.HTML]),to=new Set([D.TBODY,D.TFOOT,D.THEAD,D.TEMPLATE,D.HTML]),tl=new Set([D.TABLE,D.TEMPLATE,D.HTML]),tc=new Set([D.TD,D.TH]);class tu{get currentTmplContentOrNode(){return this._isInTemplate()?this.treeAdapter.getTemplateContent(this.current):this.current}constructor(e,t,n){this.treeAdapter=t,this.handler=n,this.items=[],this.tagIDs=[],this.stackTop=-1,this.tmplCount=0,this.currentTagId=D.UNKNOWN,this.current=e}_indexOf(e){return this.items.lastIndexOf(e,this.stackTop)}_isInTemplate(){return this.currentTagId===D.TEMPLATE&&this.treeAdapter.getNamespaceURI(this.current)===N.HTML}_updateCurrentElement(){this.current=this.items[this.stackTop],this.currentTagId=this.tagIDs[this.stackTop]}push(e,t){this.stackTop++,this.items[this.stackTop]=e,this.current=e,this.tagIDs[this.stackTop]=t,this.currentTagId=t,this._isInTemplate()&&this.tmplCount++,this.handler.onItemPush(e,t,!0)}pop(){let e=this.current;this.tmplCount>0&&this._isInTemplate()&&this.tmplCount--,this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(e,!0)}replace(e,t){let n=this._indexOf(e);this.items[n]=t,n===this.stackTop&&(this.current=t)}insertAfter(e,t,n){let r=this._indexOf(e)+1;this.items.splice(r,0,t),this.tagIDs.splice(r,0,n),this.stackTop++,r===this.stackTop&&this._updateCurrentElement(),this.current&&void 0!==this.currentTagId&&this.handler.onItemPush(this.current,this.currentTagId,r===this.stackTop)}popUntilTagNamePopped(e){let t=this.stackTop+1;do t=this.tagIDs.lastIndexOf(e,t-1);while(t>0&&this.treeAdapter.getNamespaceURI(this.items[t])!==N.HTML);this.shortenToLength(Math.max(t,0))}shortenToLength(e){for(;this.stackTop>=e;){let t=this.current;this.tmplCount>0&&this._isInTemplate()&&(this.tmplCount-=1),this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(t,this.stackTop<e)}}popUntilElementPopped(e){let t=this._indexOf(e);this.shortenToLength(Math.max(t,0))}popUntilPopped(e,t){let n=this._indexOfTagNames(e,t);this.shortenToLength(Math.max(n,0))}popUntilNumberedHeaderPopped(){this.popUntilPopped(e1,N.HTML)}popUntilTableCellPopped(){this.popUntilPopped(tc,N.HTML)}popAllUpToHtmlElement(){this.tmplCount=0,this.shortenToLength(1)}_indexOfTagNames(e,t){for(let n=this.stackTop;n>=0;n--)if(e.has(this.tagIDs[n])&&this.treeAdapter.getNamespaceURI(this.items[n])===t)return n;return -1}clearBackTo(e,t){let n=this._indexOfTagNames(e,t);this.shortenToLength(n+1)}clearBackToTableContext(){this.clearBackTo(tl,N.HTML)}clearBackToTableBodyContext(){this.clearBackTo(to,N.HTML)}clearBackToTableRowContext(){this.clearBackTo(ta,N.HTML)}remove(e){let t=this._indexOf(e);t>=0&&(t===this.stackTop?this.pop():(this.items.splice(t,1),this.tagIDs.splice(t,1),this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(e,!1)))}tryPeekProperlyNestedBodyElement(){return this.stackTop>=1&&this.tagIDs[1]===D.BODY?this.items[1]:null}contains(e){return this._indexOf(e)>-1}getCommonAncestor(e){let t=this._indexOf(e)-1;return t>=0?this.items[t]:null}isRootHtmlElementCurrent(){return 0===this.stackTop&&this.tagIDs[0]===D.HTML}hasInDynamicScope(e,t){for(let n=this.stackTop;n>=0;n--){let r=this.tagIDs[n];switch(this.treeAdapter.getNamespaceURI(this.items[n])){case N.HTML:if(r===e)return!0;if(t.has(r))return!1;break;case N.SVG:if(ti.has(r))return!1;break;case N.MATHML:if(ts.has(r))return!1}}return!0}hasInScope(e){return this.hasInDynamicScope(e,tt)}hasInListItemScope(e){return this.hasInDynamicScope(e,tn)}hasInButtonScope(e){return this.hasInDynamicScope(e,tr)}hasNumberedHeaderInScope(){for(let e=this.stackTop;e>=0;e--){let t=this.tagIDs[e];switch(this.treeAdapter.getNamespaceURI(this.items[e])){case N.HTML:if(e1.has(t))return!0;if(tt.has(t))return!1;break;case N.SVG:if(ti.has(t))return!1;break;case N.MATHML:if(ts.has(t))return!1}}return!0}hasInTableScope(e){for(let t=this.stackTop;t>=0;t--)if(this.treeAdapter.getNamespaceURI(this.items[t])===N.HTML)switch(this.tagIDs[t]){case e:return!0;case D.TABLE:case D.HTML:return!1}return!0}hasTableBodyContextInTableScope(){for(let e=this.stackTop;e>=0;e--)if(this.treeAdapter.getNamespaceURI(this.items[e])===N.HTML)switch(this.tagIDs[e]){case D.TBODY:case D.THEAD:case D.TFOOT:return!0;case D.TABLE:case D.HTML:return!1}return!0}hasInSelectScope(e){for(let t=this.stackTop;t>=0;t--)if(this.treeAdapter.getNamespaceURI(this.items[t])===N.HTML)switch(this.tagIDs[t]){case e:return!0;case D.OPTION:case D.OPTGROUP:break;default:return!1}return!0}generateImpliedEndTags(){for(;void 0!==this.currentTagId&&e7.has(this.currentTagId);)this.pop()}generateImpliedEndTagsThoroughly(){for(;void 0!==this.currentTagId&&te.has(this.currentTagId);)this.pop()}generateImpliedEndTagsWithExclusion(e){for(;void 0!==this.currentTagId&&this.currentTagId!==e&&te.has(this.currentTagId);)this.pop()}}(g=M||(M={}))[g.Marker=0]="Marker",g[g.Element=1]="Element";let th={type:M.Marker};class td{constructor(e){this.treeAdapter=e,this.entries=[],this.bookmark=null}_getNoahArkConditionCandidates(e,t){let n=[],r=t.length,s=this.treeAdapter.getTagName(e),i=this.treeAdapter.getNamespaceURI(e);for(let e=0;e<this.entries.length;e++){let t=this.entries[e];if(t.type===M.Marker)break;let{element:a}=t;if(this.treeAdapter.getTagName(a)===s&&this.treeAdapter.getNamespaceURI(a)===i){let t=this.treeAdapter.getAttrList(a);t.length===r&&n.push({idx:e,attrs:t})}}return n}_ensureNoahArkCondition(e){if(this.entries.length<3)return;let t=this.treeAdapter.getAttrList(e),n=this._getNoahArkConditionCandidates(e,t);if(n.length<3)return;let r=new Map(t.map(e=>[e.name,e.value])),s=0;for(let e=0;e<n.length;e++){let t=n[e];t.attrs.every(e=>r.get(e.name)===e.value)&&(s+=1)>=3&&this.entries.splice(t.idx,1)}}insertMarker(){this.entries.unshift(th)}pushElement(e,t){this._ensureNoahArkCondition(e),this.entries.unshift({type:M.Element,element:e,token:t})}insertElementAfterBookmark(e,t){let n=this.entries.indexOf(this.bookmark);this.entries.splice(n,0,{type:M.Element,element:e,token:t})}removeEntry(e){let t=this.entries.indexOf(e);-1!==t&&this.entries.splice(t,1)}clearToLastMarker(){let e=this.entries.indexOf(th);-1===e?this.entries.length=0:this.entries.splice(0,e+1)}getElementEntryInScopeWithTagName(e){let t=this.entries.find(t=>t.type===M.Marker||this.treeAdapter.getTagName(t.element)===e);return t&&t.type===M.Element?t:null}getElementEntry(e){return this.entries.find(t=>t.type===M.Element&&t.element===e)}}let tp={createDocument:()=>({nodeName:"#document",mode:b.NO_QUIRKS,childNodes:[]}),createDocumentFragment:()=>({nodeName:"#document-fragment",childNodes:[]}),createElement:(e,t,n)=>({nodeName:e,tagName:e,attrs:n,namespaceURI:t,childNodes:[],parentNode:null}),createCommentNode:e=>({nodeName:"#comment",data:e,parentNode:null}),createTextNode:e=>({nodeName:"#text",value:e,parentNode:null}),appendChild(e,t){e.childNodes.push(t),t.parentNode=e},insertBefore(e,t,n){let r=e.childNodes.indexOf(n);e.childNodes.splice(r,0,t),t.parentNode=e},setTemplateContent(e,t){e.content=t},getTemplateContent:e=>e.content,setDocumentType(e,t,n,r){let s=e.childNodes.find(e=>"#documentType"===e.nodeName);s?(s.name=t,s.publicId=n,s.systemId=r):tp.appendChild(e,{nodeName:"#documentType",name:t,publicId:n,systemId:r,parentNode:null})},setDocumentMode(e,t){e.mode=t},getDocumentMode:e=>e.mode,detachNode(e){if(e.parentNode){let t=e.parentNode.childNodes.indexOf(e);e.parentNode.childNodes.splice(t,1),e.parentNode=null}},insertText(e,t){if(e.childNodes.length>0){let n=e.childNodes[e.childNodes.length-1];if(tp.isTextNode(n)){n.value+=t;return}}tp.appendChild(e,tp.createTextNode(t))},insertTextBefore(e,t,n){let r=e.childNodes[e.childNodes.indexOf(n)-1];r&&tp.isTextNode(r)?r.value+=t:tp.insertBefore(e,tp.createTextNode(t),n)},adoptAttributes(e,t){let n=new Set(e.attrs.map(e=>e.name));for(let r=0;r<t.length;r++)n.has(t[r].name)||e.attrs.push(t[r])},getFirstChild:e=>e.childNodes[0],getChildNodes:e=>e.childNodes,getParentNode:e=>e.parentNode,getAttrList:e=>e.attrs,getTagName:e=>e.tagName,getNamespaceURI:e=>e.namespaceURI,getTextNodeContent:e=>e.value,getCommentNodeContent:e=>e.data,getDocumentTypeNodeName:e=>e.name,getDocumentTypeNodePublicId:e=>e.publicId,getDocumentTypeNodeSystemId:e=>e.systemId,isTextNode:e=>"#text"===e.nodeName,isCommentNode:e=>"#comment"===e.nodeName,isDocumentTypeNode:e=>"#documentType"===e.nodeName,isElementNode:e=>Object.prototype.hasOwnProperty.call(e,"tagName"),setNodeSourceCodeLocation(e,t){e.sourceCodeLocation=t},getNodeSourceCodeLocation:e=>e.sourceCodeLocation,updateNodeSourceCodeLocation(e,t){e.sourceCodeLocation={...e.sourceCodeLocation,...t}}},tf="html",tm=["+//silmaril//dtd html pro v0r11 19970101//","-//as//dtd html 3.0 aswedit + extensions//","-//advasoft ltd//dtd html 3.0 aswedit + extensions//","-//ietf//dtd html 2.0 level 1//","-//ietf//dtd html 2.0 level 2//","-//ietf//dtd html 2.0 strict level 1//","-//ietf//dtd html 2.0 strict level 2//","-//ietf//dtd html 2.0 strict//","-//ietf//dtd html 2.0//","-//ietf//dtd html 2.1e//","-//ietf//dtd html 3.0//","-//ietf//dtd html 3.2 final//","-//ietf//dtd html 3.2//","-//ietf//dtd html 3//","-//ietf//dtd html level 0//","-//ietf//dtd html level 1//","-//ietf//dtd html level 2//","-//ietf//dtd html level 3//","-//ietf//dtd html strict level 0//","-//ietf//dtd html strict level 1//","-//ietf//dtd html strict level 2//","-//ietf//dtd html strict level 3//","-//ietf//dtd html strict//","-//ietf//dtd html//","-//metrius//dtd metrius presentational//","-//microsoft//dtd internet explorer 2.0 html strict//","-//microsoft//dtd internet explorer 2.0 html//","-//microsoft//dtd internet explorer 2.0 tables//","-//microsoft//dtd internet explorer 3.0 html strict//","-//microsoft//dtd internet explorer 3.0 html//","-//microsoft//dtd internet explorer 3.0 tables//","-//netscape comm. corp.//dtd html//","-//netscape comm. corp.//dtd strict html//","-//o'reilly and associates//dtd html 2.0//","-//o'reilly and associates//dtd html extended 1.0//","-//o'reilly and associates//dtd html extended relaxed 1.0//","-//sq//dtd html 2.0 hotmetal + extensions//","-//softquad software//dtd hotmetal pro 6.0::19990601::extensions to html 4.0//","-//softquad//dtd hotmetal pro 4.0::19971010::extensions to html 4.0//","-//spyglass//dtd html 2.0 extended//","-//sun microsystems corp.//dtd hotjava html//","-//sun microsystems corp.//dtd hotjava strict html//","-//w3c//dtd html 3 1995-03-24//","-//w3c//dtd html 3.2 draft//","-//w3c//dtd html 3.2 final//","-//w3c//dtd html 3.2//","-//w3c//dtd html 3.2s draft//","-//w3c//dtd html 4.0 frameset//","-//w3c//dtd html 4.0 transitional//","-//w3c//dtd html experimental 19960712//","-//w3c//dtd html experimental 970421//","-//w3c//dtd w3 html//","-//w3o//dtd w3 html 3.0//","-//webtechs//dtd mozilla html 2.0//","-//webtechs//dtd mozilla html//"],tg=[...tm,"-//w3c//dtd html 4.01 frameset//","-//w3c//dtd html 4.01 transitional//"],tE=new Set(["-//w3o//dtd w3 html strict 3.0//en//","-/w3c/dtd html 4.0 transitional/en","html"]),tT=["-//w3c//dtd xhtml 1.0 frameset//","-//w3c//dtd xhtml 1.0 transitional//"],tA=[...tT,"-//w3c//dtd html 4.01 frameset//","-//w3c//dtd html 4.01 transitional//"];function t_(e,t){return t.some(t=>e.startsWith(t))}let tS={TEXT_HTML:"text/html",APPLICATION_XML:"application/xhtml+xml"},ty=new Map(["attributeName","attributeType","baseFrequency","baseProfile","calcMode","clipPathUnits","diffuseConstant","edgeMode","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector","zoomAndPan"].map(e=>[e.toLowerCase(),e])),tk=new Map([["xlink:actuate",{prefix:"xlink",name:"actuate",namespace:N.XLINK}],["xlink:arcrole",{prefix:"xlink",name:"arcrole",namespace:N.XLINK}],["xlink:href",{prefix:"xlink",name:"href",namespace:N.XLINK}],["xlink:role",{prefix:"xlink",name:"role",namespace:N.XLINK}],["xlink:show",{prefix:"xlink",name:"show",namespace:N.XLINK}],["xlink:title",{prefix:"xlink",name:"title",namespace:N.XLINK}],["xlink:type",{prefix:"xlink",name:"type",namespace:N.XLINK}],["xml:lang",{prefix:"xml",name:"lang",namespace:N.XML}],["xml:space",{prefix:"xml",name:"space",namespace:N.XML}],["xmlns",{prefix:"",name:"xmlns",namespace:N.XMLNS}],["xmlns:xlink",{prefix:"xmlns",name:"xlink",namespace:N.XMLNS}]]),tC=new Map(["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","textPath"].map(e=>[e.toLowerCase(),e])),tN=new Set([D.B,D.BIG,D.BLOCKQUOTE,D.BODY,D.BR,D.CENTER,D.CODE,D.DD,D.DIV,D.DL,D.DT,D.EM,D.EMBED,D.H1,D.H2,D.H3,D.H4,D.H5,D.H6,D.HEAD,D.HR,D.I,D.IMG,D.LI,D.LISTING,D.MENU,D.META,D.NOBR,D.OL,D.P,D.PRE,D.RUBY,D.S,D.SMALL,D.SPAN,D.STRONG,D.STRIKE,D.SUB,D.SUP,D.TABLE,D.TT,D.U,D.UL,D.VAR]);function tI(e){for(let t=0;t<e.attrs.length;t++)if("definitionurl"===e.attrs[t].name){e.attrs[t].name="definitionURL";break}}function tb(e){for(let t=0;t<e.attrs.length;t++){let n=ty.get(e.attrs[t].name);null!=n&&(e.attrs[t].name=n)}}function tO(e){for(let t=0;t<e.attrs.length;t++){let n=tk.get(e.attrs[t].name);n&&(e.attrs[t].prefix=n.prefix,e.attrs[t].name=n.name,e.attrs[t].namespace=n.namespace)}}(E=v||(v={}))[E.INITIAL=0]="INITIAL",E[E.BEFORE_HTML=1]="BEFORE_HTML",E[E.BEFORE_HEAD=2]="BEFORE_HEAD",E[E.IN_HEAD=3]="IN_HEAD",E[E.IN_HEAD_NO_SCRIPT=4]="IN_HEAD_NO_SCRIPT",E[E.AFTER_HEAD=5]="AFTER_HEAD",E[E.IN_BODY=6]="IN_BODY",E[E.TEXT=7]="TEXT",E[E.IN_TABLE=8]="IN_TABLE",E[E.IN_TABLE_TEXT=9]="IN_TABLE_TEXT",E[E.IN_CAPTION=10]="IN_CAPTION",E[E.IN_COLUMN_GROUP=11]="IN_COLUMN_GROUP",E[E.IN_TABLE_BODY=12]="IN_TABLE_BODY",E[E.IN_ROW=13]="IN_ROW",E[E.IN_CELL=14]="IN_CELL",E[E.IN_SELECT=15]="IN_SELECT",E[E.IN_SELECT_IN_TABLE=16]="IN_SELECT_IN_TABLE",E[E.IN_TEMPLATE=17]="IN_TEMPLATE",E[E.AFTER_BODY=18]="AFTER_BODY",E[E.IN_FRAMESET=19]="IN_FRAMESET",E[E.AFTER_FRAMESET=20]="AFTER_FRAMESET",E[E.AFTER_AFTER_BODY=21]="AFTER_AFTER_BODY",E[E.AFTER_AFTER_FRAMESET=22]="AFTER_AFTER_FRAMESET";let tD={startLine:-1,startCol:-1,startOffset:-1,endLine:-1,endCol:-1,endOffset:-1},tR=new Set([D.TABLE,D.TBODY,D.TFOOT,D.THEAD,D.TR]),tM={scriptingEnabled:!0,sourceCodeLocationInfo:!1,treeAdapter:tp,onParseError:null};class tv{constructor(e,t,n=null,r=null){this.fragmentContext=n,this.scriptHandler=r,this.currentToken=null,this.stopped=!1,this.insertionMode=v.INITIAL,this.originalInsertionMode=v.INITIAL,this.headElement=null,this.formElement=null,this.currentNotInHTML=!1,this.tmplInsertionModeStack=[],this.pendingCharacterTokens=[],this.hasNonWhitespacePendingCharacterToken=!1,this.framesetOk=!0,this.skipNextNewLine=!1,this.fosterParentingEnabled=!1,this.options={...tM,...e},this.treeAdapter=this.options.treeAdapter,this.onParseError=this.options.onParseError,this.onParseError&&(this.options.sourceCodeLocationInfo=!0),this.document=null!=t?t:this.treeAdapter.createDocument(),this.tokenizer=new e9(this.options,this),this.activeFormattingElements=new td(this.treeAdapter),this.fragmentContextID=n?eJ(this.treeAdapter.getTagName(n)):D.UNKNOWN,this._setContextModes(null!=n?n:this.document,this.fragmentContextID),this.openElements=new tu(this.document,this.treeAdapter,this)}static parse(e,t){let n=new this(t);return n.tokenizer.write(e,!0),n.document}static getFragmentParser(e,t){let n={...tM,...t};null!=e||(e=n.treeAdapter.createElement(O.TEMPLATE,N.HTML,[]));let r=n.treeAdapter.createElement("documentmock",N.HTML,[]),s=new this(n,r,e);return s.fragmentContextID===D.TEMPLATE&&s.tmplInsertionModeStack.unshift(v.IN_TEMPLATE),s._initTokenizerForFragmentParsing(),s._insertFakeRootElement(),s._resetInsertionMode(),s._findFormInFragmentContext(),s}getFragment(){let e=this.treeAdapter.getFirstChild(this.document),t=this.treeAdapter.createDocumentFragment();return this._adoptNodes(e,t),t}_err(e,t,n){var r;if(!this.onParseError)return;let s=null!==(r=e.location)&&void 0!==r?r:tD,i={code:t,startLine:s.startLine,startCol:s.startCol,startOffset:s.startOffset,endLine:n?s.startLine:s.endLine,endCol:n?s.startCol:s.endCol,endOffset:n?s.startOffset:s.endOffset};this.onParseError(i)}onItemPush(e,t,n){var r,s;null===(s=(r=this.treeAdapter).onItemPush)||void 0===s||s.call(r,e),n&&this.openElements.stackTop>0&&this._setContextModes(e,t)}onItemPop(e,t){var n,r;if(this.options.sourceCodeLocationInfo&&this._setEndLocation(e,this.currentToken),null===(r=(n=this.treeAdapter).onItemPop)||void 0===r||r.call(n,e,this.openElements.current),t){let e,t;0===this.openElements.stackTop&&this.fragmentContext?(e=this.fragmentContext,t=this.fragmentContextID):{current:e,currentTagId:t}=this.openElements,this._setContextModes(e,t)}}_setContextModes(e,t){let n=e===this.document||e&&this.treeAdapter.getNamespaceURI(e)===N.HTML;this.currentNotInHTML=!n,this.tokenizer.inForeignNode=!n&&void 0!==e&&void 0!==t&&!this._isIntegrationPoint(t,e)}_switchToTextParsing(e,t){this._insertElement(e,N.HTML),this.tokenizer.state=t,this.originalInsertionMode=this.insertionMode,this.insertionMode=v.TEXT}switchToPlaintextParsing(){this.insertionMode=v.TEXT,this.originalInsertionMode=v.IN_BODY,this.tokenizer.state=e2.PLAINTEXT}_getAdjustedCurrentElement(){return 0===this.openElements.stackTop&&this.fragmentContext?this.fragmentContext:this.openElements.current}_findFormInFragmentContext(){let e=this.fragmentContext;for(;e;){if(this.treeAdapter.getTagName(e)===O.FORM){this.formElement=e;break}e=this.treeAdapter.getParentNode(e)}}_initTokenizerForFragmentParsing(){if(this.fragmentContext&&this.treeAdapter.getNamespaceURI(this.fragmentContext)===N.HTML)switch(this.fragmentContextID){case D.TITLE:case D.TEXTAREA:this.tokenizer.state=e2.RCDATA;break;case D.STYLE:case D.XMP:case D.IFRAME:case D.NOEMBED:case D.NOFRAMES:case D.NOSCRIPT:this.tokenizer.state=e2.RAWTEXT;break;case D.SCRIPT:this.tokenizer.state=e2.SCRIPT_DATA;break;case D.PLAINTEXT:this.tokenizer.state=e2.PLAINTEXT}}_setDocumentType(e){let t=e.name||"",n=e.publicId||"",r=e.systemId||"";if(this.treeAdapter.setDocumentType(this.document,t,n,r),e.location){let t=this.treeAdapter.getChildNodes(this.document).find(e=>this.treeAdapter.isDocumentTypeNode(e));t&&this.treeAdapter.setNodeSourceCodeLocation(t,e.location)}}_attachElementToTree(e,t){if(this.options.sourceCodeLocationInfo){let n=t&&{...t,startTag:t};this.treeAdapter.setNodeSourceCodeLocation(e,n)}if(this._shouldFosterParentOnInsertion())this._fosterParentElement(e);else{let t=this.openElements.currentTmplContentOrNode;this.treeAdapter.appendChild(null!=t?t:this.document,e)}}_appendElement(e,t){let n=this.treeAdapter.createElement(e.tagName,t,e.attrs);this._attachElementToTree(n,e.location)}_insertElement(e,t){let n=this.treeAdapter.createElement(e.tagName,t,e.attrs);this._attachElementToTree(n,e.location),this.openElements.push(n,e.tagID)}_insertFakeElement(e,t){let n=this.treeAdapter.createElement(e,N.HTML,[]);this._attachElementToTree(n,null),this.openElements.push(n,t)}_insertTemplate(e){let t=this.treeAdapter.createElement(e.tagName,N.HTML,e.attrs),n=this.treeAdapter.createDocumentFragment();this.treeAdapter.setTemplateContent(t,n),this._attachElementToTree(t,e.location),this.openElements.push(t,e.tagID),this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(n,null)}_insertFakeRootElement(){let e=this.treeAdapter.createElement(O.HTML,N.HTML,[]);this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(e,null),this.treeAdapter.appendChild(this.openElements.current,e),this.openElements.push(e,D.HTML)}_appendCommentNode(e,t){let n=this.treeAdapter.createCommentNode(e.data);this.treeAdapter.appendChild(t,n),this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(n,e.location)}_insertCharacters(e){let t,n;if(this._shouldFosterParentOnInsertion()?({parent:t,beforeElement:n}=this._findFosterParentingLocation(),n?this.treeAdapter.insertTextBefore(t,e.chars,n):this.treeAdapter.insertText(t,e.chars)):(t=this.openElements.currentTmplContentOrNode,this.treeAdapter.insertText(t,e.chars)),!e.location)return;let r=this.treeAdapter.getChildNodes(t),s=n?r.lastIndexOf(n):r.length,i=r[s-1];if(this.treeAdapter.getNodeSourceCodeLocation(i)){let{endLine:t,endCol:n,endOffset:r}=e.location;this.treeAdapter.updateNodeSourceCodeLocation(i,{endLine:t,endCol:n,endOffset:r})}else this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(i,e.location)}_adoptNodes(e,t){for(let n=this.treeAdapter.getFirstChild(e);n;n=this.treeAdapter.getFirstChild(e))this.treeAdapter.detachNode(n),this.treeAdapter.appendChild(t,n)}_setEndLocation(e,t){if(this.treeAdapter.getNodeSourceCodeLocation(e)&&t.location){let n=t.location,r=this.treeAdapter.getTagName(e),s=t.type===_.END_TAG&&r===t.tagName?{endTag:{...n},endLine:n.endLine,endCol:n.endCol,endOffset:n.endOffset}:{endLine:n.startLine,endCol:n.startCol,endOffset:n.startOffset};this.treeAdapter.updateNodeSourceCodeLocation(e,s)}}shouldProcessStartTagTokenInForeignContent(e){let t,n;return!!this.currentNotInHTML&&(0===this.openElements.stackTop&&this.fragmentContext?(t=this.fragmentContext,n=this.fragmentContextID):{current:t,currentTagId:n}=this.openElements,(e.tagID!==D.SVG||this.treeAdapter.getTagName(t)!==O.ANNOTATION_XML||this.treeAdapter.getNamespaceURI(t)!==N.MATHML)&&(this.tokenizer.inForeignNode||(e.tagID===D.MGLYPH||e.tagID===D.MALIGNMARK)&&void 0!==n&&!this._isIntegrationPoint(n,t,N.HTML)))}_processToken(e){switch(e.type){case _.CHARACTER:this.onCharacter(e);break;case _.NULL_CHARACTER:this.onNullCharacter(e);break;case _.COMMENT:this.onComment(e);break;case _.DOCTYPE:this.onDoctype(e);break;case _.START_TAG:this._processStartTag(e);break;case _.END_TAG:this.onEndTag(e);break;case _.EOF:this.onEof(e);break;case _.WHITESPACE_CHARACTER:this.onWhitespaceCharacter(e)}}_isIntegrationPoint(e,t,n){let r=this.treeAdapter.getNamespaceURI(t),s=this.treeAdapter.getAttrList(t);return(!n||n===N.HTML)&&function(e,t,n){if(t===N.MATHML&&e===D.ANNOTATION_XML){for(let e=0;e<n.length;e++)if(n[e].name===I.ENCODING){let t=n[e].value.toLowerCase();return t===tS.TEXT_HTML||t===tS.APPLICATION_XML}}return t===N.SVG&&(e===D.FOREIGN_OBJECT||e===D.DESC||e===D.TITLE)}(e,r,s)||(!n||n===N.MATHML)&&r===N.MATHML&&(e===D.MI||e===D.MO||e===D.MN||e===D.MS||e===D.MTEXT)}_reconstructActiveFormattingElements(){let e=this.activeFormattingElements.entries.length;if(e){let t=this.activeFormattingElements.entries.findIndex(e=>e.type===M.Marker||this.openElements.contains(e.element)),n=-1===t?e-1:t-1;for(let e=n;e>=0;e--){let t=this.activeFormattingElements.entries[e];this._insertElement(t.token,this.treeAdapter.getNamespaceURI(t.element)),t.element=this.openElements.current}}}_closeTableCell(){this.openElements.generateImpliedEndTags(),this.openElements.popUntilTableCellPopped(),this.activeFormattingElements.clearToLastMarker(),this.insertionMode=v.IN_ROW}_closePElement(){this.openElements.generateImpliedEndTagsWithExclusion(D.P),this.openElements.popUntilTagNamePopped(D.P)}_resetInsertionMode(){for(let e=this.openElements.stackTop;e>=0;e--)switch(0===e&&this.fragmentContext?this.fragmentContextID:this.openElements.tagIDs[e]){case D.TR:this.insertionMode=v.IN_ROW;return;case D.TBODY:case D.THEAD:case D.TFOOT:this.insertionMode=v.IN_TABLE_BODY;return;case D.CAPTION:this.insertionMode=v.IN_CAPTION;return;case D.COLGROUP:this.insertionMode=v.IN_COLUMN_GROUP;return;case D.TABLE:this.insertionMode=v.IN_TABLE;return;case D.BODY:this.insertionMode=v.IN_BODY;return;case D.FRAMESET:this.insertionMode=v.IN_FRAMESET;return;case D.SELECT:this._resetInsertionModeForSelect(e);return;case D.TEMPLATE:this.insertionMode=this.tmplInsertionModeStack[0];return;case D.HTML:this.insertionMode=this.headElement?v.AFTER_HEAD:v.BEFORE_HEAD;return;case D.TD:case D.TH:if(e>0){this.insertionMode=v.IN_CELL;return}break;case D.HEAD:if(e>0){this.insertionMode=v.IN_HEAD;return}}this.insertionMode=v.IN_BODY}_resetInsertionModeForSelect(e){if(e>0)for(let t=e-1;t>0;t--){let e=this.openElements.tagIDs[t];if(e===D.TEMPLATE)break;if(e===D.TABLE){this.insertionMode=v.IN_SELECT_IN_TABLE;return}}this.insertionMode=v.IN_SELECT}_isElementCausesFosterParenting(e){return tR.has(e)}_shouldFosterParentOnInsertion(){return this.fosterParentingEnabled&&void 0!==this.openElements.currentTagId&&this._isElementCausesFosterParenting(this.openElements.currentTagId)}_findFosterParentingLocation(){for(let e=this.openElements.stackTop;e>=0;e--){let t=this.openElements.items[e];switch(this.openElements.tagIDs[e]){case D.TEMPLATE:if(this.treeAdapter.getNamespaceURI(t)===N.HTML)return{parent:this.treeAdapter.getTemplateContent(t),beforeElement:null};break;case D.TABLE:{let n=this.treeAdapter.getParentNode(t);if(n)return{parent:n,beforeElement:t};return{parent:this.openElements.items[e-1],beforeElement:null}}}}return{parent:this.openElements.items[0],beforeElement:null}}_fosterParentElement(e){let t=this._findFosterParentingLocation();t.beforeElement?this.treeAdapter.insertBefore(t.parent,e,t.beforeElement):this.treeAdapter.appendChild(t.parent,e)}_isSpecialElement(e,t){return e0[this.treeAdapter.getNamespaceURI(e)].has(t)}onCharacter(e){if(this.skipNextNewLine=!1,this.tokenizer.inForeignNode){this._insertCharacters(e),this.framesetOk=!1;return}switch(this.insertionMode){case v.INITIAL:tx(this,e);break;case v.BEFORE_HTML:tF(this,e);break;case v.BEFORE_HEAD:tB(this,e);break;case v.IN_HEAD:tG(this,e);break;case v.IN_HEAD_NO_SCRIPT:tY(this,e);break;case v.AFTER_HEAD:tz(this,e);break;case v.IN_BODY:case v.IN_CAPTION:case v.IN_CELL:case v.IN_TEMPLATE:t$(this,e);break;case v.TEXT:case v.IN_SELECT:case v.IN_SELECT_IN_TABLE:this._insertCharacters(e);break;case v.IN_TABLE:case v.IN_TABLE_BODY:case v.IN_ROW:t1(this,e);break;case v.IN_TABLE_TEXT:t8(this,e);break;case v.IN_COLUMN_GROUP:ne(this,e);break;case v.AFTER_BODY:nc(this,e);break;case v.AFTER_AFTER_BODY:nu(this,e)}}onNullCharacter(e){if(this.skipNextNewLine=!1,this.tokenizer.inForeignNode){e.chars="�",this._insertCharacters(e);return}switch(this.insertionMode){case v.INITIAL:tx(this,e);break;case v.BEFORE_HTML:tF(this,e);break;case v.BEFORE_HEAD:tB(this,e);break;case v.IN_HEAD:tG(this,e);break;case v.IN_HEAD_NO_SCRIPT:tY(this,e);break;case v.AFTER_HEAD:tz(this,e);break;case v.TEXT:this._insertCharacters(e);break;case v.IN_TABLE:case v.IN_TABLE_BODY:case v.IN_ROW:t1(this,e);break;case v.IN_COLUMN_GROUP:ne(this,e);break;case v.AFTER_BODY:nc(this,e);break;case v.AFTER_AFTER_BODY:nu(this,e)}}onComment(e){if(this.skipNextNewLine=!1,this.currentNotInHTML){tw(this,e);return}switch(this.insertionMode){case v.INITIAL:case v.BEFORE_HTML:case v.BEFORE_HEAD:case v.IN_HEAD:case v.IN_HEAD_NO_SCRIPT:case v.AFTER_HEAD:case v.IN_BODY:case v.IN_TABLE:case v.IN_CAPTION:case v.IN_COLUMN_GROUP:case v.IN_TABLE_BODY:case v.IN_ROW:case v.IN_CELL:case v.IN_SELECT:case v.IN_SELECT_IN_TABLE:case v.IN_TEMPLATE:case v.IN_FRAMESET:case v.AFTER_FRAMESET:tw(this,e);break;case v.IN_TABLE_TEXT:t6(this,e);break;case v.AFTER_BODY:!function(e,t){e._appendCommentNode(t,e.openElements.items[0])}(this,e);break;case v.AFTER_AFTER_BODY:case v.AFTER_AFTER_FRAMESET:!function(e,t){e._appendCommentNode(t,e.document)}(this,e)}}onDoctype(e){switch(this.skipNextNewLine=!1,this.insertionMode){case v.INITIAL:!function(e,t){e._setDocumentType(t);let n=t.forceQuirks?b.QUIRKS:function(e){if(e.name!==tf)return b.QUIRKS;let{systemId:t}=e;if(t&&"http://www.ibm.com/data/dtd/v11/ibmxhtml1-transitional.dtd"===t.toLowerCase())return b.QUIRKS;let{publicId:n}=e;if(null!==n){if(n=n.toLowerCase(),tE.has(n))return b.QUIRKS;let e=null===t?tg:tm;if(t_(n,e))return b.QUIRKS;if(t_(n,e=null===t?tT:tA))return b.LIMITED_QUIRKS}return b.NO_QUIRKS}(t);t.name===tf&&null===t.publicId&&(null===t.systemId||"about:legacy-compat"===t.systemId)||e._err(t,A.nonConformingDoctype),e.treeAdapter.setDocumentMode(e.document,n),e.insertionMode=v.BEFORE_HTML}(this,e);break;case v.BEFORE_HEAD:case v.IN_HEAD:case v.IN_HEAD_NO_SCRIPT:case v.AFTER_HEAD:this._err(e,A.misplacedDoctype);break;case v.IN_TABLE_TEXT:t6(this,e)}}onStartTag(e){this.skipNextNewLine=!1,this.currentToken=e,this._processStartTag(e),e.selfClosing&&!e.ackSelfClosing&&this._err(e,A.nonVoidHtmlElementStartTagWithTrailingSolidus)}_processStartTag(e){this.shouldProcessStartTagTokenInForeignContent(e)?function(e,t){if(function(e){let t=e.tagID;return t===D.FONT&&e.attrs.some(({name:e})=>e===I.COLOR||e===I.SIZE||e===I.FACE)||tN.has(t)}(t))nh(e),e._startTagOutsideForeignContent(t);else{let n=e._getAdjustedCurrentElement(),r=e.treeAdapter.getNamespaceURI(n);r===N.MATHML?tI(t):r===N.SVG&&(function(e){let t=tC.get(e.tagName);null!=t&&(e.tagName=t,e.tagID=eJ(e.tagName))}(t),tb(t)),tO(t),t.selfClosing?e._appendElement(t,r):e._insertElement(t,r),t.ackSelfClosing=!0}}(this,e):this._startTagOutsideForeignContent(e)}_startTagOutsideForeignContent(e){switch(this.insertionMode){case v.INITIAL:tx(this,e);break;case v.BEFORE_HTML:e.tagID===D.HTML?(this._insertElement(e,N.HTML),this.insertionMode=v.BEFORE_HEAD):tF(this,e);break;case v.BEFORE_HEAD:!function(e,t){switch(t.tagID){case D.HTML:tX(e,t);break;case D.HEAD:e._insertElement(t,N.HTML),e.headElement=e.openElements.current,e.insertionMode=v.IN_HEAD;break;default:tB(e,t)}}(this,e);break;case v.IN_HEAD:tH(this,e);break;case v.IN_HEAD_NO_SCRIPT:!function(e,t){switch(t.tagID){case D.HTML:tX(e,t);break;case D.BASEFONT:case D.BGSOUND:case D.HEAD:case D.LINK:case D.META:case D.NOFRAMES:case D.STYLE:tH(e,t);break;case D.NOSCRIPT:e._err(t,A.nestedNoscriptInHead);break;default:tY(e,t)}}(this,e);break;case v.AFTER_HEAD:!function(e,t){switch(t.tagID){case D.HTML:tX(e,t);break;case D.BODY:e._insertElement(t,N.HTML),e.framesetOk=!1,e.insertionMode=v.IN_BODY;break;case D.FRAMESET:e._insertElement(t,N.HTML),e.insertionMode=v.IN_FRAMESET;break;case D.BASE:case D.BASEFONT:case D.BGSOUND:case D.LINK:case D.META:case D.NOFRAMES:case D.SCRIPT:case D.STYLE:case D.TEMPLATE:case D.TITLE:e._err(t,A.abandonedHeadElementChild),e.openElements.push(e.headElement,D.HEAD),tH(e,t),e.openElements.remove(e.headElement);break;case D.HEAD:e._err(t,A.misplacedStartTagForHeadElement);break;default:tz(e,t)}}(this,e);break;case v.IN_BODY:tX(this,e);break;case v.IN_TABLE:t2(this,e);break;case v.IN_TABLE_TEXT:t6(this,e);break;case v.IN_CAPTION:!function(e,t){let n=t.tagID;t9.has(n)?e.openElements.hasInTableScope(D.CAPTION)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(D.CAPTION),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=v.IN_TABLE,t2(e,t)):tX(e,t)}(this,e);break;case v.IN_COLUMN_GROUP:t7(this,e);break;case v.IN_TABLE_BODY:nt(this,e);break;case v.IN_ROW:nr(this,e);break;case v.IN_CELL:!function(e,t){let n=t.tagID;t9.has(n)?(e.openElements.hasInTableScope(D.TD)||e.openElements.hasInTableScope(D.TH))&&(e._closeTableCell(),nr(e,t)):tX(e,t)}(this,e);break;case v.IN_SELECT:ni(this,e);break;case v.IN_SELECT_IN_TABLE:!function(e,t){let n=t.tagID;n===D.CAPTION||n===D.TABLE||n===D.TBODY||n===D.TFOOT||n===D.THEAD||n===D.TR||n===D.TD||n===D.TH?(e.openElements.popUntilTagNamePopped(D.SELECT),e._resetInsertionMode(),e._processStartTag(t)):ni(e,t)}(this,e);break;case v.IN_TEMPLATE:!function(e,t){switch(t.tagID){case D.BASE:case D.BASEFONT:case D.BGSOUND:case D.LINK:case D.META:case D.NOFRAMES:case D.SCRIPT:case D.STYLE:case D.TEMPLATE:case D.TITLE:tH(e,t);break;case D.CAPTION:case D.COLGROUP:case D.TBODY:case D.TFOOT:case D.THEAD:e.tmplInsertionModeStack[0]=v.IN_TABLE,e.insertionMode=v.IN_TABLE,t2(e,t);break;case D.COL:e.tmplInsertionModeStack[0]=v.IN_COLUMN_GROUP,e.insertionMode=v.IN_COLUMN_GROUP,t7(e,t);break;case D.TR:e.tmplInsertionModeStack[0]=v.IN_TABLE_BODY,e.insertionMode=v.IN_TABLE_BODY,nt(e,t);break;case D.TD:case D.TH:e.tmplInsertionModeStack[0]=v.IN_ROW,e.insertionMode=v.IN_ROW,nr(e,t);break;default:e.tmplInsertionModeStack[0]=v.IN_BODY,e.insertionMode=v.IN_BODY,tX(e,t)}}(this,e);break;case v.AFTER_BODY:e.tagID===D.HTML?tX(this,e):nc(this,e);break;case v.IN_FRAMESET:!function(e,t){switch(t.tagID){case D.HTML:tX(e,t);break;case D.FRAMESET:e._insertElement(t,N.HTML);break;case D.FRAME:e._appendElement(t,N.HTML),t.ackSelfClosing=!0;break;case D.NOFRAMES:tH(e,t)}}(this,e);break;case v.AFTER_FRAMESET:!function(e,t){switch(t.tagID){case D.HTML:tX(e,t);break;case D.NOFRAMES:tH(e,t)}}(this,e);break;case v.AFTER_AFTER_BODY:e.tagID===D.HTML?tX(this,e):nu(this,e);break;case v.AFTER_AFTER_FRAMESET:!function(e,t){switch(t.tagID){case D.HTML:tX(e,t);break;case D.NOFRAMES:tH(e,t)}}(this,e)}}onEndTag(e){this.skipNextNewLine=!1,this.currentToken=e,this.currentNotInHTML?function(e,t){if(t.tagID===D.P||t.tagID===D.BR){nh(e),e._endTagOutsideForeignContent(t);return}for(let n=e.openElements.stackTop;n>0;n--){let r=e.openElements.items[n];if(e.treeAdapter.getNamespaceURI(r)===N.HTML){e._endTagOutsideForeignContent(t);break}let s=e.treeAdapter.getTagName(r);if(s.toLowerCase()===t.tagName){t.tagName=s,e.openElements.shortenToLength(n);break}}}(this,e):this._endTagOutsideForeignContent(e)}_endTagOutsideForeignContent(e){var t;switch(this.insertionMode){case v.INITIAL:tx(this,e);break;case v.BEFORE_HTML:!function(e,t){let n=t.tagID;(n===D.HTML||n===D.HEAD||n===D.BODY||n===D.BR)&&tF(e,t)}(this,e);break;case v.BEFORE_HEAD:!function(e,t){let n=t.tagID;n===D.HEAD||n===D.BODY||n===D.HTML||n===D.BR?tB(e,t):e._err(t,A.endTagWithoutMatchingOpenElement)}(this,e);break;case v.IN_HEAD:!function(e,t){switch(t.tagID){case D.HEAD:e.openElements.pop(),e.insertionMode=v.AFTER_HEAD;break;case D.BODY:case D.BR:case D.HTML:tG(e,t);break;case D.TEMPLATE:tU(e,t);break;default:e._err(t,A.endTagWithoutMatchingOpenElement)}}(this,e);break;case v.IN_HEAD_NO_SCRIPT:!function(e,t){switch(t.tagID){case D.NOSCRIPT:e.openElements.pop(),e.insertionMode=v.IN_HEAD;break;case D.BR:tY(e,t);break;default:e._err(t,A.endTagWithoutMatchingOpenElement)}}(this,e);break;case v.AFTER_HEAD:!function(e,t){switch(t.tagID){case D.BODY:case D.HTML:case D.BR:tz(e,t);break;case D.TEMPLATE:tU(e,t);break;default:e._err(t,A.endTagWithoutMatchingOpenElement)}}(this,e);break;case v.IN_BODY:tZ(this,e);break;case v.TEXT:e.tagID===D.SCRIPT&&(null===(t=this.scriptHandler)||void 0===t||t.call(this,this.openElements.current)),this.openElements.pop(),this.insertionMode=this.originalInsertionMode;break;case v.IN_TABLE:t3(this,e);break;case v.IN_TABLE_TEXT:t6(this,e);break;case v.IN_CAPTION:!function(e,t){let n=t.tagID;switch(n){case D.CAPTION:case D.TABLE:e.openElements.hasInTableScope(D.CAPTION)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(D.CAPTION),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=v.IN_TABLE,n===D.TABLE&&t3(e,t));break;case D.BODY:case D.COL:case D.COLGROUP:case D.HTML:case D.TBODY:case D.TD:case D.TFOOT:case D.TH:case D.THEAD:case D.TR:break;default:tZ(e,t)}}(this,e);break;case v.IN_COLUMN_GROUP:!function(e,t){switch(t.tagID){case D.COLGROUP:e.openElements.currentTagId===D.COLGROUP&&(e.openElements.pop(),e.insertionMode=v.IN_TABLE);break;case D.TEMPLATE:tU(e,t);break;case D.COL:break;default:ne(e,t)}}(this,e);break;case v.IN_TABLE_BODY:nn(this,e);break;case v.IN_ROW:ns(this,e);break;case v.IN_CELL:!function(e,t){let n=t.tagID;switch(n){case D.TD:case D.TH:e.openElements.hasInTableScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=v.IN_ROW);break;case D.TABLE:case D.TBODY:case D.TFOOT:case D.THEAD:case D.TR:e.openElements.hasInTableScope(n)&&(e._closeTableCell(),ns(e,t));break;case D.BODY:case D.CAPTION:case D.COL:case D.COLGROUP:case D.HTML:break;default:tZ(e,t)}}(this,e);break;case v.IN_SELECT:na(this,e);break;case v.IN_SELECT_IN_TABLE:!function(e,t){let n=t.tagID;n===D.CAPTION||n===D.TABLE||n===D.TBODY||n===D.TFOOT||n===D.THEAD||n===D.TR||n===D.TD||n===D.TH?e.openElements.hasInTableScope(n)&&(e.openElements.popUntilTagNamePopped(D.SELECT),e._resetInsertionMode(),e.onEndTag(t)):na(e,t)}(this,e);break;case v.IN_TEMPLATE:e.tagID===D.TEMPLATE&&tU(this,e);break;case v.AFTER_BODY:nl(this,e);break;case v.IN_FRAMESET:e.tagID!==D.FRAMESET||this.openElements.isRootHtmlElementCurrent()||(this.openElements.pop(),this.fragmentContext||this.openElements.currentTagId===D.FRAMESET||(this.insertionMode=v.AFTER_FRAMESET));break;case v.AFTER_FRAMESET:e.tagID===D.HTML&&(this.insertionMode=v.AFTER_AFTER_FRAMESET);break;case v.AFTER_AFTER_BODY:nu(this,e)}}onEof(e){switch(this.insertionMode){case v.INITIAL:tx(this,e);break;case v.BEFORE_HTML:tF(this,e);break;case v.BEFORE_HEAD:tB(this,e);break;case v.IN_HEAD:tG(this,e);break;case v.IN_HEAD_NO_SCRIPT:tY(this,e);break;case v.AFTER_HEAD:tz(this,e);break;case v.IN_BODY:case v.IN_TABLE:case v.IN_CAPTION:case v.IN_COLUMN_GROUP:case v.IN_TABLE_BODY:case v.IN_ROW:case v.IN_CELL:case v.IN_SELECT:case v.IN_SELECT_IN_TABLE:t0(this,e);break;case v.TEXT:this._err(e,A.eofInElementThatCanContainOnlyText),this.openElements.pop(),this.insertionMode=this.originalInsertionMode,this.onEof(e);break;case v.IN_TABLE_TEXT:t6(this,e);break;case v.IN_TEMPLATE:no(this,e);break;case v.AFTER_BODY:case v.IN_FRAMESET:case v.AFTER_FRAMESET:case v.AFTER_AFTER_BODY:case v.AFTER_AFTER_FRAMESET:tP(this,e)}}onWhitespaceCharacter(e){if(this.skipNextNewLine&&(this.skipNextNewLine=!1,e.chars.charCodeAt(0)===T.LINE_FEED)){if(1===e.chars.length)return;e.chars=e.chars.substr(1)}if(this.tokenizer.inForeignNode){this._insertCharacters(e);return}switch(this.insertionMode){case v.IN_HEAD:case v.IN_HEAD_NO_SCRIPT:case v.AFTER_HEAD:case v.TEXT:case v.IN_COLUMN_GROUP:case v.IN_SELECT:case v.IN_SELECT_IN_TABLE:case v.IN_FRAMESET:case v.AFTER_FRAMESET:this._insertCharacters(e);break;case v.IN_BODY:case v.IN_CAPTION:case v.IN_CELL:case v.IN_TEMPLATE:case v.AFTER_BODY:case v.AFTER_AFTER_BODY:case v.AFTER_AFTER_FRAMESET:tq(this,e);break;case v.IN_TABLE:case v.IN_TABLE_BODY:case v.IN_ROW:t1(this,e);break;case v.IN_TABLE_TEXT:t4(this,e)}}}function tL(e,t){for(let n=0;n<8;n++){let n=function(e,t){let n=e.activeFormattingElements.getElementEntryInScopeWithTagName(t.tagName);return n?e.openElements.contains(n.element)?e.openElements.hasInScope(t.tagID)||(n=null):(e.activeFormattingElements.removeEntry(n),n=null):tJ(e,t),n}(e,t);if(!n)break;let r=function(e,t){let n=null,r=e.openElements.stackTop;for(;r>=0;r--){let s=e.openElements.items[r];if(s===t.element)break;e._isSpecialElement(s,e.openElements.tagIDs[r])&&(n=s)}return n||(e.openElements.shortenToLength(Math.max(r,0)),e.activeFormattingElements.removeEntry(t)),n}(e,n);if(!r)break;e.activeFormattingElements.bookmark=n;let s=function(e,t,n){let r=t,s=e.openElements.getCommonAncestor(t);for(let i=0,a=s;a!==n;i++,a=s){s=e.openElements.getCommonAncestor(a);let n=e.activeFormattingElements.getElementEntry(a),o=n&&i>=3;!n||o?(o&&e.activeFormattingElements.removeEntry(n),e.openElements.remove(a)):(a=function(e,t){let n=e.treeAdapter.getNamespaceURI(t.element),r=e.treeAdapter.createElement(t.token.tagName,n,t.token.attrs);return e.openElements.replace(t.element,r),t.element=r,r}(e,n),r===t&&(e.activeFormattingElements.bookmark=n),e.treeAdapter.detachNode(r),e.treeAdapter.appendChild(a,r),r=a)}return r}(e,r,n.element),i=e.openElements.getCommonAncestor(n.element);e.treeAdapter.detachNode(s),i&&function(e,t,n){let r=eJ(e.treeAdapter.getTagName(t));if(e._isElementCausesFosterParenting(r))e._fosterParentElement(n);else{let s=e.treeAdapter.getNamespaceURI(t);r===D.TEMPLATE&&s===N.HTML&&(t=e.treeAdapter.getTemplateContent(t)),e.treeAdapter.appendChild(t,n)}}(e,i,s),function(e,t,n){let r=e.treeAdapter.getNamespaceURI(n.element),{token:s}=n,i=e.treeAdapter.createElement(s.tagName,r,s.attrs);e._adoptNodes(t,i),e.treeAdapter.appendChild(t,i),e.activeFormattingElements.insertElementAfterBookmark(i,s),e.activeFormattingElements.removeEntry(n),e.openElements.remove(n.element),e.openElements.insertAfter(t,i,s.tagID)}(e,r,n)}}function tw(e,t){e._appendCommentNode(t,e.openElements.currentTmplContentOrNode)}function tP(e,t){if(e.stopped=!0,t.location){let n=e.fragmentContext?0:2;for(let r=e.openElements.stackTop;r>=n;r--)e._setEndLocation(e.openElements.items[r],t);if(!e.fragmentContext&&e.openElements.stackTop>=0){let n=e.openElements.items[0],r=e.treeAdapter.getNodeSourceCodeLocation(n);if(r&&!r.endTag&&(e._setEndLocation(n,t),e.openElements.stackTop>=1)){let n=e.openElements.items[1],r=e.treeAdapter.getNodeSourceCodeLocation(n);r&&!r.endTag&&e._setEndLocation(n,t)}}}}function tx(e,t){e._err(t,A.missingDoctype,!0),e.treeAdapter.setDocumentMode(e.document,b.QUIRKS),e.insertionMode=v.BEFORE_HTML,e._processToken(t)}function tF(e,t){e._insertFakeRootElement(),e.insertionMode=v.BEFORE_HEAD,e._processToken(t)}function tB(e,t){e._insertFakeElement(O.HEAD,D.HEAD),e.headElement=e.openElements.current,e.insertionMode=v.IN_HEAD,e._processToken(t)}function tH(e,t){switch(t.tagID){case D.HTML:tX(e,t);break;case D.BASE:case D.BASEFONT:case D.BGSOUND:case D.LINK:case D.META:e._appendElement(t,N.HTML),t.ackSelfClosing=!0;break;case D.TITLE:e._switchToTextParsing(t,e2.RCDATA);break;case D.NOSCRIPT:e.options.scriptingEnabled?e._switchToTextParsing(t,e2.RAWTEXT):(e._insertElement(t,N.HTML),e.insertionMode=v.IN_HEAD_NO_SCRIPT);break;case D.NOFRAMES:case D.STYLE:e._switchToTextParsing(t,e2.RAWTEXT);break;case D.SCRIPT:e._switchToTextParsing(t,e2.SCRIPT_DATA);break;case D.TEMPLATE:e._insertTemplate(t),e.activeFormattingElements.insertMarker(),e.framesetOk=!1,e.insertionMode=v.IN_TEMPLATE,e.tmplInsertionModeStack.unshift(v.IN_TEMPLATE);break;case D.HEAD:e._err(t,A.misplacedStartTagForHeadElement);break;default:tG(e,t)}}function tU(e,t){e.openElements.tmplCount>0?(e.openElements.generateImpliedEndTagsThoroughly(),e.openElements.currentTagId!==D.TEMPLATE&&e._err(t,A.closingOfElementWithOpenChildElements),e.openElements.popUntilTagNamePopped(D.TEMPLATE),e.activeFormattingElements.clearToLastMarker(),e.tmplInsertionModeStack.shift(),e._resetInsertionMode()):e._err(t,A.endTagWithoutMatchingOpenElement)}function tG(e,t){e.openElements.pop(),e.insertionMode=v.AFTER_HEAD,e._processToken(t)}function tY(e,t){let n=t.type===_.EOF?A.openElementsLeftAfterEof:A.disallowedContentInNoscriptInHead;e._err(t,n),e.openElements.pop(),e.insertionMode=v.IN_HEAD,e._processToken(t)}function tz(e,t){e._insertFakeElement(O.BODY,D.BODY),e.insertionMode=v.IN_BODY,tW(e,t)}function tW(e,t){switch(t.type){case _.CHARACTER:t$(e,t);break;case _.WHITESPACE_CHARACTER:tq(e,t);break;case _.COMMENT:tw(e,t);break;case _.START_TAG:tX(e,t);break;case _.END_TAG:tZ(e,t);break;case _.EOF:t0(e,t)}}function tq(e,t){e._reconstructActiveFormattingElements(),e._insertCharacters(t)}function t$(e,t){e._reconstructActiveFormattingElements(),e._insertCharacters(t),e.framesetOk=!1}function tj(e,t){e._reconstructActiveFormattingElements(),e._appendElement(t,N.HTML),e.framesetOk=!1,t.ackSelfClosing=!0}function tV(e){let t=e$(e,I.TYPE);return null!=t&&"hidden"===t.toLowerCase()}function tK(e,t){e._switchToTextParsing(t,e2.RAWTEXT)}function tQ(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,N.HTML)}function tX(e,t){switch(t.tagID){case D.I:case D.S:case D.B:case D.U:case D.EM:case D.TT:case D.BIG:case D.CODE:case D.FONT:case D.SMALL:case D.STRIKE:case D.STRONG:e._reconstructActiveFormattingElements(),e._insertElement(t,N.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t);break;case D.A:!function(e,t){let n=e.activeFormattingElements.getElementEntryInScopeWithTagName(O.A);n&&(tL(e,t),e.openElements.remove(n.element),e.activeFormattingElements.removeEntry(n)),e._reconstructActiveFormattingElements(),e._insertElement(t,N.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t)}(e,t);break;case D.H1:case D.H2:case D.H3:case D.H4:case D.H5:case D.H6:e.openElements.hasInButtonScope(D.P)&&e._closePElement(),void 0!==e.openElements.currentTagId&&e1.has(e.openElements.currentTagId)&&e.openElements.pop(),e._insertElement(t,N.HTML);break;case D.P:case D.DL:case D.OL:case D.UL:case D.DIV:case D.DIR:case D.NAV:case D.MAIN:case D.MENU:case D.ASIDE:case D.CENTER:case D.FIGURE:case D.FOOTER:case D.HEADER:case D.HGROUP:case D.DIALOG:case D.DETAILS:case D.ADDRESS:case D.ARTICLE:case D.SEARCH:case D.SECTION:case D.SUMMARY:case D.FIELDSET:case D.BLOCKQUOTE:case D.FIGCAPTION:e.openElements.hasInButtonScope(D.P)&&e._closePElement(),e._insertElement(t,N.HTML);break;case D.LI:case D.DD:case D.DT:!function(e,t){e.framesetOk=!1;let n=t.tagID;for(let t=e.openElements.stackTop;t>=0;t--){let r=e.openElements.tagIDs[t];if(n===D.LI&&r===D.LI||(n===D.DD||n===D.DT)&&(r===D.DD||r===D.DT)){e.openElements.generateImpliedEndTagsWithExclusion(r),e.openElements.popUntilTagNamePopped(r);break}if(r!==D.ADDRESS&&r!==D.DIV&&r!==D.P&&e._isSpecialElement(e.openElements.items[t],r))break}e.openElements.hasInButtonScope(D.P)&&e._closePElement(),e._insertElement(t,N.HTML)}(e,t);break;case D.BR:case D.IMG:case D.WBR:case D.AREA:case D.EMBED:case D.KEYGEN:tj(e,t);break;case D.HR:e.openElements.hasInButtonScope(D.P)&&e._closePElement(),e._appendElement(t,N.HTML),e.framesetOk=!1,t.ackSelfClosing=!0;break;case D.RB:case D.RTC:e.openElements.hasInScope(D.RUBY)&&e.openElements.generateImpliedEndTags(),e._insertElement(t,N.HTML);break;case D.RT:case D.RP:e.openElements.hasInScope(D.RUBY)&&e.openElements.generateImpliedEndTagsWithExclusion(D.RTC),e._insertElement(t,N.HTML);break;case D.PRE:case D.LISTING:e.openElements.hasInButtonScope(D.P)&&e._closePElement(),e._insertElement(t,N.HTML),e.skipNextNewLine=!0,e.framesetOk=!1;break;case D.XMP:e.openElements.hasInButtonScope(D.P)&&e._closePElement(),e._reconstructActiveFormattingElements(),e.framesetOk=!1,e._switchToTextParsing(t,e2.RAWTEXT);break;case D.SVG:e._reconstructActiveFormattingElements(),tb(t),tO(t),t.selfClosing?e._appendElement(t,N.SVG):e._insertElement(t,N.SVG),t.ackSelfClosing=!0;break;case D.HTML:0===e.openElements.tmplCount&&e.treeAdapter.adoptAttributes(e.openElements.items[0],t.attrs);break;case D.BASE:case D.LINK:case D.META:case D.STYLE:case D.TITLE:case D.SCRIPT:case D.BGSOUND:case D.BASEFONT:case D.TEMPLATE:tH(e,t);break;case D.BODY:!function(e,t){let n=e.openElements.tryPeekProperlyNestedBodyElement();n&&0===e.openElements.tmplCount&&(e.framesetOk=!1,e.treeAdapter.adoptAttributes(n,t.attrs))}(e,t);break;case D.FORM:!function(e,t){let n=e.openElements.tmplCount>0;e.formElement&&!n||(e.openElements.hasInButtonScope(D.P)&&e._closePElement(),e._insertElement(t,N.HTML),n||(e.formElement=e.openElements.current))}(e,t);break;case D.NOBR:e._reconstructActiveFormattingElements(),e.openElements.hasInScope(D.NOBR)&&(tL(e,t),e._reconstructActiveFormattingElements()),e._insertElement(t,N.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t);break;case D.MATH:e._reconstructActiveFormattingElements(),tI(t),tO(t),t.selfClosing?e._appendElement(t,N.MATHML):e._insertElement(t,N.MATHML),t.ackSelfClosing=!0;break;case D.TABLE:e.treeAdapter.getDocumentMode(e.document)!==b.QUIRKS&&e.openElements.hasInButtonScope(D.P)&&e._closePElement(),e._insertElement(t,N.HTML),e.framesetOk=!1,e.insertionMode=v.IN_TABLE;break;case D.INPUT:e._reconstructActiveFormattingElements(),e._appendElement(t,N.HTML),tV(t)||(e.framesetOk=!1),t.ackSelfClosing=!0;break;case D.PARAM:case D.TRACK:case D.SOURCE:e._appendElement(t,N.HTML),t.ackSelfClosing=!0;break;case D.IMAGE:t.tagName=O.IMG,t.tagID=D.IMG,tj(e,t);break;case D.BUTTON:e.openElements.hasInScope(D.BUTTON)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(D.BUTTON)),e._reconstructActiveFormattingElements(),e._insertElement(t,N.HTML),e.framesetOk=!1;break;case D.APPLET:case D.OBJECT:case D.MARQUEE:e._reconstructActiveFormattingElements(),e._insertElement(t,N.HTML),e.activeFormattingElements.insertMarker(),e.framesetOk=!1;break;case D.IFRAME:e.framesetOk=!1,e._switchToTextParsing(t,e2.RAWTEXT);break;case D.SELECT:e._reconstructActiveFormattingElements(),e._insertElement(t,N.HTML),e.framesetOk=!1,e.insertionMode=e.insertionMode===v.IN_TABLE||e.insertionMode===v.IN_CAPTION||e.insertionMode===v.IN_TABLE_BODY||e.insertionMode===v.IN_ROW||e.insertionMode===v.IN_CELL?v.IN_SELECT_IN_TABLE:v.IN_SELECT;break;case D.OPTION:case D.OPTGROUP:e.openElements.currentTagId===D.OPTION&&e.openElements.pop(),e._reconstructActiveFormattingElements(),e._insertElement(t,N.HTML);break;case D.NOEMBED:case D.NOFRAMES:tK(e,t);break;case D.FRAMESET:!function(e,t){let n=e.openElements.tryPeekProperlyNestedBodyElement();e.framesetOk&&n&&(e.treeAdapter.detachNode(n),e.openElements.popAllUpToHtmlElement(),e._insertElement(t,N.HTML),e.insertionMode=v.IN_FRAMESET)}(e,t);break;case D.TEXTAREA:e._insertElement(t,N.HTML),e.skipNextNewLine=!0,e.tokenizer.state=e2.RCDATA,e.originalInsertionMode=e.insertionMode,e.framesetOk=!1,e.insertionMode=v.TEXT;break;case D.NOSCRIPT:e.options.scriptingEnabled?tK(e,t):tQ(e,t);break;case D.PLAINTEXT:e.openElements.hasInButtonScope(D.P)&&e._closePElement(),e._insertElement(t,N.HTML),e.tokenizer.state=e2.PLAINTEXT;break;case D.COL:case D.TH:case D.TD:case D.TR:case D.HEAD:case D.FRAME:case D.TBODY:case D.TFOOT:case D.THEAD:case D.CAPTION:case D.COLGROUP:break;default:tQ(e,t)}}function tJ(e,t){let n=t.tagName,r=t.tagID;for(let t=e.openElements.stackTop;t>0;t--){let s=e.openElements.items[t],i=e.openElements.tagIDs[t];if(r===i&&(r!==D.UNKNOWN||e.treeAdapter.getTagName(s)===n)){e.openElements.generateImpliedEndTagsWithExclusion(r),e.openElements.stackTop>=t&&e.openElements.shortenToLength(t);break}if(e._isSpecialElement(s,i))break}}function tZ(e,t){switch(t.tagID){case D.A:case D.B:case D.I:case D.S:case D.U:case D.EM:case D.TT:case D.BIG:case D.CODE:case D.FONT:case D.NOBR:case D.SMALL:case D.STRIKE:case D.STRONG:tL(e,t);break;case D.P:e.openElements.hasInButtonScope(D.P)||e._insertFakeElement(O.P,D.P),e._closePElement();break;case D.DL:case D.UL:case D.OL:case D.DIR:case D.DIV:case D.NAV:case D.PRE:case D.MAIN:case D.MENU:case D.ASIDE:case D.BUTTON:case D.CENTER:case D.FIGURE:case D.FOOTER:case D.HEADER:case D.HGROUP:case D.DIALOG:case D.ADDRESS:case D.ARTICLE:case D.DETAILS:case D.SEARCH:case D.SECTION:case D.SUMMARY:case D.LISTING:case D.FIELDSET:case D.BLOCKQUOTE:case D.FIGCAPTION:!function(e,t){let n=t.tagID;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n))}(e,t);break;case D.LI:e.openElements.hasInListItemScope(D.LI)&&(e.openElements.generateImpliedEndTagsWithExclusion(D.LI),e.openElements.popUntilTagNamePopped(D.LI));break;case D.DD:case D.DT:!function(e,t){let n=t.tagID;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTagsWithExclusion(n),e.openElements.popUntilTagNamePopped(n))}(e,t);break;case D.H1:case D.H2:case D.H3:case D.H4:case D.H5:case D.H6:e.openElements.hasNumberedHeaderInScope()&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilNumberedHeaderPopped());break;case D.BR:e._reconstructActiveFormattingElements(),e._insertFakeElement(O.BR,D.BR),e.openElements.pop(),e.framesetOk=!1;break;case D.BODY:!function(e,t){if(e.openElements.hasInScope(D.BODY)&&(e.insertionMode=v.AFTER_BODY,e.options.sourceCodeLocationInfo)){let n=e.openElements.tryPeekProperlyNestedBodyElement();n&&e._setEndLocation(n,t)}}(e,t);break;case D.HTML:e.openElements.hasInScope(D.BODY)&&(e.insertionMode=v.AFTER_BODY,nl(e,t));break;case D.FORM:!function(e){let t=e.openElements.tmplCount>0,{formElement:n}=e;t||(e.formElement=null),(n||t)&&e.openElements.hasInScope(D.FORM)&&(e.openElements.generateImpliedEndTags(),t?e.openElements.popUntilTagNamePopped(D.FORM):n&&e.openElements.remove(n))}(e);break;case D.APPLET:case D.OBJECT:case D.MARQUEE:!function(e,t){let n=t.tagID;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n),e.activeFormattingElements.clearToLastMarker())}(e,t);break;case D.TEMPLATE:tU(e,t);break;default:tJ(e,t)}}function t0(e,t){e.tmplInsertionModeStack.length>0?no(e,t):tP(e,t)}function t1(e,t){if(void 0!==e.openElements.currentTagId&&tR.has(e.openElements.currentTagId))switch(e.pendingCharacterTokens.length=0,e.hasNonWhitespacePendingCharacterToken=!1,e.originalInsertionMode=e.insertionMode,e.insertionMode=v.IN_TABLE_TEXT,t.type){case _.CHARACTER:t8(e,t);break;case _.WHITESPACE_CHARACTER:t4(e,t)}else t5(e,t)}function t2(e,t){switch(t.tagID){case D.TD:case D.TH:case D.TR:e.openElements.clearBackToTableContext(),e._insertFakeElement(O.TBODY,D.TBODY),e.insertionMode=v.IN_TABLE_BODY,nt(e,t);break;case D.STYLE:case D.SCRIPT:case D.TEMPLATE:tH(e,t);break;case D.COL:e.openElements.clearBackToTableContext(),e._insertFakeElement(O.COLGROUP,D.COLGROUP),e.insertionMode=v.IN_COLUMN_GROUP,t7(e,t);break;case D.FORM:e.formElement||0!==e.openElements.tmplCount||(e._insertElement(t,N.HTML),e.formElement=e.openElements.current,e.openElements.pop());break;case D.TABLE:e.openElements.hasInTableScope(D.TABLE)&&(e.openElements.popUntilTagNamePopped(D.TABLE),e._resetInsertionMode(),e._processStartTag(t));break;case D.TBODY:case D.TFOOT:case D.THEAD:e.openElements.clearBackToTableContext(),e._insertElement(t,N.HTML),e.insertionMode=v.IN_TABLE_BODY;break;case D.INPUT:tV(t)?e._appendElement(t,N.HTML):t5(e,t),t.ackSelfClosing=!0;break;case D.CAPTION:e.openElements.clearBackToTableContext(),e.activeFormattingElements.insertMarker(),e._insertElement(t,N.HTML),e.insertionMode=v.IN_CAPTION;break;case D.COLGROUP:e.openElements.clearBackToTableContext(),e._insertElement(t,N.HTML),e.insertionMode=v.IN_COLUMN_GROUP;break;default:t5(e,t)}}function t3(e,t){switch(t.tagID){case D.TABLE:e.openElements.hasInTableScope(D.TABLE)&&(e.openElements.popUntilTagNamePopped(D.TABLE),e._resetInsertionMode());break;case D.TEMPLATE:tU(e,t);break;case D.BODY:case D.CAPTION:case D.COL:case D.COLGROUP:case D.HTML:case D.TBODY:case D.TD:case D.TFOOT:case D.TH:case D.THEAD:case D.TR:break;default:t5(e,t)}}function t5(e,t){let n=e.fosterParentingEnabled;e.fosterParentingEnabled=!0,tW(e,t),e.fosterParentingEnabled=n}function t4(e,t){e.pendingCharacterTokens.push(t)}function t8(e,t){e.pendingCharacterTokens.push(t),e.hasNonWhitespacePendingCharacterToken=!0}function t6(e,t){let n=0;if(e.hasNonWhitespacePendingCharacterToken)for(;n<e.pendingCharacterTokens.length;n++)t5(e,e.pendingCharacterTokens[n]);else for(;n<e.pendingCharacterTokens.length;n++)e._insertCharacters(e.pendingCharacterTokens[n]);e.insertionMode=e.originalInsertionMode,e._processToken(t)}let t9=new Set([D.CAPTION,D.COL,D.COLGROUP,D.TBODY,D.TD,D.TFOOT,D.TH,D.THEAD,D.TR]);function t7(e,t){switch(t.tagID){case D.HTML:tX(e,t);break;case D.COL:e._appendElement(t,N.HTML),t.ackSelfClosing=!0;break;case D.TEMPLATE:tH(e,t);break;default:ne(e,t)}}function ne(e,t){e.openElements.currentTagId===D.COLGROUP&&(e.openElements.pop(),e.insertionMode=v.IN_TABLE,e._processToken(t))}function nt(e,t){switch(t.tagID){case D.TR:e.openElements.clearBackToTableBodyContext(),e._insertElement(t,N.HTML),e.insertionMode=v.IN_ROW;break;case D.TH:case D.TD:e.openElements.clearBackToTableBodyContext(),e._insertFakeElement(O.TR,D.TR),e.insertionMode=v.IN_ROW,nr(e,t);break;case D.CAPTION:case D.COL:case D.COLGROUP:case D.TBODY:case D.TFOOT:case D.THEAD:e.openElements.hasTableBodyContextInTableScope()&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=v.IN_TABLE,t2(e,t));break;default:t2(e,t)}}function nn(e,t){let n=t.tagID;switch(t.tagID){case D.TBODY:case D.TFOOT:case D.THEAD:e.openElements.hasInTableScope(n)&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=v.IN_TABLE);break;case D.TABLE:e.openElements.hasTableBodyContextInTableScope()&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=v.IN_TABLE,t3(e,t));break;case D.BODY:case D.CAPTION:case D.COL:case D.COLGROUP:case D.HTML:case D.TD:case D.TH:case D.TR:break;default:t3(e,t)}}function nr(e,t){switch(t.tagID){case D.TH:case D.TD:e.openElements.clearBackToTableRowContext(),e._insertElement(t,N.HTML),e.insertionMode=v.IN_CELL,e.activeFormattingElements.insertMarker();break;case D.CAPTION:case D.COL:case D.COLGROUP:case D.TBODY:case D.TFOOT:case D.THEAD:case D.TR:e.openElements.hasInTableScope(D.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=v.IN_TABLE_BODY,nt(e,t));break;default:t2(e,t)}}function ns(e,t){switch(t.tagID){case D.TR:e.openElements.hasInTableScope(D.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=v.IN_TABLE_BODY);break;case D.TABLE:e.openElements.hasInTableScope(D.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=v.IN_TABLE_BODY,nn(e,t));break;case D.TBODY:case D.TFOOT:case D.THEAD:(e.openElements.hasInTableScope(t.tagID)||e.openElements.hasInTableScope(D.TR))&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=v.IN_TABLE_BODY,nn(e,t));break;case D.BODY:case D.CAPTION:case D.COL:case D.COLGROUP:case D.HTML:case D.TD:case D.TH:break;default:t3(e,t)}}function ni(e,t){switch(t.tagID){case D.HTML:tX(e,t);break;case D.OPTION:e.openElements.currentTagId===D.OPTION&&e.openElements.pop(),e._insertElement(t,N.HTML);break;case D.OPTGROUP:e.openElements.currentTagId===D.OPTION&&e.openElements.pop(),e.openElements.currentTagId===D.OPTGROUP&&e.openElements.pop(),e._insertElement(t,N.HTML);break;case D.HR:e.openElements.currentTagId===D.OPTION&&e.openElements.pop(),e.openElements.currentTagId===D.OPTGROUP&&e.openElements.pop(),e._appendElement(t,N.HTML),t.ackSelfClosing=!0;break;case D.INPUT:case D.KEYGEN:case D.TEXTAREA:case D.SELECT:e.openElements.hasInSelectScope(D.SELECT)&&(e.openElements.popUntilTagNamePopped(D.SELECT),e._resetInsertionMode(),t.tagID!==D.SELECT&&e._processStartTag(t));break;case D.SCRIPT:case D.TEMPLATE:tH(e,t)}}function na(e,t){switch(t.tagID){case D.OPTGROUP:e.openElements.stackTop>0&&e.openElements.currentTagId===D.OPTION&&e.openElements.tagIDs[e.openElements.stackTop-1]===D.OPTGROUP&&e.openElements.pop(),e.openElements.currentTagId===D.OPTGROUP&&e.openElements.pop();break;case D.OPTION:e.openElements.currentTagId===D.OPTION&&e.openElements.pop();break;case D.SELECT:e.openElements.hasInSelectScope(D.SELECT)&&(e.openElements.popUntilTagNamePopped(D.SELECT),e._resetInsertionMode());break;case D.TEMPLATE:tU(e,t)}}function no(e,t){e.openElements.tmplCount>0?(e.openElements.popUntilTagNamePopped(D.TEMPLATE),e.activeFormattingElements.clearToLastMarker(),e.tmplInsertionModeStack.shift(),e._resetInsertionMode(),e.onEof(t)):tP(e,t)}function nl(e,t){var n;if(t.tagID===D.HTML){if(e.fragmentContext||(e.insertionMode=v.AFTER_AFTER_BODY),e.options.sourceCodeLocationInfo&&e.openElements.tagIDs[0]===D.HTML){e._setEndLocation(e.openElements.items[0],t);let r=e.openElements.items[1];!r||(null===(n=e.treeAdapter.getNodeSourceCodeLocation(r))||void 0===n?void 0:n.endTag)||e._setEndLocation(r,t)}}else nc(e,t)}function nc(e,t){e.insertionMode=v.IN_BODY,tW(e,t)}function nu(e,t){e.insertionMode=v.IN_BODY,tW(e,t)}function nh(e){for(;e.treeAdapter.getNamespaceURI(e.openElements.current)!==N.HTML&&void 0!==e.openElements.currentTagId&&!e._isIntegrationPoint(e.openElements.currentTagId,e.openElements.current);)e.openElements.pop()}null==String.prototype.codePointAt||((e,t)=>e.codePointAt(t)),O.AREA,O.BASE,O.BASEFONT,O.BGSOUND,O.BR,O.COL,O.EMBED,O.FRAME,O.HR,O.IMG,O.INPUT,O.KEYGEN,O.LINK,O.META,O.PARAM,O.SOURCE,O.TRACK,O.WBR;var nd=n(95510),np=n(94794);let nf=/<(\/?)(iframe|noembed|noframes|plaintext|script|style|textarea|title|xmp)(?=[\t\n\f\r />])/gi,nm=new Set(["mdxFlowExpression","mdxJsxFlowElement","mdxJsxTextElement","mdxTextExpression","mdxjsEsm"]),ng={sourceCodeLocationInfo:!0,scriptingEnabled:!1};function nE(e,t){let n=function(e){let t="root"===e.type?e.children[0]:e;return!!(t&&("doctype"===t.type||"element"===t.type&&"html"===t.tagName.toLowerCase()))}(e),r=ew("type",{handlers:{root:nA,element:n_,text:nS,comment:nk,doctype:ny,raw:nC},unknown:nN}),s={parser:n?new tv(ng):tv.getFragmentParser(void 0,ng),handle(e){r(e,s)},stitches:!1,options:t||{}};r(e,s),nI(s,(0,nd.Pk)());let i=function(e,t){let n=t||{};return Q({file:n.file||void 0,location:!1,schema:"svg"===n.space?x.YP:x.dy,verbose:n.verbose||!1},e)}(n?s.parser.document:s.parser.getFragment(),{file:s.options.file});return(s.stitches&&(0,np.Vn)(i,"comment",function(e,t,n){if(e.value.stitch&&n&&void 0!==t)return n.children[t]=e.value.stitch,t}),"root"===i.type&&1===i.children.length&&i.children[0].type===e.type)?i.children[0]:i}function nT(e,t){let n=-1;if(e)for(;++n<e.length;)t.handle(e[n])}function nA(e,t){nT(e.children,t)}function n_(e,t){(function(e,t){let n=e.tagName.toLowerCase();if(t.parser.tokenizer.state===e2.PLAINTEXT)return;nI(t,(0,nd.Pk)(e));let r=t.parser.openElements.current,s="namespaceURI"in r?r.namespaceURI:j.html;s===j.html&&"svg"===n&&(s=j.svg);let i=ex({...e,children:[]},"svg"===({space:s===j.svg?"svg":"html"}).space?eb:eI),a={type:_.START_TAG,tagName:n,tagID:eJ(n),selfClosing:!1,ackSelfClosing:!1,attrs:"attrs"in i?i.attrs:[],location:nO(e)};t.parser.currentToken=a,t.parser._processToken(t.parser.currentToken),t.parser.tokenizer.lastStartTagName=n})(e,t),nT(e.children,t),function(e,t){let n=e.tagName.toLowerCase();if(!t.parser.tokenizer.inForeignNode&&eH.includes(n)||t.parser.tokenizer.state===e2.PLAINTEXT)return;nI(t,(0,nd.rb)(e));let r={type:_.END_TAG,tagName:n,tagID:eJ(n),selfClosing:!1,ackSelfClosing:!1,attrs:[],location:nO(e)};t.parser.currentToken=r,t.parser._processToken(t.parser.currentToken),n===t.parser.tokenizer.lastStartTagName&&(t.parser.tokenizer.state===e2.RCDATA||t.parser.tokenizer.state===e2.RAWTEXT||t.parser.tokenizer.state===e2.SCRIPT_DATA)&&(t.parser.tokenizer.state=e2.DATA)}(e,t)}function nS(e,t){t.parser.tokenizer.state>4&&(t.parser.tokenizer.state=0);let n={type:_.CHARACTER,chars:e.value,location:nO(e)};nI(t,(0,nd.Pk)(e)),t.parser.currentToken=n,t.parser._processToken(t.parser.currentToken)}function ny(e,t){let n={type:_.DOCTYPE,name:"html",forceQuirks:!1,publicId:"",systemId:"",location:nO(e)};nI(t,(0,nd.Pk)(e)),t.parser.currentToken=n,t.parser._processToken(t.parser.currentToken)}function nk(e,t){let n=e.value,r={type:_.COMMENT,data:n,location:nO(e)};nI(t,(0,nd.Pk)(e)),t.parser.currentToken=r,t.parser._processToken(t.parser.currentToken)}function nC(e,t){if(t.parser.tokenizer.preprocessor.html="",t.parser.tokenizer.preprocessor.pos=-1,t.parser.tokenizer.preprocessor.lastGapPos=-2,t.parser.tokenizer.preprocessor.gapStack=[],t.parser.tokenizer.preprocessor.skipNextNewLine=!1,t.parser.tokenizer.preprocessor.lastChunkWritten=!1,t.parser.tokenizer.preprocessor.endOfChunkHit=!1,t.parser.tokenizer.preprocessor.isEol=!1,nb(t,(0,nd.Pk)(e)),t.parser.tokenizer.write(t.options.tagfilter?e.value.replace(nf,"&lt;$1$2"):e.value,!1),t.parser.tokenizer._runParsingLoop(),72===t.parser.tokenizer.state||78===t.parser.tokenizer.state){t.parser.tokenizer.preprocessor.lastChunkWritten=!0;let e=t.parser.tokenizer._consume();t.parser.tokenizer._callState(e)}}function nN(e,t){if(t.options.passThrough&&t.options.passThrough.includes(e.type))!function(e,t){t.stitches=!0;let n="children"in e?(0,w.ZP)({...e,children:[]}):(0,w.ZP)(e);if("children"in e&&"children"in n){let r=nE({type:"root",children:e.children},t.options);n.children=r.children}nk({type:"comment",value:{stitch:n}},t)}(e,t);else{let t="";throw nm.has(e.type)&&(t=". It looks like you are using MDX nodes with `hast-util-raw` (or `rehype-raw`). If you use this because you are using remark or rehype plugins that inject `'html'` nodes, then please raise an issue with that plugin, as its a bad and slow idea. If you use this because you are using markdown syntax, then you have to configure this utility (or plugin) to pass through these nodes (see `passThrough` in docs), but you can also migrate to use the MDX syntax"),Error("Cannot compile `"+e.type+"` node"+t)}}function nI(e,t){nb(e,t);let n=e.parser.tokenizer.currentCharacterToken;n&&n.location&&(n.location.endLine=e.parser.tokenizer.preprocessor.line,n.location.endCol=e.parser.tokenizer.preprocessor.col+1,n.location.endOffset=e.parser.tokenizer.preprocessor.offset+1,e.parser.currentToken=n,e.parser._processToken(e.parser.currentToken)),e.parser.tokenizer.paused=!1,e.parser.tokenizer.inLoop=!1,e.parser.tokenizer.active=!1,e.parser.tokenizer.returnState=e2.DATA,e.parser.tokenizer.charRefCode=-1,e.parser.tokenizer.consumedAfterSnapshot=-1,e.parser.tokenizer.currentLocation=null,e.parser.tokenizer.currentCharacterToken=null,e.parser.tokenizer.currentToken=null,e.parser.tokenizer.currentAttr={name:"",value:""}}function nb(e,t){if(t&&void 0!==t.offset){let n={startLine:t.line,startCol:t.column,startOffset:t.offset,endLine:-1,endCol:-1,endOffset:-1};e.parser.tokenizer.preprocessor.lineStartPos=-t.column+1,e.parser.tokenizer.preprocessor.droppedBufferSize=t.offset,e.parser.tokenizer.preprocessor.line=t.line,e.parser.tokenizer.currentLocation=n}}function nO(e){let t=(0,nd.Pk)(e)||{line:void 0,column:void 0,offset:void 0},n=(0,nd.rb)(e)||{line:void 0,column:void 0,offset:void 0};return{startLine:t.line,startCol:t.column,startOffset:t.offset,endLine:n.line,endCol:n.column,endOffset:n.offset}}function nD(e){return function(t,n){return nE(t,{...e,file:n})}}},92287:function(e,t,n){"use strict";let r;n.d(t,{AE:function(){return c}});/**
 * uuidv7: A JavaScript implementation of UUID version 7
 *
 * Copyright 2021-2024 LiosK
 *
 * @license Apache-2.0
 * @packageDocumentation
 */let s="0123456789abcdef";class i{constructor(e){this.bytes=e}static ofInner(e){if(16===e.length)return new i(e);throw TypeError("not 128-bit length")}static fromFieldsV7(e,t,n,r){if(!Number.isInteger(e)||!Number.isInteger(t)||!Number.isInteger(n)||!Number.isInteger(r)||e<0||t<0||n<0||r<0||e>0xffffffffffff||t>4095||n>**********||r>**********)throw RangeError("invalid field value");let s=new Uint8Array(16);return s[0]=e/1099511627776,s[1]=e/**********,s[2]=e/16777216,s[3]=e/65536,s[4]=e/256,s[5]=e,s[6]=112|t>>>8,s[7]=t,s[8]=128|n>>>24,s[9]=n>>>16,s[10]=n>>>8,s[11]=n,s[12]=r>>>24,s[13]=r>>>16,s[14]=r>>>8,s[15]=r,new i(s)}static parse(e){var t,n,r,s;let a;switch(e.length){case 32:a=null===(t=/^[0-9a-f]{32}$/i.exec(e))||void 0===t?void 0:t[0];break;case 36:a=null===(n=/^([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})$/i.exec(e))||void 0===n?void 0:n.slice(1,6).join("");break;case 38:a=null===(r=/^\{([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})\}$/i.exec(e))||void 0===r?void 0:r.slice(1,6).join("");break;case 45:a=null===(s=/^urn:uuid:([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})$/i.exec(e))||void 0===s?void 0:s.slice(1,6).join("")}if(a){let e=new Uint8Array(16);for(let t=0;t<16;t+=4){let n=parseInt(a.substring(2*t,2*t+8),16);e[t+0]=n>>>24,e[t+1]=n>>>16,e[t+2]=n>>>8,e[t+3]=n}return new i(e)}throw SyntaxError("could not parse UUID string")}toString(){let e="";for(let t=0;t<this.bytes.length;t++)e+=s.charAt(this.bytes[t]>>>4)+s.charAt(15&this.bytes[t]),(3===t||5===t||7===t||9===t)&&(e+="-");return e}toHex(){let e="";for(let t=0;t<this.bytes.length;t++)e+=s.charAt(this.bytes[t]>>>4)+s.charAt(15&this.bytes[t]);return e}toJSON(){return this.toString()}getVariant(){let e=this.bytes[8]>>>4;if(e<0)throw Error("unreachable");if(e<=7)return this.bytes.every(e=>0===e)?"NIL":"VAR_0";if(e<=11)return"VAR_10";if(e<=13)return"VAR_110";if(e<=15)return this.bytes.every(e=>255===e)?"MAX":"VAR_RESERVED";throw Error("unreachable")}getVersion(){return"VAR_10"===this.getVariant()?this.bytes[6]>>>4:void 0}clone(){return new i(this.bytes.slice(0))}equals(e){return 0===this.compareTo(e)}compareTo(e){for(let t=0;t<16;t++){let n=this.bytes[t]-e.bytes[t];if(0!==n)return Math.sign(n)}return 0}}class a{constructor(e){this.timestamp=0,this.counter=0,this.random=null!=e?e:o()}generate(){return this.generateOrResetCore(Date.now(),1e4)}generateOrAbort(){return this.generateOrAbortCore(Date.now(),1e4)}generateOrResetCore(e,t){let n=this.generateOrAbortCore(e,t);return void 0===n&&(this.timestamp=0,n=this.generateOrAbortCore(e,t)),n}generateOrAbortCore(e,t){if(!Number.isInteger(e)||e<1||e>0xffffffffffff)throw RangeError("`unixTsMs` must be a 48-bit positive integer");if(t<0||t>0xffffffffffff)throw RangeError("`rollbackAllowance` out of reasonable range");if(e>this.timestamp)this.timestamp=e,this.resetCounter();else{if(!(e+t>=this.timestamp))return;this.counter++,this.counter>4398046511103&&(this.timestamp++,this.resetCounter())}return i.fromFieldsV7(this.timestamp,Math.trunc(this.counter/1073741824),this.counter&1073741824-1,this.random.nextUint32())}resetCounter(){this.counter=1024*this.random.nextUint32()+(1023&this.random.nextUint32())}generateV4(){let e=new Uint8Array(Uint32Array.of(this.random.nextUint32(),this.random.nextUint32(),this.random.nextUint32(),this.random.nextUint32()).buffer);return e[6]=64|e[6]>>>4,e[8]=128|e[8]>>>2,i.ofInner(e)}}let o=()=>{if("undefined"!=typeof crypto&&void 0!==crypto.getRandomValues)return new l;if("undefined"!=typeof UUIDV7_DENY_WEAK_RNG&&UUIDV7_DENY_WEAK_RNG)throw Error("no cryptographically strong RNG available");return{nextUint32:()=>65536*Math.trunc(65536*Math.random())+Math.trunc(65536*Math.random())}};class l{constructor(){this.buffer=new Uint32Array(8),this.cursor=65535}nextUint32(){return this.cursor>=this.buffer.length&&(crypto.getRandomValues(this.buffer),this.cursor=0),this.buffer[this.cursor++]}}let c=()=>u().toString(),u=()=>(r||(r=new a)).generate()}}]);