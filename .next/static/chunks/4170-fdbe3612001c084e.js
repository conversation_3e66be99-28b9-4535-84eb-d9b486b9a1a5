"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4170,7220],{44979:function(e,a,s){s.d(a,{Z:function(){return v}});var t=s(75376),n=s(32486),l=s(16006),r=s(11492),i=s(94742),c=s(90937),o=s(22397),d=s(97220),E=s(86418),u=s(47411),N=s(97712),A=s(13352),_=s(73003);let p=e=>{var a;let{children:s,className:i,type:c}=e,{state:p,dispatch:T}=(0,n.useContext)(d.R),{channelDetails:m}=p,[h,C]=(0,n.useState)(!1),[x,S]=(0,n.useState)(""),[I,L]=(0,n.useState)(""),O=(0,u.useParams)(),f=null==O?void 0:O.id,[R,v]=(0,n.useState)(!1);(0,n.useEffect)(()=>{"topic"===c?L(null==m?void 0:m.topic):S(null==m?void 0:m.description)},[c]);let D=async()=>{let e;C(!0),e="topic"===c?{topic:I}:{description:x};let a=await (0,E._x)("/channels/".concat(f),e);if((null==a?void 0:a.status)===200||(null==a?void 0:a.status)===201){var s;T({type:N.a.CHANNEL_CALLBACK,payload:!(null==p?void 0:p.channelCallback)}),A.Z.success(null==a?void 0:null===(s=a.data)||void 0===s?void 0:s.message),C(!1)}else C(!1);v(!1)};return(0,t.jsxs)(l.Vq,{open:R,onOpenChange:v,children:[(0,t.jsx)(l.hg,{className:i,children:s}),(0,t.jsxs)(l.cZ,{className:"p-0 gap-0",children:[(0,t.jsx)(l.fK,{className:"py-4 px-6 border-b",children:(0,t.jsxs)(l.$N,{className:"font-black text-[1.375rem] text-[#1D2939] capitalize",children:["Edit ",c]})}),(0,t.jsx)(l.GG,{className:"absolute right-5 top-4 text-[#344054] p-1 border border-input rounded-[0.3125rem]",children:(0,t.jsx)(o.Z,{className:"size-5 text-[#344054]"})}),(0,t.jsxs)("div",{className:"w-full p-5 space-y-3 flex flex-col",children:["topic"===c?(0,t.jsx)("textarea",{placeholder:"Add a topic",className:"bg-[#F9FAFB] w-full min-h-[10rem] p-2.5 rounded-[0.375rem] border border-input outline-none focus-within:border-[#6868F7] focus-within:ring focus-within:ring-[#D0D0FD] ",value:I,onChange:e=>L(e.target.value)}):(0,t.jsx)("textarea",{placeholder:"Add a description",className:"bg-[#F9FAFB] w-full min-h-[10rem] p-2.5 rounded-[0.375rem] border border-input outline-none focus-within:border-[#6868F7] focus-within:ring focus-within:ring-[#D0D0FD] ",value:x,onChange:e=>S(e.target.value)}),(0,t.jsxs)(l.Be,{className:"text-[0.8125rem] text-[#667085]",children:["Let people know what"," ",(0,t.jsxs)("span",{className:"font-bold text-[#475467]",children:["#",null==p?void 0:null===(a=p.channelDetails)||void 0===a?void 0:a.name]})," ","is focused on right now (ex. a project milestone). Topics are always visible in the header"]}),(0,t.jsxs)("div",{className:"ml-auto space-x-4 ",children:[(0,t.jsx)(l.GG,{children:(0,t.jsx)(r.z,{variant:"outline",className:"min-w-20 h-fit py-2.5 rounded-[0.3125rem]",children:"Cancel"})}),(0,t.jsxs)(r.z,{disabled:h||""===I&&""===x,className:"min-w-20 h-fit py-2.5 bg-primary-500 text-white rounded-[0.3125rem] disabled:opacity-50",onClick:D,children:["Save ",h&&(0,t.jsx)(_.Z,{})]})]})]})]})]})};var T=s(39713),m=s(56792);let h=e=>{let{children:a,className:s}=e,i=(0,u.useParams)().id,{state:c,dispatch:A}=(0,n.useContext)(d.R),[p,T]=(0,n.useState)(!1),m=async()=>{var e;let a={archived:(null==c?void 0:null===(e=c.channelDetails)||void 0===e?void 0:e.archived)!==!0};T(!0);let s=await (0,E.an)("/channels/".concat(i,"/archive"),a);((null==s?void 0:s.status)===200||(null==s?void 0:s.status)===201)&&A({type:N.a.CHANNEL_CALLBACK,payload:!(null==c?void 0:c.channelCallback)}),T(!1)};return(0,t.jsxs)(l.Vq,{children:[(0,t.jsx)(l.hg,{className:s,children:a}),(0,t.jsxs)(l.cZ,{className:"p-0 gap-0",children:[(0,t.jsx)(l.fK,{className:"py-4 px-6 border-b",children:(0,t.jsx)(l.$N,{className:"font-black text-[1.375rem] text-[#1D2939]",children:"Archive this channel?"})}),(0,t.jsx)(l.GG,{className:"absolute right-5 top-4 text-[#344054] p-1 border border-input rounded-[0.3125rem]",children:(0,t.jsx)(o.Z,{className:"size-5 text-[#344054]"})}),(0,t.jsxs)(l.Be,{className:"text-sm text-[#344054] space-y-3 p-5",children:[(0,t.jsx)("p",{children:"When you archive a channel, it’s archived for everyone. That means..."}),(0,t.jsxs)("ul",{className:"list-disc pl-5 space-y-1 text-sm",children:[(0,t.jsx)("li",{children:"No one will be able to send messages to the channel"}),(0,t.jsx)("li",{children:"Any apps installed in the channel will be disabled"}),(0,t.jsx)("li",{children:"If there are external people in this channel, they will be removed. They’ll still have access to the chat history."})]}),(0,t.jsx)("p",{children:"You’ll still be able to find the channel’s contents via search. And you can always unarchive the channel in the future, if you’d like."})]}),(0,t.jsxs)(l.cN,{className:"flex justify-end space-x-2 p-5",children:[(0,t.jsx)(l.GG,{asChild:!0,children:(0,t.jsx)(r.z,{variant:"outline",children:"Cancel"})}),(0,t.jsxs)(r.z,{onClick:m,variant:"destructive",children:["Archive Channel ",p&&(0,t.jsx)(_.Z,{})]})]})]})]})};var C=s(25575),x=e=>{let{data:a,placeholder:s,onSearchResults:l}=e,[r,i]=(0,n.useState)(""),o=(0,n.useMemo)(()=>r.trim()?a.filter(e=>Object.values(e).some(e=>e&&"string"==typeof e&&e.toLowerCase().includes(r.toLowerCase()))):a,[r,a]);return(0,n.useEffect)(()=>{l(o)},[o,l]),(0,t.jsxs)("div",{className:"relative flex items-center mb-4",children:[(0,t.jsx)("span",{className:"absolute left-2.5",children:(0,t.jsx)(c._V,{})}),(0,t.jsx)(C.I,{type:"text",value:r,onChange:e=>i(e.target.value),placeholder:s||"Search...",className:"bg-[#F9FAFB] pl-8 rounded-[0.375rem] focus-within:border-[#6868F7] focus-within:ring focus-within:ring-[#D0D0FD]"})]})},S=s(72738),I=s.n(S),L=s(58983);let O=[{name:"about",Component:function(e){let{setIsOpen:a}=e,[s,l]=(0,n.useState)(!1),i=(0,u.useParams)().id,{state:c,dispatch:o}=(0,n.useContext)(d.R),{channelDetails:A}=c,T=async()=>{l(!0);let e=await (0,E.xo)("/channels/".concat(i,"/leave"),{});((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201)&&(o({type:N.a.CHANNEL_CALLBACK,payload:!(null==c?void 0:c.channelCallback)}),a(!1)),l(!1)};return(0,t.jsxs)("div",{className:"bg-white text-[0.9375rem] divide-y divide-[#E6EAEF] border border-[#E6EAEF] rounded-[0.625rem] overflow-hidden",children:[(0,t.jsxs)("div",{className:"py-4 px-5 flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-bold text-[#101828]",children:"Topic"}),(0,t.jsx)("p",{className:"text-[#667085]",children:(null==A?void 0:A.topic)||"Add a topic"})]}),(0,t.jsx)(p,{type:"topic",className:"text-primary-500 text-[0.8125rem] p-0 h-fit",children:"Edit"})]}),(0,t.jsxs)("div",{className:"py-4 px-5 flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-bold text-[#101828]",children:"Description"}),(0,t.jsx)("p",{className:"text-[#667085]",children:(null==A?void 0:A.description)||"Add a description"})]}),(0,t.jsx)(p,{type:"description",className:"text-primary-500 text-[0.8125rem] p-0 h-fit",children:"Edit"})]}),(0,t.jsx)("div",{className:"py-4 px-5 flex justify-between items-center",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-bold text-[#101828]",children:"Created by"}),(0,t.jsxs)("p",{className:"text-[0.9375rem] text-[#667085]",children:[null==A?void 0:A.owner_name," on"," ",I()(null==A?void 0:A.created_at).format("ll")]})]})}),(0,t.jsx)("div",{className:"py-4 px-5 flex justify-between items-center",children:(0,t.jsx)(h,{children:(0,t.jsx)(r.z,{className:"flex items-center gap-2 text-[#D31103] font-bold p-0 h-fit",children:"Archive channel for everyone"})})}),(0,t.jsx)("div",{className:"py-4 px-5 flex justify-between items-center",children:(0,t.jsxs)(r.z,{onClick:T,className:"flex items-center text-[#D31103] font-bold p-0 h-fit gap-2",children:["Leave channel",s&&(0,t.jsx)(_.Z,{color:"red",height:"15px",width:"15px"})]})})]})}},{name:"people",Component:function(){let{state:e,dispatch:a}=(0,n.useContext)(d.R),{channelDetails:s}=e,[l,i]=(0,n.useState)(null==s?void 0:s.users);return(0,t.jsxs)("div",{children:[(0,t.jsx)(x,{data:null==s?void 0:s.users,placeholder:"Find a user",onSearchResults:i}),(0,t.jsxs)(r.z,{onClick:()=>a({type:N.a.CHANNEL_INVITE,payload:!0}),className:"h-fit p-0 py-3 gap-2 font-semibold text-[#101828] text-[0.9375rem]",children:[(0,t.jsx)("div",{className:"w-8 aspect-square rounded-full bg-[#E6F1FF] flex justify-center items-center",children:(0,t.jsx)(c.oL,{})}),"Add people"]}),(0,t.jsx)("div",{className:"max-h-[300px] overflow-auto",children:null==l?void 0:l.map(e=>{var a,s,n;return(0,t.jsxs)("div",{className:"flex justify-start items-center gap-2.5 py-3",children:[(0,t.jsxs)("figure",{className:"relative aspect-square w-[2.25rem] rounded-[0.4375rem]",children:[(0,t.jsx)(T.default,{src:(null==e?void 0:null===(a=e.profile)||void 0===a?void 0:a.avatar_url)||(null===m.Z||void 0===m.Z?void 0:m.Z.user),alt:"",fill:!0,className:"border rounded"}),(0,t.jsx)("span",{className:"absolute z-50 -bottom-1 -right-1 inline-block w-[0.625rem] aspect-square bg-[#00AD51] border-[1.5px] border-white rounded-full"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)("p",{className:"font-medium text-[#101828] text-[0.9375rem]",children:["@",null==e?void 0:null===(s=e.profile)||void 0===s?void 0:s.username]}),(0,t.jsx)("span",{className:"inline-block bg-[#E6EAEF] w-[0.375rem] aspect-square rounded-full"}),(0,t.jsx)("p",{className:"text-[#475467] text-[0.8125rem]",children:null==e?void 0:null===(n=e.profile)||void 0===n?void 0:n.username})]})]},e)})})]})}},{name:"agents",Component:function(){let{state:e,dispatch:a}=(0,n.useContext)(d.R),{channelAgents:s}=e,[l,i]=(0,n.useState)(s);return(0,t.jsxs)("div",{children:[(0,t.jsx)(x,{data:s,placeholder:"Find an agent",onSearchResults:i}),(0,t.jsxs)(r.z,{onClick:()=>a({type:N.a.AGENT_MODAL,payload:!0}),className:"h-fit p-0 py-3 gap-2 font-semibold text-[#101828] text-[0.9375rem]",children:[(0,t.jsx)("div",{className:"w-8 aspect-square rounded-full bg-[#F2F4F7] flex justify-center items-center",children:(0,t.jsx)(c.wG,{})}),"Add agents"]}),(0,t.jsxs)("div",{className:"max-h-[300px] overflow-auto",children:[null==l?void 0:l.map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center gap-2.5 py-3",children:[(0,t.jsx)("div",{className:"size-8 rounded border border-[#E6EAEF] flex items-center justify-center bg-green-100 relative overflow-hidden",children:(0,t.jsx)(T.default,{src:(null==e?void 0:e.app_logo)||(null===m.Z||void 0===m.Z?void 0:m.Z.bot),alt:null==e?void 0:e.app_name,width:20,height:20,className:"size-8 rounded"})}),(0,t.jsxs)("p",{className:"font-semibold text-[#101828] text-[0.9375rem]",children:["@",null==e?void 0:e.app_name]})]},a)),(null==l?void 0:l.length)===0&&(0,t.jsx)("p",{className:"text-center text-gray-500 my-10 text-sm",children:"No available agent"})]})]})}},{name:"files",Component:function(){return(0,t.jsx)("div",{children:"Content for Tabs here."})}}],f=e=>{var a,s,o,u;let{setIsOpen:N}=e,{state:_}=(0,n.useContext)(d.R),[p,T]=(0,n.useState)(!1),[m,h]=(0,n.useState)(null==_?void 0:_.activeTab),C=[{name:"about",notifs:0},{name:"people",notifs:(null==_?void 0:null===(s=_.channelDetails)||void 0===s?void 0:null===(a=s.users)||void 0===a?void 0:a.length)||0},{name:"agents",notifs:(null==_?void 0:null===(o=_.channelAgents)||void 0===o?void 0:o.length)||0},{name:"files",notifs:0}],x=async()=>{T(!p);let e=await (0,E.xo)("/");(200===e.status||201===e.status)&&A.Z.success(e.data.message)};return(0,t.jsxs)("section",{className:"overflow-hidden h-[600px]",children:[(0,t.jsxs)(l.fK,{className:"py-5 px-6 space-y-3.5 border-b border-[#E6EAEF]",children:[(0,t.jsxs)(l.$N,{className:"font-black text-[#1D2939] text-lg lg:text-[1.375rem] leading-8",children:["# ",null==_?void 0:null===(u=_.channelDetails)||void 0===u?void 0:u.name]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 overflow-auto",children:[(0,t.jsx)(r.z,{variant:"outline",className:"h-fit p-[0.435rem] font-semibold text-[0.8125rem] text-[#344054] gap-1",children:(0,t.jsx)(c.r7,{})}),(0,t.jsxs)(r.z,{variant:"outline",className:"h-fit p-[0.435rem] px-3 font-semibold text-[0.8125rem] text-[#344054] gap-1 ".concat(p?"border-primary-500":""),onClick:x,children:[(0,t.jsx)(c.yP,{color:p?"rgb(113, 65, 248)":""})," ","Mute"]}),(0,t.jsx)(r.z,{variant:"outline",className:"h-fit px-3 py-[0.435rem] text-[0.8125rem] font-semibold text-[#344054]",onClick:()=>h("people"),children:"Go to People Tab"})]})]}),(0,t.jsxs)(i.mQ,{value:m,onValueChange:h,className:"space-y-0",children:[(0,t.jsx)("div",{className:"overflow-x-auto overflow-y-hidden",children:(0,t.jsx)(i.dr,{className:"p-0 px-6 h-fit bg-transparent justify-start gap-8 w-[450px] sm:w-full",children:C.map(e=>{let{name:a,notifs:s}=e;return(0,t.jsxs)(i.SP,{value:a,onClick:e=>e.stopPropagation(),className:"-mb-[1.5px] py-2.5 px-1 font-bold capitalize rounded-none border-b-2 border-transparent data-[state=active]:shadow-none data-[state=active]:border-primary-500",children:[a,0!=s&&(0,t.jsx)("div",{className:"ml-1.5 w-[1.375rem] aspect-square flex justify-center items-center rounded-full text-[#5757CD] bg-[#F2F4F7]",children:s})]},a)})})}),O.map(e=>{let{name:a,Component:s}=e;return(0,t.jsx)(i.nU,{value:a,children:(0,t.jsx)(R,{className:"about"===a?"bg-[#F6F7F9] min-h-[32rem]":"",children:(0,t.jsx)(s,{setIsOpen:N})})},a)})]})]})};function R(e){let{children:a,className:s}=e;return(0,t.jsx)("div",{className:(0,L.cn)("px-2 sm:px-6 py-5 border-t border-[#E6EAEF] rounded-b-[0.625rem]",s),children:a})}var v=e=>{let{children:a,className:s}=e,[r,i]=(0,n.useState)(!1);return(0,t.jsxs)(l.Vq,{open:r,onOpenChange:e=>i(e),children:[(0,t.jsx)(l.hg,{className:s,onClick:()=>i(!0),children:a}),(0,t.jsxs)(l.cZ,{onClick:e=>e.stopPropagation(),className:"p-0 rounded-[0.625rem] w-full sm:max-w-[550px]",children:[(0,t.jsx)(l.GG,{className:"absolute right-5 top-4 text-[#344054] p-1 border border-input rounded-[0.3125rem]",children:(0,t.jsx)(o.Z,{className:"size-5 text-[#344054]"})}),(0,t.jsx)(f,{setIsOpen:i})]})]})}},38858:function(e,a,s){var t=s(75376),n=s(22397),l=s(53189),r=s(37313),i=s(32486),c=s(39713),o=s(11492),d=s(90937),E=s(56792),u=s(97220),N=s(97712),A=s(47411),_=s(36221);a.Z=()=>{let{state:e,dispatch:a}=(0,i.useContext)(u.R),s=(0,A.usePathname)(),p=null==e?void 0:e.userData;return((0,i.useEffect)(()=>{a({type:N.a.SHOW_PROFILE,payload:!1})},[s]),p)?(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsxs)("nav",{className:"flex items-center justify-between p-5 py-[23px] border-b border-[#E6EAEF]",children:[(0,t.jsx)("h2",{className:"text-[#1D2939] text-lg font-bold",children:"Profile"}),(0,t.jsx)("button",{onClick:()=>{a({type:N.a.USER_DATA}),a({type:N.a.SHOW_PROFILE,payload:!1}),a({type:N.a.HOVER_PROFILE,payload:!1})},className:"text-[#344054] p-1 border border-input rounded-[0.3125rem]",children:(0,t.jsx)(n.Z,{className:"size-5 text-[#344054]"})})]}),(0,t.jsxs)("div",{className:"py-5 flex flex-col gap-5 overflow-y-auto [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar-thumb]:rounded-full",children:[(0,t.jsxs)("div",{className:"flex flex-col gap-5 px-5",children:[(0,t.jsx)(c.default,{src:(null==p?void 0:p.avatar_url)?null==p?void 0:p.avatar_url:(null==p?void 0:p.user_type)=="user"||(null==p?void 0:p.user_type)===""?null===E.Z||void 0===E.Z?void 0:E.Z.user:null===E.Z||void 0===E.Z?void 0:E.Z.bot,alt:null==p?void 0:p.username,width:250,height:250,className:"rounded-[9px] border h-[250px] w-[250px] object-cover",unoptimized:!0}),(0,t.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,t.jsx)("div",{className:"flex items-center justify-between gap-3",children:(0,t.jsx)("h2",{className:"text-[#101828] text-[22px] font-black",children:null==p?void 0:p.username})}),(0,t.jsx)("p",{className:"text-[#344054] text-lg",children:null==p?void 0:p.title}),(0,t.jsxs)("div",{className:"flex gap-[10px] items-center",children:[(0,t.jsx)(d.yP,{color:"#475467"}),(0,t.jsx)("p",{className:"text-[15px] text-[#344054]",children:"Away, Notifications snoozed"})]}),(null==p?void 0:p.timezone)&&(0,t.jsxs)("div",{className:"flex gap-[10px] items-center",children:[(0,t.jsx)(d.T3,{}),(0,t.jsx)("p",{className:"text-[15px] text-[#344054]",children:null==p?void 0:p.timezone})]})]}),(0,t.jsxs)("div",{className:"flex gap-3 items-center",children:[(0,t.jsx)(o.z,{variant:"outline",className:"h-fit p-[7px] border-[#E6EAEF]",children:(0,t.jsx)(l.Z,{size:20,strokeWidth:1.5,color:"#667085"})}),(0,t.jsxs)(o.z,{variant:"outline",className:"h-fit py-[7px] px-[10px] border-[#E6EAEF] font-semibold text-[13px] text-[#344054] gap-1",children:[(0,t.jsx)(d.yP,{})," Mute"]}),(0,t.jsxs)(o.z,{variant:"outline",className:"h-fit py-[7px] px-[10px] border-[#E6EAEF] font-semibold text-[13px] text-[#344054] gap-1",children:[(0,t.jsx)(d.eh,{})," Hide"]}),(0,t.jsxs)(o.z,{variant:"outline",className:"h-fit py-[7px] px-[10px] border-[#E6EAEF] font-semibold text-[13px] text-[#344054] gap-1",children:[(0,t.jsx)(d._g,{})," View Files"]}),(0,t.jsx)(o.z,{variant:"outline",className:"h-fit p-[7px] border-[#E6EAEF] font-semibold text-[13px] text-[#344054] gap-1",children:(0,t.jsx)(d.TI,{})})]})]}),(0,t.jsx)("div",{className:"border-t border-[#E6EAEF]"}),(0,t.jsxs)("div",{className:"flex flex-col gap-2 mb-20 px-5",children:[(0,t.jsx)("h4",{className:"text-[15px] text-[#101828] font-bold",children:"Contact Information"}),(null==p?void 0:p.phone)&&(0,t.jsxs)("div",{className:"flex justify-between items-center gap-2",children:[(0,t.jsxs)("div",{className:"flex gap-[10px] items-center",children:[(0,t.jsx)(r.Z,{size:20,color:"#475467"}),(0,t.jsx)("span",{className:"text-sm text-[#6868F7]",children:null==p?void 0:p.phone}),(0,t.jsx)("span",{className:"text-sm text-[#667085]",children:"whatsapp only"})]}),(0,t.jsx)(_.r,{textToCopy:null==p?void 0:p.phone,children:(0,t.jsx)(d.TI,{})})]}),(null==p?void 0:p.email)&&(0,t.jsxs)("div",{className:"flex justify-between items-center gap-2",children:[(0,t.jsxs)("div",{className:"flex gap-[10px] items-center",children:[(0,t.jsx)(d.bV,{}),(0,t.jsx)("span",{className:"text-sm text-[#6868F7]",children:null==p?void 0:p.email})]}),(0,t.jsx)(_.r,{textToCopy:null==p?void 0:p.email,tooltipText:"Copied!",children:(0,t.jsx)(d.TI,{})})]})]})]})]}):null}},89564:function(e,a,s){s.d(a,{F$:function(){return c},Q5:function(){return o},qE:function(){return i}});var t=s(75376),n=s(34450),l=s(32486),r=s(58983);let i=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(n.fC,{ref:a,className:(0,r.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",s),...l})});i.displayName=n.fC.displayName;let c=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(n.Ee,{ref:a,className:(0,r.cn)("aspect-square h-full w-full",s),...l})});c.displayName=n.Ee.displayName;let o=l.forwardRef((e,a)=>{let{className:s,...l}=e;return(0,t.jsx)(n.NY,{ref:a,className:(0,r.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",s),...l})});o.displayName=n.NY.displayName},94742:function(e,a,s){s.d(a,{SP:function(){return o},dr:function(){return c},mQ:function(){return i},nU:function(){return d}});var t=s(75376),n=s(32486),l=s(73068),r=s(58983);let i=l.fC,c=n.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)(l.aV,{ref:a,className:(0,r.cn)("inline-flex h-10 items-center justify-center rounded-md bg-[#F2F4F7] p-1 text-muted-foreground",s),...n})});c.displayName=l.aV.displayName;let o=n.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)(l.xz,{ref:a,className:(0,r.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...n})});o.displayName=l.xz.displayName;let d=n.forwardRef((e,a)=>{let{className:s,...n}=e;return(0,t.jsx)(l.VY,{ref:a,className:(0,r.cn)("mt-4 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...n})});d.displayName=l.VY.displayName},97712:function(e,a,s){s.d(a,{a:function(){return t}});let t={USER:"USER",TOKEN:"TOKEN",ORG_ID:"ORG_ID",CALLBACK:"CALLBACK",LOADING:"LOADING",CHANNEL_LOADING:"CHANNEL_LOADING",NOTIFY:"NOTIFY",ONLINE_STATUS:"ONLINE_STATUS",CHANNELS:"CHANNELS",THREAD:"THREAD",WEBHOOK_STATUS:"WEBHOOK_STATUS",CHANNEL_DETAILS:"CHANNEL_DETAILS",CHANNEL_MODAL:"CHANNEL_MODAL",MEMBERS:"MEMBERS",OTHER_CHANNELS:"OTHER_CHANNELS",ARCHIVED_CHANNELS:"ARCHIVED_CHANNELS",CHANNEL_BAR:"CHANNEL_BAR",VISIBLE:"VISIBLE",VISIBLES:"VISIBLES",SHOW_THREAD:"SHOW_THREAD",OPEN_SIDEBAR:"OPEN_SIDEBAR",APP_CALLBACK:"APP_CALLBACK",SHOW:"SHOW",CREATE_CHANNEL:"CREATE_CHANNEL",CREATE_SECTION:"CREATE_SECTION",ADD_MEMBER:"ADD_MEMBER",ORG_MEMBERS:"ORG_MEMBERS",ORG_INVITES:"ORG_INVITES",ORG_ROLES:"ORG_ROLES",SHOW_RANGE:"SHOW_RANGE",SUMMARY_COUNT:"SUMMARY_COUNT",SECTION_ID:"SECTION_ID",SECTIONS:"SECTIONS",DELETE_SECTION:"DELETE_SECTION",UNGROUPED_CHANNELS:"UNGROUPED_CHANNELS",IS_OPENED:"IS_OPENED",NOTIFICATIONS:"NOTIFICATIONS",MESSAGES:"MESSAGES",MESSAGE_LOADING:"MESSAGE_LOADING",INTEGRATIONS_LOADING:"INTEGRATIONS_LOADING",CHAT_LOADING:"CHAT_LOADING",REPLY_LOADING:"REPLY_LOADING",INTEGRATIONS:"INTEGRATIONS",SUBSCRIPTION_PLAN:"SUBSCRIPTION_PLAN",CHATS:"CHATS",DMS:"DMS",REPLIES:"REPLIES",NOTIFICATION_TYPE:"NOTIFICATION_TYPE",NOTIFICATION_CALLBACK:"NOTIFICATION_CALLBACK",ORG_DATA:"ORG_DATA",CHANNEL_CALLBACK:"CHANNEL_CALLBACK",CHANNEL_SUBSCRIPTION:"CHANNEL_SUBSCRIPTION",CHAT_SUBSCRIPTION:"CHAT_SUBSCRIPTION",REPLY_SUBSCRIPTION:"REPLY_SUBSCRIPTION",NOTIFICATION_SUBSCRIPTION:"NOTIFICATION_SUBSCRIPTION",AGENT_DM:"AGENT_DM",CHANNEL_AGENTS:"CHANNEL_AGENTS",CHANNEL_WORKFLOWS:"CHANNEL_WORKFLOWS",MENTIONS:"MENTIONS",CLEAR_MENTIONS:"CLEAR_MENTIONS",INVITE_MODAL:"INVITE_MODAL",SHOW_PROFILE:"SHOW_PROFILE",REPLY:"REPLY",RECENT_PEOPLE:"RECENT_PEOPLE",RECENT_DM:"RECENT_DM",CLEAR_CHATS:"CLEAR_CHATS",CLEAR_REPLIES:"CLEAR_REPLIES",PROFILE:"PROFILE",SHOW_USER_PROFILE:"SHOW_USER_PROFILE",PROFILE_CALLBACK:"PROFILE_CALLBACK",GROUP_CALLBACK:"GROUP_CALLBACK",REPLY_CALLBACK:"REPLY_CALLBACK",USER_TYPING:"USER_TYPING",TYPING_SUBSCRIPTION:"TYPING_SUBSCRIPTION",UPDATE_MESSAGE_THREAD:"UPDATE_MESSAGE_THREAD",UPDATE_DM_MESSAGE_THREAD:"UPDATE_DM_MESSAGE_THREAD",DELETE_MESSAGE_THREAD_REPLY:"DELETE_MESSAGE_THREAD_REPLY",DELETE_DM_THREAD_REPLY:"DELETE_DM_THREAD_REPLY",DELETE_CHANNEL_MESSAGE:"DELETE_CHANNEL_MESSAGE",DELETE_DM_MESSAGE:"DELETE_DM_MESSAGE",EDIT_CHANNEL_MESSAGE:"EDIT_CHANNEL_MESSAGE",EDIT_DM_MESSAGE:"EDIT_DM_MESSAGE",EDIT_REPLY_MESSAGE:"EDIT_REPLY_MESSAGE",IS_EDIT:"IS_EDIT",IS_EDIT_REPLY:"IS_EDIT_REPLY",THREAD_REPLY:"THREAD_REPLY",ROLE:"ROLE",STATUS_CALLBACK:"STATUS_CALLBACK",DM_NOTIFICATION_SUBSCRIPTION:"DM_NOTIFICATION_SUBSCRIPTION",TOP_LABEL:"TOP_LABEL",ACTIVE_AGENTS:"ACTIVE_AGENTS",INACTIVE_AGENTS:"INACTIVE_AGENTS",MARKETPLACE_AGENTS:"MARKETPLACE_AGENTS",MARKETPLACE_AGENTS_CACHE_TIME:"MARKETPLACE_AGENTS_CACHE_TIME",UPDATE_MENTION_COUNT:"UPDATE_MENTION_COUNT",UPDATE_THREAD_COUNT:"UPDATE_THREAD_COUNT",UPDATE_DM_COUNT:"UPDATE_DM_COUNT",COUNT_CALLBACK:"COUNT_CALLBACK",THREAD_COUNT:"THREAD_COUNT",DM_COUNT:"DM_COUNT",SHOW_BADGE:"SHOW_BADGE",USER_DATA:"USER_DATA",HOVER_PROFILE:"HOVER_PROFILE",STATUS:"STATUS",NOTIFICATION_DETAIL:"NOTIFICATION_DETAIL",CHANNEL_INVITE:"CHANNEL_INVITE",AGENT_MODAL:"AGENT_MODAL",AGENT_STATE:"AGENT_STATE",AGENT_CALLBACK:"AGENT_CALLBACK",SUBSCRIPTION_PLANS:"SUBSCRIPTION_PLANS",CURRENT_SUBCRIPTION:"CURRENT_SUBCRIPTION",ACTIVE_TAB:"ACTIVE_TAB",UPDATE_STREAM:"UPDATE_STREAM",STREAM_COMPLETE:"STREAM_COMPLETE",BOOKMARKS:"BOOKMARKS",PINNED:"PINNED",UPDATE_CHANNEL_PIN:"UPDATE_CHANNEL_PIN",UPDATE_DM_PIN:"UPDATE_DM_PIN",UPDATE_REPLY_PIN:"UPDATE_REPLY_PIN",UPDATE_CHANNEL_REACTIONS:"UPDATE_CHANNEL_REACTIONS",UPDATE_DM_REACTIONS:"UPDATDMEL_REACTIONS",UPDATE_REPLY_REACTIONS:"UPDATE_REPLY_REACTIONS",UPDATE_DM_REPLY_REACTIONS:"UPDATDMEL_REACTIONS",LATER:"LATER",DATA_ID:"DATA_ID",ALL_CHANNELS:"ALL_CHANNELS",WORKFLOWS:"WORKFLOWS",WORKFLOW_CALLBACK:"WORKFLOW_CALLBACK",ACTIVE_WORKFLOWS:"ACTIVE_WORKFLOWS",INACTIVE_WORKFLOWS:"INACTIVE_WORKFLOWS",MARKETPLACE_WORKFLOWS:"MARKETPLACE_WORKFLOWS"}},97220:function(e,a,s){s.d(a,{R:function(){return i},DataProvider:function(){return c}});var t=s(75376),n=s(32486),l=s(97712),r=(e,a)=>{var s,t,n;let{type:r,payload:i}=a;switch(r){case"TOGGLE_AGENT":{let s;let t=e.activatedAgents||[],n=t.findIndex(e=>e.name===a.payload.name);return s=n>=0?[...t.slice(0,n),...t.slice(n+1)]:[...t,a.payload],localStorage.setItem("activatedAgents",JSON.stringify(s)),{...e,activatedAgents:s}}case l.a.USER:return{...e,user:i};case l.a.TOKEN:return{...e,token:i};case l.a.ORG_ID:return{...e,orgId:i};case l.a.CALLBACK:return{...e,callback:i};case l.a.APP_CALLBACK:return{...e,appCallback:i};case l.a.LOADING:return{...e,loading:i};case l.a.CHANNEL_LOADING:return{...e,channelloading:i};case l.a.NOTIFY:return{...e,notify:i};case l.a.ONLINE_STATUS:return{...e,onlineStatus:i};case l.a.CHANNELS:return{...e,channels:i};case l.a.ALL_CHANNELS:return{...e,allChannels:i};case l.a.THREAD:return{...e,thread:i};case l.a.WEBHOOK_STATUS:return{...e,webhookStatus:i};case l.a.CHANNEL_DETAILS:return{...e,channelDetails:i};case l.a.CHANNEL_MODAL:return{...e,channelModal:i};case l.a.MEMBERS:return{...e,members:i};case l.a.OTHER_CHANNELS:return{...e,otherChannels:i};case l.a.ARCHIVED_CHANNELS:return{...e,archivedChannels:i};case l.a.CHANNEL_BAR:return{...e,channelBar:i};case l.a.VISIBLE:return{...e,visible:i};case l.a.VISIBLES:return{...e,visibles:i};case l.a.SHOW_THREAD:return{...e,showThread:i};case l.a.OPEN_SIDEBAR:return{...e,openSidebar:i};case l.a.SHOW:return{...e,show:i};case l.a.CREATE_CHANNEL:return{...e,createChannel:i};case l.a.CREATE_SECTION:return{...e,createSection:i};case l.a.ADD_MEMBER:return{...e,addMember:i};case l.a.ORG_MEMBERS:return{...e,orgMembers:i};case l.a.ORG_INVITES:return{...e,orgInvites:i};case l.a.ORG_ROLES:return{...e,orgRoles:i};case l.a.SHOW_RANGE:return{...e,showRange:i};case l.a.SUMMARY_COUNT:return{...e,summaryCount:i};case l.a.SECTION_ID:return{...e,sectionId:i};case l.a.SECTIONS:return{...e,sections:i};case l.a.DELETE_SECTION:return{...e,deleteSection:i};case l.a.UNGROUPED_CHANNELS:return{...e,ungroupedChannels:i};case l.a.DMS:return{...e,dms:i};case l.a.IS_OPENED:return{...e,isOpened:i};case l.a.NOTIFICATIONS:return{...e,notifications:[...e.notifications,i]};case l.a.MESSAGES:if(null===(s=a.payload)||void 0===s?void 0:s.isRealTime)return{...e,messages:[a.payload.newMessage,...e.messages||[]]};return{...e,messages:1===a.payload.newPage?a.payload.newThreads||[]:[...e.messages||[],...a.payload.newThreads||[]]};case l.a.CHATS:if(null===(t=a.payload)||void 0===t?void 0:t.isRealTime)return{...e,chats:[a.payload.newMessage,...e.chats||[]]};return{...e,chats:1===a.payload.newPage?a.payload.newThreads||[]:[...e.chats||[],...a.payload.newThreads||[]]};case l.a.REPLIES:if(null===(n=a.payload)||void 0===n?void 0:n.isRealTime)return{...e,replies:[a.payload.newMessage,...e.replies||[]]};return{...e,replies:1===a.payload.newPage?a.payload.newThreads||[]:[...e.replies||[],...a.payload.newThreads||[]]};case l.a.CLEAR_CHATS:return{...e,chats:[]};case l.a.CLEAR_REPLIES:return{...e,replies:[]};case l.a.MESSAGE_LOADING:return{...e,messageLoading:i};case l.a.INTEGRATIONS_LOADING:return{...e,integrationsLoading:i};case l.a.CHAT_LOADING:return{...e,chatLoading:i};case l.a.REPLY_LOADING:return{...e,replyLoading:i};case l.a.INTEGRATIONS:return{...e,integrations:i};case l.a.SUBSCRIPTION_PLAN:return{...e,subscriptionPlan:i};case l.a.NOTIFICATION_TYPE:return{...e,notificationType:i};case l.a.NOTIFICATION_CALLBACK:return{...e,notificationCallback:i};case l.a.ORG_DATA:return{...e,orgData:i};case l.a.CHANNEL_CALLBACK:return{...e,channelCallback:i};case l.a.CHANNEL_SUBSCRIPTION:return{...e,channelSubscription:i};case l.a.CHAT_SUBSCRIPTION:return{...e,chatSubscription:i};case l.a.REPLY_SUBSCRIPTION:return{...e,replySubscription:i};case l.a.TYPING_SUBSCRIPTION:return{...e,typingSubscription:i};case l.a.NOTIFICATION_SUBSCRIPTION:return{...e,notificationSubscription:i};case l.a.DM_NOTIFICATION_SUBSCRIPTION:return{...e,dmNotificationSubscription:i};case l.a.AGENT_DM:return{...e,agentDm:i};case l.a.CHANNEL_AGENTS:return{...e,channelAgents:i};case l.a.MENTIONS:return{...e,mentions:[...e.mentions,...i]};case l.a.CLEAR_MENTIONS:return{...e,mentions:[]};case l.a.INVITE_MODAL:return{...e,inviteModal:i};case l.a.SHOW_PROFILE:return{...e,showProfile:i};case l.a.REPLY:return{...e,reply:i};case l.a.RECENT_DM:return{...e,recentDm:i};case l.a.RECENT_PEOPLE:return{...e,recentPeople:i};case l.a.PROFILE:return{...e,profile:i};case l.a.SHOW_USER_PROFILE:return{...e,showUserProfile:i};case l.a.PROFILE_CALLBACK:return{...e,profileCallback:i};case l.a.GROUP_CALLBACK:return{...e,groupCallback:i};case l.a.REPLY_CALLBACK:return{...e,replyCallback:i};case l.a.USER_TYPING:return{...e,userTyping:i};case l.a.UPDATE_MESSAGE_THREAD:{let{threadId:s,reply:t,updates:n}=a.payload,l=e.messages.map(e=>{if(e.thread_id===s){let a=e.messages||[],s=a.some(e=>e.user_id===t.user_id);return{...e,last_reply:null==t?void 0:t.created_at,messages:s?a:[...a,t],message_count:null==n?void 0:n.thread_count}}return e});return{...e,messages:l}}case l.a.UPDATE_DM_MESSAGE_THREAD:{let{threadId:s,reply:t,updates:n}=a.payload,l=e.chats.map(e=>{if(e.thread_id===s){let a=e.messages||[],s=a.some(e=>e.user_id===t.user_id);return{...e,last_reply:null==t?void 0:t.created_at,messages:s?a:[...a,t],message_count:null==n?void 0:n.thread_count}}return e});return{...e,chats:l}}case l.a.DELETE_CHANNEL_MESSAGE:{let{threadId:s}=a.payload;return{...e,messages:(e.messages||[]).filter(e=>e.thread_id!==s)}}case l.a.DELETE_DM_MESSAGE:{let{threadId:s}=a.payload;return{...e,chats:(e.chats||[]).filter(e=>e.thread_id!==s)}}case l.a.DELETE_MESSAGE_THREAD_REPLY:{let{threadId:s,messageId:t,updates:n}=a.payload,l=e.messages.map(e=>{if(e.thread_id===s){let a=e.messages||[],s=a.filter(e=>e.id!==t);return{...e,messages:(null==n?void 0:n.preview_section)?s:a,message_count:null==n?void 0:n.thread_count}}return e});return{...e,replies:(e.replies||[]).filter(e=>e.id!==t),messages:l}}case l.a.DELETE_DM_THREAD_REPLY:{let{threadId:s,messageId:t,updates:n}=a.payload,l=e.chats.map(e=>{if(e.thread_id===s){let a=e.messages||[],s=a.filter(e=>e.id!==t);return{...e,messages:(null==n?void 0:n.preview_section)?s:a,message_count:null==n?void 0:n.thread_count}}return e});return{...e,replies:(e.replies||[]).filter(e=>e.id!==t),chats:l}}case l.a.EDIT_CHANNEL_MESSAGE:{let{threadId:s,newMessageData:t}=a.payload,n=(e.messages||[]).map(e=>e.thread_id===s?{...e,message:t.message,edited:!0}:e);return{...e,messages:n}}case l.a.UPDATE_CHANNEL_PIN:{let{threadId:s,is_pin:t,details:n}=a.payload,l=(e.messages||[]).map(e=>e.thread_id===s?{...e,is_pinned:t,pinned_details:n}:e);return{...e,messages:l}}case l.a.UPDATE_CHANNEL_REACTIONS:{let{threadId:s,reactions:t}=a.payload,n=(e.messages||[]).map(e=>e.thread_id===s?{...e,reactions:t}:e);return{...e,messages:n}}case l.a.UPDATE_REPLY_REACTIONS:{let{messageId:s,reactions:t}=a.payload,n=(e.replies||[]).map(e=>e.id===s?{...e,reactions:t}:e);return{...e,replies:n}}case l.a.UPDATE_DM_REACTIONS:{let{threadId:s,reactions:t}=a.payload,n=(e.chats||[]).map(e=>e.thread_id===s?{...e,reactions:t}:e);return{...e,chats:n}}case l.a.UPDATE_DM_PIN:{let{threadId:s,is_pin:t,details:n}=a.payload,l=(e.chats||[]).map(e=>e.thread_id===s?{...e,is_pinned:t,pinned_details:n}:e);return{...e,chats:l}}case l.a.EDIT_DM_MESSAGE:{let{threadId:s,newMessageData:t}=a.payload,n=(e.chats||[]).map(e=>e.thread_id===s?{...e,message:t.message,edited:!0}:e);return{...e,chats:n}}case l.a.EDIT_REPLY_MESSAGE:{let{threadId:s,newMessageData:t}=a.payload,n=(e.replies||[]).map(e=>e.id===s?{...e,message:t.message,edited:!0}:e);return{...e,replies:n}}case l.a.UPDATE_REPLY_PIN:{let{threadId:s,is_pin:t,details:n}=a.payload,l=(e.replies||[]).map(e=>e.id===s?{...e,is_pinned:t,pinned_details:n}:e);return{...e,replies:l}}case l.a.IS_EDIT:return{...e,isEdit:i};case l.a.IS_EDIT_REPLY:return{...e,isEditReply:i};case l.a.THREAD_REPLY:return{...e,threadReply:i};case l.a.ROLE:return{...e,role:i};case l.a.STATUS_CALLBACK:return{...e,statusCallback:i};case l.a.TOP_LABEL:return{...e,topLabel:i};case l.a.ACTIVE_AGENTS:return{...e,activeAgents:i};case l.a.INACTIVE_AGENTS:return{...e,inactiveAgents:i};case l.a.MARKETPLACE_AGENTS:return{...e,marketPlaceAgents:i};case l.a.MARKETPLACE_AGENTS_CACHE_TIME:return{...e,marketPlaceAgentsCacheTime:i};case l.a.UPDATE_THREAD_COUNT:{let s=a.payload.channels_id;return{...e,channels:e.channels.map(e=>e.channels_id===s?{...e,mention_count:a.payload.mention_count,thread_count:a.payload.thread_count}:e)}}case l.a.UPDATE_DM_COUNT:{let s=a.payload.channel_id;return{...e,dms:e.dms.map(e=>e.channel_id===s?{...e,thread_count:a.payload.thread_count}:e)}}case l.a.COUNT_CALLBACK:return{...e,countCallback:i};case l.a.THREAD_COUNT:return{...e,threadCount:i};case l.a.DM_COUNT:return{...e,dmCount:i};case l.a.SHOW_BADGE:return{...e,showBadge:i};case l.a.USER_DATA:return{...e,userData:i};case l.a.HOVER_PROFILE:return{...e,hoverProfile:i};case l.a.STATUS:return{...e,status:i};case l.a.NOTIFICATION_DETAIL:return{...e,notificationDetail:i};case l.a.CHANNEL_INVITE:return{...e,channelInvite:i};case l.a.AGENT_MODAL:return{...e,agentModal:i};case l.a.AGENT_STATE:return{...e,agentState:i};case l.a.AGENT_CALLBACK:return{...e,agentCallback:i};case l.a.SUBSCRIPTION_PLANS:return{...e,subscriptionPlans:i};case l.a.CURRENT_SUBCRIPTION:return{...e,currentSubscription:i};case l.a.ACTIVE_TAB:return{...e,activeTab:i};case l.a.BOOKMARKS:return{...e,bookmarks:i};case l.a.PINNED:return{...e,pinned:i};case l.a.LATER:return{...e,later:i};case l.a.DATA_ID:return{...e,dataId:i};case l.a.WORKFLOWS:return{...e,workflows:i};case l.a.WORKFLOW_CALLBACK:return{...e,workflowCallback:i};case l.a.CHANNEL_WORKFLOWS:return{...e,channelWorkflows:i};case l.a.ACTIVE_WORKFLOWS:return{...e,activeWorkflows:i};case l.a.INACTIVE_WORKFLOWS:return{...e,inactiveWorkflows:i};case l.a.MARKETPLACE_WORKFLOWS:return{...e,marketPlaceWorkflows:i};default:return e}};let i=(0,n.createContext)(void 0),c=e=>{let{children:a}=e,[s,l]=(0,n.useReducer)(r,{user:null,token:null,callback:!1,loading:!1,channelloading:!0,notify:null,onlineStatus:"offline",channels:null,otherChannels:null,thread:null,webhookStatus:null,channelDetails:null,channelModal:!1,members:null,archivedChannels:null,channelBar:!1,visible:!1,visibles:!1,showThread:!1,openSidebar:!1,appCallback:!1,show:null,createChannel:!1,createSection:!1,addMember:!1,orgMembers:null,orgInvite:null,orgRoles:null,showRange:!1,summaryCount:0,sectionId:null,sections:null,deleteSection:!1,ungroupedChannels:null,isOpened:"",notifications:[],messages:[],chats:[],dms:[],replies:[],integrations:[],messageLoading:!0,integrationsLoading:!0,chatLoading:!0,replyLoading:!0,subscriptionPlan:null,orgId:null,notificationType:null,notificationCallback:!1,orgData:null,channelCallback:!1,channelSubscription:null,chatSubscription:null,replySubscription:null,agentDm:[],channelAgents:[],mentions:[],inviteModal:!1,showProfile:!1,hoverProfile:!1,reply:!1,recentPeople:[],recentDm:[],profile:null,showUserProfile:!1,profileCallback:!1,groupCallback:!1,replyCallback:!1,userTyping:[],typingSubscription:null,notificationSubscription:null,dmNotificationSubscription:null,isEdit:!1,isEditReply:!1,threadReply:null,role:"",statusCallback:!1,topLabel:"Active",activeAgents:[],inactiveAgents:[],marketPlaceAgents:[],marketPlaceAgentsCacheTime:null,countCallback:!1,threadCount:0,dmCount:0,showBadge:!1,userData:null,status:!1,notificationDetail:null,channelInvite:!1,agentModal:!1,agentState:null,agentCallback:!1,bookmarks:[],pinned:[],subscriptionPlans:null,currentSubscription:null,activeTab:"about",later:[],dataId:"",allChannels:[],workflows:[],workflowCallback:!1,channelWorkflows:[],activeWorkflows:[],inactiveWorkflows:[],marketPlaceWorkflows:[]});return(0,t.jsx)(i.Provider,{value:{state:s,dispatch:l},children:a})}}}]);