"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[927],{6114:function(e,n,t){t.d(n,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9824).Z)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},74357:function(e,n,t){t.d(n,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9824).Z)("Hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]])},81356:function(e,n,t){t.d(n,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9824).Z)("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},18208:function(e,n,t){t.d(n,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9824).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},22397:function(e,n,t){t.d(n,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9824).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},94797:function(e,n,t){t.d(n,{Dx:function(){return er},VY:function(){return et},aV:function(){return en},dk:function(){return eo},fC:function(){return J},h_:function(){return ee},x8:function(){return el},xz:function(){return Q}});var r=t(32486),o=t(20100),l=t(29626),i=t(32192),a=t(21971),u=t(31413),s=t(35878),c=t(5887),d=t(79872),f=t(53486),p=t(89801),y=t(67058),v=t(25081),g=t(15623),h=t(91007),m=t(75376),x="Dialog",[D,b]=(0,i.b)(x),[k,j]=D(x),R=e=>{let{__scopeDialog:n,children:t,open:o,defaultOpen:l,onOpenChange:i,modal:s=!0}=e,c=r.useRef(null),d=r.useRef(null),[f,p]=(0,u.T)({prop:o,defaultProp:null!=l&&l,onChange:i,caller:x});return(0,m.jsx)(k,{scope:n,triggerRef:c,contentRef:d,contentId:(0,a.M)(),titleId:(0,a.M)(),descriptionId:(0,a.M)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:s,children:t})};R.displayName=x;var C="DialogTrigger",w=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,i=j(C,t),a=(0,l.e)(n,i.triggerRef);return(0,m.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":K(i.open),...r,ref:a,onClick:(0,o.M)(e.onClick,i.onOpenToggle)})});w.displayName=C;var M="DialogPortal",[N,I]=D(M,{forceMount:void 0}),O=e=>{let{__scopeDialog:n,forceMount:t,children:o,container:l}=e,i=j(M,n);return(0,m.jsx)(N,{scope:n,forceMount:t,children:r.Children.map(o,e=>(0,m.jsx)(f.z,{present:t||i.open,children:(0,m.jsx)(d.h,{asChild:!0,container:l,children:e})}))})};O.displayName=M;var _="DialogOverlay",E=r.forwardRef((e,n)=>{let t=I(_,e.__scopeDialog),{forceMount:r=t.forceMount,...o}=e,l=j(_,e.__scopeDialog);return l.modal?(0,m.jsx)(f.z,{present:r||l.open,children:(0,m.jsx)(Z,{...o,ref:n})}):null});E.displayName=_;var F=(0,h.Z8)("DialogOverlay.RemoveScroll"),Z=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=j(_,t);return(0,m.jsx)(v.Z,{as:F,allowPinchZoom:!0,shards:[o.contentRef],children:(0,m.jsx)(p.WV.div,{"data-state":K(o.open),...r,ref:n,style:{pointerEvents:"auto",...r.style}})})}),A="DialogContent",P=r.forwardRef((e,n)=>{let t=I(A,e.__scopeDialog),{forceMount:r=t.forceMount,...o}=e,l=j(A,e.__scopeDialog);return(0,m.jsx)(f.z,{present:r||l.open,children:l.modal?(0,m.jsx)(W,{...o,ref:n}):(0,m.jsx)(V,{...o,ref:n})})});P.displayName=A;var W=r.forwardRef((e,n)=>{let t=j(A,e.__scopeDialog),i=r.useRef(null),a=(0,l.e)(n,t.contentRef,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,g.Ry)(e)},[]),(0,m.jsx)(T,{...e,ref:a,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var n;e.preventDefault(),null===(n=t.triggerRef.current)||void 0===n||n.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let n=e.detail.originalEvent,t=0===n.button&&!0===n.ctrlKey;(2===n.button||t)&&e.preventDefault()}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault())})}),V=r.forwardRef((e,n)=>{let t=j(A,e.__scopeDialog),o=r.useRef(!1),l=r.useRef(!1);return(0,m.jsx)(T,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:n=>{var r,i;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,n),n.defaultPrevented||(o.current||null===(i=t.triggerRef.current)||void 0===i||i.focus(),n.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:n=>{var r,i;null===(r=e.onInteractOutside)||void 0===r||r.call(e,n),n.defaultPrevented||(o.current=!0,"pointerdown"!==n.detail.originalEvent.type||(l.current=!0));let a=n.target;(null===(i=t.triggerRef.current)||void 0===i?void 0:i.contains(a))&&n.preventDefault(),"focusin"===n.detail.originalEvent.type&&l.current&&n.preventDefault()}})}),T=r.forwardRef((e,n)=>{let{__scopeDialog:t,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:a,...u}=e,d=j(A,t),f=r.useRef(null),p=(0,l.e)(n,f);return(0,y.EW)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(c.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:a,children:(0,m.jsx)(s.XB,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":K(d.open),...u,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)($,{titleId:d.titleId}),(0,m.jsx)(G,{contentRef:f,descriptionId:d.descriptionId})]})]})}),q="DialogTitle",z=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=j(q,t);return(0,m.jsx)(p.WV.h2,{id:o.titleId,...r,ref:n})});z.displayName=q;var B="DialogDescription",S=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=j(B,t);return(0,m.jsx)(p.WV.p,{id:o.descriptionId,...r,ref:n})});S.displayName=B;var H="DialogClose",X=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,l=j(H,t);return(0,m.jsx)(p.WV.button,{type:"button",...r,ref:n,onClick:(0,o.M)(e.onClick,()=>l.onOpenChange(!1))})});function K(e){return e?"open":"closed"}X.displayName=H;var L="DialogTitleWarning",[U,Y]=(0,i.k)(L,{contentName:A,titleName:q,docsSlug:"dialog"}),$=e=>{let{titleId:n}=e,t=Y(L),o="`".concat(t.contentName,"` requires a `").concat(t.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(t.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(t.docsSlug);return r.useEffect(()=>{n&&!document.getElementById(n)&&console.error(o)},[o,n]),null},G=e=>{let{contentRef:n,descriptionId:t}=e,o=Y("DialogDescriptionWarning"),l="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=n.current)||void 0===e?void 0:e.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(l)},[l,n,t]),null},J=R,Q=w,ee=O,en=E,et=P,er=z,eo=S,el=X},53447:function(e,n,t){t.d(n,{j:function(){return i}});var r=t(89824);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=r.W,i=(e,n)=>t=>{var r;if((null==n?void 0:n.variants)==null)return l(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:i,defaultVariants:a}=n,u=Object.keys(i).map(e=>{let n=null==t?void 0:t[e],r=null==a?void 0:a[e];if(null===n)return null;let l=o(n)||o(r);return i[e][l]}),s=t&&Object.entries(t).reduce((e,n)=>{let[t,r]=n;return void 0===r||(e[t]=r),e},{});return l(e,u,null==n?void 0:null===(r=n.compoundVariants)||void 0===r?void 0:r.reduce((e,n)=>{let{class:t,className:r,...o}=n;return Object.entries(o).every(e=>{let[n,t]=e;return Array.isArray(t)?t.includes({...a,...s}[n]):({...a,...s})[n]===t})?[...e,t,r]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}}}]);