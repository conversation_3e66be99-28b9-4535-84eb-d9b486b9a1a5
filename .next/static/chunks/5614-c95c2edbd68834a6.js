"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5614],{35614:function(e,t,l){l.d(t,{B:function(){return i}});var r=l(32486),n=l(32192),o=l(29626),c=l(91007),u=l(75376);function i(e){let t=e+"CollectionProvider",[l,i]=(0,n.b)(t),[f,a]=l(t,{collectionRef:{current:null},itemMap:new Map}),s=e=>{let{scope:t,children:l}=e,n=r.useRef(null),o=r.useRef(new Map).current;return(0,u.jsx)(f,{scope:t,itemMap:o,collectionRef:n,children:l})};s.displayName=t;let d=e+"CollectionSlot",m=(0,c.Z8)(d),p=r.forwardRef((e,t)=>{let{scope:l,children:r}=e,n=a(d,l),c=(0,o.e)(t,n.collectionRef);return(0,u.jsx)(m,{ref:c,children:r})});p.displayName=d;let R=e+"CollectionItemSlot",C="data-radix-collection-item",M=(0,c.Z8)(R),w=r.forwardRef((e,t)=>{let{scope:l,children:n,...c}=e,i=r.useRef(null),f=(0,o.e)(t,i),s=a(R,l);return r.useEffect(()=>(s.itemMap.set(i,{ref:i,...c}),()=>void s.itemMap.delete(i))),(0,u.jsx)(M,{[C]:"",ref:f,children:n})});return w.displayName=R,[{Provider:s,Slot:p,ItemSlot:w},function(t){let l=a(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=l.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(C,"]")));return Array.from(l.itemMap.values()).sort((e,l)=>t.indexOf(e.ref.current)-t.indexOf(l.ref.current))},[l.collectionRef,l.itemMap])},i]}}}]);