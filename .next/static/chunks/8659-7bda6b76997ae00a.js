"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8659],{99402:function(e,t,n){n.d(t,{Z:function(){return l}});var r=n(32486),o=n(49021);function l(e){let t=(0,r.useRef)(null),n=(0,r.useRef)(null);return n.current&&n.current.update(e),(0,r.useEffect)(()=>(n.current=new o.cW({...e,ref:t}),()=>{n.current=null}),[]),r.createElement("div",{ref:t})}},47720:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Bookmark",[["path",{d:"m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z",key:"1fy3hk"}]])},37847:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Pin",[["path",{d:"M12 17v5",key:"bb1du9"}],["path",{d:"M9 10.76a2 2 0 0 1-1.11 1.79l-1.78.9A2 2 0 0 0 5 15.24V16a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-.76a2 2 0 0 0-1.11-1.79l-1.78-.9A2 2 0 0 1 15 10.76V7a1 1 0 0 1 1-1 2 2 0 0 0 0-4H8a2 2 0 0 0 0 4 1 1 0 0 1 1 1z",key:"1nkz8b"}]])},75148:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Smile",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 14s1.5 2 4 2 4-2 4-2",key:"1y1vjs"}],["line",{x1:"9",x2:"9.01",y1:"9",y2:"9",key:"yxxnd0"}],["line",{x1:"15",x2:"15.01",y1:"9",y2:"9",key:"1p4y9e"}]])},22397:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},94797:function(e,t,n){n.d(t,{Dx:function(){return er},VY:function(){return en},aV:function(){return et},dk:function(){return eo},fC:function(){return J},h_:function(){return ee},x8:function(){return el},xz:function(){return Q}});var r=n(32486),o=n(20100),l=n(29626),i=n(32192),a=n(21971),u=n(31413),s=n(35878),c=n(5887),d=n(79872),f=n(53486),p=n(89801),v=n(67058),g=n(25081),y=n(15623),h=n(91007),x=n(75376),m="Dialog",[b,w]=(0,i.b)(m),[C,R]=b(m),j=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:l,onOpenChange:i,modal:s=!0}=e,c=r.useRef(null),d=r.useRef(null),[f,p]=(0,u.T)({prop:o,defaultProp:null!=l&&l,onChange:i,caller:m});return(0,x.jsx)(C,{scope:t,triggerRef:c,contentRef:d,contentId:(0,a.M)(),titleId:(0,a.M)(),descriptionId:(0,a.M)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:s,children:n})};j.displayName=m;var k="DialogTrigger",E=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=R(k,n),a=(0,l.e)(t,i.triggerRef);return(0,x.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":Y(i.open),...r,ref:a,onClick:(0,o.M)(e.onClick,i.onOpenToggle)})});E.displayName=k;var T="DialogPortal",[D,M]=b(T,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:l}=e,i=R(T,t);return(0,x.jsx)(D,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,x.jsx)(f.z,{present:n||i.open,children:(0,x.jsx)(d.h,{asChild:!0,container:l,children:e})}))})};O.displayName=T;var _="DialogOverlay",I=r.forwardRef((e,t)=>{let n=M(_,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,l=R(_,e.__scopeDialog);return l.modal?(0,x.jsx)(f.z,{present:r||l.open,children:(0,x.jsx)(P,{...o,ref:t})}):null});I.displayName=_;var N=(0,h.Z8)("DialogOverlay.RemoveScroll"),P=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=R(_,n);return(0,x.jsx)(g.Z,{as:N,allowPinchZoom:!0,shards:[o.contentRef],children:(0,x.jsx)(p.WV.div,{"data-state":Y(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),V="DialogContent",F=r.forwardRef((e,t)=>{let n=M(V,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,l=R(V,e.__scopeDialog);return(0,x.jsx)(f.z,{present:r||l.open,children:l.modal?(0,x.jsx)(L,{...o,ref:t}):(0,x.jsx)(A,{...o,ref:t})})});F.displayName=V;var L=r.forwardRef((e,t)=>{let n=R(V,e.__scopeDialog),i=r.useRef(null),a=(0,l.e)(t,n.contentRef,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,y.Ry)(e)},[]),(0,x.jsx)(W,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault())})}),A=r.forwardRef((e,t)=>{let n=R(V,e.__scopeDialog),o=r.useRef(!1),l=r.useRef(!1);return(0,x.jsx)(W,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,i;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current||null===(i=n.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:t=>{var r,i;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(l.current=!0));let a=t.target;(null===(i=n.triggerRef.current)||void 0===i?void 0:i.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),W=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:a,...u}=e,d=R(V,n),f=r.useRef(null),p=(0,l.e)(t,f);return(0,v.EW)(),(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(c.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:a,children:(0,x.jsx)(s.XB,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":Y(d.open),...u,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)($,{titleId:d.titleId}),(0,x.jsx)(G,{contentRef:f,descriptionId:d.descriptionId})]})]})}),Z="DialogTitle",z=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=R(Z,n);return(0,x.jsx)(p.WV.h2,{id:o.titleId,...r,ref:t})});z.displayName=Z;var B="DialogDescription",S=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=R(B,n);return(0,x.jsx)(p.WV.p,{id:o.descriptionId,...r,ref:t})});S.displayName=B;var H="DialogClose",X=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=R(H,n);return(0,x.jsx)(p.WV.button,{type:"button",...r,ref:t,onClick:(0,o.M)(e.onClick,()=>l.onOpenChange(!1))})});function Y(e){return e?"open":"closed"}X.displayName=H;var q="DialogTitleWarning",[K,U]=(0,i.k)(q,{contentName:V,titleName:Z,docsSlug:"dialog"}),$=e=>{let{titleId:t}=e,n=U(q),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},G=e=>{let{contentRef:t,descriptionId:n}=e,o=U("DialogDescriptionWarning"),l="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(l)},[l,t,n]),null},J=j,Q=E,ee=O,et=I,en=F,er=z,eo=S,el=X},82288:function(e,t,n){n.d(t,{Ce:function(){return Z},VY:function(){return H},fC:function(){return B},xz:function(){return S},zt:function(){return z}});var r=n(32486),o=n(20100),l=n(29626),i=n(32192),a=n(35878),u=n(21971),s=n(25117),c=(n(79872),n(53486)),d=n(89801),f=n(91007),p=n(31413),v=n(72002),g=n(75376),[y,h]=(0,i.b)("Tooltip",[s.D7]),x=(0,s.D7)(),m="TooltipProvider",b="tooltip.open",[w,C]=y(m),R=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:o=300,disableHoverableContent:l=!1,children:i}=e,a=r.useRef(!0),u=r.useRef(!1),s=r.useRef(0);return r.useEffect(()=>{let e=s.current;return()=>window.clearTimeout(e)},[]),(0,g.jsx)(w,{scope:t,isOpenDelayedRef:a,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(s.current),a.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(s.current),s.current=window.setTimeout(()=>a.current=!0,o)},[o]),isPointerInTransitRef:u,onPointerInTransitChange:r.useCallback(e=>{u.current=e},[]),disableHoverableContent:l,children:i})};R.displayName=m;var j="Tooltip",[k,E]=y(j),T=e=>{let{__scopeTooltip:t,children:n,open:o,defaultOpen:l,onOpenChange:i,disableHoverableContent:a,delayDuration:c}=e,d=C(j,e.__scopeTooltip),f=x(t),[v,y]=r.useState(null),h=(0,u.M)(),m=r.useRef(0),w=null!=a?a:d.disableHoverableContent,R=null!=c?c:d.delayDuration,E=r.useRef(!1),[T,D]=(0,p.T)({prop:o,defaultProp:null!=l&&l,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(b))):d.onClose(),null==i||i(e)},caller:j}),M=r.useMemo(()=>T?E.current?"delayed-open":"instant-open":"closed",[T]),O=r.useCallback(()=>{window.clearTimeout(m.current),m.current=0,E.current=!1,D(!0)},[D]),_=r.useCallback(()=>{window.clearTimeout(m.current),m.current=0,D(!1)},[D]),I=r.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{E.current=!0,D(!0),m.current=0},R)},[R,D]);return r.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),(0,g.jsx)(s.fC,{...f,children:(0,g.jsx)(k,{scope:t,contentId:h,open:T,stateAttribute:M,trigger:v,onTriggerChange:y,onTriggerEnter:r.useCallback(()=>{d.isOpenDelayedRef.current?I():O()},[d.isOpenDelayedRef,I,O]),onTriggerLeave:r.useCallback(()=>{w?_():(window.clearTimeout(m.current),m.current=0)},[_,w]),onOpen:O,onClose:_,disableHoverableContent:w,children:n})})};T.displayName=j;var D="TooltipTrigger",M=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...i}=e,a=E(D,n),u=C(D,n),c=x(n),f=r.useRef(null),p=(0,l.e)(t,f,a.onTriggerChange),v=r.useRef(!1),y=r.useRef(!1),h=r.useCallback(()=>v.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",h),[h]),(0,g.jsx)(s.ee,{asChild:!0,...c,children:(0,g.jsx)(d.WV.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...i,ref:p,onPointerMove:(0,o.M)(e.onPointerMove,e=>{"touch"===e.pointerType||y.current||u.isPointerInTransitRef.current||(a.onTriggerEnter(),y.current=!0)}),onPointerLeave:(0,o.M)(e.onPointerLeave,()=>{a.onTriggerLeave(),y.current=!1}),onPointerDown:(0,o.M)(e.onPointerDown,()=>{a.open&&a.onClose(),v.current=!0,document.addEventListener("pointerup",h,{once:!0})}),onFocus:(0,o.M)(e.onFocus,()=>{v.current||a.onOpen()}),onBlur:(0,o.M)(e.onBlur,a.onClose),onClick:(0,o.M)(e.onClick,a.onClose)})})});M.displayName=D;var[O,_]=y("TooltipPortal",{forceMount:void 0}),I="TooltipContent",N=r.forwardRef((e,t)=>{let n=_(I,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...l}=e,i=E(I,e.__scopeTooltip);return(0,g.jsx)(c.z,{present:r||i.open,children:i.disableHoverableContent?(0,g.jsx)(A,{side:o,...l,ref:t}):(0,g.jsx)(P,{side:o,...l,ref:t})})}),P=r.forwardRef((e,t)=>{let n=E(I,e.__scopeTooltip),o=C(I,e.__scopeTooltip),i=r.useRef(null),a=(0,l.e)(t,i),[u,s]=r.useState(null),{trigger:c,onClose:d}=n,f=i.current,{onPointerInTransitChange:p}=o,v=r.useCallback(()=>{s(null),p(!1)},[p]),y=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),l=Math.abs(t.left-e.x);switch(Math.min(n,r,o,l)){case l:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());s(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),p(!0)},[p]);return r.useEffect(()=>()=>v(),[v]),r.useEffect(()=>{if(c&&f){let e=e=>y(e,f),t=e=>y(e,c);return c.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[c,f,y,v]),r.useEffect(()=>{if(u){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==f?void 0:f.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,l=t.length-1;e<t.length;l=e++){let i=t[e],a=t[l],u=i.x,s=i.y,c=a.x,d=a.y;s>r!=d>r&&n<(c-u)*(r-s)/(d-s)+u&&(o=!o)}return o}(n,u);r?v():o&&(v(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,f,u,d,v]),(0,g.jsx)(A,{...e,ref:a})}),[V,F]=y(j,{isInside:!1}),L=(0,f.sA)("TooltipContent"),A=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:o,"aria-label":l,onEscapeKeyDown:i,onPointerDownOutside:u,...c}=e,d=E(I,n),f=x(n),{onClose:p}=d;return r.useEffect(()=>(document.addEventListener(b,p),()=>document.removeEventListener(b,p)),[p]),r.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,p]),(0,g.jsx)(a.XB,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,g.jsxs)(s.VY,{"data-state":d.stateAttribute,...f,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,g.jsx)(L,{children:o}),(0,g.jsx)(V,{scope:n,isInside:!0,children:(0,g.jsx)(v.fC,{id:d.contentId,role:"tooltip",children:l||o})})]})})});N.displayName=I;var W="TooltipArrow",Z=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=x(n);return F(W,n).isInside?null:(0,g.jsx)(s.Eh,{...o,...r,ref:t})});Z.displayName=W;var z=R,B=T,S=M,H=N},72002:function(e,t,n){n.d(t,{C2:function(){return i},TX:function(){return a},fC:function(){return u}});var r=n(32486),o=n(89801),l=n(75376),i=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=r.forwardRef((e,t)=>(0,l.jsx)(o.WV.span,{...e,ref:t,style:{...i,...e.style}}));a.displayName="VisuallyHidden";var u=a},53447:function(e,t,n){n.d(t,{j:function(){return i}});var r=n(89824);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=r.W,i=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return l(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:i,defaultVariants:a}=t,u=Object.keys(i).map(e=>{let t=null==n?void 0:n[e],r=null==a?void 0:a[e];if(null===t)return null;let l=o(t)||o(r);return i[e][l]}),s=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return l(e,u,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...o}=t;return Object.entries(o).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...a,...s}[t]):({...a,...s})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}}}]);