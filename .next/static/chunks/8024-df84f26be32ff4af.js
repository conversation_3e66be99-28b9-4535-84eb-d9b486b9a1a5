"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8024],{32425:function(e,r,t){t.d(r,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(9824).Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},398:function(e,r,t){t.d(r,{gm:function(){return u}});var n=t(32486);t(75376);var o=n.createContext(void 0);function u(e){let r=n.useContext(o);return e||r||"ltr"}},40472:function(e,r,t){t.d(r,{ck:function(){return K},fC:function(){return G},z$:function(){return V}});var n=t(32486),o=t(20100),u=t(29626),a=t(32192),i=t(89801),l=t(50523),c=t(31413),s=t(398),d=t(30915),f=t(75659),p=t(53486),v=t(75376),m="Radio",[w,h]=(0,a.b)(m),[b,y]=w(m),g=n.forwardRef((e,r)=>{let{__scopeRadio:t,name:a,checked:l=!1,required:c,disabled:s,value:d="on",onCheck:f,form:p,...m}=e,[w,h]=n.useState(null),y=(0,u.e)(r,e=>h(e)),g=n.useRef(!1),k=!w||p||!!w.closest("form");return(0,v.jsxs)(b,{scope:t,checked:l,disabled:s,children:[(0,v.jsx)(i.WV.button,{type:"button",role:"radio","aria-checked":l,"data-state":C(l),"data-disabled":s?"":void 0,disabled:s,value:d,...m,ref:y,onClick:(0,o.M)(e.onClick,e=>{l||null==f||f(),k&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})}),k&&(0,v.jsx)(x,{control:w,bubbles:!g.current,name:a,value:d,checked:l,required:c,disabled:s,form:p,style:{transform:"translateX(-100%)"}})]})});g.displayName=m;var k="RadioIndicator",R=n.forwardRef((e,r)=>{let{__scopeRadio:t,forceMount:n,...o}=e,u=y(k,t);return(0,v.jsx)(p.z,{present:n||u.checked,children:(0,v.jsx)(i.WV.span,{"data-state":C(u.checked),"data-disabled":u.disabled?"":void 0,...o,ref:r})})});R.displayName=k;var x=n.forwardRef((e,r)=>{let{__scopeRadio:t,control:o,checked:a,bubbles:l=!0,...c}=e,s=n.useRef(null),p=(0,u.e)(s,r),m=(0,f.D)(a),w=(0,d.t)(o);return n.useEffect(()=>{let e=s.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==a&&r){let t=new Event("click",{bubbles:l});r.call(e,a),e.dispatchEvent(t)}},[m,a,l]),(0,v.jsx)(i.WV.input,{type:"radio","aria-hidden":!0,defaultChecked:a,...c,tabIndex:-1,ref:p,style:{...c.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function C(e){return e?"checked":"unchecked"}x.displayName="RadioBubbleInput";var E=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],j="RadioGroup",[I,A]=(0,a.b)(j,[l.Pc,h]),D=(0,l.Pc)(),F=h(),[M,T]=I(j),L=n.forwardRef((e,r)=>{let{__scopeRadioGroup:t,name:n,defaultValue:o,value:u,required:a=!1,disabled:d=!1,orientation:f,dir:p,loop:m=!0,onValueChange:w,...h}=e,b=D(t),y=(0,s.gm)(p),[g,k]=(0,c.T)({prop:u,defaultProp:null!=o?o:null,onChange:w,caller:j});return(0,v.jsx)(M,{scope:t,name:n,required:a,disabled:d,value:g,onValueChange:k,children:(0,v.jsx)(l.fC,{asChild:!0,...b,orientation:f,dir:y,loop:m,children:(0,v.jsx)(i.WV.div,{role:"radiogroup","aria-required":a,"aria-orientation":f,"data-disabled":d?"":void 0,dir:y,...h,ref:r})})})});L.displayName=j;var P="RadioGroupItem",S=n.forwardRef((e,r)=>{let{__scopeRadioGroup:t,disabled:a,...i}=e,c=T(P,t),s=c.disabled||a,d=D(t),f=F(t),p=n.useRef(null),m=(0,u.e)(r,p),w=c.value===i.value,h=n.useRef(!1);return n.useEffect(()=>{let e=e=>{E.includes(e.key)&&(h.current=!0)},r=()=>h.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",r),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",r)}},[]),(0,v.jsx)(l.ck,{asChild:!0,...d,focusable:!s,active:w,children:(0,v.jsx)(g,{disabled:s,required:c.required,checked:w,...f,...i,name:c.name,ref:m,onCheck:()=>c.onValueChange(i.value),onKeyDown:(0,o.M)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,o.M)(i.onFocus,()=>{var e;h.current&&(null===(e=p.current)||void 0===e||e.click())})})})});S.displayName=P;var N=n.forwardRef((e,r)=>{let{__scopeRadioGroup:t,...n}=e,o=F(t);return(0,v.jsx)(R,{...o,...n,ref:r})});N.displayName="RadioGroupIndicator";var G=L,K=S,V=N},50523:function(e,r,t){t.d(r,{Pc:function(){return k},ck:function(){return M},fC:function(){return F}});var n=t(32486),o=t(20100),u=t(35614),a=t(29626),i=t(32192),l=t(21971),c=t(89801),s=t(15920),d=t(31413),f=t(398),p=t(75376),v="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},w="RovingFocusGroup",[h,b,y]=(0,u.B)(w),[g,k]=(0,i.b)(w,[y]),[R,x]=g(w),C=n.forwardRef((e,r)=>(0,p.jsx)(h.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(h.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(E,{...e,ref:r})})}));C.displayName=w;var E=n.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,orientation:u,loop:i=!1,dir:l,currentTabStopId:h,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:g,onEntryFocus:k,preventScrollOnEntryFocus:x=!1,...C}=e,E=n.useRef(null),j=(0,a.e)(r,E),I=(0,f.gm)(l),[A,F]=(0,d.T)({prop:h,defaultProp:null!=y?y:null,onChange:g,caller:w}),[M,T]=n.useState(!1),L=(0,s.W)(k),P=b(t),S=n.useRef(!1),[N,G]=n.useState(0);return n.useEffect(()=>{let e=E.current;if(e)return e.addEventListener(v,L),()=>e.removeEventListener(v,L)},[L]),(0,p.jsx)(R,{scope:t,orientation:u,dir:I,loop:i,currentTabStopId:A,onItemFocus:n.useCallback(e=>F(e),[F]),onItemShiftTab:n.useCallback(()=>T(!0),[]),onFocusableItemAdd:n.useCallback(()=>G(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>G(e=>e-1),[]),children:(0,p.jsx)(c.WV.div,{tabIndex:M||0===N?-1:0,"data-orientation":u,...C,ref:j,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{S.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let r=!S.current;if(e.target===e.currentTarget&&r&&!M){let r=new CustomEvent(v,m);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=P().filter(e=>e.focusable);D([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),x)}}S.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>T(!1))})})}),j="RovingFocusGroupItem",I=n.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,focusable:u=!0,active:a=!1,tabStopId:i,children:s,...d}=e,f=(0,l.M)(),v=i||f,m=x(j,t),w=m.currentTabStopId===v,y=b(t),{onFocusableItemAdd:g,onFocusableItemRemove:k,currentTabStopId:R}=m;return n.useEffect(()=>{if(u)return g(),()=>k()},[u,g,k]),(0,p.jsx)(h.ItemSlot,{scope:t,id:v,focusable:u,active:a,children:(0,p.jsx)(c.WV.span,{tabIndex:w?0:-1,"data-orientation":m.orientation,...d,ref:r,onMouseDown:(0,o.M)(e.onMouseDown,e=>{u?m.onItemFocus(v):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>m.onItemFocus(v)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){m.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let r=function(e,r,t){var n;let o=(n=e.key,"rtl"!==t?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(o)))return A[o]}(e,m.orientation,m.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)o.reverse();else if("prev"===r||"next"===r){var t,n;"prev"===r&&o.reverse();let u=o.indexOf(e.currentTarget);o=m.loop?(t=o,n=u+1,t.map((e,r)=>t[(n+r)%t.length])):o.slice(u+1)}setTimeout(()=>D(o))}}),children:"function"==typeof s?s({isCurrentTabStop:w,hasTabStop:null!=R}):s})})});I.displayName=j;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=document.activeElement;for(let n of e)if(n===t||(n.focus({preventScroll:r}),document.activeElement!==t))return}var F=C,M=I},75659:function(e,r,t){t.d(r,{D:function(){return o}});var n=t(32486);function o(e){let r=n.useRef({value:e,previous:e});return n.useMemo(()=>(r.current.value!==e&&(r.current.previous=r.current.value,r.current.value=e),r.current.previous),[e])}}}]);