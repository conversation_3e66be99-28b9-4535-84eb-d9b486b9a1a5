(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6625,2305,7154,768,8396,6497],{9824:function(e,t,r){"use strict";r.d(t,{Z:function(){return d}});var n=r(32486);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:l=2,absoluteStrokeWidth:d,className:c="",children:s,iconNode:u,...f}=e;return(0,n.createElement)("svg",{ref:t,...i,width:o,height:o,stroke:r,strokeWidth:d?24*Number(l)/Number(o):l,className:a("lucide",c),...f},[...u.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(s)?s:[s]])}),d=(e,t)=>{let r=(0,n.forwardRef)((r,i)=>{let{className:d,...c}=r;return(0,n.createElement)(l,{ref:i,iconNode:t,className:a("lucide-".concat(o(e)),d),...c})});return r.displayName="".concat(e),r}},69014:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("ArrowUpRight",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]])},33599:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79624:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("SendHorizontal",[["path",{d:"m3 3 3 9-3 9 19-9Z",key:"1aobqy"}],["path",{d:"M6 12h16",key:"s4cdu5"}]])},39713:function(e,t,r){"use strict";r.d(t,{default:function(){return o.a}});var n=r(74033),o=r.n(n)},16669:function(e,t,r){"use strict";r.d(t,{default:function(){return o.a}});var n=r(6092),o=r.n(n)},74033:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return d},getImageProps:function(){return l}});let n=r(60723),o=r(25738),a=r(28863),i=n._(r(44543));function l(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let d=a.Image},6389:function(){},75737:function(e,t,r){"use strict";r.d(t,{VY:function(){return ei},h4:function(){return eo},ck:function(){return en},fC:function(){return er},xz:function(){return ea}});var n=r(32486),o=r(32192),a=r(35614),i=r(29626),l=r(20100),d=r(31413),c=r(89801),s=r(79315),u=r(53486),f=r(21971),p=r(75376),h="Collapsible",[v,m]=(0,o.b)(h),[g,b]=v(h),w=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:o,defaultOpen:a,disabled:i,onOpenChange:l,...s}=e,[u,v]=(0,d.T)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:h});return(0,p.jsx)(g,{scope:r,disabled:i,contentId:(0,f.M)(),open:u,onOpenToggle:n.useCallback(()=>v(e=>!e),[v]),children:(0,p.jsx)(c.WV.div,{"data-state":A(u),"data-disabled":i?"":void 0,...s,ref:t})})});w.displayName=h;var x="CollapsibleTrigger",y=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,...n}=e,o=b(x,r);return(0,p.jsx)(c.WV.button,{type:"button","aria-controls":o.contentId,"aria-expanded":o.open||!1,"data-state":A(o.open),"data-disabled":o.disabled?"":void 0,disabled:o.disabled,...n,ref:t,onClick:(0,l.M)(e.onClick,o.onOpenToggle)})});y.displayName=x;var k="CollapsibleContent",C=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=b(k,e.__scopeCollapsible);return(0,p.jsx)(u.z,{present:r||o.open,children:e=>{let{present:r}=e;return(0,p.jsx)(j,{...n,ref:t,present:r})}})});C.displayName=k;var j=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:o,children:a,...l}=e,d=b(k,r),[u,f]=n.useState(o),h=n.useRef(null),v=(0,i.e)(t,h),m=n.useRef(0),g=m.current,w=n.useRef(0),x=w.current,y=d.open||u,C=n.useRef(y),j=n.useRef(void 0);return n.useEffect(()=>{let e=requestAnimationFrame(()=>C.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,s.b)(()=>{let e=h.current;if(e){j.current=j.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();m.current=t.height,w.current=t.width,C.current||(e.style.transitionDuration=j.current.transitionDuration,e.style.animationName=j.current.animationName),f(o)}},[d.open,o]),(0,p.jsx)(c.WV.div,{"data-state":A(d.open),"data-disabled":d.disabled?"":void 0,id:d.contentId,hidden:!y,...l,ref:v,style:{"--radix-collapsible-content-height":g?"".concat(g,"px"):void 0,"--radix-collapsible-content-width":x?"".concat(x,"px"):void 0,...e.style},children:y&&a})});function A(e){return e?"open":"closed"}var R=r(398),N="Accordion",_=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[I,D,z]=(0,a.B)(N),[M,O]=(0,o.b)(N,[z,m]),S=m(),Z=n.forwardRef((e,t)=>{let{type:r,...n}=e;return(0,p.jsx)(I.Provider,{scope:e.__scopeAccordion,children:"multiple"===r?(0,p.jsx)(L,{...n,ref:t}):(0,p.jsx)(W,{...n,ref:t})})});Z.displayName=N;var[E,P]=M(N),[T,V]=M(N,{collapsible:!1}),W=n.forwardRef((e,t)=>{let{value:r,defaultValue:o,onValueChange:a=()=>{},collapsible:i=!1,...l}=e,[c,s]=(0,d.T)({prop:r,defaultProp:null!=o?o:"",onChange:a,caller:N});return(0,p.jsx)(E,{scope:e.__scopeAccordion,value:n.useMemo(()=>c?[c]:[],[c]),onItemOpen:s,onItemClose:n.useCallback(()=>i&&s(""),[i,s]),children:(0,p.jsx)(T,{scope:e.__scopeAccordion,collapsible:i,children:(0,p.jsx)(B,{...l,ref:t})})})}),L=n.forwardRef((e,t)=>{let{value:r,defaultValue:o,onValueChange:a=()=>{},...i}=e,[l,c]=(0,d.T)({prop:r,defaultProp:null!=o?o:[],onChange:a,caller:N}),s=n.useCallback(e=>c(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return[...t,e]}),[c]),u=n.useCallback(e=>c(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter(t=>t!==e)}),[c]);return(0,p.jsx)(E,{scope:e.__scopeAccordion,value:l,onItemOpen:s,onItemClose:u,children:(0,p.jsx)(T,{scope:e.__scopeAccordion,collapsible:!0,children:(0,p.jsx)(B,{...i,ref:t})})})}),[H,q]=M(N),B=n.forwardRef((e,t)=>{let{__scopeAccordion:r,disabled:o,dir:a,orientation:d="vertical",...s}=e,u=n.useRef(null),f=(0,i.e)(u,t),h=D(r),v="ltr"===(0,R.gm)(a),m=(0,l.M)(e.onKeyDown,e=>{var t;if(!_.includes(e.key))return;let r=e.target,n=h().filter(e=>{var t;return!(null===(t=e.ref.current)||void 0===t?void 0:t.disabled)}),o=n.findIndex(e=>e.ref.current===r),a=n.length;if(-1===o)return;e.preventDefault();let i=o,l=a-1,c=()=>{(i=o+1)>l&&(i=0)},s=()=>{(i=o-1)<0&&(i=l)};switch(e.key){case"Home":i=0;break;case"End":i=l;break;case"ArrowRight":"horizontal"===d&&(v?c():s());break;case"ArrowDown":"vertical"===d&&c();break;case"ArrowLeft":"horizontal"===d&&(v?s():c());break;case"ArrowUp":"vertical"===d&&s()}null===(t=n[i%a].ref.current)||void 0===t||t.focus()});return(0,p.jsx)(H,{scope:r,disabled:o,direction:a,orientation:d,children:(0,p.jsx)(I.Slot,{scope:r,children:(0,p.jsx)(c.WV.div,{...s,"data-orientation":d,ref:f,onKeyDown:o?void 0:m})})})}),U="AccordionItem",[$,F]=M(U),K=n.forwardRef((e,t)=>{let{__scopeAccordion:r,value:n,...o}=e,a=q(U,r),i=P(U,r),l=S(r),d=(0,f.M)(),c=n&&i.value.includes(n)||!1,s=a.disabled||e.disabled;return(0,p.jsx)($,{scope:r,open:c,disabled:s,triggerId:d,children:(0,p.jsx)(w,{"data-orientation":a.orientation,"data-state":et(c),...l,...o,ref:t,disabled:s,open:c,onOpenChange:e=>{e?i.onItemOpen(n):i.onItemClose(n)}})})});K.displayName=U;var G="AccordionHeader",Y=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,o=q(N,r),a=F(G,r);return(0,p.jsx)(c.WV.h3,{"data-orientation":o.orientation,"data-state":et(a.open),"data-disabled":a.disabled?"":void 0,...n,ref:t})});Y.displayName=G;var J="AccordionTrigger",Q=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,o=q(N,r),a=F(J,r),i=V(J,r),l=S(r);return(0,p.jsx)(I.ItemSlot,{scope:r,children:(0,p.jsx)(y,{"aria-disabled":a.open&&!i.collapsible||void 0,"data-orientation":o.orientation,id:a.triggerId,...l,...n,ref:t})})});Q.displayName=J;var X="AccordionContent",ee=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,o=q(N,r),a=F(X,r),i=S(r);return(0,p.jsx)(C,{role:"region","aria-labelledby":a.triggerId,"data-orientation":o.orientation,...i,...n,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function et(e){return e?"open":"closed"}ee.displayName=X;var er=Z,en=K,eo=Y,ea=Q,ei=ee},398:function(e,t,r){"use strict";r.d(t,{gm:function(){return a}});var n=r(32486);r(75376);var o=n.createContext(void 0);function a(e){let t=n.useContext(o);return e||t||"ltr"}},21971:function(e,t,r){"use strict";r.d(t,{M:function(){return d}});var n,o=r(32486),a=r(79315),i=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function d(e){let[t,r]=o.useState(i());return(0,a.b)(()=>{e||r(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}}}]);