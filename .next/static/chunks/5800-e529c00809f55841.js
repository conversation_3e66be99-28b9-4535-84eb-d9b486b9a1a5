"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5800],{6114:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},81356:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},5870:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},57292:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},21920:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},6793:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},94797:function(e,t,n){n.d(t,{Dx:function(){return er},VY:function(){return en},aV:function(){return et},dk:function(){return eo},fC:function(){return Q},h_:function(){return ee},x8:function(){return el},xz:function(){return $}});var r=n(32486),o=n(20100),l=n(29626),a=n(32192),i=n(21971),u=n(31413),c=n(35878),s=n(5887),d=n(79872),f=n(53486),p=n(89801),y=n(67058),g=n(25081),v=n(15623),h=n(91007),x=n(75376),m="Dialog",[k,D]=(0,a.b)(m),[j,b]=k(m),w=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:l,onOpenChange:a,modal:c=!0}=e,s=r.useRef(null),d=r.useRef(null),[f,p]=(0,u.T)({prop:o,defaultProp:null!=l&&l,onChange:a,caller:m});return(0,x.jsx)(j,{scope:t,triggerRef:s,contentRef:d,contentId:(0,i.M)(),titleId:(0,i.M)(),descriptionId:(0,i.M)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:n})};w.displayName=m;var R="DialogTrigger",C=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=b(R,n),i=(0,l.e)(t,a.triggerRef);return(0,x.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":U(a.open),...r,ref:i,onClick:(0,o.M)(e.onClick,a.onOpenToggle)})});C.displayName=R;var M="DialogPortal",[I,N]=k(M,{forceMount:void 0}),_=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:l}=e,a=b(M,t);return(0,x.jsx)(I,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,x.jsx)(f.z,{present:n||a.open,children:(0,x.jsx)(d.h,{asChild:!0,container:l,children:e})}))})};_.displayName=M;var O="DialogOverlay",Z=r.forwardRef((e,t)=>{let n=N(O,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,l=b(O,e.__scopeDialog);return l.modal?(0,x.jsx)(f.z,{present:r||l.open,children:(0,x.jsx)(F,{...o,ref:t})}):null});Z.displayName=O;var E=(0,h.Z8)("DialogOverlay.RemoveScroll"),F=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=b(O,n);return(0,x.jsx)(g.Z,{as:E,allowPinchZoom:!0,shards:[o.contentRef],children:(0,x.jsx)(p.WV.div,{"data-state":U(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),P="DialogContent",V=r.forwardRef((e,t)=>{let n=N(P,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,l=b(P,e.__scopeDialog);return(0,x.jsx)(f.z,{present:r||l.open,children:l.modal?(0,x.jsx)(W,{...o,ref:t}):(0,x.jsx)(A,{...o,ref:t})})});V.displayName=P;var W=r.forwardRef((e,t)=>{let n=b(P,e.__scopeDialog),a=r.useRef(null),i=(0,l.e)(t,n.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,v.Ry)(e)},[]),(0,x.jsx)(q,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault())})}),A=r.forwardRef((e,t)=>{let n=b(P,e.__scopeDialog),o=r.useRef(!1),l=r.useRef(!1);return(0,x.jsx)(q,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,a;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current||null===(a=n.triggerRef.current)||void 0===a||a.focus(),t.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:t=>{var r,a;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(l.current=!0));let i=t.target;(null===(a=n.triggerRef.current)||void 0===a?void 0:a.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),q=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:i,...u}=e,d=b(P,n),f=r.useRef(null),p=(0,l.e)(t,f);return(0,y.EW)(),(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(s.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:i,children:(0,x.jsx)(c.XB,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":U(d.open),...u,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(G,{titleId:d.titleId}),(0,x.jsx)(J,{contentRef:f,descriptionId:d.descriptionId})]})]})}),T="DialogTitle",z=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=b(T,n);return(0,x.jsx)(p.WV.h2,{id:o.titleId,...r,ref:t})});z.displayName=T;var S="DialogDescription",B=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=b(S,n);return(0,x.jsx)(p.WV.p,{id:o.descriptionId,...r,ref:t})});B.displayName=S;var H="DialogClose",L=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=b(H,n);return(0,x.jsx)(p.WV.button,{type:"button",...r,ref:t,onClick:(0,o.M)(e.onClick,()=>l.onOpenChange(!1))})});function U(e){return e?"open":"closed"}L.displayName=H;var K="DialogTitleWarning",[X,Y]=(0,a.k)(K,{contentName:P,titleName:T,docsSlug:"dialog"}),G=e=>{let{titleId:t}=e,n=Y(K),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},J=e=>{let{contentRef:t,descriptionId:n}=e,o=Y("DialogDescriptionWarning"),l="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(l)},[l,t,n]),null},Q=w,$=C,ee=_,et=Z,en=V,er=z,eo=B,el=L},56762:function(e,t,n){n.d(t,{f:function(){return i}});var r=n(32486),o=n(89801),l=n(75376),a=r.forwardRef((e,t)=>(0,l.jsx)(o.WV.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null===(n=e.onMouseDown)||void 0===n||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var i=a}}]);