"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9687],{22909:function(e,t,r){r.d(t,{GoogleOAuthProvider:function(){return o},Nq:function(){return i}});var n=r(32486);let u=(0,n.createContext)(null);function o(e){let{clientId:t,nonce:r,onScriptLoadSuccess:o,onScriptLoadError:i,children:l}=e,a=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{nonce:t,onScriptLoadSuccess:r,onScriptLoadError:u}=e,[o,i]=(0,n.useState)(!1),l=(0,n.useRef)(r);l.current=r;let a=(0,n.useRef)(u);return a.current=u,(0,n.useEffect)(()=>{let e=document.createElement("script");return e.src="https://accounts.google.com/gsi/client",e.async=!0,e.defer=!0,e.nonce=t,e.onload=()=>{var e;i(!0),null===(e=l.current)||void 0===e||e.call(l)},e.onerror=()=>{var e;i(!1),null===(e=a.current)||void 0===e||e.call(a)},document.body.appendChild(e),()=>{document.body.removeChild(e)}},[t]),o}({nonce:r,onScriptLoadSuccess:o,onScriptLoadError:i}),c=(0,n.useMemo)(()=>({clientId:t,scriptLoadedSuccessfully:a}),[t,a]);return n.createElement(u.Provider,{value:c},l)}function i(e){let{flow:t="implicit",scope:r="",onSuccess:o,onError:i,onNonOAuthError:l,overrideScope:a,state:c,...s}=e,{clientId:d,scriptLoadedSuccessfully:f}=function(){let e=(0,n.useContext)(u);if(!e)throw Error("Google OAuth components must be used within GoogleOAuthProvider");return e}(),v=(0,n.useRef)(),p=(0,n.useRef)(o);p.current=o;let h=(0,n.useRef)(i);h.current=i;let m=(0,n.useRef)(l);m.current=l,(0,n.useEffect)(()=>{var e,n;if(!f)return;let u="implicit"===t?"initTokenClient":"initCodeClient",o=null===(n=null===(e=null==window?void 0:window.google)||void 0===e?void 0:e.accounts)||void 0===n?void 0:n.oauth2[u]({client_id:d,scope:a?r:"openid profile email ".concat(r),callback:e=>{var t,r;if(e.error)return null===(t=h.current)||void 0===t?void 0:t.call(h,e);null===(r=p.current)||void 0===r||r.call(p,e)},error_callback:e=>{var t;null===(t=m.current)||void 0===t||t.call(m,e)},state:c,...s});v.current=o},[d,f,t,r,c]);let b=(0,n.useCallback)(e=>{var t;return null===(t=v.current)||void 0===t?void 0:t.requestAccessToken(e)},[]),g=(0,n.useCallback)(()=>{var e;return null===(e=v.current)||void 0===e?void 0:e.requestCode()},[]);return"implicit"===t?b:g}},28780:function(e,t,r){r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},39713:function(e,t,r){r.d(t,{default:function(){return u.a}});var n=r(74033),u=r.n(n)},16669:function(e,t,r){r.d(t,{default:function(){return u.a}});var n=r(6092),u=r.n(n)},47411:function(e,t,r){var n=r(13362);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},74033:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return a},getImageProps:function(){return l}});let n=r(60723),u=r(25738),o=r(28863),i=n._(r(44543));function l(e){let{props:t}=(0,u.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let a=o.Image},95833:function(e,t,r){r.d(t,{fC:function(){return x},z$:function(){return w}});var n=r(32486),u=r(29626),o=r(32192),i=r(20100),l=r(31413),a=r(75659),c=r(30915),s=r(53486),d=r(89801),f=r(75376),v="Checkbox",[p,h]=(0,o.b)(v),[m,b]=p(v);function g(e){let{__scopeCheckbox:t,checked:r,children:u,defaultChecked:o,disabled:i,form:a,name:c,onCheckedChange:s,required:d,value:p="on",internal_do_not_use_render:h}=e,[b,g]=(0,l.T)({prop:r,defaultProp:null!=o&&o,onChange:s,caller:v}),[k,y]=n.useState(null),[x,C]=n.useState(null),w=n.useRef(!1),P=!k||!!a||!!k.closest("form"),R={checked:b,disabled:i,setChecked:g,control:k,setControl:y,name:c,form:a,value:p,hasConsumerStoppedPropagationRef:w,required:d,defaultChecked:!E(o)&&o,isFormControl:P,bubbleInput:x,setBubbleInput:C};return(0,f.jsx)(m,{scope:t,...R,children:"function"==typeof h?h(R):u})}var k="CheckboxTrigger",y=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,onKeyDown:o,onClick:l,...a}=e,{control:c,value:s,disabled:v,checked:p,required:h,setControl:m,setChecked:g,hasConsumerStoppedPropagationRef:y,isFormControl:x,bubbleInput:C}=b(k,r),w=(0,u.e)(t,m),P=n.useRef(p);return n.useEffect(()=>{let e=null==c?void 0:c.form;if(e){let t=()=>g(P.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[c,g]),(0,f.jsx)(d.WV.button,{type:"button",role:"checkbox","aria-checked":E(p)?"mixed":p,"aria-required":h,"data-state":S(p),"data-disabled":v?"":void 0,disabled:v,value:s,...a,ref:w,onKeyDown:(0,i.M)(o,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.M)(l,e=>{g(e=>!!E(e)||!e),C&&x&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})})});y.displayName=k;var x=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:u,defaultChecked:o,required:i,disabled:l,value:a,onCheckedChange:c,form:s,...d}=e;return(0,f.jsx)(g,{__scopeCheckbox:r,checked:u,defaultChecked:o,disabled:l,required:i,onCheckedChange:c,name:n,form:s,value:a,internal_do_not_use_render:e=>{let{isFormControl:n}=e;return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(y,{...d,ref:t,__scopeCheckbox:r}),n&&(0,f.jsx)(R,{__scopeCheckbox:r})]})}})});x.displayName=v;var C="CheckboxIndicator",w=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...u}=e,o=b(C,r);return(0,f.jsx)(s.z,{present:n||E(o.checked)||!0===o.checked,children:(0,f.jsx)(d.WV.span,{"data-state":S(o.checked),"data-disabled":o.disabled?"":void 0,...u,ref:t,style:{pointerEvents:"none",...e.style}})})});w.displayName=C;var P="CheckboxBubbleInput",R=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,...o}=e,{control:i,hasConsumerStoppedPropagationRef:l,checked:s,defaultChecked:v,required:p,disabled:h,name:m,value:g,form:k,bubbleInput:y,setBubbleInput:x}=b(P,r),C=(0,u.e)(t,x),w=(0,a.D)(s),R=(0,c.t)(i);n.useEffect(()=>{if(!y)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!l.current;if(w!==s&&e){let r=new Event("click",{bubbles:t});y.indeterminate=E(s),e.call(y,!E(s)&&s),y.dispatchEvent(r)}},[y,w,s,l]);let S=n.useRef(!E(s)&&s);return(0,f.jsx)(d.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=v?v:S.current,required:p,disabled:h,name:m,value:g,form:k,...o,tabIndex:-1,ref:C,style:{...o.style,...R,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function E(e){return"indeterminate"===e}function S(e){return E(e)?"indeterminate":e?"checked":"unchecked"}R.displayName=P},75659:function(e,t,r){r.d(t,{D:function(){return u}});var n=r(32486);function u(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},30915:function(e,t,r){r.d(t,{t:function(){return o}});var n=r(32486),u=r(79315);function o(e){let[t,r]=n.useState(void 0);return(0,u.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,u;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,u=t.blockSize}else n=e.offsetWidth,u=e.offsetHeight;r({width:n,height:u})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}}}]);