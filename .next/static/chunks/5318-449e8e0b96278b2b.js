"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5318,9513],{45838:function(e,t,a){a.d(t,{Z:function(){return N}});var s=a(75376),r=a(32486),l=a(10676),n=e=>{let{file:t}=e;return(0,s.jsx)("div",{className:"border rounded-lg shadow-sm bg-white min-h-[200px] w-[200px] overflow-hidden",children:(0,s.jsxs)("a",{href:t.file_link,target:"_blank",rel:"noopener noreferrer",children:[(0,s.jsx)("div",{className:"flex items-center justify-between p-3 px-2 border-b bg-gray-50",children:(0,s.jsxs)("div",{className:"flex items-center gap-2 w-full min-w-0",children:[(0,s.jsx)(l.Z,{size:20,className:"text-red-500 flex-shrink-0"}),(0,s.jsx)("div",{className:"text-xs font-medium text-gray-900 truncate min-w-0",children:t.file_name})]})}),(0,s.jsx)("div",{className:"flex justify-center items-center mt-10",children:(0,s.jsx)(l.Z,{size:50,className:"text-red-500"})})]})})},i=a(36727),o=a(19154),d=a(27199),c=a(13259),u=a.n(c);a(3694),a(27973),a(6845),a(69588),a(26833);var m=a(22397),x=a(20384),h=a(59503),A=a(18208),p=a(72738),g=a.n(p),v=a(39713),f=a(56792),b=e=>{let{onClose:t,item:a,image:l}=e,[n,i]=(0,r.useState)(1),[o,d]=(0,r.useState)(0);return(0,r.useEffect)(()=>{let e=e=>{"Escape"===e.key&&t()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[t]),(0,s.jsxs)("div",{className:"fixed inset-0 z-50 bg-black bg-opacity-90 flex flex-col justify-between p-4",children:[(0,s.jsxs)("div",{className:"relative flex justify-between items-start text-white",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(v.default,{src:(null==l?void 0:l.file_link)||(null===f.Z||void 0===f.Z?void 0:f.Z.user),width:80,height:80,alt:"Image thumbnail",unoptimized:!0,className:"h-10 w-10 rounded border object-cover"}),(0,s.jsxs)("div",{className:"overflow-hidden",children:[(0,s.jsx)("p",{className:"font-medium",children:null==a?void 0:a.username}),(0,s.jsxs)("p",{className:"flex flex-wrap text-sm text-gray-300",children:[g()(a.created_at).startOf("minute").fromNow()," in #",null==a?void 0:a.channel_name," – ",null==l?void 0:l.file_name]})]})]}),(0,s.jsx)("button",{onClick:t,className:"absolute right-5 sm:top-5 text-white hover:text-gray-300",children:(0,s.jsx)(m.Z,{size:24})})]}),(0,s.jsx)("div",{className:"flex-grow flex items-center justify-center overflow-hidden",children:(0,s.jsx)("img",{src:null==l?void 0:l.file_link,alt:(null==l?void 0:l.file_name)||"full image",style:{transform:"scale(".concat(n,") rotate(").concat(o,"deg)"),transition:"transform 0.1s ease-out"},className:"max-h-[90vh] max-w-full object-contain"})}),(0,s.jsx)("div",{className:"flex justify-between items-center text-white",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("button",{onClick:()=>{i(1),d(0)},className:"px-2 py-1 border rounded hover:bg-white/10",title:"Reset Zoom & Rotation",children:(0,s.jsx)(x.Z,{size:18})}),(0,s.jsx)("button",{onClick:()=>{i(e=>Math.max(e-.25,.5))},className:"px-2 py-1 border rounded hover:bg-white/10",title:"Zoom Out",children:(0,s.jsx)(h.Z,{size:18})}),(0,s.jsx)("div",{className:"w-24 h-1 bg-gray-600 rounded-full relative overflow-hidden",children:(0,s.jsx)("div",{className:"absolute h-full bg-white rounded-full",style:{width:"".concat((n-.5)/2.5*100,"%"),left:"0%"}})}),(0,s.jsx)("button",{onClick:()=>{i(e=>Math.min(e+.25,3))},className:"px-2 py-1 border rounded hover:bg-white/10",title:"Zoom In",children:(0,s.jsx)(A.Z,{size:18})})]})})]})},y=a(811),w=a(60353);let j=e=>{let{mediaItem:t,setIsOpen:a,setImage:l}=e,[n,i]=(0,r.useState)(!1),o=async e=>{e.stopPropagation();try{let e=await fetch(t.file_link),a=await e.blob(),s=window.URL.createObjectURL(a),r=document.createElement("a");r.href=s,r.download=t.file_name,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(s)}catch(e){console.error("Download failed:",e)}};return(0,s.jsxs)("div",{className:"relative rounded-md overflow-hidden w-full md:w-[350px] h-[300px]",onMouseEnter:()=>i(!0),onMouseLeave:()=>i(!1),children:[(0,s.jsx)("img",{src:t.file_link,alt:t.file_name,className:"w-full md:w-[350px] h-[300px] rounded-md object-cover border cursor-pointer",onClick:()=>{a(!0),l(t)}}),n&&(0,s.jsxs)("a",{href:"#",onClick:o,className:"flex items-center gap-1 absolute top-2 right-2 bg-white py-1 px-2 rounded-lg cursor-pointer z-10",title:"Download Image",children:[(0,s.jsx)("div",{className:"hover:bg-gray-200 rounded-md p-1",children:(0,s.jsx)(y.Z,{size:18})}),(0,s.jsx)("div",{className:"hover:bg-gray-200 rounded-md p-1",children:(0,s.jsx)(w.Z,{size:18})})]})]})};var N=e=>{var t;let{item:a}=e,[l,c]=(0,r.useState)(!1),[m,x]=(0,r.useState)(null),h=a.message.replace(/\n{2,}/g,"\n\n").replace(/^\n+|\n+$/g,"");return(0,r.useEffect)(()=>{void 0!==u()&&u().highlightAll()},[h]),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{style:{whiteSpace:"pre-line",wordBreak:"break-word",overflowWrap:"break-word"},className:"text-[#344054] text-[13px] lg:text-[15px] font-[400] whitespace-pre-wrap break-words custom-message",children:(0,s.jsx)(i.UG,{remarkPlugins:[o.Z],rehypePlugins:[d.Z],components:{a:e=>{let{...t}=e;return(0,s.jsx)("a",{...t,style:{textDecoration:"underline",color:"blue"},target:"_blank",rel:"noopener noreferrer"})},table:e=>(0,s.jsx)("table",{style:{width:"100%"},...e}),th:e=>(0,s.jsx)("th",{style:{border:"1px solid #ccc",padding:"8px"},...e}),td:e=>(0,s.jsx)("td",{style:{border:"1px solid #ccc",padding:"8px"},...e}),p:e=>(0,s.jsx)("p",{style:{color:(null==a?void 0:a.type)==="system"?"#aaa":""},...e}),code:e=>{let{className:t,children:a,...r}=e,l=/language-(\w+)/.exec(t||""),n={className:t,...r,style:{whiteSpace:"pre-wrap",wordBreak:"break-word",overflowX:"hidden"}},i={className:t,...r,style:{wordBreak:"break-word",whiteSpace:"pre-wrap"}};return l?(0,s.jsx)("pre",{...n,children:(0,s.jsx)("code",{...i,children:String(a).replace(/\n$/,"")})}):(0,s.jsx)("code",{className:t,...r,children:a})}},children:h})}),a.media&&a.media.length>0&&(0,s.jsx)("div",{className:"mt-2 flex items-start flex-wrap gap-4",children:null==a?void 0:null===(t=a.media)||void 0===t?void 0:t.map(e=>e.mime_type.includes("application")?(0,s.jsx)(n,{file:e},e.id):(0,s.jsx)(j,{mediaItem:e,setIsOpen:c,setImage:x},e.id))}),l&&(0,s.jsx)(b,{item:a,image:m,onClose:()=>c(!1)})]})}},35486:function(e,t,a){a.d(t,{Z:function(){return J}});var s=a(75376),r=a(32486),l=a(6769),n=a(94912),i=a(35575),o=a(95578),d=a(47720),c=a(45838),u=a(97220),m=a(97712),x=a(47411),h=a(39713),A=a(17695),p=a(56792);function g(e){let{users:t,totalReplies:a,lastReplyTime:r,handleReply:l}=e,n=null==t?void 0:t.slice(0,4),i=(null==t?void 0:t.length)-(null==n?void 0:n.length);return(0,s.jsxs)("div",{className:"flex items-center space-x-1 text-sm text-gray-600 mt-2",children:[(0,s.jsxs)("div",{className:"flex space-x-[2px]",children:[null==n?void 0:n.map((e,t)=>(0,s.jsx)("div",{className:"w-6 h-6 size-5 rounded-md border-2 border-white overflow-hidden",children:(0,s.jsx)(h.default,{src:e.avatar_url||(null===p.Z||void 0===p.Z?void 0:p.Z.user),alt:"User ".concat(e.id),width:24,height:24,className:"size-5 object-cover"})},t)),i>0&&(0,s.jsxs)("div",{className:"w-6 h-6 rounded-full bg-gray-300 border-2 border-white text-xs flex items-center justify-center text-gray-700 font-medium",children:["+",i]})]}),(null==n?void 0:n.length)>0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("span",{onClick:l,className:"text-blue-600 hover:underline cursor-pointer ml-2",children:[a," replies"]}),(0,s.jsxs)("span",{className:"text-gray-500 ml-1",children:["Last reply ",(0,A.WU)(r)]})]})]})}var v=a(74178),f=a(99402),b=a(89863),y=a(43806),w=a(5426),j=a(48324),N=a(2336),C=a(81356),k=a(37847),E=a(23633),S=a(82301),Z=a(21920),_=a(64632),R=a(16006),z=a(11492),L=a(22397),M=a(72738),B=a.n(M),U=a(86418),D=a(73003),T=e=>{let{open:t,setOpen:a}=e,{state:l}=(0,r.useContext)(u.R),{thread:n}=l,i=(0,x.useParams)().id,[o,d]=(0,r.useState)(!1),m=async()=>{d(!0);let e=await (0,U.jx)("/threads/".concat(null==n?void 0:n.thread_id,"/channels/").concat(i));((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201)&&a(!1),d(!1)};return(0,s.jsx)(R.Vq,{open:t,onOpenChange:a,children:(0,s.jsxs)(R.cZ,{className:"sm:max-w-[550px] rounded-lg p-0 overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-b border-gray-200",children:[(0,s.jsx)(R.$N,{className:"text-lg font-semibold",children:"Delete message"}),(0,s.jsx)("button",{onClick:()=>a(!1),className:"text-gray-500 hover:text-gray-700 transition","aria-label":"Close",children:(0,s.jsx)(L.Z,{className:"w-5 h-5"})})]}),(0,s.jsx)("div",{className:"px-6 pt-4 text-sm text-gray-700",children:"Are you sure you want to delete this message? This cannot be undone."}),(0,s.jsx)("div",{className:"px-6 py-4 max-h-[400px] overflow-auto",children:(0,s.jsxs)("div",{className:"border border-gray-200 rounded p-3 flex gap-3 items-start bg-white",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded flex items-center justify-center text-white text-sm font-bold",children:(0,s.jsx)(h.default,{src:(null==n?void 0:n.avatar_url)?null==n?void 0:n.avatar_url:(null==n?void 0:n.user_type)=="user"||(null==n?void 0:n.user_type)===""?null===p.Z||void 0===p.Z?void 0:p.Z.user:null===p.Z||void 0===p.Z?void 0:p.Z.bot,alt:"avatar",width:32,height:32,className:"rounded"})}),(0,s.jsxs)("div",{className:"flex flex-col gap-[2px] text-sm w-full",children:[(0,s.jsxs)("div",{className:"flex gap-2 items-baseline",children:[(0,s.jsx)("span",{className:"font-bold text-gray-800",children:null==n?void 0:n.username}),(0,s.jsx)("span",{className:"text-xs text-gray-500",children:B()(null==n?void 0:n.created_at).calendar()})]}),(0,s.jsx)(c.Z,{item:n})]})]})}),(0,s.jsxs)(R.cN,{className:"px-6 pb-4 pt-2",children:[(0,s.jsx)(z.z,{variant:"outline",className:"rounded-md text-sm px-4 py-2 border border-gray-300",onClick:()=>a(!1),children:"Cancel"}),(0,s.jsxs)(z.z,{className:"bg-rose-600 hover:bg-rose-700 text-white text-sm px-4 py-2 rounded-md shadow-sm",onClick:m,children:["Delete ",o&&(0,s.jsx)(D.Z,{})]})]})]})})},V=e=>{let{open:t,setOpen:a}=e,{state:l}=(0,r.useContext)(u.R),{thread:n}=l,i=(0,x.useParams)().id,[o,d]=(0,r.useState)(!1),m=async()=>{d(!0),await (0,U.i1)("/channels/pin/".concat(i,"/thread/").concat(null==n?void 0:n.thread_id)),a(!1),d(!1)};return(0,s.jsx)(R.Vq,{open:t,onOpenChange:a,children:(0,s.jsxs)(R.cZ,{className:"sm:max-w-[550px] rounded-lg p-0 overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-b border-gray-200",children:[(0,s.jsx)(R.$N,{className:"text-lg font-semibold",children:"Remove pinned item"}),(0,s.jsx)("button",{onClick:()=>a(!1),className:"text-gray-500 hover:text-gray-700 transition","aria-label":"Close",children:(0,s.jsx)(L.Z,{className:"w-5 h-5"})})]}),(0,s.jsx)("div",{className:"px-6 pt-4 text-sm text-gray-700",children:"Are you sure you want to remove this pinned item."}),(0,s.jsx)("div",{className:"px-6 py-4 max-h-[400px] overflow-auto",children:(0,s.jsxs)("div",{className:"border border-gray-200 rounded p-3 flex gap-3 items-start bg-white",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded flex items-center justify-center text-white text-sm font-bold",children:(0,s.jsx)(h.default,{src:(null==n?void 0:n.avatar_url)?null==n?void 0:n.avatar_url:(null==n?void 0:n.user_type)=="user"||(null==n?void 0:n.user_type)===""?null===p.Z||void 0===p.Z?void 0:p.Z.user:null===p.Z||void 0===p.Z?void 0:p.Z.bot,alt:"avatar",width:32,height:32,className:"rounded"})}),(0,s.jsxs)("div",{className:"flex flex-col gap-[2px] text-sm w-full",children:[(0,s.jsxs)("div",{className:"flex gap-2 items-baseline",children:[(0,s.jsx)("span",{className:"font-bold text-gray-800",children:null==n?void 0:n.username}),(0,s.jsx)("span",{className:"text-xs text-gray-500",children:B()(null==n?void 0:n.created_at).calendar()})]}),(0,s.jsx)(c.Z,{item:n})]})]})}),(0,s.jsxs)(R.cN,{className:"px-6 pb-4 pt-2",children:[(0,s.jsx)(z.z,{variant:"outline",className:"rounded-md text-sm px-4 py-2 border border-gray-300",onClick:()=>a(!1),children:"Cancel"}),(0,s.jsxs)(z.z,{className:"bg-rose-600 hover:bg-rose-700 text-white text-sm px-4 py-2 rounded-md shadow-sm",onClick:m,children:["Remove pinned item ",o&&(0,s.jsx)(D.Z,{})]})]})]})})};let I=e=>{let{item:t}=e,[a,l]=(0,r.useState)(!1),[n,i]=(0,r.useState)(!1),{state:o,dispatch:c}=(0,r.useContext)(u.R),{user:h}=o,A=(0,x.useParams)().id,[p,g]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=document.body;return a?e.classList.add("overflow-hidden"):e.classList.remove("overflow-hidden"),()=>{e.classList.remove("overflow-hidden")}},[a]);let f=async()=>{l(!1);let e={thread_id:t.thread_id};await (0,U.Q_)("/channels/pin/".concat(A,"/thread"),e)};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(v.J2,{open:a,onOpenChange:l,children:[(0,s.jsx)(v.xo,{asChild:!0,children:(0,s.jsx)("button",{onClick:e=>e.stopPropagation(),className:"py-[7px] px-[10px] hover:bg-gray-200 rounded","aria-label":"More options",children:(0,s.jsx)(w.Z,{size:18,className:"text-[#667085]"})})}),(0,s.jsx)(v.yk,{className:"max-width-[350px] p-0 bg-white border border-gray-200 rounded-md shadow-lg",align:"end",children:(0,s.jsxs)(y.E.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},transition:{duration:.1},className:"popover-content",children:[(0,s.jsx)("div",{className:"group flex items-center justify-between px-4 py-1 my-3 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(j.Z,{size:16}),(0,s.jsx)("span",{children:"Turn off notifications for replies"})]})}),(0,s.jsx)("hr",{}),(0,s.jsxs)("div",{className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(d.Z,{size:16}),(0,s.jsx)("span",{children:"Mark unread"})]}),(0,s.jsx)("span",{className:"ml-4 text-xs text-gray-400 group-hover:text-white",children:"U"})]}),(0,s.jsx)("hr",{}),(0,s.jsxs)("div",{className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(N.Z,{size:16}),(0,s.jsx)("span",{children:"Remind me about this"})]}),(0,s.jsx)("span",{className:"ml-4 text-xs text-gray-400 group-hover:text-white",children:">"})]}),(0,s.jsx)("hr",{}),(0,s.jsxs)("div",{className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(C.Z,{size:16}),(0,s.jsx)("span",{children:"Copy link"})]}),(0,s.jsx)("span",{className:"ml-4 text-xs text-gray-400 group-hover:text-white",children:"L"})]}),(0,s.jsx)("hr",{}),t.is_pinned?(0,s.jsxs)("div",{onClick:e=>{e.stopPropagation(),c({type:m.a.THREAD,payload:t}),g(!0),l(!1)},className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(k.Z,{size:16}),(0,s.jsx)("span",{children:"Un-pin from channel"})]}),(0,s.jsx)("span",{className:"ml-4 text-xs text-gray-400 group-hover:text-white",children:"P"})]}):(0,s.jsxs)("div",{onClick:f,className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(k.Z,{size:16}),(0,s.jsx)("span",{children:"Pin to channel"})]}),(0,s.jsx)("span",{className:"ml-4 text-xs text-gray-400 group-hover:text-white",children:"P"})]}),(0,s.jsx)("hr",{}),(0,s.jsx)("div",{className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(E.Z,{size:16}),(0,s.jsx)("span",{children:"Start a huddle in thread..."})]})}),(0,s.jsx)("hr",{}),(null==h?void 0:h.user_id)===(null==t?void 0:t.user_id)&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{onClick:()=>{c({type:m.a.THREAD,payload:t}),c({type:m.a.IS_EDIT,payload:!0})},className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(S.Z,{size:16}),(0,s.jsx)("span",{children:"Edit message"})]}),(0,s.jsx)("span",{className:"ml-4 text-xs text-gray-400 group-hover:text-white",children:"E"})]}),(0,s.jsx)("hr",{}),(0,s.jsxs)("div",{onClick:()=>{c({type:m.a.THREAD,payload:t}),i(!0)},className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-red-500 hover:bg-red-500 hover:text-white cursor-pointer",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(Z.Z,{size:16}),(0,s.jsx)("span",{children:"Delete message..."})]}),(0,s.jsx)("span",{className:"ml-4 text-xs group-hover:text-white",children:"delete"})]}),(0,s.jsx)("hr",{})]}),(0,s.jsx)("div",{className:"group flex items-center justify-between px-4 py-1 my-2 text-sm text-gray-700 hover:bg-blue-500 hover:text-white cursor-pointer",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(_.Z,{size:16}),(0,s.jsx)("span",{children:"More message shortcuts..."})]})})]})})]}),a&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black/1 z-50",onClick:()=>l(!1),"aria-hidden":"true"}),(0,s.jsx)(T,{open:n,setOpen:i}),(0,s.jsx)(V,{open:p,setOpen:g})]})};var H=e=>{let{item:t}=e;return(0,s.jsx)(I,{item:t})},P=a(80500),O=a(90937);function F(e){var t;let{item:a,handleOpen:l}=e,[n,i]=(0,r.useState)(!1),o=(0,r.useRef)(null),d=(0,r.useRef)(null),{state:c,dispatch:A}=(0,r.useContext)(u.R),{user:g}=c,f=(0,x.useRouter)(),b=()=>{d.current&&clearTimeout(d.current),o.current=setTimeout(()=>{i(!0)},300)},y=()=>{o.current&&clearTimeout(o.current),d.current=setTimeout(()=>{i(!1)},200)},w=async()=>{localStorage.setItem("channelName",null==a?void 0:a.username);let e=localStorage.getItem("orgId")||"",t={chat_type:"user",participant_id:null==a?void 0:a.user_id},s=await (0,U.xo)("/organisations/".concat(e,"/dms"),t);if((null==s?void 0:s.status)===200||(null==s?void 0:s.status)===201){var r,l,n,i;f.push("/client/home/<USER>/".concat(null==s?void 0:null===(l=s.data)||void 0===l?void 0:null===(r=l.data)||void 0===r?void 0:r.channel_id,"/").concat(null==s?void 0:null===(i=s.data)||void 0===i?void 0:null===(n=i.data)||void 0===n?void 0:n.participant_id,"/dm"))}};return(0,s.jsxs)(P.fC,{open:n,onOpenChange:i,children:[(0,s.jsx)(v.xo,{asChild:!0,children:(0,s.jsx)("div",{onMouseEnter:b,onMouseLeave:y,className:"hidden lg:flex cursor-pointer size-9 mb-2 overflow-hidden",onClick:l,children:(0,s.jsx)(h.default,{src:(null==a?void 0:a.avatar_url)?null==a?void 0:a.avatar_url:(null==a?void 0:a.user_type)=="user"||(null==a?void 0:a.user_type)===""?null===p.Z||void 0===p.Z?void 0:p.Z.user:null===p.Z||void 0===p.Z?void 0:p.Z.bot,alt:"avatar",width:40,height:40,className:"rounded-[7px] border size-9 object-cover"})})}),(0,s.jsxs)(v.yk,{onMouseEnter:b,onMouseLeave:y,sideOffset:8,side:"top",className:"z-50 w-auto rounded-md border border-gray-200 bg-white shadow-lg p-4",align:"start",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(h.default,{src:(null==a?void 0:a.avatar_url)?null==a?void 0:a.avatar_url:(null==a?void 0:a.user_type)=="user"||(null==a?void 0:a.user_type)===""?null===p.Z||void 0===p.Z?void 0:p.Z.user:null===p.Z||void 0===p.Z?void 0:p.Z.bot,alt:"avatar",width:80,height:80,className:"rounded-[7px] border size-20 object-cover"}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"font-semibold text-[15px] text-black",children:[(null==a?void 0:null===(t=a.full_name)||void 0===t?void 0:t.trim())||(null==a?void 0:a.username)," ",(null==a?void 0:a.user_id)===(null==g?void 0:g.user_id)&&(0,s.jsx)("span",{className:"text-gray-500",children:"(you)"})]}),(null==a?void 0:a.user_id)===(null==g?void 0:g.user_id)?(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-0.5",children:g.title}):(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-0.5",children:a.email})]})]}),(0,s.jsxs)("div",{className:"flex items-center text-[15px] text-gray-600 mt-4",children:[(0,s.jsx)(N.Z,{className:"w-4 h-4 mr-2"}),B()(null==a?void 0:a.created_at).format("LT")," local time"]}),(null==a?void 0:a.user_id)===(null==g?void 0:g.user_id)?(0,s.jsx)("button",{onClick:()=>A({type:m.a.STATUS,payload:!0}),className:"mt-3 w-full border text-sm font-medium border-gray-300 rounded-md py-1.5 hover:bg-gray-50 transition",children:"Set a status"}):(0,s.jsxs)("button",{onClick:w,className:"flex items-center justify-center gap-1 mt-3 w-full border text-sm font-medium border-gray-300 rounded-md py-1.5 hover:bg-gray-50 transition",children:[(0,s.jsx)(O.mT,{}),"Message"]})]})]})}var W=a(46962),Y=a(82288),J=e=>{var t,a;let{item:A,shouldShowAvatar:y,setPopupId:w,popupId:j}=e,{state:N,dispatch:C}=(0,r.useContext)(u.R),{bookmarks:k,user:E}=N,S=(0,x.usePathname)(),[Z,_]=(0,r.useState)(!1),[R,z]=(0,r.useState)(!1),L=(0,x.useParams)().id,M=null==k?void 0:k.some(e=>e.thread_id===A.thread_id),[B,D]=(0,r.useState)([]),T=()=>{C({type:m.a.THREAD,payload:A}),C({type:m.a.REPLY,payload:!0})},V=async e=>{_(!1);let t={thread_id:null==A?void 0:A.thread_id,type:"thread",reaction:e.native};await (0,U.xo)("/reactions/".concat(L),t),_(!1),z(!1)},I=async e=>{_(!1);let t={thread_id:null==A?void 0:A.thread_id,type:"thread",reaction:e};await (0,U.xo)("/reactions/".concat(L),t)},P=()=>{C({type:m.a.USER_DATA,payload:A}),C({type:m.a.HOVER_PROFILE,payload:!0})},O=async()=>{let e=[...k,{id:A.id,thread_id:A.thread_id}];C({type:m.a.BOOKMARKS,payload:e});let t=localStorage.getItem("orgId")||"",a={channels_id:null==A?void 0:A.channels_id,thread_id:A.thread_id,type:"message"};await (0,U.Q_)("/organisations/".concat(t,"/thread/save"),a)},J=async()=>{let e=k.filter(e=>e.thread_id!==A.thread_id);C({type:m.a.BOOKMARKS,payload:e});let t=localStorage.getItem("orgId")||"";await (0,U.i1)("/organisations/".concat(t,"/saved/message/").concat(A.thread_id))},q=async e=>{let t=await (0,U.Gl)("/reactions/".concat(e,"/thread/").concat(null==A?void 0:A.thread_id));if((null==t?void 0:t.status)===200||(null==t?void 0:t.status)===201){var a,s;D(null==t?void 0:null===(s=t.data)||void 0===s?void 0:null===(a=s.data)||void 0===a?void 0:a.usernames)}};return(0,s.jsxs)("div",{className:"relative group py-1 transition-colors flex items-start px-5 gap-2\n      ",children:[(0,s.jsx)("div",{className:"w-flex items-center justify-center",children:y?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(F,{item:A,handleOpen:P}),(0,s.jsx)("div",{className:"flex lg:hidden cursor-pointer size-9 mb-2 overflow-hidden",onClick:P,children:(0,s.jsx)(h.default,{src:(null==A?void 0:A.avatar_url)?null==A?void 0:A.avatar_url:(null==A?void 0:A.user_type)=="user"||(null==A?void 0:A.user_type)===""?null===p.Z||void 0===p.Z?void 0:p.Z.user:null===p.Z||void 0===p.Z?void 0:p.Z.bot,alt:"avatar",width:40,height:40,className:"rounded-[7px] border size-9 object-cover"})})]}):(0,s.jsx)("span",{className:"block text-xs w-[36px] text-[#98A2B3] mt-1 opacity-0 group-hover:opacity-100 transition-opacity",children:new Date(null==A?void 0:A.created_at).toLocaleTimeString([],{hour:"numeric",minute:"2-digit",hour12:!0}).replace(/\s?(am|pm)/i,"")})}),(0,s.jsxs)("div",{children:[y&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"hover font-bold text-[15px] text-[#1D2939] cursor-pointer",onClick:P,children:(null==A?void 0:A.username)||(null==A?void 0:A.email)}),(0,s.jsx)("span",{className:"text-xs text-[#98A2B3] mt-[1px]",children:new Date(null==A?void 0:A.created_at).toLocaleTimeString([],{hour:"numeric",minute:"2-digit",hour12:!0}).replace(/\s?(am|pm)/i,"")})]}),(0,s.jsx)("div",{className:"relative flex items-start justify-between",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(c.Z,{item:A}),(0,s.jsx)("div",{className:"text-[9px] text-neutral-500 mt-[2px]",children:(null==A?void 0:A.edited)?"(edited)":""})]})}),(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-2 rounded-md mt-1",children:[null==A?void 0:null===(t=A.reactions)||void 0===t?void 0:t.map((e,t)=>{let a=null==E?void 0:E.username,r=(B||[]).map(e=>e===a?"you":e),l="";if(0===r.length)l=" ";else if(1===r.length)l=r[0];else{let e=r[r.length-1],t=r.slice(0,-1).join(", ");l="".concat(t," and ").concat(e)}return(0,s.jsx)(W.pn,{children:(0,s.jsxs)(W.u,{children:[(0,s.jsx)(W.aJ,{asChild:!0,children:(0,s.jsxs)("div",{onMouseEnter:()=>q(e.reaction_id),onClick:t=>{I(e.reaction),t.stopPropagation()},className:"bg-primary-50 text-[13px] cursor-pointer text-blue-100 border border-blue-400 flex items-center justify-center h-[27px] py-1 px-3 rounded-2xl",children:[null==e?void 0:e.reaction," ",null==e?void 0:e.reaction_count]})}),(0,s.jsxs)(W._v,{className:"bg-black text-white p-2 rounded-md text-sm",children:[(0,s.jsx)(Y.Ce,{className:"fill-black"}),(0,s.jsx)("div",{className:"text-5xl mx-auto text-center bg-white rounded-lg flex items-center justify-center p-2 w-[70px] mb-2",children:e.reaction}),l&&(0,s.jsxs)("span",{children:[l," reacted with ",null==e?void 0:e.reaction]})]})]})},t)}),(0,s.jsxs)(v.J2,{open:R,onOpenChange:z,children:[(0,s.jsx)(W.pn,{delayDuration:0,children:(0,s.jsxs)(W.u,{children:[(0,s.jsx)(W.aJ,{asChild:!0,children:(0,s.jsx)(v.xo,{asChild:!0,children:(null==A?void 0:null===(a=A.reactions)||void 0===a?void 0:a.length)>0&&(0,s.jsx)("div",{className:"bg-primary-50 text-[13px] cursor-pointer text-blue-100 h-[27px] flex items-center justify-center py-1 px-3 rounded-full border hover:border-blue-400",onClick:e=>e.stopPropagation(),children:(0,s.jsx)(l.Z,{size:16})})})}),(0,s.jsxs)(W._v,{className:"bg-black text-white p-2 rounded-md text-sm",children:[(0,s.jsx)(Y.Ce,{className:"fill-black"}),(0,s.jsx)("span",{children:"Add reaction..."})]})]})}),(0,s.jsx)(v.yk,{className:"p-0 w-full max-w-xs",align:"end",children:(0,s.jsx)(f.Z,{data:b,onEmojiSelect:V})})]})]}),(null==A?void 0:A.message_count)>0&&(0,s.jsx)(g,{users:null==A?void 0:A.messages,totalReplies:null==A?void 0:A.message_count,lastReplyTime:A.last_reply||(null==A?void 0:A.created_at),handleReply:T}),(null==A?void 0:A.type)!=="system"&&(0,s.jsxs)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity hidden lg:flex items-center ml-4 absolute right-5 -top-6 z-10 bg-white shadow-md rounded-[8px] border border-[#E6EAEF] p-[2px]",children:[!(null==S?void 0:S.includes("/agents"))&&(0,s.jsxs)(v.J2,{open:Z,onOpenChange:_,children:[(0,s.jsx)(v.xo,{asChild:!0,children:(0,s.jsx)("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,s.jsx)(l.Z,{size:18,className:"text-[#667085]"})})}),(0,s.jsx)(v.yk,{className:"p-0 w-full max-w-xs",align:"end",children:(0,s.jsx)(f.Z,{data:b,onEmojiSelect:V})})]}),!(null==S?void 0:S.includes("/agents"))&&(0,s.jsx)("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",onClick:T,children:(0,s.jsx)(n.Z,{size:18,className:"text-[#667085]"})}),(0,s.jsx)("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,s.jsx)(i.Z,{size:18,className:"text-[#667085]"})}),M?(0,s.jsx)("button",{onClick:J,className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,s.jsx)(o.Z,{size:18,className:"text-primary-500"})}):(0,s.jsx)("button",{onClick:O,className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,s.jsx)(d.Z,{size:18,className:"text-[#667085]"})}),(0,s.jsx)(H,{item:A})]}),(null==A?void 0:A.type)!=="system"&&j===A.thread_id&&(0,s.jsxs)("div",{onClick:()=>{w(e=>e===A.thread_id?null:A.thread_id)},className:"flex lg:hidden items-center ml-4 absolute right-5 -top-6 z-10 bg-white shadow-md rounded-[8px] border border-[#E6EAEF] p-[2px]",children:[!(null==S?void 0:S.includes("/agents"))&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(v.J2,{open:Z,onOpenChange:_,children:[(0,s.jsx)(v.xo,{asChild:!0,children:(0,s.jsx)("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,s.jsx)(l.Z,{size:18,className:"text-[#667085]"})})}),(0,s.jsx)(v.yk,{className:"p-0 w-full max-w-xs",align:"end",children:(0,s.jsx)(f.Z,{data:b,onEmojiSelect:V})})]}),(0,s.jsx)("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",onClick:T,children:(0,s.jsx)(n.Z,{size:18,className:"text-[#667085]"})})]}),(0,s.jsx)("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,s.jsx)(i.Z,{size:18,className:"text-[#667085]"})}),M?(0,s.jsx)("button",{onClick:J,className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,s.jsx)(o.Z,{size:18,className:"text-primary-500"})}):(0,s.jsx)("button",{onClick:O,className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,s.jsx)(d.Z,{size:18,className:"text-[#667085]"})}),(0,s.jsx)(H,{item:A})]})]})]})}},40708:function(e,t,a){var s=a(32486),r=a(65470),l=a(63141),n=a(47233),i=a(91296),o=a(35844),d=a(8051),c=a(47641),u=a(97220),m=a(97712);t.Z=e=>{let{state:t,dispatch:a}=(0,s.useContext)(u.R),x=localStorage.getItem("channelName")||"",[h,A]=(0,s.useState)(!0);return{editor:(0,r.jE)({extensions:[l.Z.configure({bulletList:{HTMLAttributes:{class:"list-disc pl-5"}},orderedList:{HTMLAttributes:{class:"list-decimal pl-5"}},listItem:{HTMLAttributes:{class:"tracking-wide"}},paragraph:{HTMLAttributes:{class:"tracking-wide"}}}),n.Z.configure({placeholder:"Message #".concat(x)}),i.Z.configure({HTMLAttributes:{class:"mention",style:"color: blue; font-weight: normal; background:#F1F1FE; padding-left:2px; padding-right:2px"},suggestion:{items:e=>{var a,s;let r=String(e||"").toLowerCase(),l=null!==(s=null==t?void 0:null===(a=t.orgMembers)||void 0===a?void 0:a.filter(e=>((null==e?void 0:e.name)&&(null==e?void 0:e.name)!==" "?null==e?void 0:e.name:null==e?void 0:e.email).toLowerCase().includes(r)))&&void 0!==s?s:[];return"@channel".includes(r)?[{id:"channel",name:"@channel",profile_url:"/images/megaphone.png",full_name:"Notify everyone in this channel",is_online:!1},...l]:l},render:()=>{let e=null,s=-1,r=t=>{if(!e)return;let a=Array.from(e.querySelectorAll("button"));if(0!==a.length){if("ArrowDown"===t.key){if(t.preventDefault(),t.stopPropagation(),-1!==s&&a[s]){a[s].classList.remove("hover");let e=a[s].querySelector(".name-span"),t=a[s].querySelector(".secondary-span");e&&e.classList.remove("text-white"),t&&t.classList.remove("text-white")}s=(s+1)%a.length;let e=a[s];if(e){e.classList.add("hover");let t=e.querySelector(".name-span"),a=e.querySelector(".secondary-span");t&&t.classList.add("text-white"),a&&a.classList.add("text-white"),e.scrollIntoView({block:"nearest"})}}else if("ArrowUp"===t.key){if(t.preventDefault(),t.stopPropagation(),-1!==s&&a[s]){a[s].classList.remove("hover");let e=a[s].querySelector(".name-span"),t=a[s].querySelector(".secondary-span");e&&e.classList.remove("text-white"),t&&t.classList.remove("text-white")}s=(s-1+a.length)%a.length;let e=a[s];if(e){e.classList.add("hover");let t=e.querySelector(".name-span"),a=e.querySelector(".secondary-span");t&&t.classList.add("text-white"),a&&a.classList.add("text-white"),e.scrollIntoView({block:"nearest"})}}else if("Enter"===t.key){let s=e.querySelector("button.hover");if(s){t.preventDefault(),t.stopPropagation(),s.click();return}if(1===a.length){t.preventDefault(),t.stopPropagation(),a[0].click();return}}else"Escape"===t.key&&(e.remove(),e=null,s=-1,t.preventDefault(),t.stopPropagation())}},l=t=>{e&&!e.contains(t.target)&&(e.remove(),e=null,s=-1)};return{onStart:t=>{let{query:a,command:i,clientRect:o}=t;(e=document.createElement("div")).className="absolute border border-gray-300 rounded-lg shadow-lg bg-[#F9FAFB] overflow-y-auto z-50",document.body.appendChild(e),document.addEventListener("keydown",r,!0),document.addEventListener("mousedown",l),n(e,a,i,o);let d=e.querySelector("button");if(d){d.classList.add("hover");let e=d.querySelector(".name-span"),t=d.querySelector(".secondary-span");e&&e.classList.add("text-white"),t&&t.classList.add("text-white"),s=0}else s=-1},onUpdate:t=>{let{query:a,command:r,clientRect:l}=t;if(!e)return;e.innerHTML="",n(e,a,r,l);let i=e.querySelector("button");if(i){i.classList.add("hover");let e=i.querySelector(".name-span"),t=i.querySelector(".secondary-span");e&&e.classList.add("text-white"),t&&t.classList.add("text-white"),s=0}else s=-1},onExit:()=>{e&&e.remove(),document.removeEventListener("keydown",r,!0),document.removeEventListener("mousedown",l),e=null,s=-1}};function n(e,r,l,n){var i,o;let d=String(r||"").toLowerCase(),c=null!==(o=null==t?void 0:null===(i=t.orgMembers)||void 0===i?void 0:i.filter(e=>(e.name||e.email).toLowerCase().includes(d)))&&void 0!==o?o:[],u=""===d||"@channel".includes(d)?[{id:"00000000-0000-0000-0000-000000000000",name:"@channel",profile_url:"/images/megaphone.png",full_name:"Notify everyone in this channel",is_online:!1,type:"user"},...c]:c;if(0===u.length){e&&(e.style.display="none");return}e&&(e.style.display="");let x="function"==typeof n?n():null;if(x&&e){let t=x.top+window.scrollY,a=Math.min(48*u.length,300),s=t>a+48?t-a-x.height:x.bottom+window.scrollY;Object.assign(e.style,{top:"".concat(s,"px"),left:"".concat(x.left+window.scrollX,"px"),maxHeight:"".concat(300,"px")})}u.forEach(r=>{let n=document.createElement("button");n.className="group flex items-center px-3 py-2 text-left w-full gap-3",n.addEventListener("mouseover",()=>{Array.from(e.querySelectorAll("button")).forEach((e,t)=>{if(e===n){if(!e.classList.contains("hover")){e.classList.add("hover");let a=e.querySelector(".name-span"),r=e.querySelector(".secondary-span");a&&a.classList.add("text-white"),r&&r.classList.add("text-white"),s=t}}else if(e.classList.contains("hover")){e.classList.remove("hover");let t=e.querySelector(".name-span"),a=e.querySelector(".secondary-span");t&&t.classList.remove("text-white"),a&&a.classList.remove("text-white")}})});let i=document.createElement("div");i.className="w-6 h-6 flex items-center justify-center rounded-md bg-gray-200 text-white font-bold text-sm overflow-hidden",i.style.minWidth="1.5rem";let o=document.createElement("img");o.src=r.profile_url||"/images/user.png",o.alt=r.name||r.email,o.className="w-full h-full rounded-md object-cover border",i.appendChild(o);let d=document.createElement("div");d.className="flex items-center gap-2";let c=document.createElement("div");c.className="flex items-center gap-2";let u=document.createElement("span");u.textContent=r.name||r.email,u.className="text-sm font-bold capitalize text-gray-800 name-span";let x=document.createElement("div");"@channel"!==r.name&&(x.className="size-2 rounded-full border border-gray-500"),c.appendChild(u),"channel"!==r.id&&c.appendChild(x);let h=document.createElement("span");h.textContent=(null==r?void 0:r.id)==="00000000-0000-0000-0000-000000000000"?r.full_name:r.name&&r.email&&r.name.toLowerCase()!==r.email.toLowerCase()?r.email:"",h.className="text-xs text-gray-500 secondary-span",d.appendChild(c),h.textContent&&d.appendChild(h),n.appendChild(i),n.appendChild(d),n.onclick=()=>{let e=r.name;e&&""!==e.trim()||(e=r.email);let s=e.replace(/^@/,""),n={id:r.id,label:s};if("channel"!==r.id){let e={id:r.id,label:s,type:"user"};t.mentions.some(t=>t.id===e.id)||a({type:m.a.MENTIONS,payload:[e]})}l(n)},e.appendChild(n)})}}}}),c.ZP.configure({openOnClick:!0,linkOnPaste:!0,autolink:!0,defaultProtocol:"https",HTMLAttributes:{class:"text-primary-500"}}),o.ZP,d.ZP.configure({HTMLAttributes:{class:"language-javascript"}})],onCreate:e=>{let{editor:t}=e;t.commands.focus()},onUpdate:e=>{let{editor:t}=e;A(t.isEmpty)},editorProps:{handlePaste(t,a,s){var r;let l=null===(r=a.clipboardData)||void 0===r?void 0:r.items;if(l){for(let t of l)if(-1!==t.type.indexOf("image")){let a=t.getAsFile();return a&&(null==e||e(a)),!0}}return!1}}}),isEmpty:h}}},56792:function(e,t,a){a.d(t,{Z:function(){return s}});var s={bot:{src:"/_next/static/media/bot.dfdf2c7a.png",height:80,width:80,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAHlBMVEVMaXEAYC4AlUQAm0YAjUEAfDgrJCgKXTEAj0EAkEKCxuBiAAAACnRSTlMAB73oOFyq4Hx6x9l+tAAAAAlwSFlzAAAsSwAALEsBpT2WqQAAAChJREFUeJxjYEACjIxQBhsbhGZlZ2cF0SxMzMxMLCAVHExMnGiKwQAACx0AUUBiaOwAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},RedBot:{src:"/_next/static/media/bot-red.3a55df2b.png",height:40,width:40,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAIVBMVEVMaXFQAAB3BwJtCARtCQReBQB4CgJvCgRLEAskLS4hKCqjVWtSAAAAC3RSTlMABO01ulzJe9m0qZQuNjoAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAoSURBVHicY2BAAoyMUJqTC8Jg5eBgBdHMbExMbMwgGXYWFnZUxRAAAA2RAGBI3TANAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},blueBot:{src:"/_next/static/media/blue-bot.f296e2d3.png",height:40,width:40,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAKlBMVEXv7/rr7vjt7vj19v7x8f6Hh/ivr/q6u+9gYapzc//Y1/3T0v1mZ2JdX1kQXXeiAAAAA3RSTlP+nJxhYYB9AAAACXBIWXMAAAsTAAALEwEAmpwYAAAANklEQVR4nBXKyQ3AMBDEMNma9ZWk/3YDfwnSJGDHBBJBvtcAzrXmhaf2riNkVI37TaLS5cb2AyAvAORP3/L0AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},google:{src:"/_next/static/media/google.3c81f1fb.svg",height:16,width:16,blurWidth:0,blurHeight:0},send:{src:"/_next/static/media/send.7163c084.svg",height:18,width:17,blurWidth:0,blurHeight:0},disco:{src:"/_next/static/media/disco.672244de.png",height:192,width:192,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAaVBMVEVMaXHi2M2nopzZsWDKqnHy01GUkIC0m2ODeG8Zl/84f+Lmlwbyuy7Oplrew47k0KassLjamCLdr13j5eXVx6WwqJ7cniSXin7CwLzcw5mPfmV/dWh/fHevtq++qnPu7/TCu7bXybm2t7Oodw53AAAAH3RSTlMA/IlTgiVGFf0LJCo85G/2o3DNqIb6jPf4+/h6mErb4mYc6AAAAAlwSFlzAAAhOAAAITgBRZYxYAAAAEBJREFUeJwlwccBgCAAALGjgw17V9D9h/RhAhSBn7MYB2XVxKHvQNciJaEhtHlaXwmMz+L9Ccxb9vt9gVPysMp8UogCzxGkjJoAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},teammates:{src:"/_next/static/media/teammates.bbd3adac.svg",height:32,width:32,blurWidth:0,blurHeight:0},facebook:{src:"/_next/static/media/facebook.fca64f6c.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAALVBMVEVWmvD//fpHkvBNlfAMcfMee/HX6Py10/kpgvOEt/f///+ex/jG3foAZPJgovbihSuXAAAABHRSTlP+4f7+rTCusQAAAAlwSFlzAAAsSwAALEsBpT2WqQAAADhJREFUeJwFwYkRwDAMAjDsA/wk6f7jVkIYYjrgpuaTAXJ2CEisogTx7B4KYN/bBNyv6rURzqp0/DHzAXV/loHlAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},twitter:{src:"/_next/static/media/twitter.913bc175.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAGFBMVEUAAAABAQEeHh4PDw9qampFRUVYWFhSUlIoHbPFAAAAAXRSTlPhl+sKiAAAAAlwSFlzAAAsSwAALEsBpT2WqQAAAC9JREFUeJw1i0EOACAMwihD/f+PzWbk1KRFYiZRqzAIsr0GnLSDSp463mkoT69/vxFyAH2eHpTfAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},instagram:{src:"/_next/static/media/instagram.07bbefde.png",height:96,width:96,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAPFBMVEX6+vr++v76tezYrP3+pcn9tL77pen6+vn8//////7+r8/+wK7+4an/rNf+07Hpqvz/7aj6ed3+3Lb+rKgwumMZAAAACHRSTlPg////////4NsSfKYAAAAJcEhZcwAAITgAACE4AUWWMWAAAAA+SURBVHicFcs5EsAgDANAYWOQuJP8/68Zum0WkCQKUHLflgRtt/IYwTJnyZngFz3axRrvikZI54yebiNJ1R9EDgII9TTKpAAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},youtube:{src:"/_next/static/media/youtube.b9208f26.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAGFBMVEX5+fn9/////Pz8WVn8Hh78JSX+AAD8n57mNweJAAAAAXRSTlPhl+sKiAAAAAlwSFlzAAAsSwAALEsBpT2WqQAAACxJREFUeJxNi7ENADAMg4ydNP9/3ClVNyRAwmAksG0Q01U9iJzk5IenNt79AhbIAKGvX29oAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},tiktok:{src:"/_next/static/media/tiktok.029728a5.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAPFBMVEUBAQFMHietfojQv8Y1Exqzs7cBDQwAAAAAAAAZCQxVgYLI9PSxxcp1fIFNjYtHREYdOTfPr7sdVlNtoqCdn0cDAAAACHRSTlPh////////4WC/TK4AAAAJcEhZcwAALEsAACxLAaU9lqkAAAA4SURBVHicY2DgAAMGEM3GxcnBAWZws3KCGHy8rEwsHAwcQsyMPCwgKQFmYUGIGn4mRpA2doh2dgBFkAH9NNczawAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},channel:{src:"/_next/static/media/channel.71b0d2d9.png",height:1827,width:1986,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAMAAAACh/xsAAAAFVBMVEX6+P77+v328//7+/3w6/3////u5v959aEiAAAABXRSTlP94vju40yQVCYAAAAJcEhZcwAAITgAACE4AUWWMWAAAAAnSURBVHicY2BkAgMWBhY2MGBhYGYAAVZGBmZWMEBhMLCygqQYoWoAFpgAqnCsrYsAAAAASUVORK5CYII=",blurWidth:8,blurHeight:7},user:{src:"/_next/static/media/user.cc6e44b6.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAG1BMVEXz/vwD0sHq/PpK4NPU+PVi5NkY18jB9PCF6uJqQ8ocAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAM0lEQVR4nB3JwQ3AMAzEMMlnO9l/4iL9ESBVAFUUPdM8jM6PaH508oq+M7fhrOoe4uqaDxOVAKOLLWTAAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},createChannel:{src:"/_next/static/media/create-channel.d750c07c.png",height:2332,width:2544,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAMAAAACh/xsAAAAD1BMVEX+/v7t6P308vz6+vrm5OpbWQr2AAAACXBIWXMAACxLAAAsSwGlPZapAAAAJ0lEQVR4nEXHwQ0AMAzCQDDZf+aKKFKPD5bNko+mDApDEn2tbk/7AQyWAF9SvWHtAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:7},megaphone:{src:"/_next/static/media/megaphone.d567f808.png",height:24,width:24,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAJ1BMVEVMaXEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACv15x2AAAADXRSTlMAaRhcwINMCyY5ndOqyc6ZIAAAAAlwSFlzAAALEwAACxMBAJqcGAAAADRJREFUeJxVyrkNACAMwEDnI5Cw/7wIiYarXBg+OhRySZUba4dNRDAfmjeIbg83yBDmnZ8DGa0AvpK+HLsAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},visaLogo:{src:"/_next/static/media/visa-logo.e39f2d7e.svg",height:12,width:33,blurWidth:0,blurHeight:0},fileDoc:{src:"/_next/static/media/FileDoc.69079eba.svg",height:20,width:20,blurWidth:0,blurHeight:0},border:{src:"/_next/static/media/Border.8c3c175f.svg",height:1024,width:44,blurWidth:0,blurHeight:0}}},11492:function(e,t,a){a.d(t,{d:function(){return o},z:function(){return d}});var s=a(75376),r=a(32486),l=a(91007),n=a(53447),i=a(58983);let o=(0,n.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,t)=>{let{className:a,variant:r,size:n,asChild:d=!1,...c}=e,u=d?l.g7:"button";return(0,s.jsx)(u,{className:(0,i.cn)(o({variant:r,size:n,className:a})),ref:t,...c})});d.displayName="Button"},16006:function(e,t,a){a.d(t,{$N:function(){return A},Be:function(){return p},GG:function(){return c},Vq:function(){return i},cN:function(){return h},cZ:function(){return m},fK:function(){return x},hg:function(){return o},t9:function(){return u}});var s=a(75376),r=a(32486),l=a(94797),n=a(58983);let i=l.fC,o=l.xz,d=l.h_,c=l.x8,u=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(l.aV,{ref:t,className:(0,n.cn)("fixed inset-0 z-50 bg-black/50  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...r})});u.displayName=l.aV.displayName;let m=r.forwardRef((e,t)=>{let{className:a,children:r,...i}=e;return(0,s.jsxs)(d,{children:[(0,s.jsx)(u,{}),(0,s.jsx)(l.VY,{ref:t,className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-[90%] max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...i,children:r})]})});m.displayName=l.VY.displayName;let x=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,n.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};x.displayName="DialogHeader";let h=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};h.displayName="DialogFooter";let A=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(l.Dx,{ref:t,className:(0,n.cn)("text-lg font-semibold leading-none tracking-tight",a),...r})});A.displayName=l.Dx.displayName;let p=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(l.dk,{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",a),...r})});p.displayName=l.dk.displayName},25575:function(e,t,a){a.d(t,{I:function(){return n}});var s=a(75376),r=a(32486),l=a(58983);let n=r.forwardRef((e,t)=>{let{className:a,type:r,...n}=e;return(0,s.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-4 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus:outline-none disabled:cursor-not-allowed disabled:opacity-50",a),ref:t,...n})});n.displayName="Input"},73003:function(e,t,a){var s=a(75376);a(32486);var r=a(10983);t.Z=e=>{let{height:t,width:a,color:l}=e;return(0,s.jsx)(r.iT,{height:t||20,width:a||20,color:l||"#fff",visible:!0,ariaLabel:"oval-loading",secondaryColor:l||"#fff",strokeWidth:5,strokeWidthSecondary:5})}},74178:function(e,t,a){a.d(t,{J2:function(){return i},xo:function(){return o},yk:function(){return d}});var s=a(75376),r=a(32486),l=a(80500),n=a(58983);let i=l.fC,o=l.xz,d=r.forwardRef((e,t)=>{let{className:a,align:r="center",sideOffset:i=4,...o}=e;return(0,s.jsx)(l.h_,{children:(0,s.jsx)(l.VY,{ref:t,align:r,sideOffset:i,className:(0,n.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...o})})});d.displayName=l.VY.displayName},46962:function(e,t,a){a.d(t,{_v:function(){return c},aJ:function(){return d},pn:function(){return i},u:function(){return o}});var s=a(75376),r=a(32486),l=a(82288),n=a(58983);let i=l.zt,o=l.fC,d=l.xz,c=r.forwardRef((e,t)=>{let{className:a,sideOffset:r=4,...i}=e;return(0,s.jsx)(l.VY,{ref:t,sideOffset:r,className:(0,n.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]",a),...i})});c.displayName=l.VY.displayName},58983:function(e,t,a){a.d(t,{cn:function(){return l},k:function(){return n}});var s=a(89824),r=a(97215);function l(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.m6)((0,s.W)(t))}let n=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"},70336:function(e,t,a){a.d(t,{p:function(){return n}});var s=a(77947),r=a(51462),l=a(19704);let n=e=>{let t={};return null==e||e.forEach(e=>{let a;let n=new Date(e.created_at);t[a=(0,s.z)(n)?"Today":(0,r.g)(n)?"Yesterday":(0,l.WU)(n,"MMMM d, yyyy")]||(t[a]=[]),t[a].push(e)}),t}},86418:function(e,t,a){a.d(t,{Gl:function(){return n},HH:function(){return o},Q_:function(){return d},_x:function(){return u},an:function(){return m},i1:function(){return h},jx:function(){return x},x9:function(){return c},xo:function(){return i}});var s=a(20818),r=a(13352);let l=a(18648).env.NEXT_PUBLIC_BASE_URL,n=async e=>{let t=localStorage.getItem("token")||"";try{return await s.Z.get(l+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var a,r,n;return((null==e?void 0:null===(a=e.response)||void 0===a?void 0:a.status)===401||(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(r=n.data)||void 0===r?void 0:r.status_code)===401)&&(localStorage.clear(),window.location.href="/auth/login"),e}},i=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await s.Z.post(l+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var n,i,o,d,c;return r.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(n=i.data)||void 0===n?void 0:n.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},o=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await s.Z.post(l+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var n,i;return r.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(n=i.data)||void 0===n?void 0:n.message),e}},d=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await s.Z.post(l+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){return e}},c=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await s.Z.post(l+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"multipart/form-data"}})}catch(e){var n,i,o,d,c;return r.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(n=i.data)||void 0===n?void 0:n.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},u=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await s.Z.patch(l+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var n,i,o,d,c;return r.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(n=i.data)||void 0===n?void 0:n.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},m=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await s.Z.put(l+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var n,i,o,d,c;return r.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(n=i.data)||void 0===n?void 0:n.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},x=async e=>{let t=localStorage.getItem("token")||"";try{return await s.Z.delete(l+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var a,n;return r.Z.error(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(a=n.data)||void 0===a?void 0:a.message),e}},h=async e=>{let t=localStorage.getItem("token")||"";try{return await s.Z.delete(l+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){return e}}}}]);