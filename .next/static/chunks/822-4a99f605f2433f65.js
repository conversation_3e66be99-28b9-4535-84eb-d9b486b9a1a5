"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[822],{90822:function(e,a,l){l.d(a,{Z:function(){return v}});var t=l(75376),n=l(32486),s=l(16006),o=l(11492),i=l(36684),r=l(39713),d=l(97220),c=l(56792),u=l(13352),x=l(73003),m=l(20818),p=l(97712);let h=["(UTC-12:00) International Date Line West","(UTC-11:00) Coordinated Universal Time-11","(UTC-10:00) Hawaii","(UTC-09:00) Alaska","(UTC-08:00) Pacific Time (US & Canada)","(UTC-07:00) Mountain Time (US & Canada)","(UTC-06:00) Central Time (US & Canada)","(UTC-05:00) Eastern Time (US & Canada)","(UTC-04:00) Atlantic Time (Canada)","(UTC-03:30) Newfoundland","(UTC-03:00) Brasilia, Buenos Aires","(UTC-02:00) Mid-Atlantic","(UTC-01:00) Azores","(UTC+00:00) London, Dublin, Lisbon","(UTC+01:00) Paris, Berlin, Rome","(UTC+02:00) Cairo, Athens, Jerusalem","(UTC+03:00) Moscow, Nairobi, Riyadh","(UTC+03:30) Tehran","(UTC+04:00) Dubai, Baku, Samara","(UTC+04:30) Kabul","(UTC+05:00) Karachi, Tashkent","(UTC+05:30) India Standard Time","(UTC+05:45) Kathmandu","(UTC+06:00) Dhaka, Almaty","(UTC+06:30) Yangon","(UTC+07:00) Bangkok, Hanoi, Jakarta","(UTC+08:00) Beijing, Singapore, Perth","(UTC+08:45) Eucla","(UTC+09:00) Tokyo, Seoul, Osaka","(UTC+09:30) Adelaide, Darwin","(UTC+10:00) Sydney, Melbourne, Brisbane","(UTC+10:30) Lord Howe Island","(UTC+11:00) Solomon Islands, New Caledonia","(UTC+12:00) Auckland, Fiji, Kamchatka","(UTC+12:45) Chatham Islands","(UTC+13:00) Samoa, Tonga","(UTC+14:00) Kiritimati Island"],f=l(18648).env.NEXT_PUBLIC_BASE_URL;var v=e=>{let{isOpen:a,onClose:l}=e,{state:v,dispatch:C}=(0,n.useContext)(d.R),{user:y}=v,[b,N]=(0,n.useState)(""),[T,g]=(0,n.useState)(""),[j,U]=(0,n.useState)(""),[E,w]=(0,n.useState)(""),[k,F]=(0,n.useState)(""),[A,S]=(0,n.useState)(null==y?void 0:y.timezone),[Z,_]=(0,n.useState)(""),[L,z]=(0,n.useState)(""),[B,R]=(0,n.useState)(!1),P=(0,n.useRef)(null),I=(0,n.useRef)(null);(0,n.useEffect)(()=>{N((null==y?void 0:y.full_name)||""),g((null==y?void 0:y.display_name)||""),w((null==y?void 0:y.avatar_url)||(null===c.Z||void 0===c.Z?void 0:c.Z.user)),U((null==y?void 0:y.title)||""),F(null==y?void 0:y.name_pronounciation),z((null==y?void 0:y.phone)||"")},[]);let D=async()=>{let e={full_name:b,avatar_url:Z,display_name:T,username:T,phone:L,email:null==y?void 0:y.email,title:j,timezone:A,name_pronounciation:k};R(!0);try{let t=await m.Z.patch(f+"/profile",e,{headers:{Authorization:"Bearer ".concat(null==v?void 0:v.token),"Content-Type":"multipart/form-data"}});if((null==t?void 0:t.status)===200||(null==t?void 0:t.status)===201){var a;C({type:p.a.PROFILE_CALLBACK,payload:!(null==v?void 0:v.profileCallback)}),u.Z.success(null==t?void 0:null===(a=t.data)||void 0===a?void 0:a.message)}setTimeout(()=>{R(!1),l()},1e3)}catch(e){console.log(e)}},K=async()=>{try{await m.Z.delete(f+"/profile/image",{headers:{Authorization:"Bearer ".concat(null==v?void 0:v.token),"Content-Type":"application/json"}}),C({type:p.a.PROFILE_CALLBACK,payload:!(null==v?void 0:v.profileCallback)}),w(null===c.Z||void 0===c.Z?void 0:c.Z.user),P.current&&(P.current.value="")}catch(e){console.log(e),w(null===c.Z||void 0===c.Z?void 0:c.Z.user),P.current&&(P.current.value="")}};return(0,t.jsx)(s.Vq,{open:a,onOpenChange:l,children:(0,t.jsxs)(s.cZ,{className:"sm:max-w-[700px] p-0 overflow-hidden max-h-[90vh]",children:[(0,t.jsx)(s.fK,{className:"p-6 pb-0",children:(0,t.jsx)(s.$N,{className:"text-[#1D2939] text-lg lg:text-xl font-black",children:"Edit your profile"})}),(0,t.jsxs)("div",{ref:I,className:"max-h-[calc(90vh-180px)] border-t border-[#E6EAEF] space-y-5 pb-6 overflow-y-auto scrollbar-hide",children:[(0,t.jsxs)("div",{className:"flex flex-col-reverse md:flex-row",children:[(0,t.jsxs)("div",{className:"flex-1 py-3 px-6 space-y-5",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{htmlFor:"fullName",className:"text-sm text-[#101828] font-bold",children:"Full Name"}),(0,t.jsx)("input",{id:"fullName",type:"text",value:b,onChange:e=>N(e.target.value),className:"w-full px-3 py-2 border border-[#E6EAEF] rounded-md text-[15px] text-[#344054] focus:outline-none focus:ring-2 focus:ring-[#6868F7]"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{htmlFor:"displayName",className:"text-sm text-[#101828] font-bold",children:"Display Name"}),(0,t.jsx)("input",{id:"displayName",type:"text",value:T,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 border border-[#E6EAEF] rounded-md text-[15px] text-[#344054] focus:outline-none focus:ring-2 focus:ring-[#6868F7]"}),(0,t.jsx)("p",{className:"text-xs text-[#667085]",children:"This could be your first name, or a nickname - however you'd like people to refer to you."})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{htmlFor:"displayName",className:"text-sm text-[#101828] font-bold",children:"Phone Number"}),(0,t.jsx)("input",{id:"displayName",type:"text",value:L,placeholder:"+44 20 7946 0958",onChange:e=>z(e.target.value),className:"w-full px-3 py-2 border border-[#E6EAEF] rounded-md text-[15px] text-[#344054] focus:outline-none focus:ring-2 focus:ring-[#6868F7]"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{htmlFor:"title",className:"text-sm text-[#101828] font-bold",children:"Title"}),(0,t.jsx)("input",{id:"title",type:"text",value:j,onChange:e=>U(e.target.value),className:"w-full px-3 py-2 border border-[#E6EAEF] rounded-md text-[15px] text-[#344054] focus:outline-none focus:ring-2 focus:ring-[#6868F7]"})]})]}),(0,t.jsx)("div",{className:"w-[230px] pl-6 pr-3 py-3 space-y-6",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"text-sm text-[#101828] font-bold",children:"Profile Photo"}),(0,t.jsxs)("div",{className:"flex flex-col items-start gap-4",children:[(0,t.jsx)("div",{className:"relative w-[192px] h-[192px] border rounded-[9px]",children:(0,t.jsx)(r.default,{src:E,alt:b,width:192,height:192,className:"rounded-[9px] object-cover w-full h-full"})}),(0,t.jsxs)("div",{className:"space-y-1 w-full",children:[(0,t.jsxs)(o.z,{variant:"outline",onClick:()=>{var e;null===(e=P.current)||void 0===e||e.click()},className:"text-[13px] h-8 text-[#344054] border-[#E6EAEF] gap-2 w-full flex items-center justify-center",children:[(0,t.jsx)(i.Z,{size:14}),(0,t.jsx)("span",{children:"Upload photo"})]}),(0,t.jsx)("input",{ref:P,type:"file",accept:"image/*",className:"hidden",onChange:e=>{var a;let l=null===(a=e.target.files)||void 0===a?void 0:a[0];if((null==l?void 0:l.type)!=="image/jpeg"&&(null==l?void 0:l.type)!=="image/png")return u.Z.error("Image type is not supported");if(l){w(URL.createObjectURL(l));let e=new FileReader;e.onloadend=()=>{_(e.result)},e.readAsDataURL(l)}}}),(0,t.jsx)(o.z,{onClick:K,className:"text-[13px] h-8 text-[#6868F7] gap-2 py-0 w-full",children:"Remove photo"})]})]})]})})]}),(0,t.jsxs)("div",{className:"space-y-2 px-6",children:[(0,t.jsx)("label",{htmlFor:"namePronunciation",className:"text-sm text-[#101828] font-bold",children:"Name Pronunciation"}),(0,t.jsx)("input",{id:"namePronunciation",type:"text",value:k,onChange:e=>F(e.target.value),placeholder:"Zoe (pronounced 'zo-ee')",className:"w-full px-3 py-2 border border-[#E6EAEF] rounded-md text-[15px] text-[#344054] focus:outline-none focus:ring-2 focus:ring-[#6868F7]"}),(0,t.jsx)("p",{className:"text-xs text-[#667085]",children:"This could be a phonetic pronunciation, or an example of something your name sounds like."})]}),(0,t.jsxs)("div",{className:"space-y-2 px-6",children:[(0,t.jsx)("label",{htmlFor:"timezone",className:"text-sm text-[#101828] font-bold",children:"Timezone"}),(0,t.jsx)("select",{id:"timezone",value:A,onChange:e=>S(e.target.value),className:"w-full form-select px-3 py-2 border border-[#E6EAEF] rounded-md text-[15px] text-[#344054] focus:outline-none focus:ring-2 focus:ring-[#6868F7]",children:h.map((e,a)=>(0,t.jsx)("option",{value:e,children:e},a))})]})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-3 px-6 py-5 border-t border-[#E6EAEF] sticky bottom-0 bg-white",children:[(0,t.jsx)(o.z,{variant:"outline",onClick:l,className:"text-sm text-[#344054] h-9 border-[#E6EAEF]",children:"Cancel"}),(0,t.jsxs)(o.z,{onClick:D,className:"d-flex items-center gap-2 bg-[#6868F7] text-white h-9 text-sm hover:bg-[#5151d3]",disabled:B,children:["Save Changes ",B&&(0,t.jsx)(x.Z,{})]})]})]})})}}}]);