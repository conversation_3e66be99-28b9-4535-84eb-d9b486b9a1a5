(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1148],{60971:function(e,t,l){var s=0/0,n=/^\s+|\s+$/g,i=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,o=/^0o[0-7]+$/i,r=parseInt,d="object"==typeof l.g&&l.g&&l.g.Object===Object&&l.g,c="object"==typeof self&&self&&self.Object===Object&&self,u=d||c||Function("return this")(),x=Object.prototype.toString,v=Math.max,m=Math.min,p=function(){return u.Date.now()};function h(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function f(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||t&&"object"==typeof t&&"[object Symbol]"==x.call(t))return s;if(h(e)){var t,l="function"==typeof e.valueOf?e.valueOf():e;e=h(l)?l+"":l}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(n,"");var d=a.test(e);return d||o.test(e)?r(e.slice(2),d?2:8):i.test(e)?s:+e}e.exports=function(e,t,l){var s,n,i,a,o,r,d=0,c=!1,u=!1,x=!0;if("function"!=typeof e)throw TypeError("Expected a function");function b(t){var l=s,i=n;return s=n=void 0,d=t,a=e.apply(i,l)}function g(e){var l=e-r,s=e-d;return void 0===r||l>=t||l<0||u&&s>=i}function j(){var e,l,s,n=p();if(g(n))return y(n);o=setTimeout(j,(e=n-r,l=n-d,s=t-e,u?m(s,i-l):s))}function y(e){return(o=void 0,x&&s)?b(e):(s=n=void 0,a)}function N(){var e,l=p(),i=g(l);if(s=arguments,n=this,r=l,i){if(void 0===o)return d=e=r,o=setTimeout(j,t),c?b(e):a;if(u)return o=setTimeout(j,t),b(r)}return void 0===o&&(o=setTimeout(j,t)),a}return t=f(t)||0,h(l)&&(c=!!l.leading,i=(u="maxWait"in l)?v(f(l.maxWait)||0,t):i,x="trailing"in l?!!l.trailing:x),N.cancel=function(){void 0!==o&&clearTimeout(o),d=0,s=r=n=o=void 0},N.flush=function(){return void 0===o?a:y(p())},N}},23625:function(e,t,l){"use strict";l.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,l(9824).Z)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},76892:function(e,t,l){"use strict";l.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,l(9824).Z)("File",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]])},37313:function(e,t,l){"use strict";l.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,l(9824).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},79624:function(e,t,l){"use strict";l.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,l(9824).Z)("SendHorizontal",[["path",{d:"m3 3 3 9-3 9 19-9Z",key:"1aobqy"}],["path",{d:"M6 12h16",key:"s4cdu5"}]])},53189:function(e,t,l){"use strict";l.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,l(9824).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},25389:function(e,t,l){"use strict";l.d(t,{Z:function(){return f}});var s=l(75376),n=l(80500),i=l(2336),a=l(72738),o=l.n(a),r=l(39713),d=l(47411),c=l(32486),u=l(56792),x=l(74178),v=l(97712),m=l(97220),p=l(90937),h=l(86418);function f(e){var t;let{item:l}=e,[a,f]=(0,c.useState)(!1),b=(0,c.useRef)(null),g=(0,c.useRef)(null),{state:j,dispatch:y}=(0,c.useContext)(m.R),{user:N}=j,E=(0,d.useRouter)(),w=()=>{g.current&&clearTimeout(g.current),b.current=setTimeout(()=>{f(!0)},300)},C=()=>{b.current&&clearTimeout(b.current),g.current=setTimeout(()=>{f(!1)},200)},A=async()=>{localStorage.setItem("channelName",null==l?void 0:l.username);let e=localStorage.getItem("orgId")||"",t={chat_type:"user",participant_id:null==l?void 0:l.user_id},s=await (0,h.xo)("/organisations/".concat(e,"/dms"),t);if((null==s?void 0:s.status)===200||(null==s?void 0:s.status)===201){var n,i,a,o;E.push("/client/home/<USER>/".concat(null==s?void 0:null===(i=s.data)||void 0===i?void 0:null===(n=i.data)||void 0===n?void 0:n.channel_id,"/").concat(null==s?void 0:null===(o=s.data)||void 0===o?void 0:null===(a=o.data)||void 0===a?void 0:a.participant_id,"/dm"))}};return(0,s.jsxs)(n.fC,{open:a,onOpenChange:f,children:[(0,s.jsx)(x.xo,{asChild:!0,children:(0,s.jsxs)("span",{onMouseEnter:w,onMouseLeave:C,onClick:()=>{y({type:v.a.USER_DATA,payload:l}),y({type:v.a.HOVER_PROFILE,payload:!0})},className:"ml-1 py-[1px] px-[3px] bg-[#F1F1FE] text-[#7141F8] text-[15px] rounded-[3px] cursor-pointer",children:[" ","@",null==l?void 0:l.username]})}),(0,s.jsxs)(x.yk,{onMouseEnter:w,onMouseLeave:C,sideOffset:8,side:"top",className:"z-50 w-auto rounded-md border border-gray-200 bg-white shadow-lg p-4",align:"start",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(r.default,{src:(null==l?void 0:l.avatar_url)?null==l?void 0:l.avatar_url:(null==l?void 0:l.user_type)=="user"||(null==l?void 0:l.user_type)===""?null===u.Z||void 0===u.Z?void 0:u.Z.user:null===u.Z||void 0===u.Z?void 0:u.Z.bot,alt:"avatar",width:80,height:80,className:"rounded-[7px] border size-20 object-cover"}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"font-semibold text-[15px] text-black",children:[(null==l?void 0:null===(t=l.full_name)||void 0===t?void 0:t.trim())||(null==l?void 0:l.username)," ",(null==l?void 0:l.user_id)===(null==N?void 0:N.user_id)&&(0,s.jsx)("span",{className:"text-gray-500",children:"(you)"})]}),(null==l?void 0:l.user_id)===(null==N?void 0:N.user_id)?(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-0.5",children:null==N?void 0:N.title}):(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-0.5",children:null==l?void 0:l.email})]})]}),(0,s.jsxs)("div",{className:"flex items-center text-[15px] text-gray-600 mt-4",children:[(0,s.jsx)(i.Z,{className:"w-4 h-4 mr-2"}),o()(null==l?void 0:l.created_at).format("LT")," local time"]}),(null==l?void 0:l.user_id)===(null==N?void 0:N.user_id)?(0,s.jsx)("button",{onClick:()=>y({type:v.a.STATUS,payload:!0}),className:"mt-3 w-full border text-sm font-medium border-gray-300 rounded-md py-1.5 hover:bg-gray-50 transition",children:"Set a status"}):(0,s.jsxs)("button",{onClick:A,className:"flex items-center justify-center gap-1 mt-3 w-full border text-sm font-medium border-gray-300 rounded-md py-1.5 hover:bg-gray-50 transition",children:[(0,s.jsx)(p.mT,{}),"Message"]})]})]})}},78180:function(e,t,l){"use strict";var s=l(75376),n=l(32486),i=l(72742),a=l(22641),o=l(49107),r=l(28904),d=l(82310),c=l(72593),u=l(59632),x=l(75148),v=l(40708),m=l(97220),p=l(65470),h=l(16006),f=l(25575),b=l(11492),g=l(99402),j=l(89863),y=l(97712),N=l(74178);t.Z=e=>{let{subscription:t,sendMessage:l}=e,{editor:E}=(0,v.Z)(t),{state:w,dispatch:C}=(0,n.useContext)(m.R),[A,Z]=(0,n.useState)(!1),[_,k]=(0,n.useState)(""),[S,F]=(0,n.useState)(""),[z,T]=(0,n.useState)(!1),[L,I]=(0,n.useState)(!0),O=async e=>{e.preventDefault();let s=null==E?void 0:E.getHTML();(null==s?void 0:s.replace(/<[^>]+>/g,"").trim())&&(t?l(s):console.log("No connection detected"))};return(0,n.useEffect)(()=>{if(E){var e;null==E||E.commands.setContent(null==w?void 0:null===(e=w.thread)||void 0===e?void 0:e.message),E.commands.focus()}},[E]),(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"bg-white border rounded border-[#E6EAEF] w-full ".concat((null==w?void 0:w.reply)?"right-[520px]":"right-0"),children:[L&&(0,s.jsxs)("div",{className:"border-b border-[#E6EAEF] flex items-center gap-2 mb-2 bg-[#F9FAFB] pl-3 pr-4 py-[5px]",children:[(0,s.jsx)("button",{onClick:()=>null==E?void 0:E.chain().focus().toggleBold().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==E?void 0:E.isActive("bold"))?"bg-gray-200 font-semibold text-black":""),children:(0,s.jsx)(i.Z,{size:18,color:(null==E?void 0:E.isActive("bold"))?"#444444":"#CACACA"})}),(0,s.jsx)("button",{onClick:()=>null==E?void 0:E.chain().focus().toggleItalic().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==E?void 0:E.isActive("italic"))?"bg-gray-200 font-semibold text-black":""),children:(0,s.jsx)(a.Z,{size:18,color:(null==E?void 0:E.isActive("italic"))?"#444444":"#CACACA"})}),(0,s.jsx)("button",{onClick:()=>null==E?void 0:E.chain().focus().toggleStrike().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==E?void 0:E.isActive("strike"))?"bg-gray-200 font-semibold text-black":""),children:(0,s.jsx)(o.Z,{size:18,color:(null==E?void 0:E.isActive("strike"))?"#444444":"#CACACA"})}),(0,s.jsx)("div",{className:"w-px h-5 bg-[#E6EAEF]"}),(0,s.jsxs)(h.Vq,{open:A,onOpenChange:Z,children:[(0,s.jsx)(h.hg,{asChild:!0,children:(0,s.jsx)("button",{onClick:()=>Z(!0),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==E?void 0:E.isActive("link"))?"bg-gray-200 font-semibold text-black":""),children:(0,s.jsx)(r.Z,{size:18,color:(null==E?void 0:E.isActive("link"))?"#444444":"#CACACA"})})}),(0,s.jsxs)(h.cZ,{className:"w-full max-w-md",children:[(0,s.jsx)(h.fK,{children:(0,s.jsx)(h.$N,{className:"font-semibold",children:"Add link"})}),(0,s.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,s.jsx)("label",{className:"text-sm font-medium",children:"Text"}),(0,s.jsx)(f.I,{value:_,onChange:e=>k(e.target.value),placeholder:"Enter link text"}),(0,s.jsx)("label",{className:"text-sm font-medium mt-2",children:"Link"}),(0,s.jsx)(f.I,{value:S,onChange:e=>F(e.target.value),placeholder:"Enter URL",type:"url"})]}),(0,s.jsxs)(h.cN,{className:"mt-4 flex justify-end gap-2",children:[(0,s.jsx)(b.z,{variant:"outline",onClick:()=>Z(!1),children:"Cancel"}),(0,s.jsx)(b.z,{onClick:()=>{_&&S&&(null==E||E.chain().focus().insertContent('<a href="'.concat(S,'" target="_blank" rel="noopener noreferrer">').concat(_,"</a>")).run(),Z(!1),k(""),F(""))},disabled:!_||!S,className:"bg-blue-500 text-white px-10",children:"Save"})]})]})]}),(0,s.jsx)("button",{onClick:()=>null==E?void 0:E.chain().focus().toggleOrderedList().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==E?void 0:E.isActive("orderedList"))?"bg-gray-200 font-semibold text-black":""),children:(0,s.jsx)(d.Z,{size:18,color:(null==E?void 0:E.isActive("orderedList"))?"#444444":"#CACACA"})}),(0,s.jsx)("button",{onClick:()=>null==E?void 0:E.chain().focus().toggleBulletList().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==E?void 0:E.isActive("bulletList"))?"bg-gray-200 font-semibold text-black":""),children:(0,s.jsx)(c.Z,{size:18,color:(null==E?void 0:E.isActive("bulletList"))?"#444444":"#CACACA"})}),(0,s.jsx)("div",{className:"w-px h-5 bg-[#E6EAEF]"}),(0,s.jsx)("button",{onClick:()=>null==E?void 0:E.chain().focus().toggleCode().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==E?void 0:E.isActive("code"))?"bg-gray-200 font-semibold text-black":""),children:(0,s.jsx)(u.Z,{size:18,color:(null==E?void 0:E.isActive("code"))?"#444444":"#CACACA"})})]}),(0,s.jsx)("div",{className:"flex-1 relative px-3",children:(0,s.jsx)(p.kg,{editor:E,className:"py-2 rounded-md flex flex-row overflow-auto",onKeyDown:e=>{"Enter"===e.key&&(e.shiftKey?(e.preventDefault(),null==E||E.commands.enter()):(e.preventDefault(),O(e)))}})}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 py-2 pl-3 pr-4",children:[(0,s.jsx)("button",{title:"show formatting",onClick:()=>I(e=>!e),className:"p-1.5 hover:bg-gray-100 rounded text-[#606060] underline",children:"Aa"}),(0,s.jsx)("div",{className:"relative",children:(0,s.jsxs)(N.J2,{open:z,onOpenChange:T,children:[(0,s.jsx)(N.xo,{asChild:!0,children:(0,s.jsx)("button",{className:"p-1.5 hover:bg-gray-100 rounded",children:(0,s.jsx)(x.Z,{size:18,color:"#606060"})})}),(0,s.jsx)(N.yk,{className:"p-0 w-full max-w-xs",children:(0,s.jsx)(g.Z,{data:j,onEmojiSelect:e=>{E?(E.chain().focus().insertContent(null==e?void 0:e.native).run(),T(!1)):console.log("editor not available")}})})]})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1 py-2 pl-3 pr-4",children:[(0,s.jsx)("button",{className:"bg-gray-100 rounded py-2 px-4 text-xs",onClick:()=>C({type:y.a.IS_EDIT,payload:!1}),children:"Cancel"}),(0,s.jsx)("button",{onClick:O,className:"bg-blue-500 rounded py-2 px-4 text-white text-xs",children:"Save"})]})]})]})})}},37460:function(e,t,l){"use strict";var s=l(75376),n=l(32486),i=l(97220),a=l(70336),o=l(3371),r=l(39713),d=l(56792),c=l(11648),u=l(11492),x=l(35486),v=l(97712),m=l(78180),p=l(47411),h=l(86418),f=l(74258),b=l(23625),g=l(37847),j=l(14776),y=l(25389);t.Z=e=>{var t;let{participant:l}=e,{fetchMoreData:N,hasMore:E,loading:w}=(0,c.Z)(),{state:C,dispatch:A}=(0,n.useContext)(i.R),{chats:Z,user:_,isEdit:k,thread:S,notify:F,bookmarks:z,dataId:T}=C,L=(0,a.p)(Z),I=(0,p.useParams)().id,[O,R]=(0,n.useState)(!1),[D,M]=(0,n.useState)(null),P=(0,n.useRef)(null),H=(0,n.useRef)(!1),V=async()=>{await (0,h.Gl)("/dms/channels/".concat(I,"/threads?page=1&limit=1"))};(0,n.useEffect)(()=>{let e=P.current;if(!e)return;let t=()=>{let t=e.scrollTop>=0;t?(V(),H.current=!0):!t&&H.current&&(H.current=!1),R(!t)};return e.addEventListener("scroll",t),t(),()=>{e.removeEventListener("scroll",t)}},[]),(0,n.useEffect)(()=>{O||(null==F?void 0:F.user_id)===(null==_?void 0:_.user_id)||V()},[Z]);let W=async e=>{await (0,h.an)("/dms/thread/".concat(null==S?void 0:S.thread_id,"/channels/").concat(I),{content:e}),A({type:v.a.IS_EDIT,payload:!1})};return((0,n.useEffect)(()=>{let e=localStorage.getItem("data-id");if(!e)return;let t=document.getElementById("thread-".concat(e));t&&(t.scrollIntoView({behavior:"auto",block:"center"}),t.classList.add("bg-yellow-100"),setTimeout(()=>{t.classList.remove("bg-yellow-100")},1500))},[T,w]),w)?null:(0,s.jsxs)("div",{id:"scrollableDivs",ref:P,style:{height:"100vh",overflowY:"scroll",display:"flex",flexDirection:"column-reverse"},className:"w-full pb-40",children:[O&&(null==C?void 0:C.dmCount)>0&&(0,s.jsxs)(f.C,{onClick:()=>{P.current&&(P.current.scrollTop=0),V()},className:"absolute bottom-40 z-20 mx-auto cursor-pointer -translate-x-[50%] left-1/2 px-3 py-1.5 flex gap-1 bg-primary-500 font-normal text-white text-[0.8125rem] border border-[E6EAEF]",children:[(0,s.jsx)(b.Z,{}),"Latest messages"]}),(0,s.jsx)(o.Z,{dataLength:null==Z?void 0:Z.length,next:N,hasMore:E,loader:(null==Z?void 0:Z.length)!==0&&(0,s.jsx)("h4",{className:"my-5 text-xs text-center",children:"Loading threads..."}),style:{display:"flex",flexDirection:"column-reverse",overflowY:"visible"},scrollableTarget:"scrollableDivs",inverse:!0,children:null===(t=Object.entries(L))||void 0===t?void 0:t.map(e=>{let[t,l]=e;return(0,s.jsxs)(n.Fragment,{children:[null==l?void 0:l.map((e,t)=>{var i,a;let o=l[t+1],c=!o||o.user_id!==e.user_id,u=null==z?void 0:z.some(t=>t.thread_id===e.thread_id);return(0,s.jsx)(n.Fragment,{children:k&&(null==S?void 0:S.thread_id)===(null==e?void 0:e.thread_id)?(0,s.jsxs)("div",{className:"flex mb-5 mt-2 z-10 bg-white px-5 py-3 bg-blue-50 w-full",children:[(0,s.jsx)("div",{className:"size-10 mb-2 mr-3",children:(0,s.jsx)(r.default,{src:(null==e?void 0:e.avatar_url)?null==e?void 0:e.avatar_url:(null==e?void 0:e.user_type)=="user"||(null==e?void 0:e.user_type)===""?null===d.Z||void 0===d.Z?void 0:d.Z.user:null===d.Z||void 0===d.Z?void 0:d.Z.bot,alt:"avatar",width:80,height:40,className:"rounded-[7px] border size-10"})}),(0,s.jsx)(m.Z,{subscription:null==C?void 0:C.chatSubscription,sendMessage:W})]}):(0,s.jsxs)("div",{id:"thread-".concat(e.thread_id),className:"".concat(e.is_pinned?"bg-yellow-50":u?"bg-primary-50":"hover:bg-gray-50"," duration-500 ease-in-out"),children:[(null==e?void 0:e.is_pinned)?(0,s.jsxs)("div",{className:"flex items-center gap-2 bg-yellow-50 pl-10 text-[13px] font-semibold text-blue-100 pt-2",children:[(0,s.jsx)(g.Z,{size:13,className:"text-[#667085] mt-[3px]"}),"Pinned by"," ",(null==_?void 0:_.email)===(null==e?void 0:null===(i=e.pinned_details)||void 0===i?void 0:i.email)?"you":null==e?void 0:null===(a=e.pinned_details)||void 0===a?void 0:a.username]}):u?(0,s.jsxs)("div",{className:"flex items-center gap-2 pl-10 text-[13px] font-bold text-blue-100 pt-2",children:[(0,s.jsx)(j.$8n,{fontSize:13,className:"text-[#667085]"}),"Saved for Later"]}):null,(0,s.jsx)(x.Z,{item:e,shouldShowAvatar:c,setPopupId:M,popupId:D})]})},t)}),(0,s.jsxs)("div",{className:"relative my-2",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-dotted border-[#E6EAEF]"})}),(0,s.jsx)("div",{className:"relative flex justify-center",children:(0,s.jsx)("span",{className:"bg-white px-4 py-1 text-[13px] text-[#101828] border border-[#E6EAEF] rounded-[30px]",children:t})})]})]},t)})}),!E&&(0,s.jsxs)("div",{className:"mt-auto px-5",children:[(0,s.jsxs)("div",{className:"flex gap-2 items-center my-4",children:[(0,s.jsxs)("div",{className:"relative size-24",children:[(0,s.jsx)(r.default,{src:(null==l?void 0:l.avatar_url)?null==l?void 0:l.avatar_url:(null==l?void 0:l.user_type)=="user"||(null==l?void 0:l.user_type)===""?null===d.Z||void 0===d.Z?void 0:d.Z.user:null===d.Z||void 0===d.Z?void 0:d.Z.bot,alt:null==l?void 0:l.username,width:100,height:100,className:"rounded-lg border size-24 object-cover"},null==l?void 0:l.id),(0,s.jsx)("div",{className:"absolute -bottom-1 -right-1 bg-[#00AD51] w-3 h-3 rounded-full border border-white"})]}),(0,s.jsx)("h3",{className:"text-lg font-bold text-[#1D2939]",children:null==l?void 0:l.username})]}),(0,s.jsxs)("p",{className:"text-[17px] text-[#344054] mb-2",children:["This conversation is just between you and",(0,s.jsx)(y.Z,{item:l}),". Check out their profile to learn about them."]}),(0,s.jsx)(u.z,{variant:"outline",className:"h-10",onClick:()=>A({type:v.a.SHOW_PROFILE,payload:!0}),children:"View Profile"})]})]})}},71509:function(e,t,l){"use strict";l.d(t,{Z:function(){return v}});var s=l(75376),n=l(5426),i=l(32486),a=l(11492),o=l(51888),r=l(97712),d=l(97220),c=e=>{let{isOpen:t,onClose:l}=e,[n,a]=(0,i.useState)({copy:!1}),c=(0,i.useRef)(null),{dispatch:u}=(0,i.useContext)(d.R);if((0,i.useEffect)(()=>{let e=e=>{e.target.closest('[role="dialog"]')||!c.current||c.current.contains(e.target)||l()};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[l]),!t)return null;let x="px-4 py-2 text-[15px] text-[#101828] hover:bg-[#F1F1FE] flex items-center justify-between cursor-pointer",v="h-px bg-[#E6EAEF]";return(0,s.jsx)("div",{ref:c,className:"absolute top-full right-0 w-[220px] mt-2.5 bg-white rounded-[7px] shadow-lg border border-[#E6EAEF] z-20",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:x,onClick:()=>{u({type:r.a.USER_DATA,payload:null}),setTimeout(()=>{u({type:r.a.SHOW_PROFILE,payload:!0}),l()},500)},children:"View full profile"}),(0,s.jsx)("div",{className:v}),(0,s.jsx)("div",{className:x,onClick:()=>console.log("Star conversation"),children:"Star conversation"}),(0,s.jsx)("div",{className:v}),(0,s.jsxs)("div",{className:"".concat(x," relative"),onMouseEnter:()=>a({copy:!0}),onMouseLeave:()=>a({copy:!1}),children:["Copy",(0,s.jsx)(o.Z,{className:"w-4 h-4",color:"#343330"}),n.copy&&(0,s.jsxs)("div",{className:"absolute right-full top-0 w-[200px] bg-white rounded-[7px] shadow-lg border border-[#E6EAEF]",children:[(0,s.jsx)("div",{className:x,children:"Copy display name"}),(0,s.jsx)("div",{className:x,children:"Copy member ID"}),(0,s.jsx)("div",{className:x,children:"Copy link to profile"})]})]}),(0,s.jsx)("div",{className:x,onClick:()=>console.log("Search in conversation"),children:"Search in conversation"}),(0,s.jsx)("div",{className:x,onClick:()=>console.log("Open in new window"),children:"Open in new window"}),(0,s.jsx)("div",{className:v}),(0,s.jsx)("div",{className:"".concat(x," text-[#B00E03]"),onClick:()=>console.log("Leave channel"),children:"Hide"})]})})},u=l(39713),x=l(56792),v=e=>{let{user:t,loading:l}=e,[o,v]=(0,i.useState)(!1),m=(0,i.useRef)(null),{dispatch:p}=(0,i.useContext)(d.R);return(0,s.jsxs)("nav",{className:"flex items-center justify-between p-5 border-b border-[#E6EAEF]",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 cursor-pointer",onClick:()=>{setTimeout(()=>{p({type:r.a.SHOW_PROFILE,payload:!0})},500)},children:[l?"":(0,s.jsxs)("div",{className:"relative size-9",children:[(0,s.jsx)(u.default,{src:(null==t?void 0:t.avatar_url)?null==t?void 0:t.avatar_url:(null==t?void 0:t.user_type)=="user"||(null==t?void 0:t.user_type)===""?null===x.Z||void 0===x.Z?void 0:x.Z.user:null===x.Z||void 0===x.Z?void 0:x.Z.bot,alt:null==t?void 0:t.username,width:35,height:35,className:"rounded-[6px] size-9 object-cover border drop-shadow-[0px_1.75475px_3.5095px_rgba(16,24,40,0.1)]"}),(0,s.jsx)("div",{className:"absolute -bottom-0.5 -right-1 bg-[#00AD51] w-2 h-2 rounded-full border border-white"})]}),(0,s.jsx)("h2",{className:"text-[#1D2939] text-base lg:text-lg font-bold",children:null==t?void 0:t.username})]}),(0,s.jsx)("div",{className:"flex items-center gap-3",children:(0,s.jsxs)("div",{className:"relative",ref:m,children:[(0,s.jsx)(a.z,{variant:"outline",className:"p-2 border-[#E6EAEF] h-9 ".concat(o?"bg-[#F6F7F9]":""),onClick:()=>v(!o),children:(0,s.jsx)(n.Z,{className:"w-5 h-5",color:"#344054"})}),(0,s.jsx)(c,{isOpen:o,onClose:()=>v(!1)})]})})]})}},43114:function(e,t,l){"use strict";var s=l(75376),n=l(22397),i=l(53189),a=l(37313),o=l(32486),r=l(39713),d=l(11492),c=l(90937),u=l(56792),x=l(97220),v=l(97712),m=l(47411),p=l(36221);t.Z=e=>{let{user:t}=e,{dispatch:l}=(0,o.useContext)(x.R),h=(0,m.usePathname)();return(0,o.useEffect)(()=>{l({type:v.a.SHOW_PROFILE,payload:!1})},[h]),(0,s.jsxs)("div",{className:"flex flex-col h-full",children:[(0,s.jsxs)("nav",{className:"flex items-center justify-between p-5 py-[23px] border-b border-[#E6EAEF]",children:[(0,s.jsx)("h2",{className:"text-[#1D2939] text-lg font-bold",children:"Profile"}),(0,s.jsx)("button",{onClick:()=>{l({type:v.a.USER_DATA}),l({type:v.a.SHOW_PROFILE,payload:!1}),l({type:v.a.HOVER_PROFILE,payload:!1})},className:"text-[#344054] p-1 border border-input rounded-[0.3125rem]",children:(0,s.jsx)(n.Z,{className:"size-5 text-[#344054]"})})]}),(0,s.jsxs)("div",{className:"py-5 flex flex-col gap-5 overflow-y-auto [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar-thumb]:rounded-full",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-5 px-5",children:[(0,s.jsx)("div",{className:"",children:(0,s.jsx)(r.default,{src:(null==t?void 0:t.avatar_url)?null==t?void 0:t.avatar_url:(null==t?void 0:t.user_type)=="user"||(null==t?void 0:t.user_type)===""?null===u.Z||void 0===u.Z?void 0:u.Z.user:null===u.Z||void 0===u.Z?void 0:u.Z.bot,alt:null==t?void 0:t.username,width:250,height:250,className:"rounded-[9px] border h-[250px] w-[250px] object-cover",quality:100})}),(0,s.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,s.jsx)("div",{className:"flex items-center justify-between gap-3",children:(0,s.jsx)("h2",{className:"text-[#101828] text-[22px] font-black",children:null==t?void 0:t.username})}),(0,s.jsx)("p",{className:"text-[#344054] text-lg",children:null==t?void 0:t.title}),(0,s.jsxs)("div",{className:"flex gap-[10px] items-center",children:[(0,s.jsx)(c.yP,{color:"#475467"}),(0,s.jsx)("p",{className:"text-[15px] text-[#344054]",children:"Away, Notifications snoozed"})]}),(null==t?void 0:t.timezone)&&(0,s.jsxs)("div",{className:"flex gap-[10px] items-center",children:[(0,s.jsx)(c.T3,{}),(0,s.jsx)("p",{className:"text-[15px] text-[#344054]",children:null==t?void 0:t.timezone})]})]}),(0,s.jsxs)("div",{className:"flex gap-3 items-center",children:[(0,s.jsx)(d.z,{variant:"outline",className:"h-fit p-[7px] border-[#E6EAEF]",children:(0,s.jsx)(i.Z,{size:20,strokeWidth:1.5,color:"#667085"})}),(0,s.jsxs)(d.z,{variant:"outline",className:"h-fit py-[7px] px-[10px] border-[#E6EAEF] font-semibold text-[13px] text-[#344054] gap-1",children:[(0,s.jsx)(c.yP,{})," Mute"]}),(0,s.jsxs)(d.z,{variant:"outline",className:"h-fit py-[7px] px-[10px] border-[#E6EAEF] font-semibold text-[13px] text-[#344054] gap-1",children:[(0,s.jsx)(c.eh,{})," Hide"]}),(0,s.jsxs)(d.z,{variant:"outline",className:"h-fit py-[7px] px-[10px] border-[#E6EAEF] font-semibold text-[13px] text-[#344054] gap-1",children:[(0,s.jsx)(c._g,{})," View Files"]}),(0,s.jsx)(d.z,{variant:"outline",className:"h-fit p-[7px] border-[#E6EAEF] font-semibold text-[13px] text-[#344054] gap-1",children:(0,s.jsx)(c.TI,{})})]})]}),(0,s.jsx)("div",{className:"border-t border-[#E6EAEF]"}),(0,s.jsxs)("div",{className:"flex flex-col gap-2 mb-20 px-5",children:[(0,s.jsx)("h4",{className:"text-[15px] text-[#101828] font-bold",children:"Contact Information"}),(null==t?void 0:t.phone)&&(0,s.jsxs)("div",{className:"flex justify-between items-center gap-2",children:[(0,s.jsxs)("div",{className:"flex gap-[10px] items-center",children:[(0,s.jsx)(a.Z,{size:20,color:"#475467"}),(0,s.jsx)("span",{className:"text-sm text-[#6868F7]",children:null==t?void 0:t.phone}),(0,s.jsx)("span",{className:"text-sm text-[#667085]",children:"whatsapp only"})]}),(0,s.jsx)(p.r,{textToCopy:null==t?void 0:t.phone,children:(0,s.jsx)(c.TI,{})})]}),(null==t?void 0:t.email)&&(0,s.jsxs)("div",{className:"flex justify-between items-center gap-2",children:[(0,s.jsxs)("div",{className:"flex gap-[10px] items-center",children:[(0,s.jsx)(c.bV,{}),(0,s.jsx)("span",{className:"text-sm text-[#6868F7]",children:null==t?void 0:t.email})]}),(0,s.jsx)(p.r,{textToCopy:null==t?void 0:t.email,tooltipText:"Copied!",children:(0,s.jsx)(c.TI,{})})]})]})]})]})}},46569:function(e,t,l){"use strict";var s=l(47411),n=l(32486),i=l(97712),a=l(97220),o=l(86418);t.Z=()=>{var e;let t=(0,s.useParams)().id,{state:l,dispatch:r}=(0,n.useContext)(a.R),d=localStorage.getItem("token")||"",[c,u]=(0,n.useState)(1),[x,v]=(0,n.useState)(!0),[m,p]=(0,n.useState)(!0),h=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;try{var s,n,a;let d=await (0,o.Gl)("/dms/thread/".concat(null==l?void 0:null===(s=l.thread)||void 0===s?void 0:s.thread_id,"/channels/").concat(t,"?page=").concat(e,"&limit=30"));if((null==d?void 0:d.status)===200||(null==d?void 0:d.status)===201){let t=Array.isArray(null===(n=d.data)||void 0===n?void 0:n.data)?null===(a=d.data)||void 0===a?void 0:a.data:[];r({type:i.a.REPLIES,payload:{newThreads:t,newPage:e}}),e>1&&t.length>0?v(!0):v(t.length>=20)}p(!1)}catch(e){console.error("Error fetching threads:",e),v(!1)}finally{u(e)}};return(0,n.useEffect)(()=>{var e;(null==l?void 0:null===(e=l.thread)||void 0===e?void 0:e.thread_id)&&d&&h(1).finally(()=>r({type:i.a.MESSAGE_LOADING,payload:!1}))},[null==l?void 0:null===(e=l.thread)||void 0===e?void 0:e.thread_id,t,d,null==l?void 0:l.callback,r]),{fetchMoreData:()=>{x&&h(c+1)},hasMore:x,loading:m}}}}]);