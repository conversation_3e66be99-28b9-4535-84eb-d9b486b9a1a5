"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4144],{20100:function(e,n,t){t.d(n,{M:function(){return r}});function r(e,n,{checkForDefaultPrevented:t=!0}={}){return function(r){if(e?.(r),!1===t||!r.defaultPrevented)return n?.(r)}}},29626:function(e,n,t){t.d(n,{F:function(){return u},e:function(){return i}});var r=t(32486);function o(e,n){if("function"==typeof e)return e(n);null!=e&&(e.current=n)}function u(...e){return n=>{let t=!1,r=e.map(e=>{let r=o(e,n);return t||"function"!=typeof r||(t=!0),r});if(t)return()=>{for(let n=0;n<r.length;n++){let t=r[n];"function"==typeof t?t():o(e[n],null)}}}}function i(...e){return r.useCallback(u(...e),e)}},32192:function(e,n,t){t.d(n,{b:function(){return i},k:function(){return u}});var r=t(32486),o=t(75376);function u(e,n){let t=r.createContext(n),u=e=>{let{children:n,...u}=e,i=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(t.Provider,{value:i,children:n})};return u.displayName=e+"Provider",[u,function(o){let u=r.useContext(t);if(u)return u;if(void 0!==n)return n;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,n=[]){let t=[],u=()=>{let n=t.map(e=>r.createContext(e));return function(t){let o=t?.[e]||n;return r.useMemo(()=>({[`__scope${e}`]:{...t,[e]:o}}),[t,o])}};return u.scopeName=e,[function(n,u){let i=r.createContext(u),l=t.length;t=[...t,u];let c=n=>{let{scope:t,children:u,...c}=n,a=t?.[e]?.[l]||i,f=r.useMemo(()=>c,Object.values(c));return(0,o.jsx)(a.Provider,{value:f,children:u})};return c.displayName=n+"Provider",[c,function(t,o){let c=o?.[e]?.[l]||i,a=r.useContext(c);if(a)return a;if(void 0!==u)return u;throw Error(`\`${t}\` must be used within \`${n}\``)}]},function(...e){let n=e[0];if(1===e.length)return n;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=t.reduce((n,{useScope:t,scopeName:r})=>{let o=t(e)[`__scope${r}`];return{...n,...o}},{});return r.useMemo(()=>({[`__scope${n.scopeName}`]:o}),[o])}};return t.scopeName=n.scopeName,t}(u,...n)]}},53486:function(e,n,t){t.d(n,{z:function(){return i}});var r=t(32486),o=t(29626),u=t(79315),i=e=>{var n,t;let i,c;let{present:a,children:f}=e,s=function(e){var n,t;let[o,i]=r.useState(),c=r.useRef(null),a=r.useRef(e),f=r.useRef("none"),[s,d]=(n=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,n)=>{let r=t[e][n];return null!=r?r:e},n));return r.useEffect(()=>{let e=l(c.current);f.current="mounted"===s?e:"none"},[s]),(0,u.b)(()=>{let n=c.current,t=a.current;if(t!==e){let r=f.current,o=l(n);e?d("MOUNT"):"none"===o||(null==n?void 0:n.display)==="none"?d("UNMOUNT"):t&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),a.current=e}},[e,d]),(0,u.b)(()=>{if(o){var e;let n;let t=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=l(c.current).includes(e.animationName);if(e.target===o&&r&&(d("ANIMATION_END"),!a.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",n=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},u=e=>{e.target===o&&(f.current=l(c.current))};return o.addEventListener("animationstart",u),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(n),o.removeEventListener("animationstart",u),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:r.useCallback(e=>{c.current=e?getComputedStyle(e):null,i(e)},[])}}(a),d="function"==typeof f?f({present:s.isPresent}):r.Children.only(f),m=(0,o.e)(s.ref,(i=null===(n=Object.getOwnPropertyDescriptor(d.props,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in i&&i.isReactWarning?d.ref:(i=null===(t=Object.getOwnPropertyDescriptor(d,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in i&&i.isReactWarning?d.props.ref:d.props.ref||d.ref);return"function"==typeof f||s.isPresent?r.cloneElement(d,{ref:m}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},89801:function(e,n,t){t.d(n,{WV:function(){return l},jH:function(){return c}});var r=t(32486),o=t(54087),u=t(91007),i=t(75376),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,n)=>{let t=(0,u.Z8)(`Primitive.${n}`),o=r.forwardRef((e,r)=>{let{asChild:o,...u}=e,l=o?t:n;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l,{...u,ref:r})});return o.displayName=`Primitive.${n}`,{...e,[n]:o}},{});function c(e,n){e&&o.flushSync(()=>e.dispatchEvent(n))}},91007:function(e,n,t){t.d(n,{Z8:function(){return i},g7:function(){return l},sA:function(){return a}});var r=t(32486),o=t(29626),u=t(75376);function i(e){let n=function(e){let n=r.forwardRef((e,n)=>{let{children:t,...u}=e;if(r.isValidElement(t)){let e,i;let l=(e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.ref:(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.props.ref:t.props.ref||t.ref,c=function(e,n){let t={...n};for(let r in n){let o=e[r],u=n[r];/^on[A-Z]/.test(r)?o&&u?t[r]=(...e)=>{let n=u(...e);return o(...e),n}:o&&(t[r]=o):"style"===r?t[r]={...o,...u}:"className"===r&&(t[r]=[o,u].filter(Boolean).join(" "))}return{...e,...t}}(u,t.props);return t.type!==r.Fragment&&(c.ref=n?(0,o.F)(n,l):l),r.cloneElement(t,c)}return r.Children.count(t)>1?r.Children.only(null):null});return n.displayName=`${e}.SlotClone`,n}(e),t=r.forwardRef((e,t)=>{let{children:o,...i}=e,l=r.Children.toArray(o),c=l.find(f);if(c){let e=c.props.children,o=l.map(n=>n!==c?n:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,u.jsx)(n,{...i,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,u.jsx)(n,{...i,ref:t,children:o})});return t.displayName=`${e}.Slot`,t}var l=i("Slot"),c=Symbol("radix.slottable");function a(e){let n=({children:e})=>(0,u.jsx)(u.Fragment,{children:e});return n.displayName=`${e}.Slottable`,n.__radixId=c,n}function f(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}},31413:function(e,n,t){t.d(n,{T:function(){return l}});var r,o=t(32486),u=t(79315),i=(r||(r=t.t(o,2)))[" useInsertionEffect ".trim().toString()]||u.b;function l({prop:e,defaultProp:n,onChange:t=()=>{},caller:r}){let[u,l,c]=function({defaultProp:e,onChange:n}){let[t,r]=o.useState(e),u=o.useRef(t),l=o.useRef(n);return i(()=>{l.current=n},[n]),o.useEffect(()=>{u.current!==t&&(l.current?.(t),u.current=t)},[t,u]),[t,r,l]}({defaultProp:n,onChange:t}),a=void 0!==e,f=a?e:u;{let n=o.useRef(void 0!==e);o.useEffect(()=>{let e=n.current;if(e!==a){let n=a?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${n}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}n.current=a},[a,r])}return[f,o.useCallback(n=>{if(a){let t="function"==typeof n?n(e):n;t!==e&&c.current?.(t)}else l(n)},[a,e,l,c])]}Symbol("RADIX:SYNC_STATE")},79315:function(e,n,t){t.d(n,{b:function(){return o}});var r=t(32486),o=globalThis?.document?r.useLayoutEffect:()=>{}}}]);