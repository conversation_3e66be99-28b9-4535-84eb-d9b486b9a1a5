"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1790],{34980:function(e,t,n){let r;n.d(t,{LB:function(){return eH},y9:function(){return e7},g4:function(){return v},we:function(){return eb},pE:function(){return X},VK:function(){return V},Cj:function(){return eG},O1:function(){return eZ},Zj:function(){return eQ},VT:function(){return N},Dy:function(){return P}});var l,i,o,a,u,s,c,d,f,h,g,p,v,m,b,y,w,x,D,E=n(32486),C=n(54087),R=n(71714);let M={display:"none"};function S(e){let{id:t,value:n}=e;return E.createElement("div",{id:t,style:M},n)}function O(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;return E.createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":r,"aria-atomic":!0},n)}let k=(0,E.createContext)(null),I={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},j={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function L(e){let{announcements:t=j,container:n,hiddenTextDescribedById:r,screenReaderInstructions:l=I}=e,{announce:i,announcement:o}=function(){let[e,t]=(0,E.useState)("");return{announce:(0,E.useCallback)(e=>{null!=e&&t(e)},[]),announcement:e}}(),a=(0,R.Ld)("DndLiveRegion"),[u,s]=(0,E.useState)(!1);if((0,E.useEffect)(()=>{s(!0)},[]),!function(e){let t=(0,E.useContext)(k);(0,E.useEffect)(()=>{if(!t)throw Error("useDndMonitor must be used within a children of <DndContext>");return t(e)},[e,t])}((0,E.useMemo)(()=>({onDragStart(e){let{active:n}=e;i(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;t.onDragMove&&i(t.onDragMove({active:n,over:r}))},onDragOver(e){let{active:n,over:r}=e;i(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;i(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;i(t.onDragCancel({active:n,over:r}))}}),[i,t])),!u)return null;let c=E.createElement(E.Fragment,null,E.createElement(S,{id:r,value:l.draggable}),E.createElement(O,{id:a,announcement:o}));return n?(0,C.createPortal)(c,n):c}function T(){}function N(e,t){return(0,E.useMemo)(()=>({sensor:e,options:null!=t?t:{}}),[e,t])}function P(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,E.useMemo)(()=>[...t].filter(e=>null!=e),[...t])}(l=h||(h={})).DragStart="dragStart",l.DragMove="dragMove",l.DragEnd="dragEnd",l.DragCancel="dragCancel",l.DragOver="dragOver",l.RegisterDroppable="registerDroppable",l.SetDroppableDisabled="setDroppableDisabled",l.UnregisterDroppable="unregisterDroppable";let A=Object.freeze({x:0,y:0});function F(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function z(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function B(e,t,n){return void 0===t&&(t=e.left),void 0===n&&(n=e.top),{x:t+.5*e.width,y:n+.5*e.height}}let X=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=B(t,t.left,t.top),i=[];for(let e of r){let{id:t}=e,r=n.get(t);if(r){var o;let n=Math.sqrt(Math.pow((o=B(r)).x-l.x,2)+Math.pow(o.y-l.y,2));i.push({id:t,data:{droppableContainer:e,value:n}})}}return i.sort(F)},_=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=[];for(let e of r){let{id:r}=e,i=n.get(r);if(i){let n=function(e,t){let n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),l=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height);if(r<l&&n<i){let o=t.width*t.height,a=e.width*e.height,u=(l-r)*(i-n);return Number((u/(o+a-u)).toFixed(4))}return 0}(i,t);n>0&&l.push({id:r,data:{droppableContainer:e,value:n}})}}return l.sort(z)};function J(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:A}let W=function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return n.reduce((e,t)=>({...e,top:e.top+1*t.y,bottom:e.bottom+1*t.y,left:e.left+1*t.x,right:e.right+1*t.x}),{...e})};function Y(e){if(e.startsWith("matrix3d(")){let t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){let t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}let U={ignoreTransform:!1};function V(e,t){void 0===t&&(t=U);let n=e.getBoundingClientRect();if(t.ignoreTransform){let{transform:t,transformOrigin:r}=(0,R.Jj)(e).getComputedStyle(e);t&&(n=function(e,t,n){let r=Y(t);if(!r)return e;let{scaleX:l,scaleY:i,x:o,y:a}=r,u=e.left-o-(1-l)*parseFloat(n),s=e.top-a-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),c=l?e.width/l:e.width,d=i?e.height/i:e.height;return{width:c,height:d,top:s,right:u+c,bottom:s+d,left:u}}(n,t,r))}let{top:r,left:l,width:i,height:o,bottom:a,right:u}=n;return{top:r,left:l,width:i,height:o,bottom:a,right:u}}function H(e){return V(e,{ignoreTransform:!0})}function K(e,t){let n=[];return e?function r(l){var i;if(null!=t&&n.length>=t||!l)return n;if((0,R.qk)(l)&&null!=l.scrollingElement&&!n.includes(l.scrollingElement))return n.push(l.scrollingElement),n;if(!(0,R.Re)(l)||(0,R.vZ)(l)||n.includes(l))return n;let o=(0,R.Jj)(e).getComputedStyle(l);return(l!==e&&function(e,t){void 0===t&&(t=(0,R.Jj)(e).getComputedStyle(e));let n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(e=>{let r=t[e];return"string"==typeof r&&n.test(r)})}(l,o)&&n.push(l),void 0===(i=o)&&(i=(0,R.Jj)(l).getComputedStyle(l)),"fixed"===i.position)?n:r(l.parentNode)}(e):n}function q(e){let[t]=K(e,1);return null!=t?t:null}function Z(e){return R.Nq&&e?(0,R.FJ)(e)?e:(0,R.UG)(e)?(0,R.qk)(e)||e===(0,R.r3)(e).scrollingElement?window:(0,R.Re)(e)?e:null:null:null}function G(e){return(0,R.FJ)(e)?e.scrollX:e.scrollLeft}function $(e){return(0,R.FJ)(e)?e.scrollY:e.scrollTop}function Q(e){return{x:G(e),y:$(e)}}function ee(e){return!!R.Nq&&!!e&&e===document.scrollingElement}function et(e){let t={x:0,y:0},n=ee(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height},l=e.scrollTop<=t.y;return{isTop:l,isLeft:e.scrollLeft<=t.x,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}(i=g||(g={}))[i.Forward=1]="Forward",i[i.Backward=-1]="Backward";let en={x:.2,y:.2};function er(e){return e.reduce((e,t)=>(0,R.IH)(e,Q(t)),A)}function el(e,t){if(void 0===t&&(t=V),!e)return;let{top:n,left:r,bottom:l,right:i}=t(e);q(e)&&(l<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}let ei=[["x",["left","right"],function(e){return e.reduce((e,t)=>e+G(t),0)}],["y",["top","bottom"],function(e){return e.reduce((e,t)=>e+$(t),0)}]];class eo{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let n=K(t),r=er(n);for(let[t,l,i]of(this.rect={...e},this.width=e.width,this.height=e.height,ei))for(let e of l)Object.defineProperty(this,e,{get:()=>{let l=i(n),o=r[t]-l;return this.rect[e]+o},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class ea{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)})},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function eu(e,t){let n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}function es(e){e.preventDefault()}function ec(e){e.stopPropagation()}(o=p||(p={})).Click="click",o.DragStart="dragstart",o.Keydown="keydown",o.ContextMenu="contextmenu",o.Resize="resize",o.SelectionChange="selectionchange",o.VisibilityChange="visibilitychange",(a=v||(v={})).Space="Space",a.Down="ArrowDown",a.Right="ArrowRight",a.Left="ArrowLeft",a.Up="ArrowUp",a.Esc="Escape",a.Enter="Enter",a.Tab="Tab";let ed={start:[v.Space,v.Enter],cancel:[v.Esc],end:[v.Space,v.Enter,v.Tab]},ef=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case v.Right:return{...n,x:n.x+25};case v.Left:return{...n,x:n.x-25};case v.Down:return{...n,y:n.y+25};case v.Up:return{...n,y:n.y-25}}};class eh{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;let{event:{target:t}}=e;this.props=e,this.listeners=new ea((0,R.r3)(t)),this.windowListeners=new ea((0,R.Jj)(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(p.Resize,this.handleCancel),this.windowListeners.add(p.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(p.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&el(n),t(A)}handleKeyDown(e){if((0,R.vd)(e)){let{active:t,context:n,options:r}=this.props,{keyboardCodes:l=ed,coordinateGetter:i=ef,scrollBehavior:o="smooth"}=r,{code:a}=e;if(l.end.includes(a)){this.handleEnd(e);return}if(l.cancel.includes(a)){this.handleCancel(e);return}let{collisionRect:u}=n.current,s=u?{x:u.left,y:u.top}:A;this.referenceCoordinates||(this.referenceCoordinates=s);let c=i(e,{active:t,context:n.current,currentCoordinates:s});if(c){let t=(0,R.$X)(c,s),r={x:0,y:0},{scrollableAncestors:l}=n.current;for(let n of l){let l=e.code,{isTop:i,isRight:a,isLeft:u,isBottom:s,maxScroll:d,minScroll:f}=et(n),h=function(e){if(e===document.scrollingElement){let{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}let{top:t,left:n,right:r,bottom:l}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:l,width:e.clientWidth,height:e.clientHeight}}(n),g={x:Math.min(l===v.Right?h.right-h.width/2:h.right,Math.max(l===v.Right?h.left:h.left+h.width/2,c.x)),y:Math.min(l===v.Down?h.bottom-h.height/2:h.bottom,Math.max(l===v.Down?h.top:h.top+h.height/2,c.y))},p=l===v.Right&&!a||l===v.Left&&!u,m=l===v.Down&&!s||l===v.Up&&!i;if(p&&g.x!==c.x){let e=n.scrollLeft+t.x,i=l===v.Right&&e<=d.x||l===v.Left&&e>=f.x;if(i&&!t.y){n.scrollTo({left:e,behavior:o});return}i?r.x=n.scrollLeft-e:r.x=l===v.Right?n.scrollLeft-d.x:n.scrollLeft-f.x,r.x&&n.scrollBy({left:-r.x,behavior:o});break}if(m&&g.y!==c.y){let e=n.scrollTop+t.y,i=l===v.Down&&e<=d.y||l===v.Up&&e>=f.y;if(i&&!t.x){n.scrollTo({top:e,behavior:o});return}i?r.y=n.scrollTop-e:r.y=l===v.Down?n.scrollTop-d.y:n.scrollTop-f.y,r.y&&n.scrollBy({top:-r.y,behavior:o});break}}this.handleMove(e,(0,R.IH)((0,R.$X)(c,this.referenceCoordinates),r))}}}handleMove(e,t){let{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){let{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){let{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function eg(e){return!!(e&&"distance"in e)}function ep(e){return!!(e&&"delay"in e)}eh.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=ed,onActivation:l}=t,{active:i}=n,{code:o}=e.nativeEvent;if(r.start.includes(o)){let t=i.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==l||l({event:e.nativeEvent}),!0)}return!1}}];class ev{constructor(e,t,n){var r;void 0===n&&(n=function(e){let{EventTarget:t}=(0,R.Jj)(e);return e instanceof t?e:(0,R.r3)(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;let{event:l}=e,{target:i}=l;this.props=e,this.events=t,this.document=(0,R.r3)(i),this.documentListeners=new ea(this.document),this.listeners=new ea(n),this.windowListeners=new ea((0,R.Jj)(i)),this.initialCoordinates=null!=(r=(0,R.DC)(l))?r:A,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(p.Resize,this.handleCancel),this.windowListeners.add(p.DragStart,es),this.windowListeners.add(p.VisibilityChange,this.handleCancel),this.windowListeners.add(p.ContextMenu,es),this.documentListeners.add(p.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(ep(t)){this.timeoutId=setTimeout(this.handleStart,t.delay),this.handlePending(t);return}if(eg(t)){this.handlePending(t);return}}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){let{active:n,onPending:r}=this.props;r(n,e,this.initialCoordinates,t)}handleStart(){let{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(p.Click,ec,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(p.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;let{activated:n,initialCoordinates:r,props:l}=this,{onMove:i,options:{activationConstraint:o}}=l;if(!r)return;let a=null!=(t=(0,R.DC)(e))?t:A,u=(0,R.$X)(r,a);if(!n&&o){if(eg(o)){if(null!=o.tolerance&&eu(u,o.tolerance))return this.handleCancel();if(eu(u,o.distance))return this.handleStart()}return ep(o)&&eu(u,o.tolerance)?this.handleCancel():void this.handlePending(o,u)}e.cancelable&&e.preventDefault(),i(a)}handleEnd(){let{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){let{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===v.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}let em={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class eb extends ev{constructor(e){let{event:t}=e;super(e,em,(0,R.r3)(t.target))}}eb.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!!n.isPrimary&&0===n.button&&(null==r||r({event:n}),!0)}}];let ey={move:{name:"mousemove"},end:{name:"mouseup"}};(u=m||(m={}))[u.RightClick=2]="RightClick";class ew extends ev{constructor(e){super(e,ey,(0,R.r3)(e.event.target))}}ew.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==m.RightClick&&(null==r||r({event:n}),!0)}}];let ex={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class eD extends ev{constructor(e){super(e,ex)}static setup(){return window.addEventListener(ex.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(ex.move.name,e)};function e(){}}}eD.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t,{touches:l}=n;return!(l.length>1)&&(null==r||r({event:n}),!0)}}],(s=b||(b={}))[s.Pointer=0]="Pointer",s[s.DraggableRect=1]="DraggableRect",(c=y||(y={}))[c.TreeOrder=0]="TreeOrder",c[c.ReversedTreeOrder=1]="ReversedTreeOrder";let eE={x:{[g.Backward]:!1,[g.Forward]:!1},y:{[g.Backward]:!1,[g.Forward]:!1}};(d=w||(w={}))[d.Always=0]="Always",d[d.BeforeDragging=1]="BeforeDragging",d[d.WhileDragging=2]="WhileDragging",(x||(x={})).Optimized="optimized";let eC=new Map;function eR(e,t){return(0,R.Gj)(n=>e?n||("function"==typeof t?t(e):e):null,[t,e])}function eM(e){let{callback:t,disabled:n}=e,r=(0,R.zX)(t),l=(0,E.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:e}=window;return new e(r)},[n]);return(0,E.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}function eS(e){return new eo(V(e),e)}function eO(e,t,n){void 0===t&&(t=eS);let[r,l]=(0,E.useState)(null);function i(){l(r=>{if(!e)return null;if(!1===e.isConnected){var l;return null!=(l=null!=r?r:n)?l:null}let i=t(e);return JSON.stringify(r)===JSON.stringify(i)?r:i})}let o=function(e){let{callback:t,disabled:n}=e,r=(0,R.zX)(t),l=(0,E.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:e}=window;return new e(r)},[r,n]);return(0,E.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}({callback(t){if(e)for(let n of t){let{type:t,target:r}=n;if("childList"===t&&r instanceof HTMLElement&&r.contains(e)){i();break}}}}),a=eM({callback:i});return(0,R.LI)(()=>{i(),e?(null==a||a.observe(e),null==o||o.observe(document.body,{childList:!0,subtree:!0})):(null==a||a.disconnect(),null==o||o.disconnect())},[e]),r}let ek=[];function eI(e,t){void 0===t&&(t=[]);let n=(0,E.useRef)(null);return(0,E.useEffect)(()=>{n.current=null},t),(0,E.useEffect)(()=>{let t=e!==A;t&&!n.current&&(n.current=e),!t&&n.current&&(n.current=null)},[e]),n.current?(0,R.$X)(e,n.current):A}function ej(e){return(0,E.useMemo)(()=>e?function(e){let t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(e):null,[e])}let eL=[];function eT(e){if(!e)return null;if(e.children.length>1)return e;let t=e.children[0];return(0,R.Re)(t)?t:e}let eN=[{sensor:eb,options:{}},{sensor:eh,options:{}}],eP={current:{}},eA={draggable:{measure:H},droppable:{measure:H,strategy:w.WhileDragging,frequency:x.Optimized},dragOverlay:{measure:V}};class eF extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(e=>{let{disabled:t}=e;return!t})}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}let ez={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new eF,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:T},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:eA,measureDroppableContainers:T,windowRect:null,measuringScheduled:!1},eB={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:T,draggableNodes:new Map,over:null,measureDroppableContainers:T},eX=(0,E.createContext)(eB),e_=(0,E.createContext)(ez);function eJ(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new eF}}}function eW(e,t){switch(t.type){case h.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case h.DragMove:if(null==e.draggable.active)return e;return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case h.DragEnd:case h.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case h.RegisterDroppable:{let{element:n}=t,{id:r}=n,l=new eF(e.droppable.containers);return l.set(r,n),{...e,droppable:{...e.droppable,containers:l}}}case h.SetDroppableDisabled:{let{id:n,key:r,disabled:l}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;let o=new eF(e.droppable.containers);return o.set(n,{...i,disabled:l}),{...e,droppable:{...e.droppable,containers:o}}}case h.UnregisterDroppable:{let{id:n,key:r}=t,l=e.droppable.containers.get(n);if(!l||r!==l.key)return e;let i=new eF(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function eY(e){let{disabled:t}=e,{active:n,activatorEvent:r,draggableNodes:l}=(0,E.useContext)(eX),i=(0,R.D9)(r),o=(0,R.D9)(null==n?void 0:n.id);return(0,E.useEffect)(()=>{if(!t&&!r&&i&&null!=o){if(!(0,R.vd)(i)||document.activeElement===i.target)return;let e=l.get(o);if(!e)return;let{activatorNode:t,node:n}=e;(t.current||n.current)&&requestAnimationFrame(()=>{for(let e of[t.current,n.current]){if(!e)continue;let t=(0,R.so)(e);if(t){t.focus();break}}})}},[r,t,l,o,i]),null}function eU(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce((e,t)=>t({transform:e,...r}),n):n}let eV=(0,E.createContext)({...A,scaleX:1,scaleY:1});(f=D||(D={}))[f.Uninitialized=0]="Uninitialized",f[f.Initializing=1]="Initializing",f[f.Initialized=2]="Initialized";let eH=(0,E.memo)(function(e){var t,n,r,l,i,o;let{id:a,accessibility:u,autoScroll:s=!0,children:c,sensors:d=eN,collisionDetection:f=_,measuring:p,modifiers:v,...m}=e,[x,M]=(0,E.useReducer)(eW,void 0,eJ),[S,O]=function(){let[e]=(0,E.useState)(()=>new Set),t=(0,E.useCallback)(t=>(e.add(t),()=>e.delete(t)),[e]);return[(0,E.useCallback)(t=>{let{type:n,event:r}=t;e.forEach(e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)})},[e]),t]}(),[I,j]=(0,E.useState)(D.Uninitialized),T=I===D.Initialized,{draggable:{active:N,nodes:P,translate:F},droppable:{containers:z}}=x,B=null!=N?P.get(N):null,X=(0,E.useRef)({initial:null,translated:null}),Y=(0,E.useMemo)(()=>{var e;return null!=N?{id:N,data:null!=(e=null==B?void 0:B.data)?e:eP,rect:X}:null},[N,B]),U=(0,E.useRef)(null),[H,G]=(0,E.useState)(null),[$,el]=(0,E.useState)(null),ei=(0,R.Ey)(m,Object.values(m)),ea=(0,R.Ld)("DndDescribedBy",a),eu=(0,E.useMemo)(()=>z.getEnabled(),[z]),es=(0,E.useMemo)(()=>({draggable:{...eA.draggable,...null==p?void 0:p.draggable},droppable:{...eA.droppable,...null==p?void 0:p.droppable},dragOverlay:{...eA.dragOverlay,...null==p?void 0:p.dragOverlay}}),[null==p?void 0:p.draggable,null==p?void 0:p.droppable,null==p?void 0:p.dragOverlay]),{droppableRects:ec,measureDroppableContainers:ed,measuringScheduled:ef}=function(e,t){let{dragging:n,dependencies:r,config:l}=t,[i,o]=(0,E.useState)(null),{frequency:a,measure:u,strategy:s}=l,c=(0,E.useRef)(e),d=function(){switch(s){case w.Always:return!1;case w.BeforeDragging:return n;default:return!n}}(),f=(0,R.Ey)(d),h=(0,E.useCallback)(function(e){void 0===e&&(e=[]),f.current||o(t=>null===t?e:t.concat(e.filter(e=>!t.includes(e))))},[f]),g=(0,E.useRef)(null),p=(0,R.Gj)(t=>{if(d&&!n)return eC;if(!t||t===eC||c.current!==e||null!=i){let t=new Map;for(let n of e){if(!n)continue;if(i&&i.length>0&&!i.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}let e=n.node.current,r=e?new eo(u(e),e):null;n.rect.current=r,r&&t.set(n.id,r)}return t}return t},[e,i,n,d,u]);return(0,E.useEffect)(()=>{c.current=e},[e]),(0,E.useEffect)(()=>{d||h()},[n,d]),(0,E.useEffect)(()=>{i&&i.length>0&&o(null)},[JSON.stringify(i)]),(0,E.useEffect)(()=>{d||"number"!=typeof a||null!==g.current||(g.current=setTimeout(()=>{h(),g.current=null},a))},[a,d,h,...r]),{droppableRects:p,measureDroppableContainers:h,measuringScheduled:null!=i}}(eu,{dragging:T,dependencies:[F.x,F.y],config:es.droppable}),eh=function(e,t){let n=null!=t?e.get(t):void 0,r=n?n.node.current:null;return(0,R.Gj)(e=>{var n;return null==t?null:null!=(n=null!=r?r:e)?n:null},[r,t])}(P,N),eg=(0,E.useMemo)(()=>$?(0,R.DC)($):null,[$]),ep=function(){let e=(null==H?void 0:H.autoScrollEnabled)===!1,t="object"==typeof s?!1===s.enabled:!1===s,n=T&&!e&&!t;return"object"==typeof s?{...s,enabled:n}:{enabled:n}}(),ev=eR(eh,es.draggable.measure);!function(e){let{activeNode:t,measure:n,initialRect:r,config:l=!0}=e,i=(0,E.useRef)(!1),{x:o,y:a}="boolean"==typeof l?{x:l,y:l}:l;(0,R.LI)(()=>{if(!o&&!a||!t){i.current=!1;return}if(i.current||!r)return;let e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;let l=J(n(e),r);if(o||(l.x=0),a||(l.y=0),i.current=!0,Math.abs(l.x)>0||Math.abs(l.y)>0){let t=q(e);t&&t.scrollBy({top:l.y,left:l.x})}},[t,o,a,r,n])}({activeNode:null!=N?P.get(N):null,config:ep.layoutShiftCompensation,initialRect:ev,measure:es.draggable.measure});let em=eO(eh,es.draggable.measure,ev),eb=eO(eh?eh.parentElement:null),ey=(0,E.useRef)({activatorEvent:null,active:null,activeNode:eh,collisionRect:null,collisions:null,droppableRects:ec,draggableNodes:P,draggingNode:null,draggingNodeRect:null,droppableContainers:z,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),ew=z.getNodeFor(null==(t=ey.current.over)?void 0:t.id),ex=function(e){let{measure:t}=e,[n,r]=(0,E.useState)(null),l=eM({callback:(0,E.useCallback)(e=>{for(let{target:n}of e)if((0,R.Re)(n)){r(e=>{let r=t(n);return e?{...e,width:r.width,height:r.height}:r});break}},[t])}),i=(0,E.useCallback)(e=>{let n=eT(e);null==l||l.disconnect(),n&&(null==l||l.observe(n)),r(n?t(n):null)},[t,l]),[o,a]=(0,R.wm)(i);return(0,E.useMemo)(()=>({nodeRef:o,rect:n,setRef:a}),[n,o,a])}({measure:es.dragOverlay.measure}),eD=null!=(n=ex.nodeRef.current)?n:eh,eS=T?null!=(r=ex.rect)?r:em:null,eF=!!(ex.nodeRef.current&&ex.rect),ez=function(e){let t=eR(e);return J(e,t)}(eF?null:em),eB=ej(eD?(0,R.Jj)(eD):null),eH=function(e){let t=(0,E.useRef)(e),n=(0,R.Gj)(n=>e?n&&n!==ek&&e&&t.current&&e.parentNode===t.current.parentNode?n:K(e):ek,[e]);return(0,E.useEffect)(()=>{t.current=e},[e]),n}(T?null!=ew?ew:eh:null),eK=function(e,t){void 0===t&&(t=V);let[n]=e,r=ej(n?(0,R.Jj)(n):null),[l,i]=(0,E.useState)(eL);function o(){i(()=>e.length?e.map(e=>ee(e)?r:new eo(t(e),e)):eL)}let a=eM({callback:o});return(0,R.LI)(()=>{null==a||a.disconnect(),o(),e.forEach(e=>null==a?void 0:a.observe(e))},[e]),l}(eH),eq=eU(v,{transform:{x:F.x-ez.x,y:F.y-ez.y,scaleX:1,scaleY:1},activatorEvent:$,active:Y,activeNodeRect:em,containerNodeRect:eb,draggingNodeRect:eS,over:ey.current.over,overlayNodeRect:ex.rect,scrollableAncestors:eH,scrollableAncestorRects:eK,windowRect:eB}),eZ=eg?(0,R.IH)(eg,F):null,eG=function(e){let[t,n]=(0,E.useState)(null),r=(0,E.useRef)(e),l=(0,E.useCallback)(e=>{let t=Z(e.target);t&&n(e=>e?(e.set(t,Q(t)),new Map(e)):null)},[]);return(0,E.useEffect)(()=>{let t=r.current;if(e!==t){i(t);let o=e.map(e=>{let t=Z(e);return t?(t.addEventListener("scroll",l,{passive:!0}),[t,Q(t)]):null}).filter(e=>null!=e);n(o.length?new Map(o):null),r.current=e}return()=>{i(e),i(t)};function i(e){e.forEach(e=>{let t=Z(e);null==t||t.removeEventListener("scroll",l)})}},[l,e]),(0,E.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((e,t)=>(0,R.IH)(e,t),A):er(e):A,[e,t])}(eH),e$=eI(eG),eQ=eI(eG,[em]),e0=(0,R.IH)(eq,e$),e1=eS?W(eS,eq):null,e2=Y&&e1?f({active:Y,collisionRect:e1,droppableRects:ec,droppableContainers:eu,pointerCoordinates:eZ}):null,e8=function(e,t){if(!e||0===e.length)return null;let[n]=e;return n.id}(e2,0),[e3,e4]=(0,E.useState)(null),e9=(i=eF?eq:(0,R.IH)(eq,eQ),o=null!=(l=null==e3?void 0:e3.rect)?l:null,{...i,scaleX:o&&em?o.width/em.width:1,scaleY:o&&em?o.height/em.height:1}),e5=(0,E.useRef)(null),e7=(0,E.useCallback)((e,t)=>{let{sensor:n,options:r}=t;if(null==U.current)return;let l=P.get(U.current);if(!l)return;let i=e.nativeEvent,o=new n({active:U.current,activeNode:l,event:i,options:r,context:ey,onAbort(e){if(!P.get(e))return;let{onDragAbort:t}=ei.current,n={id:e};null==t||t(n),S({type:"onDragAbort",event:n})},onPending(e,t,n,r){if(!P.get(e))return;let{onDragPending:l}=ei.current,i={id:e,constraint:t,initialCoordinates:n,offset:r};null==l||l(i),S({type:"onDragPending",event:i})},onStart(e){let t=U.current;if(null==t)return;let n=P.get(t);if(!n)return;let{onDragStart:r}=ei.current,l={activatorEvent:i,active:{id:t,data:n.data,rect:X}};(0,C.unstable_batchedUpdates)(()=>{null==r||r(l),j(D.Initializing),M({type:h.DragStart,initialCoordinates:e,active:t}),S({type:"onDragStart",event:l}),G(e5.current),el(i)})},onMove(e){M({type:h.DragMove,coordinates:e})},onEnd:a(h.DragEnd),onCancel:a(h.DragCancel)});function a(e){return async function(){let{active:t,collisions:n,over:r,scrollAdjustedTranslate:l}=ey.current,o=null;if(t&&l){let{cancelDrop:a}=ei.current;o={activatorEvent:i,active:t,collisions:n,delta:l,over:r},e===h.DragEnd&&"function"==typeof a&&await Promise.resolve(a(o))&&(e=h.DragCancel)}U.current=null,(0,C.unstable_batchedUpdates)(()=>{M({type:e}),j(D.Uninitialized),e4(null),G(null),el(null),e5.current=null;let t=e===h.DragEnd?"onDragEnd":"onDragCancel";if(o){let e=ei.current[t];null==e||e(o),S({type:t,event:o})}})}}e5.current=o},[P]),e6=(0,E.useCallback)((e,t)=>(n,r)=>{let l=n.nativeEvent,i=P.get(r);null!==U.current||!i||l.dndKit||l.defaultPrevented||!0!==e(n,t.options,{active:i})||(l.dndKit={capturedBy:t.sensor},U.current=r,e7(n,t))},[P,e7]),te=(0,E.useMemo)(()=>d.reduce((e,t)=>{let{sensor:n}=t;return[...e,...n.activators.map(e=>({eventName:e.eventName,handler:e6(e.handler,t)}))]},[]),[d,e6]);(0,E.useEffect)(()=>{if(!R.Nq)return;let e=d.map(e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()});return()=>{for(let t of e)null==t||t()}},d.map(e=>{let{sensor:t}=e;return t})),(0,R.LI)(()=>{em&&I===D.Initializing&&j(D.Initialized)},[em,I]),(0,E.useEffect)(()=>{let{onDragMove:e}=ei.current,{active:t,activatorEvent:n,collisions:r,over:l}=ey.current;if(!t||!n)return;let i={active:t,activatorEvent:n,collisions:r,delta:{x:e0.x,y:e0.y},over:l};(0,C.unstable_batchedUpdates)(()=>{null==e||e(i),S({type:"onDragMove",event:i})})},[e0.x,e0.y]),(0,E.useEffect)(()=>{let{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:l}=ey.current;if(!e||null==U.current||!t||!l)return;let{onDragOver:i}=ei.current,o=r.get(e8),a=o&&o.rect.current?{id:o.id,rect:o.rect.current,data:o.data,disabled:o.disabled}:null,u={active:e,activatorEvent:t,collisions:n,delta:{x:l.x,y:l.y},over:a};(0,C.unstable_batchedUpdates)(()=>{e4(a),null==i||i(u),S({type:"onDragOver",event:u})})},[e8]),(0,R.LI)(()=>{ey.current={activatorEvent:$,active:Y,activeNode:eh,collisionRect:e1,collisions:e2,droppableRects:ec,draggableNodes:P,draggingNode:eD,draggingNodeRect:eS,droppableContainers:z,over:e3,scrollableAncestors:eH,scrollAdjustedTranslate:e0},X.current={initial:eS,translated:e1}},[Y,eh,e2,e1,P,eD,eS,ec,z,e3,eH,e0]),function(e){let{acceleration:t,activator:n=b.Pointer,canScroll:r,draggingRect:l,enabled:i,interval:o=5,order:a=y.TreeOrder,pointerCoordinates:u,scrollableAncestors:s,scrollableAncestorRects:c,delta:d,threshold:f}=e,h=function(e){let{delta:t,disabled:n}=e,r=(0,R.D9)(t);return(0,R.Gj)(e=>{if(n||!r||!e)return eE;let l={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[g.Backward]:e.x[g.Backward]||-1===l.x,[g.Forward]:e.x[g.Forward]||1===l.x},y:{[g.Backward]:e.y[g.Backward]||-1===l.y,[g.Forward]:e.y[g.Forward]||1===l.y}}},[n,t,r])}({delta:d,disabled:!i}),[p,v]=(0,R.Yz)(),m=(0,E.useRef)({x:0,y:0}),w=(0,E.useRef)({x:0,y:0}),x=(0,E.useMemo)(()=>{switch(n){case b.Pointer:return u?{top:u.y,bottom:u.y,left:u.x,right:u.x}:null;case b.DraggableRect:return l}},[n,l,u]),D=(0,E.useRef)(null),C=(0,E.useCallback)(()=>{let e=D.current;if(!e)return;let t=m.current.x*w.current.x,n=m.current.y*w.current.y;e.scrollBy(t,n)},[]),M=(0,E.useMemo)(()=>a===y.TreeOrder?[...s].reverse():s,[a,s]);(0,E.useEffect)(()=>{if(!i||!s.length||!x){v();return}for(let e of M){if((null==r?void 0:r(e))===!1)continue;let n=c[s.indexOf(e)];if(!n)continue;let{direction:l,speed:i}=function(e,t,n,r,l){let{top:i,left:o,right:a,bottom:u}=n;void 0===r&&(r=10),void 0===l&&(l=en);let{isTop:s,isBottom:c,isLeft:d,isRight:f}=et(e),h={x:0,y:0},p={x:0,y:0},v={height:t.height*l.y,width:t.width*l.x};return!s&&i<=t.top+v.height?(h.y=g.Backward,p.y=r*Math.abs((t.top+v.height-i)/v.height)):!c&&u>=t.bottom-v.height&&(h.y=g.Forward,p.y=r*Math.abs((t.bottom-v.height-u)/v.height)),!f&&a>=t.right-v.width?(h.x=g.Forward,p.x=r*Math.abs((t.right-v.width-a)/v.width)):!d&&o<=t.left+v.width&&(h.x=g.Backward,p.x=r*Math.abs((t.left+v.width-o)/v.width)),{direction:h,speed:p}}(e,n,x,t,f);for(let e of["x","y"])h[e][l[e]]||(i[e]=0,l[e]=0);if(i.x>0||i.y>0){v(),D.current=e,p(C,o),m.current=i,w.current=l;return}}m.current={x:0,y:0},w.current={x:0,y:0},v()},[t,C,r,v,i,o,JSON.stringify(x),JSON.stringify(h),p,s,M,c,JSON.stringify(f)])}({...ep,delta:F,draggingRect:e1,pointerCoordinates:eZ,scrollableAncestors:eH,scrollableAncestorRects:eK});let tt=(0,E.useMemo)(()=>({active:Y,activeNode:eh,activeNodeRect:em,activatorEvent:$,collisions:e2,containerNodeRect:eb,dragOverlay:ex,draggableNodes:P,droppableContainers:z,droppableRects:ec,over:e3,measureDroppableContainers:ed,scrollableAncestors:eH,scrollableAncestorRects:eK,measuringConfiguration:es,measuringScheduled:ef,windowRect:eB}),[Y,eh,em,$,e2,eb,ex,P,z,ec,e3,ed,eH,eK,es,ef,eB]),tn=(0,E.useMemo)(()=>({activatorEvent:$,activators:te,active:Y,activeNodeRect:em,ariaDescribedById:{draggable:ea},dispatch:M,draggableNodes:P,over:e3,measureDroppableContainers:ed}),[$,te,Y,em,M,ea,P,e3,ed]);return E.createElement(k.Provider,{value:O},E.createElement(eX.Provider,{value:tn},E.createElement(e_.Provider,{value:tt},E.createElement(eV.Provider,{value:e9},c)),E.createElement(eY,{disabled:(null==u?void 0:u.restoreFocus)===!1})),E.createElement(L,{...u,hiddenTextDescribedById:ea}))}),eK=(0,E.createContext)(null),eq="button";function eZ(e){let{id:t,data:n,disabled:r=!1,attributes:l}=e,i=(0,R.Ld)("Draggable"),{activators:o,activatorEvent:a,active:u,activeNodeRect:s,ariaDescribedById:c,draggableNodes:d,over:f}=(0,E.useContext)(eX),{role:h=eq,roleDescription:g="draggable",tabIndex:p=0}=null!=l?l:{},v=(null==u?void 0:u.id)===t,m=(0,E.useContext)(v?eV:eK),[b,y]=(0,R.wm)(),[w,x]=(0,R.wm)(),D=(0,E.useMemo)(()=>o.reduce((e,n)=>{let{eventName:r,handler:l}=n;return e[r]=e=>{l(e,t)},e},{}),[o,t]),C=(0,R.Ey)(n);return(0,R.LI)(()=>(d.set(t,{id:t,key:i,node:b,activatorNode:w,data:C}),()=>{let e=d.get(t);e&&e.key===i&&d.delete(t)}),[d,t]),{active:u,activatorEvent:a,activeNodeRect:s,attributes:(0,E.useMemo)(()=>({role:h,tabIndex:p,"aria-disabled":r,"aria-pressed":!!v&&h===eq||void 0,"aria-roledescription":g,"aria-describedby":c.draggable}),[r,h,p,v,g,c.draggable]),isDragging:v,listeners:r?void 0:D,node:b,over:f,setNodeRef:y,setActivatorNodeRef:x,transform:m}}function eG(){return(0,E.useContext)(e_)}let e$={timeout:25};function eQ(e){let{data:t,disabled:n=!1,id:r,resizeObserverConfig:l}=e,i=(0,R.Ld)("Droppable"),{active:o,dispatch:a,over:u,measureDroppableContainers:s}=(0,E.useContext)(eX),c=(0,E.useRef)({disabled:n}),d=(0,E.useRef)(!1),f=(0,E.useRef)(null),g=(0,E.useRef)(null),{disabled:p,updateMeasurementsFor:v,timeout:m}={...e$,...l},b=(0,R.Ey)(null!=v?v:r),y=eM({callback:(0,E.useCallback)(()=>{if(!d.current){d.current=!0;return}null!=g.current&&clearTimeout(g.current),g.current=setTimeout(()=>{s(Array.isArray(b.current)?b.current:[b.current]),g.current=null},m)},[m]),disabled:p||!o}),w=(0,E.useCallback)((e,t)=>{y&&(t&&(y.unobserve(t),d.current=!1),e&&y.observe(e))},[y]),[x,D]=(0,R.wm)(w),C=(0,R.Ey)(t);return(0,E.useEffect)(()=>{y&&x.current&&(y.disconnect(),d.current=!1,y.observe(x.current))},[x,y]),(0,E.useEffect)(()=>(a({type:h.RegisterDroppable,element:{id:r,key:i,disabled:n,node:x,rect:f,data:C}}),()=>a({type:h.UnregisterDroppable,key:i,id:r})),[r]),(0,E.useEffect)(()=>{n!==c.current.disabled&&(a({type:h.SetDroppableDisabled,id:r,key:i,disabled:n}),c.current.disabled=n)},[r,i,n,a]),{active:o,rect:f,isOver:(null==u?void 0:u.id)===r,node:x,over:u,setNodeRef:D}}function e0(e){let{animation:t,children:n}=e,[r,l]=(0,E.useState)(null),[i,o]=(0,E.useState)(null),a=(0,R.D9)(n);return n||r||!a||l(a),(0,R.LI)(()=>{if(!i)return;let e=null==r?void 0:r.key,n=null==r?void 0:r.props.id;if(null==e||null==n){l(null);return}Promise.resolve(t(n,i)).then(()=>{l(null)})},[t,r,i]),E.createElement(E.Fragment,null,n,r?(0,E.cloneElement)(r,{ref:o}):null)}let e1={x:0,y:0,scaleX:1,scaleY:1};function e2(e){let{children:t}=e;return E.createElement(eX.Provider,{value:eB},E.createElement(eV.Provider,{value:e1},t))}let e8={position:"fixed",touchAction:"none"},e3=e=>(0,R.vd)(e)?"transform 250ms ease":void 0,e4=(0,E.forwardRef)((e,t)=>{let{as:n,activatorEvent:r,adjustScale:l,children:i,className:o,rect:a,style:u,transform:s,transition:c=e3}=e;if(!a)return null;let d=l?s:{...s,scaleX:1,scaleY:1},f={...e8,width:a.width,height:a.height,top:a.top,left:a.left,transform:R.ux.Transform.toString(d),transformOrigin:l&&r?function(e,t){let n=(0,R.DC)(e);if(!n)return"0 0";let r={x:(n.x-t.left)/t.width*100,y:(n.y-t.top)/t.height*100};return r.x+"% "+r.y+"%"}(r,a):void 0,transition:"function"==typeof c?c(r):c,...u};return E.createElement(n,{className:o,style:f,ref:t},i)}),e9={duration:250,easing:"ease",keyframes:e=>{let{transform:{initial:t,final:n}}=e;return[{transform:R.ux.Transform.toString(t)},{transform:R.ux.Transform.toString(n)}]},sideEffects:(r={styles:{active:{opacity:"0"}}},e=>{let{active:t,dragOverlay:n}=e,l={},{styles:i,className:o}=r;if(null!=i&&i.active)for(let[e,n]of Object.entries(i.active))void 0!==n&&(l[e]=t.node.style.getPropertyValue(e),t.node.style.setProperty(e,n));if(null!=i&&i.dragOverlay)for(let[e,t]of Object.entries(i.dragOverlay))void 0!==t&&n.node.style.setProperty(e,t);return null!=o&&o.active&&t.node.classList.add(o.active),null!=o&&o.dragOverlay&&n.node.classList.add(o.dragOverlay),function(){for(let[e,n]of Object.entries(l))t.node.style.setProperty(e,n);null!=o&&o.active&&t.node.classList.remove(o.active)}})},e5=0,e7=E.memo(e=>{var t;let{adjustScale:n=!1,children:r,dropAnimation:l,style:i,transition:o,modifiers:a,wrapperElement:u="div",className:s,zIndex:c=999}=e,{activatorEvent:d,active:f,activeNodeRect:h,containerNodeRect:g,draggableNodes:p,droppableContainers:v,dragOverlay:m,over:b,measuringConfiguration:y,scrollableAncestors:w,scrollableAncestorRects:x,windowRect:D}=eG(),C=(0,E.useContext)(eV),M=(t=null==f?void 0:f.id,(0,E.useMemo)(()=>{if(null!=t)return++e5},[t])),S=eU(a,{activatorEvent:d,active:f,activeNodeRect:h,containerNodeRect:g,draggingNodeRect:m.rect,over:b,overlayNodeRect:m.rect,scrollableAncestors:w,scrollableAncestorRects:x,transform:C,windowRect:D}),O=eR(h),k=function(e){let{config:t,draggableNodes:n,droppableContainers:r,measuringConfiguration:l}=e;return(0,R.zX)((e,i)=>{if(null===t)return;let o=n.get(e);if(!o)return;let a=o.node.current;if(!a)return;let u=eT(i);if(!u)return;let{transform:s}=(0,R.Jj)(i).getComputedStyle(i),c=Y(s);if(!c)return;let d="function"==typeof t?t:function(e){let{duration:t,easing:n,sideEffects:r,keyframes:l}={...e9,...e};return e=>{let{active:i,dragOverlay:o,transform:a,...u}=e;if(!t)return;let s={x:o.rect.left-i.rect.left,y:o.rect.top-i.rect.top},c={scaleX:1!==a.scaleX?i.rect.width*a.scaleX/o.rect.width:1,scaleY:1!==a.scaleY?i.rect.height*a.scaleY/o.rect.height:1},d={x:a.x-s.x,y:a.y-s.y,...c},f=l({...u,active:i,dragOverlay:o,transform:{initial:a,final:d}}),[h]=f,g=f[f.length-1];if(JSON.stringify(h)===JSON.stringify(g))return;let p=null==r?void 0:r({active:i,dragOverlay:o,...u}),v=o.node.animate(f,{duration:t,easing:n,fill:"forwards"});return new Promise(e=>{v.onfinish=()=>{null==p||p(),e()}})}}(t);return el(a,l.draggable.measure),d({active:{id:e,data:o.data,node:a,rect:l.draggable.measure(a)},draggableNodes:n,dragOverlay:{node:i,rect:l.dragOverlay.measure(u)},droppableContainers:r,measuringConfiguration:l,transform:c})})}({config:l,draggableNodes:p,droppableContainers:v,measuringConfiguration:y}),I=O?m.setRef:void 0;return E.createElement(e2,null,E.createElement(e0,{animation:k},f&&M?E.createElement(e4,{key:M,id:f.id,ref:I,as:u,activatorEvent:d,adjustScale:n,className:s,transition:o,rect:O,style:{zIndex:c,...i},transform:S},r):null))})},51985:function(e,t,n){n.d(t,{Fo:function(){return h},Rp:function(){return o},nB:function(){return w},qw:function(){return c}});var r=n(32486),l=n(34980),i=n(71714);function o(e,t,n){let r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function a(e){return null!==e&&e>=0}let u=e=>{let{rects:t,activeIndex:n,overIndex:r,index:l}=e,i=o(t,r,n),a=t[l],u=i[l];return u&&a?{x:u.left-a.left,y:u.top-a.top,scaleX:u.width/a.width,scaleY:u.height/a.height}:null},s={scaleX:1,scaleY:1},c=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:l,rects:i,overIndex:o}=e,a=null!=(t=i[n])?t:r;if(!a)return null;if(l===n){let e=i[o];return e?{x:0,y:n<o?e.top+e.height-(a.top+a.height):e.top-a.top,...s}:null}let u=function(e,t,n){let r=e[t],l=e[t-1],i=e[t+1];return r?n<t?l?r.top-(l.top+l.height):i?i.top-(r.top+r.height):0:i?i.top-(r.top+r.height):l?r.top-(l.top+l.height):0:0}(i,l,n);return l>n&&l<=o?{x:0,y:-a.height-u,...s}:l<n&&l>=o?{x:0,y:a.height+u,...s}:{x:0,y:0,...s}},d="Sortable",f=r.createContext({activeIndex:-1,containerId:d,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:u,disabled:{draggable:!1,droppable:!1}});function h(e){let{children:t,id:n,items:o,strategy:a=u,disabled:s=!1}=e,{active:c,dragOverlay:h,droppableRects:g,over:p,measureDroppableContainers:v}=(0,l.Cj)(),m=(0,i.Ld)(d,n),b=null!==h.rect,y=(0,r.useMemo)(()=>o.map(e=>"object"==typeof e&&"id"in e?e.id:e),[o]),w=null!=c,x=c?y.indexOf(c.id):-1,D=p?y.indexOf(p.id):-1,E=(0,r.useRef)(y),C=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(y,E.current),R=-1!==D&&-1===x||C,M="boolean"==typeof s?{draggable:s,droppable:s}:s;(0,i.LI)(()=>{C&&w&&v(y)},[C,y,w,v]),(0,r.useEffect)(()=>{E.current=y},[y]);let S=(0,r.useMemo)(()=>({activeIndex:x,containerId:m,disabled:M,disableTransforms:R,items:y,overIndex:D,useDragOverlay:b,sortedRects:y.reduce((e,t,n)=>{let r=g.get(t);return r&&(e[n]=r),e},Array(y.length)),strategy:a}),[x,m,M.draggable,M.droppable,R,y,D,g,b,a]);return r.createElement(f.Provider,{value:S},t)}let g=e=>{let{id:t,items:n,activeIndex:r,overIndex:l}=e;return o(n,r,l).indexOf(t)},p=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:l,items:i,newIndex:o,previousItems:a,previousContainerId:u,transition:s}=e;return!!s&&!!r&&(a===i||l!==o)&&(!!n||o!==l&&t===u)},v={duration:200,easing:"ease"},m="transform",b=i.ux.Transition.toString({property:m,duration:0,easing:"linear"}),y={roleDescription:"sortable"};function w(e){var t,n;let{animateLayoutChanges:o=p,attributes:u,disabled:s,data:c,getNewIndex:d=g,id:h,strategy:w,resizeObserverConfig:x,transition:D=v}=e,{items:E,containerId:C,activeIndex:R,disabled:M,disableTransforms:S,sortedRects:O,overIndex:k,useDragOverlay:I,strategy:j}=(0,r.useContext)(f),L="boolean"==typeof s?{draggable:s,droppable:!1}:{draggable:null!=(t=null==s?void 0:s.draggable)?t:M.draggable,droppable:null!=(n=null==s?void 0:s.droppable)?n:M.droppable},T=E.indexOf(h),N=(0,r.useMemo)(()=>({sortable:{containerId:C,index:T,items:E},...c}),[C,c,T,E]),P=(0,r.useMemo)(()=>E.slice(E.indexOf(h)),[E,h]),{rect:A,node:F,isOver:z,setNodeRef:B}=(0,l.Zj)({id:h,data:N,disabled:L.droppable,resizeObserverConfig:{updateMeasurementsFor:P,...x}}),{active:X,activatorEvent:_,activeNodeRect:J,attributes:W,setNodeRef:Y,listeners:U,isDragging:V,over:H,setActivatorNodeRef:K,transform:q}=(0,l.O1)({id:h,data:N,attributes:{...y,...u},disabled:L.draggable}),Z=(0,i.HB)(B,Y),G=!!X,$=G&&!S&&a(R)&&a(k),Q=!I&&V,ee=Q&&$?q:null,et=$?null!=ee?ee:(null!=w?w:j)({rects:O,activeNodeRect:J,activeIndex:R,overIndex:k,index:T}):null,en=a(R)&&a(k)?d({id:h,items:E,activeIndex:R,overIndex:k}):T,er=null==X?void 0:X.id,el=(0,r.useRef)({activeId:er,items:E,newIndex:en,containerId:C}),ei=E!==el.current.items,eo=o({active:X,containerId:C,isDragging:V,isSorting:G,id:h,index:T,items:E,newIndex:el.current.newIndex,previousItems:el.current.items,previousContainerId:el.current.containerId,transition:D,wasDragging:null!=el.current.activeId}),ea=function(e){let{disabled:t,index:n,node:o,rect:a}=e,[u,s]=(0,r.useState)(null),c=(0,r.useRef)(n);return(0,i.LI)(()=>{if(!t&&n!==c.current&&o.current){let e=a.current;if(e){let t=(0,l.VK)(o.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&s(n)}}n!==c.current&&(c.current=n)},[t,n,o,a]),(0,r.useEffect)(()=>{u&&s(null)},[u]),u}({disabled:!eo,index:T,node:F,rect:A});return(0,r.useEffect)(()=>{G&&el.current.newIndex!==en&&(el.current.newIndex=en),C!==el.current.containerId&&(el.current.containerId=C),E!==el.current.items&&(el.current.items=E)},[G,en,C,E]),(0,r.useEffect)(()=>{if(er===el.current.activeId)return;if(null!=er&&null==el.current.activeId){el.current.activeId=er;return}let e=setTimeout(()=>{el.current.activeId=er},50);return()=>clearTimeout(e)},[er]),{active:X,activeIndex:R,attributes:W,data:N,rect:A,index:T,newIndex:en,items:E,isOver:z,isSorting:G,isDragging:V,listeners:U,node:F,overIndex:k,over:H,setNodeRef:Z,setActivatorNodeRef:K,setDroppableNodeRef:B,setDraggableNodeRef:Y,transform:null!=ea?ea:et,transition:ea||ei&&el.current.newIndex===T?b:(!Q||(0,i.vd)(_))&&D&&(G||eo)?i.ux.Transition.toString({...D,property:m}):void 0}}l.g4.Down,l.g4.Right,l.g4.Up,l.g4.Left},71714:function(e,t,n){n.d(t,{$X:function(){return C},D9:function(){return y},DC:function(){return M},Ey:function(){return v},FJ:function(){return o},Gj:function(){return m},HB:function(){return l},IH:function(){return E},Jj:function(){return u},LI:function(){return h},Ld:function(){return x},Nq:function(){return i},Re:function(){return c},UG:function(){return a},Yz:function(){return p},qk:function(){return s},r3:function(){return f},so:function(){return k},ux:function(){return S},vZ:function(){return d},vd:function(){return R},wm:function(){return b},zX:function(){return g}});var r=n(32486);function l(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)(()=>e=>{t.forEach(t=>t(e))},t)}let i="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function o(e){let t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function a(e){return"nodeType"in e}function u(e){var t,n;return e?o(e)?e:a(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function s(e){let{Document:t}=u(e);return e instanceof t}function c(e){return!o(e)&&e instanceof u(e).HTMLElement}function d(e){return e instanceof u(e).SVGElement}function f(e){return e?o(e)?e.document:a(e)?s(e)?e:c(e)||d(e)?e.ownerDocument:document:document:document}let h=i?r.useLayoutEffect:r.useEffect;function g(e){let t=(0,r.useRef)(e);return h(()=>{t.current=e}),(0,r.useCallback)(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}function p(){let e=(0,r.useRef)(null);return[(0,r.useCallback)((t,n)=>{e.current=setInterval(t,n)},[]),(0,r.useCallback)(()=>{null!==e.current&&(clearInterval(e.current),e.current=null)},[])]}function v(e,t){void 0===t&&(t=[e]);let n=(0,r.useRef)(e);return h(()=>{n.current!==e&&(n.current=e)},t),n}function m(e,t){let n=(0,r.useRef)();return(0,r.useMemo)(()=>{let t=e(n.current);return n.current=t,t},[...t])}function b(e){let t=g(e),n=(0,r.useRef)(null),l=(0,r.useCallback)(e=>{e!==n.current&&(null==t||t(e,n.current)),n.current=e},[]);return[n,l]}function y(e){let t=(0,r.useRef)();return(0,r.useEffect)(()=>{t.current=e},[e]),t.current}let w={};function x(e,t){return(0,r.useMemo)(()=>{if(t)return t;let n=null==w[e]?0:w[e]+1;return w[e]=n,e+"-"+n},[e,t])}function D(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>{for(let[r,l]of Object.entries(n)){let n=t[r];null!=n&&(t[r]=n+e*l)}return t},{...t})}}let E=D(1),C=D(-1);function R(e){if(!e)return!1;let{KeyboardEvent:t}=u(e.target);return t&&e instanceof t}function M(e){if(function(e){if(!e)return!1;let{TouchEvent:t}=u(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){let{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){let{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return"clientX"in e&&"clientY"in e?{x:e.clientX,y:e.clientY}:null}let S=Object.freeze({Translate:{toString(e){if(!e)return;let{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;let{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[S.Translate.toString(e),S.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),O="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function k(e){return e.matches(O)?e:e.querySelector(O)}},18208:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},57292:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},22397:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},39713:function(e,t,n){n.d(t,{default:function(){return l.a}});var r=n(74033),l=n.n(r)},74033:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return u},getImageProps:function(){return a}});let r=n(60723),l=n(25738),i=n(28863),o=r._(n(44543));function a(e){let{props:t}=(0,l.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,n]of Object.entries(t))void 0===n&&delete t[e];return{props:t}}let u=i.Image},94797:function(e,t,n){n.d(t,{Dx:function(){return er},VY:function(){return en},aV:function(){return et},dk:function(){return el},fC:function(){return $},h_:function(){return ee},x8:function(){return ei},xz:function(){return Q}});var r=n(32486),l=n(20100),i=n(29626),o=n(32192),a=n(21971),u=n(31413),s=n(35878),c=n(5887),d=n(79872),f=n(53486),h=n(89801),g=n(67058),p=n(25081),v=n(15623),m=n(91007),b=n(75376),y="Dialog",[w,x]=(0,o.b)(y),[D,E]=w(y),C=e=>{let{__scopeDialog:t,children:n,open:l,defaultOpen:i,onOpenChange:o,modal:s=!0}=e,c=r.useRef(null),d=r.useRef(null),[f,h]=(0,u.T)({prop:l,defaultProp:null!=i&&i,onChange:o,caller:y});return(0,b.jsx)(D,{scope:t,triggerRef:c,contentRef:d,contentId:(0,a.M)(),titleId:(0,a.M)(),descriptionId:(0,a.M)(),open:f,onOpenChange:h,onOpenToggle:r.useCallback(()=>h(e=>!e),[h]),modal:s,children:n})};C.displayName=y;var R="DialogTrigger",M=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=E(R,n),a=(0,i.e)(t,o.triggerRef);return(0,b.jsx)(h.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":V(o.open),...r,ref:a,onClick:(0,l.M)(e.onClick,o.onOpenToggle)})});M.displayName=R;var S="DialogPortal",[O,k]=w(S,{forceMount:void 0}),I=e=>{let{__scopeDialog:t,forceMount:n,children:l,container:i}=e,o=E(S,t);return(0,b.jsx)(O,{scope:t,forceMount:n,children:r.Children.map(l,e=>(0,b.jsx)(f.z,{present:n||o.open,children:(0,b.jsx)(d.h,{asChild:!0,container:i,children:e})}))})};I.displayName=S;var j="DialogOverlay",L=r.forwardRef((e,t)=>{let n=k(j,e.__scopeDialog),{forceMount:r=n.forceMount,...l}=e,i=E(j,e.__scopeDialog);return i.modal?(0,b.jsx)(f.z,{present:r||i.open,children:(0,b.jsx)(N,{...l,ref:t})}):null});L.displayName=j;var T=(0,m.Z8)("DialogOverlay.RemoveScroll"),N=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=E(j,n);return(0,b.jsx)(p.Z,{as:T,allowPinchZoom:!0,shards:[l.contentRef],children:(0,b.jsx)(h.WV.div,{"data-state":V(l.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),P="DialogContent",A=r.forwardRef((e,t)=>{let n=k(P,e.__scopeDialog),{forceMount:r=n.forceMount,...l}=e,i=E(P,e.__scopeDialog);return(0,b.jsx)(f.z,{present:r||i.open,children:i.modal?(0,b.jsx)(F,{...l,ref:t}):(0,b.jsx)(z,{...l,ref:t})})});A.displayName=P;var F=r.forwardRef((e,t)=>{let n=E(P,e.__scopeDialog),o=r.useRef(null),a=(0,i.e)(t,n.contentRef,o);return r.useEffect(()=>{let e=o.current;if(e)return(0,v.Ry)(e)},[]),(0,b.jsx)(B,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,l.M)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,l.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,l.M)(e.onFocusOutside,e=>e.preventDefault())})}),z=r.forwardRef((e,t)=>{let n=E(P,e.__scopeDialog),l=r.useRef(!1),i=r.useRef(!1);return(0,b.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,o;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(l.current||null===(o=n.triggerRef.current)||void 0===o||o.focus(),t.preventDefault()),l.current=!1,i.current=!1},onInteractOutside:t=>{var r,o;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(l.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let a=t.target;(null===(o=n.triggerRef.current)||void 0===o?void 0:o.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),B=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:l,onOpenAutoFocus:o,onCloseAutoFocus:a,...u}=e,d=E(P,n),f=r.useRef(null),h=(0,i.e)(t,f);return(0,g.EW)(),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(c.M,{asChild:!0,loop:!0,trapped:l,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,b.jsx)(s.XB,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":V(d.open),...u,ref:h,onDismiss:()=>d.onOpenChange(!1)})}),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(Z,{titleId:d.titleId}),(0,b.jsx)(G,{contentRef:f,descriptionId:d.descriptionId})]})]})}),X="DialogTitle",_=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=E(X,n);return(0,b.jsx)(h.WV.h2,{id:l.titleId,...r,ref:t})});_.displayName=X;var J="DialogDescription",W=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=E(J,n);return(0,b.jsx)(h.WV.p,{id:l.descriptionId,...r,ref:t})});W.displayName=J;var Y="DialogClose",U=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=E(Y,n);return(0,b.jsx)(h.WV.button,{type:"button",...r,ref:t,onClick:(0,l.M)(e.onClick,()=>i.onOpenChange(!1))})});function V(e){return e?"open":"closed"}U.displayName=Y;var H="DialogTitleWarning",[K,q]=(0,o.k)(H,{contentName:P,titleName:X,docsSlug:"dialog"}),Z=e=>{let{titleId:t}=e,n=q(H),l="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&!document.getElementById(t)&&console.error(l)},[l,t]),null},G=e=>{let{contentRef:t,descriptionId:n}=e,l=q("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(l.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(i)},[i,t,n]),null},$=C,Q=M,ee=I,et=L,en=A,er=_,el=W,ei=U},53447:function(e,t,n){n.d(t,{j:function(){return o}});var r=n(89824);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=r.W,o=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return i(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:o,defaultVariants:a}=t,u=Object.keys(o).map(e=>{let t=null==n?void 0:n[e],r=null==a?void 0:a[e];if(null===t)return null;let i=l(t)||l(r);return o[e][i]}),s=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return i(e,u,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...l}=t;return Object.entries(l).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...a,...s}[t]):({...a,...s})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}}}]);