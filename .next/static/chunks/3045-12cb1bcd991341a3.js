"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3045],{51888:function(e,t,s){s.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,s(9824).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},47411:function(e,t,s){var n=s(13362);s.o(n,"useParams")&&s.d(t,{useParams:function(){return n.useParams}}),s.o(n,"usePathname")&&s.d(t,{usePathname:function(){return n.usePathname}}),s.o(n,"useRouter")&&s.d(t,{useRouter:function(){return n.useRouter}}),s.o(n,"useSearchParams")&&s.d(t,{useSearchParams:function(){return n.useSearchParams}})},80500:function(e,t,s){s.d(t,{VY:function(){return V},fC:function(){return B},h_:function(){return G},xp:function(){return F},xz:function(){return q}});var n=s(32486),i=s(20100),r=s(29626),o=s(32192),c=s(35878),a=s(67058),h=s(5887),l=s(21971),u=s(25117),d=s(79872),_=s(53486),p=s(89801),f=s(91007),b=s(31413),g=s(15623),m=s(25081),v=s(75376),y="Popover",[T,S]=(0,o.b)(y,[u.D7]),w=(0,u.D7)(),[C,k]=T(y),P=e=>{let{__scopePopover:t,children:s,open:i,defaultOpen:r,onOpenChange:o,modal:c=!1}=e,a=w(t),h=n.useRef(null),[d,_]=n.useState(!1),[p,f]=(0,b.T)({prop:i,defaultProp:null!=r&&r,onChange:o,caller:y});return(0,v.jsx)(u.fC,{...a,children:(0,v.jsx)(C,{scope:t,contentId:(0,l.M)(),triggerRef:h,open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),hasCustomAnchor:d,onCustomAnchorAdd:n.useCallback(()=>_(!0),[]),onCustomAnchorRemove:n.useCallback(()=>_(!1),[]),modal:c,children:s})})};P.displayName=y;var E="PopoverAnchor";n.forwardRef((e,t)=>{let{__scopePopover:s,...i}=e,r=k(E,s),o=w(s),{onCustomAnchorAdd:c,onCustomAnchorRemove:a}=r;return n.useEffect(()=>(c(),()=>a()),[c,a]),(0,v.jsx)(u.ee,{...o,...i,ref:t})}).displayName=E;var x="PopoverTrigger",R=n.forwardRef((e,t)=>{let{__scopePopover:s,...n}=e,o=k(x,s),c=w(s),a=(0,r.e)(t,o.triggerRef),h=(0,v.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":J(o.open),...n,ref:a,onClick:(0,i.M)(e.onClick,o.onOpenToggle)});return o.hasCustomAnchor?h:(0,v.jsx)(u.ee,{asChild:!0,...c,children:h})});R.displayName=x;var O="PopoverPortal",[j,D]=T(O,{forceMount:void 0}),L=e=>{let{__scopePopover:t,forceMount:s,children:n,container:i}=e,r=k(O,t);return(0,v.jsx)(j,{scope:t,forceMount:s,children:(0,v.jsx)(_.z,{present:s||r.open,children:(0,v.jsx)(d.h,{asChild:!0,container:i,children:n})})})};L.displayName=O;var I="PopoverContent",A=n.forwardRef((e,t)=>{let s=D(I,e.__scopePopover),{forceMount:n=s.forceMount,...i}=e,r=k(I,e.__scopePopover);return(0,v.jsx)(_.z,{present:n||r.open,children:r.modal?(0,v.jsx)(z,{...i,ref:t}):(0,v.jsx)(M,{...i,ref:t})})});A.displayName=I;var U=(0,f.Z8)("PopoverContent.RemoveScroll"),z=n.forwardRef((e,t)=>{let s=k(I,e.__scopePopover),o=n.useRef(null),c=(0,r.e)(t,o),a=n.useRef(!1);return n.useEffect(()=>{let e=o.current;if(e)return(0,g.Ry)(e)},[]),(0,v.jsx)(m.Z,{as:U,allowPinchZoom:!0,children:(0,v.jsx)(N,{...e,ref:c,trapFocus:s.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,i.M)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),a.current||null===(t=s.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,i.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,s=0===t.button&&!0===t.ctrlKey,n=2===t.button||s;a.current=n},{checkForDefaultPrevented:!1}),onFocusOutside:(0,i.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),M=n.forwardRef((e,t)=>{let s=k(I,e.__scopePopover),i=n.useRef(!1),r=n.useRef(!1);return(0,v.jsx)(N,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,o;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(i.current||null===(o=s.triggerRef.current)||void 0===o||o.focus(),t.preventDefault()),i.current=!1,r.current=!1},onInteractOutside:t=>{var n,o;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(i.current=!0,"pointerdown"!==t.detail.originalEvent.type||(r.current=!0));let c=t.target;(null===(o=s.triggerRef.current)||void 0===o?void 0:o.contains(c))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&r.current&&t.preventDefault()}})}),N=n.forwardRef((e,t)=>{let{__scopePopover:s,trapFocus:n,onOpenAutoFocus:i,onCloseAutoFocus:r,disableOutsidePointerEvents:o,onEscapeKeyDown:l,onPointerDownOutside:d,onFocusOutside:_,onInteractOutside:p,...f}=e,b=k(I,s),g=w(s);return(0,a.EW)(),(0,v.jsx)(h.M,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:i,onUnmountAutoFocus:r,children:(0,v.jsx)(c.XB,{asChild:!0,disableOutsidePointerEvents:o,onInteractOutside:p,onEscapeKeyDown:l,onPointerDownOutside:d,onFocusOutside:_,onDismiss:()=>b.onOpenChange(!1),children:(0,v.jsx)(u.VY,{"data-state":J(b.open),role:"dialog",id:b.contentId,...g,...f,ref:t,style:{...f.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),W="PopoverClose",F=n.forwardRef((e,t)=>{let{__scopePopover:s,...n}=e,r=k(W,s);return(0,v.jsx)(p.WV.button,{type:"button",...n,ref:t,onClick:(0,i.M)(e.onClick,()=>r.onOpenChange(!1))})});function J(e){return e?"open":"closed"}F.displayName=W,n.forwardRef((e,t)=>{let{__scopePopover:s,...n}=e,i=w(s);return(0,v.jsx)(u.Eh,{...i,...n,ref:t})}).displayName="PopoverArrow";var B=P,q=R,G=L,V=A},53465:function(e,t,s){function n(e,t,s,n){return new(s||(s=Promise))(function(i,r){function o(e){try{a(n.next(e))}catch(e){r(e)}}function c(e){try{a(n.throw(e))}catch(e){r(e)}}function a(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(o,c)}a((n=n.apply(e,t||[])).next())})}s.d(t,{qX:function(){return Y}}),"function"==typeof SuppressedError&&SuppressedError;var i,r,o,c,a,h,l,u,d,_,p,f,b,g,m,v,y={exports:{}},T="object"==typeof Reflect?Reflect:null,S=T&&"function"==typeof T.apply?T.apply:function(e,t,s){return Function.prototype.apply.call(e,t,s)};d=T&&"function"==typeof T.ownKeys?T.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var w=Number.isNaN||function(e){return e!=e};function C(){C.init.call(this)}y.exports=C,y.exports.once=function(e,t){return new Promise(function(s,n){var i;function r(s){e.removeListener(t,o),n(s)}function o(){"function"==typeof e.removeListener&&e.removeListener("error",r),s([].slice.call(arguments))}I(e,t,o,{once:!0}),"error"!==t&&(i={once:!0},"function"==typeof e.on&&I(e,"error",r,i))})},C.EventEmitter=C,C.prototype._events=void 0,C.prototype._eventsCount=0,C.prototype._maxListeners=void 0;var k=10;function P(e){if("function"!=typeof e)throw TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function E(e){return void 0===e._maxListeners?C.defaultMaxListeners:e._maxListeners}function x(e,t,s,n){if(P(s),void 0===(r=e._events)?(r=e._events=Object.create(null),e._eventsCount=0):(void 0!==r.newListener&&(e.emit("newListener",t,s.listener?s.listener:s),r=e._events),o=r[t]),void 0===o)o=r[t]=s,++e._eventsCount;else if("function"==typeof o?o=r[t]=n?[s,o]:[o,s]:n?o.unshift(s):o.push(s),(i=E(e))>0&&o.length>i&&!o.warned){o.warned=!0;var i,r,o,c=Error("Possible EventEmitter memory leak detected. "+o.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");c.name="MaxListenersExceededWarning",c.emitter=e,c.type=t,c.count=o.length,console&&console.warn&&console.warn(c)}return e}function R(){if(!this.fired)return(this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0==arguments.length)?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function O(e,t,s){var n={fired:!1,wrapFn:void 0,target:e,type:t,listener:s},i=R.bind(n);return i.listener=s,n.wrapFn=i,i}function j(e,t,s){var n=e._events;if(void 0===n)return[];var i=n[t];return void 0===i?[]:"function"==typeof i?s?[i.listener||i]:[i]:s?function(e){for(var t=Array(e.length),s=0;s<t.length;++s)t[s]=e[s].listener||e[s];return t}(i):L(i,i.length)}function D(e){var t=this._events;if(void 0!==t){var s=t[e];if("function"==typeof s)return 1;if(void 0!==s)return s.length}return 0}function L(e,t){for(var s=Array(t),n=0;n<t;++n)s[n]=e[n];return s}function I(e,t,s,n){if("function"==typeof e.on)n.once?e.once(t,s):e.on(t,s);else if("function"==typeof e.addEventListener)e.addEventListener(t,function i(r){n.once&&e.removeEventListener(t,i),s(r)});else throw TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}Object.defineProperty(C,"defaultMaxListeners",{enumerable:!0,get:function(){return k},set:function(e){if("number"!=typeof e||e<0||w(e))throw RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");k=e}}),C.init=function(){(void 0===this._events||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},C.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||w(e))throw RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},C.prototype.getMaxListeners=function(){return E(this)},C.prototype.emit=function(e){for(var t=[],s=1;s<arguments.length;s++)t.push(arguments[s]);var n="error"===e,i=this._events;if(void 0!==i)n=n&&void 0===i.error;else if(!n)return!1;if(n){if(t.length>0&&(r=t[0]),r instanceof Error)throw r;var r,o=Error("Unhandled error."+(r?" ("+r.message+")":""));throw o.context=r,o}var c=i[e];if(void 0===c)return!1;if("function"==typeof c)S(c,this,t);else for(var a=c.length,h=L(c,a),s=0;s<a;++s)S(h[s],this,t);return!0},C.prototype.addListener=function(e,t){return x(this,e,t,!1)},C.prototype.on=C.prototype.addListener,C.prototype.prependListener=function(e,t){return x(this,e,t,!0)},C.prototype.once=function(e,t){return P(t),this.on(e,O(this,e,t)),this},C.prototype.prependOnceListener=function(e,t){return P(t),this.prependListener(e,O(this,e,t)),this},C.prototype.removeListener=function(e,t){var s,n,i,r,o;if(P(t),void 0===(n=this._events)||void 0===(s=n[e]))return this;if(s===t||s.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete n[e],n.removeListener&&this.emit("removeListener",e,s.listener||t));else if("function"!=typeof s){for(i=-1,r=s.length-1;r>=0;r--)if(s[r]===t||s[r].listener===t){o=s[r].listener,i=r;break}if(i<0)return this;0===i?s.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(s,i),1===s.length&&(n[e]=s[0]),void 0!==n.removeListener&&this.emit("removeListener",e,o||t)}return this},C.prototype.off=C.prototype.removeListener,C.prototype.removeAllListeners=function(e){var t,s,n;if(void 0===(s=this._events))return this;if(void 0===s.removeListener)return 0==arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==s[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete s[e]),this;if(0==arguments.length){var i,r=Object.keys(s);for(n=0;n<r.length;++n)"removeListener"!==(i=r[n])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=s[e]))this.removeListener(e,t);else if(void 0!==t)for(n=t.length-1;n>=0;n--)this.removeListener(e,t[n]);return this},C.prototype.listeners=function(e){return j(this,e,!0)},C.prototype.rawListeners=function(e){return j(this,e,!1)},C.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):D.call(e,t)},C.prototype.listenerCount=D,C.prototype.eventNames=function(){return this._eventsCount>0?d(this._events):[]};var A=(i=y.exports)&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i;function U(e){return null!=e&&"function"==typeof e}function z(e,t,s){e>31&&(e=31);let n=Math.floor(Math.random()*(Math.min(s,t*Math.pow(2,e))-0+1)+0);return Math.min(s,t+n)}function M(e){return Math.min(1e3*e,2147483647)}(r=_||(_={}))[r.timeout=1]="timeout",r[r.transportClosed=2]="transportClosed",r[r.clientDisconnected=3]="clientDisconnected",r[r.clientClosed=4]="clientClosed",r[r.clientConnectToken=5]="clientConnectToken",r[r.clientRefreshToken=6]="clientRefreshToken",r[r.subscriptionUnsubscribed=7]="subscriptionUnsubscribed",r[r.subscriptionSubscribeToken=8]="subscriptionSubscribeToken",r[r.subscriptionRefreshToken=9]="subscriptionRefreshToken",r[r.transportWriteError=10]="transportWriteError",r[r.connectionClosed=11]="connectionClosed",r[r.badConfiguration=12]="badConfiguration",(o=p||(p={}))[o.connectCalled=0]="connectCalled",o[o.transportClosed=1]="transportClosed",o[o.noPing=2]="noPing",o[o.subscribeTimeout=3]="subscribeTimeout",o[o.unsubscribeError=4]="unsubscribeError",(c=f||(f={}))[c.disconnectCalled=0]="disconnectCalled",c[c.unauthorized=1]="unauthorized",c[c.badProtocol=2]="badProtocol",c[c.messageSizeLimit=3]="messageSizeLimit",(a=b||(b={}))[a.subscribeCalled=0]="subscribeCalled",a[a.transportClosed=1]="transportClosed",(h=g||(g={}))[h.unsubscribeCalled=0]="unsubscribeCalled",h[h.unauthorized=1]="unauthorized",h[h.clientClosed=2]="clientClosed",(l=m||(m={})).Disconnected="disconnected",l.Connecting="connecting",l.Connected="connected",(u=v||(v={})).Unsubscribed="unsubscribed",u.Subscribing="subscribing",u.Subscribed="subscribed";class N extends A{constructor(e,t,s){super(),this._resubscribeTimeout=null,this._refreshTimeout=null,this.channel=t,this.state=v.Unsubscribed,this._centrifuge=e,this._token="",this._getToken=null,this._data=null,this._getData=null,this._recover=!1,this._offset=null,this._epoch=null,this._recoverable=!1,this._positioned=!1,this._joinLeave=!1,this._minResubscribeDelay=500,this._maxResubscribeDelay=2e4,this._resubscribeTimeout=null,this._resubscribeAttempts=0,this._promises={},this._promiseId=0,this._inflight=!1,this._refreshTimeout=null,this._delta="",this._delta_negotiated=!1,this._prevValue=null,this._unsubPromise=Promise.resolve(),this._setOptions(s),this._centrifuge._debugEnabled?(this.on("state",e=>{this._debug("subscription state",t,e.oldState,"->",e.newState)}),this.on("error",e=>{this._debug("subscription error",t,e)})):this.on("error",function(){Function.prototype()})}ready(e){return this.state===v.Unsubscribed?Promise.reject({code:_.subscriptionUnsubscribed,message:this.state}):this.state===v.Subscribed?Promise.resolve():new Promise((t,s)=>{let n={resolve:t,reject:s};e&&(n.timeout=setTimeout(function(){s({code:_.timeout,message:"timeout"})},e)),this._promises[this._nextPromiseId()]=n})}subscribe(){this._isSubscribed()||(this._resubscribeAttempts=0,this._setSubscribing(b.subscribeCalled,"subscribe called"))}unsubscribe(){this._unsubPromise=this._setUnsubscribed(g.unsubscribeCalled,"unsubscribe called",!0)}publish(e){return n(this,void 0,void 0,function*(){return yield this._methodCall(),this._centrifuge.publish(this.channel,e)})}presence(){return n(this,void 0,void 0,function*(){return yield this._methodCall(),this._centrifuge.presence(this.channel)})}presenceStats(){return n(this,void 0,void 0,function*(){return yield this._methodCall(),this._centrifuge.presenceStats(this.channel)})}history(e){return n(this,void 0,void 0,function*(){return yield this._methodCall(),this._centrifuge.history(this.channel,e)})}_methodCall(){return this._isSubscribed()?Promise.resolve():this._isUnsubscribed()?Promise.reject({code:_.subscriptionUnsubscribed,message:this.state}):new Promise((e,t)=>{let s=setTimeout(()=>{t({code:_.timeout,message:"timeout"})},this._centrifuge._config.timeout);this._promises[this._nextPromiseId()]={timeout:s,resolve:e,reject:t}})}_nextPromiseId(){return++this._promiseId}_needRecover(){return!0===this._recover}_isUnsubscribed(){return this.state===v.Unsubscribed}_isSubscribing(){return this.state===v.Subscribing}_isSubscribed(){return this.state===v.Subscribed}_setState(e){if(this.state!==e){let t=this.state;return this.state=e,this.emit("state",{newState:e,oldState:t,channel:this.channel}),!0}return!1}_usesToken(){return""!==this._token||null!==this._getToken}_clearSubscribingState(){this._resubscribeAttempts=0,this._clearResubscribeTimeout()}_clearSubscribedState(){this._clearRefreshTimeout()}_setSubscribed(e){if(!this._isSubscribing())return;this._clearSubscribingState(),e.recoverable&&(this._recover=!0,this._offset=e.offset||0,this._epoch=e.epoch||""),e.delta?this._delta_negotiated=!0:this._delta_negotiated=!1,this._setState(v.Subscribed);let t=this._centrifuge._getSubscribeContext(this.channel,e);this.emit("subscribed",t),this._resolvePromises();let s=e.publications;if(s&&s.length>0)for(let e in s)s.hasOwnProperty(e)&&this._handlePublication(s[e]);!0===e.expires&&(this._refreshTimeout=setTimeout(()=>this._refresh(),M(e.ttl)))}_setSubscribing(e,t){return n(this,void 0,void 0,function*(){!this._isSubscribing()&&(this._isSubscribed()&&this._clearSubscribedState(),this._setState(v.Subscribing)&&this.emit("subscribing",{channel:this.channel,code:e,reason:t}),this._centrifuge._transport&&this._centrifuge._transport.emulation()&&(yield this._unsubPromise),this._isSubscribing()&&this._subscribe())})}_subscribe(){return(this._debug("subscribing on",this.channel),this._isTransportOpen())?this._inflight?null:(this._inflight=!0,this._canSubscribeWithoutGettingToken())?this._subscribeWithoutToken():(this._getSubscriptionToken().then(e=>this._handleTokenResponse(e)).catch(e=>this._handleTokenError(e)),null):(this._debug("delay subscribe on",this.channel,"till connected"),null)}_isTransportOpen(){return this._centrifuge._transportIsOpen}_canSubscribeWithoutGettingToken(){return!this._usesToken()||!!this._token}_subscribeWithoutToken(){return this._getData?(this._getDataAndSubscribe(this._token),null):this._sendSubscribe(this._token)}_getDataAndSubscribe(e){if(!this._getData){this._inflight=!1;return}this._getData({channel:this.channel}).then(t=>{if(!this._isSubscribing()){this._inflight=!1;return}this._data=t,this._sendSubscribe(e)}).catch(e=>this._handleGetDataError(e))}_handleGetDataError(e){if(!this._isSubscribing()){this._inflight=!1;return}if(e instanceof X){this._inflight=!1,this._failUnauthorized();return}this.emit("error",{type:"subscribeData",channel:this.channel,error:{code:_.badConfiguration,message:(null==e?void 0:e.toString())||""}}),this._inflight=!1,this._scheduleResubscribe()}_handleTokenResponse(e){if(!this._isSubscribing()){this._inflight=!1;return}if(!e){this._inflight=!1,this._failUnauthorized();return}this._token=e,this._getData?this._getDataAndSubscribe(e):this._sendSubscribe(e)}_handleTokenError(e){if(!this._isSubscribing()){this._inflight=!1;return}if(e instanceof X){this._inflight=!1,this._failUnauthorized();return}this.emit("error",{type:"subscribeToken",channel:this.channel,error:{code:_.subscriptionSubscribeToken,message:(null==e?void 0:e.toString())||""}}),this._inflight=!1,this._scheduleResubscribe()}_sendSubscribe(e){if(!this._isTransportOpen())return this._inflight=!1,null;let t=this._buildSubscribeCommand(e);return this._centrifuge._call(t).then(e=>{this._inflight=!1;let t=e.reply.subscribe;this._handleSubscribeResponse(t),e.next&&e.next()},e=>{this._inflight=!1,this._handleSubscribeError(e.error),e.next&&e.next()}),t}_buildSubscribeCommand(e){let t={channel:this.channel};if(e&&(t.token=e),this._data&&(t.data=this._data),this._positioned&&(t.positioned=!0),this._recoverable&&(t.recoverable=!0),this._joinLeave&&(t.join_leave=!0),this._needRecover()){t.recover=!0;let e=this._getOffset();e&&(t.offset=e);let s=this._getEpoch();s&&(t.epoch=s)}return this._delta&&(t.delta=this._delta),{subscribe:t}}_debug(...e){this._centrifuge._debug(...e)}_handleSubscribeError(e){if(this._isSubscribing()){if(e.code===_.timeout){this._centrifuge._disconnect(p.subscribeTimeout,"subscribe timeout",!0);return}this._subscribeError(e)}}_handleSubscribeResponse(e){this._isSubscribing()&&this._setSubscribed(e)}_setUnsubscribed(e,t,s){if(this._isUnsubscribed())return Promise.resolve();let n=Promise.resolve();return this._isSubscribed()?(s&&(n=this._centrifuge._unsubscribe(this)),this._clearSubscribedState()):this._isSubscribing()&&(this._inflight&&s&&(n=this._centrifuge._unsubscribe(this)),this._clearSubscribingState()),this._inflight=!1,this._setState(v.Unsubscribed)&&this.emit("unsubscribed",{channel:this.channel,code:e,reason:t}),this._rejectPromises({code:_.subscriptionUnsubscribed,message:this.state}),n}_handlePublication(e){if(this._delta&&this._delta_negotiated){let{newData:t,newPrevValue:s}=this._centrifuge._codec.applyDeltaIfNeeded(e,this._prevValue);e.data=t,this._prevValue=s}let t=this._centrifuge._getPublicationContext(this.channel,e);this.emit("publication",t),e.offset&&(this._offset=e.offset)}_handleJoin(e){let t=this._centrifuge._getJoinLeaveContext(e.info);this.emit("join",{channel:this.channel,info:t})}_handleLeave(e){let t=this._centrifuge._getJoinLeaveContext(e.info);this.emit("leave",{channel:this.channel,info:t})}_resolvePromises(){for(let e in this._promises)this._promises.hasOwnProperty(e)&&(this._promises[e].timeout&&clearTimeout(this._promises[e].timeout),this._promises[e].resolve(),delete this._promises[e])}_rejectPromises(e){for(let t in this._promises)this._promises.hasOwnProperty(t)&&(this._promises[t].timeout&&clearTimeout(this._promises[t].timeout),this._promises[t].reject(e),delete this._promises[t])}_scheduleResubscribe(){if(!this._isSubscribing()){this._debug("not in subscribing state, skip resubscribe scheduling",this.channel);return}let e=this,t=this._getResubscribeDelay();this._resubscribeTimeout=setTimeout(function(){e._isSubscribing()&&e._subscribe()},t),this._debug("resubscribe scheduled after "+t,this.channel)}_subscribeError(e){if(this._isSubscribing()){if(e.code<100||109===e.code||!0===e.temporary){109===e.code&&(this._token="");let t={channel:this.channel,type:"subscribe",error:e};this._centrifuge.state===m.Connected&&this.emit("error",t),this._scheduleResubscribe()}else this._setUnsubscribed(e.code,e.message,!1)}}_getResubscribeDelay(){let e=z(this._resubscribeAttempts,this._minResubscribeDelay,this._maxResubscribeDelay);return this._resubscribeAttempts++,e}_setOptions(e){if(e&&(e.since&&(this._offset=e.since.offset||0,this._epoch=e.since.epoch||"",this._recover=!0),e.data&&(this._data=e.data),e.getData&&(this._getData=e.getData),void 0!==e.minResubscribeDelay&&(this._minResubscribeDelay=e.minResubscribeDelay),void 0!==e.maxResubscribeDelay&&(this._maxResubscribeDelay=e.maxResubscribeDelay),e.token&&(this._token=e.token),e.getToken&&(this._getToken=e.getToken),!0===e.positioned&&(this._positioned=!0),!0===e.recoverable&&(this._recoverable=!0),!0===e.joinLeave&&(this._joinLeave=!0),e.delta)){if("fossil"!==e.delta)throw Error("unsupported delta format");this._delta=e.delta}}_getOffset(){let e=this._offset;return null!==e?e:0}_getEpoch(){let e=this._epoch;return null!==e?e:""}_clearRefreshTimeout(){null!==this._refreshTimeout&&(clearTimeout(this._refreshTimeout),this._refreshTimeout=null)}_clearResubscribeTimeout(){null!==this._resubscribeTimeout&&(clearTimeout(this._resubscribeTimeout),this._resubscribeTimeout=null)}_getSubscriptionToken(){this._debug("get subscription token for channel",this.channel);let e={channel:this.channel},t=this._getToken;return null===t?(this.emit("error",{type:"configuration",channel:this.channel,error:{code:_.badConfiguration,message:"provide a function to get channel subscription token"}}),Promise.reject(new X(""))):t(e)}_refresh(){this._clearRefreshTimeout();let e=this;this._getSubscriptionToken().then(function(t){if(!e._isSubscribed())return;if(!t){e._failUnauthorized();return}e._token=t;let s={channel:e.channel,token:t};e._centrifuge._call({sub_refresh:s}).then(t=>{let s=t.reply.sub_refresh;e._refreshResponse(s),t.next&&t.next()},t=>{e._refreshError(t.error),t.next&&t.next()})}).catch(function(t){if(t instanceof X){e._failUnauthorized();return}e.emit("error",{type:"refreshToken",channel:e.channel,error:{code:_.subscriptionRefreshToken,message:void 0!==t?t.toString():""}}),e._refreshTimeout=setTimeout(()=>e._refresh(),e._getRefreshRetryDelay())})}_refreshResponse(e){this._isSubscribed()&&(this._debug("subscription token refreshed, channel",this.channel),this._clearRefreshTimeout(),!0===e.expires&&(this._refreshTimeout=setTimeout(()=>this._refresh(),M(e.ttl))))}_refreshError(e){this._isSubscribed()&&(e.code<100||!0===e.temporary?(this.emit("error",{type:"refresh",channel:this.channel,error:e}),this._refreshTimeout=setTimeout(()=>this._refresh(),this._getRefreshRetryDelay())):this._setUnsubscribed(e.code,e.message,!0))}_getRefreshRetryDelay(){return z(0,1e4,2e4)}_failUnauthorized(){this._setUnsubscribed(g.unauthorized,"unauthorized",!0)}}class W{constructor(e,t){this.endpoint=e,this.options=t,this._transport=null}name(){return"sockjs"}subName(){return"sockjs-"+this._transport.transport}emulation(){return!1}supported(){return null!==this.options.sockjs}initialize(e,t){this._transport=new this.options.sockjs(this.endpoint,null,this.options.sockjsOptions),this._transport.onopen=()=>{t.onOpen()},this._transport.onerror=e=>{t.onError(e)},this._transport.onclose=e=>{t.onClose(e)},this._transport.onmessage=e=>{t.onMessage(e.data)}}close(){this._transport.close()}send(e){this._transport.send(e)}}class F{constructor(e,t){this.endpoint=e,this.options=t,this._transport=null}name(){return"websocket"}subName(){return"websocket"}emulation(){return!1}supported(){return void 0!==this.options.websocket&&null!==this.options.websocket}initialize(e,t){let s="";"protobuf"===e&&(s="centrifuge-protobuf"),""!==s?this._transport=new this.options.websocket(this.endpoint,s):this._transport=new this.options.websocket(this.endpoint),"protobuf"===e&&(this._transport.binaryType="arraybuffer"),this._transport.onopen=()=>{t.onOpen()},this._transport.onerror=e=>{t.onError(e)},this._transport.onclose=e=>{t.onClose(e)},this._transport.onmessage=e=>{t.onMessage(e.data)}}close(){this._transport.close()}send(e){this._transport.send(e)}}class J{constructor(e,t){this.endpoint=e,this.options=t,this._abortController=null,this._utf8decoder=new TextDecoder,this._protocol="json"}name(){return"http_stream"}subName(){return"http_stream"}emulation(){return!0}_handleErrors(e){if(!e.ok)throw Error(e.status);return e}_fetchEventTarget(e,t,s){let n=new EventTarget;return(0,e.options.fetch)(t,s).then(e._handleErrors).then(t=>{n.dispatchEvent(new Event("open"));let s="",i=0,r=new Uint8Array,o=t.body.getReader();return new e.options.readableStream({start:t=>(function c(){return o.read().then(({done:o,value:a})=>{if(o){n.dispatchEvent(new Event("close")),t.close();return}try{if("json"===e._protocol)for(s+=e._utf8decoder.decode(a);i<s.length;)if("\n"===s[i]){let e=s.substring(0,i);n.dispatchEvent(new MessageEvent("message",{data:e})),s=s.substring(i+1),i=0}else++i;else{let t=new Uint8Array(r.length+a.length);for(t.set(r),t.set(a,r.length),r=t;;){let t=e.options.decoder.decodeReply(r);if(t.ok){let e=r.slice(0,t.pos);n.dispatchEvent(new MessageEvent("message",{data:e})),r=r.slice(t.pos);continue}break}}}catch(e){n.dispatchEvent(new Event("error",{detail:e})),n.dispatchEvent(new Event("close")),t.close();return}c()}).catch(function(e){n.dispatchEvent(new Event("error",{detail:e})),n.dispatchEvent(new Event("close")),t.close()})})()})}).catch(e=>{n.dispatchEvent(new Event("error",{detail:e})),n.dispatchEvent(new Event("close"))}),n}supported(){return null!==this.options.fetch&&null!==this.options.readableStream&&"undefined"!=typeof TextDecoder&&"undefined"!=typeof AbortController&&"undefined"!=typeof EventTarget&&"undefined"!=typeof Event&&"undefined"!=typeof MessageEvent&&"undefined"!=typeof Error}initialize(e,t,s){this._protocol=e,this._abortController=new AbortController;let n={method:"POST",headers:"json"===e?{Accept:"application/json","Content-Type":"application/json"}:{Accept:"application/octet-stream","Content-Type":"application/octet-stream"},body:s,mode:"cors",credentials:"same-origin",signal:this._abortController.signal},i=this._fetchEventTarget(this,this.endpoint,n);i.addEventListener("open",()=>{t.onOpen()}),i.addEventListener("error",e=>{this._abortController.abort(),t.onError(e)}),i.addEventListener("close",()=>{this._abortController.abort(),t.onClose({code:4,reason:"connection closed"})}),i.addEventListener("message",e=>{t.onMessage(e.data)})}close(){this._abortController.abort()}send(e,t,s){let n,i;let r={session:t,node:s,data:e};"json"===this._protocol?(n={"Content-Type":"application/json"},i=JSON.stringify(r)):(n={"Content-Type":"application/octet-stream"},i=this.options.encoder.encodeEmulationRequest(r));let o=this.options.fetch,c={method:"POST",headers:n,body:i,mode:"cors",credentials:"same-origin"};o(this.options.emulationEndpoint,c)}}class B{constructor(e,t){this.endpoint=e,this.options=t,this._protocol="json",this._transport=null,this._onClose=null}name(){return"sse"}subName(){return"sse"}emulation(){return!0}supported(){return null!==this.options.eventsource&&null!==this.options.fetch}initialize(e,t,s){let n;(n=globalThis&&globalThis.document&&globalThis.document.baseURI?new URL(this.endpoint,globalThis.document.baseURI):new URL(this.endpoint)).searchParams.append("cf_connect",s);let i=new this.options.eventsource(n.toString(),{});this._transport=i,i.onopen=function(){t.onOpen()},i.onerror=function(e){i.close(),t.onError(e),t.onClose({code:4,reason:"connection closed"})},i.onmessage=function(e){t.onMessage(e.data)},this._onClose=function(){t.onClose({code:4,reason:"connection closed"})}}close(){this._transport.close(),null!==this._onClose&&this._onClose()}send(e,t,s){let n=JSON.stringify({session:t,node:s,data:e});(0,this.options.fetch)(this.options.emulationEndpoint,{method:"POST",headers:{"Content-Type":"application/json"},body:n,mode:"cors",credentials:"same-origin"})}}class q{constructor(e,t){this.endpoint=e,this.options=t,this._transport=null,this._stream=null,this._writer=null,this._utf8decoder=new TextDecoder,this._protocol="json"}name(){return"webtransport"}subName(){return"webtransport"}emulation(){return!1}supported(){return void 0!==this.options.webtransport&&null!==this.options.webtransport}initialize(e,t){return n(this,void 0,void 0,function*(){let s,n;s=globalThis&&globalThis.document&&globalThis.document.baseURI?new URL(this.endpoint,globalThis.document.baseURI):new URL(this.endpoint),"protobuf"===e&&s.searchParams.append("cf_protocol","protobuf"),this._protocol=e;let i=new EventTarget;this._transport=new this.options.webtransport(s.toString()),this._transport.closed.then(()=>{t.onClose({code:4,reason:"connection closed"})}).catch(()=>{t.onClose({code:4,reason:"connection closed"})});try{yield this._transport.ready}catch(e){this.close();return}try{n=yield this._transport.createBidirectionalStream()}catch(e){this.close();return}this._stream=n,this._writer=this._stream.writable.getWriter(),i.addEventListener("close",()=>{t.onClose({code:4,reason:"connection closed"})}),i.addEventListener("message",e=>{t.onMessage(e.data)}),this._startReading(i),t.onOpen()})}_startReading(e){return n(this,void 0,void 0,function*(){let t=this._stream.readable.getReader(),s="",n=0,i=new Uint8Array;try{for(;;){let{done:r,value:o}=yield t.read();if(o.length>0){if("json"===this._protocol)for(s+=this._utf8decoder.decode(o);n<s.length;)if("\n"===s[n]){let t=s.substring(0,n);e.dispatchEvent(new MessageEvent("message",{data:t})),s=s.substring(n+1),n=0}else++n;else{let t=new Uint8Array(i.length+o.length);for(t.set(i),t.set(o,i.length),i=t;;){let t=this.options.decoder.decodeReply(i);if(t.ok){let s=i.slice(0,t.pos);e.dispatchEvent(new MessageEvent("message",{data:s})),i=i.slice(t.pos);continue}break}}}if(r)break}}catch(t){e.dispatchEvent(new Event("close"))}})}close(){return n(this,void 0,void 0,function*(){try{this._writer&&(yield this._writer.close()),this._transport.close()}catch(e){}})}send(e){return n(this,void 0,void 0,function*(){let t;t="json"===this._protocol?new TextEncoder().encode(e+"\n"):e;try{yield this._writer.write(t)}catch(e){this.close()}})}}let G=[-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,0,1,2,3,4,5,6,7,8,9,-1,-1,-1,-1,-1,-1,-1,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,-1,-1,-1,-1,36,-1,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,-1,-1,-1,63,-1];class V{constructor(e){this.a=e,this.pos=0}haveBytes(){return this.pos<this.a.length}getByte(){let e=this.a[this.pos];if(this.pos++,this.pos>this.a.length)throw RangeError("out of bounds");return e}getChar(){return String.fromCharCode(this.getByte())}getInt(){let e,t=0;for(;this.haveBytes()&&(e=G[127&this.getByte()])>=0;)t=(t<<6)+e;return this.pos--,t>>>0}}class Z{constructor(){this.a=[]}toByteArray(e){return Array.isArray(e)?this.a:new Uint8Array(this.a)}putArray(e,t,s){for(let n=t;n<s;n++)this.a.push(e[n])}}class H{name(){return"json"}encodeCommands(e){return e.map(e=>JSON.stringify(e)).join("\n")}decodeReplies(e){return e.trim().split("\n").map(e=>JSON.parse(e))}applyDeltaIfNeeded(e,t){let s,n;if(e.delta){let i=function(e,t){let s=0,n=new V(t),i=e.length,r=t.length,o=n.getInt();if("\n"!==n.getChar())throw Error("size integer not terminated by '\\n'");let c=new Z;for(;n.haveBytes();){let t;let a=n.getInt();switch(n.getChar()){case"@":if(t=n.getInt(),n.haveBytes()&&","!==n.getChar())throw Error("copy command not terminated by ','");if((s+=a)>o)throw Error("copy exceeds output file size");if(t+a>i)throw Error("copy extends past end of input");c.putArray(e,t,t+a);break;case":":if((s+=a)>o)throw Error("insert command gives an output larger than predicted");if(a>r)throw Error("insert count exceeds size of delta");c.putArray(n.a,n.pos,n.pos+a),n.pos+=a;break;case";":{let t=c.toByteArray(e);if(a!==function(e){let t=0,s=0,n=0,i=0,r=0,o=e.length;for(;o>=16;)t=t+e[r+0]|0,s=s+e[r+1]|0,n=n+e[r+2]|0,i=i+e[r+3]|0,t=t+e[r+4]|0,s=s+e[r+5]|0,n=n+e[r+6]|0,i=i+e[r+7]|0,t=t+e[r+8]|0,s=s+e[r+9]|0,n=n+e[r+10]|0,i=i+e[r+11]|0,t=t+e[r+12]|0,s=s+e[r+13]|0,n=n+e[r+14]|0,i=i+e[r+15]|0,r+=16,o-=16;for(;o>=4;)t=t+e[r+0]|0,s=s+e[r+1]|0,n=n+e[r+2]|0,i=i+e[r+3]|0,r+=4,o-=4;switch(i=((i+(n<<8)|0)+(s<<16)|0)+(t<<24)|0,o){case 3:i=i+(e[r+2]<<8)|0;case 2:i=i+(e[r+1]<<16)|0;case 1:i=i+(e[r+0]<<24)|0}return i>>>0}(t))throw Error("bad checksum");if(s!==o)throw Error("generated size does not match predicted size");return t}default:throw Error("unknown delta operator")}}throw Error("unterminated delta")}(t,new TextEncoder().encode(e.data));s=JSON.parse(new TextDecoder().decode(i)),n=i}else s=JSON.parse(e.data),n=new TextEncoder().encode(e.data);return{newData:s,newPrevValue:n}}}let K={headers:{},token:"",getToken:null,data:null,getData:null,debug:!1,name:"js",version:"",fetch:null,readableStream:null,websocket:null,eventsource:null,sockjs:null,sockjsOptions:{},emulationEndpoint:"/emulation",minReconnectDelay:500,maxReconnectDelay:2e4,timeout:5e3,maxServerPingDelay:1e4,networkEventTarget:null};class X extends Error{constructor(e){super(e),this.name=this.constructor.name}}class Y extends A{constructor(e,t){super(),this._reconnectTimeout=null,this._refreshTimeout=null,this._serverPingTimeout=null,this.state=m.Disconnected,this._transportIsOpen=!1,this._endpoint=e,this._emulation=!1,this._transports=[],this._currentTransportIndex=0,this._triedAllTransports=!1,this._transportWasOpen=!1,this._transport=null,this._transportId=0,this._deviceWentOffline=!1,this._transportClosed=!0,this._codec=new H,this._reconnecting=!1,this._reconnectTimeout=null,this._reconnectAttempts=0,this._client=null,this._session="",this._node="",this._subs={},this._serverSubs={},this._commandId=0,this._commands=[],this._batching=!1,this._refreshRequired=!1,this._refreshTimeout=null,this._callbacks={},this._token="",this._data=null,this._dispatchPromise=Promise.resolve(),this._serverPing=0,this._serverPingTimeout=null,this._sendPong=!1,this._promises={},this._promiseId=0,this._debugEnabled=!1,this._networkEventsSet=!1,this._config=Object.assign(Object.assign({},K),t),this._configure(),this._debugEnabled?(this.on("state",e=>{this._debug("client state",e.oldState,"->",e.newState)}),this.on("error",e=>{this._debug("client error",e)})):this.on("error",function(){Function.prototype()})}newSubscription(e,t){if(null!==this.getSubscription(e))throw Error("Subscription to the channel "+e+" already exists");let s=new N(this,e,t);return this._subs[e]=s,s}getSubscription(e){return this._getSub(e)}removeSubscription(e){e&&(e.state!==v.Unsubscribed&&e.unsubscribe(),this._removeSubscription(e))}subscriptions(){return this._subs}ready(e){switch(this.state){case m.Disconnected:return Promise.reject({code:_.clientDisconnected,message:"client disconnected"});case m.Connected:return Promise.resolve();default:return new Promise((t,s)=>{let n={resolve:t,reject:s};e&&(n.timeout=setTimeout(()=>{s({code:_.timeout,message:"timeout"})},e)),this._promises[this._nextPromiseId()]=n})}}connect(){if(this._isConnected()){this._debug("connect called when already connected");return}if(this._isConnecting()){this._debug("connect called when already connecting");return}this._debug("connect called"),this._reconnectAttempts=0,this._startConnecting()}disconnect(){this._disconnect(f.disconnectCalled,"disconnect called",!1)}setToken(e){this._token=e}setHeaders(e){this._config.headers=e}send(e){return n(this,void 0,void 0,function*(){if(yield this._methodCall(),!this._transportSendCommands([{send:{data:e}}]))throw this._createErrorObject(_.transportWriteError,"transport write error")})}rpc(e,t){return n(this,void 0,void 0,function*(){return yield this._methodCall(),{data:(yield this._callPromise({rpc:{method:e,data:t}},e=>e.rpc)).data}})}publish(e,t){return n(this,void 0,void 0,function*(){return yield this._methodCall(),yield this._callPromise({publish:{channel:e,data:t}},()=>({})),{}})}history(e,t){return n(this,void 0,void 0,function*(){let s={history:this._getHistoryRequest(e,t)};yield this._methodCall();let n=yield this._callPromise(s,e=>e.history),i=[];if(n.publications)for(let t=0;t<n.publications.length;t++)i.push(this._getPublicationContext(e,n.publications[t]));return{publications:i,epoch:n.epoch||"",offset:n.offset||0}})}presence(e){return n(this,void 0,void 0,function*(){yield this._methodCall();let t=(yield this._callPromise({presence:{channel:e}},e=>e.presence)).presence;for(let e in t)if(Object.prototype.hasOwnProperty.call(t,e)){let s=t[e],n=s.conn_info,i=s.chan_info;n&&(s.connInfo=n),i&&(s.chanInfo=i)}return{clients:t}})}presenceStats(e){return n(this,void 0,void 0,function*(){yield this._methodCall();let t=yield this._callPromise({presence_stats:{channel:e}},e=>e.presence_stats);return{numUsers:t.num_users,numClients:t.num_clients}})}startBatching(){this._batching=!0}stopBatching(){let e=this;Promise.resolve().then(function(){Promise.resolve().then(function(){e._batching=!1,e._flush()})})}_debug(...e){this._debugEnabled&&function(e,t){if(globalThis.console){let s=globalThis.console[e];U(s)&&s.apply(globalThis.console,t)}}("debug",e)}_codecName(){return this._codec.name()}_formatOverride(){}_configure(){if(!("Promise"in globalThis))throw Error("Promise polyfill required");if(!this._endpoint)throw Error("endpoint configuration required");if(null!==this._config.token&&(this._token=this._config.token),null!==this._config.data&&(this._data=this._config.data),this._codec=new H,this._formatOverride(),(!0===this._config.debug||"undefined"!=typeof localStorage&&localStorage.getItem("centrifuge.debug"))&&(this._debugEnabled=!0),this._debug("config",this._config),"string"==typeof this._endpoint);else if("object"==typeof this._endpoint&&this._endpoint instanceof Array){for(let e in this._transports=this._endpoint,this._emulation=!0,this._transports)if(this._transports.hasOwnProperty(e)){let t=this._transports[e];if(!t.endpoint||!t.transport)throw Error("malformed transport configuration");let s=t.transport;if(0>["websocket","http_stream","sse","sockjs","webtransport"].indexOf(s))throw Error("unsupported transport name: "+s)}}else throw Error("unsupported url configuration type: only string or array of objects are supported")}_setState(e){if(this.state!==e){this._reconnecting=!1;let t=this.state;return this.state=e,this.emit("state",{newState:e,oldState:t}),!0}return!1}_isDisconnected(){return this.state===m.Disconnected}_isConnecting(){return this.state===m.Connecting}_isConnected(){return this.state===m.Connected}_nextCommandId(){return++this._commandId}_setNetworkEvents(){if(this._networkEventsSet)return;let e=null;null!==this._config.networkEventTarget?e=this._config.networkEventTarget:void 0!==globalThis.addEventListener&&(e=globalThis),e&&(e.addEventListener("offline",()=>{this._debug("offline event triggered"),(this.state===m.Connected||this.state===m.Connecting)&&(this._disconnect(p.transportClosed,"transport closed",!0),this._deviceWentOffline=!0)}),e.addEventListener("online",()=>{this._debug("online event triggered"),this.state===m.Connecting&&(this._deviceWentOffline&&!this._transportClosed&&(this._deviceWentOffline=!1,this._transportClosed=!0),this._clearReconnectTimeout(),this._startReconnecting())}),this._networkEventsSet=!0)}_getReconnectDelay(){let e=z(this._reconnectAttempts,this._config.minReconnectDelay,this._config.maxReconnectDelay);return this._reconnectAttempts+=1,e}_clearOutgoingRequests(){for(let e in this._callbacks)if(this._callbacks.hasOwnProperty(e)){let t=this._callbacks[e];clearTimeout(t.timeout);let s=t.errback;if(!s)continue;s({error:this._createErrorObject(_.connectionClosed,"connection closed")})}this._callbacks={}}_clearConnectedState(){for(let e in this._client=null,this._clearServerPingTimeout(),this._clearRefreshTimeout(),this._subs){if(!this._subs.hasOwnProperty(e))continue;let t=this._subs[e];t.state===v.Subscribed&&t._setSubscribing(b.transportClosed,"transport closed")}for(let e in this._serverSubs)this._serverSubs.hasOwnProperty(e)&&this.emit("subscribing",{channel:e})}_handleWriteError(e){for(let t of e){let e=t.id;if(!(e in this._callbacks))continue;let s=this._callbacks[e];clearTimeout(this._callbacks[e].timeout),delete this._callbacks[e],(0,s.errback)({error:this._createErrorObject(_.transportWriteError,"transport write error")})}}_transportSendCommands(e){if(!e.length)return!0;if(!this._transport)return!1;try{this._transport.send(this._codec.encodeCommands(e),this._session,this._node)}catch(t){return this._debug("error writing commands",t),this._handleWriteError(e),!1}return!0}_initializeTransport(){let e,t;null!==this._config.websocket?e=this._config.websocket:"function"!=typeof globalThis.WebSocket&&"object"!=typeof globalThis.WebSocket||(e=globalThis.WebSocket);let s=null;null!==this._config.sockjs?s=this._config.sockjs:void 0!==globalThis.SockJS&&(s=globalThis.SockJS);let n=null;null!==this._config.eventsource?n=this._config.eventsource:void 0!==globalThis.EventSource&&(n=globalThis.EventSource);let i=null;null!==this._config.fetch?i=this._config.fetch:void 0!==globalThis.fetch&&(i=globalThis.fetch);let r=null;if(null!==this._config.readableStream?r=this._config.readableStream:void 0!==globalThis.ReadableStream&&(r=globalThis.ReadableStream),this._emulation){this._currentTransportIndex>=this._transports.length&&(this._triedAllTransports=!0,this._currentTransportIndex=0);let t=0;for(;;){if(t>=this._transports.length)throw Error("no supported transport found");let o=this._transports[this._currentTransportIndex],c=o.transport,a=o.endpoint;if("websocket"===c){if(this._debug("trying websocket transport"),this._transport=new F(a,{websocket:e}),!this._transport.supported()){this._debug("websocket transport not available"),this._currentTransportIndex++,t++;continue}}else if("webtransport"===c){if(this._debug("trying webtransport transport"),this._transport=new q(a,{webtransport:globalThis.WebTransport,decoder:this._codec,encoder:this._codec}),!this._transport.supported()){this._debug("webtransport transport not available"),this._currentTransportIndex++,t++;continue}}else if("http_stream"===c){if(this._debug("trying http_stream transport"),this._transport=new J(a,{fetch:i,readableStream:r,emulationEndpoint:this._config.emulationEndpoint,decoder:this._codec,encoder:this._codec}),!this._transport.supported()){this._debug("http_stream transport not available"),this._currentTransportIndex++,t++;continue}}else if("sse"===c){if(this._debug("trying sse transport"),this._transport=new B(a,{eventsource:n,fetch:i,emulationEndpoint:this._config.emulationEndpoint}),!this._transport.supported()){this._debug("sse transport not available"),this._currentTransportIndex++,t++;continue}}else if("sockjs"===c){if(this._debug("trying sockjs"),this._transport=new W(a,{sockjs:s,sockjsOptions:this._config.sockjsOptions}),!this._transport.supported()){this._debug("sockjs transport not available"),this._currentTransportIndex++,t++;continue}}else throw Error("unknown transport "+c);break}}else{var o,c;if(o=this._endpoint,c="http",0===o.lastIndexOf(c,0))throw Error("Provide explicit transport endpoints configuration in case of using HTTP (i.e. using array of TransportEndpoint instead of a single string), or use ws(s):// scheme in an endpoint if you aimed using WebSocket transport");if(this._debug("client will use websocket"),this._transport=new F(this._endpoint,{websocket:e}),!this._transport.supported())throw Error("WebSocket constructor not found, make sure it is available globally or passed as a dependency in Centrifuge options")}let a=this,h=this._transport,l=this._nextTransportId();a._debug("id of transport",l);let u=!1,d=[];if(this._transport.emulation()){let e=a._sendConnect(!0);d.push(e)}this._setNetworkEvents();let b=this._codec.encodeCommands(d);this._transportClosed=!1,t=setTimeout(function(){h.close()},this._config.timeout),this._transport.initialize(this._codecName(),{onOpen:function(){if(t&&(clearTimeout(t),t=null),a._transportId!=l){a._debug("open callback from non-actual transport"),h.close();return}u=!0,a._debug(h.subName(),"transport open"),h.emulation()||(a._transportIsOpen=!0,a._transportWasOpen=!0,a.startBatching(),a._sendConnect(!1),a._sendSubscribeCommands(),a.stopBatching(),a.emit("__centrifuge_debug:connect_frame_sent",{}))},onError:function(e){if(a._transportId!=l){a._debug("error callback from non-actual transport");return}a._debug("transport level error",e)},onClose:function(e){if(t&&(clearTimeout(t),t=null),a._transportId!=l){a._debug("close callback from non-actual transport");return}a._debug(h.subName(),"transport closed"),a._transportClosed=!0,a._transportIsOpen=!1;let s="connection closed",n=!0,i=0;if(e&&"code"in e&&e.code&&(i=e.code),e&&e.reason)try{let t=JSON.parse(e.reason);s=t.reason,n=t.reconnect}catch(t){s=e.reason,(i>=3500&&i<4e3||i>=4500&&i<5e3)&&(n=!1)}i<3e3?(1009===i?(i=f.messageSizeLimit,s="message size limit exceeded",n=!1):(i=p.transportClosed,s="transport closed"),a._emulation&&!a._transportWasOpen&&(a._currentTransportIndex++,a._currentTransportIndex>=a._transports.length&&(a._triedAllTransports=!0,a._currentTransportIndex=0))):a._transportWasOpen=!0,a._isConnecting()&&!u&&a.emit("error",{type:"transport",error:{code:_.transportClosed,message:"transport closed"},transport:h.name()}),a._reconnecting=!1,a._disconnect(i,s,n)},onMessage:function(e){a._dataReceived(e)}},b),a.emit("__centrifuge_debug:transport_initialized",{})}_sendConnect(e){let t=this._constructConnectCommand(),s=this;return this._call(t,e).then(e=>{let t=e.reply.connect;s._connectResponse(t),e.next&&e.next()},e=>{s._connectError(e.error),e.next&&e.next()}),t}_startReconnecting(){if(this._debug("start reconnecting"),!this._isConnecting()){this._debug("stop reconnecting: client not in connecting state");return}if(this._reconnecting){this._debug("reconnect already in progress, return from reconnect routine");return}if(!1===this._transportClosed){this._debug("waiting for transport close");return}this._reconnecting=!0;let e=""===this._token;if(!(this._refreshRequired||e&&null!==this._config.getToken)){this._config.getData?this._config.getData().then(e=>{this._isConnecting()&&(this._data=e,this._initializeTransport())}).catch(e=>this._handleGetDataError(e)):this._initializeTransport();return}let t=this;this._getToken().then(function(e){if(t._isConnecting()){if(null==e||void 0==e){t._failUnauthorized();return}t._token=e,t._debug("connection token refreshed"),t._config.getData?t._config.getData().then(function(e){t._isConnecting()&&(t._data=e,t._initializeTransport())}).catch(e=>t._handleGetDataError(e)):t._initializeTransport()}}).catch(function(e){if(!t._isConnecting())return;if(e instanceof X){t._failUnauthorized();return}t.emit("error",{type:"connectToken",error:{code:_.clientConnectToken,message:void 0!==e?e.toString():""}});let s=t._getReconnectDelay();t._debug("error on getting connection token, reconnect after "+s+" milliseconds",e),t._reconnecting=!1,t._reconnectTimeout=setTimeout(()=>{t._startReconnecting()},s)})}_handleGetDataError(e){if(e instanceof X){this._failUnauthorized();return}this.emit("error",{type:"connectData",error:{code:_.badConfiguration,message:(null==e?void 0:e.toString())||""}});let t=this._getReconnectDelay();this._debug("error on getting connect data, reconnect after "+t+" milliseconds",e),this._reconnecting=!1,this._reconnectTimeout=setTimeout(()=>{this._startReconnecting()},t)}_connectError(e){this.state===m.Connecting&&(109===e.code&&(this._refreshRequired=!0),e.code<100||!0===e.temporary||109===e.code?(this.emit("error",{type:"connect",error:e}),this._debug("closing transport due to connect error"),this._disconnect(e.code,e.message,!0)):this._disconnect(e.code,e.message,!1))}_scheduleReconnect(){if(!this._isConnecting())return;let e=!1;!this._emulation||this._transportWasOpen||this._triedAllTransports||(e=!0);let t=this._getReconnectDelay();e&&(t=0),this._debug("reconnect after "+t+" milliseconds"),this._clearReconnectTimeout(),this._reconnectTimeout=setTimeout(()=>{this._startReconnecting()},t)}_constructConnectCommand(){let e={};this._token&&(e.token=this._token),this._data&&(e.data=this._data),this._config.name&&(e.name=this._config.name),this._config.version&&(e.version=this._config.version),Object.keys(this._config.headers).length>0&&(e.headers=this._config.headers);let t={},s=!1;for(let e in this._serverSubs)if(this._serverSubs.hasOwnProperty(e)&&this._serverSubs[e].recoverable){s=!0;let n={recover:!0};this._serverSubs[e].offset&&(n.offset=this._serverSubs[e].offset),this._serverSubs[e].epoch&&(n.epoch=this._serverSubs[e].epoch),t[e]=n}return s&&(e.subs=t),{connect:e}}_getHistoryRequest(e,t){let s={channel:e};return void 0!==t&&(t.since&&(s.since={offset:t.since.offset},t.since.epoch&&(s.since.epoch=t.since.epoch)),void 0!==t.limit&&(s.limit=t.limit),!0===t.reverse&&(s.reverse=!0)),s}_methodCall(){return this._isConnected()?Promise.resolve():new Promise((e,t)=>{let s=setTimeout(function(){t({code:_.timeout,message:"timeout"})},this._config.timeout);this._promises[this._nextPromiseId()]={timeout:s,resolve:e,reject:t}})}_callPromise(e,t){return new Promise((s,n)=>{this._call(e,!1).then(e=>{var n;s(t(e.reply)),null===(n=e.next)||void 0===n||n.call(e)},e=>{var t;n(e.error),null===(t=e.next)||void 0===t||t.call(e)})})}_dataReceived(e){this._serverPing>0&&this._waitServerPing();let t=this._codec.decodeReplies(e);this._dispatchPromise=this._dispatchPromise.then(()=>{let e;this._dispatchPromise=new Promise(t=>{e=t}),this._dispatchSynchronized(t,e)})}_dispatchSynchronized(e,t){let s=Promise.resolve();for(let t in e)e.hasOwnProperty(t)&&(s=s.then(()=>this._dispatchReply(e[t])));s.then(()=>{t()})}_dispatchReply(e){let t;let s=new Promise(e=>{t=e});if(null==e)return this._debug("dispatch: got undefined or null reply"),t(),s;let n=e.id;return n&&n>0?this._handleReply(e,t):e.push?this._handlePush(e.push,t):this._handleServerPing(t),s}_call(e,t){return new Promise((s,n)=>{e.id=this._nextCommandId(),this._registerCall(e.id,s,n),t||this._addCommand(e)})}_startConnecting(){this._debug("start connecting"),this._setState(m.Connecting)&&this.emit("connecting",{code:p.connectCalled,reason:"connect called"}),this._client=null,this._startReconnecting()}_disconnect(e,t,s){if(this._isDisconnected())return;this._transportIsOpen=!1;let n=this.state;this._reconnecting=!1;let i={code:e,reason:t},r=!1;if(s?r=this._setState(m.Connecting):(r=this._setState(m.Disconnected),this._rejectPromises({code:_.clientDisconnected,message:"disconnected"})),this._clearOutgoingRequests(),n===m.Connecting&&this._clearReconnectTimeout(),n===m.Connected&&this._clearConnectedState(),r&&(this._isConnecting()?this.emit("connecting",i):this.emit("disconnected",i)),this._transport){this._debug("closing existing transport");let e=this._transport;this._transport=null,e.close(),this._transportClosed=!0,this._nextTransportId()}else this._debug("no transport to close");this._scheduleReconnect()}_failUnauthorized(){this._disconnect(f.unauthorized,"unauthorized",!1)}_getToken(){return(this._debug("get connection token"),this._config.getToken)?this._config.getToken({}):(this.emit("error",{type:"configuration",error:{code:_.badConfiguration,message:"token expired but no getToken function set in the configuration"}}),Promise.reject(new X("")))}_refresh(){let e=this._client,t=this;this._getToken().then(function(s){if(e!==t._client)return;if(!s){t._failUnauthorized();return}if(t._token=s,t._debug("connection token refreshed"),!t._isConnected())return;let n={refresh:{token:t._token}};t._call(n,!1).then(e=>{let s=e.reply.refresh;t._refreshResponse(s),e.next&&e.next()},e=>{t._refreshError(e.error),e.next&&e.next()})}).catch(function(e){if(t._isConnected()){if(e instanceof X){t._failUnauthorized();return}t.emit("error",{type:"refreshToken",error:{code:_.clientRefreshToken,message:void 0!==e?e.toString():""}}),t._refreshTimeout=setTimeout(()=>t._refresh(),t._getRefreshRetryDelay())}})}_refreshError(e){e.code<100||!0===e.temporary?(this.emit("error",{type:"refresh",error:e}),this._refreshTimeout=setTimeout(()=>this._refresh(),this._getRefreshRetryDelay())):this._disconnect(e.code,e.message,!1)}_getRefreshRetryDelay(){return z(0,5e3,1e4)}_refreshResponse(e){this._refreshTimeout&&(clearTimeout(this._refreshTimeout),this._refreshTimeout=null),e.expires&&(this._client=e.client,this._refreshTimeout=setTimeout(()=>this._refresh(),M(e.ttl)))}_removeSubscription(e){null!==e&&delete this._subs[e.channel]}_unsubscribe(e){if(!this._transportIsOpen)return Promise.resolve();let t={unsubscribe:{channel:e.channel}},s=this;return new Promise((e,n)=>{this._call(t,!1).then(t=>{e(),t.next&&t.next()},t=>{e(),t.next&&t.next(),s._disconnect(p.unsubscribeError,"unsubscribe error",!0)})})}_getSub(e){return this._subs[e]||null}_isServerSub(e){return void 0!==this._serverSubs[e]}_sendSubscribeCommands(){let e=[];for(let t in this._subs){if(!this._subs.hasOwnProperty(t))continue;let s=this._subs[t];if(!0!==s._inflight&&s.state===v.Subscribing){let t=s._subscribe();t&&e.push(t)}}return e}_connectResponse(e){if(this._transportIsOpen=!0,this._transportWasOpen=!0,this._reconnectAttempts=0,this._refreshRequired=!1,this._isConnected())return;this._client=e.client,this._setState(m.Connected),this._refreshTimeout&&clearTimeout(this._refreshTimeout),e.expires&&(this._refreshTimeout=setTimeout(()=>this._refresh(),M(e.ttl))),this._session=e.session,this._node=e.node,this.startBatching(),this._sendSubscribeCommands(),this.stopBatching();let t={client:e.client,transport:this._transport.subName()};e.data&&(t.data=e.data),this.emit("connected",t),this._resolvePromises(),this._processServerSubs(e.subs||{}),e.ping&&e.ping>0?(this._serverPing=1e3*e.ping,this._sendPong=!0===e.pong,this._waitServerPing()):this._serverPing=0}_processServerSubs(e){for(let t in e){if(!e.hasOwnProperty(t))continue;let s=e[t];this._serverSubs[t]={offset:s.offset,epoch:s.epoch,recoverable:s.recoverable||!1};let n=this._getSubscribeContext(t,s);this.emit("subscribed",n)}for(let t in e){if(!e.hasOwnProperty(t))continue;let s=e[t];if(s.recovered){let e=s.publications;if(e&&e.length>0)for(let s in e)e.hasOwnProperty(s)&&this._handlePublication(t,e[s])}}for(let t in this._serverSubs)this._serverSubs.hasOwnProperty(t)&&(e[t]||(this.emit("unsubscribed",{channel:t}),delete this._serverSubs[t]))}_clearRefreshTimeout(){null!==this._refreshTimeout&&(clearTimeout(this._refreshTimeout),this._refreshTimeout=null)}_clearReconnectTimeout(){null!==this._reconnectTimeout&&(clearTimeout(this._reconnectTimeout),this._reconnectTimeout=null)}_clearServerPingTimeout(){null!==this._serverPingTimeout&&(clearTimeout(this._serverPingTimeout),this._serverPingTimeout=null)}_waitServerPing(){0!==this._config.maxServerPingDelay&&this._isConnected()&&(this._clearServerPingTimeout(),this._serverPingTimeout=setTimeout(()=>{this._isConnected()&&this._disconnect(p.noPing,"no ping",!0)},this._serverPing+this._config.maxServerPingDelay))}_getSubscribeContext(e,t){let s={channel:e,positioned:!1,recoverable:!1,wasRecovering:!1,recovered:!1,hasRecoveredPublications:!1};t.recovered&&(s.recovered=!0),t.positioned&&(s.positioned=!0),t.recoverable&&(s.recoverable=!0),t.was_recovering&&(s.wasRecovering=!0);let n="";"epoch"in t&&(n=t.epoch);let i=0;return"offset"in t&&(i=t.offset),(s.positioned||s.recoverable)&&(s.streamPosition={offset:i,epoch:n}),Array.isArray(t.publications)&&t.publications.length>0&&(s.hasRecoveredPublications=!0),t.data&&(s.data=t.data),s}_handleReply(e,t){let s=e.id;if(!(s in this._callbacks)){t();return}let n=this._callbacks[s];if(clearTimeout(this._callbacks[s].timeout),delete this._callbacks[s],"error"in e&&null!==e.error){let s=n.errback;if(!s){t();return}s({error:{code:e.error.code,message:e.error.message||"",temporary:e.error.temporary||!1},next:t})}else{let s=n.callback;if(!s)return;s({reply:e,next:t})}}_handleJoin(e,t){let s=this._getSub(e);if(!s){if(this._isServerSub(e)){let s={channel:e,info:this._getJoinLeaveContext(t.info)};this.emit("join",s)}return}s._handleJoin(t)}_handleLeave(e,t){let s=this._getSub(e);if(!s){if(this._isServerSub(e)){let s={channel:e,info:this._getJoinLeaveContext(t.info)};this.emit("leave",s)}return}s._handleLeave(t)}_handleUnsubscribe(e,t){let s=this._getSub(e);if(!s){this._isServerSub(e)&&(delete this._serverSubs[e],this.emit("unsubscribed",{channel:e}));return}t.code<2500?s._setUnsubscribed(t.code,t.reason,!1):s._setSubscribing(t.code,t.reason)}_handleSubscribe(e,t){this._serverSubs[e]={offset:t.offset,epoch:t.epoch,recoverable:t.recoverable||!1},this.emit("subscribed",this._getSubscribeContext(e,t))}_handleDisconnect(e){let t=e.code,s=!0;(t>=3500&&t<4e3||t>=4500&&t<5e3)&&(s=!1),this._disconnect(t,e.reason,s)}_getPublicationContext(e,t){let s={channel:e,data:t.data};return t.offset&&(s.offset=t.offset),t.info&&(s.info=this._getJoinLeaveContext(t.info)),t.tags&&(s.tags=t.tags),s}_getJoinLeaveContext(e){let t={client:e.client,user:e.user},s=e.conn_info;s&&(t.connInfo=s);let n=e.chan_info;return n&&(t.chanInfo=n),t}_handlePublication(e,t){let s=this._getSub(e);if(!s){if(this._isServerSub(e)){let s=this._getPublicationContext(e,t);this.emit("publication",s),void 0!==t.offset&&(this._serverSubs[e].offset=t.offset)}return}s._handlePublication(t)}_handleMessage(e){this.emit("message",{data:e.data})}_handleServerPing(e){this._sendPong&&this._transportSendCommands([{}]),e()}_handlePush(e,t){let s=e.channel;e.pub?this._handlePublication(s,e.pub):e.message?this._handleMessage(e.message):e.join?this._handleJoin(s,e.join):e.leave?this._handleLeave(s,e.leave):e.unsubscribe?this._handleUnsubscribe(s,e.unsubscribe):e.subscribe?this._handleSubscribe(s,e.subscribe):e.disconnect&&this._handleDisconnect(e.disconnect),t()}_flush(){let e=this._commands.slice(0);this._commands=[],this._transportSendCommands(e)}_createErrorObject(e,t,s){let n={code:e,message:t};return s&&(n.temporary=!0),n}_registerCall(e,t,s){this._callbacks[e]={callback:t,errback:s,timeout:null},this._callbacks[e].timeout=setTimeout(()=>{delete this._callbacks[e],U(s)&&s({error:this._createErrorObject(_.timeout,"timeout")})},this._config.timeout)}_addCommand(e){this._batching?this._commands.push(e):this._transportSendCommands([e])}_nextPromiseId(){return++this._promiseId}_nextTransportId(){return++this._transportId}_resolvePromises(){for(let e in this._promises)this._promises.hasOwnProperty(e)&&(this._promises[e].timeout&&clearTimeout(this._promises[e].timeout),this._promises[e].resolve(),delete this._promises[e])}_rejectPromises(e){for(let t in this._promises)this._promises.hasOwnProperty(t)&&(this._promises[t].timeout&&clearTimeout(this._promises[t].timeout),this._promises[t].reject(e),delete this._promises[t])}}Y.SubscriptionState=v,Y.State=m,Y.UnauthorizedError=X}}]);