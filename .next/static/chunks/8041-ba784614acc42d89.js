(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8041,6081,8024],{60971:function(e,t,r){var n=0/0,o=/^\s+|\s+$/g,a=/^[-+]0x[0-9a-f]+$/i,i=/^0b[01]+$/i,u=/^0o[0-7]+$/i,c=parseInt,s="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g,l="object"==typeof self&&self&&self.Object===Object&&self,d=s||l||Function("return this")(),f=Object.prototype.toString,p=Math.max,v=Math.min,h=function(){return d.Date.now()};function m(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function y(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||t&&"object"==typeof t&&"[object Symbol]"==f.call(t))return n;if(m(e)){var t,r="function"==typeof e.valueOf?e.valueOf():e;e=m(r)?r+"":r}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(o,"");var s=i.test(e);return s||u.test(e)?c(e.slice(2),s?2:8):a.test(e)?n:+e}e.exports=function(e,t,r){var n,o,a,i,u,c,s=0,l=!1,d=!1,f=!0;if("function"!=typeof e)throw TypeError("Expected a function");function b(t){var r=n,a=o;return n=o=void 0,s=t,i=e.apply(a,r)}function w(e){var r=e-c,n=e-s;return void 0===c||r>=t||r<0||d&&n>=a}function g(){var e,r,n,o=h();if(w(o))return x(o);u=setTimeout(g,(e=o-c,r=o-s,n=t-e,d?v(n,a-r):n))}function x(e){return(u=void 0,f&&n)?b(e):(n=o=void 0,i)}function k(){var e,r=h(),a=w(r);if(n=arguments,o=this,c=r,a){if(void 0===u)return s=e=c,u=setTimeout(g,t),l?b(e):i;if(d)return u=setTimeout(g,t),b(c)}return void 0===u&&(u=setTimeout(g,t)),i}return t=y(t)||0,m(r)&&(l=!!r.leading,a=(d="maxWait"in r)?p(y(r.maxWait)||0,t):a,f="trailing"in r?!!r.trailing:f),k.cancel=function(){void 0!==u&&clearTimeout(u),s=0,n=c=o=u=void 0},k.flush=function(){return void 0===u?i:x(h())},k}},23625:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},28780:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},32425:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},76892:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("File",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]])},37313:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},57292:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},79624:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("SendHorizontal",[["path",{d:"m3 3 3 9-3 9 19-9Z",key:"1aobqy"}],["path",{d:"M6 12h16",key:"s4cdu5"}]])},53189:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},16669:function(e,t,r){"use strict";r.d(t,{default:function(){return o.a}});var n=r(6092),o=r.n(n)},82183:function(e,t,r){"use strict";/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n=r(32486),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,i=n.useEffect,u=n.useLayoutEffect,c=n.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!o(e,r)}catch(e){return!0}}var l="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),o=n[0].inst,l=n[1];return u(function(){o.value=r,o.getSnapshot=t,s(o)&&l({inst:o})},[e,r,t]),i(function(){return s(o)&&l({inst:o}),e(function(){s(o)&&l({inst:o})})},[e]),c(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:l},37286:function(e,t,r){"use strict";e.exports=r(82183)},34450:function(e,t,r){"use strict";r.d(t,{NY:function(){return E},Ee:function(){return j},fC:function(){return k}});var n=r(32486),o=r(32192),a=r(15920),i=r(79315),u=r(89801),c=r(37286);function s(){return()=>{}}var l=r(75376),d="Avatar",[f,p]=(0,o.b)(d),[v,h]=f(d),m=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...o}=e,[a,i]=n.useState("idle");return(0,l.jsx)(v,{scope:r,imageLoadingStatus:a,onImageLoadingStatusChange:i,children:(0,l.jsx)(u.WV.span,{...o,ref:t})})});m.displayName=d;var y="AvatarImage",b=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:o,onLoadingStatusChange:d=()=>{},...f}=e,p=h(y,r),v=function(e,t){let{referrerPolicy:r,crossOrigin:o}=t,a=(0,c.useSyncExternalStore)(s,()=>!0,()=>!1),u=n.useRef(null),l=a?(u.current||(u.current=new window.Image),u.current):null,[d,f]=n.useState(()=>x(l,e));return(0,i.b)(()=>{f(x(l,e))},[l,e]),(0,i.b)(()=>{let e=e=>()=>{f(e)};if(!l)return;let t=e("loaded"),n=e("error");return l.addEventListener("load",t),l.addEventListener("error",n),r&&(l.referrerPolicy=r),"string"==typeof o&&(l.crossOrigin=o),()=>{l.removeEventListener("load",t),l.removeEventListener("error",n)}},[l,o,r]),d}(o,f),m=(0,a.W)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,i.b)(()=>{"idle"!==v&&m(v)},[v,m]),"loaded"===v?(0,l.jsx)(u.WV.img,{...f,ref:t,src:o}):null});b.displayName=y;var w="AvatarFallback",g=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:o,...a}=e,i=h(w,r),[c,s]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>s(!0),o);return()=>window.clearTimeout(e)}},[o]),c&&"loaded"!==i.imageLoadingStatus?(0,l.jsx)(u.WV.span,{...a,ref:t}):null});function x(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}g.displayName=w;var k=m,j=b,E=g},95833:function(e,t,r){"use strict";r.d(t,{fC:function(){return x},z$:function(){return j}});var n=r(32486),o=r(29626),a=r(32192),i=r(20100),u=r(31413),c=r(75659),s=r(30915),l=r(53486),d=r(89801),f=r(75376),p="Checkbox",[v,h]=(0,a.b)(p),[m,y]=v(p);function b(e){let{__scopeCheckbox:t,checked:r,children:o,defaultChecked:a,disabled:i,form:c,name:s,onCheckedChange:l,required:d,value:v="on",internal_do_not_use_render:h}=e,[y,b]=(0,u.T)({prop:r,defaultProp:null!=a&&a,onChange:l,caller:p}),[w,g]=n.useState(null),[x,k]=n.useState(null),j=n.useRef(!1),E=!w||!!c||!!w.closest("form"),R={checked:y,disabled:i,setChecked:b,control:w,setControl:g,name:s,form:c,value:v,hasConsumerStoppedPropagationRef:j,required:d,defaultChecked:!C(a)&&a,isFormControl:E,bubbleInput:x,setBubbleInput:k};return(0,f.jsx)(m,{scope:t,...R,children:"function"==typeof h?h(R):o})}var w="CheckboxTrigger",g=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,onKeyDown:a,onClick:u,...c}=e,{control:s,value:l,disabled:p,checked:v,required:h,setControl:m,setChecked:b,hasConsumerStoppedPropagationRef:g,isFormControl:x,bubbleInput:k}=y(w,r),j=(0,o.e)(t,m),E=n.useRef(v);return n.useEffect(()=>{let e=null==s?void 0:s.form;if(e){let t=()=>b(E.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[s,b]),(0,f.jsx)(d.WV.button,{type:"button",role:"checkbox","aria-checked":C(v)?"mixed":v,"aria-required":h,"data-state":S(v),"data-disabled":p?"":void 0,disabled:p,value:l,...c,ref:j,onKeyDown:(0,i.M)(a,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.M)(u,e=>{b(e=>!!C(e)||!e),k&&x&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})})});g.displayName=w;var x=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:o,defaultChecked:a,required:i,disabled:u,value:c,onCheckedChange:s,form:l,...d}=e;return(0,f.jsx)(b,{__scopeCheckbox:r,checked:o,defaultChecked:a,disabled:u,required:i,onCheckedChange:s,name:n,form:l,value:c,internal_do_not_use_render:e=>{let{isFormControl:n}=e;return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(g,{...d,ref:t,__scopeCheckbox:r}),n&&(0,f.jsx)(R,{__scopeCheckbox:r})]})}})});x.displayName=p;var k="CheckboxIndicator",j=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...o}=e,a=y(k,r);return(0,f.jsx)(l.z,{present:n||C(a.checked)||!0===a.checked,children:(0,f.jsx)(d.WV.span,{"data-state":S(a.checked),"data-disabled":a.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});j.displayName=k;var E="CheckboxBubbleInput",R=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,...a}=e,{control:i,hasConsumerStoppedPropagationRef:u,checked:l,defaultChecked:p,required:v,disabled:h,name:m,value:b,form:w,bubbleInput:g,setBubbleInput:x}=y(E,r),k=(0,o.e)(t,x),j=(0,c.D)(l),R=(0,s.t)(i);n.useEffect(()=>{if(!g)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!u.current;if(j!==l&&e){let r=new Event("click",{bubbles:t});g.indeterminate=C(l),e.call(g,!C(l)&&l),g.dispatchEvent(r)}},[g,j,l,u]);let S=n.useRef(!C(l)&&l);return(0,f.jsx)(d.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=p?p:S.current,required:v,disabled:h,name:m,value:b,form:w,...a,tabIndex:-1,ref:k,style:{...a.style,...R,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function C(e){return"indeterminate"===e}function S(e){return C(e)?"indeterminate":e?"checked":"unchecked"}R.displayName=E},398:function(e,t,r){"use strict";r.d(t,{gm:function(){return a}});var n=r(32486);r(75376);var o=n.createContext(void 0);function a(e){let t=n.useContext(o);return e||t||"ltr"}},40472:function(e,t,r){"use strict";r.d(t,{ck:function(){return W},fC:function(){return P},z$:function(){return Z}});var n=r(32486),o=r(20100),a=r(29626),i=r(32192),u=r(89801),c=r(50523),s=r(31413),l=r(398),d=r(30915),f=r(75659),p=r(53486),v=r(75376),h="Radio",[m,y]=(0,i.b)(h),[b,w]=m(h),g=n.forwardRef((e,t)=>{let{__scopeRadio:r,name:i,checked:c=!1,required:s,disabled:l,value:d="on",onCheck:f,form:p,...h}=e,[m,y]=n.useState(null),w=(0,a.e)(t,e=>y(e)),g=n.useRef(!1),x=!m||p||!!m.closest("form");return(0,v.jsxs)(b,{scope:r,checked:c,disabled:l,children:[(0,v.jsx)(u.WV.button,{type:"button",role:"radio","aria-checked":c,"data-state":E(c),"data-disabled":l?"":void 0,disabled:l,value:d,...h,ref:w,onClick:(0,o.M)(e.onClick,e=>{c||null==f||f(),x&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})}),x&&(0,v.jsx)(j,{control:m,bubbles:!g.current,name:i,value:d,checked:c,required:s,disabled:l,form:p,style:{transform:"translateX(-100%)"}})]})});g.displayName=h;var x="RadioIndicator",k=n.forwardRef((e,t)=>{let{__scopeRadio:r,forceMount:n,...o}=e,a=w(x,r);return(0,v.jsx)(p.z,{present:n||a.checked,children:(0,v.jsx)(u.WV.span,{"data-state":E(a.checked),"data-disabled":a.disabled?"":void 0,...o,ref:t})})});k.displayName=x;var j=n.forwardRef((e,t)=>{let{__scopeRadio:r,control:o,checked:i,bubbles:c=!0,...s}=e,l=n.useRef(null),p=(0,a.e)(l,t),h=(0,f.D)(i),m=(0,d.t)(o);return n.useEffect(()=>{let e=l.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==i&&t){let r=new Event("click",{bubbles:c});t.call(e,i),e.dispatchEvent(r)}},[h,i,c]),(0,v.jsx)(u.WV.input,{type:"radio","aria-hidden":!0,defaultChecked:i,...s,tabIndex:-1,ref:p,style:{...s.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function E(e){return e?"checked":"unchecked"}j.displayName="RadioBubbleInput";var R=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],C="RadioGroup",[S,I]=(0,i.b)(C,[c.Pc,y]),M=(0,c.Pc)(),T=y(),[D,V]=S(C),A=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,name:n,defaultValue:o,value:a,required:i=!1,disabled:d=!1,orientation:f,dir:p,loop:h=!0,onValueChange:m,...y}=e,b=M(r),w=(0,l.gm)(p),[g,x]=(0,s.T)({prop:a,defaultProp:null!=o?o:null,onChange:m,caller:C});return(0,v.jsx)(D,{scope:r,name:n,required:i,disabled:d,value:g,onValueChange:x,children:(0,v.jsx)(c.fC,{asChild:!0,...b,orientation:f,dir:w,loop:h,children:(0,v.jsx)(u.WV.div,{role:"radiogroup","aria-required":i,"aria-orientation":f,"data-disabled":d?"":void 0,dir:w,...y,ref:t})})})});A.displayName=C;var F="RadioGroupItem",L=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,disabled:i,...u}=e,s=V(F,r),l=s.disabled||i,d=M(r),f=T(r),p=n.useRef(null),h=(0,a.e)(t,p),m=s.value===u.value,y=n.useRef(!1);return n.useEffect(()=>{let e=e=>{R.includes(e.key)&&(y.current=!0)},t=()=>y.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,v.jsx)(c.ck,{asChild:!0,...d,focusable:!l,active:m,children:(0,v.jsx)(g,{disabled:l,required:s.required,checked:m,...f,...u,name:s.name,ref:h,onCheck:()=>s.onValueChange(u.value),onKeyDown:(0,o.M)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,o.M)(u.onFocus,()=>{var e;y.current&&(null===(e=p.current)||void 0===e||e.click())})})})});L.displayName=F;var N=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,...n}=e,o=T(r);return(0,v.jsx)(k,{...o,...n,ref:t})});N.displayName="RadioGroupIndicator";var P=A,W=L,Z=N},50523:function(e,t,r){"use strict";r.d(t,{Pc:function(){return x},ck:function(){return D},fC:function(){return T}});var n=r(32486),o=r(20100),a=r(35614),i=r(29626),u=r(32192),c=r(21971),s=r(89801),l=r(15920),d=r(31413),f=r(398),p=r(75376),v="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},m="RovingFocusGroup",[y,b,w]=(0,a.B)(m),[g,x]=(0,u.b)(m,[w]),[k,j]=g(m),E=n.forwardRef((e,t)=>(0,p.jsx)(y.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(y.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(R,{...e,ref:t})})}));E.displayName=m;var R=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:u=!1,dir:c,currentTabStopId:y,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:g,onEntryFocus:x,preventScrollOnEntryFocus:j=!1,...E}=e,R=n.useRef(null),C=(0,i.e)(t,R),S=(0,f.gm)(c),[I,T]=(0,d.T)({prop:y,defaultProp:null!=w?w:null,onChange:g,caller:m}),[D,V]=n.useState(!1),A=(0,l.W)(x),F=b(r),L=n.useRef(!1),[N,P]=n.useState(0);return n.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(v,A),()=>e.removeEventListener(v,A)},[A]),(0,p.jsx)(k,{scope:r,orientation:a,dir:S,loop:u,currentTabStopId:I,onItemFocus:n.useCallback(e=>T(e),[T]),onItemShiftTab:n.useCallback(()=>V(!0),[]),onFocusableItemAdd:n.useCallback(()=>P(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>P(e=>e-1),[]),children:(0,p.jsx)(s.WV.div,{tabIndex:D||0===N?-1:0,"data-orientation":a,...E,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{L.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!L.current;if(e.target===e.currentTarget&&t&&!D){let t=new CustomEvent(v,h);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=F().filter(e=>e.focusable);M([e.find(e=>e.active),e.find(e=>e.id===I),...e].filter(Boolean).map(e=>e.ref.current),j)}}L.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>V(!1))})})}),C="RovingFocusGroupItem",S=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:i=!1,tabStopId:u,children:l,...d}=e,f=(0,c.M)(),v=u||f,h=j(C,r),m=h.currentTabStopId===v,w=b(r),{onFocusableItemAdd:g,onFocusableItemRemove:x,currentTabStopId:k}=h;return n.useEffect(()=>{if(a)return g(),()=>x()},[a,g,x]),(0,p.jsx)(y.ItemSlot,{scope:r,id:v,focusable:a,active:i,children:(0,p.jsx)(s.WV.span,{tabIndex:m?0:-1,"data-orientation":h.orientation,...d,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a?h.onItemFocus(v):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>h.onItemFocus(v)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){h.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return I[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=w().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)o.reverse();else if("prev"===t||"next"===t){var r,n;"prev"===t&&o.reverse();let a=o.indexOf(e.currentTarget);o=h.loop?(r=o,n=a+1,r.map((e,t)=>r[(n+t)%r.length])):o.slice(a+1)}setTimeout(()=>M(o))}}),children:"function"==typeof l?l({isCurrentTabStop:m,hasTabStop:null!=k}):l})})});S.displayName=C;var I={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function M(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var T=E,D=S},73068:function(e,t,r){"use strict";r.d(t,{VY:function(){return D},aV:function(){return M},fC:function(){return I},xz:function(){return T}});var n=r(32486),o=r(20100),a=r(32192),i=r(50523),u=r(53486),c=r(89801),s=r(398),l=r(31413),d=r(21971),f=r(75376),p="Tabs",[v,h]=(0,a.b)(p,[i.Pc]),m=(0,i.Pc)(),[y,b]=v(p),w=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:o,defaultValue:a,orientation:i="horizontal",dir:u,activationMode:v="automatic",...h}=e,m=(0,s.gm)(u),[b,w]=(0,l.T)({prop:n,onChange:o,defaultProp:null!=a?a:"",caller:p});return(0,f.jsx)(y,{scope:r,baseId:(0,d.M)(),value:b,onValueChange:w,orientation:i,dir:m,activationMode:v,children:(0,f.jsx)(c.WV.div,{dir:m,"data-orientation":i,...h,ref:t})})});w.displayName=p;var g="TabsList",x=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...o}=e,a=b(g,r),u=m(r);return(0,f.jsx)(i.fC,{asChild:!0,...u,orientation:a.orientation,dir:a.dir,loop:n,children:(0,f.jsx)(c.WV.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})});x.displayName=g;var k="TabsTrigger",j=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:a=!1,...u}=e,s=b(k,r),l=m(r),d=C(s.baseId,n),p=S(s.baseId,n),v=n===s.value;return(0,f.jsx)(i.ck,{asChild:!0,...l,focusable:!a,active:v,children:(0,f.jsx)(c.WV.button,{type:"button",role:"tab","aria-selected":v,"aria-controls":p,"data-state":v?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:d,...u,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():s.onValueChange(n)}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&s.onValueChange(n)}),onFocus:(0,o.M)(e.onFocus,()=>{let e="manual"!==s.activationMode;v||a||!e||s.onValueChange(n)})})})});j.displayName=k;var E="TabsContent",R=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,forceMount:a,children:i,...s}=e,l=b(E,r),d=C(l.baseId,o),p=S(l.baseId,o),v=o===l.value,h=n.useRef(v);return n.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(u.z,{present:a||v,children:r=>{let{present:n}=r;return(0,f.jsx)(c.WV.div,{"data-state":v?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":d,hidden:!n,id:p,tabIndex:0,...s,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:n&&i})}})});function C(e,t){return"".concat(e,"-trigger-").concat(t)}function S(e,t){return"".concat(e,"-content-").concat(t)}R.displayName=E;var I=w,M=x,T=j,D=R},75659:function(e,t,r){"use strict";r.d(t,{D:function(){return o}});var n=r(32486);function o(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}}]);