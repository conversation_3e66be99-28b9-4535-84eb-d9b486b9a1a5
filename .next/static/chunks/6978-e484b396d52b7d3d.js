"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6978],{98154:function(e,l,t){t.d(l,{Z:function(){return E}});var a=t(75376),s=t(32486),n=t(47411),i=t(97712),o=t(97220),r=t(86418),d=()=>{let e=(0,n.useParams)().id,{state:l,dispatch:t}=(0,s.useContext)(o.R),a=localStorage.getItem("token")||"",[d,c]=(0,s.useState)(1),[u,x]=(0,s.useState)(!0),[h,v]=(0,s.useState)(!0),m=async function(){let l=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;try{let n=await (0,r.Gl)("/threads/channels/".concat(e,"?page=").concat(l,"&limit=80"));if((null==n?void 0:n.status)===200||(null==n?void 0:n.status)===201){var a,s;let e=Array.isArray(null===(a=n.data)||void 0===a?void 0:a.data)?null===(s=n.data)||void 0===s?void 0:s.data:[];t({type:i.a.MESSAGES,payload:{newThreads:e,newPage:l}}),l>1&&e.length>0?x(!0):x(e.length>=80)}v(!1)}catch(e){console.error("Error fetching threads:",e),x(!1)}finally{c(l)}};return(0,s.useEffect)(()=>{e&&a&&m(1).finally(()=>t({type:i.a.MESSAGE_LOADING,payload:!1}))},[e,a,t,null==l?void 0:l.countCallback]),{fetchMoreData:()=>{u&&m(d+1)},hasMore:u,loading:h}},c=t(70336),u=t(3371),x=t(39713),h=t(56792),v=()=>{let{state:e,dispatch:l}=(0,s.useContext)(o.R),{channelDetails:t}=e;return(0,a.jsx)("div",{className:"bg-gradient-to-b from-[#E3D9FE] via-white via-[73.49%] to-white to-[96.49%] h-[50vh]",children:(0,a.jsx)("div",{className:"px-5 pt-[150px]",children:(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)(x.default,{src:h.Z.disco,alt:"logo",width:64,height:64}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("h1",{className:"text-[28px] font-bold text-[#1D2939]",children:["Welcome to #",null==t?void 0:t.name]}),(0,a.jsxs)("p",{className:"text-[15px] text-[#475467]",children:["Share all information relating to ",null==t?void 0:t.name," here. All team members await you! \uD83D\uDE09"]}),(0,a.jsxs)("div",{onClick:()=>l({type:i.a.CHANNEL_INVITE,payload:!0}),className:"mt-5 border border-[#E6EAEF] px-2 py-3 rounded-[7px] max-w-[344px] flex items-center gap-[10px] cursor-pointer",children:[(0,a.jsx)("div",{children:(0,a.jsx)(x.default,{src:h.Z.teammates,alt:"teammates",width:32,height:32})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-[15px] font-semibold text-[#1D2939] mb-1",children:"Invite teammates"}),(0,a.jsx)("p",{className:"text-[13px] text-[#475467]",children:"Add your entire team in seconds"})]})]})]})]})})})})},m=e=>{let{item:l}=e;return(0,a.jsxs)("div",{className:"relative bg-white group hover:bg-gray-50 py-2 transition-colors flex items-start px-3 mx-5 border-2 rounded-lg my-5 ".concat((null==l?void 0:l.status)==="success"?"border-[#00CC5F]":"border-[#F81404]"),children:[(0,a.jsx)("div",{className:"w-8 mr-2 flex items-center justify-center",children:(0,a.jsx)(x.default,{src:(null==l?void 0:l.avatar_url)||(null===h.Z||void 0===h.Z?void 0:h.Z.user),alt:"avatar",width:40,height:40,className:"rounded-[7px] border"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"font-bold pb-1 text-[15px] text-[#1D2939]",children:null==l?void 0:l.username}),(0,a.jsx)("span",{className:"text-xs text-[#98A2B3]",children:new Date(null==l?void 0:l.created_at).toLocaleTimeString([],{hour:"numeric",minute:"2-digit",hour12:!0})})]}),(0,a.jsx)("div",{className:"relative flex items-start justify-between",children:(0,a.jsxs)("div",{className:"gap-2",children:[(0,a.jsx)("small",{className:"text-sm font-bold text-neutral-700",children:null==l?void 0:l.event_name}),(0,a.jsx)("small",{className:"text-sm text-neutral-500 mb-1",children:null==l?void 0:l.message.split("\\n").map((e,l)=>{let t=e.match(/^(.*?):\s*(.*)$/);return(0,a.jsx)("p",{style:{whiteSpace:"pre-line",wordBreak:"break-word",overflowWrap:"break-word"},className:"mb-1",children:t?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("strong",{children:[t[1],":"]})," \xa0\xa0 ",t[2]]}):e},l)})})]})})]})]})},p=t(35486),f=t(78180),g=t(74258),D=t(23625),b=t(37847),j=t(14776),E=()=>{var e;let{fetchMoreData:l,hasMore:t,loading:E}=d(),{state:y,dispatch:N}=(0,s.useContext)(o.R),{messages:w,user:C,isEdit:A,thread:_,notify:k,bookmarks:S,dataId:L}=y,F=(0,c.p)(w),Z=(0,n.useParams)().id,I=(0,n.usePathname)(),[T,z]=(0,s.useState)(!1),[R,P]=(0,s.useState)(null),O=(0,s.useRef)(null),B=(0,s.useRef)(!1),M=async()=>{await (0,r.Gl)("/threads/channels/".concat(Z,"?page=1&limit=1"))};(0,s.useEffect)(()=>{let e=O.current;if(!e)return;let l=()=>{let l=e.scrollTop>=0;l?(M(),B.current=!0):!l&&B.current&&(B.current=!1),z(!l)};return e.addEventListener("scroll",l),l(),()=>{e.removeEventListener("scroll",l)}},[]),(0,s.useEffect)(()=>{T||(null==k?void 0:k.user_id)===(null==C?void 0:C.user_id)||M()},[w]);let U=async e=>{await (0,r.an)("/threads/".concat(null==_?void 0:_.thread_id,"/channels/").concat(Z),{content:e}),N({type:i.a.IS_EDIT,payload:!1})};return((0,s.useEffect)(()=>{let e=localStorage.getItem("data-id");if(!e||!I.includes("/later"))return;let l=document.getElementById("thread-".concat(e));l&&(l.scrollIntoView({behavior:"auto",block:"center"}),l.classList.add("bg-yellow-100"),setTimeout(()=>{l.classList.remove("bg-yellow-100")},1500))},[L,E]),E)?null:(0,a.jsxs)("div",{id:"scrollableDivs",ref:O,style:{height:t||(null==w?void 0:w.length)?"100vh":"",overflowY:"scroll",display:"flex",flexDirection:"column-reverse"},className:"w-full pb-40",children:[T&&(null==y?void 0:y.threadCount)>0&&(0,a.jsxs)(g.C,{onClick:()=>{O.current&&(O.current.scrollTop=0),N({type:i.a.COUNT_CALLBACK,payload:!(null==y?void 0:y.countCallback)})},className:"absolute bottom-40 z-20 mx-auto cursor-pointer -translate-x-[50%] left-1/2 px-3 py-1.5 flex gap-1 bg-primary-500 font-normal text-white text-[0.8125rem] border border-[E6EAEF]",children:[(0,a.jsx)(D.Z,{}),"Latest messages"]}),(0,a.jsx)(u.Z,{dataLength:(null==w?void 0:w.length)||0,next:l,hasMore:t,loader:(null==w?void 0:w.length)!==0&&(0,a.jsx)("h4",{className:"my-5 text-xs text-center",children:"Loading older threads..."}),style:{display:"flex",flexDirection:"column-reverse",overflowY:"visible"},scrollableTarget:"scrollableDivs",inverse:!0,children:null===(e=Object.entries(F))||void 0===e?void 0:e.map(e=>{let[l,t]=e;return(0,a.jsxs)(s.Fragment,{children:[null==t?void 0:t.map((e,l)=>{var n,i;let o=t[l+1],r=!o||o.user_id!==e.user_id,d=null==S?void 0:S.some(l=>l.thread_id===e.thread_id);return(0,a.jsx)(s.Fragment,{children:(null==e?void 0:e.type)==="thread"?(0,a.jsx)(m,{item:e}):(0,a.jsx)(a.Fragment,{children:A&&(null==_?void 0:_.thread_id)===(null==e?void 0:e.thread_id)?(0,a.jsxs)("div",{className:"flex mb-5 mt-2 z-10 px-5 py-3 bg-blue-50 w-full",children:[(0,a.jsx)("div",{className:"size-10 mb-2 mr-3",children:(0,a.jsx)(x.default,{src:(null==e?void 0:e.avatar_url)?null==e?void 0:e.avatar_url:(null==e?void 0:e.user_type)=="user"||(null==e?void 0:e.user_type)===""?null===h.Z||void 0===h.Z?void 0:h.Z.user:null===h.Z||void 0===h.Z?void 0:h.Z.bot,alt:"avatar",width:80,height:40,className:"rounded-[7px] border size-10"})}),(0,a.jsx)(f.Z,{subscription:null==y?void 0:y.channelSubscription,sendMessage:U})]}):(0,a.jsxs)("div",{id:"thread-".concat(e.thread_id),className:"".concat(e.is_pinned?"bg-yellow-50":d?"bg-primary-50":"hover:bg-gray-50"," duration-500 ease-in-out"),children:[(null==e?void 0:e.is_pinned)?(0,a.jsxs)("div",{className:"flex items-center gap-2 bg-yellow-50 pl-10 text-[13px] font-semibold text-blue-100 pt-2",children:[(0,a.jsx)(b.Z,{size:13,className:"text-[#667085] mt-[3px]"}),"Pinned by"," ",(null==C?void 0:C.email)===(null==e?void 0:null===(n=e.pinned_details)||void 0===n?void 0:n.email)?"you":null==e?void 0:null===(i=e.pinned_details)||void 0===i?void 0:i.username]}):d?(0,a.jsxs)("div",{id:"thread-".concat(e.thread_id),className:"flex items-center gap-2 pl-10 text-[13px] font-bold text-blue-100 pt-2",children:[(0,a.jsx)(j.$8n,{fontSize:13,className:"text-[#667085]"}),"Saved for Later"]}):null,(0,a.jsx)(p.Z,{item:e,shouldShowAvatar:r,setPopupId:P,popupId:R})]})})},l)}),(0,a.jsxs)("div",{className:"relative my-2",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-dotted border-[#E6EAEF]"})}),(0,a.jsx)("div",{className:"relative flex justify-center",children:(0,a.jsx)("span",{className:"bg-white px-4 py-1 text-[13px] text-[#101828] border border-[#E6EAEF] rounded-[30px]",children:l})})]})]},l)})}),!t&&(0,a.jsx)(v,{})]})}},25708:function(e,l,t){var a=t(75376),s=t(47411),n=t(32486),i=t(11492),o=t(73003),r=t(97712),d=t(97220),c=t(86418);l.Z=()=>{var e,l;let{state:t,dispatch:u}=(0,n.useContext)(d.R),[x,h]=(0,n.useState)(!1),v=(0,s.useParams)().id,m=async()=>{var e;let l={archived:(null==t?void 0:null===(e=t.channelDetails)||void 0===e?void 0:e.archived)!==!0};h(!0);let a=await (0,c.an)("/channels/".concat(v,"/archive"),l);((null==a?void 0:a.status)===200||(null==a?void 0:a.status)===201)&&u({type:r.a.CHANNEL_CALLBACK,payload:!(null==t?void 0:t.channelCallback)}),h(!1)};return(0,a.jsxs)("div",{className:"h-[170px] bg-neutral-100 border-t flex flex-col items-center justify-center ",children:[(0,a.jsxs)("h1",{className:"mb-2 text-xl font-semibold",children:["# ",null==t?void 0:null===(e=t.channelDetails)||void 0===e?void 0:e.name]}),(0,a.jsxs)("p",{className:"text-base mb-3",children:["You are viewing #",null==t?void 0:null===(l=t.channelDetails)||void 0===l?void 0:l.name," in an archived channel"]}),(0,a.jsx)(i.z,{onClick:m,className:"bg-blue-500 text-white px-5",children:x?(0,a.jsxs)("span",{className:"flex items-center gap-x-2",children:[(0,a.jsx)("span",{className:"animate-pulse",children:"Loading..."})," ",(0,a.jsx)(o.Z,{width:"20",height:"40"})]}):(0,a.jsx)("span",{children:"Unarchive channel"})})]})}},61897:function(e,l,t){t.d(l,{Z:function(){return Z}});var a=t(75376),s=t(18208),n=t(5426),i=t(32486),o=t(89564),r=t(11492),d=t(57292),c=t(28780),u=t(25575),x=t(56792),h=t(39713),v=t(47411),m=t(86418),p=t(61838),f=e=>{let{isOpen:l,agents:t,showModal:s,setCallback:n,callback:o}=e,f=(0,v.useRouter)(),g=(0,v.useParams)().id,[D,b]=(0,i.useState)(""),[j,E]=(0,i.useState)(t.filter(e=>e.is_active).map(e=>e.id)),y=async e=>{let l=localStorage.getItem("orgId")||"",t=!j.includes(null==e?void 0:e.id);E(l=>t?[...l,null==e?void 0:e.id]:l.filter(l=>l!==(null==e?void 0:e.id))),await (0,m.xo)("/organisations/".concat(l,"/agents/").concat(null==e?void 0:e.id,"/channels/").concat(g),{status:null==e||!e.is_active}),n(!o)},N=(0,p.y)(t,D);return l?(0,a.jsxs)("div",{className:"absolute top-full right-0 mt-2.5 w-[339px] bg-white rounded-[7px] shadow-lg border border-[#E6EAEF] z-20",children:[(0,a.jsx)("div",{className:"p-3 border-b border-[#E6EAEF] overflow-hidden",children:(0,a.jsxs)("div",{className:"flex items-center p-[10px] gap-2 border border-[#E6EAEF] rounded-[6px] h-10",children:[(0,a.jsx)(d.Z,{className:"w-5 h-5 text-[#667085]"}),(0,a.jsx)(u.I,{placeholder:"Find an agent",className:"w-full border-none p-0 h-full",value:D,onChange:e=>b(e.target.value)})]})}),0===t.length?(0,a.jsx)("div",{className:"py-5 text-[15px] px-3 text-center",children:"You have no activated agents"}):(0,a.jsxs)("div",{className:"pt-3 px-3 pb-[6px] max-h-[200px] overflow-y-auto",children:[(0,a.jsx)("h3",{className:"text-blue-500 font-semibold text-[13px] mb-2",children:"Activated Agents"}),null==N?void 0:N.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between py-[10px] hover:bg-gray-50 cursor-pointer",onClick:()=>y(e),children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-5 h-5 rounded-sm border border-[#E6EAEF] flex items-center justify-center relative bg-green-100",children:(0,a.jsx)(h.default,{src:(null==e?void 0:e.app_logo)||(null===x.Z||void 0===x.Z?void 0:x.Z.bot),alt:e.app_name,width:20,height:20})}),(0,a.jsx)("p",{className:"text-[13px] font-semibold text-[#344054]",children:e.app_name})]}),(0,a.jsxs)("div",{className:"w-5 h-5 rounded-full border ".concat(e.is_active?"border-[#8686F9] bg-[#8686F9]":"border-[#E6EAEF]"," flex items-center justify-center"),children:[e.is_active&&(0,a.jsx)(c.Z,{className:"w-3 h-3 text-white"})," "]})]},e.id))]}),(0,a.jsxs)("div",{className:"bg-[#F6F7F9] p-3 flex gap-10",children:[(0,a.jsx)(r.z,{onClick:()=>f.push("/client/agents/browse-agents"),variant:"outline",className:"w-full h-9",children:(0,a.jsx)("span",{children:"Browse Agents"})}),(0,a.jsx)(r.z,{onClick:s,variant:"default",className:"w-full bg-[#7141F8] text-white h-9",children:(0,a.jsx)("span",{children:"Add New Agent"})})]})]}):null},g=t(16669),D=t(51888),b=t(44979),j=t(58983),E=t(16006),y=t(17222),N=t(19828),w=t(22397),C=t(73003);function A(){let[e,l]=(0,i.useState)(!1),[t,s]=(0,i.useState)("all"),[n,o]=(0,i.useState)(!1),[d,c]=(0,i.useState)(!1),[u,x]=(0,i.useState)(!1),[h,p]=(0,i.useState)(!1),f=(0,v.useParams)().id;(0,i.useEffect)(()=>{(async()=>{let e=await (0,m.Gl)("/channels/".concat(f,"/notification-preference?device_type=web"));if(200===e.status||201===e.status){let{at_channel:l,at_mentions:t,muted:a}=e.data.data;l&&t?s("all"):t&&!l?s("mentions"):s("channels"),x(!!a),c(!!t)}})()},[e,f]);let D=async()=>{p(!0),await (0,m.xo)("/channels/".concat(f,"/notification-preference"),{muted:u,at_mentions:"all"===t||"mentions"===t,at_channel:"all"===t||"channels"===t,device_type:"web"}),p(!1),l(!1)};return(0,a.jsxs)(E.Vq,{open:e,onOpenChange:l,children:[(0,a.jsx)(E.hg,{asChild:!0,children:(0,a.jsx)("div",{className:"px-4 py-[10px] text-[15px] text-[#101828] hover:bg-[#F1F1FE] flex items-center justify-between cursor-pointer",children:"Notification Settings"})}),(0,a.jsxs)(E.cZ,{className:"sm:max-w-md rounded-md px-6 py-5",children:[(0,a.jsxs)(E.fK,{className:"flex flex-row justify-between items-center",children:[(0,a.jsx)(E.$N,{className:"text-xl font-semibold text-gray-900",children:"Notifications"}),(0,a.jsx)("button",{onClick:()=>l(!1),children:(0,a.jsx)(w.Z,{className:"w-4 h-4 text-gray-500"})})]}),(0,a.jsxs)("div",{className:"mt-4 space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-900 mb-2 block",children:"Send a notification for"}),(0,a.jsxs)(y.E,{value:t,onValueChange:e=>s(e),className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(y.m,{value:"all",id:"all"}),(0,a.jsx)("label",{htmlFor:"all",className:"text-sm text-gray-700",children:"All new messages"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(y.m,{value:"mentions",id:"mentions"}),(0,a.jsx)("label",{htmlFor:"mentions",className:"text-sm text-gray-700",children:"Mentions"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(y.m,{value:"channels",id:"channels"}),(0,a.jsx)("label",{htmlFor:"channels",className:"text-sm text-gray-700",children:"Channels"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-3 pt-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(N.X,{id:"mobile",checked:n,onCheckedChange:e=>o(!!e)}),(0,a.jsx)("label",{htmlFor:"mobile",className:"text-sm text-gray-700",children:"Use different settings for mobile devices"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(N.X,{id:"thread",checked:d,onCheckedChange:e=>c(!!e)}),(0,a.jsx)("label",{htmlFor:"thread",className:"text-sm text-gray-700",children:"Get notified about all thread replies in this channel"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(N.X,{id:"mute",checked:u,onCheckedChange:e=>x(!!e)}),(0,a.jsx)("label",{htmlFor:"mute",className:"text-sm text-gray-700",children:"Mute channel"})]})]})]}),(0,a.jsxs)("p",{className:"text-[12px] text-gray-500 mt-4 leading-tight",children:["Note: You can set notification keywords and change your workspace-wide settings in your"," ",(0,a.jsx)(g.default,{href:"/client/settings/personal/notifications",className:"text-indigo-600 underline",children:"settings"}),"."]}),(0,a.jsxs)("div",{className:"flex justify-end gap-2 mt-6",children:[(0,a.jsx)(r.z,{variant:"ghost",className:"text-sm text-gray-700",onClick:()=>l(!1),children:"Cancel"}),(0,a.jsxs)(r.z,{className:"flex gap-1 items-center bg-blue-500 text-white hover:bg-blue-700 text-sm",disabled:h,onClick:D,children:["Save Changes",h&&(0,a.jsx)(C.Z,{})]})]})]})]})}var _=t(97712),k=t(97220),S=e=>{let{isOpen:l,onClose:t}=e,[s,n]=(0,i.useState)({copy:!1}),o=(0,v.useRouter)(),r=(0,i.useRef)(null),{state:d,dispatch:c}=(0,i.useContext)(k.R),[u,x]=(0,i.useState)(!1),h=(0,v.useParams)(),p=null==h?void 0:h.id;if((0,i.useEffect)(()=>{let e=e=>{e.target.closest('[role="dialog"]')||!r.current||r.current.contains(e.target)||t()};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[t]),!l)return null;let f=e=>{n(l=>({...l,[e]:!l[e]}))},E="px-4 py-[10px] text-[15px] text-[#101828] hover:bg-[#F1F1FE] flex items-center justify-between cursor-pointer",y="h-px bg-[#E6EAEF]",N=async()=>{x(!0);let e=await (0,m.xo)("/channels/".concat(p,"/leave"),{});((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201)&&(c({type:_.a.CALLBACK,payload:!(null==d?void 0:d.callback)}),t()),x(!1)};return(0,a.jsx)("div",{ref:r,className:"absolute top-full right-0 w-[220px] mt-2.5 bg-white rounded-[7px] shadow-lg border border-[#E6EAEF] z-20",onClick:()=>c({type:_.a.ACTIVE_TAB,payload:"about"}),children:(0,a.jsxs)("div",{children:[(0,a.jsx)(b.Z,{className:(0,j.cn)(E,"w-full"),children:"Open channel details"}),(0,a.jsx)("div",{className:y}),(0,a.jsx)(A,{}),(0,a.jsx)("div",{className:E,onClick:()=>o.push("/workflows/new"),children:"Add a workflow"}),(0,a.jsx)("div",{className:y}),(0,a.jsxs)("div",{className:"".concat(E," relative"),onClick:()=>f("copy"),onMouseEnter:()=>n({copy:!0}),onMouseLeave:()=>n({copy:!1}),children:["Copy",(0,a.jsx)(D.Z,{className:"w-4 h-4",color:"#343330"}),s.copy&&(0,a.jsxs)("div",{className:"absolute right-full top-0 w-[200px] bg-white rounded-[7px] shadow-lg border border-[#E6EAEF]",children:[(0,a.jsx)("div",{className:E,children:"Channel Link"}),(0,a.jsx)("div",{className:E,children:"Channel ID"})]})]}),(0,a.jsx)("div",{className:E,onClick:()=>o.push("/channel/search"),children:"Search in channel"}),(0,a.jsx)(g.default,{href:"/channel/new-window",target:"_blank",className:E,children:"Open in new window"}),(0,a.jsx)("div",{className:y}),(0,a.jsxs)("div",{className:"".concat(E," text-[#B00E03]"),onClick:N,children:["Leave channel"," ",u&&(0,a.jsx)(C.Z,{color:"red",height:"15px",width:"15px"})]})]})})},L=t(13352),F=e=>{let{isOpen:l,workflows:t}=e,s=(0,v.useRouter)(),n=(0,v.useParams)().id,[o,p]=(0,i.useState)(""),[f,g]=(0,i.useState)(null==t?void 0:t.filter(e=>e.is_active).map(e=>e.id)),D=async e=>{let l=!f.includes(null==e?void 0:e.id);g(t=>l?[...t,null==e?void 0:e.id]:t.filter(l=>l!==(null==e?void 0:e.id)));let t={workflow_id:null==e?void 0:e.id,channel_id:n};await (0,m.xo)("/channel-workflows",t),L.Z.success("Workflow activated for this channel")},b=null==t?void 0:t.filter(e=>e.name.toLowerCase().includes(o.toLowerCase()));return l?(0,a.jsxs)("div",{className:"absolute z-50 top-full right-0 mt-2.5 w-[339px] bg-white rounded-[7px] shadow-lg border border-[#E6EAEF]",children:[(0,a.jsx)("div",{className:"p-3 border-b border-[#E6EAEF] overflow-hidden",children:(0,a.jsxs)("div",{className:"flex items-center p-[10px] gap-2 border border-[#E6EAEF] rounded-[6px] h-10",children:[(0,a.jsx)(d.Z,{className:"w-5 h-5 text-[#667085]"}),(0,a.jsx)(u.I,{placeholder:"Find a workflow",className:"w-full border-none p-0 h-full",value:o,onChange:e=>p(e.target.value)})]})}),0===t.length?(0,a.jsx)("div",{className:"py-5 text-[15px] px-3 text-center",children:"You have no workflows"}):(0,a.jsxs)("div",{className:"pt-3 px-3 pb-[6px] max-h-[200px] overflow-y-auto",children:[(0,a.jsx)("h3",{className:"text-blue-500 font-semibold text-[13px] mb-2",children:"Workflows"}),null==b?void 0:b.map(e=>{let l=f.includes(e.id);return(0,a.jsxs)("div",{className:"flex items-center justify-between py-[10px] hover:bg-gray-50 cursor-pointer",onClick:()=>D(e),children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-5 h-5 rounded-sm border border-[#E6EAEF] flex items-center justify-center relative bg-green-100",children:(0,a.jsx)(h.default,{src:null===x.Z||void 0===x.Z?void 0:x.Z.blueBot,alt:e.name,width:20,height:20})}),(0,a.jsx)("p",{className:"text-[13px] font-semibold text-[#344054]",children:e.name})]}),(0,a.jsx)("div",{className:"w-5 h-5 rounded-full border ".concat(l?"border-[#8686F9] bg-[#8686F9]":"border-[#E6EAEF]"," flex items-center justify-center"),children:l&&(0,a.jsx)(c.Z,{className:"w-3 h-3 text-white"})})]},e.id)})]}),(0,a.jsxs)("div",{className:"bg-[#F6F7F9] p-3 flex items-center justify-between gap-3",children:[(0,a.jsx)(r.z,{onClick:()=>s.push("/client/workflows"),variant:"outline",className:"flex-1 h-9",children:(0,a.jsx)("span",{children:"Browse Workflows"})}),(0,a.jsx)(r.z,{onClick:()=>s.push("/client/workflows/new"),variant:"default",className:"flex-1 bg-[#7141F8] text-white h-9",children:(0,a.jsx)("span",{children:"Add New"})})]})]}):null},Z=()=>{var e,l,t,d;let[c,x]=(0,i.useState)(!1),[h,p]=(0,i.useState)(!1),[g,D]=(0,i.useState)(!1),j=(0,i.useRef)(null),y=(0,i.useRef)(null),N=(0,i.useRef)(null),{state:w,dispatch:A}=(0,i.useContext)(k.R),{channelDetails:Z,agentModal:I}=w,[T,z]=(0,i.useState)([]),[R,P]=(0,i.useState)(""),[O,B]=(0,i.useState)(!1),[M,U]=(0,i.useState)(!1),G=(0,v.useParams)().id;(0,i.useEffect)(()=>{let e=e=>{j.current&&!j.current.contains(e.target)&&x(!1)},l=e=>{y.current&&!y.current.contains(e.target)&&p(!1)};return document.addEventListener("mousedown",e),document.addEventListener("mousedown",l),()=>{document.removeEventListener("mousedown",e),document.removeEventListener("mousedown",l)}},[]),(0,i.useEffect)(()=>{(async()=>{let e=await (0,m.Gl)("/organisations/".concat(null==w?void 0:w.orgId,"/channels/").concat(G,"/agents"));if((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201){var l,t;z(null==e?void 0:null===(l=e.data)||void 0===l?void 0:l.data),A({type:_.a.CHANNEL_AGENTS,payload:null==e?void 0:null===(t=e.data)||void 0===t?void 0:t.data})}})()},[M,null==w?void 0:w.orgId,G]),(0,i.useEffect)(()=>{let e=localStorage.getItem("orgId");(async()=>{let l=await (0,m.Gl)("/channel-workflows/organisations/".concat(e,"/channels/").concat(G));if((null==l?void 0:l.status)===200||(null==l?void 0:l.status)===201){var t;A({type:_.a.CHANNEL_WORKFLOWS,payload:null==l?void 0:null===(t=l.data)||void 0===t?void 0:t.data})}})()},[M,null==w?void 0:w.orgId,G]);let H=async e=>{e.preventDefault();let l=localStorage.getItem("orgId")||"";B(!0);let t=await (0,m.xo)("/organisations/".concat(l,"/agents"),{json_url:R});if((null==t?void 0:t.status)===200||(null==t?void 0:t.status)===201){var a;U(!M),A({type:_.a.AGENT_CALLBACK,payload:!(null==w?void 0:w.agentCallback)}),L.Z.success(null==t?void 0:null===(a=t.data)||void 0===a?void 0:a.message),setTimeout(()=>{A({type:_.a.AGENT_MODAL,payload:!1})},1e3)}B(!1)};return(0,a.jsxs)("nav",{className:"flex items-center justify-between px-3 py-3 md:p-5 border-b border-[#E6EAEF]",children:[(0,a.jsx)(b.Z,{children:(0,a.jsxs)("h2",{className:"text-base lg:text-lg font-bold hover:text-blue-300",children:[(null==Z?void 0:Z.name)?"#":""," ",null==Z?void 0:Z.name]})}),!(null==w?void 0:w.channelloading)&&(null==w?void 0:null===(e=w.channelDetails)||void 0===e?void 0:e.access)===!0&&!(null==w?void 0:null===(l=w.channelDetails)||void 0===l?void 0:l.archived)&&(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("div",{className:"relative hidden sm:flex",ref:y,children:[(0,a.jsxs)(r.z,{variant:"outline",className:"border-blue-50 h-9 hover:bg-primary-50",onClick:()=>p(!h),children:[(0,a.jsx)(s.Z,{className:"w-5 h-5",color:"#8686F9"}),(0,a.jsx)("span",{className:"ml-1 text-[13px] font-semibold text-blue-200",children:"Add a workflow"})]}),(0,a.jsx)(F,{isOpen:h,workflows:null==w?void 0:w.channelWorkflows})]}),(0,a.jsxs)("div",{className:"relative hidden sm:flex",ref:j,children:[(0,a.jsxs)(r.z,{variant:"outline",className:"border-blue-50 h-9 hover:bg-primary-50",onClick:()=>x(!c),children:[(0,a.jsx)(s.Z,{className:"w-5 h-5",color:"#8686F9"}),(0,a.jsx)("span",{className:"ml-1 text-[13px] font-semibold text-blue-200",children:"Add an Agent"})]}),(0,a.jsx)(f,{isOpen:c,agents:T,showModal:()=>{A({type:_.a.AGENT_MODAL,payload:!0}),x(!1)},setCallback:U,callback:M})]}),(0,a.jsx)(E.Vq,{open:I,onOpenChange:()=>A({type:_.a.AGENT_MODAL,payload:I}),children:(0,a.jsxs)(E.cZ,{className:"w-full max-w-md",children:[(0,a.jsx)(E.fK,{className:"mb-5",children:(0,a.jsx)(E.$N,{className:"text-blue-500",children:"Provide your Agent Json url"})}),(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Json url"}),(0,a.jsx)(u.I,{value:R,onChange:e=>P(e.target.value),placeholder:"Enter json url"})]}),(0,a.jsxs)(E.cN,{className:"mt-4 flex justify-end gap-2",children:[(0,a.jsx)(r.z,{variant:"outline",onClick:()=>A({type:_.a.AGENT_MODAL,payload:!1}),children:"Cancel"}),(0,a.jsxs)(r.z,{onClick:H,disabled:!R,className:"bg-blue-500 gap-1 text-white px-8",children:["Save",O&&(0,a.jsx)(C.Z,{})]})]})]})}),(0,a.jsx)("div",{className:"w-px h-5 bg-[#E6EAEF] hidden lg:block"}),(0,a.jsx)(b.Z,{children:(0,a.jsx)("div",{onClick:()=>A({type:_.a.ACTIVE_TAB,payload:"people"}),className:"hidden lg:flex rounded-[5px] border border-[#E6EAEF] p-2 h-9 cursor-pointer hover:bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-center gap-1.5",children:[null==Z?void 0:null===(t=Z.users)||void 0===t?void 0:t.slice(0,3).map((e,l)=>{var t;return(0,a.jsx)(o.qE,{className:"rounded-[5px] w-5 h-5 border border-[#E6EAEF] object-cover ".concat(l>0?"-ml-2.5":""),children:(0,a.jsx)(o.F$,{src:(null==e?void 0:null===(t=e.profile)||void 0===t?void 0:t.avatar_url)||"/images/user.png",className:"object-cover"})},e.id)}),(null==Z?void 0:null===(d=Z.users)||void 0===d?void 0:d.length)>3&&(0,a.jsxs)("span",{className:"text-[13px] font-semibold text-[#344054]",children:["+",Z.users.length-3]})]})})}),(0,a.jsxs)("div",{className:"relative",ref:N,children:[(0,a.jsx)(r.z,{variant:"outline",className:"p-2 border-[#E6EAEF] h-9 ".concat(g?"bg-[#F6F7F9]":""),onClick:()=>D(!g),children:(0,a.jsx)(n.Z,{className:"w-5 h-5",color:"#344054"})}),(0,a.jsx)(S,{isOpen:g,onClose:()=>D(!1)})]})]})]})}},36221:function(e,l,t){t.d(l,{r:function(){return i}});var a=t(75376),s=t(32486),n=t(46962);function i(e){let{textToCopy:l,tooltipText:t="Copied",children:i}=e,[o,r]=(0,s.useState)(!1),d=async()=>{try{await navigator.clipboard.writeText(l),r(!0),setTimeout(()=>r(!1),2e3)}catch(e){console.error("Failed to copy text:",e)}};return(0,a.jsx)(n.pn,{children:(0,a.jsxs)(n.u,{open:o,children:[(0,a.jsx)(n.aJ,{asChild:!0,children:(0,a.jsx)("div",{onClick:d,className:"cursor-pointer inline-flex",children:i})}),(0,a.jsx)(n._v,{side:"top",className:"bg-black text-white",children:t})]})})}},67903:function(e,l,t){var a=t(75376),s=t(47411),n=t(32486),i=t(11492),o=t(73003),r=t(97712),d=t(97220),c=t(86418);l.Z=()=>{var e;let{state:l,dispatch:t}=(0,n.useContext)(d.R),[u,x]=(0,n.useState)(!1),h=(0,s.useParams)().id,v=async()=>{x(!0);let e=JSON.parse(localStorage.getItem("user")||"{}"),a=await (0,c.xo)("/channels/".concat(h,"/join"),{username:null==e?void 0:e.username});((null==a?void 0:a.status)===200||(null==a?void 0:a.status)===200)&&t({type:r.a.CHANNEL_CALLBACK,payload:!(null==l?void 0:l.channelCallback)}),x(!1)};return(0,a.jsxs)("div",{className:"h-[170px] bg-neutral-100 border-t flex flex-col items-center justify-center ",children:[(0,a.jsxs)("h1",{className:"mb-2 text-xl font-semibold",children:["# ",null==l?void 0:null===(e=l.channelDetails)||void 0===e?void 0:e.name]}),(0,a.jsx)("p",{className:"text-base mb-3",children:"You are not a member of this channel"}),(0,a.jsx)(i.z,{onClick:v,className:"bg-blue-500 text-white px-5",children:u?(0,a.jsxs)("span",{className:"flex items-center gap-x-2",children:[(0,a.jsx)("span",{className:"animate-pulse",children:"Joining..."})," ",(0,a.jsx)(o.Z,{width:"20",height:"40"})]}):(0,a.jsx)("span",{children:"Join channel"})})]})}},78180:function(e,l,t){var a=t(75376),s=t(32486),n=t(72742),i=t(22641),o=t(49107),r=t(28904),d=t(82310),c=t(72593),u=t(59632),x=t(75148),h=t(40708),v=t(97220),m=t(65470),p=t(16006),f=t(25575),g=t(11492),D=t(99402),b=t(89863),j=t(97712),E=t(74178);l.Z=e=>{let{subscription:l,sendMessage:t}=e,{editor:y}=(0,h.Z)(l),{state:N,dispatch:w}=(0,s.useContext)(v.R),[C,A]=(0,s.useState)(!1),[_,k]=(0,s.useState)(""),[S,L]=(0,s.useState)(""),[F,Z]=(0,s.useState)(!1),[I,T]=(0,s.useState)(!0),z=async e=>{e.preventDefault();let a=null==y?void 0:y.getHTML();(null==a?void 0:a.replace(/<[^>]+>/g,"").trim())&&(l?t(a):console.log("No connection detected"))};return(0,s.useEffect)(()=>{if(y){var e;null==y||y.commands.setContent(null==N?void 0:null===(e=N.thread)||void 0===e?void 0:e.message),y.commands.focus()}},[y]),(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"bg-white border rounded border-[#E6EAEF] w-full ".concat((null==N?void 0:N.reply)?"right-[520px]":"right-0"),children:[I&&(0,a.jsxs)("div",{className:"border-b border-[#E6EAEF] flex items-center gap-2 mb-2 bg-[#F9FAFB] pl-3 pr-4 py-[5px]",children:[(0,a.jsx)("button",{onClick:()=>null==y?void 0:y.chain().focus().toggleBold().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==y?void 0:y.isActive("bold"))?"bg-gray-200 font-semibold text-black":""),children:(0,a.jsx)(n.Z,{size:18,color:(null==y?void 0:y.isActive("bold"))?"#444444":"#CACACA"})}),(0,a.jsx)("button",{onClick:()=>null==y?void 0:y.chain().focus().toggleItalic().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==y?void 0:y.isActive("italic"))?"bg-gray-200 font-semibold text-black":""),children:(0,a.jsx)(i.Z,{size:18,color:(null==y?void 0:y.isActive("italic"))?"#444444":"#CACACA"})}),(0,a.jsx)("button",{onClick:()=>null==y?void 0:y.chain().focus().toggleStrike().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==y?void 0:y.isActive("strike"))?"bg-gray-200 font-semibold text-black":""),children:(0,a.jsx)(o.Z,{size:18,color:(null==y?void 0:y.isActive("strike"))?"#444444":"#CACACA"})}),(0,a.jsx)("div",{className:"w-px h-5 bg-[#E6EAEF]"}),(0,a.jsxs)(p.Vq,{open:C,onOpenChange:A,children:[(0,a.jsx)(p.hg,{asChild:!0,children:(0,a.jsx)("button",{onClick:()=>A(!0),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==y?void 0:y.isActive("link"))?"bg-gray-200 font-semibold text-black":""),children:(0,a.jsx)(r.Z,{size:18,color:(null==y?void 0:y.isActive("link"))?"#444444":"#CACACA"})})}),(0,a.jsxs)(p.cZ,{className:"w-full max-w-md",children:[(0,a.jsx)(p.fK,{children:(0,a.jsx)(p.$N,{className:"font-semibold",children:"Add link"})}),(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Text"}),(0,a.jsx)(f.I,{value:_,onChange:e=>k(e.target.value),placeholder:"Enter link text"}),(0,a.jsx)("label",{className:"text-sm font-medium mt-2",children:"Link"}),(0,a.jsx)(f.I,{value:S,onChange:e=>L(e.target.value),placeholder:"Enter URL",type:"url"})]}),(0,a.jsxs)(p.cN,{className:"mt-4 flex justify-end gap-2",children:[(0,a.jsx)(g.z,{variant:"outline",onClick:()=>A(!1),children:"Cancel"}),(0,a.jsx)(g.z,{onClick:()=>{_&&S&&(null==y||y.chain().focus().insertContent('<a href="'.concat(S,'" target="_blank" rel="noopener noreferrer">').concat(_,"</a>")).run(),A(!1),k(""),L(""))},disabled:!_||!S,className:"bg-blue-500 text-white px-10",children:"Save"})]})]})]}),(0,a.jsx)("button",{onClick:()=>null==y?void 0:y.chain().focus().toggleOrderedList().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==y?void 0:y.isActive("orderedList"))?"bg-gray-200 font-semibold text-black":""),children:(0,a.jsx)(d.Z,{size:18,color:(null==y?void 0:y.isActive("orderedList"))?"#444444":"#CACACA"})}),(0,a.jsx)("button",{onClick:()=>null==y?void 0:y.chain().focus().toggleBulletList().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==y?void 0:y.isActive("bulletList"))?"bg-gray-200 font-semibold text-black":""),children:(0,a.jsx)(c.Z,{size:18,color:(null==y?void 0:y.isActive("bulletList"))?"#444444":"#CACACA"})}),(0,a.jsx)("div",{className:"w-px h-5 bg-[#E6EAEF]"}),(0,a.jsx)("button",{onClick:()=>null==y?void 0:y.chain().focus().toggleCode().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==y?void 0:y.isActive("code"))?"bg-gray-200 font-semibold text-black":""),children:(0,a.jsx)(u.Z,{size:18,color:(null==y?void 0:y.isActive("code"))?"#444444":"#CACACA"})})]}),(0,a.jsx)("div",{className:"flex-1 relative px-3",children:(0,a.jsx)(m.kg,{editor:y,className:"py-2 rounded-md flex flex-row overflow-auto",onKeyDown:e=>{"Enter"===e.key&&(e.shiftKey?(e.preventDefault(),null==y||y.commands.enter()):(e.preventDefault(),z(e)))}})}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 py-2 pl-3 pr-4",children:[(0,a.jsx)("button",{title:"show formatting",onClick:()=>T(e=>!e),className:"p-1.5 hover:bg-gray-100 rounded text-[#606060] underline",children:"Aa"}),(0,a.jsx)("div",{className:"relative",children:(0,a.jsxs)(E.J2,{open:F,onOpenChange:Z,children:[(0,a.jsx)(E.xo,{asChild:!0,children:(0,a.jsx)("button",{className:"p-1.5 hover:bg-gray-100 rounded",children:(0,a.jsx)(x.Z,{size:18,color:"#606060"})})}),(0,a.jsx)(E.yk,{className:"p-0 w-full max-w-xs",children:(0,a.jsx)(D.Z,{data:b,onEmojiSelect:e=>{y?(y.chain().focus().insertContent(null==e?void 0:e.native).run(),Z(!1)):console.log("editor not available")}})})]})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 py-2 pl-3 pr-4",children:[(0,a.jsx)("button",{className:"bg-gray-100 rounded py-2 px-4 text-xs",onClick:()=>w({type:j.a.IS_EDIT,payload:!1}),children:"Cancel"}),(0,a.jsx)("button",{onClick:z,className:"bg-blue-500 rounded py-2 px-4 text-white text-xs",children:"Save"})]})]})]})})}},71240:function(e,l,t){t.d(l,{Z:function(){return U}});var a=t(75376),s=t(32486),n=t(72742),i=t(22641),o=t(49107),r=t(28904),d=t(82310),c=t(72593),u=t(59632),x=t(76892),h=t(22397),v=t(41102),m=t(75148),p=t(35602),f=t(9274),g=t(60601),D=t(40497),b=t(79624),j=t(39713),E=t(40708),y=t(97220),N=t(86418),w=t(47411),C=t(92287),A=t(65470),_=t(16006),k=t(25575),S=t(11492),L=t(99402),F=t(89863),Z=t(73003),I=t(97712),T=t(74178),z=t(409),R=t.n(z),P=t(60971),O=t.n(P),B=e=>{let{state:l}=(0,s.useContext)(y.R);return{handleTyping:O()(t=>{if(e){var a,s;null==e||e.publish({user:{id:null==l?void 0:null===(a=l.user)||void 0===a?void 0:a.id,username:null==l?void 0:null===(s=l.user)||void 0===s?void 0:s.username},typing:t,type:"typing"})}},50)}};let M={":-)":"\uD83D\uDE0A",":)":"\uD83D\uDE0A","(:":"\uD83D\uDE0A","=)":"\uD83D\uDE0A",":-D":"\uD83D\uDE00",":D":"\uD83D\uDE00",xD:"\uD83D\uDE06",XD:"\uD83D\uDE06",":-(":"\uD83D\uDE1E",":(":"\uD83D\uDE1E","):":"\uD83D\uDE1E","=(":"\uD83D\uDE1E",";-)":"\uD83D\uDE09",";)":"\uD83D\uDE09",";]":"\uD83D\uDE09","*-)":"\uD83D\uDE09",":-P":"\uD83D\uDE1B",":P":"\uD83D\uDE1B",":-p":"\uD83D\uDE1B",":p":"\uD83D\uDE1B",":-o":"\uD83D\uDE2E",":o":"\uD83D\uDE2E",":-O":"\uD83D\uDE2E",":O":"\uD83D\uDE2E",":-*":"\uD83D\uDE18",":*":"\uD83D\uDE18",":-x":"\uD83D\uDE18",":x":"\uD83D\uDE18",":'(":"\uD83D\uDE22",":'‑(":"\uD83D\uDE22",":'‑)":"\uD83D\uDE02",":'D":"\uD83D\uDE02",":-/":"\uD83D\uDE15",":/":"\uD83D\uDE15",":-\\":"\uD83D\uDE15",":\\":"\uD83D\uDE15",":-|":"\uD83D\uDE10",":|":"\uD83D\uDE10",":-]":"\uD83D\uDE10",":]":"\uD83D\uDE10","<3":"❤️","</3":"\uD83D\uDC94","<\\3":"\uD83D\uDC94","♥":"❤️",":3":"\uD83D\uDE3A",":-3":"\uD83D\uDE3A",":>":"\uD83D\uDE3A",":^)":"\uD83D\uDE04","O:)":"\uD83D\uDE07","O:-)":"\uD83D\uDE07","0:)":"\uD83D\uDE07","0:-)":"\uD83D\uDE07",">:(":"\uD83D\uDE20",">:-(":"\uD83D\uDE20","D:<":"\uD83D\uDE20",D8:"\uD83D\uDE31","D-:<":"\uD83D\uDE20","D':":"\uD83D\uDE31",">:O":"\uD83D\uDE21",">:o":"\uD83D\uDE21",":-$":"\uD83E\uDD10",":$":"\uD83E\uDD10",":-#":"\uD83E\uDD10",":#":"\uD83E\uDD10",":-X":"\uD83E\uDD10",":X":"\uD83E\uDD10",":-!":"\uD83E\uDD10","B-)":"\uD83D\uDE0E","B)":"\uD83D\uDE0E","8-)":"\uD83D\uDE0E","8)":"\uD83D\uDE0E",":-B":"\uD83D\uDE0E",":-b":"\uD83D\uDE0E","|-)":"\uD83D\uDE34","|)":"\uD83D\uDE34","-_-":"\uD83D\uDE11","=_=":"\uD83D\uDE11",">.<":"\uD83D\uDE23",">_>":"\uD83D\uDE12","<_<":"\uD83D\uDE12","-.-":"\uD83D\uDE12",u_u:"\uD83D\uDE14","^_^":"\uD83D\uDE0A","^.^":"\uD83D\uDE0A","^-^":"\uD83D\uDE0A",":‑]":"\uD83D\uDE42","=]":"\uD83D\uDE42",":}":"\uD83D\uDE42",":-}":"\uD83D\uDE42",":')":"\uD83D\uDE02",":‑')":"\uD83D\uDE02",":‑))":"\uD83D\uDE02",":’-D":"\uD83D\uDE02",":L":"\uD83D\uDE15",":S":"\uD83D\uDE16",":Z":"\uD83D\uDE36",":T":"\uD83D\uDE24","D:":"\uD83D\uDE27",DX:"\uD83D\uDE27",QQ:"\uD83D\uDE2D",T_T:"\uD83D\uDE2D","T-T":"\uD83D\uDE2D",TT:"\uD83D\uDE2D",":-@":"\uD83D\uDE21",":@":"\uD83D\uDE21",">:D":"\uD83D\uDE08",">;]":"\uD83D\uDE08",">:)":"\uD83D\uDE08",">:3":"\uD83D\uDE08","<(^_^)>":"\uD83E\uDD17","<(o_o<)":"\uD83E\uDD17","(>o_o)>":"\uD83E\uDD17","(>'-')>":"\uD83E\uDD17","<('-'<)":"\uD83E\uDD17","ヽ(•‿•)ノ":"\uD83D\uDE0A","(╯\xb0□\xb0）╯︵ ┻━┻":"\uD83D\uDE21","┬─┬ ノ( ゜-゜ノ)":"\uD83D\uDE0C","(☞ﾟヮﾟ)☞":"\uD83D\uDC49","☜(ﾟヮﾟ☜)":"\uD83D\uDC48","(づ｡◕‿‿◕｡)づ":"\uD83E\uDD17","(~_^)":"\uD83D\uDE09","(^_−)☆":"\uD83D\uDE09","(^_^)":"\uD83D\uDE0A","(^.^)":"\uD83D\uDE0A","(-_-)":"\uD83D\uDE34","(\xac_\xac)":"\uD83D\uDE12","(ಠ_ಠ)":"\uD83D\uDE11","(ʘ‿ʘ)":"\uD83D\uDE33","(ಥ﹏ಥ)":"\uD83D\uDE2D","(\xac‿\xac)":"\uD83D\uDE0F","(ง •̀_•́)ง":"\uD83D\uDCAA"};var U=e=>{let{subscription:l,sendMessage:t,show:z=!0}=e,{state:P,dispatch:O}=(0,s.useContext)(y.R),U=(0,w.useParams)().id,G=(0,C.AE)(),[H,K]=(0,s.useState)(!1),[$,X]=(0,s.useState)(""),[Y,V]=(0,s.useState)(""),[W,J]=(0,s.useState)(!1),[q,Q]=(0,s.useState)(!0),ee=(0,s.useRef)(null),[el,et]=(0,s.useState)([]),[ea,es]=(0,s.useState)([]),[en,ei]=(0,s.useState)([]),{handleTyping:eo}=B(l);(0,s.useEffect)(()=>()=>{el.forEach(e=>URL.revokeObjectURL(e.preview))},[el]);let{editor:er,isEmpty:ed}=(0,E.Z)(e=>{let l={id:Date.now()+Math.random(),file:e,type:"image",preview:URL.createObjectURL(e)};et(e=>[...e,l]),ei(e=>[...e,l.id]);let t=new FormData;t.append("files",e),(0,N.x9)("/files/upload-files",t).then(e=>{var l;let t=null==e?void 0:null===(l=e.data)||void 0===l?void 0:l.data[0];t&&er&&es(e=>[...e,t])}).catch(e=>{console.error("Image paste upload failed",e)}).finally(()=>{ei(e=>e.filter(e=>e!==l.id))})}),ec=async e=>{let l=e.target.files;if(!l)return;let t=Array.from(l).map(e=>{let l=e.type.split("/")[0];return{id:Date.now()+Math.random(),file:e,type:l,preview:"image"===l||"video"===l?URL.createObjectURL(e):null}});for(let e of(et(e=>[...e,...t]),t)){ei(l=>[...l,e.id]);let l=new FormData;l.append("files",e.file);try{var a;let e=await (0,N.x9)("/files/upload-files",l);(null==e?void 0:null===(a=e.data)||void 0===a?void 0:a.data)&&es(l=>[...l,...e.data.data])}catch(e){console.error("Upload failed",e)}finally{ei(l=>l.filter(l=>l!==e.id))}}},eu=e=>{et(l=>l.filter(t=>t!==l[e])),es(l=>l.filter((l,t)=>t!==e))},ex=e=>{let l=e;for(let e in M){let t=RegExp(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"g");l=l.replace(t,M[e])}return l},eh=async e=>{if(e.preventDefault(),!er)return;let a=er.getHTML(),s=er.getText().trim(),n=s.length>0,i=ea.length>0;if(!n&&!i)return;let o=ex(s),r=R().shortnameToUnicode(o);a=a.replace(s,r),l?(er.commands.clearContent(),et([]),es([]),t(U,G,a,ea),O({type:I.a.CLEAR_MENTIONS}),eo(!1)):console.log("No connection detected")};return(0,s.useEffect)(()=>{er&&er.commands.focus()},[er]),(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{onClick:()=>er&&er.commands.focus(),className:"bg-white border rounded-xl mx-3 md:mx-5 border-[#E6EAEF] overflow-hidden ".concat((null==P?void 0:P.reply)?"right-[520px]":"right-0"," ").concat((null==er?void 0:er.isFocused)?"border-primary-400":"border-gray-200"),children:[q&&(0,a.jsxs)("div",{className:"border-b border-[#E6EAEF] flex items-center gap-2 bg-[#F9FAFB] pl-3 pr-4 py-[5px]",children:[(0,a.jsx)("button",{onClick:()=>null==er?void 0:er.chain().focus().toggleBold().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==er?void 0:er.isActive("bold"))?"bg-gray-200 font-semibold text-black":""),children:(0,a.jsx)(n.Z,{size:18,color:(null==er?void 0:er.isActive("bold"))?"#444444":"#CACACA"})}),(0,a.jsx)("button",{onClick:()=>null==er?void 0:er.chain().focus().toggleItalic().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==er?void 0:er.isActive("italic"))?"bg-gray-200 font-semibold text-black":""),children:(0,a.jsx)(i.Z,{size:18,color:(null==er?void 0:er.isActive("italic"))?"#444444":"#CACACA"})}),(0,a.jsx)("button",{onClick:()=>null==er?void 0:er.chain().focus().toggleStrike().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==er?void 0:er.isActive("strike"))?"bg-gray-200 font-semibold text-black":""),children:(0,a.jsx)(o.Z,{size:18,color:(null==er?void 0:er.isActive("strike"))?"#444444":"#CACACA"})}),(0,a.jsx)("div",{className:"w-px h-5 bg-[#E6EAEF]"}),(0,a.jsxs)(_.Vq,{open:H,onOpenChange:K,children:[(0,a.jsx)(_.hg,{asChild:!0,children:(0,a.jsx)("button",{onClick:()=>K(!0),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==er?void 0:er.isActive("link"))?"bg-gray-200 font-semibold text-black":""),children:(0,a.jsx)(r.Z,{size:18,color:(null==er?void 0:er.isActive("link"))?"#444444":"#CACACA"})})}),(0,a.jsxs)(_.cZ,{className:"w-full max-w-md",children:[(0,a.jsx)(_.fK,{children:(0,a.jsx)(_.$N,{className:"font-semibold",children:"Add link"})}),(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Text"}),(0,a.jsx)(k.I,{value:$,onChange:e=>X(e.target.value),placeholder:"Enter link text"}),(0,a.jsx)("label",{className:"text-sm font-medium mt-2",children:"Link"}),(0,a.jsx)(k.I,{value:Y,onChange:e=>V(e.target.value),placeholder:"Enter URL",type:"url"})]}),(0,a.jsxs)(_.cN,{className:"mt-4 flex justify-end gap-2",children:[(0,a.jsx)(S.z,{variant:"outline",onClick:()=>K(!1),children:"Cancel"}),(0,a.jsx)(S.z,{onClick:()=>{$&&Y&&(null==er||er.chain().focus().insertContent('<a href="'.concat(Y,'" target="_blank" rel="noopener noreferrer">').concat($,"</a>")).run(),K(!1),X(""),V(""))},disabled:!$||!Y,className:"bg-blue-500 text-white px-10",children:"Save"})]})]})]}),(0,a.jsx)("button",{onClick:()=>null==er?void 0:er.chain().focus().toggleOrderedList().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==er?void 0:er.isActive("orderedList"))?"bg-gray-200 font-semibold text-black":""),children:(0,a.jsx)(d.Z,{size:18,color:(null==er?void 0:er.isActive("orderedList"))?"#444444":"#CACACA"})}),(0,a.jsx)("button",{onClick:()=>null==er?void 0:er.chain().focus().toggleBulletList().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==er?void 0:er.isActive("bulletList"))?"bg-gray-200 font-semibold text-black":""),children:(0,a.jsx)(c.Z,{size:18,color:(null==er?void 0:er.isActive("bulletList"))?"#444444":"#CACACA"})}),(0,a.jsx)("div",{className:"w-px h-5 bg-[#E6EAEF]"}),(0,a.jsx)("button",{onClick:()=>null==er?void 0:er.chain().focus().toggleCode().run(),className:"p-1.5 hover:bg-gray-100 rounded ".concat((null==er?void 0:er.isActive("code"))?"bg-gray-200 font-semibold text-black":""),children:(0,a.jsx)(u.Z,{size:18,color:(null==er?void 0:er.isActive("code"))?"#444444":"#CACACA"})})]}),(0,a.jsxs)("div",{className:"md:flex-1 relative px-3",children:[(0,a.jsx)(A.kg,{editor:er,className:"py-2 rounded-md flex flex-row overflow-auto",onKeyDown:e=>{"Enter"===e.key&&(e.shiftKey?(e.preventDefault(),null==er||er.commands.enter()):(e.preventDefault(),eh(e)))}}),(0,a.jsx)("div",{className:"flex gap-3 ".concat((null==el?void 0:el.length)>0?"mt-3":""),children:null==el?void 0:el.map((e,l)=>(0,a.jsxs)("div",{className:"relative w-[70px] h-[70px]",children:["image"===e.type?(0,a.jsx)(j.default,{src:e.preview,alt:"Uploaded ".concat(l),width:70,height:70,className:"w-[70px] h-[70px] rounded-md object-cover border border-primary-400 cursor-pointer"}):"application"===e.type?(0,a.jsx)("div",{className:"w-[70px] h-[70px] flex items-center justify-center border border-primary-500 rounded-md bg-gray-100",children:(0,a.jsxs)("a",{href:URL.createObjectURL(e.file),target:"_blank",rel:"noopener noreferrer",className:"flex flex-col items-center",children:[(0,a.jsx)(x.Z,{size:24,color:"#606060"}),(0,a.jsx)("span",{className:"text-xs text-blue-500 mt-1",children:"PDF"})]})}):null,(0,a.jsx)("button",{onClick:()=>eu(l),className:"absolute -top-1 -right-2 p-1 bg-gray-500 text-white rounded-full w-5 h-5 text-xs flex items-center justify-center",children:(0,a.jsx)(h.Z,{size:14})}),en.includes(null==e?void 0:e.id)&&(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-50",children:(0,a.jsx)(Z.Z,{})})]},l))})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 py-2 pl-3 pr-4",children:[(0,a.jsxs)("button",{onClick:()=>{var e;return null===(e=ee.current)||void 0===e?void 0:e.click()},className:"p-1.5 hover:bg-gray-100 rounded-full bg-[#F2F4F7]",children:[(0,a.jsx)("input",{type:"file",ref:ee,style:{display:"none"},onChange:ec,accept:"image/*, application/pdf",multiple:!0}),(0,a.jsx)(v.Z,{size:18,color:"#606060"})]}),(0,a.jsx)("button",{title:"show formatting",onClick:()=>Q(e=>!e),className:"p-1.5 hover:bg-gray-100 rounded text-[#606060] underline",children:"Aa"}),(0,a.jsx)("div",{className:"relative",children:(0,a.jsxs)(T.J2,{open:W,onOpenChange:J,children:[(0,a.jsx)(T.xo,{asChild:!0,children:(0,a.jsx)("button",{onClick:e=>e.stopPropagation(),className:"p-1.5 hover:bg-gray-100 rounded",children:(0,a.jsx)(m.Z,{size:18,color:"#606060"})})}),(0,a.jsx)(T.yk,{className:"p-0 w-full max-w-xs",children:(0,a.jsx)(L.Z,{data:F,onEmojiSelect:e=>{er?(er.chain().focus().insertContent(null==e?void 0:e.native).run(),J(!1)):console.log("editor not available")}})})]})}),(0,a.jsx)("button",{onClick:()=>{null==er||er.chain().focus().insertContent("@").run()},className:"p-1.5 hover:bg-gray-100 rounded",children:(0,a.jsx)(p.Z,{size:18,color:"#606060"})}),z&&(0,a.jsxs)(s.Fragment,{children:[(0,a.jsx)("div",{className:"w-px h-5 bg-[#E6EAEF] hidden sm:flex"}),(0,a.jsx)("button",{className:"p-1.5 hover:bg-gray-100 rounded hidden sm:flex",children:(0,a.jsx)(f.Z,{size:18,color:"#606060"})}),(0,a.jsx)("button",{className:"p-1.5 hover:bg-gray-100 rounded hidden sm:flex",children:(0,a.jsx)(g.Z,{size:18,color:"#606060"})}),(0,a.jsx)("div",{className:"w-px h-5 bg-[#E6EAEF]"}),(0,a.jsx)("button",{className:"p-1.5 hover:bg-gray-100 rounded hidden sm:flex",children:(0,a.jsx)(D.Z,{size:18,color:"#606060",className:"[&>path]:stroke-[2.5]"})})]})]}),(0,a.jsx)("div",{className:"flex items-center gap-1 py-2 pl-3 pr-4",children:(0,a.jsx)("button",{type:"submit",className:"p-1.5 hover:bg-gray-100 rounded size-8 flex items-center justify-center",onClick:eh,disabled:ed&&(null==el?void 0:el.length)===0,children:(0,a.jsx)(b.Z,{color:ed&&(null==el?void 0:el.length)===0?"#999":"black"})})})]})]})})}},61945:function(e,l,t){var a=t(47411),s=t(32486),n=t(97712),i=t(97220),o=t(86418);l.Z=()=>{let e=(0,a.useParams)().id,{state:l,dispatch:t}=(0,s.useContext)(i.R),r=localStorage.getItem("token")||"",[d,c]=(0,s.useState)(1),[u,x]=(0,s.useState)(!0),[h,v]=(0,s.useState)(!0),m=async function(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;try{var s,i,r;let d=await (0,o.Gl)("/threads/".concat(null==l?void 0:null===(s=l.thread)||void 0===s?void 0:s.thread_id,"/channels/").concat(e,"?page=").concat(a,"&limit=30"));if((null==d?void 0:d.status)===200||(null==d?void 0:d.status)===201){let e=Array.isArray(null===(i=d.data)||void 0===i?void 0:i.data)?null===(r=d.data)||void 0===r?void 0:r.data:[];t({type:n.a.REPLIES,payload:{newThreads:e,newPage:a}}),a>1&&e.length>0?x(!0):x(e.length>=30)}v(!1)}catch(e){console.error("Error fetching threads:",e),x(!1)}finally{c(a)}};return(0,s.useEffect)(()=>{var e;(null==l?void 0:null===(e=l.thread)||void 0===e?void 0:e.thread_id)&&r&&m(1).finally(()=>t({type:n.a.MESSAGE_LOADING,payload:!1}))},[null==l?void 0:l.thread,r,t]),{fetchMoreData:()=>{u&&m(d+1)},hasMore:u,loading:h}}},75149:function(e,l,t){var a=t(75376),s=t(47411),n=t(32486),i=t(97712),o=t(97220),r=t(86418);l.Z=()=>{let e=(0,s.useParams)().id,{state:l,dispatch:t}=(0,n.useContext)(o.R);return(0,n.useEffect)(()=>{e&&(async()=>{let l=await (0,r.Gl)("/channels/".concat(e));if((null==l?void 0:l.status)===200||(null==l?void 0:l.status)===201){var a;t({type:i.a.CHANNEL_DETAILS,payload:null==l?void 0:null===(a=l.data)||void 0===a?void 0:a.data})}t({type:i.a.CHANNEL_LOADING,payload:!1})})()},[t,e,null==l?void 0:l.channelCallback]),(0,a.jsx)(a.Fragment,{})}},68937:function(e,l,t){t.d(l,{Z:function(){return u}});var a=t(75376),s=t(32486),n=t(97220),i=t(20818),o=t(53465),r=t(97712),d=t(47411),c=t(18648);function u(){let e=(0,d.useParams)().id,{state:l,dispatch:t}=(0,s.useContext)(n.R),u=c.env.NEXT_PUBLIC_CONNECT_URL,x=async()=>{let e=localStorage.getItem("token")||"";return(await i.Z.get("".concat(c.env.NEXT_PUBLIC_BASE_URL,"/token/connection"),{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}})).data.data.token},h=async e=>{let l=localStorage.getItem("token")||"";return(await i.Z.post("".concat(c.env.NEXT_PUBLIC_BASE_URL,"/token/subscription"),{channel:e},{headers:{Authorization:"Bearer ".concat(l),"Content-Type":"application/json"}})).data.data.token};return(0,s.useEffect)(()=>{if(e){let l=new o.qX(u,{getToken:x,debug:!0});l.on("connect",()=>{console.log("Connected to Centrifuge")}),l.on("disconnect",()=>{console.log("Disconnected from Centrifuge")});let a=async()=>h(e),s=l.newSubscription(e,{getToken:a});return s.on("subscribed",()=>{t({type:r.a.CHANNEL_SUBSCRIPTION,payload:s})}),s.on("publication",e=>{var l,a,s,n,i,o,d,c,u,x,h,v,m,p,f,g,D,b,j;let E=null==e?void 0:e.data;if((null==e?void 0:null===(l=e.data)||void 0===l?void 0:l.type)==="message"&&t({type:r.a.MESSAGES,payload:{newMessage:e.data,isRealTime:!0}}),(null==E?void 0:E.section)==="channels_section"&&(null==E?void 0:E.notification_type)==="reply_count_change"){let l=null==e?void 0:null===(a=e.data)||void 0===a?void 0:a.data,n=null==e?void 0:null===(s=e.data)||void 0===s?void 0:s.update_change;t({type:r.a.UPDATE_MESSAGE_THREAD,payload:{threadId:l.thread_id,reply:l,updates:n}})}if((null==E?void 0:E.section)==="thread_message"&&(null==E?void 0:E.notification_type)==="updated"){let l=null==e?void 0:null===(n=e.data)||void 0===n?void 0:n.data;t({type:r.a.EDIT_CHANNEL_MESSAGE,payload:{threadId:l.thread_id,newMessageData:l}})}if((null==E?void 0:E.section)==="thread_message"&&(null==E?void 0:E.notification_type)==="deleted"){let l=null==e?void 0:null===(i=e.data)||void 0===i?void 0:i.modification_ids;t({type:r.a.DELETE_CHANNEL_MESSAGE,payload:{threadId:l.thread_id}})}if((null==E?void 0:E.section)==="reply_message"&&(null==E?void 0:E.notification_type)==="updated"){let l=null==e?void 0:null===(o=e.data)||void 0===o?void 0:o.data,a=null==e?void 0:null===(c=e.data)||void 0===c?void 0:null===(d=c.modification_ids)||void 0===d?void 0:d.message_id;t({type:r.a.EDIT_REPLY_MESSAGE,payload:{threadId:a,newMessageData:l}})}if((null==E?void 0:E.section)==="reply_message"&&(null==E?void 0:E.notification_type)==="deleted"){let l=null==e?void 0:null===(u=e.data)||void 0===u?void 0:u.modification_ids,a=null==e?void 0:null===(x=e.data)||void 0===x?void 0:x.update_change;t({type:r.a.DELETE_MESSAGE_THREAD_REPLY,payload:{threadId:l.thread_id,messageId:null==l?void 0:l.message_id,updates:a}})}if((null==E?void 0:E.section)==="thread_message"&&(null==E?void 0:E.notification_type)==="pinned_message_event"){let l=null==e?void 0:null===(h=e.data)||void 0===h?void 0:h.modification_ids,a=null==e?void 0:null===(v=e.data)||void 0===v?void 0:v.pinned_details;t({type:r.a.UPDATE_CHANNEL_PIN,payload:{threadId:l.thread_id,is_pin:!0,details:a}})}if((null==E?void 0:E.section)==="reply_message"&&(null==E?void 0:E.notification_type)==="pinned_message_event"){let l=null==e?void 0:null===(m=e.data)||void 0===m?void 0:m.modification_ids;t({type:r.a.UPDATE_REPLY_PIN,payload:{threadId:l.message_id,is_pin:!0}})}if((null==E?void 0:E.section)==="thread_message"&&(null==E?void 0:E.notification_type)==="unpinned_message_event"){let l=null==e?void 0:null===(p=e.data)||void 0===p?void 0:p.modification_ids;t({type:r.a.UPDATE_CHANNEL_PIN,payload:{threadId:l.thread_id,is_pin:!1}})}if((null==E?void 0:E.section)==="reply_message"&&(null==E?void 0:E.notification_type)==="unpinned_message_event"){let l=null==e?void 0:null===(f=e.data)||void 0===f?void 0:f.modification_ids;t({type:r.a.UPDATE_REPLY_PIN,payload:{threadId:l.message_id,is_pin:!1}})}if((null==E?void 0:E.section)==="thread_message"&&(null==E?void 0:E.notification_type)==="reaction_event"){let l=null==e?void 0:null===(g=e.data)||void 0===g?void 0:g.modification_ids,a=null==e?void 0:null===(D=e.data)||void 0===D?void 0:D.reactions;t({type:r.a.UPDATE_CHANNEL_REACTIONS,payload:{threadId:l.thread_id,reactions:a}})}if((null==E?void 0:E.section)==="reply_message"&&(null==E?void 0:E.notification_type)==="reaction_event"){let l=null==e?void 0:null===(b=e.data)||void 0===b?void 0:b.modification_ids,a=null==e?void 0:null===(j=e.data)||void 0===j?void 0:j.reactions;t({type:r.a.UPDATE_REPLY_REACTIONS,payload:{messageId:l.message_id,reactions:a}})}}),s.on("error",e=>{console.error("Subscription error: ".concat(e.message))}),l.connect(),s.subscribe(),()=>{s.unsubscribe(),l.disconnect()}}},[e,u,t]),(0,a.jsx)("div",{})}},19828:function(e,l,t){t.d(l,{X:function(){return r}});var a=t(75376),s=t(32486),n=t(95833),i=t(28780),o=t(58983);let r=s.forwardRef((e,l)=>{let{className:t,...s}=e;return(0,a.jsx)(n.fC,{ref:l,className:(0,o.cn)("peer h-[15px] w-[15px] shrink-0 rounded-sm border border-[#adadeaf] ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary-500 data-[state=checked]:text-primary-500",t),...s,children:(0,a.jsx)(n.z$,{className:(0,o.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(i.Z,{className:"h-[9px] w-[9px] text-white",strokeWidth:5})})})});r.displayName=n.fC.displayName},17222:function(e,l,t){t.d(l,{E:function(){return r},m:function(){return d}});var a=t(75376),s=t(32486),n=t(40472),i=t(32425),o=t(58983);let r=s.forwardRef((e,l)=>{let{className:t,...s}=e;return(0,a.jsx)(n.fC,{className:(0,o.cn)("grid gap-2",t),...s,ref:l})});r.displayName=n.fC.displayName;let d=s.forwardRef((e,l)=>{let{className:t,...s}=e;return(0,a.jsx)(n.ck,{ref:l,className:(0,o.cn)("relative aspect-square h-4 w-4 rounded-full border border-primary-500 text-primary-500 ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),...s,children:(0,a.jsx)(n.z$,{className:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2",children:(0,a.jsx)(i.Z,{className:"h-2.5 w-2.5 fill-current text-current"})})})});d.displayName=n.ck.displayName},61838:function(e,l,t){t.d(l,{y:function(){return a}});let a=(e,l)=>null==e?void 0:e.filter(e=>Object.values(e).join(" ").toLowerCase().match(l))}}]);