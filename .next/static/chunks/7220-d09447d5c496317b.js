"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7220],{97712:function(e,a,E){E.d(a,{a:function(){return r}});let r={USER:"USER",TOKEN:"TOKEN",ORG_ID:"ORG_ID",CALLBACK:"CALLBACK",LOADING:"LOADING",CHANNEL_LOADING:"CHANNEL_LOADING",NOTIFY:"NOTIFY",ONLINE_STATUS:"ONLINE_STATUS",CHANNELS:"CHANNELS",THREAD:"THREAD",WEBHOOK_STATUS:"WEBHOOK_STATUS",CHANNEL_DETAILS:"CHANNEL_DETAILS",CHANNEL_MODAL:"CHANNEL_MODAL",MEMBERS:"MEMBERS",OTHER_CHANNELS:"OTHER_CHANNELS",ARCHIVED_CHANNELS:"ARCHIVED_CHANNELS",CHANNEL_BAR:"CHANNEL_BAR",VISIBLE:"VISIBLE",VISIBLES:"VISIBLES",SHOW_THREAD:"SHOW_THREAD",OPEN_SIDEBAR:"OPEN_SIDEBAR",APP_CALLBACK:"APP_CALLBACK",SHOW:"SHOW",CREATE_CHANNEL:"CREATE_CHANNEL",CREATE_SECTION:"CREATE_SECTION",ADD_MEMBER:"ADD_MEMBER",ORG_MEMBERS:"ORG_MEMBERS",ORG_INVITES:"ORG_INVITES",ORG_ROLES:"ORG_ROLES",SHOW_RANGE:"SHOW_RANGE",SUMMARY_COUNT:"SUMMARY_COUNT",SECTION_ID:"SECTION_ID",SECTIONS:"SECTIONS",DELETE_SECTION:"DELETE_SECTION",UNGROUPED_CHANNELS:"UNGROUPED_CHANNELS",IS_OPENED:"IS_OPENED",NOTIFICATIONS:"NOTIFICATIONS",MESSAGES:"MESSAGES",MESSAGE_LOADING:"MESSAGE_LOADING",INTEGRATIONS_LOADING:"INTEGRATIONS_LOADING",CHAT_LOADING:"CHAT_LOADING",REPLY_LOADING:"REPLY_LOADING",INTEGRATIONS:"INTEGRATIONS",SUBSCRIPTION_PLAN:"SUBSCRIPTION_PLAN",CHATS:"CHATS",DMS:"DMS",REPLIES:"REPLIES",NOTIFICATION_TYPE:"NOTIFICATION_TYPE",NOTIFICATION_CALLBACK:"NOTIFICATION_CALLBACK",ORG_DATA:"ORG_DATA",CHANNEL_CALLBACK:"CHANNEL_CALLBACK",CHANNEL_SUBSCRIPTION:"CHANNEL_SUBSCRIPTION",CHAT_SUBSCRIPTION:"CHAT_SUBSCRIPTION",REPLY_SUBSCRIPTION:"REPLY_SUBSCRIPTION",NOTIFICATION_SUBSCRIPTION:"NOTIFICATION_SUBSCRIPTION",AGENT_DM:"AGENT_DM",CHANNEL_AGENTS:"CHANNEL_AGENTS",CHANNEL_WORKFLOWS:"CHANNEL_WORKFLOWS",MENTIONS:"MENTIONS",CLEAR_MENTIONS:"CLEAR_MENTIONS",INVITE_MODAL:"INVITE_MODAL",SHOW_PROFILE:"SHOW_PROFILE",REPLY:"REPLY",RECENT_PEOPLE:"RECENT_PEOPLE",RECENT_DM:"RECENT_DM",CLEAR_CHATS:"CLEAR_CHATS",CLEAR_REPLIES:"CLEAR_REPLIES",PROFILE:"PROFILE",SHOW_USER_PROFILE:"SHOW_USER_PROFILE",PROFILE_CALLBACK:"PROFILE_CALLBACK",GROUP_CALLBACK:"GROUP_CALLBACK",REPLY_CALLBACK:"REPLY_CALLBACK",USER_TYPING:"USER_TYPING",TYPING_SUBSCRIPTION:"TYPING_SUBSCRIPTION",UPDATE_MESSAGE_THREAD:"UPDATE_MESSAGE_THREAD",UPDATE_DM_MESSAGE_THREAD:"UPDATE_DM_MESSAGE_THREAD",DELETE_MESSAGE_THREAD_REPLY:"DELETE_MESSAGE_THREAD_REPLY",DELETE_DM_THREAD_REPLY:"DELETE_DM_THREAD_REPLY",DELETE_CHANNEL_MESSAGE:"DELETE_CHANNEL_MESSAGE",DELETE_DM_MESSAGE:"DELETE_DM_MESSAGE",EDIT_CHANNEL_MESSAGE:"EDIT_CHANNEL_MESSAGE",EDIT_DM_MESSAGE:"EDIT_DM_MESSAGE",EDIT_REPLY_MESSAGE:"EDIT_REPLY_MESSAGE",IS_EDIT:"IS_EDIT",IS_EDIT_REPLY:"IS_EDIT_REPLY",THREAD_REPLY:"THREAD_REPLY",ROLE:"ROLE",STATUS_CALLBACK:"STATUS_CALLBACK",DM_NOTIFICATION_SUBSCRIPTION:"DM_NOTIFICATION_SUBSCRIPTION",TOP_LABEL:"TOP_LABEL",ACTIVE_AGENTS:"ACTIVE_AGENTS",INACTIVE_AGENTS:"INACTIVE_AGENTS",MARKETPLACE_AGENTS:"MARKETPLACE_AGENTS",MARKETPLACE_AGENTS_CACHE_TIME:"MARKETPLACE_AGENTS_CACHE_TIME",UPDATE_MENTION_COUNT:"UPDATE_MENTION_COUNT",UPDATE_THREAD_COUNT:"UPDATE_THREAD_COUNT",UPDATE_DM_COUNT:"UPDATE_DM_COUNT",COUNT_CALLBACK:"COUNT_CALLBACK",THREAD_COUNT:"THREAD_COUNT",DM_COUNT:"DM_COUNT",SHOW_BADGE:"SHOW_BADGE",USER_DATA:"USER_DATA",HOVER_PROFILE:"HOVER_PROFILE",STATUS:"STATUS",NOTIFICATION_DETAIL:"NOTIFICATION_DETAIL",CHANNEL_INVITE:"CHANNEL_INVITE",AGENT_MODAL:"AGENT_MODAL",AGENT_STATE:"AGENT_STATE",AGENT_CALLBACK:"AGENT_CALLBACK",SUBSCRIPTION_PLANS:"SUBSCRIPTION_PLANS",CURRENT_SUBCRIPTION:"CURRENT_SUBCRIPTION",ACTIVE_TAB:"ACTIVE_TAB",UPDATE_STREAM:"UPDATE_STREAM",STREAM_COMPLETE:"STREAM_COMPLETE",BOOKMARKS:"BOOKMARKS",PINNED:"PINNED",UPDATE_CHANNEL_PIN:"UPDATE_CHANNEL_PIN",UPDATE_DM_PIN:"UPDATE_DM_PIN",UPDATE_REPLY_PIN:"UPDATE_REPLY_PIN",UPDATE_CHANNEL_REACTIONS:"UPDATE_CHANNEL_REACTIONS",UPDATE_DM_REACTIONS:"UPDATDMEL_REACTIONS",UPDATE_REPLY_REACTIONS:"UPDATE_REPLY_REACTIONS",UPDATE_DM_REPLY_REACTIONS:"UPDATDMEL_REACTIONS",LATER:"LATER",DATA_ID:"DATA_ID",ALL_CHANNELS:"ALL_CHANNELS",WORKFLOWS:"WORKFLOWS",WORKFLOW_CALLBACK:"WORKFLOW_CALLBACK",ACTIVE_WORKFLOWS:"ACTIVE_WORKFLOWS",INACTIVE_WORKFLOWS:"INACTIVE_WORKFLOWS",MARKETPLACE_WORKFLOWS:"MARKETPLACE_WORKFLOWS"}},97220:function(e,a,E){E.d(a,{R:function(){return s},DataProvider:function(){return _}});var r=E(75376),n=E(32486),A=E(97712),t=(e,a)=>{var E,r,n;let{type:t,payload:s}=a;switch(t){case"TOGGLE_AGENT":{let E;let r=e.activatedAgents||[],n=r.findIndex(e=>e.name===a.payload.name);return E=n>=0?[...r.slice(0,n),...r.slice(n+1)]:[...r,a.payload],localStorage.setItem("activatedAgents",JSON.stringify(E)),{...e,activatedAgents:E}}case A.a.USER:return{...e,user:s};case A.a.TOKEN:return{...e,token:s};case A.a.ORG_ID:return{...e,orgId:s};case A.a.CALLBACK:return{...e,callback:s};case A.a.APP_CALLBACK:return{...e,appCallback:s};case A.a.LOADING:return{...e,loading:s};case A.a.CHANNEL_LOADING:return{...e,channelloading:s};case A.a.NOTIFY:return{...e,notify:s};case A.a.ONLINE_STATUS:return{...e,onlineStatus:s};case A.a.CHANNELS:return{...e,channels:s};case A.a.ALL_CHANNELS:return{...e,allChannels:s};case A.a.THREAD:return{...e,thread:s};case A.a.WEBHOOK_STATUS:return{...e,webhookStatus:s};case A.a.CHANNEL_DETAILS:return{...e,channelDetails:s};case A.a.CHANNEL_MODAL:return{...e,channelModal:s};case A.a.MEMBERS:return{...e,members:s};case A.a.OTHER_CHANNELS:return{...e,otherChannels:s};case A.a.ARCHIVED_CHANNELS:return{...e,archivedChannels:s};case A.a.CHANNEL_BAR:return{...e,channelBar:s};case A.a.VISIBLE:return{...e,visible:s};case A.a.VISIBLES:return{...e,visibles:s};case A.a.SHOW_THREAD:return{...e,showThread:s};case A.a.OPEN_SIDEBAR:return{...e,openSidebar:s};case A.a.SHOW:return{...e,show:s};case A.a.CREATE_CHANNEL:return{...e,createChannel:s};case A.a.CREATE_SECTION:return{...e,createSection:s};case A.a.ADD_MEMBER:return{...e,addMember:s};case A.a.ORG_MEMBERS:return{...e,orgMembers:s};case A.a.ORG_INVITES:return{...e,orgInvites:s};case A.a.ORG_ROLES:return{...e,orgRoles:s};case A.a.SHOW_RANGE:return{...e,showRange:s};case A.a.SUMMARY_COUNT:return{...e,summaryCount:s};case A.a.SECTION_ID:return{...e,sectionId:s};case A.a.SECTIONS:return{...e,sections:s};case A.a.DELETE_SECTION:return{...e,deleteSection:s};case A.a.UNGROUPED_CHANNELS:return{...e,ungroupedChannels:s};case A.a.DMS:return{...e,dms:s};case A.a.IS_OPENED:return{...e,isOpened:s};case A.a.NOTIFICATIONS:return{...e,notifications:[...e.notifications,s]};case A.a.MESSAGES:if(null===(E=a.payload)||void 0===E?void 0:E.isRealTime)return{...e,messages:[a.payload.newMessage,...e.messages||[]]};return{...e,messages:1===a.payload.newPage?a.payload.newThreads||[]:[...e.messages||[],...a.payload.newThreads||[]]};case A.a.CHATS:if(null===(r=a.payload)||void 0===r?void 0:r.isRealTime)return{...e,chats:[a.payload.newMessage,...e.chats||[]]};return{...e,chats:1===a.payload.newPage?a.payload.newThreads||[]:[...e.chats||[],...a.payload.newThreads||[]]};case A.a.REPLIES:if(null===(n=a.payload)||void 0===n?void 0:n.isRealTime)return{...e,replies:[a.payload.newMessage,...e.replies||[]]};return{...e,replies:1===a.payload.newPage?a.payload.newThreads||[]:[...e.replies||[],...a.payload.newThreads||[]]};case A.a.CLEAR_CHATS:return{...e,chats:[]};case A.a.CLEAR_REPLIES:return{...e,replies:[]};case A.a.MESSAGE_LOADING:return{...e,messageLoading:s};case A.a.INTEGRATIONS_LOADING:return{...e,integrationsLoading:s};case A.a.CHAT_LOADING:return{...e,chatLoading:s};case A.a.REPLY_LOADING:return{...e,replyLoading:s};case A.a.INTEGRATIONS:return{...e,integrations:s};case A.a.SUBSCRIPTION_PLAN:return{...e,subscriptionPlan:s};case A.a.NOTIFICATION_TYPE:return{...e,notificationType:s};case A.a.NOTIFICATION_CALLBACK:return{...e,notificationCallback:s};case A.a.ORG_DATA:return{...e,orgData:s};case A.a.CHANNEL_CALLBACK:return{...e,channelCallback:s};case A.a.CHANNEL_SUBSCRIPTION:return{...e,channelSubscription:s};case A.a.CHAT_SUBSCRIPTION:return{...e,chatSubscription:s};case A.a.REPLY_SUBSCRIPTION:return{...e,replySubscription:s};case A.a.TYPING_SUBSCRIPTION:return{...e,typingSubscription:s};case A.a.NOTIFICATION_SUBSCRIPTION:return{...e,notificationSubscription:s};case A.a.DM_NOTIFICATION_SUBSCRIPTION:return{...e,dmNotificationSubscription:s};case A.a.AGENT_DM:return{...e,agentDm:s};case A.a.CHANNEL_AGENTS:return{...e,channelAgents:s};case A.a.MENTIONS:return{...e,mentions:[...e.mentions,...s]};case A.a.CLEAR_MENTIONS:return{...e,mentions:[]};case A.a.INVITE_MODAL:return{...e,inviteModal:s};case A.a.SHOW_PROFILE:return{...e,showProfile:s};case A.a.REPLY:return{...e,reply:s};case A.a.RECENT_DM:return{...e,recentDm:s};case A.a.RECENT_PEOPLE:return{...e,recentPeople:s};case A.a.PROFILE:return{...e,profile:s};case A.a.SHOW_USER_PROFILE:return{...e,showUserProfile:s};case A.a.PROFILE_CALLBACK:return{...e,profileCallback:s};case A.a.GROUP_CALLBACK:return{...e,groupCallback:s};case A.a.REPLY_CALLBACK:return{...e,replyCallback:s};case A.a.USER_TYPING:return{...e,userTyping:s};case A.a.UPDATE_MESSAGE_THREAD:{let{threadId:E,reply:r,updates:n}=a.payload,A=e.messages.map(e=>{if(e.thread_id===E){let a=e.messages||[],E=a.some(e=>e.user_id===r.user_id);return{...e,last_reply:null==r?void 0:r.created_at,messages:E?a:[...a,r],message_count:null==n?void 0:n.thread_count}}return e});return{...e,messages:A}}case A.a.UPDATE_DM_MESSAGE_THREAD:{let{threadId:E,reply:r,updates:n}=a.payload,A=e.chats.map(e=>{if(e.thread_id===E){let a=e.messages||[],E=a.some(e=>e.user_id===r.user_id);return{...e,last_reply:null==r?void 0:r.created_at,messages:E?a:[...a,r],message_count:null==n?void 0:n.thread_count}}return e});return{...e,chats:A}}case A.a.DELETE_CHANNEL_MESSAGE:{let{threadId:E}=a.payload;return{...e,messages:(e.messages||[]).filter(e=>e.thread_id!==E)}}case A.a.DELETE_DM_MESSAGE:{let{threadId:E}=a.payload;return{...e,chats:(e.chats||[]).filter(e=>e.thread_id!==E)}}case A.a.DELETE_MESSAGE_THREAD_REPLY:{let{threadId:E,messageId:r,updates:n}=a.payload,A=e.messages.map(e=>{if(e.thread_id===E){let a=e.messages||[],E=a.filter(e=>e.id!==r);return{...e,messages:(null==n?void 0:n.preview_section)?E:a,message_count:null==n?void 0:n.thread_count}}return e});return{...e,replies:(e.replies||[]).filter(e=>e.id!==r),messages:A}}case A.a.DELETE_DM_THREAD_REPLY:{let{threadId:E,messageId:r,updates:n}=a.payload,A=e.chats.map(e=>{if(e.thread_id===E){let a=e.messages||[],E=a.filter(e=>e.id!==r);return{...e,messages:(null==n?void 0:n.preview_section)?E:a,message_count:null==n?void 0:n.thread_count}}return e});return{...e,replies:(e.replies||[]).filter(e=>e.id!==r),chats:A}}case A.a.EDIT_CHANNEL_MESSAGE:{let{threadId:E,newMessageData:r}=a.payload,n=(e.messages||[]).map(e=>e.thread_id===E?{...e,message:r.message,edited:!0}:e);return{...e,messages:n}}case A.a.UPDATE_CHANNEL_PIN:{let{threadId:E,is_pin:r,details:n}=a.payload,A=(e.messages||[]).map(e=>e.thread_id===E?{...e,is_pinned:r,pinned_details:n}:e);return{...e,messages:A}}case A.a.UPDATE_CHANNEL_REACTIONS:{let{threadId:E,reactions:r}=a.payload,n=(e.messages||[]).map(e=>e.thread_id===E?{...e,reactions:r}:e);return{...e,messages:n}}case A.a.UPDATE_REPLY_REACTIONS:{let{messageId:E,reactions:r}=a.payload,n=(e.replies||[]).map(e=>e.id===E?{...e,reactions:r}:e);return{...e,replies:n}}case A.a.UPDATE_DM_REACTIONS:{let{threadId:E,reactions:r}=a.payload,n=(e.chats||[]).map(e=>e.thread_id===E?{...e,reactions:r}:e);return{...e,chats:n}}case A.a.UPDATE_DM_PIN:{let{threadId:E,is_pin:r,details:n}=a.payload,A=(e.chats||[]).map(e=>e.thread_id===E?{...e,is_pinned:r,pinned_details:n}:e);return{...e,chats:A}}case A.a.EDIT_DM_MESSAGE:{let{threadId:E,newMessageData:r}=a.payload,n=(e.chats||[]).map(e=>e.thread_id===E?{...e,message:r.message,edited:!0}:e);return{...e,chats:n}}case A.a.EDIT_REPLY_MESSAGE:{let{threadId:E,newMessageData:r}=a.payload,n=(e.replies||[]).map(e=>e.id===E?{...e,message:r.message,edited:!0}:e);return{...e,replies:n}}case A.a.UPDATE_REPLY_PIN:{let{threadId:E,is_pin:r,details:n}=a.payload,A=(e.replies||[]).map(e=>e.id===E?{...e,is_pinned:r,pinned_details:n}:e);return{...e,replies:A}}case A.a.IS_EDIT:return{...e,isEdit:s};case A.a.IS_EDIT_REPLY:return{...e,isEditReply:s};case A.a.THREAD_REPLY:return{...e,threadReply:s};case A.a.ROLE:return{...e,role:s};case A.a.STATUS_CALLBACK:return{...e,statusCallback:s};case A.a.TOP_LABEL:return{...e,topLabel:s};case A.a.ACTIVE_AGENTS:return{...e,activeAgents:s};case A.a.INACTIVE_AGENTS:return{...e,inactiveAgents:s};case A.a.MARKETPLACE_AGENTS:return{...e,marketPlaceAgents:s};case A.a.MARKETPLACE_AGENTS_CACHE_TIME:return{...e,marketPlaceAgentsCacheTime:s};case A.a.UPDATE_THREAD_COUNT:{let E=a.payload.channels_id;return{...e,channels:e.channels.map(e=>e.channels_id===E?{...e,mention_count:a.payload.mention_count,thread_count:a.payload.thread_count}:e)}}case A.a.UPDATE_DM_COUNT:{let E=a.payload.channel_id;return{...e,dms:e.dms.map(e=>e.channel_id===E?{...e,thread_count:a.payload.thread_count}:e)}}case A.a.COUNT_CALLBACK:return{...e,countCallback:s};case A.a.THREAD_COUNT:return{...e,threadCount:s};case A.a.DM_COUNT:return{...e,dmCount:s};case A.a.SHOW_BADGE:return{...e,showBadge:s};case A.a.USER_DATA:return{...e,userData:s};case A.a.HOVER_PROFILE:return{...e,hoverProfile:s};case A.a.STATUS:return{...e,status:s};case A.a.NOTIFICATION_DETAIL:return{...e,notificationDetail:s};case A.a.CHANNEL_INVITE:return{...e,channelInvite:s};case A.a.AGENT_MODAL:return{...e,agentModal:s};case A.a.AGENT_STATE:return{...e,agentState:s};case A.a.AGENT_CALLBACK:return{...e,agentCallback:s};case A.a.SUBSCRIPTION_PLANS:return{...e,subscriptionPlans:s};case A.a.CURRENT_SUBCRIPTION:return{...e,currentSubscription:s};case A.a.ACTIVE_TAB:return{...e,activeTab:s};case A.a.BOOKMARKS:return{...e,bookmarks:s};case A.a.PINNED:return{...e,pinned:s};case A.a.LATER:return{...e,later:s};case A.a.DATA_ID:return{...e,dataId:s};case A.a.WORKFLOWS:return{...e,workflows:s};case A.a.WORKFLOW_CALLBACK:return{...e,workflowCallback:s};case A.a.CHANNEL_WORKFLOWS:return{...e,channelWorkflows:s};case A.a.ACTIVE_WORKFLOWS:return{...e,activeWorkflows:s};case A.a.INACTIVE_WORKFLOWS:return{...e,inactiveWorkflows:s};case A.a.MARKETPLACE_WORKFLOWS:return{...e,marketPlaceWorkflows:s};default:return e}};let s=(0,n.createContext)(void 0),_=e=>{let{children:a}=e,[E,A]=(0,n.useReducer)(t,{user:null,token:null,callback:!1,loading:!1,channelloading:!0,notify:null,onlineStatus:"offline",channels:null,otherChannels:null,thread:null,webhookStatus:null,channelDetails:null,channelModal:!1,members:null,archivedChannels:null,channelBar:!1,visible:!1,visibles:!1,showThread:!1,openSidebar:!1,appCallback:!1,show:null,createChannel:!1,createSection:!1,addMember:!1,orgMembers:null,orgInvite:null,orgRoles:null,showRange:!1,summaryCount:0,sectionId:null,sections:null,deleteSection:!1,ungroupedChannels:null,isOpened:"",notifications:[],messages:[],chats:[],dms:[],replies:[],integrations:[],messageLoading:!0,integrationsLoading:!0,chatLoading:!0,replyLoading:!0,subscriptionPlan:null,orgId:null,notificationType:null,notificationCallback:!1,orgData:null,channelCallback:!1,channelSubscription:null,chatSubscription:null,replySubscription:null,agentDm:[],channelAgents:[],mentions:[],inviteModal:!1,showProfile:!1,hoverProfile:!1,reply:!1,recentPeople:[],recentDm:[],profile:null,showUserProfile:!1,profileCallback:!1,groupCallback:!1,replyCallback:!1,userTyping:[],typingSubscription:null,notificationSubscription:null,dmNotificationSubscription:null,isEdit:!1,isEditReply:!1,threadReply:null,role:"",statusCallback:!1,topLabel:"Active",activeAgents:[],inactiveAgents:[],marketPlaceAgents:[],marketPlaceAgentsCacheTime:null,countCallback:!1,threadCount:0,dmCount:0,showBadge:!1,userData:null,status:!1,notificationDetail:null,channelInvite:!1,agentModal:!1,agentState:null,agentCallback:!1,bookmarks:[],pinned:[],subscriptionPlans:null,currentSubscription:null,activeTab:"about",later:[],dataId:"",allChannels:[],workflows:[],workflowCallback:!1,channelWorkflows:[],activeWorkflows:[],inactiveWorkflows:[],marketPlaceWorkflows:[]});return(0,r.jsx)(s.Provider,{value:{state:E,dispatch:A},children:a})}}}]);