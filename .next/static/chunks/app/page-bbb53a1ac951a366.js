(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1931],{45693:function(e,s,n){Promise.resolve().then(n.bind(n,15187))},47411:function(e,s,n){"use strict";var r=n(13362);n.o(r,"useParams")&&n.d(s,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(s,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(s,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(s,{useSearchParams:function(){return r.useSearchParams}})},15187:function(e,s,n){"use strict";n.r(s),n.d(s,{default:function(){return a}});var r=n(75376),u=n(32486),t=n(47411);function a(){let e=(0,t.useRouter)();return(0,u.useEffect)(()=>{e.push("/dashboard")},[e]),(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold mb-4",children:"Loading Telex..."}),(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"})]})})}}},function(e){e.O(0,[7542,2344,1744],function(){return e(e.s=45693)}),_N_E=e.O()}]);