(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6695],{90075:function(e,t,r){Promise.resolve().then(r.bind(r,87589))},22909:function(e,t,r){"use strict";r.d(t,{GoogleOAuthProvider:function(){return u},Nq:function(){return i}});var n=r(32486);let o=(0,n.createContext)(null);function u(e){let{clientId:t,nonce:r,onScriptLoadSuccess:u,onScriptLoadError:i,children:c}=e,l=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{nonce:t,onScriptLoadSuccess:r,onScriptLoadError:o}=e,[u,i]=(0,n.useState)(!1),c=(0,n.useRef)(r);c.current=r;let l=(0,n.useRef)(o);return l.current=o,(0,n.useEffect)(()=>{let e=document.createElement("script");return e.src="https://accounts.google.com/gsi/client",e.async=!0,e.defer=!0,e.nonce=t,e.onload=()=>{var e;i(!0),null===(e=c.current)||void 0===e||e.call(c)},e.onerror=()=>{var e;i(!1),null===(e=l.current)||void 0===e||e.call(l)},document.body.appendChild(e),()=>{document.body.removeChild(e)}},[t]),u}({nonce:r,onScriptLoadSuccess:u,onScriptLoadError:i}),s=(0,n.useMemo)(()=>({clientId:t,scriptLoadedSuccessfully:l}),[t,l]);return n.createElement(o.Provider,{value:s},c)}function i(e){let{flow:t="implicit",scope:r="",onSuccess:u,onError:i,onNonOAuthError:c,overrideScope:l,state:s,...a}=e,{clientId:f,scriptLoadedSuccessfully:d}=function(){let e=(0,n.useContext)(o);if(!e)throw Error("Google OAuth components must be used within GoogleOAuthProvider");return e}(),v=(0,n.useRef)(),h=(0,n.useRef)(u);h.current=u;let m=(0,n.useRef)(i);m.current=i;let p=(0,n.useRef)(c);p.current=c,(0,n.useEffect)(()=>{var e,n;if(!d)return;let o="implicit"===t?"initTokenClient":"initCodeClient",u=null===(n=null===(e=null==window?void 0:window.google)||void 0===e?void 0:e.accounts)||void 0===n?void 0:n.oauth2[o]({client_id:f,scope:l?r:"openid profile email ".concat(r),callback:e=>{var t,r;if(e.error)return null===(t=m.current)||void 0===t?void 0:t.call(m,e);null===(r=h.current)||void 0===r||r.call(h,e)},error_callback:e=>{var t;null===(t=p.current)||void 0===t||t.call(p,e)},state:s,...a});v.current=u},[f,d,t,r,s]);let g=(0,n.useCallback)(e=>{var t;return null===(t=v.current)||void 0===t?void 0:t.requestAccessToken(e)},[]),T=(0,n.useCallback)(()=>{var e;return null===(e=v.current)||void 0===e?void 0:e.requestCode()},[]);return"implicit"===t?g:T}},47411:function(e,t,r){"use strict";var n=r(13362);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},18648:function(e,t,r){"use strict";var n,o;e.exports=(null==(n=r.g.process)?void 0:n.env)&&"object"==typeof(null==(o=r.g.process)?void 0:o.env)?r.g.process:r(30027)},30027:function(e){!function(){var t={229:function(e){var t,r,n,o=e.exports={};function u(){throw Error("setTimeout has not been defined")}function i(){throw Error("clearTimeout has not been defined")}function c(e){if(t===setTimeout)return setTimeout(e,0);if((t===u||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(r){try{return t.call(null,e,0)}catch(r){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:u}catch(e){t=u}try{r="function"==typeof clearTimeout?clearTimeout:i}catch(e){r=i}}();var l=[],s=!1,a=-1;function f(){s&&n&&(s=!1,n.length?l=n.concat(l):a=-1,l.length&&d())}function d(){if(!s){var e=c(f);s=!0;for(var t=l.length;t;){for(n=l,l=[];++a<t;)n&&n[a].run();a=-1,t=l.length}n=null,s=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===i||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function v(e,t){this.fun=e,this.array=t}function h(){}o.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];l.push(new v(e,t)),1!==l.length||s||c(d)},v.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=h,o.addListener=h,o.once=h,o.off=h,o.removeListener=h,o.removeAllListeners=h,o.emit=h,o.prependListener=h,o.prependOnceListener=h,o.listeners=function(e){return[]},o.binding=function(e){throw Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw Error("process.chdir is not supported")},o.umask=function(){return 0}}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var u=r[e]={exports:{}},i=!0;try{t[e](u,u.exports,n),i=!1}finally{i&&delete r[e]}return u.exports}n.ab="//";var o=n(229);e.exports=o}()},87589:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return s}});var n=r(75376),o=r(22909),u=r(47411),i=r(32486),c=r(97220),l=r(18648);function s(e){let{children:t}=e,[r,s]=(0,i.useState)(!0),a=(0,u.useRouter)();if((0,i.useEffect)(()=>{if(localStorage.getItem("token")){a.push("/client");return}s(!1)},[a]),!r)return(0,n.jsx)(o.GoogleOAuthProvider,{clientId:l.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,children:(0,n.jsx)(c.DataProvider,{children:t})})}}},function(e){e.O(0,[7220,7542,2344,1744],function(){return e(e.s=90075)}),_N_E=e.O()}]);