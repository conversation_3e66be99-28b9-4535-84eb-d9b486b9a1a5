(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8649],{77648:function(e,t,n){Promise.resolve().then(n.bind(n,73080))},39713:function(e,t,n){"use strict";n.d(t,{default:function(){return l.a}});var r=n(74033),l=n.n(r)},16669:function(e,t,n){"use strict";n.d(t,{default:function(){return l.a}});var r=n(6092),l=n.n(r)},47411:function(e,t,n){"use strict";var r=n(13362);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},74033:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return o},getImageProps:function(){return s}});let r=n(60723),l=n(25738),i=n(28863),a=r._(n(44543));function s(e){let{props:t}=(0,l.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,n]of Object.entries(t))void 0===n&&delete t[e];return{props:t}}let o=i.Image},73080:function(e,t,n){"use strict";var r=n(75376),l=n(47411),i=n(16669),a=n(39713);n(32486);var s=n(11492);t.default=()=>{let e=(0,l.useSearchParams)().get("email")||"";return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("main",{className:"w-full min-h-screen flex flex-col md:flex-row",children:[(0,r.jsxs)("section",{className:"w-full md:w-[45%] bg-[#F9FAFB] px-[20px] md:px-[40px] py-[20px] hidden lg:block",children:[(0,r.jsx)(i.default,{href:"/",children:(0,r.jsx)(a.default,{src:"/login_logo.svg",alt:"",width:86,height:31})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"w-full flex flex-col gap-[12px] mb-[40px] md:mb-[71px]",children:[(0,r.jsxs)("h1",{className:"w-full text-center mt-[40px] md:mt-[70px] text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]",children:["All Your"," ",(0,r.jsx)("span",{className:"text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px] bg-gradient-to-t from-[#8860F8] to-[#7141F8] bg-clip-text text-transparent",children:"Notifications"})," ","In One App!!!"]}),(0,r.jsx)("p",{className:"w-full text-center text-[12px] md:text-[14px] text-[#344054] font-[400] leading-[18px] md:leading-[21px] px-[20px] md:px-[90px]",children:"Get real-time notifications per deliverables and achieve efficient communication with teammates and your deployed solutions."})]}),(0,r.jsx)(a.default,{src:"/login_img.svg",alt:"loginImg",width:320,height:320,className:"flex justify-center items-center mx-auto md:w-[420px] md:h-[420px]"})]})]}),(0,r.jsxs)("section",{className:"w-full md:w-[55%] flex flex-col max-w-xs md:max-w-lg mx-auto items-start justify-center pt-[20px] md:pt-[30px]",children:[(0,r.jsx)(i.default,{href:"/",children:(0,r.jsx)(a.default,{src:"/logomobile.svg",alt:"logo_mobile",width:86,height:31,className:"sm:block md:block lg:hidden flex"})}),(0,r.jsxs)("div",{className:"w-full flex flex-col justify-center mt-[60px] md:mt-[80px] items-center gap-[8px] mb-[10px]",children:[(0,r.jsx)("h1",{className:"w-full text-left text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]",children:"Check your Email"}),(0,r.jsxs)("p",{className:"w-full text-left text-[14px] md:text-[16px] text-[#344054] font-[400] leading-[21px] md:leading-[27px] mb-[32px]",children:["We have sent an email with the password reset information to"," ",e?e.replace(/(.{2}).+(@.+)/,"$1***$2"):"your email address","."]}),(0,r.jsx)("p",{className:"w-full text-center text-[12px] md:text-[14px] text-[#344054] font-[400] leading-[18px] md:leading-[21px]",children:"Didn’t receive the email? Check your spam folder or"})]}),(0,r.jsxs)("div",{className:"w-full flex flex-col gap-[16px]",children:[(0,r.jsx)(s.z,{type:"button",variant:"default",className:"py-6 bg-[#7141F8] hover:bg-[#8760f8] text-white",children:"Resend Email"}),(0,r.jsx)(i.default,{href:"/auth/login",children:(0,r.jsx)(s.z,{type:"button",variant:"outline",className:"w-full py-6 border border-[#8760f8] text-[#8760f8] bg-white hover:bg-[#7141F8] hover:text-white",children:"Back to Login"})})]})]})]})})}},11492:function(e,t,n){"use strict";n.d(t,{d:function(){return o},z:function(){return u}});var r=n(75376),l=n(32486),i=n(91007),a=n(53447),s=n(58983);let o=(0,a.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=l.forwardRef((e,t)=>{let{className:n,variant:l,size:a,asChild:u=!1,...d}=e,c=u?i.g7:"button";return(0,r.jsx)(c,{className:(0,s.cn)(o({variant:l,size:a,className:n})),ref:t,...d})});u.displayName="Button"},58983:function(e,t,n){"use strict";n.d(t,{cn:function(){return i},k:function(){return a}});var r=n(89824),l=n(97215);function i(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,l.m6)((0,r.W)(t))}let a=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"},29626:function(e,t,n){"use strict";n.d(t,{F:function(){return i},e:function(){return a}});var r=n(32486);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let n=!1,r=e.map(e=>{let r=l(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():l(e[t],null)}}}}function a(...e){return r.useCallback(i(...e),e)}},91007:function(e,t,n){"use strict";n.d(t,{Z8:function(){return a},g7:function(){return s},sA:function(){return u}});var r=n(32486),l=n(29626),i=n(75376);function a(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){let e,a;let s=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,o=function(e,t){let n={...t};for(let r in t){let l=e[r],i=t[r];/^on[A-Z]/.test(r)?l&&i?n[r]=(...e)=>{let t=i(...e);return l(...e),t}:l&&(n[r]=l):"style"===r?n[r]={...l,...i}:"className"===r&&(n[r]=[l,i].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==r.Fragment&&(o.ref=t?(0,l.F)(t,s):s),r.cloneElement(n,o)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:l,...a}=e,s=r.Children.toArray(l),o=s.find(d);if(o){let e=o.props.children,l=s.map(t=>t!==o?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...a,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,l):null})}return(0,i.jsx)(t,{...a,ref:n,children:l})});return n.displayName=`${e}.Slot`,n}var s=a("Slot"),o=Symbol("radix.slottable");function u(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=o,t}function d(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},53447:function(e,t,n){"use strict";n.d(t,{j:function(){return a}});var r=n(89824);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=r.W,a=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return i(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:a,defaultVariants:s}=t,o=Object.keys(a).map(e=>{let t=null==n?void 0:n[e],r=null==s?void 0:s[e];if(null===t)return null;let i=l(t)||l(r);return a[e][i]}),u=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return i(e,o,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...l}=t;return Object.entries(l).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...s,...u}[t]):({...s,...u})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}}},function(e){e.O(0,[8863,7140,6092,7542,2344,1744],function(){return e(e.s=77648)}),_N_E=e.O()}]);