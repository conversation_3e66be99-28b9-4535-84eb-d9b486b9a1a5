(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8945],{61658:function(e,t,a){Promise.resolve().then(a.bind(a,73886))},39713:function(e,t,a){"use strict";a.d(t,{default:function(){return r.a}});var n=a(74033),r=a.n(n)},16669:function(e,t,a){"use strict";a.d(t,{default:function(){return r.a}});var n=a(6092),r=a.n(n)},47411:function(e,t,a){"use strict";var n=a(13362);a.o(n,"useParams")&&a.d(t,{useParams:function(){return n.useParams}}),a.o(n,"usePathname")&&a.d(t,{usePathname:function(){return n.usePathname}}),a.o(n,"useRouter")&&a.d(t,{useRouter:function(){return n.useRouter}}),a.o(n,"useSearchParams")&&a.d(t,{useSearchParams:function(){return n.useSearchParams}})},74033:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return l},getImageProps:function(){return i}});let n=a(60723),r=a(25738),s=a(28863),o=n._(a(44543));function i(e){let{props:t}=(0,r.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,a]of Object.entries(t))void 0===a&&delete t[e];return{props:t}}let l=s.Image},73886:function(e,t,a){"use strict";a.r(t);var n=a(75376),r=a(16669),s=a(39713),o=a(32486),i=a(11492),l=a(47411),d=a(56603),u=a(10952),c=a(73003),f=a(13352);t.default=function(){let e=(0,l.useRouter)(),[t,a]=(0,o.useState)(!1),[x,p]=(0,o.useState)(!0),m=(0,l.useSearchParams)().get("token"),[v,g]=(0,o.useState)(""),[h,w]=(0,o.useState)(""),b=o.useRef(!1);(0,o.useEffect)(()=>{m&&!b.current&&(async()=>{b.current=!0;let e=await (0,u.xo)("/auth/magick-link/verify",{token:m});if((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201){var t,a,n,r,s,o,i,l,d,c,x;w(null==e?void 0:null===(n=e.data)||void 0===n?void 0:null===(a=n.data)||void 0===a?void 0:null===(t=a.user)||void 0===t?void 0:t.is_onboarded),localStorage.setItem("token",null==e?void 0:null===(s=e.data)||void 0===s?void 0:null===(r=s.data)||void 0===r?void 0:r.access_token),localStorage.setItem("user",JSON.stringify(null==e?void 0:null===(i=e.data)||void 0===i?void 0:null===(o=i.data)||void 0===o?void 0:o.user)),localStorage.setItem("orgId",null==e?void 0:null===(c=e.data)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:null===(l=d.user)||void 0===l?void 0:l.current_org),f.Z.success(null==e?void 0:null===(x=e.data)||void 0===x?void 0:x.message),g("verified")}else g("not-verified");p(!1)})()},[m]);let j=async()=>{a(!0),setTimeout(()=>{"verified"===v?h?e.push("/client"):e.push("/client/welcome"):e.push("/auth/login")},2e3)};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(d.Toaster,{}),(0,n.jsxs)("main",{className:"w-full min-h-screen flex flex-col md:flex-row",children:[(0,n.jsxs)("section",{className:"w-full md:w-[45%] bg-[#F9FAFB] px-[20px] md:px-[40px] py-[20px] hidden lg:block",children:[(0,n.jsx)(r.default,{href:"/",children:(0,n.jsx)(s.default,{src:"/login_logo.svg",alt:"",width:86,height:31})}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"w-full flex flex-col gap-[12px] mb-[40px] md:mb-[71px]",children:[(0,n.jsxs)("h1",{className:"w-full text-center mt-[40px] md:mt-[70px] text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]",children:["All Your"," ",(0,n.jsx)("span",{className:"text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px] bg-gradient-to-t from-[#8860F8] to-[#7141F8] bg-clip-text text-transparent",children:"Notifications"})," ","In One App!!!"]}),(0,n.jsx)("p",{className:"w-full text-center text-[12px] md:text-[14px] text-[#344054] font-[400] leading-[18px] md:leading-[21px] px-[20px] md:px-[90px]",children:"Your Central Hub for Real-Time Notifications, Events, and Errors – Stay Connected to Your Infrastructure, Databases, and Servers with Instant Updates."})]}),(0,n.jsx)(s.default,{src:"/login_img.png",alt:"loginImg",width:320,height:320,className:"flex justify-center items-center mx-auto md:w-[420px] md:h-[420px]"})]})]}),x?(0,n.jsx)("section",{className:"w-full text-center md:w-[55%] flex flex-col max-w-xs md:max-w-lg mx-auto items-start pt-[30px]",children:(0,n.jsx)("div",{className:"w-full flex flex-col justify-center mt-[100px] md:mt-[160px] items-center gap-[8px] mb-[32px] ",children:(0,n.jsx)(c.Z,{height:"60",width:"60",color:"#7141F8"})})}):(0,n.jsx)(n.Fragment,{children:"verified"===v?(0,n.jsxs)("section",{className:"w-full text-center md:w-[55%] flex flex-col max-w-xs md:max-w-lg mx-auto items-start pt-[30px]",children:[(0,n.jsx)(r.default,{href:"/",children:(0,n.jsx)(s.default,{src:"/logomobile.svg",alt:"logo_mobile",width:86,height:31,className:"w-full sm:block md:block mx-auto lg:hidden flex"})}),(0,n.jsxs)("div",{className:"w-full flex flex-col justify-center mt-[100px] md:mt-[160px] items-center gap-[8px] mb-[32px] ",children:[(0,n.jsx)("h1",{className:"w-full text-center text-[18px] md:text-[22px] font-[600] leading-[30px] md:leading-[35px]",children:"Login Successful"}),(0,n.jsx)("p",{className:"w-full text-center text-[14px] md:text-[16px] text-[#344054] font-[400] leading-[21px] md:leading-[27px]",children:"Please click the button below to proceed to your dashboard"})]}),(0,n.jsx)("div",{className:"w-full text-center",children:(0,n.jsx)(i.z,{type:"submit",variant:"default",className:"py-6 bg-[#7141F8] hover:bg-[#8760f8] text-white mx-auto",onClick:j,children:t?(0,n.jsxs)("span",{className:"flex items-center gap-x-2",children:[(0,n.jsx)("span",{className:"animate-pulse",children:"Loading..."})," ",(0,n.jsx)(c.Z,{width:"20",height:"20"})]}):"Proceed to dashboard"})})]}):(0,n.jsxs)("section",{className:"w-full text-center md:w-[55%] flex flex-col max-w-xs md:max-w-lg mx-auto items-start pt-[30px]",children:[(0,n.jsx)(r.default,{href:"/",children:(0,n.jsx)(s.default,{src:"/logomobile.svg",alt:"logo_mobile",width:86,height:31,className:"w-full sm:block md:block mx-auto lg:hidden flex"})}),(0,n.jsxs)("div",{className:"w-full flex flex-col justify-center mt-[100px] md:mt-[160px] items-center gap-[8px] mb-[32px] ",children:[(0,n.jsx)("h1",{className:"w-full text-center text-[18px] md:text-[22px] font-[600] leading-[30px] md:leading-[35px]",children:"Token Invalid or Expired"}),(0,n.jsx)("p",{className:"w-full text-center text-[14px] md:text-[16px] text-[#344054] font-[400] leading-[21px] md:leading-[27px]",children:"Please go to the magic link page and try again"})]}),(0,n.jsxs)("div",{className:"w-full text-center",children:[(0,n.jsx)(i.z,{type:"submit",variant:"default",className:"py-6 bg-[#7141F8] hover:bg-[#8760f8] text-white mx-auto",onClick:j,children:t?(0,n.jsxs)("span",{className:"flex items-center gap-x-2",children:[(0,n.jsx)("span",{className:"animate-pulse",children:"Loading..."})," ",(0,n.jsx)(c.Z,{width:"20",height:"20"})]}):"Proceed to Login"}),(0,n.jsxs)("p",{className:"text-[14px] mt-4 font-[400] leading-[21px] text-center",children:["Don’t have an account?"," ",(0,n.jsx)(r.default,{href:"/auth/sign-up",children:(0,n.jsx)("span",{className:"text-[14px] font-[500] leading-[21px] text-[#7141F8] hover:text-[#0A0A0A]",children:"Sign up"})})]})]})]})})]})]})}},11492:function(e,t,a){"use strict";a.d(t,{d:function(){return l},z:function(){return d}});var n=a(75376),r=a(32486),s=a(91007),o=a(53447),i=a(58983);let l=(0,o.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,t)=>{let{className:a,variant:r,size:o,asChild:d=!1,...u}=e,c=d?s.g7:"button";return(0,n.jsx)(c,{className:(0,i.cn)(l({variant:r,size:o,className:a})),ref:t,...u})});d.displayName="Button"},73003:function(e,t,a){"use strict";var n=a(75376);a(32486);var r=a(10983);t.Z=e=>{let{height:t,width:a,color:s}=e;return(0,n.jsx)(r.iT,{height:t||20,width:a||20,color:s||"#fff",visible:!0,ariaLabel:"oval-loading",secondaryColor:s||"#fff",strokeWidth:5,strokeWidthSecondary:5})}},56603:function(e,t,a){"use strict";a.d(t,{Toaster:function(){return g}});var n=a(75376),r=a(32486),s=a(65807),o=a(53447),i=a(22397),l=a(58983);let d=s.zt,u=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)(s.l_,{ref:t,className:(0,l.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",a),...r})});u.displayName=s.l_.displayName;let c=(0,o.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),f=r.forwardRef((e,t)=>{let{className:a,variant:r,...o}=e;return(0,n.jsx)(s.fC,{ref:t,className:(0,l.cn)(c({variant:r}),a),...o})});f.displayName=s.fC.displayName,r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)(s.aU,{ref:t,className:(0,l.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",a),...r})}).displayName=s.aU.displayName;let x=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)(s.x8,{ref:t,className:(0,l.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",a),"toast-close":"",...r,children:(0,n.jsx)(i.Z,{className:"h-4 w-4"})})});x.displayName=s.x8.displayName;let p=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)(s.Dx,{ref:t,className:(0,l.cn)("text-sm font-semibold",a),...r})});p.displayName=s.Dx.displayName;let m=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)(s.dk,{ref:t,className:(0,l.cn)("text-sm opacity-90",a),...r})});m.displayName=s.dk.displayName;var v=a(15125);function g(){let{toasts:e}=(0,v.pm)();return(0,n.jsxs)(d,{children:[e.map(function(e){let{id:t,title:a,description:r,action:s,...o}=e;return(0,n.jsxs)(f,{...o,children:[(0,n.jsxs)("div",{className:"grid gap-1",children:[a&&(0,n.jsx)(p,{children:a}),r&&(0,n.jsx)(m,{children:r})]}),s,(0,n.jsx)(x,{})]},t)}),(0,n.jsx)(u,{})]})}},15125:function(e,t,a){"use strict";a.d(t,{pm:function(){return f}});var n=a(32486);let r=0,s=new Map,o=e=>{if(s.has(e))return;let t=setTimeout(()=>{s.delete(e),u({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?o(a):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function u(e){d=i(d,e),l.forEach(e=>{e(d)})}function c(e){let{...t}=e,a=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),n=()=>u({type:"DISMISS_TOAST",toastId:a});return u({type:"ADD_TOAST",toast:{...t,id:a,open:!0,onOpenChange:e=>{e||n()}}}),{id:a,dismiss:n,update:e=>u({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function f(){let[e,t]=n.useState(d);return n.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:c,dismiss:e=>u({type:"DISMISS_TOAST",toastId:e})}}},58983:function(e,t,a){"use strict";a.d(t,{cn:function(){return s},k:function(){return o}});var n=a(89824),r=a(97215);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.m6)((0,n.W)(t))}let o=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"},10952:function(e,t,a){"use strict";a.d(t,{Gl:function(){return i},an:function(){return d},jx:function(){return u},xo:function(){return l}});var n=a(20818),r=a(13352),s=a(18648);let o=s.env.NEXT_PUBLIC_BASE_URL;s.env.NEXT_PUBLIC_INTEGRATION_URL;let i=async(e,t)=>{try{return await n.Z.get(o+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var a,r,s;return((null==e?void 0:null===(a=e.response)||void 0===a?void 0:a.status)===401||(null==e?void 0:null===(s=e.response)||void 0===s?void 0:null===(r=s.data)||void 0===r?void 0:r.status_code)===401)&&(localStorage.clear(),window.location.href="/auth/login"),e}},l=async(e,t,a)=>{try{return await n.Z.post(o+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var s,i,l,d,u;return r.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(s=i.data)||void 0===s?void 0:s.message),(null==e?void 0:null===(l=e.response)||void 0===l?void 0:l.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(u=e.response)||void 0===u?void 0:null===(d=u.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},d=async(e,t,a)=>{try{return await n.Z.put(o+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var s,i,l,d,u;return r.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(s=i.data)||void 0===s?void 0:s.message),(null==e?void 0:null===(l=e.response)||void 0===l?void 0:l.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(u=e.response)||void 0===u?void 0:null===(d=u.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},u=async(e,t)=>{try{return await n.Z.delete(o+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var a,s,i,l,d;return r.Z.error(null==e?void 0:null===(s=e.response)||void 0===s?void 0:null===(a=s.data)||void 0===a?void 0:a.message),(null==e?void 0:null===(i=e.response)||void 0===i?void 0:i.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(l=d.data)||void 0===l?void 0:l.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}}}},function(e){e.O(0,[4269,8863,7140,6092,4144,5300,5614,2987,5243,7542,2344,1744],function(){return e(e.s=61658)}),_N_E=e.O()}]);