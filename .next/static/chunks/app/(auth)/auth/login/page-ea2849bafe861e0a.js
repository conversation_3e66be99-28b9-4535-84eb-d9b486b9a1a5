(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7974],{77340:function(e,t,a){Promise.resolve().then(a.bind(a,48609))},48609:function(e,t,a){"use strict";a.r(t);var s=a(75376),o=a(16669),r=a(19828),l=a(39713),n=a(32486),i=a(11492),d=a(47411),c=a(56603),u=a(10952),x=a(73003),p=a(72859),f=a(22909);t.default=function(){let[e,t]=(0,n.useState)(""),[a,v]=(0,n.useState)(""),[m,g]=(0,n.useState)(!1),[h,w]=(0,n.useState)({email:"",password:""}),b=(0,d.useRouter)(),j=(0,d.useSearchParams)(),[y,N]=(0,n.useState)(!1),[S,_]=(0,n.useState)(!1),T=()=>{let t={email:"",password:""};return e?/\S+@\S+\.\S+/.test(e)||(t.email="Invalid email address"):t.email="Email is required",a?a.length<6&&(t.password="Password must be at least 6 characters"):t.password="Password is required",w(t),!t.email&&!t.password},I=async t=>{if(t.preventDefault(),T()){var s,o,r,l,n,i,d,c,x,f,v,m,g,h,w,y;N(!0);let t=j.get("redirect")||"",S=await (0,u.xo)("/auth/login",{email:e,password:a});(null==S?void 0:S.status)===200||(null==S?void 0:S.status)===201?(localStorage.setItem("token",null==S?void 0:null===(o=S.data)||void 0===o?void 0:null===(s=o.data)||void 0===s?void 0:s.access_token),localStorage.setItem("useremail",null==S?void 0:null===(n=S.data)||void 0===n?void 0:null===(l=n.data)||void 0===l?void 0:null===(r=l.user)||void 0===r?void 0:r.email),localStorage.setItem("user",JSON.stringify(null==S?void 0:null===(d=S.data)||void 0===d?void 0:null===(i=d.data)||void 0===i?void 0:i.user)),localStorage.setItem("orgId",null==S?void 0:null===(f=S.data)||void 0===f?void 0:null===(x=f.data)||void 0===x?void 0:null===(c=x.user)||void 0===c?void 0:c.current_org),t?setTimeout(()=>{b.push(t)},100):setTimeout(()=>{var e,t,a;(null==S?void 0:null===(a=S.data)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:null===(e=t.user)||void 0===e?void 0:e.is_onboarded)?b.push("/client"):b.push("/client/welcome")},100),(0,p.TH)(null==S?void 0:null===(g=S.data)||void 0===g?void 0:null===(m=g.data)||void 0===m?void 0:null===(v=m.user)||void 0===v?void 0:v.username,"New User Login",null==S?void 0:null===(h=S.data)||void 0===h?void 0:h.message,"success")):(N(!1),(0,p.TH)(e,"New User Login",null==S?void 0:null===(y=S.response)||void 0===y?void 0:null===(w=y.data)||void 0===w?void 0:w.message,"error"))}},k=(0,f.Nq)({flow:"auth-code",onSuccess:async e=>{_(!0);let{code:t}=e;try{let e=await (0,u.xo)("/auth/google",{grant_code:t});if((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201){var a,s,o,r,l;let t=null==e?void 0:null===(s=e.data)||void 0===s?void 0:null===(a=s.data)||void 0===a?void 0:a.user,n=j.get("redirect")||"";localStorage.setItem("token",null==e?void 0:null===(r=e.data)||void 0===r?void 0:null===(o=r.data)||void 0===o?void 0:o.access_token),localStorage.setItem("useremail",null==t?void 0:t.email),localStorage.setItem("user",JSON.stringify(t)),localStorage.setItem("orgId",null==t?void 0:t.current_org),n?b.push(n):(null==t?void 0:t.is_onboarded)?b.push("/client"):b.push("/client/welcome"),(0,p.TH)(null==t?void 0:t.username,"New User Login",null==e?void 0:null===(l=e.data)||void 0===l?void 0:l.message,"success")}}catch(e){console.error("Google Login Error:",e)}finally{_(!1)}},onError:()=>{console.error("Login Failed")}});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.Toaster,{}),(0,s.jsxs)("main",{className:"w-full min-h-screen flex flex-col md:flex-row",children:[(0,s.jsxs)("section",{className:"w-full md:w-[45%] bg-[#F9FAFB] px-[20px] md:px-[40px] py-[20px] hidden lg:block",children:[(0,s.jsx)(o.default,{href:"/",children:(0,s.jsx)(l.default,{src:"/login_logo.svg",alt:"",width:86,height:31})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"w-full flex flex-col gap-[12px] mb-[40px] md:mb-[71px]",children:[(0,s.jsxs)("h1",{className:"w-full text-center mt-[40px] md:mt-[70px] text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]",children:["All Your"," ",(0,s.jsx)("span",{className:"text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px] bg-gradient-to-t from-[#8860F8] to-[#7141F8] bg-clip-text text-transparent",children:"Notifications"})," ","In One App!!!"]}),(0,s.jsx)("p",{className:"w-full text-center text-[12px] md:text-[14px] text-[#344054] font-[400] leading-[18px] md:leading-[21px] px-[20px] md:px-[90px]",children:"Your Central Hub for Real-Time Notifications, Events, and Errors – Stay Connected to Your Infrastructure, Databases, and Servers with Instant Updates."})]}),(0,s.jsx)(l.default,{src:"/login_img.png",alt:"loginImg",width:320,height:320,className:"flex justify-center items-center mx-auto md:w-[420px] md:h-[420px]"})]})]}),(0,s.jsxs)("section",{className:"w-full md:w-[55%] flex flex-col max-w-xs md:max-w-lg mx-auto items-start justify-start pt-[20px] md:pt-0",children:[(0,s.jsx)(o.default,{href:"/",children:(0,s.jsx)(l.default,{src:"/logomobile.svg",alt:"logo_mobile",width:86,height:31,className:"sm:block md:block lg:hidden flex"})}),(0,s.jsxs)("div",{className:"w-full flex flex-col justify-center mt-[60px] md:mt-[80px] items-center gap-[8px] mb-[32px]",children:[(0,s.jsx)("h1",{className:"w-full text-center text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]",children:"Login to Telex"}),(0,s.jsx)("p",{className:"w-full text-center text-[14px] md:text-[16px] text-[#344054] font-[400] leading-[21px] md:leading-[27px]",children:"Welcome back! We've missed you!"})]}),(0,s.jsxs)("form",{onSubmit:I,className:"w-full",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-[16px]",children:[(0,s.jsxs)("div",{className:"w-full flex flex-col gap-[8px] relative",children:[(0,s.jsx)("label",{htmlFor:"email",className:"text-[14px] font-[400] leading-[21px]",children:"Email address"}),(0,s.jsxs)("div",{className:"w-full flex flex-col gap-[2px]",children:[(0,s.jsx)("input",{type:"text",value:e,onChange:e=>{t(e.target.value),w(e=>({...e,email:""}))},placeholder:"Enter your email",className:"w-full text-[14px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border ".concat(h.email?"border-[#F81404]":"border-[#D0D0FD]"," outline-none rounded-md py-[13px] pl-[13px]")}),h.email&&(0,s.jsx)("small",{className:"text-[12px] text-[#F81404]",children:h.email})]})]}),(0,s.jsxs)("div",{className:"w-full flex flex-col gap-[8px] relative",children:[(0,s.jsx)("label",{htmlFor:"password",className:"text-[14px] font-[400] leading-[21px]",children:"Password"}),(0,s.jsxs)("div",{className:"w-full flex flex-col gap-[2px]",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:m?"text":"password",value:a,onChange:e=>{v(e.target.value),w(e=>({...e,password:""}))},placeholder:"Password",className:"w-full text-[14px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border ".concat(h.password?"border-[#F81404]":"border-[#D0D0FD]"," outline-none rounded-md py-[13px] pl-[13px] pr-[40px]")}),(0,s.jsx)("button",{type:"button",onClick:()=>{g(!m)},className:"absolute right-3 top-1/2 transform -translate-y-1/2 focus:outline-none",children:(0,s.jsx)(l.default,{src:m?"/eye_closed.svg":"/eye_open.svg",alt:m?"Hide password":"Show password",width:20,height:20})})]}),h.password&&(0,s.jsx)("small",{className:"text-[12px] text-[#F81404]",children:h.password})]})]})]}),(0,s.jsxs)("div",{className:"mt-[10px] flex justify-between mb-[32px]",children:[(0,s.jsxs)("div",{className:"flex flex-row gap-[4px] items-center",children:[(0,s.jsx)(r.X,{}),(0,s.jsx)("p",{className:"text-[14px] font-[500] leading-[17.64px]",children:"Remember me"})]}),(0,s.jsx)(o.default,{href:"/auth/forgot-password",children:(0,s.jsx)("p",{className:"text-[14px] font-[500] leading-[21px] hover:text-[#7141F8]",children:"Forgot Password?"})})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-[24px]",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-[16px]",children:[(0,s.jsx)(i.z,{type:"submit",variant:"default",className:"py-6 bg-[#7141F8] hover:bg-[#8760f8] text-white",disabled:!!S,children:y?(0,s.jsxs)("span",{className:"flex items-center gap-x-2",children:[(0,s.jsx)("span",{className:"animate-pulse",children:"Logging in..."})," ",(0,s.jsx)(x.Z,{width:"20",height:"40"})]}):(0,s.jsx)("span",{children:"Login"})}),(0,s.jsx)(o.default,{href:"/auth/magiclink",children:(0,s.jsx)(i.z,{type:"submit",variant:"outline",className:"w-full py-6 border border-[#8760f8] text-[#8760f8] bg-white hover:bg-[#7141F8] hover:text-white",children:"Login with magic link"})})]}),(0,s.jsx)("div",{className:"flex flex-col gap-[10px]",children:(0,s.jsx)("div",{className:"border border-[#D0D0FD] rounded-md",children:(0,s.jsxs)("div",{onClick:()=>k(),className:"cursor-pointer flex flex-row gap-[10px] border border-[#D0D0FD] rounded-md justify-center py-[11px]",children:[(0,s.jsx)(l.default,{src:"/google.svg",width:24,height:24,alt:"google"}),(0,s.jsx)("div",{className:"text-[16px] font-[600] leading-[20.16px]",children:S?(0,s.jsxs)("span",{className:"flex items-center gap-x-2",children:[(0,s.jsx)("span",{className:"animate-pulse",children:"Logging in..."}),(0,s.jsx)(x.Z,{width:"20",height:"20",color:"#7141F8"})]}):"Sign in with Google"})]})})}),(0,s.jsx)("div",{className:"flex justify-center items-center",children:(0,s.jsxs)("p",{className:"text-[14px] font-[400] leading-[21px]",children:["Don’t have an account?"," ",(0,s.jsx)(o.default,{href:"/auth/sign-up".concat(j.get("redirect")?"?redirect=".concat(encodeURIComponent(j.get("redirect"))):""),children:(0,s.jsx)("span",{className:"text-[14px] font-[500] leading-[21px] text-[#7141F8] hover:text-[#0A0A0A]",children:"Sign up"})})]})})]})]})]})]})]})}},11492:function(e,t,a){"use strict";a.d(t,{d:function(){return i},z:function(){return d}});var s=a(75376),o=a(32486),r=a(91007),l=a(53447),n=a(58983);let i=(0,l.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=o.forwardRef((e,t)=>{let{className:a,variant:o,size:l,asChild:d=!1,...c}=e,u=d?r.g7:"button";return(0,s.jsx)(u,{className:(0,n.cn)(i({variant:o,size:l,className:a})),ref:t,...c})});d.displayName="Button"},19828:function(e,t,a){"use strict";a.d(t,{X:function(){return i}});var s=a(75376),o=a(32486),r=a(95833),l=a(28780),n=a(58983);let i=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)(r.fC,{ref:t,className:(0,n.cn)("peer h-[15px] w-[15px] shrink-0 rounded-sm border border-[#adadeaf] ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary-500 data-[state=checked]:text-primary-500",a),...o,children:(0,s.jsx)(r.z$,{className:(0,n.cn)("flex items-center justify-center text-current"),children:(0,s.jsx)(l.Z,{className:"h-[9px] w-[9px] text-white",strokeWidth:5})})})});i.displayName=r.fC.displayName},73003:function(e,t,a){"use strict";var s=a(75376);a(32486);var o=a(10983);t.Z=e=>{let{height:t,width:a,color:r}=e;return(0,s.jsx)(o.iT,{height:t||20,width:a||20,color:r||"#fff",visible:!0,ariaLabel:"oval-loading",secondaryColor:r||"#fff",strokeWidth:5,strokeWidthSecondary:5})}},56603:function(e,t,a){"use strict";a.d(t,{Toaster:function(){return g}});var s=a(75376),o=a(32486),r=a(65807),l=a(53447),n=a(22397),i=a(58983);let d=r.zt,c=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)(r.l_,{ref:t,className:(0,i.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",a),...o})});c.displayName=r.l_.displayName;let u=(0,l.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),x=o.forwardRef((e,t)=>{let{className:a,variant:o,...l}=e;return(0,s.jsx)(r.fC,{ref:t,className:(0,i.cn)(u({variant:o}),a),...l})});x.displayName=r.fC.displayName,o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)(r.aU,{ref:t,className:(0,i.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",a),...o})}).displayName=r.aU.displayName;let p=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)(r.x8,{ref:t,className:(0,i.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",a),"toast-close":"",...o,children:(0,s.jsx)(n.Z,{className:"h-4 w-4"})})});p.displayName=r.x8.displayName;let f=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)(r.Dx,{ref:t,className:(0,i.cn)("text-sm font-semibold",a),...o})});f.displayName=r.Dx.displayName;let v=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)(r.dk,{ref:t,className:(0,i.cn)("text-sm opacity-90",a),...o})});v.displayName=r.dk.displayName;var m=a(15125);function g(){let{toasts:e}=(0,m.pm)();return(0,s.jsxs)(d,{children:[e.map(function(e){let{id:t,title:a,description:o,action:r,...l}=e;return(0,s.jsxs)(x,{...l,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[a&&(0,s.jsx)(f,{children:a}),o&&(0,s.jsx)(v,{children:o})]}),r,(0,s.jsx)(p,{})]},t)}),(0,s.jsx)(c,{})]})}},15125:function(e,t,a){"use strict";a.d(t,{pm:function(){return x}});var s=a(32486);let o=0,r=new Map,l=e=>{if(r.has(e))return;let t=setTimeout(()=>{r.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);r.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?l(a):e.toasts.forEach(e=>{l(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},i=[],d={toasts:[]};function c(e){d=n(d,e),i.forEach(e=>{e(d)})}function u(e){let{...t}=e,a=(o=(o+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>c({type:"DISMISS_TOAST",toastId:a});return c({type:"ADD_TOAST",toast:{...t,id:a,open:!0,onOpenChange:e=>{e||s()}}}),{id:a,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function x(){let[e,t]=s.useState(d);return s.useEffect(()=>(i.push(t),()=>{let e=i.indexOf(t);e>-1&&i.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},58983:function(e,t,a){"use strict";a.d(t,{cn:function(){return r},k:function(){return l}});var s=a(89824),o=a(97215);function r(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,o.m6)((0,s.W)(t))}let l=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"},10952:function(e,t,a){"use strict";a.d(t,{Gl:function(){return n},an:function(){return d},jx:function(){return c},xo:function(){return i}});var s=a(20818),o=a(13352),r=a(18648);let l=r.env.NEXT_PUBLIC_BASE_URL;r.env.NEXT_PUBLIC_INTEGRATION_URL;let n=async(e,t)=>{try{return await s.Z.get(l+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var a,o,r;return((null==e?void 0:null===(a=e.response)||void 0===a?void 0:a.status)===401||(null==e?void 0:null===(r=e.response)||void 0===r?void 0:null===(o=r.data)||void 0===o?void 0:o.status_code)===401)&&(localStorage.clear(),window.location.href="/auth/login"),e}},i=async(e,t,a)=>{try{return await s.Z.post(l+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var r,n,i,d,c;return o.Z.error(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(r=n.data)||void 0===r?void 0:r.message),(null==e?void 0:null===(i=e.response)||void 0===i?void 0:i.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},d=async(e,t,a)=>{try{return await s.Z.put(l+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var r,n,i,d,c;return o.Z.error(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(r=n.data)||void 0===r?void 0:r.message),(null==e?void 0:null===(i=e.response)||void 0===i?void 0:i.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},c=async(e,t)=>{try{return await s.Z.delete(l+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var a,r,n,i,d;return o.Z.error(null==e?void 0:null===(r=e.response)||void 0===r?void 0:null===(a=r.data)||void 0===a?void 0:a.message),(null==e?void 0:null===(n=e.response)||void 0===n?void 0:n.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(i=d.data)||void 0===i?void 0:i.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}}},72859:function(e,t,a){"use strict";a.d(t,{TH:function(){return r}});var s=a(20818),o=a(18648);let r=async(e,t,a,r)=>{try{return s.Z.get("".concat(o.env.NEXT_PUBLIC_LOGIN_WEBHOOK_URL,"?event_name=").concat(t,"&message=").concat(a,"&status=").concat(r,"&username=").concat(e))}catch(e){return e}}}},function(e){e.O(0,[4269,8863,7140,6092,4144,5300,5614,2987,5243,9687,7542,2344,1744],function(){return e(e.s=77340)}),_N_E=e.O()}]);