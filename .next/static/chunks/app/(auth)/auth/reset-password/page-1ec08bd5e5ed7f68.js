(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8842],{35861:function(e,s,t){Promise.resolve().then(t.bind(t,30121))},30121:function(e,s,t){"use strict";t.r(s);var l=t(75376),a=t(16669),r=t(39713),n=t(32486),x=t(11492),d=t(47411),o=t(13352),i=t(10952),c=t(73003),p=t(9425),m=t(83945);s.default=function(){let[e,s]=(0,n.useState)(""),[t,f]=(0,n.useState)(""),[u,h]=(0,n.useState)(!1),[w,g]=(0,n.useState)({password:"",confirmPassword:""}),j=(0,d.useRouter)(),[v,N]=(0,n.useState)(!1),b=(0,d.useSearchParams)().get("email"),[y,P]=(0,n.useState)(60),[F,S]=(0,n.useState)(""),[_,C]=(0,n.useState)(!1);(0,n.useEffect)(()=>{if(y<=0)return;let e=setInterval(()=>{P(e=>e-1)},1e3);return()=>clearInterval(e)},[y]);let k=()=>{h(!u)},D=()=>{let s={password:"",confirmPassword:""};return e?e.length<6&&(s.password="Password must be at least 6 characters"):s.password="Password is required",t!==e&&(s.confirmPassword="Passwords do not match"),g(s),!s.password&&!s.confirmPassword},E=async s=>{if(s.preventDefault(),D()){N(!0);let s=await (0,i.xo)("/auth/password-reset/verify",{token:F,new_password:e});(null==s?void 0:s.status)===200||(null==s?void 0:s.status)===201?(o.Z.success(null==s?void 0:s.data.message),j.push("/auth/reset-password-success")):N(!1)}},I=async()=>{let e=await (0,i.xo)("/auth/email-request",{email:b});((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201)&&(P(60),o.Z.success(null==e?void 0:e.data.message)),C(!1)};return(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)("main",{className:"w-full min-h-screen flex flex-col md:flex-row",children:[(0,l.jsxs)("section",{className:"w-full md:w-[45%] bg-[#F9FAFB] px-[20px] md:px-[40px] py-[20px] hidden lg:block",children:[(0,l.jsx)(a.default,{href:"/",children:(0,l.jsx)(r.default,{src:"/login_logo.svg",alt:"",width:86,height:31})}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"w-full flex flex-col gap-[12px] mb-[40px] md:mb-[71px]",children:[(0,l.jsxs)("h1",{className:"w-full text-center mt-[40px] md:mt-[70px] text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]",children:["All Your"," ",(0,l.jsx)("span",{className:"text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px] bg-gradient-to-t from-[#8860F8] to-[#7141F8] bg-clip-text text-transparent",children:"Notifications"})," ","In One App!!!"]}),(0,l.jsx)("p",{className:"w-full text-center text-[12px] md:text-[14px] text-[#344054] font-[400] leading-[18px] md:leading-[21px] px-[20px] md:px-[90px]",children:"Your Central Hub for Real-Time Notifications, Events, and Errors – Stay Connected to Your Infrastructure, Databases, and Servers with Instant Updates."})]}),(0,l.jsx)(r.default,{src:"/login_img.png",alt:"loginImg",width:320,height:320,className:"flex justify-center items-center mx-auto md:w-[420px] md:h-[420px]"})]})]}),(0,l.jsxs)("section",{className:"w-full md:w-[55%] flex flex-col max-w-xs md:max-w-lg mx-auto items-start justify-center pt-[20px] md:pt-[30px]",children:[(0,l.jsx)(a.default,{href:"/",children:(0,l.jsx)(r.default,{src:"/logomobile.svg",alt:"logo_mobile",width:86,height:31,className:"sm:block md:block lg:hidden flex"})}),(0,l.jsxs)("div",{className:"w-full flex flex-col mt-[60px] gap-[8px] mb-[32px]",children:[(0,l.jsx)("h1",{className:"w-full text-center text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]",children:"Reset Password"}),(0,l.jsx)("p",{className:"w-full text-center text-[14px] md:text-[16px] text-[#344054] font-[400] leading-[21px] md:leading-[27px]",children:"Choose a new password for your account"}),(0,l.jsxs)("p",{className:"w-full text-center text-xs text-[#344054] font-[400] leading-[21px] md:leading-[27px]",children:["We have sent a code to your email ",(0,m.of)(b||"")]})]}),(0,l.jsxs)("form",{onSubmit:E,className:"w-full",children:[(0,l.jsxs)("div",{className:"flex flex-col gap-[16px] mb-[32px]",children:[(0,l.jsxs)("div",{className:"w-full flex flex-col gap-[8px] relative",children:[(0,l.jsx)("label",{htmlFor:"password",className:"text-[14px] font-[400] leading-[21px]",children:"Enter code from email"}),(0,l.jsx)(p.Zn,{maxLength:6,className:"flex w-full",value:F,onChange:S,disabled:_,children:[...[0,1,2,3,4,5].map(e=>(0,l.jsx)(p.hf,{children:(0,l.jsx)(p.cY,{index:e})},e))]})]}),(0,l.jsxs)("div",{className:"w-full flex flex-col gap-[8px] relative",children:[(0,l.jsx)("label",{htmlFor:"password",className:"text-[14px] font-[400] leading-[21px]",children:"Enter new password"}),(0,l.jsxs)("div",{className:"w-full flex flex-col gap-[2px]",children:[(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("input",{type:u?"text":"password",value:e,onChange:e=>{s(e.target.value),w.password&&g(e=>({...e,password:""}))},placeholder:"Password",className:"w-full text-[12px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border ".concat(w.password?"border-[#F81404]":"border-[#D0D0FD]"," outline-none rounded-md py-[13px] pl-[13px] pr-[40px]")}),(0,l.jsx)("button",{type:"button",onClick:k,className:"absolute right-3 top-1/2 transform -translate-y-1/2 focus:outline-none",children:(0,l.jsx)(r.default,{src:u?"/eye_closed.svg":"/eye_open.svg",alt:u?"Hide password":"Show password",width:20,height:20})})]}),w.password&&(0,l.jsx)("small",{className:"text-[12px] text-[#F81404]",children:w.password})]})]}),(0,l.jsxs)("div",{className:"w-full flex flex-col gap-[8px] relative",children:[(0,l.jsx)("label",{htmlFor:"confirmPassword",className:"text-[14px] font-[400] leading-[21px]",children:"Confirm new password"}),(0,l.jsxs)("div",{className:"w-full flex flex-col gap-[2px]",children:[(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("input",{type:u?"text":"password",value:t,onChange:e=>{f(e.target.value),w.confirmPassword&&g(e=>({...e,confirmPassword:""}))},placeholder:"Confirm New Password",className:"w-full text-[12px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border ".concat(w.confirmPassword?"border-[#F81404]":"border-[#D0D0FD]"," outline-none rounded-md py-[13px] pl-[13px] pr-[40px]")}),(0,l.jsx)("button",{type:"button",onClick:k,className:"absolute right-3 top-1/2 transform -translate-y-1/2 focus:outline-none",children:(0,l.jsx)(r.default,{src:u?"/eye_closed.svg":"/eye_open.svg",alt:u?"Hide password":"Show password",width:20,height:20})})]}),w.confirmPassword&&(0,l.jsx)("small",{className:"text-[12px] text-[#F81404]",children:w.confirmPassword})]})]})]}),(0,l.jsx)("div",{className:"flex flex-col gap-[24px]",children:(0,l.jsxs)("div",{className:"flex flex-col gap-[16px]",children:[(0,l.jsx)(x.z,{type:"submit",variant:"default",className:"py-6 bg-[#7141F8] hover:bg-[#8760f8] text-white",children:v?(0,l.jsxs)("span",{className:"flex items-center gap-x-2",children:[(0,l.jsx)("span",{className:"animate-pulse",children:"Loading..."})," ",(0,l.jsx)(c.Z,{width:"20",height:"20"})]}):"Reset Password"}),(0,l.jsx)(a.default,{href:"/auth/login",children:(0,l.jsx)(x.z,{type:"submit",variant:"outline",className:"w-full py-6 border border-[#8760f8] text-[#8760f8] bg-white hover:bg-[#7141F8] hover:text-white",children:"Back to Login"})})]})}),(0,l.jsx)("div",{className:"flex flex-col items-center mt-3",children:(0,l.jsxs)("p",{className:"text-xs text-gray-500",children:[y<=0?(0,l.jsx)("span",{className:"cursor-pointer text-underline",onClick:I,children:"Resend code"}):(0,l.jsx)("span",{className:"cursor-disabled",children:"Resend code after"})," ",(0,l.jsxs)("span",{className:"text-[#7141F8]",children:[(0,m.mr)(y)," secs"]})]})})]})]})]})})}}},function(e){e.O(0,[4269,8863,7140,6092,5300,2987,2,1433,7542,2344,1744],function(){return e(e.s=35861)}),_N_E=e.O()}]);