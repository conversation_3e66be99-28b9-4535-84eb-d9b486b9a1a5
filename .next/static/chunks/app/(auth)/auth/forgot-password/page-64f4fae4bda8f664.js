(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[863],{20328:function(e,t,r){Promise.resolve().then(r.bind(r,71635))},39713:function(e,t,r){"use strict";r.d(t,{default:function(){return n.a}});var a=r(74033),n=r.n(a)},16669:function(e,t,r){"use strict";r.d(t,{default:function(){return n.a}});var a=r(6092),n=r.n(a)},47411:function(e,t,r){"use strict";var a=r(13362);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},74033:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return i}});let a=r(60723),n=r(25738),s=r(28863),o=a._(r(44543));function i(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=s.Image},71635:function(e,t,r){"use strict";r.r(t);var a=r(75376),n=r(16669),s=r(39713),o=r(32486),i=r(11492),l=r(47411),d=r(56603),u=r(10952),c=r(13352),f=r(73003);t.default=function(){let[e,t]=(0,o.useState)(""),[r,p]=(0,o.useState)({email:""}),x=(0,l.useRouter)(),[m,v]=(0,o.useState)(!1),g=()=>{let t={email:""};return e?/\S+@\S+\.\S+/.test(e)||(t.email="Invalid email address"):t.email="Email is required",p(t),!t.email},h=async t=>{if(t.preventDefault(),g()){v(!0);let t=await (0,u.xo)("/auth/password-reset",{email:e});(null==t?void 0:t.status)===200||(null==t?void 0:t.status)===201?(c.Z.success(null==t?void 0:t.data.message),x.push("/auth/reset-password?email=".concat(encodeURIComponent(e)))):v(!1)}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.Toaster,{}),(0,a.jsxs)("main",{className:"w-full min-h-screen flex flex-col md:flex-row",children:[(0,a.jsxs)("section",{className:"w-full md:w-[45%] bg-[#F9FAFB] px-[20px] md:px-[40px] py-[20px] hidden lg:block",children:[(0,a.jsx)(n.default,{href:"/",children:(0,a.jsx)(s.default,{src:"/login_logo.svg",alt:"",width:86,height:31})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"w-full flex flex-col gap-[12px] mb-[40px] md:mb-[71px]",children:[(0,a.jsxs)("h1",{className:"w-full text-center mt-[40px] md:mt-[70px] text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]",children:["All Your"," ",(0,a.jsx)("span",{className:"text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px] bg-gradient-to-t from-[#8860F8] to-[#7141F8] bg-clip-text text-transparent",children:"Notifications"})," ","In One App!!!"]}),(0,a.jsx)("p",{className:"w-full text-center text-[12px] md:text-[14px] text-[#344054] font-[400] leading-[18px] md:leading-[21px] px-[20px] md:px-[90px]",children:"Your Central Hub for Real-Time Notifications, Events, and Errors – Stay Connected to Your Infrastructure, Databases, and Servers with Instant Updates."})]}),(0,a.jsx)(s.default,{src:"/login_img.png",alt:"loginImg",width:320,height:320,className:"flex justify-center items-center mx-auto md:w-[420px] md:h-[420px]"})]})]}),(0,a.jsxs)("section",{className:"w-full md:w-[55%] flex flex-col max-w-xs md:max-w-lg mx-auto items-start pt-[20px] md:pt-[30px]",children:[(0,a.jsx)(n.default,{href:"/",children:(0,a.jsx)(s.default,{src:"/logomobile.svg",alt:"logo_mobile",width:86,height:31,className:"sm:block md:block lg:hidden flex"})}),(0,a.jsxs)("div",{className:"w-full flex flex-col justify-center mt-[60px] md:mt-[120px] items-center gap-[8px] mb-[32px]",children:[(0,a.jsx)("h1",{className:"w-full text-center text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]",children:"Forgot Password"}),(0,a.jsx)("p",{className:"w-full text-center text-[14px] md:text-[16px] text-[#344054] font-[400] leading-[21px] md:leading-[27px]",children:"Enter the email you used in creating your account, we will send you instructions on how to reset your password."})]}),(0,a.jsxs)("form",{onSubmit:h,className:"w-full",children:[(0,a.jsx)("div",{className:"flex flex-col gap-[16px] mb-[32px]",children:(0,a.jsxs)("div",{className:"w-full flex flex-col gap-[8px]",children:[(0,a.jsx)("label",{htmlFor:"email",className:"text-[14px] font-[400] leading-[21px]",children:"Email address"}),(0,a.jsxs)("div",{className:"w-full flex flex-col gap-[2px]",children:[(0,a.jsx)("input",{type:"email",value:e,onChange:e=>{t(e.target.value),r.email&&p(e=>({...e,email:""}))},placeholder:"Enter your email",className:"w-full text-[14px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border ".concat(r.email?"border-[#F81404]":"border-[#D0D0FD]"," outline-none rounded-md py-[13px] pl-[13px]")}),r.email&&(0,a.jsx)("small",{className:"text-[12px] text-[#F81404]",children:r.email})]})]})}),(0,a.jsx)("div",{className:"flex flex-col gap-[24px]",children:(0,a.jsxs)("div",{className:"flex flex-col gap-[16px]",children:[(0,a.jsx)(i.z,{type:"submit",variant:"default",className:"py-6 bg-[#7141F8] hover:bg-[#8760f8] text-white",children:m?(0,a.jsxs)("span",{className:"flex items-center gap-x-2",children:[(0,a.jsx)("span",{className:"animate-pulse",children:" Submitting..."})," ",(0,a.jsx)(f.Z,{width:"20",height:"40"})]}):(0,a.jsx)("span",{children:"Submit"})}),(0,a.jsx)(n.default,{href:"/auth/login",children:(0,a.jsx)(i.z,{type:"submit",variant:"outline",className:"w-full py-6 border border-[#8760f8] text-[#8760f8] bg-white hover:bg-[#7141F8] hover:text-white",children:"Back to Login"})})]})})]})]})]})]})}},11492:function(e,t,r){"use strict";r.d(t,{d:function(){return l},z:function(){return d}});var a=r(75376),n=r(32486),s=r(91007),o=r(53447),i=r(58983);let l=(0,o.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,t)=>{let{className:r,variant:n,size:o,asChild:d=!1,...u}=e,c=d?s.g7:"button";return(0,a.jsx)(c,{className:(0,i.cn)(l({variant:n,size:o,className:r})),ref:t,...u})});d.displayName="Button"},73003:function(e,t,r){"use strict";var a=r(75376);r(32486);var n=r(10983);t.Z=e=>{let{height:t,width:r,color:s}=e;return(0,a.jsx)(n.iT,{height:t||20,width:r||20,color:s||"#fff",visible:!0,ariaLabel:"oval-loading",secondaryColor:s||"#fff",strokeWidth:5,strokeWidthSecondary:5})}},56603:function(e,t,r){"use strict";r.d(t,{Toaster:function(){return g}});var a=r(75376),n=r(32486),s=r(65807),o=r(53447),i=r(22397),l=r(58983);let d=s.zt,u=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.l_,{ref:t,className:(0,l.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",r),...n})});u.displayName=s.l_.displayName;let c=(0,o.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),f=n.forwardRef((e,t)=>{let{className:r,variant:n,...o}=e;return(0,a.jsx)(s.fC,{ref:t,className:(0,l.cn)(c({variant:n}),r),...o})});f.displayName=s.fC.displayName,n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.aU,{ref:t,className:(0,l.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",r),...n})}).displayName=s.aU.displayName;let p=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.x8,{ref:t,className:(0,l.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",r),"toast-close":"",...n,children:(0,a.jsx)(i.Z,{className:"h-4 w-4"})})});p.displayName=s.x8.displayName;let x=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.Dx,{ref:t,className:(0,l.cn)("text-sm font-semibold",r),...n})});x.displayName=s.Dx.displayName;let m=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(s.dk,{ref:t,className:(0,l.cn)("text-sm opacity-90",r),...n})});m.displayName=s.dk.displayName;var v=r(15125);function g(){let{toasts:e}=(0,v.pm)();return(0,a.jsxs)(d,{children:[e.map(function(e){let{id:t,title:r,description:n,action:s,...o}=e;return(0,a.jsxs)(f,{...o,children:[(0,a.jsxs)("div",{className:"grid gap-1",children:[r&&(0,a.jsx)(x,{children:r}),n&&(0,a.jsx)(m,{children:n})]}),s,(0,a.jsx)(p,{})]},t)}),(0,a.jsx)(u,{})]})}},15125:function(e,t,r){"use strict";r.d(t,{pm:function(){return f}});var a=r(32486);let n=0,s=new Map,o=e=>{if(s.has(e))return;let t=setTimeout(()=>{s.delete(e),u({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?o(r):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function u(e){d=i(d,e),l.forEach(e=>{e(d)})}function c(e){let{...t}=e,r=(n=(n+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>u({type:"DISMISS_TOAST",toastId:r});return u({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||a()}}}),{id:r,dismiss:a,update:e=>u({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function f(){let[e,t]=a.useState(d);return a.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:c,dismiss:e=>u({type:"DISMISS_TOAST",toastId:e})}}},58983:function(e,t,r){"use strict";r.d(t,{cn:function(){return s},k:function(){return o}});var a=r(89824),n=r(97215);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.m6)((0,a.W)(t))}let o=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"},10952:function(e,t,r){"use strict";r.d(t,{Gl:function(){return i},an:function(){return d},jx:function(){return u},xo:function(){return l}});var a=r(20818),n=r(13352),s=r(18648);let o=s.env.NEXT_PUBLIC_BASE_URL;s.env.NEXT_PUBLIC_INTEGRATION_URL;let i=async(e,t)=>{try{return await a.Z.get(o+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var r,n,s;return((null==e?void 0:null===(r=e.response)||void 0===r?void 0:r.status)===401||(null==e?void 0:null===(s=e.response)||void 0===s?void 0:null===(n=s.data)||void 0===n?void 0:n.status_code)===401)&&(localStorage.clear(),window.location.href="/auth/login"),e}},l=async(e,t,r)=>{try{return await a.Z.post(o+e,t,{headers:{Authorization:"Bearer ".concat(r),"Content-Type":"application/json"}})}catch(e){var s,i,l,d,u;return n.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(s=i.data)||void 0===s?void 0:s.message),(null==e?void 0:null===(l=e.response)||void 0===l?void 0:l.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(u=e.response)||void 0===u?void 0:null===(d=u.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},d=async(e,t,r)=>{try{return await a.Z.put(o+e,t,{headers:{Authorization:"Bearer ".concat(r),"Content-Type":"application/json"}})}catch(e){var s,i,l,d,u;return n.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(s=i.data)||void 0===s?void 0:s.message),(null==e?void 0:null===(l=e.response)||void 0===l?void 0:l.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(u=e.response)||void 0===u?void 0:null===(d=u.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},u=async(e,t)=>{try{return await a.Z.delete(o+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var r,s,i,l,d;return n.Z.error(null==e?void 0:null===(s=e.response)||void 0===s?void 0:null===(r=s.data)||void 0===r?void 0:r.message),(null==e?void 0:null===(i=e.response)||void 0===i?void 0:i.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(l=d.data)||void 0===l?void 0:l.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}}}},function(e){e.O(0,[4269,8863,7140,6092,4144,5300,5614,2987,5243,7542,2344,1744],function(){return e(e.s=20328)}),_N_E=e.O()}]);