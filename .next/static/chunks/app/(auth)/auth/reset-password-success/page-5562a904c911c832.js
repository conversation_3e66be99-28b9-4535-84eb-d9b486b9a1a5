(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3311],{28355:function(e,t,n){Promise.resolve().then(n.bind(n,6248))},39713:function(e,t,n){"use strict";n.d(t,{default:function(){return l.a}});var r=n(74033),l=n.n(r)},16669:function(e,t,n){"use strict";n.d(t,{default:function(){return l.a}});var r=n(6092),l=n.n(r)},74033:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return a},getImageProps:function(){return o}});let r=n(60723),l=n(25738),i=n(28863),s=r._(n(44543));function o(e){let{props:t}=(0,l.getImgProps)(e,{defaultLoader:s.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,n]of Object.entries(t))void 0===n&&delete t[e];return{props:t}}let a=i.Image},6248:function(e,t,n){"use strict";n.r(t);var r=n(75376),l=n(16669),i=n(39713),s=n(11492);t.default=function(){return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("main",{className:"w-full min-h-screen flex flex-col md:flex-row",children:[(0,r.jsxs)("section",{className:"w-full md:w-[45%] bg-[#F9FAFB] px-[20px] md:px-[40px] py-[20px] hidden lg:block",children:[(0,r.jsx)(l.default,{href:"/",children:(0,r.jsx)(i.default,{src:"/login_logo.svg",alt:"",width:86,height:31})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"w-full flex flex-col gap-[12px] mb-[40px] md:mb-[71px]",children:[(0,r.jsxs)("h1",{className:"w-full text-center mt-[40px] md:mt-[70px] text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]",children:["All Your"," ",(0,r.jsx)("span",{className:"text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px] bg-gradient-to-t from-[#8860F8] to-[#7141F8] bg-clip-text text-transparent",children:"Notifications"})," ","In One App!!!"]}),(0,r.jsx)("p",{className:"w-full text-center text-[12px] md:text-[14px] text-[#344054] font-[400] leading-[18px] md:leading-[21px] px-[20px] md:px-[90px]",children:"Your Central Hub for Real-Time Notifications, Events, and Errors – Stay Connected to Your Infrastructure, Databases, and Servers with Instant Updates."})]}),(0,r.jsx)(i.default,{src:"/login_img.png",alt:"loginImg",width:320,height:320,className:"flex justify-center items-center mx-auto md:w-[420px] md:h-[420px]"})]})]}),(0,r.jsxs)("section",{className:"w-full md:w-[55%] flex flex-col max-w-xs md:max-w-lg mx-auto items-center justify-center pt-[20px] md:pt-[30px]",children:[(0,r.jsx)(l.default,{href:"/",children:(0,r.jsx)(i.default,{src:"/logomobile.svg",alt:"logo_mobile",width:86,height:31,className:"sm:block md:block lg:hidden pt-[30px] flex"})}),(0,r.jsxs)("div",{className:"w-full flex flex-col justify-center mt-0 md:mt-[80px] items-center gap-[8px] mb-[32px]",children:[(0,r.jsx)(i.default,{src:"/success_check.webp",alt:"success_check",width:200,height:400,className:"sm:block md:block flex"}),(0,r.jsx)("p",{className:"w-full text-center text-[14px] md:text-[16px] text-[#344054] font-[400] leading-[21px] md:leading-[27px]",children:"Password reset successfully"})]}),(0,r.jsx)("div",{className:" w-full flex flex-col gap-[24px]",children:(0,r.jsx)("div",{className:"flex flex-col gap-[16px]",children:(0,r.jsx)(l.default,{href:"/auth/login",children:(0,r.jsx)(s.z,{type:"submit",variant:"default",className:"w-full py-6 bg-[#7141F8] hover:bg-[#8760f8] text-white",children:"Login"})})})})]})]})})}},11492:function(e,t,n){"use strict";n.d(t,{d:function(){return a},z:function(){return u}});var r=n(75376),l=n(32486),i=n(91007),s=n(53447),o=n(58983);let a=(0,s.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=l.forwardRef((e,t)=>{let{className:n,variant:l,size:s,asChild:u=!1,...c}=e,d=u?i.g7:"button";return(0,r.jsx)(d,{className:(0,o.cn)(a({variant:l,size:s,className:n})),ref:t,...c})});u.displayName="Button"},58983:function(e,t,n){"use strict";n.d(t,{cn:function(){return i},k:function(){return s}});var r=n(89824),l=n(97215);function i(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,l.m6)((0,r.W)(t))}let s=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"},29626:function(e,t,n){"use strict";n.d(t,{F:function(){return i},e:function(){return s}});var r=n(32486);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let n=!1,r=e.map(e=>{let r=l(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():l(e[t],null)}}}}function s(...e){return r.useCallback(i(...e),e)}},91007:function(e,t,n){"use strict";n.d(t,{Z8:function(){return s},g7:function(){return o},sA:function(){return u}});var r=n(32486),l=n(29626),i=n(75376);function s(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){let e,s;let o=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,a=function(e,t){let n={...t};for(let r in t){let l=e[r],i=t[r];/^on[A-Z]/.test(r)?l&&i?n[r]=(...e)=>{let t=i(...e);return l(...e),t}:l&&(n[r]=l):"style"===r?n[r]={...l,...i}:"className"===r&&(n[r]=[l,i].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==r.Fragment&&(a.ref=t?(0,l.F)(t,o):o),r.cloneElement(n,a)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:l,...s}=e,o=r.Children.toArray(l),a=o.find(c);if(a){let e=a.props.children,l=o.map(t=>t!==a?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...s,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,l):null})}return(0,i.jsx)(t,{...s,ref:n,children:l})});return n.displayName=`${e}.Slot`,n}var o=s("Slot"),a=Symbol("radix.slottable");function u(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=a,t}function c(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}},53447:function(e,t,n){"use strict";n.d(t,{j:function(){return s}});var r=n(89824);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=r.W,s=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return i(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:s,defaultVariants:o}=t,a=Object.keys(s).map(e=>{let t=null==n?void 0:n[e],r=null==o?void 0:o[e];if(null===t)return null;let i=l(t)||l(r);return s[e][i]}),u=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return i(e,a,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...l}=t;return Object.entries(l).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...o,...u}[t]):({...o,...u})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}}},function(e){e.O(0,[8863,7140,6092,7542,2344,1744],function(){return e(e.s=28355)}),_N_E=e.O()}]);