(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1492],{90580:function(e,t,l){Promise.resolve().then(l.bind(l,98099))},9824:function(e,t,l){"use strict";l.d(t,{Z:function(){return i}});var a=l(32486);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=function(){for(var e=arguments.length,t=Array(e),l=0;l<e;l++)t[l]=arguments[l];return t.filter((e,t,l)=>!!e&&l.indexOf(e)===t).join(" ")};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a.forwardRef)((e,t)=>{let{color:l="currentColor",size:r=24,strokeWidth:s=2,absoluteStrokeWidth:i,className:d="",children:c,iconNode:u,...x}=e;return(0,a.createElement)("svg",{ref:t,...o,width:r,height:r,stroke:l,strokeWidth:i?24*Number(s)/Number(r):s,className:n("lucide",d),...x},[...u.map(e=>{let[t,l]=e;return(0,a.createElement)(t,l)}),...Array.isArray(c)?c:[c]])}),i=(e,t)=>{let l=(0,a.forwardRef)((l,o)=>{let{className:i,...d}=l;return(0,a.createElement)(s,{ref:o,iconNode:t,className:n("lucide-".concat(r(e)),i),...d})});return l.displayName="".concat(e),l}},98099:function(e,t,l){"use strict";l.r(t);var a=l(75376),r=l(16669),n=l(39713),o=l(32486),s=l(11492),i=l(47411),d=l(19828),c=l(73003),u=l(10952),x=l(22909),p=l(72859);t.default=function(){let[e,t]=(0,o.useState)(""),[l,v]=(0,o.useState)(""),[f,m]=(0,o.useState)(!1),[g,h]=(0,o.useState)(!1),[w,b]=(0,o.useState)(!1),[j,y]=(0,o.useState)({email:"",password:""}),N=(0,i.useRouter)(),S=(0,i.useSearchParams)(),[_,k]=(0,o.useState)(!1),[C,I]=(0,o.useState)(!1),[F,A]=(0,o.useState)(!1),[E,D]=(0,o.useState)(!1);(0,o.useEffect)(()=>{if(new URLSearchParams(window.location.search).get("invite")){let e=window.location.href;localStorage.setItem("postInviteRedirect",e)}},[]),(0,o.useEffect)(()=>{let e=l.length>=6;k(e),I(e),m(!1)},[l]);let L=()=>{let t={email:"",password:""};return e&&""!==e.trim()?/\S+@\S+\.\S+/.test(e)||(t.email="Invalid email address"):t.email="Email is required",l&&""!==l.trim()?C?_||(t.password="Password must have a minimum of 6 characters"):t.password="Password is not valid":t.password="Password is required",y(t),!t.email&&!t.password},T=async t=>{if(t.preventDefault(),L()&&C){A(!0);let t=S.get("redirect")||"",w=await (0,u.xo)("/auth/register",{email:e,password:l});if((null==w?void 0:w.status)===200||(null==w?void 0:w.status)===201){var a,r,n,o,s,i,d,c,x,v,f,m,g,h;localStorage.setItem("token",null==w?void 0:null===(r=w.data)||void 0===r?void 0:null===(a=r.data)||void 0===a?void 0:a.access_token),localStorage.setItem("useremail",null==w?void 0:null===(s=w.data)||void 0===s?void 0:null===(o=s.data)||void 0===o?void 0:null===(n=o.user)||void 0===n?void 0:n.email),localStorage.setItem("user",JSON.stringify(null==w?void 0:null===(d=w.data)||void 0===d?void 0:null===(i=d.data)||void 0===i?void 0:i.user)),localStorage.setItem("orgId",null==w?void 0:null===(v=w.data)||void 0===v?void 0:null===(x=v.data)||void 0===x?void 0:null===(c=x.user)||void 0===c?void 0:c.current_org),t?setTimeout(()=>{N.push(t)},100):setTimeout(()=>{var e,t,l;(null==w?void 0:null===(l=w.data)||void 0===l?void 0:null===(t=l.data)||void 0===t?void 0:null===(e=t.user)||void 0===e?void 0:e.is_onboarded)?N.push("/client"):N.push("/client/welcome")},100),(0,p.TH)(null==w?void 0:null===(g=w.data)||void 0===g?void 0:null===(m=g.data)||void 0===m?void 0:null===(f=m.user)||void 0===f?void 0:f.username,"New User Signup",null==w?void 0:null===(h=w.data)||void 0===h?void 0:h.message,"success")}else A(!1)}},B=(0,x.Nq)({flow:"auth-code",onSuccess:async e=>{D(!0);let{code:t}=e;try{let e=await (0,u.xo)("/auth/google",{grant_code:t});if((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201){var l,a,r,n,o,s,i,d;let t=null==e?void 0:null===(a=e.data)||void 0===a?void 0:null===(l=a.data)||void 0===l?void 0:l.user,c=S.get("redirect")||"";localStorage.setItem("token",null==e?void 0:null===(n=e.data)||void 0===n?void 0:null===(r=n.data)||void 0===r?void 0:r.access_token),localStorage.setItem("useremail",null==t?void 0:t.email),localStorage.setItem("user",JSON.stringify(t)),localStorage.setItem("orgId",null==t?void 0:t.current_org),c?N.push(c):(null==t?void 0:t.is_onboarded)?N.push("/client"):N.push("/client/welcome"),(0,p.TH)(null==e?void 0:null===(i=e.data)||void 0===i?void 0:null===(s=i.data)||void 0===s?void 0:null===(o=s.user)||void 0===o?void 0:o.username,"New User Signup",null==e?void 0:null===(d=e.data)||void 0===d?void 0:d.message,"success")}}catch(e){console.error("Google Login Error:",e)}finally{D(!1)}},onError:()=>{console.error("Login Failed")}});return(0,a.jsxs)("main",{className:"w-full min-h-screen flex flex-col md:flex-row",children:[(0,a.jsxs)("section",{className:"w-full md:w-[45%] bg-[#F9FAFB] px-[20px] md:px-[40px] py-[20px] hidden lg:block",children:[(0,a.jsx)(r.default,{href:"/",children:(0,a.jsx)(n.default,{src:"/login_logo.svg",alt:"",width:86,height:31})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"w-full flex flex-col gap-[12px] mb-[40px] md:mb-[71px]",children:[(0,a.jsxs)("h1",{className:"w-full text-center mt-[40px] md:mt-[70px] text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]",children:["All Your"," ",(0,a.jsx)("span",{className:"text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px] bg-gradient-to-t from-[#8860F8] to-[#7141F8] bg-clip-text text-transparent",children:"Notifications"})," ","In One App!!!"]}),(0,a.jsx)("p",{className:"w-full text-center text-[12px] md:text-[14px] text-[#344054] font-[400] leading-[21px] md:leading-[27px] px-[20px] md:px-[90px]",children:"Your Central Hub for Real-Time Notifications, Events, and Errors – Stay Connected to Your Infrastructure, Databases, and Servers with Instant Updates."})]}),(0,a.jsx)(n.default,{src:"/login_img.png",alt:"loginImg",width:320,height:320,className:"flex justify-center items-center mx-auto md:w-[420px] md:h-[420px]"})]})]}),(0,a.jsxs)("section",{className:"w-full md:w-[55%] flex flex-col max-w-xs md:max-w-lg mx-auto items-start justify-start pt-[20px] md:pt-0 ",children:[(0,a.jsx)(r.default,{href:"/",children:(0,a.jsx)(n.default,{src:"/logomobile.svg",alt:"logo_mobile",width:86,height:31,className:"sm:block md:block lg:hidden flex"})}),(0,a.jsxs)("div",{className:"w-full flex flex-col justify-center mt-[60px] md:mt-[80px] items-center gap-[8px] mb-[32px]",children:[(0,a.jsx)("h1",{className:"w-full text-center text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]",children:"Create A Telex Account"}),(0,a.jsx)("p",{className:"w-full text-center text-[14px] md:text-[16px] text-[#344054] font-[400] leading-[19px] md:leading-[26px] px-[65px]",children:"Welcome! Let's get your profile set up in just a minute."})]}),(0,a.jsxs)("form",{onSubmit:T,className:"w-full pb-[40px]",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-[16px] mb-[16px]",children:[(0,a.jsxs)("div",{className:"w-full flex flex-col gap-[8px] relative",children:[(0,a.jsx)("label",{htmlFor:"email",className:"text-[14px] font-[400] leading-[21px]",children:"Email address"}),(0,a.jsxs)("div",{className:"w-full flex flex-col gap-[2px]",children:[(0,a.jsx)("input",{type:"email",value:e,onChange:e=>{t(e.target.value),j.email&&y(e=>({...e,email:""}))},placeholder:"Enter your email",className:"w-full text-[14px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border ".concat(j.email?"border-[#F81404]":"border-[#D0D0FD]"," outline-none rounded-md py-[13px] pl-[13px]")}),j.email&&(0,a.jsx)("small",{className:"text-[12px] text-[#F81404]",children:j.email})]})]}),(0,a.jsxs)("div",{className:"w-full flex flex-col gap-[8px] relative",children:[(0,a.jsx)("label",{htmlFor:"password",className:"text-[14px] font-[400] leading-[21px]",children:"Password"}),(0,a.jsxs)("div",{className:"w-full flex flex-col gap-[2px]",children:[(0,a.jsxs)("div",{className:"relative ",children:[(0,a.jsx)("input",{type:g?"text":"password",value:l,onChange:e=>{v(e.target.value),j.password&&y(e=>({...e,password:""}))},placeholder:"Password",onFocus:()=>{b(!0)},onBlur:()=>{b(!1)},className:"w-full text-[14px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border ".concat(f?j.password?"border-[#F81404]":"border-[#D0D0FD]":w&&j.password?"border-[#F81404]":"border-[#D0D0FD]"," outline-none rounded-md py-[13px] pl-[13px] pr-[10px]")}),(0,a.jsx)("button",{type:"button",onClick:()=>{h(!g)},className:"absolute right-3 top-1/2 transform -translate-y-1/2 focus:outline-none",children:(0,a.jsx)(n.default,{src:g?"/eye_closed.svg":"/eye_open.svg",alt:g?"Hide password":"Show password",width:20,height:20})})]}),j.password&&(0,a.jsx)("small",{className:"text-[12px] text-[#F81404]",children:j.password})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-[24px]",children:[(0,a.jsx)("div",{className:"flex flex-col gap-[16px]",children:(0,a.jsx)(s.z,{type:"submit",variant:"default",className:"py-6 bg-[#7141F8] hover:bg-[#8760f8] text-white",children:F?(0,a.jsxs)("span",{className:"flex items-center gap-x-2",children:[(0,a.jsx)("span",{className:"animate-pulse",children:"Creating..."})," ",(0,a.jsx)(c.Z,{width:"20",height:"20"})]}):(0,a.jsx)("span",{children:"Create Account"})})}),(0,a.jsx)("div",{className:"flex flex-col gap-[12px]",children:(0,a.jsxs)("div",{className:"flex flex-row gap-[6px] items-center",children:[(0,a.jsx)(d.X,{className:"mt-[2px]"}),(0,a.jsx)("p",{className:"text-[14px] font-[400] leading-[17.64px]",children:"Stay Signed In"})]})}),(0,a.jsx)("div",{className:"border border-[#D0D0FD] rounded-md",children:(0,a.jsxs)("div",{onClick:()=>B(),className:"cursor-pointer flex flex-row gap-[10px] border border-[#D0D0FD] rounded-md justify-center py-[11px]",children:[(0,a.jsx)(n.default,{src:"/google.svg",width:24,height:24,alt:"google"}),(0,a.jsx)("div",{className:"text-[16px] font-[600] leading-[20.16px]",children:E?(0,a.jsxs)("span",{className:"flex items-center gap-x-2",children:[(0,a.jsx)("span",{className:"animate-pulse",children:"Logging in..."}),(0,a.jsx)(c.Z,{width:"20",height:"20",color:"#7141F8"})]}):"Sign up with Google"})]})})]}),(0,a.jsxs)("p",{className:"text-[14px] font-[400] leading-[17.64px] text-center m-2",children:["By Signing up, you agree to our",(0,a.jsx)(r.default,{href:"/terms-of-service",target:"_blank",className:"px-1 font-[500] leading-[21px] text-[#7141F8] hover:text-[#9678e8]",children:"terms of service"}),"and",(0,a.jsx)(r.default,{href:"/policy",target:"_blank",className:"px-1 font-[500] leading-[21px] text-[#7141F8] hover:text-[#9678e8]",children:"privacy policy"})]}),(0,a.jsx)("div",{className:"w-full flex justify-center items-center mt-[16px]",children:(0,a.jsxs)("p",{className:"text-[14px] font-[400] leading-[17.64px] text-center",children:["Already have an account?"," ",(0,a.jsx)(r.default,{href:"/auth/login".concat(S.get("redirect")?"?redirect=".concat(encodeURIComponent(S.get("redirect"))):""),className:"font-[500] leading-[21px] text-[#7141F8] hover:text-[#0A0A0A]",children:"Login"})]})})]})]})]})}},11492:function(e,t,l){"use strict";l.d(t,{d:function(){return i},z:function(){return d}});var a=l(75376),r=l(32486),n=l(91007),o=l(53447),s=l(58983);let i=(0,o.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,t)=>{let{className:l,variant:r,size:o,asChild:d=!1,...c}=e,u=d?n.g7:"button";return(0,a.jsx)(u,{className:(0,s.cn)(i({variant:r,size:o,className:l})),ref:t,...c})});d.displayName="Button"},19828:function(e,t,l){"use strict";l.d(t,{X:function(){return i}});var a=l(75376),r=l(32486),n=l(95833),o=l(28780),s=l(58983);let i=r.forwardRef((e,t)=>{let{className:l,...r}=e;return(0,a.jsx)(n.fC,{ref:t,className:(0,s.cn)("peer h-[15px] w-[15px] shrink-0 rounded-sm border border-[#adadeaf] ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary-500 data-[state=checked]:text-primary-500",l),...r,children:(0,a.jsx)(n.z$,{className:(0,s.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(o.Z,{className:"h-[9px] w-[9px] text-white",strokeWidth:5})})})});i.displayName=n.fC.displayName},73003:function(e,t,l){"use strict";var a=l(75376);l(32486);var r=l(10983);t.Z=e=>{let{height:t,width:l,color:n}=e;return(0,a.jsx)(r.iT,{height:t||20,width:l||20,color:n||"#fff",visible:!0,ariaLabel:"oval-loading",secondaryColor:n||"#fff",strokeWidth:5,strokeWidthSecondary:5})}},58983:function(e,t,l){"use strict";l.d(t,{cn:function(){return n},k:function(){return o}});var a=l(89824),r=l(97215);function n(){for(var e=arguments.length,t=Array(e),l=0;l<e;l++)t[l]=arguments[l];return(0,r.m6)((0,a.W)(t))}let o=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"},10952:function(e,t,l){"use strict";l.d(t,{Gl:function(){return s},an:function(){return d},jx:function(){return c},xo:function(){return i}});var a=l(20818),r=l(13352),n=l(18648);let o=n.env.NEXT_PUBLIC_BASE_URL;n.env.NEXT_PUBLIC_INTEGRATION_URL;let s=async(e,t)=>{try{return await a.Z.get(o+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var l,r,n;return((null==e?void 0:null===(l=e.response)||void 0===l?void 0:l.status)===401||(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(r=n.data)||void 0===r?void 0:r.status_code)===401)&&(localStorage.clear(),window.location.href="/auth/login"),e}},i=async(e,t,l)=>{try{return await a.Z.post(o+e,t,{headers:{Authorization:"Bearer ".concat(l),"Content-Type":"application/json"}})}catch(e){var n,s,i,d,c;return r.Z.error(null==e?void 0:null===(s=e.response)||void 0===s?void 0:null===(n=s.data)||void 0===n?void 0:n.message),(null==e?void 0:null===(i=e.response)||void 0===i?void 0:i.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},d=async(e,t,l)=>{try{return await a.Z.put(o+e,t,{headers:{Authorization:"Bearer ".concat(l),"Content-Type":"application/json"}})}catch(e){var n,s,i,d,c;return r.Z.error(null==e?void 0:null===(s=e.response)||void 0===s?void 0:null===(n=s.data)||void 0===n?void 0:n.message),(null==e?void 0:null===(i=e.response)||void 0===i?void 0:i.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},c=async(e,t)=>{try{return await a.Z.delete(o+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var l,n,s,i,d;return r.Z.error(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(l=n.data)||void 0===l?void 0:l.message),(null==e?void 0:null===(s=e.response)||void 0===s?void 0:s.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(i=d.data)||void 0===i?void 0:i.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}}},72859:function(e,t,l){"use strict";l.d(t,{TH:function(){return n}});var a=l(20818),r=l(18648);let n=async(e,t,l,n)=>{try{return a.Z.get("".concat(r.env.NEXT_PUBLIC_LOGIN_WEBHOOK_URL,"?event_name=").concat(t,"&message=").concat(l,"&status=").concat(n,"&username=").concat(e))}catch(e){return e}}},53447:function(e,t,l){"use strict";l.d(t,{j:function(){return o}});var a=l(89824);let r=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=a.W,o=(e,t)=>l=>{var a;if((null==t?void 0:t.variants)==null)return n(e,null==l?void 0:l.class,null==l?void 0:l.className);let{variants:o,defaultVariants:s}=t,i=Object.keys(o).map(e=>{let t=null==l?void 0:l[e],a=null==s?void 0:s[e];if(null===t)return null;let n=r(t)||r(a);return o[e][n]}),d=l&&Object.entries(l).reduce((e,t)=>{let[l,a]=t;return void 0===a||(e[l]=a),e},{});return n(e,i,null==t?void 0:null===(a=t.compoundVariants)||void 0===a?void 0:a.reduce((e,t)=>{let{class:l,className:a,...r}=t;return Object.entries(r).every(e=>{let[t,l]=e;return Array.isArray(l)?l.includes({...s,...d}[t]):({...s,...d})[t]===l})?[...e,l,a]:e},[]),null==l?void 0:l.class,null==l?void 0:l.className)}}},function(e){e.O(0,[4269,8863,7140,6092,4144,5300,2987,9687,7542,2344,1744],function(){return e(e.s=90580)}),_N_E=e.O()}]);