(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3226],{3897:function(e,t,s){Promise.resolve().then(s.bind(s,51055))},39713:function(e,t,s){"use strict";s.d(t,{default:function(){return a.a}});var n=s(74033),a=s.n(n)},16669:function(e,t,s){"use strict";s.d(t,{default:function(){return a.a}});var n=s(6092),a=s.n(n)},47411:function(e,t,s){"use strict";var n=s(13362);s.o(n,"useParams")&&s.d(t,{useParams:function(){return n.useParams}}),s.o(n,"usePathname")&&s.d(t,{usePathname:function(){return n.usePathname}}),s.o(n,"useRouter")&&s.d(t,{useRouter:function(){return n.useRouter}}),s.o(n,"useSearchParams")&&s.d(t,{useSearchParams:function(){return n.useSearchParams}})},74033:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return c},getImageProps:function(){return i}});let n=s(60723),a=s(25738),l=s(28863),r=n._(s(44543));function i(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:r.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let c=l.Image},51055:function(e,t,s){"use strict";s.r(t);var n=s(75376),a=s(32486),l=s(39713),r=s(16669),i=s(47411);let c=()=>{let e=(0,i.useSearchParams)().get("email");return(0,n.jsxs)("p",{className:"font-normal text-[lg] text-[#344054] text-center",children:["We've just sent an email to ",(0,n.jsx)("strong",{children:e})," with detailed instructions on how to access your account."]})};t.default=()=>{let e="/logomobile.svg";return(0,n.jsxs)("section",{className:"w-full min-h-screen flex flex-col md:flex-row",children:[(0,n.jsxs)("div",{className:"w-full md:w-[45%] bg-[#F9FAFB] px-[20px] md:px-[40px] py-[20px] hidden lg:block",children:[(0,n.jsx)(r.default,{href:"/",children:(0,n.jsx)(l.default,{src:e,alt:"Logo",width:86,height:31})}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"w-full flex flex-col gap-[12px] mb-[40px] md:mb-[71px]",children:[(0,n.jsxs)("h3",{className:"w-full text-center mt-[40px] md:mt-[70px] text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]",children:["All Your"," ",(0,n.jsx)("span",{className:"text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px] bg-gradient-to-t from-[#8860F8] to-[#7141F8] bg-clip-text text-transparent",children:"Notifications"})," ","In One App!!!"]}),(0,n.jsx)("p",{className:"w-full text-center text-[12px] md:text-[14px] text-[#344054] font-[400] leading-[18px] md:leading-[21px] px-[20px] md:px-[90px]",children:"Your Central Hub for Real-Time Notifications, Events, and Errors – Stay Connected to Your Infrastructure, Databases, and Servers with Instant Updates."})]}),(0,n.jsx)(l.default,{className:"flex justify-center items-center mx-auto md:w-[420px] md:h-[420px]",src:"/login_img.svg",alt:"Logo",width:320,height:320})]})]}),(0,n.jsx)("div",{className:"w-full md:w-[55%] flex flex-col max-w-xs md:max-w-lg mx-auto items-center justify-center pt-[20px] md:pt-[30px]",children:(0,n.jsxs)("main",{className:"flex flex-col gap-6 mx-auto md:mx-auto items-center",children:[(0,n.jsx)(r.default,{href:"/",children:(0,n.jsx)(l.default,{className:"h-8 md:hidden mt-5 ",src:e,alt:"Logo",width:100,height:100})}),(0,n.jsxs)("div",{className:"flex flex-col gap-[32px] items-center",children:[(0,n.jsx)("h3",{className:"font-semibold text-2xl text-[#1D2939] text-center",children:"Awesome! mail sent."}),(0,n.jsx)(l.default,{src:"/success.png",alt:"success",width:120,height:120}),(0,n.jsx)(a.Suspense,{fallback:(0,n.jsx)("p",{children:"Loading email..."}),children:(0,n.jsx)(c,{})})]})]})})]})}}},function(e){e.O(0,[8863,6092,7542,2344,1744],function(){return e(e.s=3897)}),_N_E=e.O()}]);