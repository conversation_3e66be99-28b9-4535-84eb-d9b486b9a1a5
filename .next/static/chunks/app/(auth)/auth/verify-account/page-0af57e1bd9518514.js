(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1371],{56083:function(e,t,n){Promise.resolve().then(n.bind(n,3802))},74383:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Dot",[["circle",{cx:"12.1",cy:"12.1",r:"1",key:"18d7e5"}]])},39713:function(e,t,n){"use strict";n.d(t,{default:function(){return a.a}});var r=n(74033),a=n.n(r)},16669:function(e,t,n){"use strict";n.d(t,{default:function(){return a.a}});var r=n(6092),a=n.n(r)},47411:function(e,t,n){"use strict";var r=n(13362);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},74033:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return i},getImageProps:function(){return o}});let r=n(60723),a=n(25738),l=n(28863),s=r._(n(44543));function o(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:s.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,n]of Object.entries(t))void 0===n&&delete t[e];return{props:t}}let i=l.Image},3802:function(e,t,n){"use strict";n.r(t);var r=n(75376),a=n(16669),l=n(39713),s=n(32486),o=n(11492),i=n(47411),u=n(56603),c=n(9425),d=n(83945),p=n(73003),f=n(13352),m=n(10952);t.default=function(){let e=(0,i.useRouter)(),[t,n]=(0,s.useState)(!1),[x,h]=(0,s.useState)(60),[g,v]=(0,s.useState)(""),[b,w]=(0,s.useState)(!1),y=(0,i.useSearchParams)().get("email");(0,s.useEffect)(()=>{if(x<=0)return;let e=setInterval(()=>{h(e=>e-1)},1e3);return()=>clearInterval(e)},[x]);let S=async()=>{if(""===g||(null==g?void 0:g.length)<6){f.Z.error("OTP inputs cannot be empty");return}w(!0);let t=await (0,m.xo)("/auth/email-request/verify",{token:g});if((null==t?void 0:t.status)===200||(null==t?void 0:t.status)===201){var n;f.Z.success(null==t?void 0:null===(n=t.data)||void 0===n?void 0:n.message),e.push("/auth/login")}w(!1)},j=async()=>{let e=await (0,m.xo)("/auth/email-request",{email:y});((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201)&&(h(60),f.Z.success(null==e?void 0:e.data.message)),n(!1)};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.Toaster,{}),(0,r.jsxs)("main",{className:"w-full min-h-screen flex flex-col md:flex-row",children:[(0,r.jsxs)("section",{className:"w-full md:w-[45%] bg-[#F9FAFB] px-[20px] md:px-[40px] py-[20px] hidden lg:block",children:[(0,r.jsx)(a.default,{href:"/",children:(0,r.jsx)(l.default,{src:"/login_logo.svg",alt:"",width:86,height:31})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"w-full flex flex-col gap-[12px] mb-[40px] md:mb-[71px]",children:[(0,r.jsxs)("h1",{className:"w-full text-center mt-[40px] md:mt-[70px] text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]",children:["All Your"," ",(0,r.jsx)("span",{className:"text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px] bg-gradient-to-t from-[#8860F8] to-[#7141F8] bg-clip-text text-transparent",children:"Notifications"})," ","In One App!!!"]}),(0,r.jsx)("p",{className:"w-full text-center text-[12px] md:text-[14px] text-[#344054] font-[400] leading-[18px] md:leading-[21px] px-[20px] md:px-[90px]",children:"Your Central Hub for Real-Time Notifications, Events, and Errors – Stay Connected to Your Infrastructure, Databases, and Servers with Instant Updates."})]}),(0,r.jsx)(l.default,{src:"/login_img.png",alt:"loginImg",width:320,height:320,className:"flex justify-center items-center mx-auto md:w-[420px] md:h-[420px]"})]})]}),(0,r.jsxs)("section",{className:"w-full md:w-[55%] flex flex-col max-w-xs md:max-w-lg mx-auto items-start justify-start pt-[20px] md:pt-0",children:[(0,r.jsx)(a.default,{href:"/",children:(0,r.jsx)(l.default,{src:"/logomobile.svg",alt:"logo_mobile",width:86,height:31,className:"sm:block md:block lg:hidden flex"})}),(0,r.jsxs)("div",{"aria-labelledby":"dialog-title","aria-describedby":"dialog-description",className:"flex w-full flex-col items-center gap-5 sm:max-w-[425px] mx-auto mt-[60px] md:mt-[130px]",children:[(0,r.jsx)("h4",{className:"w-full text-center text-xl font-bold text-[#0F172A]",children:"Verify your Telex Account"}),(0,r.jsxs)("div",{className:"flex flex-col items-center text-[#0F172A]",children:[(0,r.jsxs)("p",{className:"text-sm font-medium text-[#0F172A] mb-2",children:["We have sent a code to your email ",(0,d.of)(y||"")]}),(0,r.jsx)("div",{className:"flex flex-col items-center text-base",children:(0,r.jsx)("span",{className:"text-xs",children:"check your spam if you do not recive the email"})})]}),(0,r.jsx)(c.Zn,{maxLength:6,className:"flex w-full",value:g,onChange:v,disabled:t,children:[...[0,1,2,3,4,5].map(e=>(0,r.jsx)(c.hf,{children:(0,r.jsx)(c.cY,{index:e})},e))]}),(0,r.jsx)(o.z,{type:"submit",variant:"default",className:"w-full sm:w-[300px] py-6 bg-[#7141F8] hover:bg-[#8760f8] text-white",onClick:S,children:b?(0,r.jsxs)("span",{className:"flex items-center gap-x-2",children:[(0,r.jsx)("span",{className:"animate-pulse",children:"Verifying..."})," ",(0,r.jsx)(p.Z,{width:"20",height:"20"})]}):"Verify Account"}),(0,r.jsx)("div",{className:"flex flex-col items-center",children:(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:["Didn't receive the code?"," ",x<=0?(0,r.jsx)("span",{className:"cursor-pointer text-[#7141F8]",onClick:j,children:"resend"}):(0,r.jsx)("span",{className:"cursor-pointer text-[#7141F8]",children:(0,d.mr)(x)})]})}),(0,r.jsx)("p",{className:"text-center text-xs text-gray-500",children:"We would process your data as set forth in our Terms of Use, Privacy Policy and Data Processing Agreement"})]})]})]})]})}},56603:function(e,t,n){"use strict";n.d(t,{Toaster:function(){return g}});var r=n(75376),a=n(32486),l=n(65807),s=n(53447),o=n(22397),i=n(58983);let u=l.zt,c=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)(l.l_,{ref:t,className:(0,i.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",n),...a})});c.displayName=l.l_.displayName;let d=(0,s.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),p=a.forwardRef((e,t)=>{let{className:n,variant:a,...s}=e;return(0,r.jsx)(l.fC,{ref:t,className:(0,i.cn)(d({variant:a}),n),...s})});p.displayName=l.fC.displayName,a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)(l.aU,{ref:t,className:(0,i.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",n),...a})}).displayName=l.aU.displayName;let f=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)(l.x8,{ref:t,className:(0,i.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",n),"toast-close":"",...a,children:(0,r.jsx)(o.Z,{className:"h-4 w-4"})})});f.displayName=l.x8.displayName;let m=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)(l.Dx,{ref:t,className:(0,i.cn)("text-sm font-semibold",n),...a})});m.displayName=l.Dx.displayName;let x=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)(l.dk,{ref:t,className:(0,i.cn)("text-sm opacity-90",n),...a})});x.displayName=l.dk.displayName;var h=n(15125);function g(){let{toasts:e}=(0,h.pm)();return(0,r.jsxs)(u,{children:[e.map(function(e){let{id:t,title:n,description:a,action:l,...s}=e;return(0,r.jsxs)(p,{...s,children:[(0,r.jsxs)("div",{className:"grid gap-1",children:[n&&(0,r.jsx)(m,{children:n}),a&&(0,r.jsx)(x,{children:a})]}),l,(0,r.jsx)(f,{})]},t)}),(0,r.jsx)(c,{})]})}},15125:function(e,t,n){"use strict";n.d(t,{pm:function(){return p}});var r=n(32486);let a=0,l=new Map,s=e=>{if(l.has(e))return;let t=setTimeout(()=>{l.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);l.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:n}=t;return n?s(n):e.toasts.forEach(e=>{s(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===n||void 0===n?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},i=[],u={toasts:[]};function c(e){u=o(u,e),i.forEach(e=>{e(u)})}function d(e){let{...t}=e,n=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>c({type:"DISMISS_TOAST",toastId:n});return c({type:"ADD_TOAST",toast:{...t,id:n,open:!0,onOpenChange:e=>{e||r()}}}),{id:n,dismiss:r,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:n}})}}function p(){let[e,t]=r.useState(u);return r.useEffect(()=>(i.push(t),()=>{let e=i.indexOf(t);e>-1&&i.splice(e,1)}),[e]),{...e,toast:d,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},43426:function(e,t,n){"use strict";n.d(t,{VM:function(){return m},uZ:function(){return x}});var r=n(32486),a=Object.defineProperty,l=Object.defineProperties,s=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,u=Object.prototype.propertyIsEnumerable,c=(e,t,n)=>t in e?a(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,d=(e,t)=>{for(var n in t||(t={}))i.call(t,n)&&c(e,n,t[n]);if(o)for(var n of o(t))u.call(t,n)&&c(e,n,t[n]);return e},p=(e,t)=>l(e,s(t)),f=(e,t)=>{var n={};for(var r in e)i.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&o)for(var r of o(e))0>t.indexOf(r)&&u.call(e,r)&&(n[r]=e[r]);return n},m=r.createContext({}),x=r.forwardRef((e,t)=>{let n;var a,l,s,o,i,{value:u,onChange:c,maxLength:x,textAlign:v="left",pattern:b,placeholder:w,inputMode:y="numeric",onComplete:S,pushPasswordManagerStrategy:j="increase-width",pasteTransformer:E,containerClassName:N,noScriptCSSFallback:P=g,render:T,children:C}=e,k=f(e,["value","onChange","maxLength","textAlign","pattern","placeholder","inputMode","onComplete","pushPasswordManagerStrategy","pasteTransformer","containerClassName","noScriptCSSFallback","render","children"]);let[A,M]=r.useState("string"==typeof k.defaultValue?k.defaultValue:""),_=null!=u?u:A,O=(n=r.useRef(),r.useEffect(()=>{n.current=_}),n.current),D=r.useCallback(e=>{null==c||c(e),M(e)},[c]),R=r.useMemo(()=>b?"string"==typeof b?new RegExp(b):b:null,[b]),I=r.useRef(null),F=r.useRef(null),W=r.useRef({value:_,onChange:D,isIOS:"undefined"!=typeof window&&(null==(l=null==(a=null==window?void 0:window.CSS)?void 0:a.supports)?void 0:l.call(a,"-webkit-touch-callout","none"))}),B=r.useRef({prev:[null==(s=I.current)?void 0:s.selectionStart,null==(o=I.current)?void 0:o.selectionEnd,null==(i=I.current)?void 0:i.selectionDirection]});r.useImperativeHandle(t,()=>I.current,[]),r.useEffect(()=>{let e=I.current,t=F.current;if(!e||!t)return;function n(){if(document.activeElement!==e){U(null),$(null);return}let t=e.selectionStart,n=e.selectionEnd,r=e.selectionDirection,a=e.maxLength,l=e.value,s=B.current.prev,o=-1,i=-1,u;if(0!==l.length&&null!==t&&null!==n){let e=t===n,r=t===l.length&&l.length<a;if(e&&!r){if(0===t)o=0,i=1,u="forward";else if(t===a)o=t-1,i=t,u="backward";else if(a>1&&l.length>1){let e=0;if(null!==s[0]&&null!==s[1]){u=t<s[1]?"backward":"forward";let n=s[0]===s[1]&&s[0]<a;"backward"!==u||n||(e=-1)}o=e+t,i=e+t+1}}-1!==o&&-1!==i&&o!==i&&I.current.setSelectionRange(o,i,u)}let c=-1!==o?o:t,d=-1!==i?i:n,p=null!=u?u:r;U(c),$(d),B.current.prev=[c,d,p]}if(W.current.value!==e.value&&W.current.onChange(e.value),B.current.prev=[e.selectionStart,e.selectionEnd,e.selectionDirection],document.addEventListener("selectionchange",n,{capture:!0}),n(),document.activeElement===e&&z(!0),!document.getElementById("input-otp-style")){let e=document.createElement("style");if(e.id="input-otp-style",document.head.appendChild(e),e.sheet){let t="background: transparent !important; color: transparent !important; border-color: transparent !important; opacity: 0 !important; box-shadow: none !important; -webkit-box-shadow: none !important; -webkit-text-fill-color: transparent !important;";h(e.sheet,"[data-input-otp]::selection { background: transparent !important; color: transparent !important; }"),h(e.sheet,`[data-input-otp]:autofill { ${t} }`),h(e.sheet,`[data-input-otp]:-webkit-autofill { ${t} }`),h(e.sheet,"@supports (-webkit-touch-callout: none) { [data-input-otp] { letter-spacing: -.6em !important; font-weight: 100 !important; font-stretch: ultra-condensed; font-optical-sizing: none !important; left: -1px !important; right: 1px !important; } }"),h(e.sheet,"[data-input-otp] + * { pointer-events: all !important; }")}}let r=()=>{t&&t.style.setProperty("--root-height",`${e.clientHeight}px`)};r();let a=new ResizeObserver(r);return a.observe(e),()=>{document.removeEventListener("selectionchange",n,{capture:!0}),a.disconnect()}},[]);let[V,H]=r.useState(!1),[L,z]=r.useState(!1),[Z,U]=r.useState(null),[G,$]=r.useState(null);r.useEffect(()=>{var e;setTimeout(e=()=>{var e,t,n,r;null==(e=I.current)||e.dispatchEvent(new Event("input"));let a=null==(t=I.current)?void 0:t.selectionStart,l=null==(n=I.current)?void 0:n.selectionEnd,s=null==(r=I.current)?void 0:r.selectionDirection;null!==a&&null!==l&&(U(a),$(l),B.current.prev=[a,l,s])},0),setTimeout(e,10),setTimeout(e,50)},[_,L]),r.useEffect(()=>{void 0!==O&&_!==O&&O.length<x&&_.length===x&&(null==S||S(_))},[x,S,O,_]);let Y=function({containerRef:e,inputRef:t,pushPasswordManagerStrategy:n,isFocused:a}){let[l,s]=r.useState(!1),[o,i]=r.useState(!1),[u,c]=r.useState(!1),d=r.useMemo(()=>"none"!==n&&("increase-width"===n||"experimental-no-flickering"===n)&&l&&o,[l,o,n]),p=r.useCallback(()=>{let r=e.current,a=t.current;if(!r||!a||u||"none"===n)return;let l=r.getBoundingClientRect().left+r.offsetWidth,o=r.getBoundingClientRect().top+r.offsetHeight/2;0===document.querySelectorAll('[data-lastpass-icon-root],com-1password-button,[data-dashlanecreated],[style$="2147483647 !important;"]').length&&document.elementFromPoint(l-18,o)===r||(s(!0),c(!0))},[e,t,u,n]);return r.useEffect(()=>{let t=e.current;if(!t||"none"===n)return;function r(){i(window.innerWidth-t.getBoundingClientRect().right>=40)}r();let a=setInterval(r,1e3);return()=>{clearInterval(a)}},[e,n]),r.useEffect(()=>{let e=a||document.activeElement===t.current;if("none"===n||!e)return;let r=setTimeout(p,0),l=setTimeout(p,2e3),s=setTimeout(p,5e3),o=setTimeout(()=>{c(!0)},6e3);return()=>{clearTimeout(r),clearTimeout(l),clearTimeout(s),clearTimeout(o)}},[t,a,n,p]),{hasPWMBadge:l,willPushPWMBadge:d,PWM_BADGE_SPACE_WIDTH:"40px"}}({containerRef:F,inputRef:I,pushPasswordManagerStrategy:j,isFocused:L}),q=r.useCallback(e=>{let t=e.currentTarget.value.slice(0,x);if(t.length>0&&R&&!R.test(t)){e.preventDefault();return}"string"==typeof O&&t.length<O.length&&document.dispatchEvent(new Event("selectionchange")),D(t)},[x,D,O,R]),X=r.useCallback(()=>{var e;if(I.current){let t=Math.min(I.current.value.length,x-1),n=I.current.value.length;null==(e=I.current)||e.setSelectionRange(t,n),U(t),$(n)}z(!0)},[x]),J=r.useCallback(e=>{var t,n;let r=I.current;if(!E&&(!W.current.isIOS||!e.clipboardData||!r))return;let a=e.clipboardData.getData("text/plain"),l=E?E(a):a;e.preventDefault();let s=null==(t=I.current)?void 0:t.selectionStart,o=null==(n=I.current)?void 0:n.selectionEnd,i=(s!==o?_.slice(0,s)+l+_.slice(o):_.slice(0,s)+l+_.slice(s)).slice(0,x);if(i.length>0&&R&&!R.test(i))return;r.value=i,D(i);let u=Math.min(i.length,x-1),c=i.length;r.setSelectionRange(u,c),U(u),$(c)},[x,D,R,_]),K=r.useMemo(()=>({position:"relative",cursor:k.disabled?"default":"text",userSelect:"none",WebkitUserSelect:"none",pointerEvents:"none"}),[k.disabled]),Q=r.useMemo(()=>({position:"absolute",inset:0,width:Y.willPushPWMBadge?`calc(100% + ${Y.PWM_BADGE_SPACE_WIDTH})`:"100%",clipPath:Y.willPushPWMBadge?`inset(0 ${Y.PWM_BADGE_SPACE_WIDTH} 0 0)`:void 0,height:"100%",display:"flex",textAlign:v,opacity:"1",color:"transparent",pointerEvents:"all",background:"transparent",caretColor:"transparent",border:"0 solid transparent",outline:"0 solid transparent",boxShadow:"none",lineHeight:"1",letterSpacing:"-.5em",fontSize:"var(--root-height)",fontFamily:"monospace",fontVariantNumeric:"tabular-nums"}),[Y.PWM_BADGE_SPACE_WIDTH,Y.willPushPWMBadge,v]),ee=r.useMemo(()=>r.createElement("input",p(d({autoComplete:k.autoComplete||"one-time-code"},k),{"data-input-otp":!0,"data-input-otp-placeholder-shown":0===_.length||void 0,"data-input-otp-mss":Z,"data-input-otp-mse":G,inputMode:y,pattern:null==R?void 0:R.source,"aria-placeholder":w,style:Q,maxLength:x,value:_,ref:I,onPaste:e=>{var t;J(e),null==(t=k.onPaste)||t.call(k,e)},onChange:q,onMouseOver:e=>{var t;H(!0),null==(t=k.onMouseOver)||t.call(k,e)},onMouseLeave:e=>{var t;H(!1),null==(t=k.onMouseLeave)||t.call(k,e)},onFocus:e=>{var t;X(),null==(t=k.onFocus)||t.call(k,e)},onBlur:e=>{var t;z(!1),null==(t=k.onBlur)||t.call(k,e)}})),[q,X,J,y,Q,x,G,Z,k,null==R?void 0:R.source,_]),et=r.useMemo(()=>({slots:Array.from({length:x}).map((e,t)=>{var n;let r=L&&null!==Z&&null!==G&&(Z===G&&t===Z||t>=Z&&t<G),a=void 0!==_[t]?_[t]:null;return{char:a,placeholderChar:void 0!==_[0]?null:null!=(n=null==w?void 0:w[t])?n:null,isActive:r,hasFakeCaret:r&&null===a}}),isFocused:L,isHovering:!k.disabled&&V}),[L,V,x,G,Z,k.disabled,_]),en=r.useMemo(()=>T?T(et):r.createElement(m.Provider,{value:et},C),[C,et,T]);return r.createElement(r.Fragment,null,null!==P&&r.createElement("noscript",null,r.createElement("style",null,P)),r.createElement("div",{ref:F,"data-input-otp-container":!0,style:K,className:N},en,r.createElement("div",{style:{position:"absolute",inset:0,pointerEvents:"none"}},ee)))});function h(e,t){try{e.insertRule(t)}catch(e){console.error("input-otp could not insert CSS rule:",t)}}x.displayName="Input";var g=`
[data-input-otp] {
  --nojs-bg: white !important;
  --nojs-fg: black !important;

  background-color: var(--nojs-bg) !important;
  color: var(--nojs-fg) !important;
  caret-color: var(--nojs-fg) !important;
  letter-spacing: .25em !important;
  text-align: center !important;
  border: 1px solid var(--nojs-fg) !important;
  border-radius: 4px !important;
  width: 100% !important;
}
@media (prefers-color-scheme: dark) {
  [data-input-otp] {
    --nojs-bg: black !important;
    --nojs-fg: white !important;
  }
}`}},function(e){e.O(0,[4269,8863,7140,6092,4144,5300,5614,2987,5243,1433,7542,2344,1744],function(){return e(e.s=56083)}),_N_E=e.O()}]);