(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5694],{56675:function(e,l,s){Promise.resolve().then(s.t.bind(s,28863,23)),Promise.resolve().then(s.t.bind(s,6092,23)),Promise.resolve().then(s.bind(s,72645))},72645:function(e,l,s){"use strict";var n=s(75376),o=s(32486),t=s(74980);s(6389),l.default=e=>{let{subtext:l}=e,[s,r]=(0,o.useState)({fullName:"",profession:"",phoneNumber:"",email:""}),a=e=>{r({...s,[e.target.name]:e.target.value})};return(0,n.jsxs)("div",{className:"space-y-10 bg-[#f9f8fe] m-auto sm:min-w-[600px] w-full px-4",children:[(0,n.jsx)(t.Ix,{}),(0,n.jsxs)("div",{className:"flex items-center flex-col space-y-4 text-center w-[95%] md:w-[70%] mx-auto",children:[(0,n.jsx)("h1",{className:"text-2xl md:text-3xl lg:text-4xl font-semibold",children:"Get Started with Telex"}),(0,n.jsx)("p",{className:"text-[#6D7482] font-semibold w-[70%] text-sm sm:text-lg",children:l})]}),(0,n.jsx)("div",{className:"w-full sm:w-[70%] bg-white m-auto p-8 md:p-16 rounded-lg container",children:(0,n.jsx)("form",{onSubmit:e=>{e.preventDefault();let{fullName:l,profession:n,phoneNumber:o,email:a}=s;if(!l||!n||!o||!a){t.Am.error("Please fill in all fields!",{position:"top-right",autoClose:2e3});return}t.Am.success("Form submitted successfully!",{position:"top-right",autoClose:2e3}),r({fullName:"",profession:"",phoneNumber:"",email:""})},className:"w-full space-y-10",children:(0,n.jsxs)("div",{className:"space-y-10 w-full",children:[(0,n.jsxs)("div",{className:"space-y-2 w-full",children:[(0,n.jsx)("label",{htmlFor:"fullName",className:"font-semibold",children:"Full Name"}),(0,n.jsx)("input",{type:"text",name:"fullName",id:"fullName",placeholder:"Your Name",className:"w-full border border-[#E0E3E7] py-3 px-4 rounded-md outline-none",value:s.fullName,onChange:a})]}),(0,n.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,n.jsx)("label",{htmlFor:"profession",className:"font-semibold",children:"Profession"}),(0,n.jsx)("input",{type:"text",name:"profession",id:"profession",placeholder:"e.g Designer, Filmmaker, etc.",className:"w-full border border-[#E0E3E7] py-3 px-4 rounded-md outline-none",value:s.profession,onChange:a})]}),(0,n.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,n.jsx)("label",{htmlFor:"phoneNumber",className:"font-semibold",children:"Phone Number"}),(0,n.jsx)("input",{type:"number",name:"phoneNumber",id:"phoneNumber",placeholder:"Your Phone Number",className:"w-full border border-[#E0E3E7] py-3 px-4 rounded-md outline-none",value:s.phoneNumber,onChange:a})]}),(0,n.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,n.jsx)("label",{htmlFor:"email",className:"font-semibold",children:"Email"}),(0,n.jsx)("input",{type:"email",name:"email",id:"email",placeholder:"<EMAIL>",className:"w-full border border-[#E0E3E7] py-3 px-4 rounded-md outline-none",value:s.email,onChange:a})]}),(0,n.jsx)("button",{type:"submit",className:"border-[#2A2B67] mt-8 bg-[#2A2B67] text-white hover:bg-white hover:text-[#2A2B67] w-full border py-3 px-4 rounded-md outline-none transition-all duration-500 font-semibold",children:"Get Demo"})]})})})]})}},6389:function(){},89824:function(e,l,s){"use strict";function n(){for(var e,l,s=0,n="",o=arguments.length;s<o;s++)(e=arguments[s])&&(l=function e(l){var s,n,o="";if("string"==typeof l||"number"==typeof l)o+=l;else if("object"==typeof l){if(Array.isArray(l)){var t=l.length;for(s=0;s<t;s++)l[s]&&(n=e(l[s]))&&(o&&(o+=" "),o+=n)}else for(n in l)l[n]&&(o&&(o+=" "),o+=n)}return o}(e))&&(n&&(n+=" "),n+=l);return n}s.d(l,{W:function(){return n}}),l.Z=n}},function(e){e.O(0,[6383,8863,6092,4980,7542,2344,1744],function(){return e(e.s=56675)}),_N_E=e.O()}]);