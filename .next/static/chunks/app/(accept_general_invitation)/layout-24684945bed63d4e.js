(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8374],{18989:function(e,t,r){Promise.resolve().then(r.bind(r,22909)),Promise.resolve().then(r.bind(r,56603))},22909:function(e,t,r){"use strict";r.d(t,{GoogleOAuthProvider:function(){return s},Nq:function(){return a}});var o=r(32486);let n=(0,o.createContext)(null);function s(e){let{clientId:t,nonce:r,onScriptLoadSuccess:s,onScriptLoadError:a,children:i}=e,u=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{nonce:t,onScriptLoadSuccess:r,onScriptLoadError:n}=e,[s,a]=(0,o.useState)(!1),i=(0,o.useRef)(r);i.current=r;let u=(0,o.useRef)(n);return u.current=n,(0,o.useEffect)(()=>{let e=document.createElement("script");return e.src="https://accounts.google.com/gsi/client",e.async=!0,e.defer=!0,e.nonce=t,e.onload=()=>{var e;a(!0),null===(e=i.current)||void 0===e||e.call(i)},e.onerror=()=>{var e;a(!1),null===(e=u.current)||void 0===e||e.call(u)},document.body.appendChild(e),()=>{document.body.removeChild(e)}},[t]),s}({nonce:r,onScriptLoadSuccess:s,onScriptLoadError:a}),l=(0,o.useMemo)(()=>({clientId:t,scriptLoadedSuccessfully:u}),[t,u]);return o.createElement(n.Provider,{value:l},i)}function a(e){let{flow:t="implicit",scope:r="",onSuccess:s,onError:a,onNonOAuthError:i,overrideScope:u,state:l,...d}=e,{clientId:c,scriptLoadedSuccessfully:f}=function(){let e=(0,o.useContext)(n);if(!e)throw Error("Google OAuth components must be used within GoogleOAuthProvider");return e}(),p=(0,o.useRef)(),v=(0,o.useRef)(s);v.current=s;let m=(0,o.useRef)(a);m.current=a;let g=(0,o.useRef)(i);g.current=i,(0,o.useEffect)(()=>{var e,o;if(!f)return;let n="implicit"===t?"initTokenClient":"initCodeClient",s=null===(o=null===(e=null==window?void 0:window.google)||void 0===e?void 0:e.accounts)||void 0===o?void 0:o.oauth2[n]({client_id:c,scope:u?r:"openid profile email ".concat(r),callback:e=>{var t,r;if(e.error)return null===(t=m.current)||void 0===t?void 0:t.call(m,e);null===(r=v.current)||void 0===r||r.call(v,e)},error_callback:e=>{var t;null===(t=g.current)||void 0===t||t.call(g,e)},state:l,...d});p.current=s},[c,f,t,r,l]);let x=(0,o.useCallback)(e=>{var t;return null===(t=p.current)||void 0===t?void 0:t.requestAccessToken(e)},[]),h=(0,o.useCallback)(()=>{var e;return null===(e=p.current)||void 0===e?void 0:e.requestCode()},[]);return"implicit"===t?x:h}},56603:function(e,t,r){"use strict";r.d(t,{Toaster:function(){return x}});var o=r(75376),n=r(32486),s=r(65807),a=r(53447),i=r(22397),u=r(58983);let l=s.zt,d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,o.jsx)(s.l_,{ref:t,className:(0,u.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",r),...n})});d.displayName=s.l_.displayName;let c=(0,a.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),f=n.forwardRef((e,t)=>{let{className:r,variant:n,...a}=e;return(0,o.jsx)(s.fC,{ref:t,className:(0,u.cn)(c({variant:n}),r),...a})});f.displayName=s.fC.displayName,n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,o.jsx)(s.aU,{ref:t,className:(0,u.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",r),...n})}).displayName=s.aU.displayName;let p=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,o.jsx)(s.x8,{ref:t,className:(0,u.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",r),"toast-close":"",...n,children:(0,o.jsx)(i.Z,{className:"h-4 w-4"})})});p.displayName=s.x8.displayName;let v=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,o.jsx)(s.Dx,{ref:t,className:(0,u.cn)("text-sm font-semibold",r),...n})});v.displayName=s.Dx.displayName;let m=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,o.jsx)(s.dk,{ref:t,className:(0,u.cn)("text-sm opacity-90",r),...n})});m.displayName=s.dk.displayName;var g=r(15125);function x(){let{toasts:e}=(0,g.pm)();return(0,o.jsxs)(l,{children:[e.map(function(e){let{id:t,title:r,description:n,action:s,...a}=e;return(0,o.jsxs)(f,{...a,children:[(0,o.jsxs)("div",{className:"grid gap-1",children:[r&&(0,o.jsx)(v,{children:r}),n&&(0,o.jsx)(m,{children:n})]}),s,(0,o.jsx)(p,{})]},t)}),(0,o.jsx)(d,{})]})}},15125:function(e,t,r){"use strict";r.d(t,{pm:function(){return f}});var o=r(32486);let n=0,s=new Map,a=e=>{if(s.has(e))return;let t=setTimeout(()=>{s.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?a(r):e.toasts.forEach(e=>{a(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},u=[],l={toasts:[]};function d(e){l=i(l,e),u.forEach(e=>{e(l)})}function c(e){let{...t}=e,r=(n=(n+1)%Number.MAX_SAFE_INTEGER).toString(),o=()=>d({type:"DISMISS_TOAST",toastId:r});return d({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||o()}}}),{id:r,dismiss:o,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function f(){let[e,t]=o.useState(l);return o.useEffect(()=>(u.push(t),()=>{let e=u.indexOf(t);e>-1&&u.splice(e,1)}),[e]),{...e,toast:c,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},58983:function(e,t,r){"use strict";r.d(t,{cn:function(){return s},k:function(){return a}});var o=r(89824),n=r(97215);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.m6)((0,o.W)(t))}let a=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"}},function(e){e.O(0,[7140,4144,5614,5243,7542,2344,1744],function(){return e(e.s=18989)}),_N_E=e.O()}]);