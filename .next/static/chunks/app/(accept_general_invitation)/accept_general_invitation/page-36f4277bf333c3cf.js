(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9573],{98015:function(e,t,r){Promise.resolve().then(r.bind(r,93901))},22909:function(e,t,r){"use strict";r.d(t,{GoogleOAuthProvider:function(){return a},Nq:function(){return l}});var o=r(32486);let n=(0,o.createContext)(null);function a(e){let{clientId:t,nonce:r,onScriptLoadSuccess:a,onScriptLoadError:l,children:i}=e,s=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{nonce:t,onScriptLoadSuccess:r,onScriptLoadError:n}=e,[a,l]=(0,o.useState)(!1),i=(0,o.useRef)(r);i.current=r;let s=(0,o.useRef)(n);return s.current=n,(0,o.useEffect)(()=>{let e=document.createElement("script");return e.src="https://accounts.google.com/gsi/client",e.async=!0,e.defer=!0,e.nonce=t,e.onload=()=>{var e;l(!0),null===(e=i.current)||void 0===e||e.call(i)},e.onerror=()=>{var e;l(!1),null===(e=s.current)||void 0===e||e.call(s)},document.body.appendChild(e),()=>{document.body.removeChild(e)}},[t]),a}({nonce:r,onScriptLoadSuccess:a,onScriptLoadError:l}),u=(0,o.useMemo)(()=>({clientId:t,scriptLoadedSuccessfully:s}),[t,s]);return o.createElement(n.Provider,{value:u},i)}function l(e){let{flow:t="implicit",scope:r="",onSuccess:a,onError:l,onNonOAuthError:i,overrideScope:s,state:u,...c}=e,{clientId:d,scriptLoadedSuccessfully:v}=function(){let e=(0,o.useContext)(n);if(!e)throw Error("Google OAuth components must be used within GoogleOAuthProvider");return e}(),m=(0,o.useRef)(),h=(0,o.useRef)(a);h.current=a;let f=(0,o.useRef)(l);f.current=l;let g=(0,o.useRef)(i);g.current=i,(0,o.useEffect)(()=>{var e,o;if(!v)return;let n="implicit"===t?"initTokenClient":"initCodeClient",a=null===(o=null===(e=null==window?void 0:window.google)||void 0===e?void 0:e.accounts)||void 0===o?void 0:o.oauth2[n]({client_id:d,scope:s?r:"openid profile email ".concat(r),callback:e=>{var t,r;if(e.error)return null===(t=f.current)||void 0===t?void 0:t.call(f,e);null===(r=h.current)||void 0===r||r.call(h,e)},error_callback:e=>{var t;null===(t=g.current)||void 0===t||t.call(g,e)},state:u,...c});m.current=a},[d,v,t,r,u]);let p=(0,o.useCallback)(e=>{var t;return null===(t=m.current)||void 0===t?void 0:t.requestAccessToken(e)},[]),x=(0,o.useCallback)(()=>{var e;return null===(e=m.current)||void 0===e?void 0:e.requestCode()},[]);return"implicit"===t?p:x}},9824:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var o=r(32486);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:i=2,absoluteStrokeWidth:s,className:u="",children:c,iconNode:d,...v}=e;return(0,o.createElement)("svg",{ref:t,...l,width:n,height:n,stroke:r,strokeWidth:s?24*Number(i)/Number(n):i,className:a("lucide",u),...v},[...d.map(e=>{let[t,r]=e;return(0,o.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),s=(e,t)=>{let r=(0,o.forwardRef)((r,l)=>{let{className:s,...u}=r;return(0,o.createElement)(i,{ref:l,iconNode:t,className:a("lucide-".concat(n(e)),s),...u})});return r.displayName="".concat(e),r}},20603:function(e,t,r){"use strict";r.d(t,{Z:function(){return o}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=(0,r(9824).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},39713:function(e,t,r){"use strict";r.d(t,{default:function(){return n.a}});var o=r(74033),n=r.n(o)},16669:function(e,t,r){"use strict";r.d(t,{default:function(){return n.a}});var o=r(6092),n=r.n(o)},47411:function(e,t,r){"use strict";var o=r(13362);r.o(o,"useParams")&&r.d(t,{useParams:function(){return o.useParams}}),r.o(o,"usePathname")&&r.d(t,{usePathname:function(){return o.usePathname}}),r.o(o,"useRouter")&&r.d(t,{useRouter:function(){return o.useRouter}}),r.o(o,"useSearchParams")&&r.d(t,{useSearchParams:function(){return o.useSearchParams}})},74033:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return s},getImageProps:function(){return i}});let o=r(60723),n=r(25738),a=r(28863),l=o._(r(44543));function i(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let s=a.Image},93901:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return m}});var o=r(75376),n=r(32486),a=r(20603),l=r(16669),i=r(39713),s=r(86418),u=r(73003),c=r(47411),d=r(22909),v=()=>{var e;let[t,r]=(0,n.useState)(!1),[v,m]=(0,n.useState)(""),[h,f]=(0,n.useState)(""),[g,p]=(0,n.useState)(!1),[x,w]=(0,n.useState)(!1),[y,j]=(0,n.useState)(!1),b=(0,c.useRouter)(),[k,N]=(0,n.useState)(null),S=(0,c.useSearchParams)(),C=S.get("org_id"),_=S.get("invitation_token"),[Z,P]=(0,n.useState)(!0);(0,n.useEffect)(()=>{C&&(localStorage.setItem("orgId",C),(async()=>{let e=await (0,s.Gl)("/organisations/".concat(C,"/load-org-info"));if((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201){var t;N(null==e?void 0:null===(t=e.data)||void 0===t?void 0:t.data)}P(!1)})())},[C]);let I=async e=>{e.preventDefault(),p(!0);let t=await (0,s.HH)("/auth/register",{email:v,password:h});if((null==t?void 0:t.status)===200||(null==t?void 0:t.status)===201){var r,o,n,a;localStorage.setItem("token",null==t?void 0:null===(o=t.data)||void 0===o?void 0:null===(r=o.data)||void 0===r?void 0:r.access_token),localStorage.setItem("user",JSON.stringify(null==t?void 0:null===(a=t.data)||void 0===a?void 0:null===(n=a.data)||void 0===n?void 0:n.user)),setTimeout(()=>{E()},1500)}else p(!1)},A=(0,d.Nq)({flow:"auth-code",onSuccess:async e=>{j(!0);let{code:t}=e;try{let e=await (0,s.xo)("/auth/google",{grant_code:t});if((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201){var r,o,n,a;localStorage.setItem("token",null==e?void 0:null===(o=e.data)||void 0===o?void 0:null===(r=o.data)||void 0===r?void 0:r.access_token),localStorage.setItem("user",JSON.stringify(null==e?void 0:null===(a=e.data)||void 0===a?void 0:null===(n=a.data)||void 0===n?void 0:n.user)),setTimeout(()=>{E()},1500)}}catch(e){console.error("Google Login Error:",e)}finally{j(!1)}},onError:()=>{console.error("Login Failed")}}),E=async()=>{p(!0);let e=await (0,s.HH)("/invite/general/verify",{token:_});(null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201?b.push("/client/invited?org_id=".concat(C)):p(!1)};return(0,o.jsxs)("div",{className:"w-full pb-20",children:[(0,o.jsxs)("div",{className:"flex flex-col items-center justify-center w-full bg-[#faf8f6] py-10 rounded-md text-center px-6",children:[(0,o.jsx)(l.default,{href:"/",children:(0,o.jsx)(i.default,{src:"/login_logo.svg",alt:"",width:86,height:31})}),Z?(0,o.jsx)("div",{className:"w-full h-[30vh] max-w-[48rem] mx-auto flex items-center justify-center py-[2rem]",children:(0,o.jsx)(u.Z,{height:"50",width:"50",color:"#7141F8"})}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("h1",{className:"text-3xl sm:text-4xl lg:text-[48px] font-bold leading-tight mt-14",children:["See what"," ",(0,o.jsx)("span",{className:"text-[#6E1EFF] capitalize",children:null==k?void 0:k.organisation_name})," ","is up to"]}),(0,o.jsx)("p",{className:"mt-2 text-gray-700 text-base",children:"Telex is where work happens for companies of all sizes."}),(0,o.jsx)("div",{className:"mt-6 flex items-center justify-center",children:(0,o.jsx)("div",{className:"flex -space-x-3",children:null==k?void 0:null===(e=k.users_photos)||void 0===e?void 0:e.map((e,t)=>(0,o.jsx)(i.default,{src:e,className:"size-14 rounded-lg border-2 border-white object-cover",alt:"members",width:100,height:100},t))})}),(0,o.jsxs)("p",{className:"mt-1 text-base text-gray-600",children:[null==k?void 0:k.org_user_info,"."]})]})]}),(0,o.jsx)("div",{className:"max-w-lg w-full mx-auto bg-white rounded-md text-center",children:t?(0,o.jsxs)("form",{onSubmit:I,className:"text-left mt-10 space-y-5 px-6",children:[(0,o.jsxs)("p",{className:"text-sm font-medium",children:["We suggest using the"," ",(0,o.jsx)("strong",{children:"email account you use for work."})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium",children:"Email"}),(0,o.jsx)("input",{id:"email",type:"email",value:v,onChange:e=>m(e.target.value),className:"w-full mt-1 px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-purple-600",placeholder:"<EMAIL>",required:!0})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium",children:"Password"}),(0,o.jsx)("input",{id:"password",type:"password",value:h,onChange:e=>f(e.target.value),className:"w-full mt-1 px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-purple-600",placeholder:"********",required:!0})]}),(0,o.jsxs)("button",{disabled:g||""===v||""===h,className:"w-full bg-blue-200 hover:bg-blue-400 text-white font-semibold py-3 rounded-md flex items-center justify-center transition",children:["Continue",g&&(0,o.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"})]}),(0,o.jsxs)("div",{className:"flex items-start gap-2 mt-3",children:[(0,o.jsx)("input",{id:"marketing",type:"checkbox",checked:x,onChange:e=>w(e.target.checked),className:"mt-1"}),(0,o.jsx)("label",{htmlFor:"marketing",className:"text-sm text-gray-600",children:"It’s okay to send me marketing communications about Salesforce, including Telex. I can unsubscribe at any time."})]}),(0,o.jsxs)("p",{className:"text-xs text-gray-500 mt-3",children:["By continuing, you’re agreeing to our"," ",(0,o.jsx)("a",{href:"/terms-of-service",target:"_blank",className:"text-blue-600 underline",children:"User Terms of Service"}),". Additional disclosures are available in our"," ",(0,o.jsx)("a",{href:"/policy",target:"_blank",className:"text-blue-600 underline",children:"Privacy Policy"}),"."]})]}):(0,o.jsxs)("div",{className:"text-left mt-10 space-y-4 px-8",children:[(0,o.jsxs)("p",{className:"text-base font-medium text-center",children:["We suggest using the"," ",(0,o.jsx)("strong",{children:"email account you use for work."})]}),(0,o.jsx)("div",{className:"flex flex-col gap-[10px]",children:(0,o.jsx)("div",{className:"border border-[#D0D0FD] rounded-md",children:(0,o.jsxs)("div",{onClick:()=>A(),className:"cursor-pointer flex flex-row gap-[10px] border border-[#D0D0FD] rounded-md items-center justify-center py-[11px]",children:[(0,o.jsx)(i.default,{src:"/google.svg",width:24,height:24,alt:"google"}),(0,o.jsx)("div",{className:"text-[16px] font-[600] leading-[20.16px]",children:y?(0,o.jsxs)("span",{className:"flex items-center gap-x-2",children:[(0,o.jsx)("span",{className:"animate-pulse",children:"Logging in..."}),(0,o.jsx)(u.Z,{width:"20",height:"20",color:"#7141F8"})]}):"Continue with Google"})]})})}),(0,o.jsxs)("button",{className:"w-full border border-gray-300 rounded-md py-2 flex items-center justify-center gap-2 hover:bg-gray-50 transition",onClick:()=>r(!0),children:[(0,o.jsx)(a.Z,{size:22}),(0,o.jsx)("span",{className:"font-medium",children:"Continue With Email"})]})]})})]})},m=()=>(0,o.jsx)("div",{className:"",children:(0,o.jsx)(n.Suspense,{fallback:(0,o.jsx)("p",{className:"w-full max-w-[1240px] mx-auto px-6 sm:px-12 lg:px-[3.5rem] mb-[2rem] text-lg",children:"Loading..."}),children:(0,o.jsx)(v,{})})})},73003:function(e,t,r){"use strict";var o=r(75376);r(32486);var n=r(10983);t.Z=e=>{let{height:t,width:r,color:a}=e;return(0,o.jsx)(n.iT,{height:t||20,width:r||20,color:a||"#fff",visible:!0,ariaLabel:"oval-loading",secondaryColor:a||"#fff",strokeWidth:5,strokeWidthSecondary:5})}},86418:function(e,t,r){"use strict";r.d(t,{Gl:function(){return l},HH:function(){return s},Q_:function(){return u},_x:function(){return d},an:function(){return v},i1:function(){return h},jx:function(){return m},x9:function(){return c},xo:function(){return i}});var o=r(20818),n=r(13352);let a=r(18648).env.NEXT_PUBLIC_BASE_URL,l=async e=>{let t=localStorage.getItem("token")||"";try{return await o.Z.get(a+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var r,n,l;return((null==e?void 0:null===(r=e.response)||void 0===r?void 0:r.status)===401||(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(n=l.data)||void 0===n?void 0:n.status_code)===401)&&(localStorage.clear(),window.location.href="/auth/login"),e}},i=async(e,t)=>{let r=localStorage.getItem("token")||"";try{return await o.Z.post(a+e,t,{headers:{Authorization:"Bearer ".concat(r),"Content-Type":"application/json"}})}catch(e){var l,i,s,u,c;return n.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(l=i.data)||void 0===l?void 0:l.message),(null==e?void 0:null===(s=e.response)||void 0===s?void 0:s.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(u=c.data)||void 0===u?void 0:u.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},s=async(e,t)=>{let r=localStorage.getItem("token")||"";try{return await o.Z.post(a+e,t,{headers:{Authorization:"Bearer ".concat(r),"Content-Type":"application/json"}})}catch(e){var l,i;return n.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(l=i.data)||void 0===l?void 0:l.message),e}},u=async(e,t)=>{let r=localStorage.getItem("token")||"";try{return await o.Z.post(a+e,t,{headers:{Authorization:"Bearer ".concat(r),"Content-Type":"application/json"}})}catch(e){return e}},c=async(e,t)=>{let r=localStorage.getItem("token")||"";try{return await o.Z.post(a+e,t,{headers:{Authorization:"Bearer ".concat(r),"Content-Type":"multipart/form-data"}})}catch(e){var l,i,s,u,c;return n.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(l=i.data)||void 0===l?void 0:l.message),(null==e?void 0:null===(s=e.response)||void 0===s?void 0:s.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(u=c.data)||void 0===u?void 0:u.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},d=async(e,t)=>{let r=localStorage.getItem("token")||"";try{return await o.Z.patch(a+e,t,{headers:{Authorization:"Bearer ".concat(r),"Content-Type":"application/json"}})}catch(e){var l,i,s,u,c;return n.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(l=i.data)||void 0===l?void 0:l.message),(null==e?void 0:null===(s=e.response)||void 0===s?void 0:s.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(u=c.data)||void 0===u?void 0:u.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},v=async(e,t)=>{let r=localStorage.getItem("token")||"";try{return await o.Z.put(a+e,t,{headers:{Authorization:"Bearer ".concat(r),"Content-Type":"application/json"}})}catch(e){var l,i,s,u,c;return n.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(l=i.data)||void 0===l?void 0:l.message),(null==e?void 0:null===(s=e.response)||void 0===s?void 0:s.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(u=c.data)||void 0===u?void 0:u.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},m=async e=>{let t=localStorage.getItem("token")||"";try{return await o.Z.delete(a+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var r,l;return n.Z.error(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(r=l.data)||void 0===r?void 0:r.message),e}},h=async e=>{let t=localStorage.getItem("token")||"";try{return await o.Z.delete(a+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){return e}}}},function(e){e.O(0,[4269,8863,6092,5300,2987,7542,2344,1744],function(){return e(e.s=98015)}),_N_E=e.O()}]);