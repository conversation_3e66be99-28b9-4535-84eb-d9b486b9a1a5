(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8610],{54013:function(e,t,a){Promise.resolve().then(a.bind(a,21437))},39713:function(e,t,a){"use strict";a.d(t,{default:function(){return s.a}});var r=a(74033),s=a.n(r)},47411:function(e,t,a){"use strict";var r=a(13362);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},74033:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return l},getImageProps:function(){return i}});let r=a(60723),s=a(25738),o=a(28863),n=r._(a(44543));function i(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,a]of Object.entries(t))void 0===a&&delete t[e];return{props:t}}let l=o.Image},21437:function(e,t,a){"use strict";a.r(t);var r=a(75376),s=a(39713),o=a(32486),n=a(11492),i=a(47411),l=a(56603),d=a(10952),u=a(73003);t.default=function(){let[e,t]=(0,o.useState)(""),[a,c]=(0,o.useState)(""),[f,p]=(0,o.useState)(!1),[m,v]=(0,o.useState)({email:"",password:""}),x=(0,i.useRouter)(),[g,h]=(0,o.useState)(!1),w=()=>{let t={email:"",password:""};return e?/\S+@\S+\.\S+/.test(e)||(t.email="Invalid email address"):t.email="Email is required",a?a.length<6&&(t.password="Password must be at least 6 characters"):t.password="Password is required",v(t),!t.email&&!t.password},y=async t=>{if(t.preventDefault(),w()){h(!0);let t=await (0,d.xo)("/backoffice/login",{email:e,password:a});if((null==t?void 0:t.status)===200||(null==t?void 0:t.status)===201){var r,s,o,n,i,l,u;localStorage.setItem("admintoken",null==t?void 0:null===(s=t.data)||void 0===s?void 0:null===(r=s.data)||void 0===r?void 0:r.access_token),localStorage.setItem("adminemail",null==t?void 0:null===(i=t.data)||void 0===i?void 0:null===(n=i.data)||void 0===n?void 0:null===(o=n.admin)||void 0===o?void 0:o.email),localStorage.setItem("admin",JSON.stringify(null==t?void 0:null===(u=t.data)||void 0===u?void 0:null===(l=u.data)||void 0===l?void 0:l.admin)),x.push("/admin/dashboard")}else h(!1)}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.Toaster,{}),(0,r.jsx)("main",{className:"w-full min-h-screen flex flex-col md:flex-row",children:(0,r.jsxs)("section",{className:"w-full md:w-[55%] flex flex-col max-w-xs md:max-w-lg mx-auto items-start justify-start pt-[20px] md:pt-0",children:[(0,r.jsxs)("div",{className:"w-full flex flex-col justify-center mt-[60px] md:mt-[80px] items-center gap-[8px] mb-[32px]",children:[(0,r.jsx)("h1",{className:"w-full text-center text-[24px] md:text-[28px] font-[600] leading-[30px] md:leading-[35px]",children:"Telex Admin"}),(0,r.jsx)("p",{className:"w-full text-center text-[14px] md:text-[16px] text-[#344054] font-[400] leading-[21px] md:leading-[27px]",children:"Login to Control Panel"})]}),(0,r.jsxs)("form",{onSubmit:y,className:"w-full",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-[16px]",children:[(0,r.jsxs)("div",{className:"w-full flex flex-col gap-[8px] relative",children:[(0,r.jsx)("label",{htmlFor:"email",className:"text-[14px] font-[400] leading-[21px]",children:"Email address"}),(0,r.jsxs)("div",{className:"w-full flex flex-col gap-[2px]",children:[(0,r.jsx)("input",{type:"text",value:e,onChange:e=>{t(e.target.value),v(e=>({...e,email:""}))},placeholder:"Enter your email",className:"w-full text-[12px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border ".concat(m.email?"border-[#F81404]":"border-[#D0D0FD]"," outline-none rounded-md py-[13px] pl-[13px]")}),m.email&&(0,r.jsx)("small",{className:"text-[12px] text-[#F81404]",children:m.email})]})]}),(0,r.jsxs)("div",{className:"w-full flex flex-col gap-[8px] relative",children:[(0,r.jsx)("label",{htmlFor:"password",className:"text-[14px] font-[400] leading-[21px]",children:"Password"}),(0,r.jsxs)("div",{className:"w-full flex flex-col gap-[2px]",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:f?"text":"password",value:a,onChange:e=>{c(e.target.value),v(e=>({...e,password:""}))},placeholder:"Password",className:"w-full text-[12px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border ".concat(m.password?"border-[#F81404]":"border-[#D0D0FD]"," outline-none rounded-md py-[13px] pl-[13px] pr-[40px]")}),(0,r.jsx)("button",{type:"button",onClick:()=>{p(!f)},className:"absolute right-3 top-1/2 transform -translate-y-1/2 focus:outline-none",children:(0,r.jsx)(s.default,{src:f?"/eye_closed.svg":"/eye_open.svg",alt:f?"Hide password":"Show password",width:20,height:20})})]}),m.password&&(0,r.jsx)("small",{className:"text-[12px] text-[#F81404]",children:m.password})]})]})]}),(0,r.jsx)("div",{className:"flex flex-col gap-[24px] mt-10",children:(0,r.jsx)("div",{className:"flex flex-col gap-[16px]",children:(0,r.jsx)(n.z,{type:"submit",variant:"default",className:"py-6 bg-[#7141F8] hover:bg-[#8760f8] text-white",children:g?(0,r.jsxs)("span",{className:"flex items-center gap-x-2",children:[(0,r.jsx)("span",{className:"animate-pulse",children:"Logging in..."})," ",(0,r.jsx)(u.Z,{width:"20",height:"40"})]}):(0,r.jsx)("span",{children:"Login"})})})})]})]})})]})}},11492:function(e,t,a){"use strict";a.d(t,{d:function(){return l},z:function(){return d}});var r=a(75376),s=a(32486),o=a(91007),n=a(53447),i=a(58983);let l=(0,n.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,t)=>{let{className:a,variant:s,size:n,asChild:d=!1,...u}=e,c=d?o.g7:"button";return(0,r.jsx)(c,{className:(0,i.cn)(l({variant:s,size:n,className:a})),ref:t,...u})});d.displayName="Button"},73003:function(e,t,a){"use strict";var r=a(75376);a(32486);var s=a(10983);t.Z=e=>{let{height:t,width:a,color:o}=e;return(0,r.jsx)(s.iT,{height:t||20,width:a||20,color:o||"#fff",visible:!0,ariaLabel:"oval-loading",secondaryColor:o||"#fff",strokeWidth:5,strokeWidthSecondary:5})}},56603:function(e,t,a){"use strict";a.d(t,{Toaster:function(){return g}});var r=a(75376),s=a(32486),o=a(65807),n=a(53447),i=a(22397),l=a(58983);let d=o.zt,u=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(o.l_,{ref:t,className:(0,l.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",a),...s})});u.displayName=o.l_.displayName;let c=(0,n.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),f=s.forwardRef((e,t)=>{let{className:a,variant:s,...n}=e;return(0,r.jsx)(o.fC,{ref:t,className:(0,l.cn)(c({variant:s}),a),...n})});f.displayName=o.fC.displayName,s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(o.aU,{ref:t,className:(0,l.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",a),...s})}).displayName=o.aU.displayName;let p=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(o.x8,{ref:t,className:(0,l.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",a),"toast-close":"",...s,children:(0,r.jsx)(i.Z,{className:"h-4 w-4"})})});p.displayName=o.x8.displayName;let m=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(o.Dx,{ref:t,className:(0,l.cn)("text-sm font-semibold",a),...s})});m.displayName=o.Dx.displayName;let v=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(o.dk,{ref:t,className:(0,l.cn)("text-sm opacity-90",a),...s})});v.displayName=o.dk.displayName;var x=a(15125);function g(){let{toasts:e}=(0,x.pm)();return(0,r.jsxs)(d,{children:[e.map(function(e){let{id:t,title:a,description:s,action:o,...n}=e;return(0,r.jsxs)(f,{...n,children:[(0,r.jsxs)("div",{className:"grid gap-1",children:[a&&(0,r.jsx)(m,{children:a}),s&&(0,r.jsx)(v,{children:s})]}),o,(0,r.jsx)(p,{})]},t)}),(0,r.jsx)(u,{})]})}},15125:function(e,t,a){"use strict";a.d(t,{pm:function(){return f}});var r=a(32486);let s=0,o=new Map,n=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),u({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?n(a):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function u(e){d=i(d,e),l.forEach(e=>{e(d)})}function c(e){let{...t}=e,a=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>u({type:"DISMISS_TOAST",toastId:a});return u({type:"ADD_TOAST",toast:{...t,id:a,open:!0,onOpenChange:e=>{e||r()}}}),{id:a,dismiss:r,update:e=>u({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function f(){let[e,t]=r.useState(d);return r.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:c,dismiss:e=>u({type:"DISMISS_TOAST",toastId:e})}}},58983:function(e,t,a){"use strict";a.d(t,{cn:function(){return o},k:function(){return n}});var r=a(89824),s=a(97215);function o(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.m6)((0,r.W)(t))}let n=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"},10952:function(e,t,a){"use strict";a.d(t,{Gl:function(){return i},an:function(){return d},jx:function(){return u},xo:function(){return l}});var r=a(20818),s=a(13352),o=a(18648);let n=o.env.NEXT_PUBLIC_BASE_URL;o.env.NEXT_PUBLIC_INTEGRATION_URL;let i=async(e,t)=>{try{return await r.Z.get(n+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var a,s,o;return((null==e?void 0:null===(a=e.response)||void 0===a?void 0:a.status)===401||(null==e?void 0:null===(o=e.response)||void 0===o?void 0:null===(s=o.data)||void 0===s?void 0:s.status_code)===401)&&(localStorage.clear(),window.location.href="/auth/login"),e}},l=async(e,t,a)=>{try{return await r.Z.post(n+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var o,i,l,d,u;return s.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(o=i.data)||void 0===o?void 0:o.message),(null==e?void 0:null===(l=e.response)||void 0===l?void 0:l.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(u=e.response)||void 0===u?void 0:null===(d=u.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},d=async(e,t,a)=>{try{return await r.Z.put(n+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var o,i,l,d,u;return s.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(o=i.data)||void 0===o?void 0:o.message),(null==e?void 0:null===(l=e.response)||void 0===l?void 0:l.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(u=e.response)||void 0===u?void 0:null===(d=u.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},u=async(e,t)=>{try{return await r.Z.delete(n+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var a,o,i,l,d;return s.Z.error(null==e?void 0:null===(o=e.response)||void 0===o?void 0:null===(a=o.data)||void 0===a?void 0:a.message),(null==e?void 0:null===(i=e.response)||void 0===i?void 0:i.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(l=d.data)||void 0===l?void 0:l.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}}}},function(e){e.O(0,[4269,8863,7140,4144,5300,5614,2987,5243,7542,2344,1744],function(){return e(e.s=54013)}),_N_E=e.O()}]);