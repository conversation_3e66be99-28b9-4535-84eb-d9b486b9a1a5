(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3030],{80423:function(e,t,n){Promise.resolve().then(n.bind(n,68705))},9824:function(e,t,n){"use strict";n.d(t,{Z:function(){return o}});var a=n(32486);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&n.indexOf(e)===t).join(" ")};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a.forwardRef)((e,t)=>{let{color:n="currentColor",size:l=24,strokeWidth:s=2,absoluteStrokeWidth:o,className:d="",children:c,iconNode:u,...m}=e;return(0,a.createElement)("svg",{ref:t,...i,width:l,height:l,stroke:n,strokeWidth:o?24*Number(s)/Number(l):s,className:r("lucide",d),...m},[...u.map(e=>{let[t,n]=e;return(0,a.createElement)(t,n)}),...Array.isArray(c)?c:[c]])}),o=(e,t)=>{let n=(0,a.forwardRef)((n,i)=>{let{className:o,...d}=n;return(0,a.createElement)(s,{ref:i,iconNode:t,className:r("lucide-".concat(l(e)),o),...d})});return n.displayName="".concat(e),n}},25344:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,n(9824).Z)("CircleCheckBig",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2336:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,n(9824).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},78:function(e,t,n){"use strict";n.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,n(9824).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},16669:function(e,t,n){"use strict";n.d(t,{default:function(){return l.a}});var a=n(6092),l=n.n(a)},65683:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return a}});let a=n(60723)._(n(32486)).default.createContext(null)},68705:function(e,t,n){"use strict";n.r(t);var a=n(75376),l=n(32486),r=n(10952),i=n(16669),s=n(25344),o=n(2336),d=n(78),c=n(26242);t.default=function(){var e,t,n,u,m,v;let[h,g]=(0,l.useState)([]),[x,p]=(0,l.useState)([]),[f,j]=(0,l.useState)([]),y=e=>"active"===e?(0,a.jsx)(s.Z,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(o.Z,{className:"h-4 w-4 text-yellow-600"}),b=async e=>{try{let t=(await (0,r.Gl)("/backoffice/agents/all",e)).data.data;g(t)}catch(e){console.log(e)}},N=async e=>{try{let t=(await (0,r.Gl)("/backoffice/agents/metrics",e)).data.data;p(t)}catch(e){console.log(e)}},w=async e=>{try{let t=await (0,r.Gl)("/backoffice/credits/usage",e);console.log(t);let n=t.data.data;j(n)}catch(e){console.log(e)}};return(0,l.useEffect)(()=>{let e=localStorage.getItem("admintoken");b(e),N(e),w(e)},[]),(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"mx-auto xl:max-w-[1440px] grow max-w-screen",children:[(0,a.jsxs)("div",{className:"mt-5 p-4",children:[(0,a.jsx)("h2",{className:"font-bold text-[20px]",children:"Dashboard"}),(0,a.jsx)("p",{className:"text-gray-600 text-[14px]",children:"Welcome to Your Agents Dashboard — View, Manage, and Monitor All Agents in One Place"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-4 p-4",children:[(0,a.jsxs)("div",{className:"p-5 bg-white border border-gray-100 rounded-md",children:[(0,a.jsx)("span",{className:"text-2xl font-bold",children:null!==(e=null==x?void 0:x.active)&&void 0!==e?e:0}),(0,a.jsxs)("p",{className:"text-gray-400 mb-5 text-[12px]",children:["For ",null!==(t=null==x?void 0:x.organizations)&&void 0!==t?t:0," organizations"]}),(0,a.jsx)("p",{className:"text-sm pt-5 text-gray-600 font-semibold",children:"Active Agents"}),(0,a.jsx)("div",{className:"w-full mt-4 pb-2",children:(0,a.jsx)("div",{className:"bg-gray-300 h-2 rounded-full",children:(0,a.jsx)("div",{className:"bg-green-500 h-2 rounded-full",style:{width:"".concat(Math.min(((null==x?void 0:x.active)||0)/100*100,100),"%")}})})})]}),(0,a.jsxs)("div",{className:"p-5 bg-white border border-gray-100 rounded-md",children:[(0,a.jsx)("span",{className:"text-2xl font-bold",children:null!==(n=null==x?void 0:x.inactive)&&void 0!==n?n:0}),(0,a.jsxs)("p",{className:"text-gray-400 mb-5 text-[12px]",children:["For ",null!==(u=null==x?void 0:x.organizations)&&void 0!==u?u:0," organizations"]}),(0,a.jsx)("p",{className:"text-sm pt-5 text-gray-600 font-semibold",children:"Inactive Agents"}),(0,a.jsx)("div",{className:"w-full mt-4 pb-2",children:(0,a.jsx)("div",{className:"bg-gray-300 h-2 rounded-full",children:(0,a.jsx)("div",{className:"bg-red-800 h-2 rounded-full",style:{width:"".concat(Math.min(((null==x?void 0:x.inactive)||0)/100*100,100),"%")}})})})]}),(0,a.jsxs)("div",{className:"p-5 bg-white border border-gray-100 rounded-md",children:[(0,a.jsx)("span",{className:"text-2xl font-bold",children:null!==(m=null==x?void 0:x.organizations)&&void 0!==m?m:0}),(0,a.jsx)("p",{className:"text-gray-400 mb-5 text-[12px]",children:"Including Inactive organizations"}),(0,a.jsx)("p",{className:"text-sm pt-5 text-gray-600 font-semibold",children:"Organizations"}),(0,a.jsx)("div",{className:"w-full mt-4 pb-2",children:(0,a.jsx)("div",{className:"bg-gray-300 h-2 rounded-full",children:(0,a.jsx)("div",{className:"bg-[#7b50fb] h-2 rounded-full",style:{width:"".concat(Math.min(((null==x?void 0:x.organizations)||0)/500*100,100),"%")}})})})]}),(0,a.jsxs)("div",{className:"p-5 bg-white border border-gray-100 rounded-md",children:[(0,a.jsx)("span",{className:"text-2xl font-bold",children:null!==(v=null==x?void 0:x.credits)&&void 0!==v?v:0}),(0,a.jsx)("p",{className:"text-gray-400 mb-5 text-[12px]",children:"Accumulated accross all organizations"}),(0,a.jsx)("p",{className:"text-sm pt-5 font-semibold text-gray-600",children:"Credit Accumulated"}),(0,a.jsx)("div",{className:"w-full mt-4 pb-2",children:(0,a.jsx)("div",{className:"bg-gray-300 h-2 rounded-full",children:(0,a.jsx)("div",{className:"bg-yellow-500 h-2 rounded-full",style:{width:"".concat(Math.min(((null==x?void 0:x.credits)||0)/1e3*100,100),"%")}})})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 p-4 pb-10",children:[(0,a.jsxs)("div",{className:"p-5 bg-white rounded-md border border-gray-100",children:[(0,a.jsx)("p",{className:"font-bold text-[16px] border-b pb-2 border-gray-100 mb-3 text-gray-600",children:"Recent Added Agents"}),(0,a.jsx)("div",{className:"space-y-4",children:null==h?void 0:h.slice(0,4).map((e,t)=>{var n,l;return(0,a.jsxs)(i.default,{href:(null==e?void 0:e.source)=="organization"?"/admin/agents/organization/".concat(null==e?void 0:e.integration_id):"/admin/agents/integration/".concat(null==e?void 0:e.id),children:[(0,a.jsxs)("div",{className:"p-6 border rounded-lg space-y-4 hover gap-2 hover:bg-gray-50 transition",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between pb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"p-2 bg-solar-green-100 rounded-full",children:(0,a.jsx)(d.Z,{className:"h-5 w-5 text-solar-green-600"})}),(0,a.jsx)("div",{children:(0,a.jsx)("h3",{className:"font-semibold",children:(null==e?void 0:e.app_name)||(null==e?void 0:e.name)})})]}),(0,a.jsx)("div",{className:"flex items-center space-x-1 px-3 py-1 rounded-full border ".concat((null==e?void 0:e.is_active)?(0,c.z2)("active"):(0,c.z2)("inactive")),children:(null==e?void 0:e.is_active)?(0,a.jsxs)(a.Fragment,{children:[y("active"),(0,a.jsx)("span",{className:"capitalize font-medium",children:"Active"})]}):(0,a.jsxs)(a.Fragment,{children:[y("inactive"),(0,a.jsx)("span",{className:"capitalize font-medium",children:"inactive"})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Agent Name"}),(0,a.jsx)("p",{className:"font-medium",children:(null==e?void 0:e.app_name)||(null==e?void 0:e.name)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Provider"}),(0,a.jsx)("p",{className:"font-medium",children:(null==e?void 0:null===(n=e.provider)||void 0===n?void 0:n.organization)?null==e?void 0:null===(l=e.provider)||void 0===l?void 0:l.organization:"Unknown"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Type"}),(0,a.jsx)("p",{className:"font-medium",children:"Free"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Date Created"}),(0,a.jsx)("p",{className:"font-medium",children:new Date(null==e?void 0:e.created_at).toLocaleString("en-US",{weekday:"long",month:"long",day:"2-digit",year:"numeric"})})]})]})]}),(0,a.jsx)("br",{})]},t)})})]}),(0,a.jsxs)("div",{className:"p-5 bg-gray-100 h-[600px] bg-white rounded-md border border-gray-100",children:[(0,a.jsx)("p",{className:"font-bold text-[16px] border-b pb-2 border-gray-100 mb-3 text-gray-600",children:"Recent Credit Charge"}),(0,a.jsx)("div",{className:"space-y-4",children:null==f?void 0:f.slice(0,4).map((e,t)=>(0,a.jsx)("div",{className:"p-6 border rounded-lg space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Organization"}),(0,a.jsx)("p",{className:"font-medium",children:null==e?void 0:e.org_name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Agent Name"}),(0,a.jsx)("p",{className:"font-medium",children:(null==e?void 0:e.agent_name)||"Unknown"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Amount"}),(0,a.jsxs)("p",{className:"font-medium",children:[null==e?void 0:e.amount," credits"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Date Created"}),(0,a.jsx)("p",{className:"font-medium",children:new Date(null==e?void 0:e.created_at).toLocaleString("en-US",{weekday:"long",month:"long",day:"2-digit",year:"numeric"})})]})]})},t))})]})]})]})})}},10952:function(e,t,n){"use strict";n.d(t,{Gl:function(){return s},an:function(){return d},jx:function(){return c},xo:function(){return o}});var a=n(20818),l=n(13352),r=n(18648);let i=r.env.NEXT_PUBLIC_BASE_URL;r.env.NEXT_PUBLIC_INTEGRATION_URL;let s=async(e,t)=>{try{return await a.Z.get(i+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var n,l,r;return((null==e?void 0:null===(n=e.response)||void 0===n?void 0:n.status)===401||(null==e?void 0:null===(r=e.response)||void 0===r?void 0:null===(l=r.data)||void 0===l?void 0:l.status_code)===401)&&(localStorage.clear(),window.location.href="/auth/login"),e}},o=async(e,t,n)=>{try{return await a.Z.post(i+e,t,{headers:{Authorization:"Bearer ".concat(n),"Content-Type":"application/json"}})}catch(e){var r,s,o,d,c;return l.Z.error(null==e?void 0:null===(s=e.response)||void 0===s?void 0:null===(r=s.data)||void 0===r?void 0:r.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},d=async(e,t,n)=>{try{return await a.Z.put(i+e,t,{headers:{Authorization:"Bearer ".concat(n),"Content-Type":"application/json"}})}catch(e){var r,s,o,d,c;return l.Z.error(null==e?void 0:null===(s=e.response)||void 0===s?void 0:null===(r=s.data)||void 0===r?void 0:r.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},c=async(e,t)=>{try{return await a.Z.delete(i+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var n,r,s,o,d;return l.Z.error(null==e?void 0:null===(r=e.response)||void 0===r?void 0:null===(n=r.data)||void 0===n?void 0:n.message),(null==e?void 0:null===(s=e.response)||void 0===s?void 0:s.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(o=d.data)||void 0===o?void 0:o.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}}},26242:function(e,t,n){"use strict";n.d(t,{BY:function(){return r},CZ:function(){return a},Qm:function(){return l},ju:function(){return i},mu:function(){return o},z2:function(){return s}}),n(32486),n(13352);let a=e=>e<1e3?e.toString():e<1e6?"".concat(Math.floor(e/1e3),"k+"):"".concat(Math.floor(e/1e6),"m+"),l=e=>null==e?void 0:e.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2),r=e=>e.sort((e,t)=>e.name.toLowerCase()<t.name.toLowerCase()?-1:e.name.toLowerCase()>t.name.toLowerCase()?1:0);function i(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=document.createElement("div");return t.innerHTML=e,t.textContent||t.innerText||""}function s(e){return"active"===e?"text-green-600 bg-green-50 border-green-200":"text-gray-600 bg-gray-50 border-gray-200"}function o(e,t){e(!0),t&&t(),setTimeout(()=>e(!1),1e3)}}},function(e){e.O(0,[6092,5300,7542,2344,1744],function(){return e(e.s=80423)}),_N_E=e.O()}]);