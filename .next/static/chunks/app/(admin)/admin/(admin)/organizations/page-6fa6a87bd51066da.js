(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4354],{14792:function(e,t,a){Promise.resolve().then(a.bind(a,7871))},20114:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(9824).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},20384:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(9824).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},57292:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(9824).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},51178:function(e,t,a){"use strict";var s=a(75376);a(32486);var r=a(39713);t.Z=e=>{let{title:t,msg:a,imgSrc:n}=e;return(0,s.jsx)("div",{className:"w-full h-screen flex flex-col",children:(0,s.jsx)("div",{className:"w-full h-screen flex flex-col",children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center md:p-10 mt-6",children:[n&&(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)(r.default,{src:n,height:"100",width:"100",alt:"No app yet",className:"w-30 md:w-[150px] h-64"})}),(0,s.jsx)("p",{className:"mt-[40px] md:flex text-xl md:text-2xl text-center font-semibold leading-8",children:t}),(0,s.jsx)("p",{className:"w-[300px] md:w-[481px] text-center text-sm font-light leading-6",children:a})]})})})}},7871:function(e,t,a){"use strict";a.r(t);var s=a(75376),r=a(32486),n=a(10952),l=a(11492),i=a(56927),o=a(26242),d=a(25368),c=a(57292),u=a(20114),m=a(20384),f=a(21912),x=a(1896),p=a(51178);t.default=function(){let[e,t]=(0,r.useState)("All"),[a,h]=(0,r.useState)(""),[j,g]=(0,r.useState)(!1),[N,v]=(0,r.useState)([]),[y,b]=(0,r.useState)(!0),[w,k]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{(async()=>{let e=localStorage.getItem("admintoken");try{let t=await (0,n.Gl)("/backoffice/organisations/all",e);console.log(t);let a=t.data.data;v(a),k(!0)}catch(e){console.log(e),k(!1)}b(!1)})()},[]),(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"mx-auto xl:max-w-[1440px] grow max-w-screen",children:[(0,s.jsxs)("div",{className:"mt-5 p-4",children:[(0,s.jsx)("h2",{className:"font-bold text-[20px]",children:"Organizations"}),(0,s.jsx)("p",{className:"text-gray-600 text-[14px]",children:"Welcome to Your Agents Dashboard — View, Manage, and Monitor All Agents in One Place"})]}),(0,s.jsxs)("div",{className:"flex justify-between mb-5 p-4",children:[(0,s.jsxs)("div",{className:"flex items-center relative w-[500px]",children:[(0,s.jsx)(c.Z,{className:"absolute left-3 text-gray-400 h-4 w-4"}),(0,s.jsx)("input",{type:"text",placeholder:"Search organizations...",value:a,onChange:e=>h(e.target.value),className:"w-full p-2 pl-9 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#7b50fb]"})]}),(0,s.jsxs)("div",{className:"flex flex-row",children:[(0,s.jsx)("div",{className:"mr-5",children:(0,s.jsxs)(d.h_,{children:[(0,s.jsx)(d.$F,{asChild:!0,children:(0,s.jsxs)(l.z,{variant:"outline",size:"sm",children:[(0,s.jsx)(u.Z,{className:"mr-2 h-4 w-4"}),e]})}),(0,s.jsxs)(d.AW,{side:"bottom",align:"start",children:[(0,s.jsx)(d.Ju,{children:"Filter Agents"}),(0,s.jsx)(d.VD,{}),(0,s.jsxs)(d._x,{value:e,onValueChange:e=>t(e),children:[(0,s.jsx)(d.qB,{value:"All",children:"All"}),(0,s.jsx)(d.qB,{value:"Active",children:"Active"}),(0,s.jsx)(d.qB,{value:"Inactive",children:"Inactive"})]})]})]})}),(0,s.jsx)("button",{onClick:()=>(0,o.mu)(g),"aria-label":"RefreshCw",className:"py-1 px-4 h-[37px] border bg-white border-gray-200 rounded-md hover:bg-gray-100 transition",children:(0,s.jsx)(m.Z,{className:"h-4 w-4 text-gray-500 transition-transform ".concat(j?"animate-spin":"")})})]})]}),y&&(0,s.jsx)(x.Z,{}),!y&&w?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"bg-white capitalize",children:(0,s.jsxs)(f.iA,{children:[(0,s.jsx)(f.xD,{children:(0,s.jsxs)(f.SC,{children:[(0,s.jsx)(f.ss,{children:"Organization Name"}),(0,s.jsx)(f.ss,{children:"Credit Balance"}),(0,s.jsx)(f.ss,{children:"Type"}),(0,s.jsx)(f.ss,{children:"Country"}),(0,s.jsx)(f.ss,{children:"Date Created"})]})}),(0,s.jsx)(f.RM,{children:null==N?void 0:N.map(e=>(0,s.jsxs)(f.SC,{className:"hover:bg-gray-50 transition-colors",children:[(0,s.jsx)(f.pj,{children:e.name}),(0,s.jsxs)(f.pj,{children:[e.credit_balance," Credits"]}),(0,s.jsx)(f.pj,{children:e.type}),(0,s.jsx)(f.pj,{children:e.country}),(0,s.jsx)(f.pj,{children:new Date(e.created_at).toLocaleString("en-US",{weekday:"long",month:"long",day:"2-digit",year:"numeric"})})]},e.id))})]})}),(0,s.jsxs)("div",{className:"flex items-center pt-5 mt-5 pb-10 mb-10 justify-between px-6 space-x-2 py-4",children:[(0,s.jsxs)(l.z,{variant:"outline",size:"sm",className:"flex items-center gap-2 border-gray-300",children:[(0,s.jsx)(i.Z,{name:"move-left",svgProps:{}}),"Previous"]}),(0,s.jsx)("div",{className:"flex items-center space-x-1",children:Array.from({length:7},(e,t)=>{let a;return 7===(a=t+1)?(0,s.jsx)("span",{className:"px-2",children:"..."},"ellipsis2"):(0,s.jsx)(l.z,{variant:1===a?"default":"outline",size:"sm",className:"w-8 h-8 p-0",children:a},a)})}),(0,s.jsxs)(l.z,{variant:"outline",size:"sm",className:"flex items-center gap-2 border-gray-300",children:["Next",(0,s.jsx)(i.Z,{name:"move-right",svgProps:{}})]})]})]}):(0,s.jsx)(p.Z,{title:"There are no organizations yet",msg:"Create, View, Manage, and Monitor All Agents in One Place",imgSrc:"/image/empty-agent.svg"})]})})}},25368:function(e,t,a){"use strict";a.d(t,{$F:function(){return c},AW:function(){return m},Ju:function(){return p},KM:function(){return j},VD:function(){return h},Xi:function(){return f},_x:function(){return u},h_:function(){return d},qB:function(){return x}});var s=a(75376),r=a(32486),n=a(18493),l=a(51888),i=a(28780),o=a(58983);let d=n.fC,c=n.xz;n.ZA,n.Uv,n.Tr;let u=n.Ee;r.forwardRef((e,t)=>{let{className:a,inset:r,children:i,...d}=e;return(0,s.jsxs)(n.fF,{ref:t,className:(0,o.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",r&&"pl-8",a),...d,children:[i,(0,s.jsx)(l.Z,{className:"ml-auto h-4 w-4"})]})}).displayName=n.fF.displayName,r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.tu,{ref:t,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...r})}).displayName=n.tu.displayName;let m=r.forwardRef((e,t)=>{let{className:a,sideOffset:r=4,...l}=e;return(0,s.jsx)(n.Uv,{children:(0,s.jsx)(n.VY,{ref:t,sideOffset:r,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...l})})});m.displayName=n.VY.displayName;let f=r.forwardRef((e,t)=>{let{className:a,inset:r,...l}=e;return(0,s.jsx)(n.ck,{ref:t,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r&&"pl-8",a),...l})});f.displayName=n.ck.displayName,r.forwardRef((e,t)=>{let{className:a,children:r,checked:l,...d}=e;return(0,s.jsxs)(n.oC,{ref:t,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:l,...d,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(n.wU,{children:(0,s.jsx)(i.Z,{className:"h-4 w-4"})})}),r]})}).displayName=n.oC.displayName;let x=r.forwardRef((e,t)=>{let{className:a,children:r,...l}=e;return(0,s.jsxs)(n.Rk,{ref:t,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...l,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(n.wU,{children:(0,s.jsx)("div",{className:"bg-gradient-to-b from-[#8860F8] to-[#7141F8] w-5 h-5 rounded-sm flex items-center justify-center",children:(0,s.jsx)(i.Z,{color:"#ffffff"})})})}),r]})});x.displayName=n.Rk.displayName;let p=r.forwardRef((e,t)=>{let{className:a,inset:r,...l}=e;return(0,s.jsx)(n.__,{ref:t,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",a),...l})});p.displayName=n.__.displayName;let h=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.Z0,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",a),...r})});h.displayName=n.Z0.displayName;let j=e=>{let{className:t,...a}=e;return(0,s.jsx)("span",{className:(0,o.cn)("ml-auto text-xs tracking-widest opacity-60",t),...a})};j.displayName="DropdownMenuShortcut"},21912:function(e,t,a){"use strict";a.d(t,{RM:function(){return o},SC:function(){return d},iA:function(){return l},pj:function(){return u},ss:function(){return c},xD:function(){return i}});var s=a(75376),r=a(32486),n=a(58983);let l=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{className:"relative w-full overflow-auto",children:(0,s.jsx)("table",{ref:t,className:(0,n.cn)("w-full caption-bottom text-sm",a),...r})})});l.displayName="Table";let i=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("thead",{ref:t,className:(0,n.cn)("[&_tr]:border-b",a),...r})});i.displayName="TableHeader";let o=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("tbody",{ref:t,className:(0,n.cn)("[&_tr:last-child]:border-0",a),...r})});o.displayName="TableBody",r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("tfoot",{ref:t,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a),...r})}).displayName="TableFooter";let d=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("tr",{ref:t,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...r})});d.displayName="TableRow";let c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("th",{ref:t,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...r})});c.displayName="TableHead";let u=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("td",{ref:t,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...r})});u.displayName="TableCell",r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("caption",{ref:t,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",a),...r})}).displayName="TableCaption"},53447:function(e,t,a){"use strict";a.d(t,{j:function(){return l}});var s=a(89824);let r=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=s.W,l=(e,t)=>a=>{var s;if((null==t?void 0:t.variants)==null)return n(e,null==a?void 0:a.class,null==a?void 0:a.className);let{variants:l,defaultVariants:i}=t,o=Object.keys(l).map(e=>{let t=null==a?void 0:a[e],s=null==i?void 0:i[e];if(null===t)return null;let n=r(t)||r(s);return l[e][n]}),d=a&&Object.entries(a).reduce((e,t)=>{let[a,s]=t;return void 0===s||(e[a]=s),e},{});return n(e,o,null==t?void 0:null===(s=t.compoundVariants)||void 0===s?void 0:s.reduce((e,t)=>{let{class:a,className:s,...r}=t;return Object.entries(r).every(e=>{let[t,a]=e;return Array.isArray(a)?a.includes({...i,...d}[t]):({...i,...d})[t]===a})?[...e,a,s]:e},[]),null==a?void 0:a.class,null==a?void 0:a.className)}}},function(e){e.O(0,[4269,8863,7140,4144,5300,5614,2987,6343,6329,9254,3975,6539,1269,7542,2344,1744],function(){return e(e.s=14792)}),_N_E=e.O()}]);