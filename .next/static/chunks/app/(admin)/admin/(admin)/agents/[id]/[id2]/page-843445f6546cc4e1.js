(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8453],{55025:function(e,t,s){Promise.resolve().then(s.bind(s,23749))},9824:function(e,t,s){"use strict";s.d(t,{Z:function(){return c}});var a=s(32486);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&s.indexOf(e)===t).join(" ")};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,a.forwardRef)((e,t)=>{let{color:s="currentColor",size:n=24,strokeWidth:i=2,absoluteStrokeWidth:c,className:o="",children:d,iconNode:m,...x}=e;return(0,a.createElement)("svg",{ref:t,...l,width:n,height:n,stroke:s,strokeWidth:c?24*Number(i)/Number(n):i,className:r("lucide",o),...x},[...m.map(e=>{let[t,s]=e;return(0,a.createElement)(t,s)}),...Array.isArray(d)?d:[d]])}),c=(e,t)=>{let s=(0,a.forwardRef)((s,l)=>{let{className:c,...o}=s;return(0,a.createElement)(i,{ref:l,iconNode:t,className:r("lucide-".concat(n(e)),c),...o})});return s.displayName="".concat(e),s}},25344:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(9824).Z)("CircleCheckBig",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2336:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(9824).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},47679:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(9824).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},98755:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(9824).Z)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},83255:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(9824).Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},78:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(9824).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},82301:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(9824).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},85905:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(9824).Z)("Trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])},39713:function(e,t,s){"use strict";s.d(t,{default:function(){return n.a}});var a=s(74033),n=s.n(a)},47411:function(e,t,s){"use strict";var a=s(13362);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},74033:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return c},getImageProps:function(){return i}});let a=s(60723),n=s(25738),r=s(28863),l=a._(s(44543));function i(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let c=r.Image},23749:function(e,t,s){"use strict";s.r(t);var a=s(75376),n=s(32486),r=s(10952),l=s(39713),i=s(25344),c=s(2336),o=s(98755),d=s(83255),m=s(47679),x=s(78),u=s(85905),h=s(82301),p=s(26242),f=s(47411),g=s(1896),j=s(13352);t.default=function(){var e,t,s,y;let[v,N]=(0,n.useState)([]),[b,w]=(0,n.useState)([]),[k,Z]=(0,n.useState)(0),[S,C]=(0,n.useState)(0),[_,A]=(0,n.useState)(0),P=(0,f.useParams)(),[z,M]=(0,n.useState)(!1),E=P.id,I=P.id2,[O,T]=(0,n.useState)(!1),[R,V]=(0,n.useState)(!0),[F,L]=(0,n.useState)(!1),[D,q]=(0,n.useState)(!1),[W,B]=(0,n.useState)(!1),[G,H]=(0,n.useState)(!1),U=async()=>{B(!W),await J(!W,D,G)},$=async()=>{q(!D),await J(W,!D,G)},K=e=>"active"===e?(0,a.jsx)(i.Z,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(c.Z,{className:"h-4 w-4 text-yellow-600"}),Y=async()=>{let e=localStorage.getItem("admintoken");try{await (0,r.jx)("/backoffice/agents/".concat(I),e),j.Z.success("Agent deleted successfully"),window.location.href="/admin/agents"}catch(e){console.log(e)}},J=async(e,t,s)=>{let a=localStorage.getItem("admintoken"),n={is_system:t,is_active:e,is_approved:s};console.log(n);try{let e=await (0,r.an)("/backoffice/agents/".concat(I),n,a);console.log(e)}catch(e){console.log(e)}};(0,n.useEffect)(()=>{Q()},[]);let Q=async()=>{V(!0),L(!1);let e=localStorage.getItem("admintoken");try{let t=await (0,r.Gl)("/backoffice/agents/".concat(E,"/").concat(I),e);console.log(t);let s=t.data.data;N(s.agent),w(s.user),Z(s.credit_used),L(!0),q(s.agent.is_system),B(s.agent.is_active),H(s.agent.is_approved),C(s.maker_total_earning),A(s.telex_total_earning)}catch(e){console.log(e),L(!1)}V(!1)};return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"mx-auto xl:max-w-[1440px] grow max-w-screen",children:[(0,a.jsxs)("div",{className:"mt-5 p-4",children:[(0,a.jsx)("h2",{className:"font-bold text-[20px]",children:"Agent Informations"}),(0,a.jsx)("p",{className:"text-gray-600 text-[14px]",children:"Welcome to Your Agents Dashboard — View, Manage, and Monitor All Agents in One Place"})]}),R&&(0,a.jsx)(g.Z,{}),!R&&F?(0,a.jsxs)("div",{className:"p-4 mt-10 bg-white rounded-md border border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center mt-3 mb-5",children:[(0,a.jsxs)("p",{className:"flex-1",children:["API Key:"," ",z?null==v?void 0:v.preshared_key:"••••••••••••••••••••••••"]}),(0,a.jsx)("button",{onClick:()=>M(e=>!e),"aria-label":"Toggle API key visibility",className:"p-1 ml-2",children:z?(0,a.jsx)(o.Z,{size:20}):(0,a.jsx)(d.Z,{size:20})}),(0,a.jsx)("button",{onClick:()=>{navigator.clipboard.writeText(null==v?void 0:v.preshared_key),T(!0),setTimeout(()=>T(!1),2e3)},"aria-label":"Copy API key to clipboard",className:"p-1 ml-2",children:O?(0,a.jsx)(m.Z,{size:20,color:"#4ade80"}):(0,a.jsx)(m.Z,{size:20})})]}),(0,a.jsxs)("div",{className:"flex flex-row justify-between",children:[(0,a.jsxs)("div",{className:"flex flex-row",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"p-2 bg-solar-green-100 rounded-full",children:(0,a.jsx)(x.Z,{className:"h-5 w-5 text-solar-green-600"})}),(0,a.jsx)("div",{children:(0,a.jsx)("h3",{className:"font-semibold",children:v.app_name})})]}),(0,a.jsx)("p",{className:"text-[12px] text-gray-500 mt-3 ml-2",children:"- Free"})]}),(0,a.jsxs)("div",{className:"flex flex-row",children:[(0,a.jsx)("button",{onClick:Y,"aria-label":"RefreshCw",className:"py-1 px-4 h-[37px] border bg-white border-gray-200 rounded-md hover:bg-gray-100 transition",children:(0,a.jsx)(u.Z,{color:"#ff0000",className:"h-4 w-4 text-gray-500 transition-transform"})}),(0,a.jsx)("button",{"aria-label":"RefreshCw",className:"py-1 px-4 ml-3 h-[37px] border bg-white border-gray-200 rounded-md hover:bg-gray-100 transition",children:(0,a.jsx)(h.Z,{color:"#007bff",className:"h-4 w-4 text-gray-500 transition-transform"})})]})]}),(0,a.jsxs)("div",{className:"flex flew-row space-x-2",children:[(0,a.jsx)("button",{onClick:U,className:"flex items-center text-[14px] mb-5 px-4 space-x-1 px-3 py-1 rounded-full border shadow-md transition-colors duration-300 ".concat(W?(0,p.z2)("active"):(0,p.z2)("inactive")),children:W?(0,a.jsxs)(a.Fragment,{children:[K("active"),(0,a.jsx)("span",{className:"capitalize font-medium",children:"Active"})]}):(0,a.jsxs)(a.Fragment,{children:[K("inactive"),(0,a.jsx)("span",{className:"capitalize font-medium",children:"inactive"})]})}),(0,a.jsx)("button",{onClick:$,className:"flex items-center text-[14px] mb-5 px-4 space-x-1 px-3 py-1 rounded-full border shadow-md transition-colors duration-300 ".concat(D?"bg-blue-300 text-white":"bg-red-200"),children:D?(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("span",{className:"capitalize font-medium",children:"System Agent"})}):(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("span",{className:"capitalize font-medium",children:"Non-System Agent"})})})]}),(0,a.jsxs)("div",{className:"mb-3 flex flex-row space-x-2",children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Creator: "}),(0,a.jsxs)("p",{className:"font-medium",children:[null==b?void 0:b.name," -"," ",(0,a.jsx)("span",{className:"text-gray-400 text-[14px]",children:null==b?void 0:b.email})]})]}),(0,a.jsxs)("div",{className:"mb-3 flex flex-row space-x-2",children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Total Credits used: "}),(0,a.jsxs)("p",{className:"font-medium",children:[k," Credit(s)"]})]}),(0,a.jsxs)("div",{className:"mb-3 flex flex-row space-x-2",children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Agent Earnings: "}),(0,a.jsxs)("p",{className:"font-medium",children:[S," Credit(s)"]})]}),(0,a.jsxs)("div",{className:"mb-3 flex flex-row space-x-2",children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Telex Earnings: "}),(0,a.jsxs)("p",{className:"font-medium",children:[_," Credit(s)"]})]}),(0,a.jsxs)("div",{className:"mb-3 flex flex-row space-x-2",children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Date Created: "}),(0,a.jsx)("p",{className:"font-medium",children:new Date(null==v?void 0:v.created_at).toLocaleString("en-US",{month:"long",day:"2-digit",year:"numeric"})})]}),(0,a.jsxs)("div",{className:"mb-3 flex flex-row space-x-2",children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Provider"}),(0,a.jsx)("p",{className:"font-medium",children:(null==v?void 0:null===(e=v.provider)||void 0===e?void 0:e.organization)?null==v?void 0:null===(t=v.provider)||void 0===t?void 0:t.organization:"Unknown"})]}),(0,a.jsxs)("div",{className:"border-b border-gray-200 mt-5 pb-5",children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Description"}),(0,a.jsx)("p",{className:"",children:null==v?void 0:v.app_description})]}),(0,a.jsxs)("div",{className:"border-b border-gray-200 mt-5 pb-5",children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Agent Prices"}),null==v?void 0:null===(s=v.prices)||void 0===s?void 0:s.map((e,t)=>(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("p",{className:"text-base mt-5",children:[(0,a.jsx)("span",{children:"Amount"}),": ",e.amount]}),(0,a.jsxs)("p",{className:"text-base",children:[(0,a.jsx)("span",{children:"Currency Code"}),": ",e.currency]}),(0,a.jsxs)("p",{className:"text-base",children:[(0,a.jsx)("span",{children:"Operation Type"}),": ",e.operation_type]})]},t))]}),(0,a.jsxs)("div",{className:"border-b border-gray-200 mt-5 pb-5 mb-5",children:[(0,a.jsx)("p",{className:"text-gray-500",children:"Agent Skills"}),null==v?void 0:null===(y=v.skills)||void 0===y?void 0:y.map((e,t)=>{var s;return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("p",{className:"text-base mt-5",children:[(0,a.jsx)("span",{children:"Name"}),": ",null==e?void 0:e.name]}),(0,a.jsxs)("p",{className:"text-base",children:[(0,a.jsx)("span",{children:"Tags"}),": ",null==e?void 0:null===(s=e.tags)||void 0===s?void 0:s.join(", ")]}),(0,a.jsxs)("p",{className:"text-base",children:[(0,a.jsx)("span",{children:"Description"}),": ",null==e?void 0:e.description]})]},t)})]})]}):(0,a.jsx)("div",{className:"w-full h-screen flex flex-col",children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center md:p-10 mt-6",children:[(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)(l.default,{src:"/image/empty-agent.svg",height:"100",width:"100",alt:"No app yet image",className:"w-30 md:w-[150px] h-64"})}),(0,a.jsx)("p",{className:"mt-[40px] md:flex text-xl md:text-2xl text-center font-semibold leading-8",children:"Agent not found"}),(0,a.jsx)("p",{className:"w-[300px] md:w-[481px] text-center text-sm font-light leading-6",children:"Ooops!, it seems this agent does not exist"})]})})]})})}}},function(e){e.O(0,[4269,8863,5300,2987,1269,7542,2344,1744],function(){return e(e.s=55025)}),_N_E=e.O()}]);