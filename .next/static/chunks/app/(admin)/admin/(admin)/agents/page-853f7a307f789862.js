(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[543],{68028:function(e,t,a){Promise.resolve().then(a.bind(a,54787))},23972:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(9824).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},33512:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(9824).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},25344:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(9824).Z)("CircleCheckBig",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2336:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(9824).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},20114:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(9824).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},78:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(9824).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},20384:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(9824).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},57292:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(9824).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},16669:function(e,t,a){"use strict";a.d(t,{default:function(){return n.a}});var s=a(6092),n=a.n(s)},54787:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return k}});var s=a(75376),n=a(32486),r=a(10952),l=a(16669),i=a(25344),d=a(2336),c=a(78),o=a(11492),u=a(25368),x=a(57292),m=a(20114);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let f=(0,a(9824).Z)("ArrowUpNarrowWide",[["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}],["path",{d:"M11 12h4",key:"q8tih4"}],["path",{d:"M11 16h7",key:"uosisv"}],["path",{d:"M11 20h10",key:"jvxblo"}]]);var p=a(20384),h=a(26242),g=a(1896),v=a(51178),j=a(23972),y=a(33512),b=a(73003),N=a(13352);let w=e=>{let[t,a]=(0,n.useState)(!1),[l,i]=(0,n.useState)(""),[d,c]=(0,n.useState)("");(0,n.useEffect)(()=>{c(localStorage.getItem("admintoken")||"")},[d]);let u=async t=>{if(t.preventDefault(),!l){N.Z.error("You must provide a valid JSON URL!!");return}a(!0);let s=await (0,r.xo)("/backoffice/agents/system",{json_url:l},d);((null==s?void 0:s.status)===200||(null==s?void 0:s.status)===201)&&(e.closeModal(),N.Z.success("Agent created successfully")),a(!1)};return(0,s.jsxs)("div",{className:"w-screen h-screen fixed top-0 right-0 flex items-center justify-center",children:[(0,s.jsx)("div",{onClick:()=>e.closeModal(),className:"w-full h-full absolute bg-black opacity-20"}),(0,s.jsxs)("div",{className:"bg-white w-[400px] lg:w-[600px] flex flex-col rounded-xl  z-10 gap-8",children:[(0,s.jsxs)("div",{className:"border-b border-gray-200 p-6 pb-4",children:[(0,s.jsx)("h1",{className:"text-[#1D2939] lg:text-xl text-lg font-semibold leading-normal",children:"Create System Agent"}),(0,s.jsx)("div",{className:"flex gap-4 mt-4",children:(0,s.jsx)("button",{className:"text-sm font-medium border-b-2 border-[#4A90E2] text-[#4A90E2] pb-2 ",children:"JSON URL"})})]}),(0,s.jsxs)("form",{onSubmit:u,className:"w-full px-5",children:[(0,s.jsx)("div",{className:"flex flex-col gap-[16px]",children:(0,s.jsxs)("div",{className:"w-full flex flex-col gap-[8px] relative",children:[(0,s.jsx)("label",{htmlFor:"url",className:"text-[14px] font-[400] leading-[21px]",children:"JSON URL"}),(0,s.jsx)("div",{className:"w-full flex flex-col gap-[2px]",children:(0,s.jsx)("input",{value:l,onChange:e=>{i(e.target.value)},id:"url",type:"url",placeholder:"Enter agent json url",className:"w-full text-[12px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border border-[#D0D0FD]\n                                        outline-none rounded-md py-[13px] pl-[13px]"})})]})}),(0,s.jsx)("div",{className:"flex flex-col gap-[24px] mt-5 mb-10 pb-5",children:(0,s.jsx)("div",{className:"flex flex-col gap-[16px]",children:(0,s.jsx)(o.z,{type:"submit",variant:"default",className:"py-6 bg-[#7141F8] hover:bg-[#8760f8] text-white",children:t?(0,s.jsxs)("span",{className:"flex items-center gap-x-2",children:[(0,s.jsx)("span",{className:"animate-pulse",children:"Processing..."})," ",(0,s.jsx)(b.Z,{width:"20",height:"40"})]}):(0,s.jsx)("span",{children:"Create an Agent"})})})})]})]})]})};var k=function(){let[e,t]=(0,n.useState)("System Agent"),[a,b]=(0,n.useState)("Date Created"),[N,k]=(0,n.useState)(""),[_,z]=(0,n.useState)(!1),[A,Z]=(0,n.useState)([]),[C,S]=(0,n.useState)(!0),[M,F]=(0,n.useState)(!1),[R,E]=(0,n.useState)(1),[D,O]=(0,n.useState)(!1),[U,q]=(0,n.useState)(""),V=e=>"active"===e?(0,s.jsx)(i.Z,{className:"h-4 w-4 text-green-600"}):(0,s.jsx)(d.Z,{className:"h-4 w-4 text-yellow-600"}),P=async()=>{S(!0);let t=localStorage.getItem("admintoken");var s=!0;"system_agent"==e?s=!0:"organization_agent"==e&&(s=!1);var n="created_at";"date_created"==a?n="created_at":"credit_used"==a&&(n=a);try{let e=await (0,r.Gl)("/backoffice/agents/all?page=".concat(R,"&is_system=").concat(s,"&search=").concat(N,"&sort_by=").concat(n),t);console.log(e);let a=e.data;Z(a.data),F(!0),E(a.pagination[0].current_page)}catch(e){console.log(e),F(!1)}S(!1)};return(0,n.useEffect)(()=>{P()},[R,e,N,a]),(0,s.jsxs)(s.Fragment,{children:[D&&(0,s.jsx)(w,{closeModal:()=>{O(!1)}}),(0,s.jsxs)("div",{className:"mx-auto xl:max-w-[1440px] grow max-w-screen",children:[(0,s.jsxs)("div",{className:"mt-5 p-4",children:[(0,s.jsx)("h2",{className:"font-bold text-[20px]",children:"Custom Agents"}),(0,s.jsx)("p",{className:"text-gray-600 text-[14px]",children:"Welcome to Your Agents Dashboard — View, Manage, and Monitor All Agents in One Place"})]}),(0,s.jsxs)("div",{className:"flex justify-between mb-5 p-4",children:[(0,s.jsxs)("div",{className:"flex items-center relative w-[500px]",children:[(0,s.jsx)(x.Z,{className:"absolute left-3 text-gray-400 h-4 w-4"}),(0,s.jsx)("input",{type:"text",placeholder:"Search agents...",onChange:e=>q(e.target.value),onKeyDown:e=>{"Enter"===e.key&&k(U)},className:"w-full p-2 pl-9 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#7b50fb]"})]}),(0,s.jsxs)("div",{className:"flex flex-row space-x-2",children:[(0,s.jsx)("div",{className:"",children:(0,s.jsxs)(u.h_,{children:[(0,s.jsx)(u.$F,{asChild:!0,children:(0,s.jsxs)(o.z,{variant:"outline",size:"sm",children:[(0,s.jsx)(m.Z,{className:"mr-2 h-4 w-4 text-gray-400"}),e.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase())]})}),(0,s.jsxs)(u.AW,{side:"bottom",align:"start",children:[(0,s.jsx)(u.Ju,{children:"Filter Agents"}),(0,s.jsx)(u.VD,{}),(0,s.jsxs)(u._x,{value:e,onValueChange:e=>t(e),children:[(0,s.jsx)(u.qB,{value:"system_agent",children:"System Agents"}),(0,s.jsx)(u.qB,{value:"organization_agent",children:"Organization Agents"})]})]})]})}),(0,s.jsx)("div",{className:"",children:(0,s.jsxs)(u.h_,{children:[(0,s.jsx)(u.$F,{asChild:!0,children:(0,s.jsxs)(o.z,{variant:"outline",size:"sm",children:[(0,s.jsx)(f,{className:"mr-2 h-4 w-4 text-gray-400"}),a.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase())]})}),(0,s.jsxs)(u.AW,{side:"bottom",align:"start",children:[(0,s.jsx)(u.Ju,{children:"Sort Agents"}),(0,s.jsx)(u.VD,{}),(0,s.jsxs)(u._x,{value:a,onValueChange:e=>b(e),children:[(0,s.jsx)(u.qB,{value:"date_created",children:"Date Created"}),(0,s.jsx)(u.qB,{value:"credit_used",children:"Credit Used"}),(0,s.jsx)(u.qB,{value:"most_used",children:"Most Earning"})]})]})]})}),(0,s.jsx)("button",{onClick:()=>{(0,h.mu)(z),P()},"aria-label":"RefreshCw",className:"py-1 px-4 h-[37px] border bg-white border-gray-200 rounded-md hover:bg-gray-100 transition",children:(0,s.jsx)(p.Z,{className:"h-4 w-4 text-gray-500 transition-transform ".concat(_?"animate-spin":"")})}),(0,s.jsx)("button",{onClick:()=>{O(!0)},className:"py-1 px-4 h-[37px] border text-white bg-blue-400 border-gray-200 rounded-md hover:bg-blue-600 transition",children:"Create System Agent"})]})]}),C&&(0,s.jsx)(g.Z,{}),!C&&M?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"p-5 bg-white rounded-md border border-gray-100",children:(0,s.jsx)("div",{className:"space-y-4",children:null==A?void 0:A.map((e,t)=>{var a,n;return(0,s.jsxs)(l.default,{href:"organization"==e.source?"/admin/agents/organization/".concat(null==e?void 0:e.integration_id):"/admin/agents/integration/".concat(null==e?void 0:e.id),children:[(0,s.jsxs)("div",{className:"p-6 border rounded-lg space-y-4 hover gap-2 hover:bg-gray-50 transition",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)("div",{className:"p-2 bg-solar-green-100 rounded-full",children:(0,s.jsx)(c.Z,{className:"h-5 w-5 text-solar-green-600"})}),(0,s.jsx)("div",{children:(0,s.jsx)("h3",{className:"font-semibold",children:(null==e?void 0:e.app_name)||(null==e?void 0:e.name)})})]}),(0,s.jsx)("div",{className:"flex items-center space-x-1 px-3 py-1 rounded-full ".concat((null==e?void 0:e.is_active)?(0,h.z2)("active"):(0,h.z2)("inactive")),children:(null==e?void 0:e.is_active)?(0,s.jsxs)(s.Fragment,{children:[V("active"),(0,s.jsx)("span",{className:"capitalize font-medium",children:"Active"})]}):(0,s.jsxs)(s.Fragment,{children:[V("inactive"),(0,s.jsx)("span",{className:"capitalize font-medium",children:"inActive"})]})})]}),(0,s.jsxs)("div",{className:"flex flex-row space-x-2 pb-3",children:[(0,s.jsx)("div",{className:"flex items-center space-x-1 px-3 py-1 text-[10px] rounded-full ".concat((null==e?void 0:e.source)=="organization"?"bg-amber-500 text-white":"bg-gray-300"),children:(null==e?void 0:e.source)=="organization"?(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("span",{className:"capitalize font-medium",children:"Organization Agent"})}):(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("span",{className:"capitalize font-medium",children:"Non-Organization Agent"})})}),(0,s.jsx)("div",{className:"flex items-center text-[10px] space-x-1 px-3 py-1 rounded-full bg-gray-100",children:(0,s.jsxs)("span",{className:"capitalize font-medium",children:["Credits Used:"," ",(null==e?void 0:e.credit_used)?null==e?void 0:e.credit_used:0," ","Credits"]})}),(0,s.jsx)("div",{className:"flex items-center text-[10px] space-x-1 px-3 py-1 rounded-full bg-amber-100",children:(0,s.jsxs)("span",{className:"capitalize font-medium",children:["Agent Earnings:"," ",(null==e?void 0:e.maker_total_earning)?null==e?void 0:e.maker_total_earning:0," ","Credits"]})}),(0,s.jsx)("div",{className:"flex items-center text-[10px] space-x-1 px-3 py-1 rounded-full bg-green-100",children:(0,s.jsxs)("span",{className:"capitalize font-medium",children:["Telex Earnings:"," ",(null==e?void 0:e.telex_total_earning)?null==e?void 0:e.telex_total_earning:0," ","Credits"]})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-gray-500",children:"Agent Name"}),(0,s.jsx)("p",{className:"font-medium",children:(null==e?void 0:e.app_name)||(null==e?void 0:e.name)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-gray-500",children:"Provider"}),(0,s.jsx)("p",{className:"font-medium",children:(null==e?void 0:null===(a=e.provider)||void 0===a?void 0:a.organization)?null==e?void 0:null===(n=e.provider)||void 0===n?void 0:n.organization:"Unknown"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-gray-500",children:"Type"}),(null==e?void 0:e.is_paid)?(0,s.jsx)("p",{className:"font-medium",children:"Paid Agent"}):(0,s.jsx)("p",{className:"font-medium",children:"Free Agent"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-gray-500",children:"Date Created"}),(0,s.jsx)("p",{className:"font-medium",children:new Date(null==e?void 0:e.created_at).toLocaleString("en-US",{weekday:"long",month:"long",day:"2-digit",year:"numeric"})})]})]})]}),(0,s.jsx)("br",{})]},t)})})}),(0,s.jsxs)("div",{className:"flex items-center pt-5 mt-5 pb-10 mb-10 justify-around px-6 space-x-2 py-4",children:[(0,s.jsxs)(o.z,{onClick:()=>E(e=>Math.max(e-1,1)),disabled:1===R,variant:"outline",children:[(0,s.jsx)(j.Z,{className:"text-[#667085] w-4 h-4"})," Previous"]}),(0,s.jsx)("div",{className:"",children:(function(e,t){let a=[],s=Math.max(2,e-2),n=Math.min(t-1,e+2);a.push(1),s>2&&a.push("...");for(let e=s;e<=n;e++)a.push(e);return n<t-1&&a.push("..."),t>1&&a.push(t),a})(R,Math.ceil(A.length/10)).map((e,t)=>(0,s.jsx)("button",{className:"px-3 py-1 rounded ".concat(e===R?"bg-[#E6EAEF] font-medium":"hover:bg-gray-100"," ").concat("..."===e?"cursor-default":"cursor-pointer"),onClick:()=>"number"==typeof e&&E(e),disabled:"..."===e,children:e},t))}),(0,s.jsxs)(o.z,{onClick:()=>{E(e=>e<Math.ceil(A.length/10)?e+1:e)},disabled:R===Math.ceil(A.length/10),variant:"outline",className:"flex items-center gap-1",children:["Next ",(0,s.jsx)(y.Z,{className:"text-[#667085] w-4 h-4"})]})]})]}):(0,s.jsx)(v.Z,{title:"There are no Agents yet",msg:"Create, View, Manage, and Monitor All Agents in One Place",imgSrc:"/image/empty-agent.svg"})]})]})}},51178:function(e,t,a){"use strict";var s=a(75376);a(32486);var n=a(39713);t.Z=e=>{let{title:t,msg:a,imgSrc:r}=e;return(0,s.jsx)("div",{className:"w-full h-screen flex flex-col",children:(0,s.jsx)("div",{className:"w-full h-screen flex flex-col",children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center md:p-10 mt-6",children:[r&&(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)(n.default,{src:r,height:"100",width:"100",alt:"No app yet",className:"w-30 md:w-[150px] h-64"})}),(0,s.jsx)("p",{className:"mt-[40px] md:flex text-xl md:text-2xl text-center font-semibold leading-8",children:t}),(0,s.jsx)("p",{className:"w-[300px] md:w-[481px] text-center text-sm font-light leading-6",children:a})]})})})}},11492:function(e,t,a){"use strict";a.d(t,{d:function(){return d},z:function(){return c}});var s=a(75376),n=a(32486),r=a(91007),l=a(53447),i=a(58983);let d=(0,l.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=n.forwardRef((e,t)=>{let{className:a,variant:n,size:l,asChild:c=!1,...o}=e,u=c?r.g7:"button";return(0,s.jsx)(u,{className:(0,i.cn)(d({variant:n,size:l,className:a})),ref:t,...o})});c.displayName="Button"},25368:function(e,t,a){"use strict";a.d(t,{$F:function(){return o},AW:function(){return x},Ju:function(){return p},KM:function(){return g},VD:function(){return h},Xi:function(){return m},_x:function(){return u},h_:function(){return c},qB:function(){return f}});var s=a(75376),n=a(32486),r=a(18493),l=a(51888),i=a(28780),d=a(58983);let c=r.fC,o=r.xz;r.ZA,r.Uv,r.Tr;let u=r.Ee;n.forwardRef((e,t)=>{let{className:a,inset:n,children:i,...c}=e;return(0,s.jsxs)(r.fF,{ref:t,className:(0,d.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",n&&"pl-8",a),...c,children:[i,(0,s.jsx)(l.Z,{className:"ml-auto h-4 w-4"})]})}).displayName=r.fF.displayName,n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,s.jsx)(r.tu,{ref:t,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...n})}).displayName=r.tu.displayName;let x=n.forwardRef((e,t)=>{let{className:a,sideOffset:n=4,...l}=e;return(0,s.jsx)(r.Uv,{children:(0,s.jsx)(r.VY,{ref:t,sideOffset:n,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...l})})});x.displayName=r.VY.displayName;let m=n.forwardRef((e,t)=>{let{className:a,inset:n,...l}=e;return(0,s.jsx)(r.ck,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",n&&"pl-8",a),...l})});m.displayName=r.ck.displayName,n.forwardRef((e,t)=>{let{className:a,children:n,checked:l,...c}=e;return(0,s.jsxs)(r.oC,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:l,...c,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(r.wU,{children:(0,s.jsx)(i.Z,{className:"h-4 w-4"})})}),n]})}).displayName=r.oC.displayName;let f=n.forwardRef((e,t)=>{let{className:a,children:n,...l}=e;return(0,s.jsxs)(r.Rk,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...l,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(r.wU,{children:(0,s.jsx)("div",{className:"bg-gradient-to-b from-[#8860F8] to-[#7141F8] w-5 h-5 rounded-sm flex items-center justify-center",children:(0,s.jsx)(i.Z,{color:"#ffffff"})})})}),n]})});f.displayName=r.Rk.displayName;let p=n.forwardRef((e,t)=>{let{className:a,inset:n,...l}=e;return(0,s.jsx)(r.__,{ref:t,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",n&&"pl-8",a),...l})});p.displayName=r.__.displayName;let h=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,s.jsx)(r.Z0,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",a),...n})});h.displayName=r.Z0.displayName;let g=e=>{let{className:t,...a}=e;return(0,s.jsx)("span",{className:(0,d.cn)("ml-auto text-xs tracking-widest opacity-60",t),...a})};g.displayName="DropdownMenuShortcut"},58983:function(e,t,a){"use strict";a.d(t,{cn:function(){return r},k:function(){return l}});var s=a(89824),n=a(97215);function r(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.m6)((0,s.W)(t))}let l=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"},53447:function(e,t,a){"use strict";a.d(t,{j:function(){return l}});var s=a(89824);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,r=s.W,l=(e,t)=>a=>{var s;if((null==t?void 0:t.variants)==null)return r(e,null==a?void 0:a.class,null==a?void 0:a.className);let{variants:l,defaultVariants:i}=t,d=Object.keys(l).map(e=>{let t=null==a?void 0:a[e],s=null==i?void 0:i[e];if(null===t)return null;let r=n(t)||n(s);return l[e][r]}),c=a&&Object.entries(a).reduce((e,t)=>{let[a,s]=t;return void 0===s||(e[a]=s),e},{});return r(e,d,null==t?void 0:null===(s=t.compoundVariants)||void 0===s?void 0:s.reduce((e,t)=>{let{class:a,className:s,...n}=t;return Object.entries(n).every(e=>{let[t,a]=e;return Array.isArray(a)?a.includes({...i,...c}[t]):({...i,...c})[t]===a})?[...e,a,s]:e},[]),null==a?void 0:a.class,null==a?void 0:a.className)}}},function(e){e.O(0,[4269,8863,7140,6092,4144,5300,5614,2987,6343,6329,9254,3975,1269,7542,2344,1744],function(){return e(e.s=68028)}),_N_E=e.O()}]);