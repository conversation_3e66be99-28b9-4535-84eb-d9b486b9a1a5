(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2627],{49616:function(e,t,r){Promise.resolve().then(r.bind(r,61571))},9824:function(e,t,r){"use strict";r.d(t,{Z:function(){return o}});var n=r(32486);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:s=2,absoluteStrokeWidth:o,className:u="",children:c,iconNode:d,...f}=e;return(0,n.createElement)("svg",{ref:t,...l,width:a,height:a,stroke:r,strokeWidth:o?24*Number(s)/Number(a):s,className:i("lucide",u),...f},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),o=(e,t)=>{let r=(0,n.forwardRef)((r,l)=>{let{className:o,...u}=r;return(0,n.createElement)(s,{ref:l,iconNode:t,className:i("lucide-".concat(a(e)),o),...u})});return r.displayName="".concat(e),r}},78710:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},35751:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},40463:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},39713:function(e,t,r){"use strict";r.d(t,{default:function(){return a.a}});var n=r(74033),a=r.n(n)},16669:function(e,t,r){"use strict";r.d(t,{default:function(){return a.a}});var n=r(6092),a=r.n(n)},47411:function(e,t,r){"use strict";var n=r(13362);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},74033:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return o},getImageProps:function(){return s}});let n=r(60723),a=r(25738),i=r(28863),l=n._(r(44543));function s(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let o=i.Image},61571:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return y}});var n=r(75376),a=r(47411),i=r(32486),l=r(97712),s=r(97220),o=r(78710),u=r(9824);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,u.Z)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]),d=(0,u.Z)("BriefcaseBusiness",[["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2",key:"1ksdt3"}],["path",{d:"M22 13a18.15 18.15 0 0 1-20 0",key:"12hx5q"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]),f=(0,u.Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);var h=r(40463),m=r(35751);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let p=(0,u.Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);var g=r(16669),x=()=>{let e=(0,a.usePathname)(),{state:t,dispatch:r}=(0,i.useContext)(s.R),u=[{id:"home",icon:o.Z,label:"Dashboard",link:"/admin/dashboard"},{id:"agents",icon:c,label:"Agents",link:"/admin/agents"},{id:"organizations",icon:d,label:"Organizations",link:"/admin/organizations"},{id:"credits_report",icon:f,label:"Credits Report",link:"/admin/credits-report"},{id:"users",icon:h.Z,label:"Users",link:"/admin/users"},{id:"settings",icon:m.Z,label:"Settings",link:"/admin/settings"},{id:"logout",icon:p,label:"Log Out",link:""}];return(0,n.jsx)("div",{className:"fixed top-0 z-50 md:z-0 md:translate-x-0 transition-transform duration-300 ease-in-out\n               ".concat((null==t?void 0:t.openSidebar)===!0?"translate-x-0":"-translate-x-40"),children:(0,n.jsx)("div",{className:"h-[100vh] w-[250px] p-[8px] -z-50\n        md:flex flex-col justify-start",children:(0,n.jsx)("div",{className:"h-full w-full text-gray-200 rounded-[12px] z-auto bg-[#7b50fb] p-4",children:(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsxs)("div",{className:"border-b text-[15px] border-gray-200 pb-5 mt-4 flex flex-col items-center text-center",children:[(0,n.jsx)(g.default,{href:"",className:"flex flex-col items-center group mb-2 space-y-1 w-full p-[5px] font-semibold scale-[1.1]",children:(0,n.jsx)("span",{className:"bg-[#7b50fb] p-[4px] rounded-[7px] flex items-center justify-center",children:(0,n.jsx)(h.Z,{strokeWidth:1.2,className:"fill-white stroke-[#7b50fb]"})})}),(0,n.jsx)("p",{className:"font-bold",children:"Ganiu Jamiu"}),(0,n.jsx)("p",{children:"<EMAIL>"})]}),(0,n.jsxs)("div",{className:"flex flex-col items-center mt-6 w-full text-[12px]",onClick:()=>r({type:l.a.OPEN_SIDEBAR,payload:!1}),children:[u.map(t=>{let r="home"===t.id?"/admin/dashboard"===e:e.includes(t.id);return(0,n.jsxs)(g.default,{href:t.link,className:"flex flex-row group mb-2 space-y-1 w-full p-[5px] ".concat(r?"font-semibold":"hover:font-semibold "),children:[(0,n.jsx)("span",{className:"p-[4px] rounded-[7px]  ".concat(r?"bg-[#7b50fb]":"group-hover:bg-[#7b50fb]"),children:(0,n.jsx)(t.icon,{strokeWidth:1.2,className:"".concat(r?"fill-white stroke-[#7b50fb]":"group-hover:fill-white group-hover:stroke-[#7b50fb]")})}),(0,n.jsx)("span",{className:"text-[12px] ml-3",children:t.label})]},t.id)}),";"]})]})})})})},v=r(20945);function y(e){let{children:t}=e,[r,o]=(0,i.useState)(!0),u=(0,a.useRouter)(),{dispatch:c}=(0,i.useContext)(s.R);if((0,i.useEffect)(()=>{let e=localStorage.getItem("admintoken");if(c({type:l.a.TOKEN,payload:e}),!e){u.push("/admin/login");return}o(!1)},[c,u]),!r)return(0,n.jsx)(s.DataProvider,{children:(0,n.jsx)(v.Z,{children:(0,n.jsxs)("div",{className:"w-full flex relative bg-gray-100",children:[(0,n.jsx)(x,{}),(0,n.jsx)("div",{className:"md:ml-[250px] w-full relative",children:t})]})})})}},20945:function(e,t,r){"use strict";var n=r(75376),a=r(39713),i=r(32486),l=r(11492);class s extends i.Component{static getDerivedStateFromError(e){return{hasError:!0}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error",e,t)}resetError(){this.setState({hasError:!1}),window.location.href="/client"}render(){return this.state.hasError?(0,n.jsx)("div",{children:(0,n.jsxs)("div",{className:"max-w-full h-screen flex flex-col items-center justify-center gap-5",children:[(0,n.jsx)(a.default,{src:"/images/error-img.svg",height:"100",width:"400",alt:"error image",className:"w-60 h-40"}),(0,n.jsx)("p",{className:"text-xl font-semibold leading-6 text-slate-600",children:"Ooops!! Something went wrong"}),(0,n.jsx)("p",{className:"text-neutral-700 text-center leading-6",children:"We cannot load this page at the moment"}),(0,n.jsx)(l.z,{onClick:this.resetError,className:"bg-gradient-to-b from-[#8760f8] to-[#7141f8] py-2 px-6 text-white",children:"Go Back to Dashboard"})]})}):this.props.children}constructor(e){super(e),this.state={hasError:!1},this.resetError=this.resetError.bind(this)}}t.Z=s},11492:function(e,t,r){"use strict";r.d(t,{d:function(){return o},z:function(){return u}});var n=r(75376),a=r(32486),i=r(91007),l=r(53447),s=r(58983);let o=(0,l.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=a.forwardRef((e,t)=>{let{className:r,variant:a,size:l,asChild:u=!1,...c}=e,d=u?i.g7:"button";return(0,n.jsx)(d,{className:(0,s.cn)(o({variant:a,size:l,className:r})),ref:t,...c})});u.displayName="Button"},58983:function(e,t,r){"use strict";r.d(t,{cn:function(){return i},k:function(){return l}});var n=r(89824),a=r(97215);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,n.W)(t))}let l=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"},29626:function(e,t,r){"use strict";r.d(t,{F:function(){return i},e:function(){return l}});var n=r(32486);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}function l(...e){return n.useCallback(i(...e),e)}},91007:function(e,t,r){"use strict";r.d(t,{Z8:function(){return l},g7:function(){return s},sA:function(){return u}});var n=r(32486),a=r(29626),i=r(75376);function l(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){let e,l;let s=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,o=function(e,t){let r={...t};for(let n in t){let a=e[n],i=t[n];/^on[A-Z]/.test(n)?a&&i?r[n]=(...e)=>{let t=i(...e);return a(...e),t}:a&&(r[n]=a):"style"===n?r[n]={...a,...i}:"className"===n&&(r[n]=[a,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(o.ref=t?(0,a.F)(t,s):s),n.cloneElement(r,o)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:a,...l}=e,s=n.Children.toArray(a),o=s.find(c);if(o){let e=o.props.children,a=s.map(t=>t!==o?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,i.jsx)(t,{...l,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var s=l("Slot"),o=Symbol("radix.slottable");function u(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=o,t}function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},53447:function(e,t,r){"use strict";r.d(t,{j:function(){return l}});var n=r(89824);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.W,l=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:s}=t,o=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],n=null==s?void 0:s[e];if(null===t)return null;let i=a(t)||a(n);return l[e][i]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return i(e,o,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...u}[t]):({...s,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}},function(e){e.O(0,[8863,7140,6092,7220,7542,2344,1744],function(){return e(e.s=49616)}),_N_E=e.O()}]);