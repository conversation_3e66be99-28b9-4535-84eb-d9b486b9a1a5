(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1474],{94646:function(e,n,s){Promise.resolve().then(s.bind(s,53062))},53062:function(e,n,s){"use strict";s.r(n);var t=s(75376);n.default=function(){return(0,t.jsx)(t.Fragment,{children:(0,t.jsx)("div",{className:"mx-auto xl:max-w-[1440px] grow max-w-screen",children:(0,t.jsxs)("div",{className:"mt-5 p-4",children:[(0,t.jsx)("h2",{className:"font-bold text-[20px]",children:"Credits Report"}),(0,t.jsx)("p",{className:"text-gray-600 text-[14px]",children:"Welcome to Your Agents Dashboard — View, Manage, and Monitor All Agents in One Place"})]})})})}}},function(e){e.O(0,[7542,2344,1744],function(){return e(e.s=94646)}),_N_E=e.O()}]);