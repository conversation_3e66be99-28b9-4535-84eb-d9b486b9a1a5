(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9368],{46609:function(e,t,n){Promise.resolve().then(n.bind(n,23077))},28780:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},9823:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]])},22397:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},6540:function(e,t,n){"use strict";var r=n(75376);n(32486),t.Z=()=>(0,r.jsxs)("div",{className:"mb-2",children:[(0,r.jsx)("h1",{className:"text-base font-semibold p-4 lg:px-8",children:"Settings"}),(0,r.jsx)("hr",{className:"bg-[#E6EAEF] border"})]})},23077:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return w}});var r=n(75376),a=n(32486),o=n(6540),s=n(11492),l=n(74258),i=n(58983);let d=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",n),...a})});d.displayName="Card",a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",n),...a})}).displayName="CardHeader",a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",n),...a})}).displayName="CardTitle",a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",n),...a})}).displayName="CardDescription";let c=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",n),...a})});c.displayName="CardContent",a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",n),...a})}).displayName="CardFooter";var u=n(9823),m=n(16006),f=n(22397),p=n(28780),g=n(13352),v=n(86418),h=n(97220),x=n(97712),b=e=>{let{isOpen:t,onClose:n,isNew:o=!1,roleId:l,roleName:i,description:d,permissions:c,onRoleCreated:u}=e,[b,y]=(0,a.useState)(i||""),[w,j]=(0,a.useState)(d||null),[N,_]=(0,a.useState)([{id:"can_view_billing",name:"View billing settings",enabled:!1},{id:"can_invite_members",name:"Invite members",enabled:!1},{id:"can_remove_people_from_organization",name:"Remove members",enabled:!1},{id:"can_create_webhooks",name:"Create webhooks",enabled:!1},{id:"can_create_channel",name:"Create channels",enabled:!1},{id:"can_view_channels",name:"View channels",enabled:!0},{id:"can_comment_on_threads",name:"Comment on threads",enabled:!1},{id:"can_create_custom_role",name:"Create custom roles",enabled:!1},{id:"can_change_user_org_role",name:"Change user organization role",enabled:!1}]),[C,R]=(0,a.useState)(!1),{state:k,dispatch:D}=(0,a.useContext)(h.R);(0,a.useEffect)(()=>{!o&&c&&_(e=>e.map(e=>({...e,enabled:Object.keys(c).includes(e.id)&&c[e.id]})))},[o,c]);let z=e=>{_(t=>t.map(t=>t.id===e?{...t,enabled:!t.enabled}:t))},S=async()=>{R(!0);try{if(o){n(),g.Z.success("Role created successfully");let t=await (0,v.xo)("/organisations/".concat(k.orgId,"/roles"),{name:b,description:w||""});if((null==t?void 0:t.status)===200||(null==t?void 0:t.status)===201){var e;let n=(null===(e=t.data)||void 0===e?void 0:e.data)||t.data;u&&u(n)}else throw Error("Failed to create role")}else{let e=await (0,v.an)("/organisations/".concat(k.orgId,"/roles/").concat(l),{name:b,description:w||""});console.log(e),g.Z.success("Role updated successfully"),D({type:x.a.ORG_ROLES,payload:null==k?void 0:k.orgRoles}),n()}}catch(e){if(o){let e=((null==k?void 0:k.orgRoles)||[]).filter(e=>!e.id.startsWith("temp-"));D({type:x.a.ORG_ROLES,payload:e}),g.Z.error("Failed to create role")}else g.Z.error("Failed to update role")}finally{R(!1)}};return(0,r.jsx)(m.Vq,{open:t,onOpenChange:n,children:(0,r.jsx)(m.t9,{className:"backdrop-blur-sm bg-black/50",children:(0,r.jsx)(m.cZ,{className:"sm:max-w-2xl bg-white rounded-lg shadow-xl border-0 p-0 overflow-hidden max-h-[90vh] overflow-y-auto",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(m.fK,{className:"px-6 pt-6 pb-4 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(m.$N,{className:"text-xl font-semibold text-gray-900",children:o?"Create New Role":"Update Role"}),(0,r.jsx)(s.z,{variant:"ghost",size:"icon",onClick:n,className:"p-1 rounded-md hover:bg-gray-100",children:(0,r.jsx)(f.Z,{size:20,className:"text-gray-500"})})]})}),(0,r.jsxs)("div",{className:"px-6 py-6 space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-900",children:"Role Name"}),(0,r.jsx)("input",{type:"text",value:b,onChange:e=>y(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"e.g. Manager"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-900",children:"Role Description"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("textarea",{value:w||"",onChange:e=>j(e.target.value),maxLength:36,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none",rows:3,placeholder:"e.g. Tier 2 - Second- in-command"}),(0,r.jsx)("div",{className:"absolute bottom-2 right-2 text-xs text-gray-400",children:"".concat(w?w.length:0,"/36")})]})]}),(0,r.jsx)("div",{className:"space-y-4",children:N.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:"checkbox",id:e.id,checked:e.enabled,onChange:()=>z(e.id),className:"sr-only"}),(0,r.jsx)("label",{htmlFor:e.id,className:"flex items-center justify-center w-5 h-5 border rounded cursor-pointer ".concat(e.enabled?"bg-white border-blue-600":"bg-white border-gray-300"),children:e.enabled&&(0,r.jsx)(p.Z,{className:"h-3 w-3 text-[#7141F8]"})})]}),(0,r.jsx)("label",{htmlFor:e.id,className:"text-sm font-medium text-gray-900 cursor-pointer",children:e.name})]},e.id))})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 my-4"}),(0,r.jsxs)("div",{className:"text-sm text-gray-500 px-5",children:[(0,r.jsx)("span",{className:"font-medium",children:"Note:"})," This affects your team's access."]}),(0,r.jsxs)("div",{className:"px-6 py-4 bg-white flex justify-end space-x-3",children:[(0,r.jsx)(s.z,{variant:"outline",onClick:n,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,r.jsx)(s.z,{onClick:S,disabled:C,className:"bg-[#7141F8] text-white rounded-md hover:bg-[#7141F8]/80 transition-colors",children:C?"Saving...":"Save Changes"})]})]})})})})},y=e=>{let{id:t,roleName:n,description:o,actions:i,permissions:m}=e,[f,p]=a.useState(!1);return(0,r.jsxs)(d,{className:"w-full ",children:[(0,r.jsx)(b,{isOpen:f,onClose:()=>p(!1),isNew:!1,roleId:t,roleName:n,description:o,permissions:m}),(0,r.jsx)(c,{className:"p-3",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:n}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:o})]}),(0,r.jsx)(s.z,{variant:"ghost",size:"sm",className:"text-gray-500 hover:text-gray-700 border rounded-md",onClick:()=>p(!0),children:(0,r.jsx)(u.Z,{className:"h-4 w-4"})})]}),(0,r.jsx)("div",{className:"border-t border-dotted border-gray-200 [border-style:dotted_8px]"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-3",children:i.map((e,t)=>(0,r.jsx)(l.C,{className:"text-sm text-gray-700 border border-[#E6EAEF] bg-[#F6F7F9] w-fit rounded-sm",children:e.label},t))})]})})]})},w=()=>{let[e,t]=a.useState(!1),{state:n,dispatch:l}=(0,a.useContext)(h.R),{orgRoles:i}=n,d=e=>{let t=[];return e.can_remove_people_from_organization&&t.push({label:"Remove members from organization"}),e.can_invite_members&&t.push({label:"Invite members"}),e.can_create_custom_role&&t.push({label:"Create custom roles"}),e.can_create_channel&&t.push({label:"Create channels"}),e.can_comment_on_threads&&t.push({label:"Comment on threads"}),e.can_view_billing&&t.push({label:"View billing"}),e.can_create_webhooks&&t.push({label:"Create webhooks"}),e.can_view_channels&&t.push({label:"View channels"}),e.can_change_user_org_role&&t.push({label:"Change user organization role"}),t};return console.log("orgRoles",i),(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsx)(o.Z,{}),(0,r.jsx)(b,{isOpen:e,onClose:()=>t(!1),isNew:!0,onRoleCreated:e=>{l({type:x.a.ORG_ROLES,payload:[...i,{...e,permissions:{id:"temp-perm-".concat(Date.now()),role_id:"temp-".concat(Date.now()),permission_list:{can_remove_people_from_organization:!1,can_invite_members:!1,can_create_custom_role:!1,can_create_channel:!1,can_comment_on_threads:!1,can_view_billing:!1,can_create_webhooks:!1,can_view_channels:!1,can_change_user_org_role:!1},is_default:!1}}]})}}),(0,r.jsxs)("div",{className:"p-4 lg:px-8 w-full",children:[(0,r.jsxs)("div",{className:"mb-6 flex flex-col sm:flex-row justify-between items-start gap-4 sm:gap-0",children:[(0,r.jsxs)("div",{className:"w-full sm:w-auto",children:[(0,r.jsx)("h1",{className:"text-base font-semibold",children:"Your Team Access"}),(0,r.jsx)("p",{className:"text-sm text-[#344054] max-w-[500px]",children:"Manage who can access what. Create roles or update permissions to stay in control."})]}),(0,r.jsx)(s.z,{className:"w-full sm:w-auto px-4 py-2 bg-[#7141F8] text-white rounded-md hover:bg-[#7141F8]/80 transition-colors",onClick:()=>t(!0),children:"New Role"})]}),(0,r.jsx)("div",{className:"flex flex-col gap-5",children:null==i?void 0:i.map(e=>(0,r.jsx)(y,{roleName:e.name,description:e.description,actions:d(e.permissions.permission_list),permissions:e.permissions.permission_list,id:e.id},e.id))})]})]})}},74258:function(e,t,n){"use strict";n.d(t,{C:function(){return l}});var r=n(75376);n(32486);var a=n(53447),o=n(58983);let s=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:n,...a}=e;return(0,r.jsx)("div",{className:(0,o.cn)(s({variant:n}),t),...a})}},11492:function(e,t,n){"use strict";n.d(t,{d:function(){return i},z:function(){return d}});var r=n(75376),a=n(32486),o=n(91007),s=n(53447),l=n(58983);let i=(0,s.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:n,variant:a,size:s,asChild:d=!1,...c}=e,u=d?o.g7:"button";return(0,r.jsx)(u,{className:(0,l.cn)(i({variant:a,size:s,className:n})),ref:t,...c})});d.displayName="Button"},16006:function(e,t,n){"use strict";n.d(t,{$N:function(){return g},Be:function(){return v},GG:function(){return c},Vq:function(){return l},cN:function(){return p},cZ:function(){return m},fK:function(){return f},hg:function(){return i},t9:function(){return u}});var r=n(75376),a=n(32486),o=n(94797),s=n(58983);let l=o.fC,i=o.xz,d=o.h_,c=o.x8,u=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)(o.aV,{ref:t,className:(0,s.cn)("fixed inset-0 z-50 bg-black/50  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",n),...a})});u.displayName=o.aV.displayName;let m=a.forwardRef((e,t)=>{let{className:n,children:a,...l}=e;return(0,r.jsxs)(d,{children:[(0,r.jsx)(u,{}),(0,r.jsx)(o.VY,{ref:t,className:(0,s.cn)("fixed left-[50%] top-[50%] z-50 grid w-[90%] max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",n),...l,children:a})]})});m.displayName=o.VY.displayName;let f=e=>{let{className:t,...n}=e;return(0,r.jsx)("div",{className:(0,s.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...n})};f.displayName="DialogHeader";let p=e=>{let{className:t,...n}=e;return(0,r.jsx)("div",{className:(0,s.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...n})};p.displayName="DialogFooter";let g=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)(o.Dx,{ref:t,className:(0,s.cn)("text-lg font-semibold leading-none tracking-tight",n),...a})});g.displayName=o.Dx.displayName;let v=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,r.jsx)(o.dk,{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",n),...a})});v.displayName=o.dk.displayName},58983:function(e,t,n){"use strict";n.d(t,{cn:function(){return o},k:function(){return s}});var r=n(89824),a=n(97215);function o(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,a.m6)((0,r.W)(t))}let s=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"},86418:function(e,t,n){"use strict";n.d(t,{Gl:function(){return s},HH:function(){return i},Q_:function(){return d},_x:function(){return u},an:function(){return m},i1:function(){return p},jx:function(){return f},x9:function(){return c},xo:function(){return l}});var r=n(20818),a=n(13352);let o=n(18648).env.NEXT_PUBLIC_BASE_URL,s=async e=>{let t=localStorage.getItem("token")||"";try{return await r.Z.get(o+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var n,a,s;return((null==e?void 0:null===(n=e.response)||void 0===n?void 0:n.status)===401||(null==e?void 0:null===(s=e.response)||void 0===s?void 0:null===(a=s.data)||void 0===a?void 0:a.status_code)===401)&&(localStorage.clear(),window.location.href="/auth/login"),e}},l=async(e,t)=>{let n=localStorage.getItem("token")||"";try{return await r.Z.post(o+e,t,{headers:{Authorization:"Bearer ".concat(n),"Content-Type":"application/json"}})}catch(e){var s,l,i,d,c;return a.Z.error(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(s=l.data)||void 0===s?void 0:s.message),(null==e?void 0:null===(i=e.response)||void 0===i?void 0:i.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},i=async(e,t)=>{let n=localStorage.getItem("token")||"";try{return await r.Z.post(o+e,t,{headers:{Authorization:"Bearer ".concat(n),"Content-Type":"application/json"}})}catch(e){var s,l;return a.Z.error(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(s=l.data)||void 0===s?void 0:s.message),e}},d=async(e,t)=>{let n=localStorage.getItem("token")||"";try{return await r.Z.post(o+e,t,{headers:{Authorization:"Bearer ".concat(n),"Content-Type":"application/json"}})}catch(e){return e}},c=async(e,t)=>{let n=localStorage.getItem("token")||"";try{return await r.Z.post(o+e,t,{headers:{Authorization:"Bearer ".concat(n),"Content-Type":"multipart/form-data"}})}catch(e){var s,l,i,d,c;return a.Z.error(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(s=l.data)||void 0===s?void 0:s.message),(null==e?void 0:null===(i=e.response)||void 0===i?void 0:i.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},u=async(e,t)=>{let n=localStorage.getItem("token")||"";try{return await r.Z.patch(o+e,t,{headers:{Authorization:"Bearer ".concat(n),"Content-Type":"application/json"}})}catch(e){var s,l,i,d,c;return a.Z.error(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(s=l.data)||void 0===s?void 0:s.message),(null==e?void 0:null===(i=e.response)||void 0===i?void 0:i.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},m=async(e,t)=>{let n=localStorage.getItem("token")||"";try{return await r.Z.put(o+e,t,{headers:{Authorization:"Bearer ".concat(n),"Content-Type":"application/json"}})}catch(e){var s,l,i,d,c;return a.Z.error(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(s=l.data)||void 0===s?void 0:s.message),(null==e?void 0:null===(i=e.response)||void 0===i?void 0:i.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},f=async e=>{let t=localStorage.getItem("token")||"";try{return await r.Z.delete(o+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var n,s;return a.Z.error(null==e?void 0:null===(s=e.response)||void 0===s?void 0:null===(n=s.data)||void 0===n?void 0:n.message),e}},p=async e=>{let t=localStorage.getItem("token")||"";try{return await r.Z.delete(o+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){return e}}},94797:function(e,t,n){"use strict";n.d(t,{Dx:function(){return er},VY:function(){return en},aV:function(){return et},dk:function(){return ea},fC:function(){return Q},h_:function(){return ee},x8:function(){return eo},xz:function(){return J}});var r=n(32486),a=n(20100),o=n(29626),s=n(32192),l=n(21971),i=n(31413),d=n(35878),c=n(5887),u=n(79872),m=n(53486),f=n(89801),p=n(67058),g=n(25081),v=n(15623),h=n(91007),x=n(75376),b="Dialog",[y,w]=(0,s.b)(b),[j,N]=y(b),_=e=>{let{__scopeDialog:t,children:n,open:a,defaultOpen:o,onOpenChange:s,modal:d=!0}=e,c=r.useRef(null),u=r.useRef(null),[m,f]=(0,i.T)({prop:a,defaultProp:null!=o&&o,onChange:s,caller:b});return(0,x.jsx)(j,{scope:t,triggerRef:c,contentRef:u,contentId:(0,l.M)(),titleId:(0,l.M)(),descriptionId:(0,l.M)(),open:m,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:d,children:n})};_.displayName=b;var C="DialogTrigger",R=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,s=N(C,n),l=(0,o.e)(t,s.triggerRef);return(0,x.jsx)(f.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":U(s.open),...r,ref:l,onClick:(0,a.M)(e.onClick,s.onOpenToggle)})});R.displayName=C;var k="DialogPortal",[D,z]=y(k,{forceMount:void 0}),S=e=>{let{__scopeDialog:t,forceMount:n,children:a,container:o}=e,s=N(k,t);return(0,x.jsx)(D,{scope:t,forceMount:n,children:r.Children.map(a,e=>(0,x.jsx)(m.z,{present:n||s.open,children:(0,x.jsx)(u.h,{asChild:!0,container:o,children:e})}))})};S.displayName=k;var Z="DialogOverlay",I=r.forwardRef((e,t)=>{let n=z(Z,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,o=N(Z,e.__scopeDialog);return o.modal?(0,x.jsx)(m.z,{present:r||o.open,children:(0,x.jsx)(O,{...a,ref:t})}):null});I.displayName=Z;var F=(0,h.Z8)("DialogOverlay.RemoveScroll"),O=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=N(Z,n);return(0,x.jsx)(g.Z,{as:F,allowPinchZoom:!0,shards:[a.contentRef],children:(0,x.jsx)(f.WV.div,{"data-state":U(a.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),E="DialogContent",A=r.forwardRef((e,t)=>{let n=z(E,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,o=N(E,e.__scopeDialog);return(0,x.jsx)(m.z,{present:r||o.open,children:o.modal?(0,x.jsx)(V,{...a,ref:t}):(0,x.jsx)(T,{...a,ref:t})})});A.displayName=E;var V=r.forwardRef((e,t)=>{let n=N(E,e.__scopeDialog),s=r.useRef(null),l=(0,o.e)(t,n.contentRef,s);return r.useEffect(()=>{let e=s.current;if(e)return(0,v.Ry)(e)},[]),(0,x.jsx)(M,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.M)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,a.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,a.M)(e.onFocusOutside,e=>e.preventDefault())})}),T=r.forwardRef((e,t)=>{let n=N(E,e.__scopeDialog),a=r.useRef(!1),o=r.useRef(!1);return(0,x.jsx)(M,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,s;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented||(a.current||null===(s=n.triggerRef.current)||void 0===s||s.focus(),t.preventDefault()),a.current=!1,o.current=!1},onInteractOutside:t=>{var r,s;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(a.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let l=t.target;(null===(s=n.triggerRef.current)||void 0===s?void 0:s.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),M=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:a,onOpenAutoFocus:s,onCloseAutoFocus:l,...i}=e,u=N(E,n),m=r.useRef(null),f=(0,o.e)(t,m);return(0,p.EW)(),(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(c.M,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:s,onUnmountAutoFocus:l,children:(0,x.jsx)(d.XB,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":U(u.open),...i,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(X,{titleId:u.titleId}),(0,x.jsx)($,{contentRef:m,descriptionId:u.descriptionId})]})]})}),B="DialogTitle",P=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=N(B,n);return(0,x.jsx)(f.WV.h2,{id:a.titleId,...r,ref:t})});P.displayName=B;var W="DialogDescription",L=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=N(W,n);return(0,x.jsx)(f.WV.p,{id:a.descriptionId,...r,ref:t})});L.displayName=W;var G="DialogClose",H=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=N(G,n);return(0,x.jsx)(f.WV.button,{type:"button",...r,ref:t,onClick:(0,a.M)(e.onClick,()=>o.onOpenChange(!1))})});function U(e){return e?"open":"closed"}H.displayName=G;var Y="DialogTitleWarning",[q,K]=(0,s.k)(Y,{contentName:E,titleName:B,docsSlug:"dialog"}),X=e=>{let{titleId:t}=e,n=K(Y),a="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&!document.getElementById(t)&&console.error(a)},[a,t]),null},$=e=>{let{contentRef:t,descriptionId:n}=e,a=K("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(a.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(o)},[o,t,n]),null},Q=_,J=R,ee=S,et=I,en=A,er=P,ea=L,eo=H},53447:function(e,t,n){"use strict";n.d(t,{j:function(){return s}});var r=n(89824);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=r.W,s=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return o(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:s,defaultVariants:l}=t,i=Object.keys(s).map(e=>{let t=null==n?void 0:n[e],r=null==l?void 0:l[e];if(null===t)return null;let o=a(t)||a(r);return s[e][o]}),d=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return o(e,i,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...a}=t;return Object.entries(a).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...l,...d}[t]):({...l,...d})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}}},function(e){e.O(0,[7140,4144,5300,6343,7220,7542,2344,1744],function(){return e(e.s=46609)}),_N_E=e.O()}]);