(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[279,9513],{77967:function(e,t,a){Promise.resolve().then(a.bind(a,24009))},38984:function(e,t,a){"use strict";var s=a(75376),l=a(39713);t.Z=e=>{let{title:t,description:a}=e;return(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-16 px-4",children:[(0,s.jsx)("div",{className:"relative mb-8",children:(0,s.jsx)("div",{className:"",children:(0,s.jsx)(l.default,{src:"/empty-box.svg",width:100,height:100,alt:"empty state icon",className:"w-[300px] h-[300px]"})})}),(0,s.jsx)("p",{className:"text-sm font-medium text-[#344054] -mt-12",children:t}),(0,s.jsx)("p",{className:"text-[#344054] text-center text-sm",children:a})]})}},6540:function(e,t,a){"use strict";var s=a(75376);a(32486),t.Z=()=>(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsx)("h1",{className:"text-base font-semibold p-4 lg:px-8",children:"Settings"}),(0,s.jsx)("hr",{className:"bg-[#E6EAEF] border"})]})},24009:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return K}});var s=a(75376),l=a(32486),r=a(6540),i=a(94742),n=a(11492),o=a(16006),d=a(22397),c=a(81356),u=a(25575),m=a(48063),x=a(21920),h=a(56762),A=a(53447),g=a(58983);let p=(0,A.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),v=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(h.f,{ref:t,className:(0,g.cn)(p(),a),...l})});v.displayName=h.f.displayName;var f=e=>{let{users:t,setUsers:a,roles:r,onUsersChange:i}=e,o=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),d=(e,s,l)=>{a(t.map(t=>{if(t.id===e){if("email"===s){let e=l&&!o(l)?"Please enter a valid email address":void 0;return{...t,[s]:l,emailError:e}}return{...t,[s]:l}}return t}))},c=e=>{a(t.filter(t=>t.id!==e))},h=(0,l.useCallback)(()=>{i(t.filter(e=>e.email&&e.role&&!e.emailError).map(e=>{let{email:t,role:a}=e;return{email:t,role:a}}))},[t,i]);return(0,l.useEffect)(()=>{h()},[h]),(0,s.jsx)("div",{className:"w-full max-w-4xl mx-auto border-none",children:(0,s.jsxs)("div",{className:"space-y-6 max-h-[400px] overflow-auto px-6",children:[t.map((e,a)=>(0,s.jsxs)("div",{className:"!mt-3",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-12 gap-6 items-end",children:[(0,s.jsxs)("div",{className:"md:col-span-7 space-y-2",children:[(0,s.jsx)(v,{htmlFor:"email-".concat(e.id),className:"text-sm font-medium text-gray-700",children:"Email Address"}),(0,s.jsx)("div",{className:"space-y-1",children:(0,s.jsx)(u.I,{id:"email-".concat(e.id),type:"email",value:e.email,onChange:t=>d(e.id,"email",t.target.value),placeholder:"Enter email address",className:"w-full ".concat(e.emailError?"border-red-500":"")})})]}),(0,s.jsxs)("div",{className:"md:col-span-4 space-y-2",children:[(0,s.jsx)(v,{htmlFor:"role-".concat(e.id),className:"text-sm font-medium text-gray-700",children:"Role"}),(0,s.jsxs)(m.Ph,{value:e.role,onValueChange:t=>d(e.id,"role",t),children:[(0,s.jsx)(m.i4,{className:"w-full",children:(0,s.jsx)(m.ki,{placeholder:"Select role"})}),(0,s.jsx)(m.Bw,{children:r&&r.map(e=>(0,s.jsx)(m.Ql,{value:e.id,children:e.name},e.id))})]})]}),(0,s.jsx)("div",{className:"md:col-span-1 flex",children:t.length>1&&0!==a&&(0,s.jsx)(n.z,{variant:"ghost",size:"icon",onClick:()=>c(e.id),className:"text-red-500 hover:text-red-700 hover:bg-white !p-0",children:(0,s.jsx)(x.Z,{className:"h-5 w-5",color:"black"})})})]},e.id),e.emailError&&(0,s.jsx)("div",{className:"text-red-500 text-sm mt-1",children:e.emailError})]},a)),(0,s.jsx)("div",{className:"!mt-0",children:(0,s.jsx)(n.z,{onClick:()=>{a([...t,{id:Date.now().toString(),email:"",role:""}])},variant:"ghost",className:"text-[#7141F8] hover:text-blue-700 hover:bg-white font-medium px-0",children:"Add Row"})})]})})},b=a(6114),j=a(97220),N=a(86418),w=e=>{let{emails:t,setEmails:a}=e,[r,i]=(0,l.useState)([]),[n,o]=(0,l.useState)(""),[c,u]=(0,l.useState)(null),m=(0,l.useRef)(null),x=(0,l.useRef)(null),{state:h}=(0,l.useContext)(j.R),{inviteModal:A,orgId:g}=h,[p,f]=(0,l.useState)(!1),[w,y]=(0,l.useState)("");(0,l.useEffect)(()=>{i(t.map((e,t)=>({id:"initial-".concat(t),email:e})))},[]),(0,l.useEffect)(()=>{let e=r.map(e=>e.email);JSON.stringify(e)!==JSON.stringify(t)&&a(e)},[r]),(0,l.useEffect)(()=>{A&&m.current&&m.current.focus()},[A]);let C=e=>{let t={id:Date.now().toString(),email:e};i(e=>[...e,t]),o("")},S=e=>{var t;i(t=>t.filter(t=>t.id!==e)),null===(t=m.current)||void 0===t||t.focus()};return(0,l.useEffect)(()=>{(async()=>{let e=await (0,N.Gl)("/organisations/".concat(g,"/roles"));if((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201){var t,a;let s=null==e?void 0:null===(a=e.data)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.find(e=>(null==e?void 0:e.name)==="User");y(null==s?void 0:s.id)}})()},[g]),(0,s.jsx)("div",{className:"bg-white rounded-lg w-full  max-w-full",children:(0,s.jsxs)("div",{className:"mt-2 relative px-6",children:[(0,s.jsx)(v,{htmlFor:"invite-input",className:"text-sm font-medium text-gray-700",children:"Email Addresses"}),(0,s.jsxs)("div",{ref:x,className:"flex mt-[8px] flex-wrap content-start gap-2 items-start min-h-[25dvh] border rounded-xl px-3 py-2 cursor-text w-full ".concat(c?"border-red-500":"border-[#E6EAEF]"),onClick:()=>{var e;null===(e=m.current)||void 0===e||e.focus()},children:[r.map(e=>(0,s.jsxs)("div",{className:"flex items-center gap-1 bg-[#F1F1FE] rounded-[3px] px-1 py-[2px]",children:[(0,s.jsx)("span",{className:"text-xs font-semibold text-[#101828] break-all",children:e.email}),(0,s.jsx)("button",{type:"button",onClick:t=>{t.stopPropagation(),S(e.id)},className:"hover:bg-gray-200 rounded-full p-1 flex-shrink-0",children:(0,s.jsx)(d.Z,{size:14,className:"text-[#667085]"})})]},e.id)),(0,s.jsx)("textarea",{id:"invite-input",ref:m,value:n,onChange:e=>{o(e.target.value),c&&u(null)},onKeyDown:e=>{if(("Enter"===e.key||" "===e.key)&&n.trim()){e.preventDefault();let t=n.trim();/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)?r.some(e=>e.email===t)?u("Email already added"):(C(t),u(null)):u("Please enter a valid email address")}},placeholder:0===r.length?"Input the address and press enter to complete":"",rows:1,className:"flex-1 outline-none text-sm min-w-[100px] w-full resize-none placeholder:text-[#98A2B3] pt-1",style:{overflow:"hidden",lineHeight:"normal"},autoFocus:!0})]}),c&&(0,s.jsxs)("div",{className:"flex items-center gap-1 mt-1.5 text-red-500 text-xs",children:[(0,s.jsx)(b.Z,{size:12}),(0,s.jsx)("span",{children:c})]})]})})},y=a(13352),C=a(73003),S=e=>{let{isOpen:t,onClose:a,onInviteSuccess:r}=e,[u,m]=(0,l.useState)("invite-few"),[x,h]=(0,l.useState)(""),[A,g]=(0,l.useState)([]),[p,v]=(0,l.useState)([{id:"1",email:"",role:""}]),[b,S]=(0,l.useState)([]),[R,k]=(0,l.useState)(!1),[E,z]=(0,l.useState)([]),{state:U}=(0,l.useContext)(j.R),{orgId:B,orgRoles:M}=U,F=(e,t)=>e.map(e=>({id:"temp-".concat(Date.now(),"-").concat(Math.random()),email:e,username:"",phone_number:"",profile_url:"",name:"",role:t,status:"invited",created_at:new Date().toISOString(),entity_type:"user"})),V=async()=>{if(B)try{let a=await (0,N.xo)("/invite/general",{organisation_id:B,role_id:x});if((null==a?void 0:a.status)===200||(null==a?void 0:a.status)===201){var e,t;let s=null==a?void 0:null===(t=a.data)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.invitation_link;await navigator.clipboard.writeText(s),y.Z.success("Invite link copied to clipboard")}else y.Z.error("Failed to generate invite link")}catch(e){console.error(e),y.Z.error("An error occurred while copying the invite link")}},D=async()=>{if(!B||!x)return;k(!0);let e=await (0,N.xo)("/invite/invite-few",{org_id:B,invitations:E});if((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201){var t,s;y.Z.success(null==e?void 0:null===(t=e.data)||void 0===t?void 0:t.message),r&&r(F(E.map(e=>e.email),(null===(s=A.find(e=>e.id===x))||void 0===s?void 0:s.name)||"User")),a()}k(!1)},I=async()=>{if(!B||!x)return;k(!0);let e=await (0,N.xo)("/invite",{org_id:B,emails:b,role_id:x});if((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201){var t,s;y.Z.success(null==e?void 0:null===(t=e.data)||void 0===t?void 0:t.message),r&&r(F(b,(null===(s=A.find(e=>e.id===x))||void 0===s?void 0:s.name)||"User")),a()}k(!1)};return(0,l.useEffect)(()=>{g(M);let e=null==M?void 0:M.find(e=>(null==e?void 0:e.name)==="User");h(null==e?void 0:e.id)},[B]),(0,s.jsx)(o.Vq,{open:t,onOpenChange:a,children:(0,s.jsx)(o.t9,{className:"backdrop-blur-sm bg-black/10",children:(0,s.jsx)(o.cZ,{className:"sm:max-w-2xl bg-white rounded-lg shadow-xl border-0 p-0 overflow-hidden",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(o.fK,{className:"px-6 pt-6 pb-4 border-b border-gray-100",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(o.$N,{className:"text-lg font-semibold text-gray-900",children:"Invite Your Team"}),(0,s.jsx)(n.z,{variant:"ghost",size:"icon",onClick:a,className:" p-1 rounded-md border hover:bg-gray-100",children:(0,s.jsx)(d.Z,{size:20,color:"#344054"})})]})}),(0,s.jsxs)(i.mQ,{value:u,onValueChange:m,className:"w-full mt-0",children:[(0,s.jsx)("div",{className:"border-b px-6 border-gray-200",children:(0,s.jsxs)(i.dr,{className:"flex w-fit bg-white rounded-none h-auto p-0 space-x-10",children:[(0,s.jsx)(i.SP,{value:"invite-few",className:"data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-purple-600 data-[state=active]:text-black rounded-none py-3 px-0 font-medium text-gray-600 hover:text-gray-900 transition-colors flex items-center gap-2",children:"Invite Few"}),(0,s.jsx)(i.SP,{value:"invite-many",className:"data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-purple-600 data-[state=active]:text-black rounded-none py-3 px-0 font-medium text-gray-600 hover:text-gray-900 transition-colors flex items-center gap-2","data-testid":"invite-many-tab",children:"Invite Many"})]})}),(0,s.jsx)(i.nU,{value:"invite-few",className:"mt-6",children:(0,s.jsx)(f,{users:p,setUsers:v,roles:A,onUsersChange:z})}),(0,s.jsx)(i.nU,{value:"invite-many",className:"mt-6 ",children:(0,s.jsx)(w,{emails:b,setEmails:S})})]}),(0,s.jsx)("div",{className:"flex  justify-between items-center px-4 mt-4",children:(0,s.jsx)("div",{className:"w-full h-[0.5px] bg-[#D6DAE0] !mt-0"})}),(0,s.jsx)("div",{className:"!mt-0 p-4",children:"invite-few"===u?(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:[(0,s.jsx)("span",{className:"font-semibold text-black",children:"Note:"})," You can also copy the invite link to invite members of your team as ",(0,s.jsx)("span",{className:"font-semibold text-black",children:'"Users"'})," ","and give them basic access."]})}):(0,s.jsxs)(s.Fragment,{children:[" ",(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:[(0,s.jsx)("span",{className:"font-semibold text-black",children:"Note:"})," This automatically invites all members as"," ",(0,s.jsx)("span",{className:"font-semibold text-black",children:'"Users"'})," ","and gives them basic access. Copying the invite link to share also does this."]})]})}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between gap-3 !mt-0 px-4 pb-4",children:[(0,s.jsxs)(n.z,{variant:"outline",className:"h-9 border border-[#7141F8]/40 w-full sm:w-auto",onClick:V,children:[(0,s.jsx)(c.Z,{size:16,color:"#7141F8"}),(0,s.jsx)("span",{className:"ml-2 text-[#7141F8]",children:"Copy invite link"})]}),(0,s.jsxs)("div",{className:"flex gap-3 w-full sm:w-auto",children:[(0,s.jsx)(n.z,{variant:"outline",className:"h-9 flex-1 sm:flex-none",onClick:a,children:"Cancel"}),(0,s.jsxs)(n.z,{className:"bg-[#7141F8] h-9 text-white px-7 gap-3 flex-1 sm:flex-none",disabled:"invite-few"===u&&!p.some(e=>e.email&&e.role)||"invite-many"===u&&0===b.length||R,onClick:"invite-few"===u?D:I,children:["Send Invite"," ",R&&(0,s.jsx)(C.Z,{width:"14",height:"14"})]})]})]})]})})})})},R=a(61533),k=a(62909),E=a(21912),z=a(56927),U=a(39713),B=a(56792),M=a(38984),F=a(5870),V=a(6793),D=a(57292),I=a(97712),Z=e=>{var t,a;let{isOpen:r,onClose:i,selectedMember:c}=e,{state:u,dispatch:x}=(0,l.useContext)(j.R),{orgRoles:h,orgMembers:A}=u,g=c&&c.email||"",p=c&&c.role||"",v=c&&c.created_at||"",[f,b]=(0,l.useState)(!1),w=(null==h?void 0:null===(t=h.find(e=>e.name===p))||void 0===t?void 0:t.id)||p,[y,C]=(0,l.useState)(w),S=(null==h?void 0:null===(a=h.find(e=>e.id===y))||void 0===a?void 0:a.name)||"",R=async()=>{b(!0);let e=await (0,N._x)("/organisations/".concat(u.orgId,"/users/").concat(null==c?void 0:c.id,"/role"),{role_id:y});if((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201){let e=(A||[]).map(e=>e.id===(null==c?void 0:c.id)?{...e,role:S}:e);x({type:I.a.ORG_MEMBERS,payload:e}),i()}b(!1)};return(0,s.jsx)(o.Vq,{open:r,onOpenChange:i,children:(0,s.jsx)(o.t9,{className:"backdrop-blur-sm bg-black/10",children:(0,s.jsx)(o.cZ,{className:"sm:max-w-2xl bg-white rounded-lg shadow-xl border-0 p-0 overflow-hidden",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(o.fK,{className:"px-6 pt-6 pb-4 border-b border-gray-100",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(o.$N,{className:"text-lg font-semibold text-gray-900",children:"Edit Member Role"}),(0,s.jsx)(n.z,{variant:"ghost",size:"icon",onClick:i,className:" p-1 rounded-md border hover:bg-gray-100",children:(0,s.jsx)(d.Z,{size:20,color:"#344054"})})]})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"border rounded-lg m-5 p-3 flex flex-col gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"text-xs font-semibold text-[#475467]",children:"Email Address"}),(0,s.jsx)("div",{className:"border-transparent rounded-none px-0 text-[#101828] !py-0 h-fit",children:g})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-xs font-semibold text-[#475467]",children:"Role"}),(0,s.jsx)("div",{className:"border-transparent rounded-none px-0 text-[#101828] !py-0 h-fit",children:p})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-xs font-semibold text-[#475467]",children:"Date Invited"}),(0,s.jsx)("div",{className:"border-transparent rounded-none px-0 text-[#101828] !py-0 h-fit",children:new Date(v).toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"})})]})]}),(0,s.jsxs)("div",{className:"px-5",children:[(0,s.jsx)("label",{className:"text-xs font-semibold text-[#475467]",children:"Role"}),(0,s.jsxs)(m.Ph,{disabled:"bot"===p,defaultValue:w,onValueChange:e=>C(e),children:[(0,s.jsx)(m.i4,{className:"w-full mt-1",children:(0,s.jsx)(m.ki,{placeholder:"Select a role"})}),(0,s.jsx)(m.Bw,{children:null==h?void 0:h.map(e=>(0,s.jsx)(m.Ql,{value:e.id,children:e.name},e.id))})]})]}),(0,s.jsx)("div",{className:"border-t mx-5 py-4 mt-4 ",children:(0,s.jsxs)("p",{className:"text-sm text-[#667085]",children:[(0,s.jsx)("span",{className:"text-[#475467] font-semibold",children:"Note:"})," ","This will take effect once the user joins the organisation."]})}),(0,s.jsxs)("div",{className:"flex justify-end gap-3 px-5 pb-4  ",children:[(0,s.jsx)(n.z,{variant:"outline",onClick:i,className:"h-9",children:"Cancel"}),(0,s.jsx)(n.z,{className:"bg-[#7141F8] h-9 text-white px-7",onClick:R,disabled:f,children:f?"Updating...":"Update Role"})]})]})]})})})})},P=e=>{let{isOpen:t,onClose:a,selectedMember:r}=e,[i,c]=(0,l.useState)(!1),{state:u,dispatch:m}=(0,l.useContext)(j.R),{orgId:x}=u,h=async()=>{c(!0);let e=await (0,N._x)("/organisations/".concat(x,"/users/").concat(null==r?void 0:r.id,"/status"),{active:(null==r?void 0:r.status)!=="active"}),t=(u.orgMembers||[]).map(e=>e.id===(null==r?void 0:r.id)?{...e,active:!(null==r?void 0:r.status)}:e);m({type:I.a.ORG_MEMBERS,payload:t}),((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201)&&a(),c(!1)},A=(null==r?void 0:r.status)==="active",g=A?"Restrict":"Activate";return(0,s.jsx)(o.Vq,{open:t,onOpenChange:a,children:(0,s.jsx)(o.t9,{className:"backdrop-blur-sm bg-black/10",children:(0,s.jsx)(o.cZ,{className:"sm:max-w-2xl bg-white rounded-lg shadow-xl border-0 p-0 overflow-hidden",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(o.fK,{className:"px-6 pt-6 pb-4 border-b border-gray-100",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(o.$N,{className:"text-lg font-semibold text-gray-900",children:[g," User Access"]}),(0,s.jsx)(n.z,{variant:"ghost",size:"icon",onClick:a,className:" p-1 rounded-md border hover:bg-gray-100",children:(0,s.jsx)(d.Z,{size:20,color:"#344054"})})]})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"border rounded-lg m-5 p-3 flex flex-col gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"text-xs font-semibold text-[#475467]",children:"Email Address"}),(0,s.jsx)("div",{className:"border-transparent rounded-none px-0 text-[#101828] !py-0 h-fit",children:null==r?void 0:r.email})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-xs font-semibold text-[#475467]",children:"Role"}),(0,s.jsx)("div",{className:"border-transparent rounded-none px-0 text-[#101828] !py-0 h-fit",children:null==r?void 0:r.role})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-xs font-semibold text-[#475467]",children:"Date Invited"}),(0,s.jsx)("div",{className:"border-transparent rounded-none px-0 text-[#101828] !py-0 h-fit",children:null==r?void 0:r.created_at})]})]}),(0,s.jsx)("div",{className:"border-t mx-5 py-4 mt-4 ",children:(0,s.jsxs)("p",{className:"text-sm text-[#667085]",children:[(0,s.jsx)("span",{className:"text-[#475467] font-semibold",children:"Note:"}),"The original link sent to this user will lead to an Error 404 page restricting their access to your organisation."]})}),(0,s.jsxs)("div",{className:"flex justify-end gap-3 px-5 pb-4  ",children:[(0,s.jsx)(n.z,{variant:"outline",onClick:a,className:"h-9",children:"Cancel"}),(0,s.jsx)(n.z,{className:"".concat(A?"bg-[#F81404]":"bg-[#7141F8]"," h-9 text-white px-7"),onClick:h,disabled:i,children:i?"".concat(g,"ing..."):"".concat(g," Access")})]})]})]})})})})};function T(e){var t,a,r,i,o;let{membersData:d=[],isLoading:c=!1}=e,[h,A]=l.useState([]),[p,v]=l.useState([]),[f,b]=(0,l.useState)(!1),[N,w]=(0,l.useState)(!1),[y,C]=(0,l.useState)(null),S=[{accessorKey:"email",header:e=>{let{column:t}=e;return(0,s.jsxs)(n.z,{variant:"ghost",onClick:()=>t.toggleSorting("asc"===t.getIsSorted()),className:"h-auto p-0 font-medium hover:bg-transparent text-[#667085]",children:["Email Address",(0,s.jsx)(z.Z,{name:"move-down",svgProps:{}})]})},cell:e=>{let{row:t}=e;return(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("div",{className:"rounded-md h-[30px] w-[30px] cursor-pointer hover:opacity-90",children:(0,s.jsx)(U.default,{src:""!=t.original.profile_url?t.original.profile_url:null===B.Z||void 0===B.Z?void 0:B.Z.user,alt:"",width:40,height:40,className:"rounded-md w-full h-full"})}),(0,s.jsx)("span",{className:"text-gray-700",children:t.original.email})]})},sortingFn:"alphanumeric"},{accessorKey:"role",header:e=>{let{column:t}=e;return(0,s.jsxs)(n.z,{variant:"ghost",onClick:()=>t.toggleSorting("asc"===t.getIsSorted()),className:"h-auto p-0 font-medium hover:bg-transparent text-[#667085]",children:["Role",(0,s.jsx)(z.Z,{name:"move-down",svgProps:{}})]})},cell:e=>{let{row:t}=e;return(0,s.jsx)("span",{className:"text-gray-700",children:t.getValue("role")})},sortingFn:"alphanumeric"},{accessorKey:"created_at",header:e=>{let{column:t}=e;return(0,s.jsxs)(n.z,{variant:"ghost",onClick:()=>t.toggleSorting("asc"===t.getIsSorted()),className:"h-auto p-0 font-medium hover:bg-transparent text-[#667085]",children:["Date Joined",(0,s.jsx)(z.Z,{name:"move-down",svgProps:{}})]})},cell:e=>{let{row:t}=e;return(0,s.jsx)("div",{className:"flex flex-col items-start gap-0",children:(0,s.jsx)("span",{className:"text-gray-700",children:new Date(t.original.created_at).toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"})})})},sortingFn:"datetime"},{accessorKey:"status",header:e=>{let{column:t}=e;return(0,s.jsxs)(n.z,{variant:"ghost",onClick:()=>t.toggleSorting("asc"===t.getIsSorted()),className:"h-auto p-0 font-medium hover:bg-transparent text-[#667085]",children:["Status",(0,s.jsx)(z.Z,{name:"move-down",svgProps:{}})]})},cell:e=>{let{row:t}=e;return(0,s.jsx)("div",{className:"flex items-center gap-3",children:(0,s.jsxs)("div",{className:(0,g.cn)("flex gap-1 w-fit px-2 py-1 rounded-md items-center",{"border border-2 border-green-100 text-green-700":"active"===t.original.status,"border border-2 border-red-100 text-red-700":"inactive"===t.original.status,"border border-2 border-blue-100 text-blue-700":"invited"===t.original.status,"border border-2 border-yellow-100 text-yellow-700":"pending"===t.original.status,"border border-2 border-gray-100 text-gray-700":"deactivated"===t.original.status}),children:[(0,s.jsx)("div",{className:(0,g.cn)("w-2 h-2 rounded-full",{"bg-red-500":"inactive"===t.original.status,"bg-green-500":"active"===t.original.status,"bg-blue-500":"invited"===t.original.status,"bg-yellow-500":"pending"===t.original.status,"bg-gray-500":"deactivated"===t.original.status})}),(0,s.jsx)("span",{className:"capitalize text-xs",children:t.original.status})]})})},sortingFn:"alphanumeric"},{accessorKey:"action",header:()=>(0,s.jsx)("div",{className:"h-auto p-0 font-medium hover:bg-transparent text-[#667085]",children:"Actions"}),cell:e=>{let{row:t}=e;return(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(n.z,{variant:"outline",className:"text-gray-700 h-fit p-1",onClick:()=>{b(!0),C(t.original)},children:(0,s.jsx)(F.Z,{size:20})}),"inactive"===t.original.status?(0,s.jsx)(n.z,{variant:"outline",className:"text-green-700 h-fit p-1",onClick:()=>{w(!0),C(t.original)},children:(0,s.jsx)(V.Z,{size:20})}):(0,s.jsx)(n.z,{variant:"outline",className:"text-gray-700 h-fit p-1",onClick:()=>{w(!0),C(t.original)},children:(0,s.jsx)(x.Z,{size:20})})]})},enableSorting:!1}],I=(0,R.b7)({data:d,columns:S,getCoreRowModel:(0,k.sC)(),getPaginationRowModel:(0,k.G_)(),getSortedRowModel:(0,k.tj)(),getFilteredRowModel:(0,k.vL)(),onSortingChange:A,onColumnFiltersChange:v,state:{sorting:h,columnFilters:p},initialState:{pagination:{pageSize:10}},autoResetPageIndex:!1}),T=I.getState().pagination.pageIndex+1,_=I.getPageCount(),H=d.length>0,{state:K}=(0,l.useContext)(j.R),{orgRoles:L}=K;return c?(0,s.jsx)("div",{className:"w-full mx-auto bg-white border mt-6 rounded-xl overflow-hidden",children:(0,s.jsxs)("div",{className:"flex items-center justify-center py-16",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"}),(0,s.jsx)("span",{className:"ml-2 text-gray-500",children:"Loading payment history..."})]})}):(0,s.jsxs)("div",{className:"w-full mx-auto  bg-white border  mt-6 rounded-xl overflow-hidden",children:[(0,s.jsx)(Z,{isOpen:f,onClose:()=>b(!1),selectedMember:y}),(0,s.jsx)(P,{isOpen:N,onClose:()=>w(!1),selectedMember:y}),H?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"flex items-center justify-between px-6 py-4 border-b",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(D.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400",size:16}),(0,s.jsx)(u.I,{placeholder:"Search by name or email",value:null!==(i=null===(t=I.getColumn("email"))||void 0===t?void 0:t.getFilterValue())&&void 0!==i?i:"",onChange:e=>{var t;return null===(t=I.getColumn("email"))||void 0===t?void 0:t.setFilterValue(e.target.value)},className:"pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder:text-neutral-400 text-neutral-800 w-[300px]"})]}),(0,s.jsxs)(m.Ph,{value:null!==(o=null===(a=I.getColumn("role"))||void 0===a?void 0:a.getFilterValue())&&void 0!==o?o:"all",onValueChange:e=>{var t;return null===(t=I.getColumn("role"))||void 0===t?void 0:t.setFilterValue("all"===e?"":e)},defaultValue:"all",children:[(0,s.jsx)(m.i4,{className:"w-fit gap-3",children:(0,s.jsx)(m.ki,{placeholder:"All"})}),(0,s.jsxs)(m.Bw,{children:[(0,s.jsx)(m.Ql,{value:"all",children:"All"}),L&&L.map(e=>(0,s.jsx)(m.Ql,{value:e.name.toLowerCase(),children:e.name},e.id))]})]})]})}),(0,s.jsx)("div",{className:"border-b",children:(0,s.jsxs)(E.iA,{children:[(0,s.jsx)(E.xD,{children:I.getHeaderGroups().map(e=>(0,s.jsx)(E.SC,{className:"bg-gray-50",children:e.headers.map(e=>(0,s.jsx)(E.ss,{className:"font-medium text-gray-700",children:e.isPlaceholder?null:(0,R.ie)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,s.jsx)(E.RM,{children:(null===(r=I.getRowModel().rows)||void 0===r?void 0:r.length)?I.getRowModel().rows.map(e=>(0,s.jsx)(E.SC,{"data-state":e.getIsSelected()&&"selected",className:"hover:bg-gray-50",children:e.getVisibleCells().map(e=>(0,s.jsx)(E.pj,{className:"py-4",children:(0,R.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,s.jsx)(E.SC,{children:(0,s.jsx)(E.pj,{colSpan:S.length,className:"h-24 text-center",children:"No results."})})})]})}),(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 space-x-2 py-4",children:[(0,s.jsxs)(n.z,{variant:"outline",size:"sm",onClick:()=>I.previousPage(),disabled:!I.getCanPreviousPage(),className:"flex items-center gap-2 border-gray-300",children:[(0,s.jsx)(z.Z,{name:"move-left",svgProps:{}}),"Previous"]}),(0,s.jsx)("div",{className:"flex items-center space-x-1",children:Array.from({length:Math.min(_,7)},(e,t)=>{let a;return 4===(a=_<=7?t+1:T<=4?t+1:T>=_-3?_-6+t:T-3+t)&&T>4&&_>7?(0,s.jsx)("span",{className:"px-2",children:"..."},"ellipsis1"):a===_-3&&T<_-3&&_>7?(0,s.jsx)("span",{className:"px-2",children:"..."},"ellipsis2"):(0,s.jsx)(n.z,{variant:T===a?"default":"outline",size:"sm",onClick:()=>I.setPageIndex(a-1),className:"w-8 h-8 p-0",children:a},a)})}),(0,s.jsxs)(n.z,{variant:"outline",size:"sm",onClick:()=>I.nextPage(),disabled:!I.getCanNextPage(),className:"flex items-center gap-2 border-gray-300",children:["Next",(0,s.jsx)(z.Z,{name:"move-right",svgProps:{}})]})]})]}):(0,s.jsx)(M.Z,{title:"No members found.",description:"Your members will appear here once you invite them."})]})}var _=e=>{let{isOpen:t,onClose:a,selectedMember:r}=e,[i,c]=(0,l.useState)(!1),{state:u,dispatch:m}=(0,l.useContext)(j.R),{orgInvites:x}=u,h=null==r?void 0:r.id,A=async()=>{c(!0);let e=await (0,N.jx)("/invite/".concat(h));if((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201){let e=(x||[]).filter(e=>e.id!==h);m({type:I.a.ORG_INVITES,payload:e}),a()}c(!1)};return(0,s.jsx)(o.Vq,{open:t,onOpenChange:a,children:(0,s.jsx)(o.t9,{className:"backdrop-blur-sm bg-black/10",children:(0,s.jsx)(o.cZ,{className:"sm:max-w-2xl bg-white rounded-lg shadow-xl border-0 p-0 overflow-hidden",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(o.fK,{className:"px-6 pt-6 pb-4 border-b border-gray-100",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(o.$N,{className:"text-lg font-semibold text-gray-900",children:"Delete User Invite"}),(0,s.jsx)(n.z,{variant:"ghost",size:"icon",onClick:a,className:" p-1 rounded-md border hover:bg-gray-100",children:(0,s.jsx)(d.Z,{size:20,color:"#344054"})})]})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"border rounded-lg m-5 p-3 flex flex-col gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"text-xs font-semibold text-[#475467]",children:"Email Address"}),(0,s.jsx)("div",{className:"border-transparent rounded-none px-0 text-[#101828] !py-0 h-fit",children:null==r?void 0:r.email})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-xs font-semibold text-[#475467]",children:"Role"}),(0,s.jsx)("div",{className:"border-transparent rounded-none px-0 text-[#101828] !py-0 h-fit",children:null==r?void 0:r.role})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-xs font-semibold text-[#475467]",children:"Date Invited"}),(0,s.jsx)("div",{className:"border-transparent rounded-none px-0 text-[#101828] !py-0 h-fit",children:null==r?void 0:r.created_at})]})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-3 px-5 pb-4  ",children:[(0,s.jsx)(n.z,{variant:"outline",onClick:a,className:"h-9",children:"Cancel"}),(0,s.jsx)(n.z,{className:"bg-[#F81404] h-9 text-white px-7",onClick:A,disabled:i,children:i?"Deleting...":"Delete Invite"})]})]})]})})})})};function H(e){var t,a,r,i,o;let{invitesData:d=[],isLoading:c=!1}=e,[h,A]=l.useState([]),[p,v]=l.useState([]),[f,b]=(0,l.useState)(!1),[N,w]=(0,l.useState)(!1),[y,C]=(0,l.useState)(null),S=[{accessorKey:"email",header:()=>(0,s.jsx)("p",{className:"text-[#667085]",children:"Email Address"}),cell:e=>{let{row:t}=e;return(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("div",{className:"rounded-md h-[30px] w-[30px] cursor-pointer hover:opacity-90",children:(0,s.jsx)(U.default,{src:null===B.Z||void 0===B.Z?void 0:B.Z.user,alt:"",width:40,height:40,className:"rounded-md w-full h-full"})}),(0,s.jsx)("span",{className:"text-gray-700",children:t.original.email})]})}},{accessorKey:"role",header:e=>{let{column:t}=e;return(0,s.jsx)(n.z,{variant:"ghost",onClick:()=>t.toggleSorting("asc"===t.getIsSorted()),className:"h-auto p-0 font-medium hover:bg-transparent text-[#667085]",children:"Role"})},cell:e=>{let{row:t}=e;return(0,s.jsx)("span",{className:"text-gray-700",children:t.getValue("role")})}},{accessorKey:"amount.total",header:e=>{let{column:t}=e;return(0,s.jsxs)(n.z,{variant:"ghost",onClick:()=>t.toggleSorting("asc"===t.getIsSorted()),className:"h-auto p-0 font-medium hover:bg-transparent text-[#667085]",children:["Date Joined",(0,s.jsx)(z.Z,{name:"move-down",svgProps:{}})]})},cell:e=>{let{row:t}=e;return(0,s.jsx)("div",{className:"flex flex-col items-start gap-0",children:(0,s.jsx)("span",{className:"text-gray-700",children:new Date(t.original.created_at).toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"})})})}},{accessorKey:"status",header:e=>{let{column:t}=e;return(0,s.jsxs)(n.z,{variant:"ghost",onClick:()=>t.toggleSorting("asc"===t.getIsSorted()),className:"h-auto p-0 font-medium hover:bg-transparent text-[#667085]",children:["Status",(0,s.jsx)(z.Z,{name:"move-down",svgProps:{}})]})},cell:e=>{let{row:t}=e;return(0,s.jsx)("div",{className:"flex items-center gap-3",children:(0,s.jsxs)("div",{className:(0,g.cn)("flex gap-1 w-fit px-2 py-1 rounded-md items-center",{"border border border-green-100 text-green-700":"accepted"===t.original.status,"border border border-red-100 text-red-700":"inactive"===t.original.status,"border border border-[#91C3FF] text-blue-700":"invited"===t.original.status,"border border border-yellow-100 text-yellow-700":"pending"===t.original.status,"border border border-gray-100 text-gray-700":"deactivated"===t.original.status}),children:[(0,s.jsx)("div",{className:(0,g.cn)("w-2 h-2 rounded-full",{"bg-red-500":"inactive"===t.original.status,"bg-green-500":"accepted"===t.original.status,"bg-[#0074FF]":"invited"===t.original.status,"bg-yellow-500":"pending"===t.original.status,"bg-gray-500":"deactivated"===t.original.status})}),(0,s.jsx)("span",{className:"capitalize text-xs",children:t.original.status})]})})}},{accessorKey:"action",header:()=>(0,s.jsx)("div",{className:"h-auto p-0 font-medium hover:bg-transparent text-[#667085]",children:"Actions"}),cell:e=>{let{row:t}=e;return(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(n.z,{variant:"outline",className:"text-gray-700 h-fit p-1",disabled:"accepted"===t.original.status,onClick:()=>{b(!0),C(t.original)},children:(0,s.jsx)(F.Z,{size:20})}),(0,s.jsx)(n.z,{variant:"outline",className:"text-gray-700 h-fit p-1",onClick:()=>{w(!0),C(t.original)},children:(0,s.jsx)(x.Z,{size:20})})]})}}],V=(0,R.b7)({data:d,columns:S,getCoreRowModel:(0,k.sC)(),getPaginationRowModel:(0,k.G_)(),getSortedRowModel:(0,k.tj)(),getFilteredRowModel:(0,k.vL)(),onSortingChange:A,onColumnFiltersChange:v,state:{sorting:h,columnFilters:p},initialState:{pagination:{pageSize:10}},autoResetPageIndex:!1}),I=V.getState().pagination.pageIndex+1,P=V.getPageCount(),T=d.length>0,{state:H}=(0,l.useContext)(j.R),{orgRoles:K}=H;return c?(0,s.jsx)("div",{className:"w-full mx-auto bg-white border mt-6 rounded-xl overflow-hidden",children:(0,s.jsxs)("div",{className:"flex items-center justify-center py-16",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"}),(0,s.jsx)("span",{className:"ml-2 text-gray-500",children:"Loading payment history..."})]})}):(0,s.jsxs)("div",{className:"w-full mx-auto  bg-white border  mt-6 rounded-xl overflow-hidden",children:[(0,s.jsx)(Z,{isOpen:f,onClose:()=>b(!1),selectedMember:y}),(0,s.jsx)(_,{isOpen:N,onClose:()=>w(!1),selectedMember:y}),T?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"flex items-center justify-between px-6 py-4 border-b",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(D.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400",size:16}),(0,s.jsx)(u.I,{placeholder:"Search by name or email",value:null!==(i=null===(t=V.getColumn("email"))||void 0===t?void 0:t.getFilterValue())&&void 0!==i?i:"",onChange:e=>{var t;return null===(t=V.getColumn("email"))||void 0===t?void 0:t.setFilterValue(e.target.value)},className:"pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder:text-neutral-400 text-neutral-800 w-[300px]"})]}),(0,s.jsxs)(m.Ph,{value:null!==(o=null===(a=V.getColumn("role"))||void 0===a?void 0:a.getFilterValue())&&void 0!==o?o:"all",onValueChange:e=>{var t;return null===(t=V.getColumn("role"))||void 0===t?void 0:t.setFilterValue("all"===e?"":e)},defaultValue:"all",children:[(0,s.jsx)(m.i4,{className:"w-fit gap-3",children:(0,s.jsx)(m.ki,{placeholder:"All"})}),(0,s.jsxs)(m.Bw,{children:[(0,s.jsx)(m.Ql,{value:"all",children:"All"}),K&&K.map(e=>(0,s.jsx)(m.Ql,{value:e.name.toLowerCase(),children:e.name},e.id))]})]})]})}),(0,s.jsx)("div",{className:"border-b",children:(0,s.jsxs)(E.iA,{children:[(0,s.jsx)(E.xD,{children:V.getHeaderGroups().map(e=>(0,s.jsx)(E.SC,{className:"bg-gray-50",children:e.headers.map(e=>(0,s.jsx)(E.ss,{className:"font-medium text-gray-700",children:e.isPlaceholder?null:(0,R.ie)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,s.jsx)(E.RM,{children:(null===(r=V.getRowModel().rows)||void 0===r?void 0:r.length)?V.getRowModel().rows.map(e=>(0,s.jsx)(E.SC,{"data-state":e.getIsSelected()&&"selected",className:"hover:bg-gray-50",children:e.getVisibleCells().map(e=>(0,s.jsx)(E.pj,{className:"py-4",children:(0,R.ie)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,s.jsx)(E.SC,{children:(0,s.jsx)(E.pj,{colSpan:S.length,className:"h-24 text-center",children:"No results."})})})]})}),(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 space-x-2 py-4",children:[(0,s.jsxs)(n.z,{variant:"outline",size:"sm",onClick:()=>V.previousPage(),disabled:!V.getCanPreviousPage(),className:"flex items-center gap-2 border-gray-300",children:[(0,s.jsx)(z.Z,{name:"move-left",svgProps:{}}),"Previous"]}),(0,s.jsx)("div",{className:"flex items-center space-x-1",children:Array.from({length:Math.min(P,7)},(e,t)=>{let a;return 4===(a=P<=7?t+1:I<=4?t+1:I>=P-3?P-6+t:I-3+t)&&I>4&&P>7?(0,s.jsx)("span",{className:"px-2",children:"..."},"ellipsis1"):a===P-3&&I<P-3&&P>7?(0,s.jsx)("span",{className:"px-2",children:"..."},"ellipsis2"):(0,s.jsx)(n.z,{variant:I===a?"default":"outline",size:"sm",onClick:()=>V.setPageIndex(a-1),className:"w-8 h-8 p-0",children:a},a)})}),(0,s.jsxs)(n.z,{variant:"outline",size:"sm",onClick:()=>V.nextPage(),disabled:!V.getCanNextPage(),className:"flex items-center gap-2 border-gray-300",children:["Next",(0,s.jsx)(z.Z,{name:"move-right",svgProps:{}})]})]})]}):(0,s.jsx)(M.Z,{title:"No members found.",description:"Your members will appear here once you invite them."})]})}var K=()=>{let{state:e,dispatch:t}=(0,l.useContext)(j.R),[a,o]=(0,l.useState)(0),[d,c]=(0,l.useState)(0),[u,m]=(0,l.useState)(!1),{orgMembers:x,orgInvites:h}=e;return(0,s.jsxs)("div",{children:[(0,s.jsx)(r.Z,{}),(0,s.jsx)(S,{isOpen:u,onClose:()=>m(!1),onInviteSuccess:e=>{let a=[...e,...h||[]];t({type:I.a.ORG_INVITES,payload:a})}}),(0,s.jsxs)("div",{className:"p-4 lg:px-8",children:[(0,s.jsxs)("div",{className:"mb-6 flex justify-between items-start",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-base font-semibold",children:"Your Team"}),(0,s.jsx)("p",{className:"text-sm text-[#344054]",children:"Manage all members of your team."})]}),(0,s.jsx)(n.z,{className:"px-4 py-2 bg-[#7141F8] text-white rounded-md hover:bg-[#7141F8]/80 transition-colors",onClick:()=>m(!0),children:"Invite People"})]}),(0,s.jsxs)(i.mQ,{defaultValue:"members",className:"w-full mt-6",children:[(0,s.jsx)("div",{className:"border-b border-gray-200",children:(0,s.jsxs)(i.dr,{className:"flex w-fit bg-white rounded-none h-auto p-0 space-x-10",children:[(0,s.jsxs)(i.SP,{value:"members",className:"data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-purple-600 data-[state=active]:text-black rounded-none py-3 px-0 font-medium text-gray-600 hover:text-gray-900 transition-colors flex items-center gap-2",children:["Members",(0,s.jsx)("div",{className:"flex items-center justify-center rounded-full bg-[#F2F4F7] text-[#5757CD] tracking-[-0.5%] font-semibold text-xs px-2 py-1 data-[state=active]:block data-[state=inactive]:hidden",children:x?x.length:0})]}),(0,s.jsxs)(i.SP,{value:"invites",className:"data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-purple-600 data-[state=active]:text-black rounded-none py-3 px-0 font-medium text-gray-600 hover:text-gray-900 transition-colors flex items-center gap-2","data-testid":"invites-tab",children:["Invites",(0,s.jsx)("div",{className:"flex items-center justify-center rounded-full bg-[#F2F4F7] text-[#5757CD] tracking-[-0.5%] font-semibold text-xs px-2 py-1 data-[state=active]:block data-[state=inactive]:hidden",children:h?h.length:0})]})]})}),(0,s.jsx)(i.nU,{value:"members",className:"mt-6",children:(0,s.jsx)(T,{membersData:x})}),(0,s.jsx)(i.nU,{value:"invites",className:"mt-6 ",children:(0,s.jsx)(H,{invitesData:h})})]})]})]})}},56792:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});var s={bot:{src:"/_next/static/media/bot.dfdf2c7a.png",height:80,width:80,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAHlBMVEVMaXEAYC4AlUQAm0YAjUEAfDgrJCgKXTEAj0EAkEKCxuBiAAAACnRSTlMAB73oOFyq4Hx6x9l+tAAAAAlwSFlzAAAsSwAALEsBpT2WqQAAAChJREFUeJxjYEACjIxQBhsbhGZlZ2cF0SxMzMxMLCAVHExMnGiKwQAACx0AUUBiaOwAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},RedBot:{src:"/_next/static/media/bot-red.3a55df2b.png",height:40,width:40,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAIVBMVEVMaXFQAAB3BwJtCARtCQReBQB4CgJvCgRLEAskLS4hKCqjVWtSAAAAC3RSTlMABO01ulzJe9m0qZQuNjoAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAoSURBVHicY2BAAoyMUJqTC8Jg5eBgBdHMbExMbMwgGXYWFnZUxRAAAA2RAGBI3TANAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},blueBot:{src:"/_next/static/media/blue-bot.f296e2d3.png",height:40,width:40,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAKlBMVEXv7/rr7vjt7vj19v7x8f6Hh/ivr/q6u+9gYapzc//Y1/3T0v1mZ2JdX1kQXXeiAAAAA3RSTlP+nJxhYYB9AAAACXBIWXMAAAsTAAALEwEAmpwYAAAANklEQVR4nBXKyQ3AMBDEMNma9ZWk/3YDfwnSJGDHBBJBvtcAzrXmhaf2riNkVI37TaLS5cb2AyAvAORP3/L0AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},google:{src:"/_next/static/media/google.3c81f1fb.svg",height:16,width:16,blurWidth:0,blurHeight:0},send:{src:"/_next/static/media/send.7163c084.svg",height:18,width:17,blurWidth:0,blurHeight:0},disco:{src:"/_next/static/media/disco.672244de.png",height:192,width:192,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAaVBMVEVMaXHi2M2nopzZsWDKqnHy01GUkIC0m2ODeG8Zl/84f+Lmlwbyuy7Oplrew47k0KassLjamCLdr13j5eXVx6WwqJ7cniSXin7CwLzcw5mPfmV/dWh/fHevtq++qnPu7/TCu7bXybm2t7Oodw53AAAAH3RSTlMA/IlTgiVGFf0LJCo85G/2o3DNqIb6jPf4+/h6mErb4mYc6AAAAAlwSFlzAAAhOAAAITgBRZYxYAAAAEBJREFUeJwlwccBgCAAALGjgw17V9D9h/RhAhSBn7MYB2XVxKHvQNciJaEhtHlaXwmMz+L9Ccxb9vt9gVPysMp8UogCzxGkjJoAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},teammates:{src:"/_next/static/media/teammates.bbd3adac.svg",height:32,width:32,blurWidth:0,blurHeight:0},facebook:{src:"/_next/static/media/facebook.fca64f6c.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAALVBMVEVWmvD//fpHkvBNlfAMcfMee/HX6Py10/kpgvOEt/f///+ex/jG3foAZPJgovbihSuXAAAABHRSTlP+4f7+rTCusQAAAAlwSFlzAAAsSwAALEsBpT2WqQAAADhJREFUeJwFwYkRwDAMAjDsA/wk6f7jVkIYYjrgpuaTAXJ2CEisogTx7B4KYN/bBNyv6rURzqp0/DHzAXV/loHlAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},twitter:{src:"/_next/static/media/twitter.913bc175.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAGFBMVEUAAAABAQEeHh4PDw9qampFRUVYWFhSUlIoHbPFAAAAAXRSTlPhl+sKiAAAAAlwSFlzAAAsSwAALEsBpT2WqQAAAC9JREFUeJw1i0EOACAMwihD/f+PzWbk1KRFYiZRqzAIsr0GnLSDSp463mkoT69/vxFyAH2eHpTfAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},instagram:{src:"/_next/static/media/instagram.07bbefde.png",height:96,width:96,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAPFBMVEX6+vr++v76tezYrP3+pcn9tL77pen6+vn8//////7+r8/+wK7+4an/rNf+07Hpqvz/7aj6ed3+3Lb+rKgwumMZAAAACHRSTlPg////////4NsSfKYAAAAJcEhZcwAAITgAACE4AUWWMWAAAAA+SURBVHicFcs5EsAgDANAYWOQuJP8/68Zum0WkCQKUHLflgRtt/IYwTJnyZngFz3axRrvikZI54yebiNJ1R9EDgII9TTKpAAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},youtube:{src:"/_next/static/media/youtube.b9208f26.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAGFBMVEX5+fn9/////Pz8WVn8Hh78JSX+AAD8n57mNweJAAAAAXRSTlPhl+sKiAAAAAlwSFlzAAAsSwAALEsBpT2WqQAAACxJREFUeJxNi7ENADAMg4ydNP9/3ClVNyRAwmAksG0Q01U9iJzk5IenNt79AhbIAKGvX29oAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},tiktok:{src:"/_next/static/media/tiktok.029728a5.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAPFBMVEUBAQFMHietfojQv8Y1Exqzs7cBDQwAAAAAAAAZCQxVgYLI9PSxxcp1fIFNjYtHREYdOTfPr7sdVlNtoqCdn0cDAAAACHRSTlPh////////4WC/TK4AAAAJcEhZcwAALEsAACxLAaU9lqkAAAA4SURBVHicY2DgAAMGEM3GxcnBAWZws3KCGHy8rEwsHAwcQsyMPCwgKQFmYUGIGn4mRpA2doh2dgBFkAH9NNczawAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},channel:{src:"/_next/static/media/channel.71b0d2d9.png",height:1827,width:1986,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAMAAAACh/xsAAAAFVBMVEX6+P77+v328//7+/3w6/3////u5v959aEiAAAABXRSTlP94vju40yQVCYAAAAJcEhZcwAAITgAACE4AUWWMWAAAAAnSURBVHicY2BkAgMWBhY2MGBhYGYAAVZGBmZWMEBhMLCygqQYoWoAFpgAqnCsrYsAAAAASUVORK5CYII=",blurWidth:8,blurHeight:7},user:{src:"/_next/static/media/user.cc6e44b6.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAG1BMVEXz/vwD0sHq/PpK4NPU+PVi5NkY18jB9PCF6uJqQ8ocAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAM0lEQVR4nB3JwQ3AMAzEMMlnO9l/4iL9ESBVAFUUPdM8jM6PaH508oq+M7fhrOoe4uqaDxOVAKOLLWTAAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},createChannel:{src:"/_next/static/media/create-channel.d750c07c.png",height:2332,width:2544,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAMAAAACh/xsAAAAD1BMVEX+/v7t6P308vz6+vrm5OpbWQr2AAAACXBIWXMAACxLAAAsSwGlPZapAAAAJ0lEQVR4nEXHwQ0AMAzCQDDZf+aKKFKPD5bNko+mDApDEn2tbk/7AQyWAF9SvWHtAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:7},megaphone:{src:"/_next/static/media/megaphone.d567f808.png",height:24,width:24,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAJ1BMVEVMaXEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACv15x2AAAADXRSTlMAaRhcwINMCyY5ndOqyc6ZIAAAAAlwSFlzAAALEwAACxMBAJqcGAAAADRJREFUeJxVyrkNACAMwEDnI5Cw/7wIiYarXBg+OhRySZUba4dNRDAfmjeIbg83yBDmnZ8DGa0AvpK+HLsAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},visaLogo:{src:"/_next/static/media/visa-logo.e39f2d7e.svg",height:12,width:33,blurWidth:0,blurHeight:0},fileDoc:{src:"/_next/static/media/FileDoc.69079eba.svg",height:20,width:20,blurWidth:0,blurHeight:0},border:{src:"/_next/static/media/Border.8c3c175f.svg",height:1024,width:44,blurWidth:0,blurHeight:0}}},16006:function(e,t,a){"use strict";a.d(t,{$N:function(){return A},Be:function(){return g},GG:function(){return c},Vq:function(){return n},cN:function(){return h},cZ:function(){return m},fK:function(){return x},hg:function(){return o},t9:function(){return u}});var s=a(75376),l=a(32486),r=a(94797),i=a(58983);let n=r.fC,o=r.xz,d=r.h_,c=r.x8,u=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(r.aV,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-black/50  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...l})});u.displayName=r.aV.displayName;let m=l.forwardRef((e,t)=>{let{className:a,children:l,...n}=e;return(0,s.jsxs)(d,{children:[(0,s.jsx)(u,{}),(0,s.jsx)(r.VY,{ref:t,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-[90%] max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...n,children:l})]})});m.displayName=r.VY.displayName;let x=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};x.displayName="DialogHeader";let h=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};h.displayName="DialogFooter";let A=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(r.Dx,{ref:t,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",a),...l})});A.displayName=r.Dx.displayName;let g=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(r.dk,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",a),...l})});g.displayName=r.dk.displayName},25575:function(e,t,a){"use strict";a.d(t,{I:function(){return i}});var s=a(75376),l=a(32486),r=a(58983);let i=l.forwardRef((e,t)=>{let{className:a,type:l,...i}=e;return(0,s.jsx)("input",{type:l,className:(0,r.cn)("flex h-10 w-full rounded-md border border-input bg-background px-4 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus:outline-none disabled:cursor-not-allowed disabled:opacity-50",a),ref:t,...i})});i.displayName="Input"},73003:function(e,t,a){"use strict";var s=a(75376);a(32486);var l=a(10983);t.Z=e=>{let{height:t,width:a,color:r}=e;return(0,s.jsx)(l.iT,{height:t||20,width:a||20,color:r||"#fff",visible:!0,ariaLabel:"oval-loading",secondaryColor:r||"#fff",strokeWidth:5,strokeWidthSecondary:5})}},48063:function(e,t,a){"use strict";a.d(t,{Bw:function(){return A},Ph:function(){return c},Ql:function(){return g},i4:function(){return m},ki:function(){return u}});var s=a(75376),l=a(32486),r=a(56343),i=a(33599),n=a(14596),o=a(28780),d=a(58983);let c=r.fC;r.ZA;let u=r.B4,m=l.forwardRef((e,t)=>{let{className:a,children:l,...n}=e;return(0,s.jsxs)(r.xz,{ref:t,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...n,children:[l,(0,s.jsx)(r.JO,{asChild:!0,children:(0,s.jsx)(i.Z,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=r.xz.displayName;let x=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(r.u_,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),...l,children:(0,s.jsx)(n.Z,{className:"h-4 w-4"})})});x.displayName=r.u_.displayName;let h=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(r.$G,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),...l,children:(0,s.jsx)(i.Z,{className:"h-4 w-4"})})});h.displayName=r.$G.displayName;let A=l.forwardRef((e,t)=>{let{className:a,children:l,position:i="popper",...n}=e;return(0,s.jsx)(r.h_,{children:(0,s.jsxs)(r.VY,{ref:t,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:i,...n,children:[(0,s.jsx)(x,{}),(0,s.jsx)(r.l_,{className:(0,d.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:l}),(0,s.jsx)(h,{})]})})});A.displayName=r.VY.displayName,l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(r.__,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...l})}).displayName=r.__.displayName;let g=l.forwardRef((e,t)=>{let{className:a,children:l,...i}=e;return(0,s.jsxs)(r.ck,{ref:t,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...i,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(r.wU,{children:(0,s.jsx)(o.Z,{className:"h-4 w-4"})})}),(0,s.jsx)(r.eT,{children:l})]})});g.displayName=r.ck.displayName,l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(r.Z0,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",a),...l})}).displayName=r.Z0.displayName},21912:function(e,t,a){"use strict";a.d(t,{RM:function(){return o},SC:function(){return d},iA:function(){return i},pj:function(){return u},ss:function(){return c},xD:function(){return n}});var s=a(75376),l=a(32486),r=a(58983);let i=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)("div",{className:"relative w-full overflow-auto",children:(0,s.jsx)("table",{ref:t,className:(0,r.cn)("w-full caption-bottom text-sm",a),...l})})});i.displayName="Table";let n=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)("thead",{ref:t,className:(0,r.cn)("[&_tr]:border-b",a),...l})});n.displayName="TableHeader";let o=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)("tbody",{ref:t,className:(0,r.cn)("[&_tr:last-child]:border-0",a),...l})});o.displayName="TableBody",l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)("tfoot",{ref:t,className:(0,r.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a),...l})}).displayName="TableFooter";let d=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)("tr",{ref:t,className:(0,r.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...l})});d.displayName="TableRow";let c=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)("th",{ref:t,className:(0,r.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...l})});c.displayName="TableHead";let u=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)("td",{ref:t,className:(0,r.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...l})});u.displayName="TableCell",l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)("caption",{ref:t,className:(0,r.cn)("mt-4 text-sm text-muted-foreground",a),...l})}).displayName="TableCaption"},94742:function(e,t,a){"use strict";a.d(t,{SP:function(){return d},dr:function(){return o},mQ:function(){return n},nU:function(){return c}});var s=a(75376),l=a(32486),r=a(73068),i=a(58983);let n=r.fC,o=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(r.aV,{ref:t,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-[#F2F4F7] p-1 text-muted-foreground",a),...l})});o.displayName=r.aV.displayName;let d=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(r.xz,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...l})});d.displayName=r.xz.displayName;let c=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(r.VY,{ref:t,className:(0,i.cn)("mt-4 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...l})});c.displayName=r.VY.displayName},86418:function(e,t,a){"use strict";a.d(t,{Gl:function(){return i},HH:function(){return o},Q_:function(){return d},_x:function(){return u},an:function(){return m},i1:function(){return h},jx:function(){return x},x9:function(){return c},xo:function(){return n}});var s=a(20818),l=a(13352);let r=a(18648).env.NEXT_PUBLIC_BASE_URL,i=async e=>{let t=localStorage.getItem("token")||"";try{return await s.Z.get(r+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var a,l,i;return((null==e?void 0:null===(a=e.response)||void 0===a?void 0:a.status)===401||(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(l=i.data)||void 0===l?void 0:l.status_code)===401)&&(localStorage.clear(),window.location.href="/auth/login"),e}},n=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await s.Z.post(r+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var i,n,o,d,c;return l.Z.error(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(i=n.data)||void 0===i?void 0:i.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},o=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await s.Z.post(r+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var i,n;return l.Z.error(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(i=n.data)||void 0===i?void 0:i.message),e}},d=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await s.Z.post(r+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){return e}},c=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await s.Z.post(r+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"multipart/form-data"}})}catch(e){var i,n,o,d,c;return l.Z.error(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(i=n.data)||void 0===i?void 0:i.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},u=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await s.Z.patch(r+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var i,n,o,d,c;return l.Z.error(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(i=n.data)||void 0===i?void 0:i.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},m=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await s.Z.put(r+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var i,n,o,d,c;return l.Z.error(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(i=n.data)||void 0===i?void 0:i.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},x=async e=>{let t=localStorage.getItem("token")||"";try{return await s.Z.delete(r+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var a,i;return l.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(a=i.data)||void 0===a?void 0:a.message),e}},h=async e=>{let t=localStorage.getItem("token")||"";try{return await s.Z.delete(r+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){return e}}}},function(e){e.O(0,[4269,8863,7140,4144,5300,5614,2987,6343,6329,9254,94,3668,5800,7220,6539,7542,2344,1744],function(){return e(e.s=77967)}),_N_E=e.O()}]);