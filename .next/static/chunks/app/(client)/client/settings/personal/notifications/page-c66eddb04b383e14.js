(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9292],{24897:function(e,t,a){Promise.resolve().then(a.bind(a,3290))},6540:function(e,t,a){"use strict";var o=a(75376);a(32486),t.Z=()=>(0,o.jsxs)("div",{className:"mb-2",children:[(0,o.jsx)("h1",{className:"text-base font-semibold p-4 lg:px-8",children:"Settings"}),(0,o.jsx)("hr",{className:"bg-[#E6EAEF] border"})]})},3290:function(e,t,a){"use strict";a.r(t);var o=a(75376),n=a(32486),i=a(6540),l=a(86418),s=a(73003);let r={notificationType:"all",timeFrom:"00:00",timeTo:"00:00",infoMethod:"mobile"};t.default=()=>{let[e,t]=(0,n.useState)(r),[a,c]=(0,n.useState)(r),[d,u]=(0,n.useState)(!0),[m,h]=(0,n.useState)(!1),p=JSON.stringify(e)!==JSON.stringify(a);(0,n.useEffect)(()=>{let e=localStorage.getItem("orgId")||"";(async()=>{u(!0);let a=await (0,l.Gl)("/organisations/".concat(e,"/notification-preference?device_type=web"));if(200===a.status||201===a.status){let e=a.data.data,[o,n]=e.time_range.split(" - ").map(g);t({notificationType:v(e.notify_about),timeFrom:o,timeTo:n,infoMethod:e.send_mail?"email":"mobile"}),c({notificationType:v(e.notify_about),timeFrom:o,timeTo:n,infoMethod:e.send_mail?"email":"mobile"})}u(!1)})()},[]);let v=e=>"all_new_messages"===e?"all":"mentions"===e?"mentions":"nothing",f=e=>{let[t,a]=e.split(":"),o=parseInt(t,10);return"".concat((o%12==0?12:o%12).toString().padStart(2,"0"),":").concat(a," ").concat(o>=12?"PM":"AM")},g=e=>{let[t,a]=e.split(" "),[o,n]=t.split(":").map(Number);return"PM"===a&&12!==o&&(o+=12),"AM"===a&&12===o&&(o=0),"".concat(o.toString().padStart(2,"0"),":").concat(n.toString().padStart(2,"0"))},x=async()=>{let t=localStorage.getItem("orgId")||"";h(!0);let a={notify_about:"all"===e.notificationType?"all_new_messages":"mentions"===e.notificationType?"mentions":"nothing",send_mail:"email"===e.infoMethod,time_range:"".concat(f(e.timeFrom)," - ").concat(f(e.timeTo)),device_type:"web"},o=await (0,l.xo)("/organisations/".concat(t,"/notification-preference?device_type=web"),a);(200===o.status||201===o.status)&&c(e),h(!1)};return d?(0,o.jsx)("div",{className:"p-6",children:(0,o.jsx)(s.Z,{})}):(0,o.jsxs)("div",{children:[(0,o.jsx)(i.Z,{}),(0,o.jsxs)("div",{className:"p-4 lg:px-8",children:[(0,o.jsxs)("div",{className:"mb-6",children:[(0,o.jsx)("h1",{className:"text-base font-semibold",children:"Your Notification Preferences"}),(0,o.jsx)("p",{className:"text-sm text-[#344054]",children:"Manage when and why you get notified."})]}),(0,o.jsxs)("div",{className:"max-w-2xl",children:[(0,o.jsx)("label",{className:"block font-semibold text-sm mb-2",children:"Send notifications for:"}),(0,o.jsx)("div",{className:"space-y-3",children:["all","mentions","nothing"].map(a=>(0,o.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,o.jsx)("input",{type:"radio",name:"notificationType",checked:e.notificationType===a,onChange:()=>t(e=>({...e,notificationType:a}))}),(0,o.jsx)("span",{className:"capitalize text-sm",children:"all"===a?"All new messages":a})]},a))})]}),(0,o.jsx)("hr",{className:"bg-[#E6EAEF] border my-4"}),(0,o.jsxs)("div",{className:"max-w-2xl",children:[(0,o.jsx)("label",{className:"block font-semibold text-sm mb-2",children:"Receive notifications only within:"}),(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,o.jsxs)("div",{className:"flex flex-col items-start gap-1 text-sm",children:[(0,o.jsx)("span",{className:"font-medium",children:"From"}),(0,o.jsx)("input",{type:"time",value:e.timeFrom,onChange:e=>t(t=>({...t,timeFrom:e.target.value})),className:"border rounded p-3"})]}),(0,o.jsxs)("div",{className:"flex flex-col items-start gap-1 text-sm",children:[(0,o.jsx)("span",{className:"font-medium",children:"To"}),(0,o.jsx)("input",{type:"time",value:e.timeTo,onChange:e=>t(t=>({...t,timeTo:e.target.value})),className:"border rounded p-3"})]})]}),(0,o.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:[(0,o.jsx)("span",{className:"font-medium text-black",children:"Note:"})," Outside this time, notifications are paused."]})]}),(0,o.jsx)("hr",{className:"bg-[#E6EAEF] border my-4"}),(0,o.jsxs)("div",{className:"mb-4 max-w-2xl",children:[(0,o.jsx)("label",{className:"block font-semibold text-sm mb-2",children:"To keep me informed:"}),(0,o.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,o.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,o.jsx)("input",{type:"radio",name:"infoMethod",checked:"mobile"===e.infoMethod,onChange:()=>t(e=>({...e,infoMethod:"mobile"}))}),(0,o.jsx)("span",{children:"Use different settings for mobile devices"})]}),(0,o.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,o.jsx)("input",{type:"radio",name:"infoMethod",checked:"email"===e.infoMethod,onChange:()=>t(e=>({...e,infoMethod:"email"}))}),(0,o.jsx)("span",{children:"Receive notifications via email"})]})]})]}),p&&(0,o.jsxs)("div",{className:"flex justify-end space-x-3 pt-4 max-w-xl",children:[(0,o.jsx)("button",{onClick:()=>{t(a)},className:"px-4 py-2 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-100",children:"Revert Changes"}),(0,o.jsx)("button",{onClick:x,disabled:m,className:"px-4 py-2 rounded-md bg-purple-600 text-white hover:bg-purple-700",children:m?(0,o.jsx)(s.Z,{}):"Save Changes"})]})]})]})}},73003:function(e,t,a){"use strict";var o=a(75376);a(32486);var n=a(10983);t.Z=e=>{let{height:t,width:a,color:i}=e;return(0,o.jsx)(n.iT,{height:t||20,width:a||20,color:i||"#fff",visible:!0,ariaLabel:"oval-loading",secondaryColor:i||"#fff",strokeWidth:5,strokeWidthSecondary:5})}},86418:function(e,t,a){"use strict";a.d(t,{Gl:function(){return l},HH:function(){return r},Q_:function(){return c},_x:function(){return u},an:function(){return m},i1:function(){return p},jx:function(){return h},x9:function(){return d},xo:function(){return s}});var o=a(20818),n=a(13352);let i=a(18648).env.NEXT_PUBLIC_BASE_URL,l=async e=>{let t=localStorage.getItem("token")||"";try{return await o.Z.get(i+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var a,n,l;return((null==e?void 0:null===(a=e.response)||void 0===a?void 0:a.status)===401||(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(n=l.data)||void 0===n?void 0:n.status_code)===401)&&(localStorage.clear(),window.location.href="/auth/login"),e}},s=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await o.Z.post(i+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var l,s,r,c,d;return n.Z.error(null==e?void 0:null===(s=e.response)||void 0===s?void 0:null===(l=s.data)||void 0===l?void 0:l.message),(null==e?void 0:null===(r=e.response)||void 0===r?void 0:r.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(c=d.data)||void 0===c?void 0:c.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},r=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await o.Z.post(i+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var l,s;return n.Z.error(null==e?void 0:null===(s=e.response)||void 0===s?void 0:null===(l=s.data)||void 0===l?void 0:l.message),e}},c=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await o.Z.post(i+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){return e}},d=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await o.Z.post(i+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"multipart/form-data"}})}catch(e){var l,s,r,c,d;return n.Z.error(null==e?void 0:null===(s=e.response)||void 0===s?void 0:null===(l=s.data)||void 0===l?void 0:l.message),(null==e?void 0:null===(r=e.response)||void 0===r?void 0:r.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(c=d.data)||void 0===c?void 0:c.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},u=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await o.Z.patch(i+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var l,s,r,c,d;return n.Z.error(null==e?void 0:null===(s=e.response)||void 0===s?void 0:null===(l=s.data)||void 0===l?void 0:l.message),(null==e?void 0:null===(r=e.response)||void 0===r?void 0:r.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(c=d.data)||void 0===c?void 0:c.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},m=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await o.Z.put(i+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var l,s,r,c,d;return n.Z.error(null==e?void 0:null===(s=e.response)||void 0===s?void 0:null===(l=s.data)||void 0===l?void 0:l.message),(null==e?void 0:null===(r=e.response)||void 0===r?void 0:r.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(c=d.data)||void 0===c?void 0:c.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},h=async e=>{let t=localStorage.getItem("token")||"";try{return await o.Z.delete(i+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var a,l;return n.Z.error(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(a=l.data)||void 0===a?void 0:a.message),e}},p=async e=>{let t=localStorage.getItem("token")||"";try{return await o.Z.delete(i+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){return e}}}},function(e){e.O(0,[4269,5300,2987,7542,2344,1744],function(){return e(e.s=24897)}),_N_E=e.O()}]);