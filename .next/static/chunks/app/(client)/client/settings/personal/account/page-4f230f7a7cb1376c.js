(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2661],{45048:function(e,s,t){Promise.resolve().then(t.bind(t,83365))},6540:function(e,s,t){"use strict";var l=t(75376);t(32486),s.Z=()=>(0,l.jsxs)("div",{className:"mb-2",children:[(0,l.jsx)("h1",{className:"text-base font-semibold p-4 lg:px-8",children:"Settings"}),(0,l.jsx)("hr",{className:"bg-[#E6EAEF] border"})]})},83365:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return g}});var l=t(75376),n=t(32486),a=t(9823),r=t(6540),i=t(22397),o=t(64858),c=t(98755),d=t(83255),u=t(16006),x=t(97220),m=t(86418),h=t(13352),f=t(73003),p=()=>{var e;let[s,t]=(0,n.useState)(!1),[a,r]=(0,n.useState)(""),[p,v]=(0,n.useState)(!1),[b,j]=(0,n.useState)(!1),[g,N]=(0,n.useState)(!1),{state:y}=(0,n.useContext)(x.R),[w,k]=(0,n.useState)(!1),C=a.length>0&&b,Z=async e=>{e.preventDefault();let s=JSON.parse(localStorage.get("user")||"{}");k(!0);let t=await (0,m.jx)("/users/".concat(null==s?void 0:s.id));if((null==t?void 0:t.status)==200||(null==t?void 0:t.status)===201){var l;h.Z.success(null==t?void 0:null===(l=t.data)||void 0===l?void 0:l.message),window.location.href="/"}k(!1)};return(0,l.jsxs)(u.Vq,{open:g,onOpenChange:N,children:[(0,l.jsx)(u.hg,{asChild:!0,children:(0,l.jsx)("button",{className:"inline-block py-2 px-4 mt-6 border rounded-[4px] border-[#F81404] text-[#F81404] text-sm font-semibold hover:bg-[#F81404] hover:text-white transition-colors duration-200",children:"Delete my account"})}),(0,l.jsxs)(u.cZ,{className:"max-w-xl p-0 overflow-hidden max-h-[90vh]",children:[(0,l.jsx)(u.fK,{children:(0,l.jsxs)(u.$N,{className:"flex items-center justify-between border-b p-4 text-[#1D2939] text-xl font-black",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold ",children:"Delete Account"}),(0,l.jsx)(i.Z,{className:"text-gray-500 hover:text-gray-700 text-xl font-bold cursor-pointers border",onClick:()=>{N(!1),t(!1)}})]})}),s?(0,l.jsxs)("div",{className:"space-y-5 p-6 pt-3",children:[(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-1",htmlFor:"password",children:"Password"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("input",{id:"password",type:p?"text":"password",className:"w-full border rounded px-3 py-2 pr-10 focus:outline-none focus:ring-1 focus:ring-purple-500",value:a,onChange:e=>r(e.target.value),autoComplete:"new-password"}),(0,l.jsx)("button",{type:"button",onClick:()=>v(e=>!e),className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",children:p?(0,l.jsx)(c.Z,{size:18}):(0,l.jsx)(d.Z,{size:18})})]})]}),(0,l.jsxs)("div",{className:"flex items-start mb-6",children:[(0,l.jsx)("input",{id:"consent",type:"checkbox",checked:b,onChange:()=>j(e=>!e),className:"mt-1 mr-2"}),(0,l.jsx)("label",{htmlFor:"consent",className:"text-sm text-gray-700",children:"I absolve Telex of any responsibility in this account deletion."})]}),(0,l.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,l.jsx)("button",{type:"button",onClick:()=>{N(!1),t(!1)},className:"px-4 py-2 rounded-md border border-gray-300 text-gray-700",children:"Cancel"}),(0,l.jsxs)("button",{onClick:Z,disabled:!C,className:"flex items-center justify-center px-4 py-2 rounded text-white font-medium ".concat(C?"bg-red-600 hover:bg-red-700":"bg-red-300 cursor-not-allowed"),children:["Delete Account ",w&&(0,l.jsx)(f.Z,{})]})]})]}):(0,l.jsxs)("div",{className:"p-6 pt-3 space-y-3",children:[(0,l.jsxs)("h3",{className:"text-sm font-medium",children:["Are you sure you want to delete your account with"," ",(0,l.jsx)("span",{className:"text-[#667085]",children:null==y?void 0:null===(e=y.orgData)||void 0===e?void 0:e.name}),"? You will lose:"]}),(0,l.jsxs)("div",{className:"border-b pb-4 mb-4 space-y-2 text-sm",children:[(0,l.jsxs)("p",{children:[(0,l.jsx)(o.Z,{className:"inline mr-2 text-green-500",size:18}),"Access to this workspace."]}),(0,l.jsxs)("p",{children:[(0,l.jsx)(o.Z,{className:"inline mr-2 text-green-500",size:18}),"All your chats history."]})]}),(0,l.jsxs)("p",{className:"text-xs text-gray-500",children:[(0,l.jsx)("strong",{children:"Note:"})," This action cannot be undone."," "]}),(0,l.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,l.jsx)("button",{type:"button",onClick:()=>N(!1),className:"px-4 py-2 rounded-md border border-gray-300 text-gray-700",children:"Cancel"}),(0,l.jsx)("button",{onClick:()=>t(!0),className:"px-4 py-2 rounded-md text-white bg-primary-500 hover:bg-blue-300",children:"Continue"})]})]})]})]})},v=t(90822),b=t(56792),j=t(39713),g=()=>{let[e,s]=(0,n.useState)(!1),{state:t}=(0,n.useContext)(x.R),{user:i}=t;return(0,l.jsxs)("div",{className:"min-h-screen",children:[(0,l.jsx)(r.Z,{}),(0,l.jsxs)("div",{className:"p-4 lg:px-8",children:[(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsx)("h1",{className:"text-base font-semibold",children:"Your Account Information"}),(0,l.jsx)("p",{className:"text-sm text-[#344054]",children:"Manage your account data with ease."})]}),(0,l.jsxs)("div",{className:"flex flex-col md:flex-row items-start gap-4",children:[(0,l.jsxs)("div",{className:"border rounded-xl p-4 w-full md:flex-1 relative",children:[(0,l.jsx)("div",{className:"border absolute top-4 right-4 p-2 rounded-md cursor-pointer",onClick:()=>s(!0),children:(0,l.jsx)(a.Z,{className:"w-5 h-5"})}),(0,l.jsx)("div",{className:"mx-auto md:mx-0 h-28 w-28 md:h-36 md:w-36 border rounded-xl bg-[#F2F4F7] flex items-center justify-center",children:(0,l.jsx)(j.default,{src:(null==i?void 0:i.avatar_url)||(null===b.Z||void 0===b.Z?void 0:b.Z.user),alt:"account logo",className:"w-full h-full object-cover rounded-xl",height:100,width:100})}),(0,l.jsxs)("div",{className:"flex flex-col gap-4 mt-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("h3",{className:"text-sm text-[#475467]",children:"Name"}),(0,l.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,l.jsx)("p",{className:"break-all",children:(null==i?void 0:i.full_name)&&(null==i?void 0:i.full_name)!==" "&&(null==i?void 0:i.full_name)||(null==i?void 0:i.first_name)}),(0,l.jsx)("div",{className:"w-2 h-2 rounded-full bg-[#D9D9D9]"}),(0,l.jsxs)("span",{className:"text-[#667085] break-all",children:[(null==i?void 0:i.display_name)||(null==i?void 0:i.username)&&"@",(null==i?void 0:i.display_name)||(null==i?void 0:i.username)]})]})]}),(null==i?void 0:i.title)&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("h3",{className:"text-sm text-[#475467]",children:"Title"}),(0,l.jsx)("p",{className:"break-all",children:null==i?void 0:i.title})]}),(null==i?void 0:i.timezone)&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("h3",{className:"text-sm text-[#475467]",children:"Time Zone"}),(0,l.jsx)("p",{className:"break-all",children:null==i?void 0:i.timezone})]})]})]}),(0,l.jsxs)("div",{className:"border rounded-xl p-4 w-full md:flex-1 flex flex-col gap-4",children:[(0,l.jsx)("div",{className:"flex items-center justify-between",children:(0,l.jsxs)("div",{className:"space-y-2 w-full",children:[(0,l.jsx)("h3",{className:"text-sm text-[#475467]",children:"Email Address"}),(0,l.jsx)("p",{className:"break-all",children:null==i?void 0:i.email})]})}),(null==i?void 0:i.phone)&&(0,l.jsx)("div",{className:"flex items-center justify-between",children:(0,l.jsxs)("div",{className:"space-y-2 w-full",children:[(0,l.jsx)("h3",{className:"text-sm text-[#475467]",children:"Phone Number"}),(0,l.jsx)("p",{className:"break-all",children:null==i?void 0:i.phone})]})})]})]}),(0,l.jsx)(p,{})]}),(0,l.jsx)(v.Z,{isOpen:e,onClose:()=>s(!1)})]})}},11492:function(e,s,t){"use strict";t.d(s,{d:function(){return o},z:function(){return c}});var l=t(75376),n=t(32486),a=t(91007),r=t(53447),i=t(58983);let o=(0,r.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=n.forwardRef((e,s)=>{let{className:t,variant:n,size:r,asChild:c=!1,...d}=e,u=c?a.g7:"button";return(0,l.jsx)(u,{className:(0,i.cn)(o({variant:n,size:r,className:t})),ref:s,...d})});c.displayName="Button"},58983:function(e,s,t){"use strict";t.d(s,{cn:function(){return a},k:function(){return r}});var l=t(89824),n=t(97215);function a(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,n.m6)((0,l.W)(s))}let r=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"}},function(e){e.O(0,[4269,8863,7140,4144,5300,2987,6343,4152,7220,9513,822,7542,2344,1744],function(){return e(e.s=45048)}),_N_E=e.O()}]);