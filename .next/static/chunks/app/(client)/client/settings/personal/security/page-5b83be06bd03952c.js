(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3980],{59393:function(e,t,a){Promise.resolve().then(a.bind(a,69822))},6540:function(e,t,a){"use strict";var s=a(75376);a(32486),t.Z=()=>(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsx)("h1",{className:"text-base font-semibold p-4 lg:px-8",children:"Settings"}),(0,s.jsx)("hr",{className:"bg-[#E6EAEF] border"})]})},69822:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return w}});var s=a(75376),r=a(23625),o=a(23972),l=a(33512),n=a(32486),i=a(6540),d=a(22397),c=a(98755),u=a(83255),m=e=>{let{setChangePasswordDialog:t}=e,[a,r]=(0,n.useState)(""),[o,l]=(0,n.useState)(!1),[i,m]=(0,n.useState)(""),[x,p]=(0,n.useState)(!1),[h,f]=(0,n.useState)(""),[v,g]=(0,n.useState)(!1),[b,j]=(0,n.useState)(!1),y=i===h,w=a.length>0,N=""!==i&&""!==h&&y;return(0,s.jsxs)("div",{className:"max-w-lg min-w-[450px] mx-auto bg-white shadow-lg rounded-xl border border-purple-300 mt-10 relative",children:[(0,s.jsx)(d.Z,{className:"absolute top-3 right-3 text-gray-500 hover:text-gray-700 text-xl font-bold cursor-pointer",onClick:()=>t(!1)}),b?(0,s.jsxs)("div",{className:"",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold border-b p-4",children:"Set New Password"}),(0,s.jsxs)("form",{className:"space-y-5 p-6",onSubmit:e=>{e.preventDefault(),N?console.log("Password changed successfully"):console.error("Passwords do not match or are invalid")},children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-1",htmlFor:"password",children:"New Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:"new-password",placeholder:"*********",type:x?"text":"password",className:"w-full border rounded px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-purple-500",value:i,onChange:e=>m(e.target.value)}),(0,s.jsx)("button",{type:"button",onClick:()=>p(e=>!e),className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",children:x?(0,s.jsx)(c.Z,{size:18}):(0,s.jsx)(u.Z,{size:18})})]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-1",htmlFor:"password",children:"Confirm Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:"confirm-password",placeholder:"*********",type:v?"text":"password",className:"w-full border rounded px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-purple-500",value:h,onChange:e=>f(e.target.value)}),(0,s.jsx)("button",{type:"button",onClick:()=>g(e=>!e),className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",children:v?(0,s.jsx)(c.Z,{size:18}):(0,s.jsx)(u.Z,{size:18})})]})]}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:[(0,s.jsx)("strong",{children:"Note:"})," You would need to login again to effect this change."," "]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,s.jsx)("button",{type:"button",onClick:()=>t(!1),className:"px-4 py-2 rounded-md border border-gray-300 text-gray-700",children:"Cancel"}),(0,s.jsx)("button",{type:"submit",disabled:!N,className:"px-4 py-2 rounded-md  text-white ".concat(N?"bg-purple-600 hover:bg-purple-700":"bg-gray-300 cursor-not-allowed"),children:"Save Changes"})]})]})]}):(0,s.jsxs)("div",{className:"",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold border-b p-4",children:"Confirm Current Password"}),(0,s.jsxs)("form",{className:"space-y-5 p-6",onSubmit:e=>{e.preventDefault(),console.log("Password validity checked"),j(!0)},children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-1",htmlFor:"password",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:"password",placeholder:"*********",type:o?"text":"password",className:"w-full border rounded px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-purple-500",value:a,onChange:e=>r(e.target.value)}),(0,s.jsx)("button",{type:"button",onClick:()=>l(e=>!e),className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",children:o?(0,s.jsx)(c.Z,{size:18}):(0,s.jsx)(u.Z,{size:18})})]})]}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:[(0,s.jsx)("strong",{children:"Note:"})," This helps us confirm your identity."," "]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,s.jsx)("button",{type:"button",onClick:()=>t(!1),className:"px-4 py-2 rounded-md border border-gray-300 text-gray-700",children:"Cancel"}),(0,s.jsx)("button",{type:"submit",disabled:!w,className:"px-4 py-2 rounded-md  text-white ".concat(w?"bg-purple-600 hover:bg-purple-700":"bg-gray-300 cursor-not-allowed"),children:"Next"})]})]})]})]})},x=a(11492),p=a(86418),h=a(72738),f=a.n(h),v=a(21912),g=a(16006),b=a(13352),j=a(73003),y=()=>{let[e,t]=(0,n.useState)(!1),[a,r]=(0,n.useState)(!1),[o,l]=(0,n.useState)(""),[i,m]=(0,n.useState)(!1),[x,h]=(0,n.useState)(""),[f,v]=(0,n.useState)(!1),[y,w]=(0,n.useState)(""),[N,S]=(0,n.useState)(!1),C=""!==o&&""!==x&&""!==y,k=async e=>{if(e.preventDefault(),x!==y){b.Z.error("Password does not match");return}r(!0);let a=await (0,p.an)("/auth/change-password",{old_password:o,new_password:x});if(200===a.status||201===a.status){var s;b.Z.success(null==a?void 0:null===(s=a.data)||void 0===s?void 0:s.message),t(!1),l(""),h(""),w("")}r(!1)};return(0,s.jsxs)(g.Vq,{open:e,onOpenChange:t,children:[(0,s.jsx)(g.hg,{asChild:!0,children:(0,s.jsx)("button",{className:"border px-4 py-2 rounded-md text-sm",children:"Change Password"})}),(0,s.jsxs)(g.cZ,{className:"max-w-lg p-0 overflow-hidden max-h-[90vh]",children:[(0,s.jsx)(g.fK,{children:(0,s.jsxs)(g.$N,{className:"flex items-center justify-between border-b p-4 text-[#1D2939] text-xl font-black",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold ",children:"Update password"}),(0,s.jsx)(d.Z,{className:"text-gray-500 hover:text-gray-700 text-xl font-bold cursor-pointers border",onClick:()=>{t(!1)}})]})}),(0,s.jsx)("div",{className:"",children:(0,s.jsxs)("form",{className:"space-y-5 p-6",onSubmit:k,children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-1",htmlFor:"password",children:"Old Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:"password",placeholder:"*********",type:i?"text":"password",className:"w-full border rounded px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-purple-500",value:o,onChange:e=>l(e.target.value)}),(0,s.jsx)("button",{type:"button",onClick:()=>m(e=>!e),className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",children:i?(0,s.jsx)(c.Z,{size:18}):(0,s.jsx)(u.Z,{size:18})})]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-1",htmlFor:"password",children:"New Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:"new-password",placeholder:"*********",type:f?"text":"password",className:"w-full border rounded px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-purple-500",value:x,onChange:e=>h(e.target.value)}),(0,s.jsx)("button",{type:"button",onClick:()=>v(e=>!e),className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",children:f?(0,s.jsx)(c.Z,{size:18}):(0,s.jsx)(u.Z,{size:18})})]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-1",htmlFor:"password",children:"Confirm Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:"confirm-password",placeholder:"*********",type:N?"text":"password",className:"w-full border rounded px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-purple-500",value:y,onChange:e=>w(e.target.value)}),(0,s.jsx)("button",{type:"button",onClick:()=>S(e=>!e),className:"absolute right-2 top-1/2 -translate-y-1/2 text-gray-500",children:N?(0,s.jsx)(c.Z,{size:18}):(0,s.jsx)(u.Z,{size:18})})]})]}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:[(0,s.jsx)("strong",{children:"Note:"})," You would need to login again to effect this change."," "]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,s.jsx)("button",{type:"button",onClick:()=>t(!1),className:"px-4 py-2 rounded-md border border-gray-300 text-gray-700",children:"Cancel"}),(0,s.jsxs)("button",{type:"submit",disabled:!C,className:"flex items-center gap-2 px-4 py-2 rounded-md  text-white ".concat(C?"bg-purple-600 hover:bg-purple-700":"bg-gray-300 cursor-not-allowed"),children:["Save Changes",a&&(0,s.jsx)(j.Z,{})]})]})]})})]})]})},w=()=>{let[e,t]=(0,n.useState)(1),[a,d]=(0,n.useState)(!1),[c,u]=(0,n.useState)([]),[h,g]=(0,n.useState)(1);return(0,n.useEffect)(()=>{let t=JSON.parse(localStorage.getItem("user")||"{}");(async()=>{let a=await (0,p.Gl)("/users/".concat(null==t?void 0:t.id,"/login-audit?page=").concat(e,"&limit=").concat(7));if((null==a?void 0:a.status)===200||(null==a?void 0:a.status)===201){var s,r,o,l;u((null==a?void 0:null===(s=a.data)||void 0===s?void 0:s.data)||[]),g(Math.ceil(((null==a?void 0:null===(o=a.data)||void 0===o?void 0:null===(r=o.meta)||void 0===r?void 0:r.total)||(null==a?void 0:null===(l=a.data)||void 0===l?void 0:l.total)||0)/7))}})()},[e]),(0,s.jsxs)("div",{children:[(0,s.jsx)(i.Z,{}),(0,s.jsxs)("div",{className:"p-4 lg:px-8 flex flex-col md:flex-row items-start md:items-center justify-between gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-base font-semibold",children:"Your Account Security"}),(0,s.jsx)("p",{className:"text-sm text-[#344054]",children:"Keeping your data secure by staying in-the-know"})]}),(0,s.jsx)(y,{})]}),(0,s.jsxs)("div",{className:"mx-2 md:m-4 lg:mx-8 border rounded-md overflow-x-auto",children:[(0,s.jsx)("div",{className:"min-w-[800px]",children:(0,s.jsxs)(v.iA,{children:[(0,s.jsx)(v.xD,{className:"bg-[#f6f7f9] text-[#667085]",children:(0,s.jsxs)(v.SC,{children:[(0,s.jsx)(v.ss,{className:"text-xs md:text-sm",children:"Device"}),(0,s.jsx)(v.ss,{className:"text-xs md:text-sm",children:"Location"}),(0,s.jsx)(v.ss,{className:"text-xs md:text-sm",children:(0,s.jsxs)("div",{className:"flex items-center gap-1",children:["Date",(0,s.jsx)(r.Z,{className:"text-[#98A2B3] w-3 h-3 md:w-4 md:h-4"})]})}),(0,s.jsx)(v.ss,{className:"text-xs md:text-sm",children:(0,s.jsxs)("div",{className:"flex items-center gap-1",children:["Last Active",(0,s.jsx)(r.Z,{className:"text-[#98A2B3] w-3 h-3 md:w-4 md:h-4"})]})}),(0,s.jsx)(v.ss,{className:"text-xs md:text-sm",children:"Status"})]})}),(0,s.jsx)(v.RM,{children:c.map((e,t)=>(0,s.jsxs)(v.SC,{className:"text-xs md:text-sm text-[#344054]",children:[(0,s.jsx)(v.pj,{children:e.device}),(0,s.jsx)(v.pj,{children:e.location}),(0,s.jsx)(v.pj,{children:f()(e.created_at).format("lll")}),(0,s.jsx)(v.pj,{children:f()(e.login_at).format("lll")}),(0,s.jsx)(v.pj,{children:e.is_live?"Active":"Not Active"})]},t))})]})}),(null==c?void 0:c.length)===0&&(0,s.jsx)("p",{className:"text-center mt-10 mb-10 md:mt-20 md:mb-20 text-gray-500",children:"No available data"}),h>1&&(0,s.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center gap-4 p-3 md:p-5 border-t border-[#E6EAEF]",children:[(0,s.jsxs)(x.z,{onClick:()=>t(e=>Math.max(e-1,1)),disabled:1===e,variant:"outline",className:"w-full md:w-auto",children:[(0,s.jsx)(o.Z,{className:"text-[#667085] w-4 h-4"})," Previous"]}),(0,s.jsx)("div",{className:"flex gap-1 overflow-x-auto",children:(function(e,t){let a=[],s=Math.max(2,e-2),r=Math.min(t-1,e+2);a.push(1),s>2&&a.push("...");for(let e=s;e<=r;e++)a.push(e);return r<t-1&&a.push("..."),t>1&&a.push(t),a})(e,h).map((a,r)=>(0,s.jsx)("button",{className:"px-2 md:px-3 py-1 rounded text-sm ".concat(a===e?"bg-[#E6EAEF] font-medium":"hover:bg-gray-100"," ").concat("..."===a?"cursor-default":"cursor-pointer"),onClick:()=>"number"==typeof a&&t(a),disabled:"..."===a,children:a},r))}),(0,s.jsxs)(x.z,{onClick:()=>t(e=>e<h?e+1:e),disabled:e===h,variant:"outline",className:"flex items-center gap-1 w-full md:w-auto",children:["Next ",(0,s.jsx)(l.Z,{className:"text-[#667085] w-4 h-4"})]})]})]}),a&&(0,s.jsx)("div",{className:"fixed inset-0 z-20 flex items-center justify-center p-4 backdrop-blur-sm bg-black/10",children:(0,s.jsx)("div",{className:"w-full max-w-md",children:(0,s.jsx)(m,{setChangePasswordDialog:d})})})]})}},11492:function(e,t,a){"use strict";a.d(t,{d:function(){return i},z:function(){return d}});var s=a(75376),r=a(32486),o=a(91007),l=a(53447),n=a(58983);let i=(0,l.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,t)=>{let{className:a,variant:r,size:l,asChild:d=!1,...c}=e,u=d?o.g7:"button";return(0,s.jsx)(u,{className:(0,n.cn)(i({variant:r,size:l,className:a})),ref:t,...c})});d.displayName="Button"},16006:function(e,t,a){"use strict";a.d(t,{$N:function(){return h},Be:function(){return f},GG:function(){return c},Vq:function(){return n},cN:function(){return p},cZ:function(){return m},fK:function(){return x},hg:function(){return i},t9:function(){return u}});var s=a(75376),r=a(32486),o=a(94797),l=a(58983);let n=o.fC,i=o.xz,d=o.h_,c=o.x8,u=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(o.aV,{ref:t,className:(0,l.cn)("fixed inset-0 z-50 bg-black/50  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...r})});u.displayName=o.aV.displayName;let m=r.forwardRef((e,t)=>{let{className:a,children:r,...n}=e;return(0,s.jsxs)(d,{children:[(0,s.jsx)(u,{}),(0,s.jsx)(o.VY,{ref:t,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-[90%] max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...n,children:r})]})});m.displayName=o.VY.displayName;let x=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};x.displayName="DialogHeader";let p=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};p.displayName="DialogFooter";let h=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(o.Dx,{ref:t,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",a),...r})});h.displayName=o.Dx.displayName;let f=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(o.dk,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",a),...r})});f.displayName=o.dk.displayName},73003:function(e,t,a){"use strict";var s=a(75376);a(32486);var r=a(10983);t.Z=e=>{let{height:t,width:a,color:o}=e;return(0,s.jsx)(r.iT,{height:t||20,width:a||20,color:o||"#fff",visible:!0,ariaLabel:"oval-loading",secondaryColor:o||"#fff",strokeWidth:5,strokeWidthSecondary:5})}},21912:function(e,t,a){"use strict";a.d(t,{RM:function(){return i},SC:function(){return d},iA:function(){return l},pj:function(){return u},ss:function(){return c},xD:function(){return n}});var s=a(75376),r=a(32486),o=a(58983);let l=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{className:"relative w-full overflow-auto",children:(0,s.jsx)("table",{ref:t,className:(0,o.cn)("w-full caption-bottom text-sm",a),...r})})});l.displayName="Table";let n=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("thead",{ref:t,className:(0,o.cn)("[&_tr]:border-b",a),...r})});n.displayName="TableHeader";let i=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("tbody",{ref:t,className:(0,o.cn)("[&_tr:last-child]:border-0",a),...r})});i.displayName="TableBody",r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("tfoot",{ref:t,className:(0,o.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a),...r})}).displayName="TableFooter";let d=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("tr",{ref:t,className:(0,o.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...r})});d.displayName="TableRow";let c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("th",{ref:t,className:(0,o.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...r})});c.displayName="TableHead";let u=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("td",{ref:t,className:(0,o.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...r})});u.displayName="TableCell",r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("caption",{ref:t,className:(0,o.cn)("mt-4 text-sm text-muted-foreground",a),...r})}).displayName="TableCaption"},58983:function(e,t,a){"use strict";a.d(t,{cn:function(){return o},k:function(){return l}});var s=a(89824),r=a(97215);function o(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.m6)((0,s.W)(t))}let l=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"},86418:function(e,t,a){"use strict";a.d(t,{Gl:function(){return l},HH:function(){return i},Q_:function(){return d},_x:function(){return u},an:function(){return m},i1:function(){return p},jx:function(){return x},x9:function(){return c},xo:function(){return n}});var s=a(20818),r=a(13352);let o=a(18648).env.NEXT_PUBLIC_BASE_URL,l=async e=>{let t=localStorage.getItem("token")||"";try{return await s.Z.get(o+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var a,r,l;return((null==e?void 0:null===(a=e.response)||void 0===a?void 0:a.status)===401||(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(r=l.data)||void 0===r?void 0:r.status_code)===401)&&(localStorage.clear(),window.location.href="/auth/login"),e}},n=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await s.Z.post(o+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var l,n,i,d,c;return r.Z.error(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(l=n.data)||void 0===l?void 0:l.message),(null==e?void 0:null===(i=e.response)||void 0===i?void 0:i.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},i=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await s.Z.post(o+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var l,n;return r.Z.error(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(l=n.data)||void 0===l?void 0:l.message),e}},d=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await s.Z.post(o+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){return e}},c=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await s.Z.post(o+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"multipart/form-data"}})}catch(e){var l,n,i,d,c;return r.Z.error(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(l=n.data)||void 0===l?void 0:l.message),(null==e?void 0:null===(i=e.response)||void 0===i?void 0:i.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},u=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await s.Z.patch(o+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var l,n,i,d,c;return r.Z.error(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(l=n.data)||void 0===l?void 0:l.message),(null==e?void 0:null===(i=e.response)||void 0===i?void 0:i.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},m=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await s.Z.put(o+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var l,n,i,d,c;return r.Z.error(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(l=n.data)||void 0===l?void 0:l.message),(null==e?void 0:null===(i=e.response)||void 0===i?void 0:i.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},x=async e=>{let t=localStorage.getItem("token")||"";try{return await s.Z.delete(o+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var a,l;return r.Z.error(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(a=l.data)||void 0===a?void 0:a.message),e}},p=async e=>{let t=localStorage.getItem("token")||"";try{return await s.Z.delete(o+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){return e}}}},function(e){e.O(0,[4269,748,7140,4144,5300,2987,6343,4474,7542,2344,1744],function(){return e(e.s=59393)}),_N_E=e.O()}]);