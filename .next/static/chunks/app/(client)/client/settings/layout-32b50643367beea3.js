(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4246],{93199:function(e,t,n){Promise.resolve().then(n.bind(n,33810))},99616:function(e,t,n){"use strict";n.d(t,{Z:function(){return x}});var o=n(75376),a=n(74178),r=n(33599),l=n(51888),i=n(97220),s=n(32486),c=n(39713),d=n(97712),u=n(26242),p=n(16669);function x(e){let{name:t}=e,{state:n,dispatch:x}=(0,s.useContext)(i.R),{orgData:h}=n,[m,v]=(0,s.useState)(!1),f=async()=>{localStorage.clear(),window.location.href="/auth/login"};return(0,o.jsx)("div",{className:"",children:(0,o.jsxs)(a.J2,{children:[(0,o.jsx)(a.xo,{asChild:!0,children:(0,o.jsxs)("div",{className:"flex items-center gap-1 cursor-pointer",children:[(0,o.jsx)("h6",{className:"text-lg leading-[26px] font-semibold text-white",children:t||(null==h?void 0:h.name)}),(0,o.jsx)(r.Z,{className:"text-white mt-1"})]})}),(0,o.jsxs)(a.yk,{align:"start",className:"w-[270px] p-0 rounded-md shadow-xl",onClick:()=>x({type:d.a.CHANNEL_BAR,payload:!(null==n?void 0:n.channelBar)}),children:[(0,o.jsxs)("div",{className:"",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2 p-3 border-b font-medium text-sm",children:[(0,o.jsx)("div",{className:"size-9 rounded border overflow-hidden flex items-center justify-center",children:(null==h?void 0:h.logo_url)?(0,o.jsx)(c.default,{src:null==h?void 0:h.logo_url,alt:"",width:50,height:50,unoptimized:!0,className:"size-9"}):(0,o.jsx)("h3",{className:"text-primary-500 font-bold text-sm",children:(0,u.Qm)(null==h?void 0:h.name)})}),(0,o.jsx)("div",{className:"text-sm",children:null==h?void 0:h.name})]}),(0,o.jsxs)("div",{className:"flex gap-2 text-xs px-3 py-3 font-medium border-b hover:bg-blue-500 hover:text-white cursor-pointer",children:["\uD83D\uDE80",(0,o.jsxs)("div",{children:["Your ",(0,o.jsx)("strong",{children:"Pro trial"})," lasts through"," ",(0,o.jsx)("strong",{children:"June 13"}),".",(0,o.jsx)("a",{href:"#",className:"text-blue-600 block hover:underline",children:"See upgrade options"})]})]})]}),(0,o.jsxs)("ul",{className:"text-sm pb-3",children:[(0,o.jsxs)("li",{onClick:()=>x({type:d.a.INVITE_MODAL,payload:!0}),className:"hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 text-[15px]",children:["Invite people to ",null==h?void 0:h.name]}),(0,o.jsx)("li",{className:"hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]",children:(0,o.jsx)(p.default,{href:"/client/settings/personal/account",children:"settings"})}),(0,o.jsxs)("li",{className:"relative hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]",onMouseEnter:()=>v(!0),onMouseLeave:()=>v(!1),children:["Apps ",(0,o.jsx)(l.Z,{className:"h-4 w-4"}),m&&(0,o.jsxs)("ul",{className:"absolute left-full top-0 w-[200px] bg-white text-black rounded-[7px] shadow-lg border border-[#E6EAEF] overflow-hidden",children:[(0,o.jsx)("li",{className:"hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]",children:"App1"}),(0,o.jsx)("li",{className:"hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]",children:"App2"}),(0,o.jsx)("li",{className:"hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]",children:"App3"}),(0,o.jsx)("li",{className:"hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 flex justify-between text-[15px]",children:"App4"})]})]}),(0,o.jsx)("li",{onClick:f,className:"hover:bg-blue-500 hover:text-white cursor-pointer px-4 py-2 font-medium text-[15px]",children:"Sign out"})]})]})]})})}},33810:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return j}});var o=n(75376),a=n(32486),r=n(97220),l=n(97712),i=n(47411),s=n(40463),c=n(88788),d=n(66862),u=n(35751),p=n(23633),x=n(61134),h=n(99625),m=n(90937),v=n(16669),f=n(99616);function g(){let{state:e,dispatch:t}=(0,a.useContext)(r.R),[n,g]=(0,a.useState)(()=>{let e=localStorage.getItem("Accordions2");return e?JSON.parse(e):["personal","organisation"]}),w=(0,i.usePathname)(),j=(0,a.useRef)(null),y=[{id:"personal",trigger:"Personal",content:[{title:"Account",icon:(0,o.jsx)(s.Z,{className:"w-5"}),link:"/client/settings/personal/account"},{title:"Notifications",icon:(0,o.jsx)(c.Z,{className:"w-5"}),link:"/client/settings/personal/notifications"},{title:"Security",icon:(0,o.jsx)(d.Z,{className:"w-5"}),link:"/client/settings/personal/security"}]},{id:"organisation",trigger:"Organisation",content:[{title:"General",icon:(0,o.jsx)(u.Z,{className:"w-5"}),link:"/client/settings/organisation/general"},{title:"User Management",icon:(0,o.jsx)(p.Z,{className:"w-5"}),link:"/client/settings/organisation/user-management"},{title:"Roles & Permissions",icon:(0,o.jsx)(s.Z,{className:"w-5"}),link:"/client/settings/organisation/roles-permissions"},{title:"Billing",icon:(0,o.jsx)(x.Z,{className:"w-5"}),link:"/client/settings/organisation/billing"}]}];return(0,a.useEffect)(()=>{let e=sessionStorage.getItem("sidebar-scroll");j.current&&e&&(j.current.scrollTop=parseInt(e,10))},[]),(0,o.jsxs)("div",{className:"fixed top-[60px] lg:left-[145px] h-[calc(100vh-60px)] bg-blue-300 lg:translate-x-0 ".concat((null==e?void 0:e.channelBar)===!0&&(null==e?void 0:e.openSidebar)?"translate-x-[145px]":(null==e?void 0:e.channelBar)!==!0||(null==e?void 0:e.openSidebar)?"-translate-x-full ":"translate-x-0","\n  pt-4 flex flex-col gap-4 w-[350px] transition-transform duration-300 ease-in-out z-20"),children:[(0,o.jsx)("div",{className:"flex items-center justify-between px-4",children:(0,o.jsx)("div",{className:"flex items-center gap-[5px] md:justify-between w-full",children:(0,o.jsx)("div",{className:"flex items-center gap-[5px] md:justify-between w-full",children:(0,o.jsx)(f.Z,{})})})}),(0,o.jsx)("div",{className:"overflow-auto text-blue-50 cursor-pointer px-3",ref:j,onClick:()=>t({type:l.a.CHANNEL_BAR,payload:!1}),children:(0,o.jsx)(h.Accordion,{type:"multiple",className:"w-full",value:n,onValueChange:e=>{g(e),localStorage.setItem("Accordions2",JSON.stringify(e))},children:y.map(e=>(0,o.jsxs)(h.AccordionItem,{value:e.id,className:"border-none",children:[(0,o.jsx)(h.AccordionTrigger,{className:"font-normal w-full py-0",children:(0,o.jsxs)("div",{className:"relative py-3 mx-2 flex items-center gap-1 rounded-lg cursor-pointer w-full",children:[(0,o.jsx)(m.K,{className:"transition-transform duration-300 ".concat(n.includes(e.id)?"rotate-0":"-rotate-90")}),(0,o.jsx)("h3",{className:"text-sm font-medium",children:e.trigger})]})}),(0,o.jsx)(h.AccordionContent,{children:(0,o.jsx)("ul",{className:"flex flex-col gap-1",children:e.content.map((e,t)=>(0,o.jsxs)("li",{className:"flex items-center gap-2 px-2 py-1 rounded-lg ".concat(w===e.link?"bg-blue-200":"hover:bg-blue-200"),children:[(0,o.jsx)("div",{className:"text-blue-50",children:e.icon}),(0,o.jsx)(v.default,{href:e.link,className:"text-sm leading-4 truncate w-full text-blue-50 ".concat(w===e.link?"text-white":""," "),children:e.title})]},t))})})]},e.id))})})]})}var w=n(86418),j=function(e){let{children:t}=e,{dispatch:n}=(0,a.useContext)(r.R);return(0,a.useEffect)(()=>{let e=localStorage.getItem("orgId");(async()=>{let t=await (0,w.Gl)("/organisations/".concat(e,"/roles"));if((null==t?void 0:t.status)===200||(null==t?void 0:t.status)===201){var o;n({type:l.a.ORG_ROLES,payload:null==t?void 0:null===(o=t.data)||void 0===o?void 0:o.data})}})()},[n]),(0,o.jsxs)("div",{className:"w-full flex relative",children:[(0,o.jsx)(g,{}),(0,o.jsx)("div",{className:"w-full lg:ml-[495px] mt-[60px]",children:t})]})}},99625:function(e,t,n){"use strict";n.d(t,{Accordion:function(){return i},AccordionContent:function(){return d},AccordionItem:function(){return s},AccordionTrigger:function(){return c}});var o=n(75376),a=n(32486),r=n(75737),l=n(58983);let i=r.fC,s=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,o.jsx)(r.ck,{ref:t,className:(0,l.cn)("border-b",n),...a})});s.displayName="AccordionItem";let c=a.forwardRef((e,t)=>{let{className:n,children:a,...i}=e;return(0,o.jsx)(r.h4,{className:"flex",children:(0,o.jsx)(r.xz,{ref:t,className:(0,l.cn)("flex flex-1 text-left items-start justify-between py-4 font-semibold transition-all [&[data-state=open]>svg]:rotate-180",n),...i,children:a})})});c.displayName=r.xz.displayName;let d=a.forwardRef((e,t)=>{let{className:n,children:a,...i}=e;return(0,o.jsx)(r.VY,{ref:t,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...i,children:(0,o.jsx)("div",{className:(0,l.cn)("pb-4 pt-0",n),children:a})})});d.displayName=r.VY.displayName},74178:function(e,t,n){"use strict";n.d(t,{J2:function(){return i},xo:function(){return s},yk:function(){return c}});var o=n(75376),a=n(32486),r=n(80500),l=n(58983);let i=r.fC,s=r.xz,c=a.forwardRef((e,t)=>{let{className:n,align:a="center",sideOffset:i=4,...s}=e;return(0,o.jsx)(r.h_,{children:(0,o.jsx)(r.VY,{ref:t,align:a,sideOffset:i,className:(0,l.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n),...s})})});c.displayName=r.VY.displayName},58983:function(e,t,n){"use strict";n.d(t,{cn:function(){return r},k:function(){return l}});var o=n(89824),a=n(97215);function r(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,a.m6)((0,o.W)(t))}let l=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"},86418:function(e,t,n){"use strict";n.d(t,{Gl:function(){return l},HH:function(){return s},Q_:function(){return c},_x:function(){return u},an:function(){return p},i1:function(){return h},jx:function(){return x},x9:function(){return d},xo:function(){return i}});var o=n(20818),a=n(13352);let r=n(18648).env.NEXT_PUBLIC_BASE_URL,l=async e=>{let t=localStorage.getItem("token")||"";try{return await o.Z.get(r+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var n,a,l;return((null==e?void 0:null===(n=e.response)||void 0===n?void 0:n.status)===401||(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(a=l.data)||void 0===a?void 0:a.status_code)===401)&&(localStorage.clear(),window.location.href="/auth/login"),e}},i=async(e,t)=>{let n=localStorage.getItem("token")||"";try{return await o.Z.post(r+e,t,{headers:{Authorization:"Bearer ".concat(n),"Content-Type":"application/json"}})}catch(e){var l,i,s,c,d;return a.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(l=i.data)||void 0===l?void 0:l.message),(null==e?void 0:null===(s=e.response)||void 0===s?void 0:s.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(c=d.data)||void 0===c?void 0:c.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},s=async(e,t)=>{let n=localStorage.getItem("token")||"";try{return await o.Z.post(r+e,t,{headers:{Authorization:"Bearer ".concat(n),"Content-Type":"application/json"}})}catch(e){var l,i;return a.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(l=i.data)||void 0===l?void 0:l.message),e}},c=async(e,t)=>{let n=localStorage.getItem("token")||"";try{return await o.Z.post(r+e,t,{headers:{Authorization:"Bearer ".concat(n),"Content-Type":"application/json"}})}catch(e){return e}},d=async(e,t)=>{let n=localStorage.getItem("token")||"";try{return await o.Z.post(r+e,t,{headers:{Authorization:"Bearer ".concat(n),"Content-Type":"multipart/form-data"}})}catch(e){var l,i,s,c,d;return a.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(l=i.data)||void 0===l?void 0:l.message),(null==e?void 0:null===(s=e.response)||void 0===s?void 0:s.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(c=d.data)||void 0===c?void 0:c.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},u=async(e,t)=>{let n=localStorage.getItem("token")||"";try{return await o.Z.patch(r+e,t,{headers:{Authorization:"Bearer ".concat(n),"Content-Type":"application/json"}})}catch(e){var l,i,s,c,d;return a.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(l=i.data)||void 0===l?void 0:l.message),(null==e?void 0:null===(s=e.response)||void 0===s?void 0:s.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(c=d.data)||void 0===c?void 0:c.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},p=async(e,t)=>{let n=localStorage.getItem("token")||"";try{return await o.Z.put(r+e,t,{headers:{Authorization:"Bearer ".concat(n),"Content-Type":"application/json"}})}catch(e){var l,i,s,c,d;return a.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(l=i.data)||void 0===l?void 0:l.message),(null==e?void 0:null===(s=e.response)||void 0===s?void 0:s.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(c=d.data)||void 0===c?void 0:c.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},x=async e=>{let t=localStorage.getItem("token")||"";try{return await o.Z.delete(r+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var n,l;return a.Z.error(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(n=l.data)||void 0===n?void 0:n.message),e}},h=async e=>{let t=localStorage.getItem("token")||"";try{return await o.Z.delete(r+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){return e}}},26242:function(e,t,n){"use strict";n.d(t,{BY:function(){return r},CZ:function(){return o},Qm:function(){return a},ju:function(){return l},mu:function(){return s},z2:function(){return i}}),n(32486),n(13352);let o=e=>e<1e3?e.toString():e<1e6?"".concat(Math.floor(e/1e3),"k+"):"".concat(Math.floor(e/1e6),"m+"),a=e=>null==e?void 0:e.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2),r=e=>e.sort((e,t)=>e.name.toLowerCase()<t.name.toLowerCase()?-1:e.name.toLowerCase()>t.name.toLowerCase()?1:0);function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=document.createElement("div");return t.innerHTML=e,t.textContent||t.innerText||""}function i(e){return"active"===e?"text-green-600 bg-green-50 border-green-200":"text-gray-600 bg-gray-50 border-gray-200"}function s(e,t){e(!0),t&&t(),setTimeout(()=>e(!1),1e3)}}},function(e){e.O(0,[8863,7140,6092,4144,5300,5614,6343,6329,9254,1121,7220,937,7542,2344,1744],function(){return e(e.s=93199)}),_N_E=e.O()}]);