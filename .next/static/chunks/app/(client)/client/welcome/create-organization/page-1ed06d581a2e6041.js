(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1369],{33182:function(e,a,t){Promise.resolve().then(t.bind(t,87452))},39713:function(e,a,t){"use strict";t.d(a,{default:function(){return n.a}});var l=t(74033),n=t.n(l)},47411:function(e,a,t){"use strict";var l=t(13362);t.o(l,"useParams")&&t.d(a,{useParams:function(){return l.useParams}}),t.o(l,"usePathname")&&t.d(a,{usePathname:function(){return l.usePathname}}),t.o(l,"useRouter")&&t.d(a,{useRouter:function(){return l.useRouter}}),t.o(l,"useSearchParams")&&t.d(a,{useSearchParams:function(){return l.useSearchParams}})},74033:function(e,a,t){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),function(e,a){for(var t in a)Object.defineProperty(e,t,{enumerable:!0,get:a[t]})}(a,{default:function(){return r},getImageProps:function(){return o}});let l=t(60723),n=t(25738),s=t(28863),i=l._(t(44543));function o(e){let{props:a}=(0,n.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,t]of Object.entries(a))void 0===t&&delete a[e];return{props:a}}let r=s.Image},87452:function(e,a,t){"use strict";t.r(a);var l=t(75376),n=t(39713),s=t(47411),i=t(32486),o=t(11492),r=t(25575),u=t(73003),c=t(10952),d=t(66024),m=t(35580),x=t(56039);let f={organisationName:"",organisationType:"",organisationEmail:""};a.default=()=>{let e=(0,s.useRouter)(),[a,t]=(0,i.useState)(!1),[h,g]=(0,i.useState)(f),[b,v]=(0,i.useState)(!0),[p,j]=(0,i.useState)([]),[N,y]=(0,i.useState)(null),{firstChannel:w}=(0,x.Z)();(0,i.useEffect)(()=>{(async()=>{var a,t;let l=localStorage.getItem("token")||"",n=await (0,c.Gl)("/auth/onboard-status",l);(null==n?void 0:null===(t=n.data)||void 0===t?void 0:null===(a=t.data)||void 0===a?void 0:a.status)?e.push("/client"):v(!1),v(!1)})()},[e]),(0,i.useEffect)(()=>{let e=d.Z.getAllCountries();j(null==e?void 0:e.map(e=>({label:e.name,value:e.name})))},[]);let S=e=>{let{name:a,value:t}=e.target;g({...h,[a]:t})},E=async e=>{e.preventDefault();let a=localStorage.getItem("token")||"";t(!0);let l={name:h.organisationName,type:h.organisationType,country:null==N?void 0:N.value,email:h.organisationEmail},n=await (0,c.xo)("/organisations",l,a);if((null==n?void 0:n.status)===200||(null==n?void 0:n.status)===201){var s,i,o,r;await (0,c.an)("/auth/onboard-status",{},a),localStorage.setItem("orgId",null==n?void 0:null===(i=n.data)||void 0===i?void 0:null===(s=i.data)||void 0===s?void 0:s.id);let e=await w(null==n?void 0:null===(r=n.data)||void 0===r?void 0:null===(o=r.data)||void 0===o?void 0:o.id);window.location.href="/client/home/<USER>/".concat(e)}else t(!1)};return(0,l.jsx)("div",{className:"w-full mt-0",children:(0,l.jsxs)("div",{className:"w-full max-w-2xl mx-auto",children:[(0,l.jsx)("div",{className:"mt-10 mb-10 mx-auto",children:(0,l.jsxs)("div",{className:"flex gap-2 justify-center md:hidden mb-20",children:[(0,l.jsx)(n.default,{src:"/TelexIcon.svg",alt:"Icon",width:40,height:40}),(0,l.jsx)("h1",{className:"font-semibold text-2xl text-center flex justify-center items-center",children:"Telex"})]})}),(0,l.jsx)("div",{className:"mx-3 md:mx-10",children:b?(0,l.jsx)("div",{className:"w-full flex justify-center mt-20",children:(0,l.jsx)(u.Z,{width:"40",height:"40",color:"blue"})}):(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"font-semibold sm:text-3xl text-2xl text-center",children:"Create Your Organization"}),(0,l.jsx)("p",{className:" text-[#6E6E6F] text-md mt-4 mb-6 text-center",children:"Input the details of your organization below"}),(0,l.jsxs)("form",{onSubmit:E,children:[(0,l.jsxs)("div",{className:"form-box mb-3",children:[(0,l.jsx)("label",{className:"mb-2 block text-sm",children:"Organization Name"}),(0,l.jsx)(r.I,{placeholder:"Enter your organization Name",className:"focus:border-blue-500 py-6",type:"text",value:h.organisationName,onChange:S,name:"organisationName"})]}),(0,l.jsxs)("div",{className:"form-box mb-3",children:[(0,l.jsx)("label",{className:"mb-2 block text-sm font-semibold",children:"Organization Email"}),(0,l.jsx)(r.I,{placeholder:"Enter your organization Email",className:"focus:border-blue-500 py-6",type:"email",value:h.organisationEmail,onChange:S,name:"organisationEmail",required:!0})]}),(0,l.jsxs)("div",{className:"form-box mb-3",children:[(0,l.jsx)("label",{className:"mb-2 block text-sm",children:"Organization Type"}),(0,l.jsx)(r.I,{placeholder:"What does your organization do",className:"focus:border-blue-500 py-6",type:"text",value:h.organisationType,onChange:S,name:"organisationType"})]}),(0,l.jsx)("div",{className:"flex flex-col sm:flex-row align-center justify-between gap-6",children:(0,l.jsxs)("div",{className:"form-box sm:mb-3 w-full",children:[(0,l.jsx)("label",{className:"mb-2 block text-sm",children:"Country"}),(0,l.jsx)(m.Z,{options:p,placeholder:"Select an option...",onChange:y,defaultValue:N,isDisabled:!1})]})}),(0,l.jsx)(o.z,{type:"submit",className:"w-full bg-blue-400 font-semibold my-3 py-6 px-10 text-white",children:a?(0,l.jsxs)("span",{className:"flex items-center gap-x-2",children:[(0,l.jsx)("span",{className:"animate-pulse",children:"Loading..."})," ",(0,l.jsx)(u.Z,{width:"20",height:"40"})]}):"Submit"})]})]})})]})})}}},function(e){e.O(0,[4269,8863,7140,5300,2987,6329,8992,6024,2037,7542,2344,1744],function(){return e(e.s=33182)}),_N_E=e.O()}]);