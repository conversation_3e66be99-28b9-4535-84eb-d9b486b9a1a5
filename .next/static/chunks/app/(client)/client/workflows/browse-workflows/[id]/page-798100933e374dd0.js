(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5621],{6780:function(e,t,A){Promise.resolve().then(<PERSON>.bind(A,52135))},52135:function(e,t,A){"use strict";A.r(t),A.d(t,{default:function(){return v}});var a=A(75376),i=A(32486),r=A(57292),n=A(51888),l=A(39713),o=A(5426),s=e=>{let{title:t,imageUrl:A,buttonText:i,buttonIcon:n,inputPlaceholder:l,onButtonClick:s,moreIcon:d}=e;return(0,a.jsxs)("header",{className:"flex items-center p-5 justify-between border-b border-[#E6EAEF] bg-white",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[A&&(0,a.jsx)("img",{src:A,alt:"icon",className:"w-8 h-8 rounded-full"}),(0,a.jsx)("h1",{className:"text-lg text-[#1D2939] font-bold",children:t})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[l?(0,a.jsxs)("div",{className:"w-[272px] h-[36px] rounded-md shadow-md border border-[#E6EAEF] p-[10px] gap-[6px] flex items-center",children:[(0,a.jsx)(r.Z,{size:16,color:"#98A2B3"}),(0,a.jsx)("input",{type:"text",placeholder:l,className:"text-[#98A2B3] focus:outline-none w-full"})]}):i||n?(0,a.jsxs)("button",{onClick:s,className:"w-8 h-8 flex justify-center shadow-sm border border-[#E6EAEF] items-center gap-2 px-4 py-2 text-sm font-medium text-white rounded-[5px]",children:[n&&(0,a.jsx)("span",{children:n}),i]}):null,(0,a.jsx)("div",{className:"w-8 h-8 flex items-center justify-center rounded-[5px] border border-[#E6EAEF] shadow-sm",children:d||(0,a.jsx)(o.Z,{className:"w-6 h-6 text-gray-600 cursor-pointer hover:text-gray-800"})})]})]})},d=A(97220),c=A(47411),u=A(56792),g=A(31415),h=A(86418),m=A(73003);function v(){var e,t,A,o;let[v,x]=(0,i.useState)("Details"),{state:p}=(0,i.useContext)(d.R),b=(0,c.useRouter)(),w=(0,c.useParams)().id,[f,E]=(0,i.useState)(!0),C=(0,c.useSearchParams)().get("json_url")||"",[B,S]=(0,i.useState)(!1),[U,j]=(0,i.useState)(null);(0,i.useEffect)(()=>{let e=localStorage.getItem("orgId")||"";w&&(async()=>{j((await (0,h.Gl)("/organisations/".concat(e,"/workflows/").concat(w))).data.data),E(!1)})()},[C,w]);let N=async(e,t)=>{S(e),console.log(t)},y=["Details",...B?["Settings","Output"]:[],"Usage",...B?["Key"]:[]];return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s,{title:"Workflow Profile",buttonIcon:(0,a.jsx)(r.Z,{size:20,className:"text-gray-600 cursor-pointer hover:text-gray-800"})}),f?(0,a.jsx)("div",{className:"flex items-center justify-center mt-20",children:(0,a.jsx)(m.Z,{width:"40",height:"40",color:"blue"})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-3 text-sm py-3 border-b px-4 md:px-5",children:[(0,a.jsxs)("span",{onClick:()=>b.back(),className:"text-[#667085] cursor-pointer",children:[null==p?void 0:p.topLabel," Workflows"]}),(0,a.jsx)(n.Z,{}),(0,a.jsx)("span",{className:"break-all",children:null==U?void 0:null===(e=U.descriptions)||void 0===e?void 0:e.app_name})]}),(0,a.jsxs)("div",{className:"bg-white p-4 md:p-6 mx-auto",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-center gap-4 w-full md:w-auto",children:[(0,a.jsx)("div",{className:"w-[100px] h-[100px] min-w-[100px] border rounded-xl",children:(0,a.jsx)(l.default,{src:(null==U?void 0:null===(t=U.descriptions)||void 0===t?void 0:t.app_logo)||"/image/bluebot.svg",alt:null==U?void 0:null===(A=U.descriptions)||void 0===A?void 0:A.app_name,width:100,height:100,className:"rounded-xl bg-green-100"})}),(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold flex items-center gap-2 flex-wrap",children:null==U?void 0:U.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:(null==U?void 0:U.description)||"Workflow description"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 rounded-3xl border ".concat((null==U?void 0:U.is_active)?"border-[#7141F8]":""," py-5 px-3 h-0"),children:[(0,a.jsx)("span",{className:"text-sm ".concat((null==U?void 0:U.is_active)?"text-[#7141F8]":"text-[#667085]"),children:(null==U?void 0:U.is_active)?"Enabled":"Disabled"}),(0,a.jsx)(g.r,{className:"bg-green-500",checked:B,onCheckedChange:e=>{N(e,w)}})]})]}),(0,a.jsx)("div",{className:"mt-8 border-b border-gray-200 overflow-x-auto",children:(0,a.jsx)("div",{className:"flex gap-4 min-w-max",children:y.map(e=>(0,a.jsx)("button",{onClick:()=>x(e),className:"px-3 py-2 text-sm font-medium whitespace-nowrap ".concat(v===e?"text-indigo-600 border-b-2 border-indigo-600":"text-gray-500 hover:text-gray-700"),children:e},e))})}),"Details"===v&&(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("h2",{className:"font-semibold text-sm text-gray-800 mb-4",children:["Agents that works with ",null==U?void 0:U.name]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-5 items-start",children:null==U?void 0:null===(o=U.agents_details)||void 0===o?void 0:o.map((e,t)=>(0,a.jsx)("div",{className:"transition-all duration-300 border rounded-xl bg-white shadow-sm hover:border-[#D0D0FD] cursor-pointer",children:(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border-b",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("div",{className:"relative h-9 w-9 rounded-lg flex items-center justify-center border border-[#E6EAEF]",children:[(0,a.jsx)(l.default,{src:(null==e?void 0:e.app_logo)||u.Z.blueBot,alt:"Bot",width:20,height:20,className:"h-8 w-8"}),(0,a.jsx)("div",{className:"absolute -bottom-[3px] -right-[3px] w-[10px] h-[10px] bg-[#6DC347] rounded-full border border-white"})]}),(0,a.jsx)("div",{children:(0,a.jsx)("h2",{className:"text-sm font-semibold text-[#344054]",children:e.name})})]}),(0,a.jsx)("div",{className:"text-xs text-[#667085] border border-[#D0D5DD] rounded-full px-2 py-[2px]",children:t+1})]})},t))})]})]})]})]})}},56792:function(e,t,A){"use strict";A.d(t,{Z:function(){return a}});var a={bot:{src:"/_next/static/media/bot.dfdf2c7a.png",height:80,width:80,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAHlBMVEVMaXEAYC4AlUQAm0YAjUEAfDgrJCgKXTEAj0EAkEKCxuBiAAAACnRSTlMAB73oOFyq4Hx6x9l+tAAAAAlwSFlzAAAsSwAALEsBpT2WqQAAAChJREFUeJxjYEACjIxQBhsbhGZlZ2cF0SxMzMxMLCAVHExMnGiKwQAACx0AUUBiaOwAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},RedBot:{src:"/_next/static/media/bot-red.3a55df2b.png",height:40,width:40,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAIVBMVEVMaXFQAAB3BwJtCARtCQReBQB4CgJvCgRLEAskLS4hKCqjVWtSAAAAC3RSTlMABO01ulzJe9m0qZQuNjoAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAoSURBVHicY2BAAoyMUJqTC8Jg5eBgBdHMbExMbMwgGXYWFnZUxRAAAA2RAGBI3TANAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},blueBot:{src:"/_next/static/media/blue-bot.f296e2d3.png",height:40,width:40,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAKlBMVEXv7/rr7vjt7vj19v7x8f6Hh/ivr/q6u+9gYapzc//Y1/3T0v1mZ2JdX1kQXXeiAAAAA3RSTlP+nJxhYYB9AAAACXBIWXMAAAsTAAALEwEAmpwYAAAANklEQVR4nBXKyQ3AMBDEMNma9ZWk/3YDfwnSJGDHBBJBvtcAzrXmhaf2riNkVI37TaLS5cb2AyAvAORP3/L0AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},google:{src:"/_next/static/media/google.3c81f1fb.svg",height:16,width:16,blurWidth:0,blurHeight:0},send:{src:"/_next/static/media/send.7163c084.svg",height:18,width:17,blurWidth:0,blurHeight:0},disco:{src:"/_next/static/media/disco.672244de.png",height:192,width:192,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAaVBMVEVMaXHi2M2nopzZsWDKqnHy01GUkIC0m2ODeG8Zl/84f+Lmlwbyuy7Oplrew47k0KassLjamCLdr13j5eXVx6WwqJ7cniSXin7CwLzcw5mPfmV/dWh/fHevtq++qnPu7/TCu7bXybm2t7Oodw53AAAAH3RSTlMA/IlTgiVGFf0LJCo85G/2o3DNqIb6jPf4+/h6mErb4mYc6AAAAAlwSFlzAAAhOAAAITgBRZYxYAAAAEBJREFUeJwlwccBgCAAALGjgw17V9D9h/RhAhSBn7MYB2XVxKHvQNciJaEhtHlaXwmMz+L9Ccxb9vt9gVPysMp8UogCzxGkjJoAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},teammates:{src:"/_next/static/media/teammates.bbd3adac.svg",height:32,width:32,blurWidth:0,blurHeight:0},facebook:{src:"/_next/static/media/facebook.fca64f6c.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAALVBMVEVWmvD//fpHkvBNlfAMcfMee/HX6Py10/kpgvOEt/f///+ex/jG3foAZPJgovbihSuXAAAABHRSTlP+4f7+rTCusQAAAAlwSFlzAAAsSwAALEsBpT2WqQAAADhJREFUeJwFwYkRwDAMAjDsA/wk6f7jVkIYYjrgpuaTAXJ2CEisogTx7B4KYN/bBNyv6rURzqp0/DHzAXV/loHlAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},twitter:{src:"/_next/static/media/twitter.913bc175.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAGFBMVEUAAAABAQEeHh4PDw9qampFRUVYWFhSUlIoHbPFAAAAAXRSTlPhl+sKiAAAAAlwSFlzAAAsSwAALEsBpT2WqQAAAC9JREFUeJw1i0EOACAMwihD/f+PzWbk1KRFYiZRqzAIsr0GnLSDSp463mkoT69/vxFyAH2eHpTfAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},instagram:{src:"/_next/static/media/instagram.07bbefde.png",height:96,width:96,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAPFBMVEX6+vr++v76tezYrP3+pcn9tL77pen6+vn8//////7+r8/+wK7+4an/rNf+07Hpqvz/7aj6ed3+3Lb+rKgwumMZAAAACHRSTlPg////////4NsSfKYAAAAJcEhZcwAAITgAACE4AUWWMWAAAAA+SURBVHicFcs5EsAgDANAYWOQuJP8/68Zum0WkCQKUHLflgRtt/IYwTJnyZngFz3axRrvikZI54yebiNJ1R9EDgII9TTKpAAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},youtube:{src:"/_next/static/media/youtube.b9208f26.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAGFBMVEX5+fn9/////Pz8WVn8Hh78JSX+AAD8n57mNweJAAAAAXRSTlPhl+sKiAAAAAlwSFlzAAAsSwAALEsBpT2WqQAAACxJREFUeJxNi7ENADAMg4ydNP9/3ClVNyRAwmAksG0Q01U9iJzk5IenNt79AhbIAKGvX29oAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},tiktok:{src:"/_next/static/media/tiktok.029728a5.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAPFBMVEUBAQFMHietfojQv8Y1Exqzs7cBDQwAAAAAAAAZCQxVgYLI9PSxxcp1fIFNjYtHREYdOTfPr7sdVlNtoqCdn0cDAAAACHRSTlPh////////4WC/TK4AAAAJcEhZcwAALEsAACxLAaU9lqkAAAA4SURBVHicY2DgAAMGEM3GxcnBAWZws3KCGHy8rEwsHAwcQsyMPCwgKQFmYUGIGn4mRpA2doh2dgBFkAH9NNczawAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},channel:{src:"/_next/static/media/channel.71b0d2d9.png",height:1827,width:1986,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAMAAAACh/xsAAAAFVBMVEX6+P77+v328//7+/3w6/3////u5v959aEiAAAABXRSTlP94vju40yQVCYAAAAJcEhZcwAAITgAACE4AUWWMWAAAAAnSURBVHicY2BkAgMWBhY2MGBhYGYAAVZGBmZWMEBhMLCygqQYoWoAFpgAqnCsrYsAAAAASUVORK5CYII=",blurWidth:8,blurHeight:7},user:{src:"/_next/static/media/user.cc6e44b6.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAG1BMVEXz/vwD0sHq/PpK4NPU+PVi5NkY18jB9PCF6uJqQ8ocAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAM0lEQVR4nB3JwQ3AMAzEMMlnO9l/4iL9ESBVAFUUPdM8jM6PaH508oq+M7fhrOoe4uqaDxOVAKOLLWTAAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},createChannel:{src:"/_next/static/media/create-channel.d750c07c.png",height:2332,width:2544,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAMAAAACh/xsAAAAD1BMVEX+/v7t6P308vz6+vrm5OpbWQr2AAAACXBIWXMAACxLAAAsSwGlPZapAAAAJ0lEQVR4nEXHwQ0AMAzCQDDZf+aKKFKPD5bNko+mDApDEn2tbk/7AQyWAF9SvWHtAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:7},megaphone:{src:"/_next/static/media/megaphone.d567f808.png",height:24,width:24,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAJ1BMVEVMaXEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACv15x2AAAADXRSTlMAaRhcwINMCyY5ndOqyc6ZIAAAAAlwSFlzAAALEwAACxMBAJqcGAAAADRJREFUeJxVyrkNACAMwEDnI5Cw/7wIiYarXBg+OhRySZUba4dNRDAfmjeIbg83yBDmnZ8DGa0AvpK+HLsAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},visaLogo:{src:"/_next/static/media/visa-logo.e39f2d7e.svg",height:12,width:33,blurWidth:0,blurHeight:0},fileDoc:{src:"/_next/static/media/FileDoc.69079eba.svg",height:20,width:20,blurWidth:0,blurHeight:0},border:{src:"/_next/static/media/Border.8c3c175f.svg",height:1024,width:44,blurWidth:0,blurHeight:0}}},73003:function(e,t,A){"use strict";var a=A(75376);A(32486);var i=A(10983);t.Z=e=>{let{height:t,width:A,color:r}=e;return(0,a.jsx)(i.iT,{height:t||20,width:A||20,color:r||"#fff",visible:!0,ariaLabel:"oval-loading",secondaryColor:r||"#fff",strokeWidth:5,strokeWidthSecondary:5})}},31415:function(e,t,A){"use strict";A.d(t,{r:function(){return l}});var a=A(75376),i=A(32486),r=A(70144),n=A(58983);let l=i.forwardRef((e,t)=>{let{className:A,...i}=e;return(0,a.jsx)(r.fC,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",A),...i,ref:t,children:(0,a.jsx)(r.bU,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});l.displayName=r.fC.displayName},58983:function(e,t,A){"use strict";A.d(t,{cn:function(){return r},k:function(){return n}});var a=A(89824),i=A(97215);function r(){for(var e=arguments.length,t=Array(e),A=0;A<e;A++)t[A]=arguments[A];return(0,i.m6)((0,a.W)(t))}let n=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"},86418:function(e,t,A){"use strict";A.d(t,{Gl:function(){return n},HH:function(){return o},Q_:function(){return s},_x:function(){return c},an:function(){return u},i1:function(){return h},jx:function(){return g},x9:function(){return d},xo:function(){return l}});var a=A(20818),i=A(13352);let r=A(18648).env.NEXT_PUBLIC_BASE_URL,n=async e=>{let t=localStorage.getItem("token")||"";try{return await a.Z.get(r+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var A,i,n;return((null==e?void 0:null===(A=e.response)||void 0===A?void 0:A.status)===401||(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(i=n.data)||void 0===i?void 0:i.status_code)===401)&&(localStorage.clear(),window.location.href="/auth/login"),e}},l=async(e,t)=>{let A=localStorage.getItem("token")||"";try{return await a.Z.post(r+e,t,{headers:{Authorization:"Bearer ".concat(A),"Content-Type":"application/json"}})}catch(e){var n,l,o,s,d;return i.Z.error(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(n=l.data)||void 0===n?void 0:n.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(s=d.data)||void 0===s?void 0:s.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},o=async(e,t)=>{let A=localStorage.getItem("token")||"";try{return await a.Z.post(r+e,t,{headers:{Authorization:"Bearer ".concat(A),"Content-Type":"application/json"}})}catch(e){var n,l;return i.Z.error(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(n=l.data)||void 0===n?void 0:n.message),e}},s=async(e,t)=>{let A=localStorage.getItem("token")||"";try{return await a.Z.post(r+e,t,{headers:{Authorization:"Bearer ".concat(A),"Content-Type":"application/json"}})}catch(e){return e}},d=async(e,t)=>{let A=localStorage.getItem("token")||"";try{return await a.Z.post(r+e,t,{headers:{Authorization:"Bearer ".concat(A),"Content-Type":"multipart/form-data"}})}catch(e){var n,l,o,s,d;return i.Z.error(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(n=l.data)||void 0===n?void 0:n.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(s=d.data)||void 0===s?void 0:s.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},c=async(e,t)=>{let A=localStorage.getItem("token")||"";try{return await a.Z.patch(r+e,t,{headers:{Authorization:"Bearer ".concat(A),"Content-Type":"application/json"}})}catch(e){var n,l,o,s,d;return i.Z.error(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(n=l.data)||void 0===n?void 0:n.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(s=d.data)||void 0===s?void 0:s.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},u=async(e,t)=>{let A=localStorage.getItem("token")||"";try{return await a.Z.put(r+e,t,{headers:{Authorization:"Bearer ".concat(A),"Content-Type":"application/json"}})}catch(e){var n,l,o,s,d;return i.Z.error(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(n=l.data)||void 0===n?void 0:n.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(s=d.data)||void 0===s?void 0:s.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},g=async e=>{let t=localStorage.getItem("token")||"";try{return await a.Z.delete(r+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var A,n;return i.Z.error(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(A=n.data)||void 0===A?void 0:A.message),e}},h=async e=>{let t=localStorage.getItem("token")||"";try{return await a.Z.delete(r+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){return e}}}},function(e){e.O(0,[4269,8863,7140,5300,2987,3912,7220,7542,2344,1744],function(){return e(e.s=6780)}),_N_E=e.O()}]);