(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[815],{42725:function(e,t,s){Promise.resolve().then(s.bind(s,35288))},35288:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return w}});var a=s(75376),l=s(32486),i=s(39713),r=s(22397),n=s(18208),c=s(6769),o=s(94912),d=s(35575),x=s(47720),A=s(5426),p=s(56792),m=s(11492),h=s(99202),u=s(91697),g=s(79375),b=s(85150),f=s(46750);let v=[{id:1,sender:"Ruby - Social Media Handler",text:"Hello <PERSON>, what would you like me to do for you today?",timestamp:"2021-01-01 12:00:00",avatar:p.Z.bot,color:"#E6FAEF",loading:!0,type:"textBlock"},{id:2,sender:"Adaeze Ndupu",text:"I want to add social media handles for my brand",timestamp:"2021-01-01 12:00:00",avatar:"https://avatars.githubusercontent.com/u/1214686",type:"textBlock"},{id:3,type:"socialMediaBlock",text:"Let’s connect your accounts. Which platforms do you want me to monitor? You can add more later \uD83D\uDE09",timestamp:"2021-01-01 12:00:00",avatar:p.Z.bot,color:"#E6FAEF",platforms:[{name:"Instagram",icon:p.Z.instagram},{name:"Twitter",icon:p.Z.twitter},{name:"Tiktok",icon:p.Z.tiktok},{name:"Facebook",icon:p.Z.facebook},{name:"Youtube",icon:p.Z.youtube}]},{id:4,sender:"Adaeze Ndupu",text:"My notification settings?",timestamp:"2021-01-01 12:00:00",avatar:"https://avatars.githubusercontent.com/u/1214686",type:"textBlock"},{id:5,sender:"Ruby - Social Media Handler",text:"How would you like to be notified?",timestamp:"2021-01-01 12:00:00",avatar:p.Z.bot,color:"#E6FAEF",type:"optionBlock",options:["Real-time alerts (Instant notifications for mentions, trends, and engagement spikes)","Daily summary (A digest of everything important)","Both real-time & daily summary"]},{id:6,sender:"Adaeze Ndupu",text:"Yeah phrases like “ai language game” “learn a language”, “how to learn french”",timestamp:"2021-01-01 12:00:00",avatar:"https://avatars.githubusercontent.com/u/1214686",type:"textBlock"},{id:7,sender:"Ruby - Social Media Handler",text:"Want me to analyze sentiment? I'll flag positive, neutral, and negative mentions",timestamp:"2021-01-01 12:00:00",avatar:p.Z.bot,color:"#E6FAEF",type:"optionSubmitBlock",options:["Yes","No"],submitText:"Send"},{id:8,sender:"Adaeze Ndupu",text:"I would love to add some links",timestamp:"2021-01-01 12:00:00",avatar:"https://avatars.githubusercontent.com/u/1214686",type:"textBlock"},{id:9,sender:"Ruby - Social Media Handler",text:"Want to track engagement on specific posts? Share the link to the post in question",timestamp:"2021-01-01 12:00:00",avatar:p.Z.bot,color:"#E6FAEF",type:"linkBlock"},{id:10,sender:"Adaeze Ndupu",text:"I want to see some alerts",timestamp:"2021-01-01 12:00:00",avatar:"https://avatars.githubusercontent.com/u/1214686",type:"textBlock"},{id:11,sender:"Ruby - Social Media Handler",text:"All alerts will be sent there",timestamp:"2021-01-01 12:00:00",avatar:p.Z.bot,color:"#E6FAEF",options:[{name:"Red Alerts",description:"Critical information is being shared",icon:"\uD83D\uDD34"},{name:"Green Alerts",description:"Routine updates and general insights are being shared",icon:"\uD83D\uDFE2"},{name:"Black Alerts",description:"Actions that require user intervention are being shared",icon:"⚫"}],type:"alertBlock"},{id:12,sender:"Adaeze Ndupu",text:"I want to see how my posts are performing",timestamp:"2021-01-01 12:00:00",avatar:"https://avatars.githubusercontent.com/u/1214686",type:"textBlock"},{id:13,sender:"Ruby - Social Media Handler",timestamp:"2021-01-01 12:00:00",avatar:p.Z.bot,type:"postBlock",platform:"Instagram",icon:p.Z.instagram,color:"#E6FAEF",posts:[{alert:"green",review:"Your latest post is gaining traction!",image:"https://avatars.githubusercontent.com/u/1214686",title:"Product Launch day!\uD83D\uDE80",likes:"+5k",comments:"230",shares:"180",suggestion:"Reply to top comments & use stories to drive traffic",type:"media"},{alert:"red",review:"Negative Comment Alert \uD83D\uDEA8",type:"comment",comment:"Your website is slow! Tried checking out, but it crashed",sender:"AdebisiEmediong",suggestion:"Acknowledge & offer a solution"}]},{id:13,sender:"Adaeze Ndupu",text:"What about tiktok",timestamp:"2021-01-01 12:00:00",avatar:"https://avatars.githubusercontent.com/u/1214686",type:"textBlock"},{id:13,sender:"Ruby - Social Media Handler",timestamp:"2021-01-01 12:00:00",avatar:p.Z.bot,type:"postBlock",color:"#E6FAEF",platform:"Tiktok",icon:p.Z.tiktok,posts:[{alert:"green",review:"New Video Performance",image:"https://avatars.githubusercontent.com/u/1214686",title:"AI in Action at AlexTech",likes:"1.2k",comments:"400",shares:"180",suggestion:"Engage with comments & post a follow-up",type:"media"},{alert:"black",review:"Competitor Alert!",image:"https://avatars.githubusercontent.com/u/1214686",title:"@Allnnovators just posted about a similar topic!",suggestion:"Duet their video for engagement boost",type:"media"}]},{id:14,sender:"Knox - Code Analyser",text:"To get started, I need access to your code. You can either:",timestamp:"2021-01-01 12:00:00",avatar:p.Z.bot,color:"#FEE8E6",type:"codeOptionsBlock",options:["Upload Project","Paste Code"]},{id:15,sender:"Knox - Code Analyser",timestamp:"2021-01-01 12:00:00",avatar:p.Z.bot,type:"codeAlertBlock",color:"#FEE8E6",text:"I found 1 critical security vulnerability in your code",codes:[{language:"php",code:"<?php\n$username = $_GET['username'];\n$query = \"SELECT * FROM users WHERE username = '$username'\";\n$result = mysqli_query($conn, $query);\n?>||",errorLines:[3],alert:"red"},{review:"⚠ SQL Injection Risk Found!",language:"php",problem:"This query is vulnerable because it inserts user input directly into the database.",riskLevel:"\uD83D\uDD34 Critical",solution:"Use prepared statements to prevent malicious injections.",code:'$stmt = $conn->prepare("SELECT * FROM users WHERE username = ?");\n$stmt->bind_param("s", $username);\n$stmt->execute();',alert:"red"}]},{id:16,sender:"Knox - Code Analyser",text:"Would you like to apply this fix now?",timestamp:"2021-01-01 12:00:00",avatar:p.Z.bot,color:"#FEE8E6",type:"codeOptionsBlock",options:["Mark as Reviewed","Apply fix"]},{id:17,sender:"Lynx - Site Broken Link Analyser",text:"Let’s analyse your links. What URLs would you like me to check? You can add multiple links \uD83D\uDE09",timestamp:"2021-01-01 12:00:00",avatar:p.Z.bot,color:"#FEE8E6",type:"domainLinkBlock",links:[]},{id:18,sender:"Lynx - Site Broken Link Analyser",text:"I found 1 broken link in your code",timestamp:"2021-01-01 12:00:00",avatar:p.Z.bot,color:"#FEE8E6",type:"summaryReportBlock",alert:"black",title:"Timbu Tech Ltd Summary Report",chartData:[{name:"Checked pages",value:5,color:"#F5A30B"},{name:"Checked links",value:99,color:"#7086FD"},{name:"Active Links",value:95,color:"#14B8A6"},{name:"Broken Links",value:4,color:"#FD7072"}],tableOverview:{title:"Blog",linkCount:35,brokenLinkCount:1},tableData:[{link:"https://www.timbu.com",status:"broken",issueType:"The page no longer exists (404 Error).",suggestedFix:"Redirect to a relevant article or remove the broken link."},{link:"https://www.timbu.com/update",status:"active",issueType:"---",suggestedFix:"---"}]}];var w=()=>{let[e,t]=(0,l.useState)(v),s=(0,l.useRef)(null),[w,j]=(0,l.useState)([]),[N,y]=(0,l.useState)({}),[E,k]=(0,l.useState)(!0),[F,B]=(0,l.useState)([]),[C,D]=(0,l.useState)(!0),[S,R]=(0,l.useState)([]),[M,U]=(0,l.useState)([]),[L,z]=(0,l.useState)(!0),[T,H]=(0,l.useState)(""),[V,W]=(0,l.useState)("markdown"),[Z,I]=(0,l.useState)({}),Y=()=>{var e;null===(e=s.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})};(0,l.useEffect)(()=>{Y()},[e]);let O=s=>{s.trim()&&(t([...e,{id:Date.now().toString(),text:s,timestamp:"2021-01-01 12:00:00",sender:"Adaeze Ndupu",avatar:"https://avatars.githubusercontent.com/u/1214686",type:"textBlock"}]),"paste code"===s.toLowerCase()&&setTimeout(()=>{K()},100),"apply fix"===s.toLowerCase()&&setTimeout(()=>{J()},100))},K=()=>{let e={id:Date.now().toString(),sender:"Knox - Code Analyser",timestamp:"2021-01-01 12:00:00",avatar:p.Z.bot,color:"#FEE8E6",type:"codeBlock",language:"markdown",code:"-----------------------------------------------\nPaste your code below for analysis:\n-----------------------------------------------",state:"input"};t(t=>[...t,e])},J=()=>{let e={id:15,sender:"Knox - Code Analyser",timestamp:"2021-01-01 12:00:00",avatar:p.Z.bot,type:"codeAlertBlock",color:"#FEE8E6",text:"Your code is now secure! Let me know if you need another scan.",codes:[{language:"php",code:'<?php\n$username = $_GET[\'username\'];\n// Using prepared statements to prevent SQL injection\n$stmt = $conn->prepare("SELECT * FROM users WHERE username = ?");\n$stmt->bind_param("s", $username);\n$stmt->execute();\n$result = $stmt->get_result();\n?>||',alert:"green"}]};t(t=>[...t,e])},P=e=>new Date(e).toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric",year:"numeric"}),Q=e=>{j(w.filter(t=>t.name!==e))},G=e=>{B(F.filter((t,s)=>s!==e))},X=e=>{R(S.filter((t,s)=>s!==e)),U(M.filter((t,s)=>s!==e))},q=()=>{let e=[{name:"Instagram",icon:p.Z.instagram},{name:"Twitter",icon:p.Z.twitter},{name:"Tiktok",icon:p.Z.tiktok},{name:"Facebook",icon:p.Z.facebook},{name:"Youtube",icon:p.Z.youtube}].find(e=>!w.some(t=>t.name===e.name));e&&j([...w,e])},_=()=>{B([...F,""])},$=()=>{R([...S,""]),U([...M,""])},ee=()=>{Object.keys(N).length===w.length&&Object.values(N).every(e=>e.trim())&&k(!1)},et=()=>{F.every(e=>e.trim())&&D(!1)},es=()=>{S.every(e=>e.trim())&&M.every(e=>e.trim())&&z(!1)},ea=(e,t)=>{y({...N,[e]:t})},el=(e,t)=>{let s=[...F];s[e]=t,B(s)},ei=(e,t)=>{let s=[...S];s[e]=t,R(s)},er=(e,t)=>{let s=[...M];s[e]=t,U(s)},en=s=>{if("textBlock"===s.type)return(0,a.jsx)("p",{className:"text-[#344054] text-[15px] mb-2",children:s.text});if("linkBlock"===s.type)return(0,a.jsxs)("div",{className:"flex flex-col gap-2 mb-2",children:[(0,a.jsx)("p",{className:"text-[#344054] text-[15px]",children:s.text}),(0,a.jsxs)("div",{className:"p-2 bg-[#F1F1FE] rounded-[5px] flex flex-col gap-4",children:[F.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("div",{className:"flex-1 max-w-[400px] flex items-center bg-white border border-[#E6EAEF] rounded py-[9px] px-3",children:[(0,a.jsx)("div",{className:"text-[#98A2B3] text-sm mr-2 whitespace-nowrap",children:"https:// |"}),(0,a.jsx)("input",{type:"text",placeholder:"example.com",value:e,onChange:e=>el(t,e.target.value),disabled:!C,className:"bg-transparent flex-1 outline-none text-[15px] text-[#344054] placeholder:text-[#98A2B3]"})]}),C&&(0,a.jsx)("button",{onClick:()=>G(t),children:(0,a.jsx)(r.Z,{size:20,strokeWidth:1.5,className:"text-[#344054]"})})]},t)),C&&(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)(m.z,{className:"flex items-center gap-2 p-0",onClick:_,children:[(0,a.jsx)(n.Z,{size:14,strokeWidth:1,className:"text-[#7141F8]"}),(0,a.jsx)("span",{className:"text-[#7141F8] text-xs",children:"Add a link"})]}),(0,a.jsx)(m.z,{className:"bg-[#7141F8] text-white h-8 w-full max-w-[75px]",onClick:et,disabled:!F.length||!F.every(e=>e.trim()),children:(0,a.jsx)("span",{children:"Send"})})]})]})]});if("domainLinkBlock"===s.type)return(0,a.jsxs)("div",{className:"flex flex-col w-full gap-2 mb-2",children:[(0,a.jsx)("p",{className:"text-[#344054] text-[15px]",children:s.text}),(0,a.jsxs)("div",{className:"p-2 bg-[#F1F1FE] rounded-[5px] flex flex-col gap-4 overflow-x-auto scrollbar-none [&::-webkit-scrollbar]:hidden",children:[S.length>0?S.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("div",{className:"flex gap-2 w-full max-w-[500px]",children:[(0,a.jsx)("div",{className:"flex w-[174px] max-w-[174px] items-center bg-white border border-[#E6EAEF] rounded py-[9px] px-3",children:(0,a.jsx)("input",{type:"text",placeholder:"E.g Main Domain",value:M[t]||"",onChange:e=>er(t,e.target.value),disabled:!L,className:"bg-transparent flex-1 outline-none text-[15px] text-[#344054] placeholder:text-[#98A2B3]"})}),(0,a.jsxs)("div",{className:"flex flex-1 items-center bg-white border border-[#E6EAEF] rounded py-[9px] px-3 overflow-hidden",children:[(0,a.jsx)("div",{className:"text-[#98A2B3] text-sm mr-2 whitespace-nowrap",children:"https:// |"}),(0,a.jsx)("input",{type:"text",placeholder:"example.com",value:e,onChange:e=>ei(t,e.target.value),disabled:!L,className:"bg-transparent outline-none text-[15px] text-[#344054] placeholder:text-[#98A2B3] min-w-0"})]})]}),L&&(0,a.jsx)("button",{onClick:()=>X(t),children:(0,a.jsx)(r.Z,{size:20,strokeWidth:1.5,className:"text-[#344054]"})})]},t)):(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("div",{className:"flex gap-2 w-full max-w-[500px]",children:[(0,a.jsx)("div",{className:"flex w-[174px] max-w-[174px] items-center bg-white border border-[#E6EAEF] rounded py-[9px] px-3",children:(0,a.jsx)("input",{type:"text",placeholder:"E.g Main Domain",className:"bg-transparent flex-1 outline-none text-[15px] text-[#344054] placeholder:text-[#98A2B3]",onChange:e=>{0===M.length&&er(0,e.target.value)}})}),(0,a.jsxs)("div",{className:"flex flex-1 items-center bg-white border border-[#E6EAEF] rounded py-[9px] px-3 overflow-hidden",children:[(0,a.jsx)("div",{className:"text-[#98A2B3] text-sm mr-2 whitespace-nowrap",children:"https:// |"}),(0,a.jsx)("input",{type:"text",placeholder:"example.com",className:"bg-transparent outline-none text-[15px] text-[#344054] placeholder:text-[#98A2B3]",onChange:e=>{0===S.length&&ei(0,e.target.value)}})]})]}),(0,a.jsx)("button",{onClick:()=>{0===S.length&&0===M.length&&(R([""]),U([""]))},children:(0,a.jsx)(r.Z,{size:20,strokeWidth:1.5,className:"text-[#344054]"})})]}),L&&(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)(m.z,{className:"flex items-center gap-2 p-0",onClick:$,children:[(0,a.jsx)(n.Z,{size:14,strokeWidth:1,className:"text-[#7141F8]"}),(0,a.jsx)("span",{className:"text-[#7141F8] text-xs",children:"Add a link"})]}),(0,a.jsx)(m.z,{className:"bg-[#7141F8] text-white h-8 w-full max-w-[75px]",onClick:es,disabled:!S.length||!S.every(e=>e.trim())||!M.every(e=>e.trim()),children:(0,a.jsx)("span",{children:"Send"})})]})]})]});if("socialMediaBlock"===s.type)return 0===w.length&&s.platforms&&j(s.platforms),(0,a.jsxs)("div",{className:"flex flex-col gap-2 mb-2",children:[(0,a.jsx)("p",{className:"text-[#344054] text-[15px]",children:s.text}),(0,a.jsxs)("div",{className:"p-2 bg-[#F1F1FE] rounded-[5px] flex flex-col gap-4",children:[w.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(i.default,{src:e.icon,alt:e.name,width:36,height:36,className:"rounded-[3px]"}),(0,a.jsxs)("div",{className:"flex-1 w-full max-w-[400px] flex items-center bg-white border border-[#E6EAEF] rounded py-[9px] px-3",children:[(0,a.jsx)("div",{className:"text-[#98A2B3] text-sm mr-2 whitespace-nowrap",children:"@ |"}),(0,a.jsx)("input",{type:"text",placeholder:"socialmediahandle",value:N[e.name]||"",onChange:t=>ea(e.name,t.target.value),disabled:!E,className:"bg-transparent flex-1 outline-none text-[15px] text-[#344054] placeholder:text-[#98A2B3]"})]}),E&&(0,a.jsx)("button",{onClick:()=>Q(e.name),children:(0,a.jsx)(r.Z,{size:20,strokeWidth:1.5,className:"text-[#344054]"})})]},"".concat(e.name,"-").concat(t))),E&&(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)(m.z,{className:"flex items-center gap-2 p-0",onClick:q,disabled:w.length>=5,children:[(0,a.jsx)(n.Z,{size:14,strokeWidth:1,className:"text-[#7141F8] disabled:text-[#98A2B3]"}),(0,a.jsx)("span",{className:"text-[#7141F8] disabled:text-[#98A2B3] text-xs",children:"Add an account"})]}),(0,a.jsx)(m.z,{className:"bg-[#7141F8] text-white h-8 w-full max-w-[75px]",onClick:ee,disabled:Object.keys(N).length!==w.length||!Object.values(N).every(e=>e.trim()),children:(0,a.jsx)("span",{children:"Send"})})]})]})]});if("optionBlock"===s.type)return(0,a.jsxs)("div",{className:"flex flex-col gap-2 mb-2",children:[(0,a.jsx)("p",{className:"text-[#344054] text-[15px]",children:s.text}),(0,a.jsx)("div",{className:"flex flex-wrap gap-4 p-2 rounded-[5px] bg-[#F1F1FE]",children:s.options.map((e,t)=>(0,a.jsx)("div",{className:"flex items-center gap-3 p-[10px] rounded-[9px] border border-[#E6EAEF] bg-white",children:(0,a.jsxs)("label",{htmlFor:"option-".concat(t),className:"flex items-center gap-3 cursor-pointer",children:[(0,a.jsx)("span",{className:"text-[#344054] text-sm",children:e}),(0,a.jsxs)("div",{className:"relative flex items-center",children:[(0,a.jsx)("input",{type:"radio",name:"option",id:"option-".concat(t),className:"peer h-4 w-4 appearance-none rounded-full border border-[#C5CCD3] bg-white checked:border-[#6868F7] cursor-pointer",defaultChecked:0===t}),(0,a.jsx)("div",{className:"pointer-events-none absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 opacity-0 peer-checked:opacity-100 ",children:(0,a.jsx)("div",{className:"h-1.5 w-1.5 bg-[#6868F7] rounded-full"})})]})]})},t))})]});if("optionSubmitBlock"===s.type)return(0,a.jsxs)("div",{className:"flex flex-col gap-2 w-full mb-2",children:[(0,a.jsx)("p",{className:"text-[#344054] text-[15px]",children:s.text}),(0,a.jsxs)("div",{className:"flex justify-between flex-1 items-center gap-4 p-2 rounded-[5px] bg-[#F1F1FE]",children:[(0,a.jsx)("div",{className:"w-fit flex gap-4",children:s.options.map((e,t)=>(0,a.jsx)("div",{className:"flex items-center gap-3 p-[10px] rounded-[9px] border border-[#E6EAEF] bg-white",children:(0,a.jsxs)("label",{htmlFor:"optionSubmit-".concat(t),className:"flex items-center gap-3 cursor-pointer",children:[(0,a.jsx)("span",{className:"text-[#344054] text-sm",children:e}),(0,a.jsxs)("div",{className:"relative flex items-center",children:[(0,a.jsx)("input",{type:"radio",name:"optionSubmit",id:"optionSubmit-".concat(t),className:"peer h-4 w-4 appearance-none rounded-full border border-[#C5CCD3] bg-white checked:border-[#6868F7] cursor-pointer",defaultChecked:0===t}),(0,a.jsx)("div",{className:"pointer-events-none absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 opacity-0 peer-checked:opacity-100 ",children:(0,a.jsx)("div",{className:"h-1.5 w-1.5 bg-[#6868F7] rounded-full"})})]})]})},t))}),(0,a.jsx)(m.z,{className:"bg-[#7141F8] text-white h-8 w-full max-w-[75px]",children:s.submitText})]})]});if("alertBlock"===s.type)return(0,a.jsxs)("div",{className:"flex flex-col gap-2 w-full mb-2",children:[(0,a.jsx)("p",{className:"text-[#344054] text-[15px]",children:s.text}),(0,a.jsx)("div",{className:"flex flex-col w-full gap-2 p-2 rounded-[5px] bg-[#F1F1FE]",children:s.options.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("span",{className:"text-[#667085] text-[15px]",children:[e.icon," ",e.name,":"]}),(0,a.jsx)("span",{className:"text-[#344054] text-[15px]",children:e.description})]},t))})]});else if("postBlock"===s.type)return(0,a.jsx)("div",{className:"flex flex-col gap-2 w-full mb-2",children:(0,a.jsx)("div",{className:"flex flex-col w-full space-y-4 p-2 rounded-[5px] bg-[#F1F1FE]",children:s.posts.map((e,t)=>(0,a.jsxs)(l.Fragment,{children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("div",{className:"relative flex flex-col items-center",children:[(0,a.jsx)("div",{className:"w-[6px] h-[6px] rounded-[2px] ".concat("red"===e.alert?"bg-[#FB6B61]":"black"===e.alert?"bg-[#344054]":"bg-[#5EDF9A]")}),(0,a.jsx)("div",{className:"w-[1px] h-full ".concat("red"===e.alert?"bg-[#FC9A93]":"black"===e.alert?"bg-[#98A2B3]":"bg-[#91E9BA]")}),(0,a.jsx)("div",{className:"w-[6px] h-[6px] rounded-[2px] ".concat("red"===e.alert?"bg-[#FB6B61]":"black"===e.alert?"bg-[#344054]":"bg-[#5EDF9A]")})]}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col gap-2",children:[0===t&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(i.default,{src:s.icon,alt:s.name,width:24,height:24,className:"rounded-[3px]"}),(0,a.jsxs)("span",{className:"text-[#344054] text-[15px]",children:[s.platform," Update"]})]}),(0,a.jsx)("h4",{className:"text-[#344054] text-[15px]",children:e.review}),(0,a.jsxs)("div",{className:"flex items-center gap-2 py-1 px-[6px] rounded-[3px] bg-white",children:["media"===e.type&&(0,a.jsx)(i.default,{src:e.image,alt:e.title,width:72,height:72,className:"rounded-[3px] border border-black/5"}),(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:["media"===e.type&&(0,a.jsxs)("h5",{className:"text-[#344054] text-[15px]",children:['"',null==e?void 0:e.title,'"']}),"media"===e.type&&((null==e?void 0:e.likes)||(null==e?void 0:e.comments)||(null==e?void 0:e.shares))&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsxs)("span",{className:"text-[#667085] text-[15px]",children:[null==e?void 0:e.likes," Likes"]}),(0,a.jsx)("span",{className:"text-[#667085] text-[15px]",children:"|"}),(0,a.jsxs)("span",{className:"text-[#667085] text-[15px]",children:[null==e?void 0:e.comments," Comments"]}),(0,a.jsx)("span",{className:"text-[#667085] text-[15px]",children:"|"}),(0,a.jsxs)("span",{className:"text-[#667085] text-[15px]",children:[null==e?void 0:e.shares," Shares"]})]}),"comment"===e.type&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[(0,a.jsxs)("span",{className:"text-[#667085] text-[15px]",children:["@",null==e?void 0:e.sender,":"]}),(0,a.jsxs)("span",{className:"text-[#344054] text-[15px]",children:['"',null==e?void 0:e.comment,'"']})]}),(0,a.jsx)(m.z,{className:"p-0 h-fit text-[#7141F8] text-[13px] w-fit",children:"media"===e.type?"View Post":"View Comment"})]})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[(0,a.jsx)("span",{className:"text-[#667085] text-[15px]",children:"Suggested Action:"}),(0,a.jsx)("span",{className:"text-[#344054] text-[15px]",children:null==e?void 0:e.suggestion})]})]})]}),t!==s.posts.length-1&&(0,a.jsx)("div",{className:"h-[1px] bg-white w-full"})]},t))})});else if("codeOptionsBlock"===s.type){let e=Z[s.id];return(0,a.jsxs)("div",{className:"flex flex-col gap-2 w-full mb-2",children:[(0,a.jsx)("p",{className:"text-[#344054] text-[15px]",children:s.text}),(0,a.jsx)("div",{className:"flex gap-4 p-2 rounded-[5px] bg-[#F1F1FE]",children:s.options.map((t,l)=>{let i=Z[s.id]===t;return e?(0,a.jsx)("div",{className:"text-[13px] flex items-center ".concat(i?"text-[#7141F8]":"text-[#667085]"),children:t},l):(0,a.jsx)(m.z,{className:"h-8 ".concat(0===l?"border border-[#7141F8] bg-white text-[#7141F8]":"bg-[#7141F8] text-white"),onClick:()=>{I({...Z,[s.id]:t}),O(t)},children:t},l)})})]})}else if("codeBlock"===s.type)return(0,a.jsx)("div",{className:"w-full p-2 rounded-[5px] bg-[#F1F1FE] mb-2",children:(0,a.jsxs)("div",{className:"border border-[#D0D0FD] rounded-[7px] overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center px-4 py-2 h-[42px] bg-[#F6F7F9] ",children:[(0,a.jsx)("span",{className:"text-[#344054] text-[13px] font-semibold",children:"input"===s.state?V:s.language}),(0,a.jsx)(m.z,{className:"p-0 h-fit text-[#667085] ".concat("display"===s.state?"text-opacity-100":"text-opacity-40"," text-[13px]"),onClick:()=>navigator.clipboard.writeText(s.code),disabled:"display"!==s.state,children:"Copy code"})]}),"input"===s.state?(0,a.jsxs)("div",{className:"p-4 bg-white",children:[(0,a.jsx)("pre",{className:"whitespace-pre-wrap mb-2 text-[#344054] text-[13px]",children:s.code}),(0,a.jsx)("textarea",{value:T,onChange:e=>{H(e.target.value),W("php")},className:"w-full h-[150px] outline-none resize-none text-[#344054] text-[13px]"}),(0,a.jsx)("div",{className:"flex justify-end mt-2",children:(0,a.jsx)(m.z,{className:"bg-[#7141F8] text-white h-8 px-6 py-3",disabled:!T,onClick:()=>{let a={...s,code:s.code+"\n"+T,language:V,state:"display"};t(e.map(e=>e.id===s.id?a:e)),H("")},children:"Send"})})]}):(0,a.jsx)("pre",{className:"p-4 bg-white overflow-x-auto text-[#344054] text-[13px]",children:(0,a.jsx)("code",{children:s.code})})]})});else if("codeAlertBlock"===s.type)return(0,a.jsxs)("div",{className:"flex flex-col gap-2 w-full mb-2",children:[(0,a.jsx)("p",{className:"text-[#344054] text-[15px]",children:s.text}),(0,a.jsx)("div",{className:"flex flex-col w-full space-y-4 p-2 rounded-[5px] bg-[#F1F1FE]",children:s.codes.map((e,t)=>(0,a.jsxs)(l.Fragment,{children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("div",{className:"relative flex flex-col items-center flex-shrink-0",children:[(0,a.jsx)("div",{className:"w-[6px] h-[6px] rounded-[2px] ".concat("red"===e.alert?"bg-[#FB6B61]":"black"===e.alert?"bg-[#344054]":"bg-[#5EDF9A]")}),(0,a.jsx)("div",{className:"w-[1px] h-full ".concat("red"===e.alert?"bg-[#FC9A93]":"black"===e.alert?"bg-[#98A2B3]":"bg-[#91E9BA]")}),(0,a.jsx)("div",{className:"w-[6px] h-[6px] rounded-[2px] ".concat("red"===e.alert?"bg-[#FB6B61]":"black"===e.alert?"bg-[#344054]":"bg-[#5EDF9A]")})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-2 w-full overflow-hidden",children:[e.review&&(0,a.jsx)("p",{className:"text-[#344054] text-[15px] break-words",children:e.review}),e.problem&&e.riskLevel&&e.solution&&[{label:"Problem",value:e.problem},{label:"Risk Level",value:e.riskLevel},{label:"Solution",value:e.solution}].map((e,t)=>(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[(0,a.jsxs)("span",{className:"text-[#667085] text-[15px]",children:[e.label,":"]}),(0,a.jsx)("span",{className:"text-[#344054] text-[15px] break-words",children:e.value})]},t)),(0,a.jsxs)("div",{className:"border border-[#D0D0FD] rounded-[7px] overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center px-4 py-2 h-[42px] bg-[#F6F7F9]",children:[(0,a.jsx)("span",{className:"text-[#344054] text-[13px] font-semibold",children:e.language}),(0,a.jsx)(m.z,{className:"p-0 h-fit text-[#667085] text-[13px]",onClick:()=>navigator.clipboard.writeText(e.code),children:"Copy code"})]}),(0,a.jsx)("pre",{className:"p-4 bg-white overflow-x-auto text-[#344054] text-[13px] max-w-full",children:(0,a.jsx)("code",{className:"break-words whitespace-pre-wrap",children:e.errorLines?(0,a.jsx)(a.Fragment,{children:e.code.split("\n").map((t,s)=>{var l;return(0,a.jsx)("div",{className:(null===(l=e.errorLines)||void 0===l?void 0:l.includes(s+1))?"bg-[#FEE8E6] text-[#F81404] px-1 rounded":"",children:t},s)})}):e.code})})]})]})]}),t!==s.codes.length-1&&(0,a.jsx)("div",{className:"h-[1px] bg-white w-full"})]},t))})]});else if("summaryReportBlock"===s.type)return(0,a.jsxs)("div",{className:"flex flex-col gap-2 w-full mb-2",children:[(0,a.jsx)("p",{className:"text-[#344054] text-[15px]",children:s.text}),(0,a.jsxs)("div",{className:"w-full flex gap-2 p-2 rounded-[5px] bg-[#F1F1FE] mb-2",children:[(0,a.jsxs)("div",{className:"relative flex flex-col items-center flex-shrink-0",children:[(0,a.jsx)("div",{className:"w-[6px] h-[6px] rounded-[2px] ".concat("red"===s.alert?"bg-[#FB6B61]":"black"===s.alert?"bg-[#344054]":"bg-[#5EDF9A]")}),(0,a.jsx)("div",{className:"w-[1px] h-full ".concat("red"===s.alert?"bg-[#FC9A93]":"black"===s.alert?"bg-[#98A2B3]":"bg-[#91E9BA]")}),(0,a.jsx)("div",{className:"w-[6px] h-[6px] rounded-[2px] ".concat("red"===s.alert?"bg-[#FB6B61]":"black"===s.alert?"bg-[#344054]":"bg-[#5EDF9A]")})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-2 w-full",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(i.default,{src:"https://avatars.githubusercontent.com/u/10347539?v=4",alt:"org",width:24,height:24,className:"rounded-[3px]"}),(0,a.jsx)("span",{className:"text-[#344054] text-[15px]",children:"Timbu Tech Ltd"})]}),(0,a.jsxs)("div",{className:"w-full border border-[#D0D0FD] rounded-[7px] overflow-hidden",children:[(0,a.jsx)("div",{className:"px-4 py-2 h-[42px] bg-[#F6F7F9] border-b border-[#DDE6EB]",children:(0,a.jsx)("span",{className:"text-[#344054] text-[13px] font-semibold",children:s.title})}),(0,a.jsxs)("div",{className:"py-4 bg-white flex flex-col overflow-x-auto text-[#344054] text-[13px]",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center gap-5 flex-wrap border-b border-[#E6EAEF] w-full",children:[(0,a.jsx)("div",{className:"h-[250px] w-[200px] flex justify-center items-center",children:(0,a.jsx)(h.h,{width:"100%",height:"100%",children:(0,a.jsx)(u.u,{children:(0,a.jsxs)(g.b,{data:s.chartData,cx:"50%",cy:"50%",innerRadius:60,outerRadius:80,paddingAngle:2,dataKey:"value",children:[s.chartData.map((e,t)=>(0,a.jsx)(b.b,{fill:e.color},"cell-".concat(t))),(0,a.jsx)(f._,{content:e=>{let{viewBox:t}=e,{cx:s,cy:l}=t;return(0,a.jsx)("text",{x:s,y:l,textAnchor:"middle",dominantBaseline:"middle",className:"text-xs fill-[#344054]",children:"Full Scan"})},position:"center"})]})})})}),(0,a.jsx)("div",{className:"flex flex-col gap-3 justify-center",children:s.chartData.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full",style:{backgroundColor:e.color}}),(0,a.jsxs)("span",{className:"text-[#667085] text-xs",children:[e.name," ",(0,a.jsx)("span",{className:"text-black font-semibold ml-1",children:e.value})]})]},t))})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-[10px]",children:[(0,a.jsx)("span",{className:"text-[#475467] text-[13px] font-medium",children:s.tableOverview.title}),(0,a.jsx)("span",{className:"w-2 h-2 rounded-full bg-[#E4E7EC]"}),(0,a.jsxs)("span",{className:"text-[#475467] text-[13px] font-medium",children:[s.tableOverview.linkCount," links"]}),(0,a.jsx)("span",{className:"w-2 h-2 rounded-full bg-[#E4E7EC]"}),(0,a.jsxs)("span",{className:"text-[#475467] text-[13px] font-medium",children:[(0,a.jsx)("span",{className:"text-[#FD7072]",children:s.tableOverview.brokenLinkCount})," ","broken"]})]}),(0,a.jsx)("div",{className:"mt-4 overflow-x-auto scrollbar-none [&::-webkit-scrollbar]:hidden rounded-[7px] border border-[#E4E7EC]",children:(0,a.jsxs)("table",{className:"w-full border-collapse",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"bg-[#F6F7F9] h-[32px]",children:[(0,a.jsx)("th",{className:"text-left text-[10px] font-medium text-[#667085] p-[10px]",children:"Link"}),(0,a.jsx)("th",{className:"text-left text-[10px] font-medium text-[#667085] p-[10px]",children:"Status"}),(0,a.jsx)("th",{className:"text-left text-[10px] font-medium text-[#667085] p-[10px]",children:"Issue Type"}),(0,a.jsx)("th",{className:"text-left text-[10px] font-medium text-[#667085] p-[10px]",children:"Suggested Fix"})]})}),(0,a.jsx)("tbody",{children:s.tableData.map((e,t)=>(0,a.jsxs)("tr",{className:"".concat(t!==s.tableData.length-1?"border-b border-[#F6F7F9]":""),children:[(0,a.jsx)("td",{className:"text-xs text-[#5F5FE1] p-[10px] h-[56px]",children:(0,a.jsx)("a",{href:e.link,target:"_blank",rel:"noreferrer",children:e.link})}),(0,a.jsx)("td",{className:"text-xs p-[10px] h-[56px]",children:(0,a.jsx)("span",{className:"px-4 py-1 rounded-[20px] text-xs border capitalize ".concat("broken"===e.status?"bg-[#FEE8E6] text-[#700902] border-[#FC9A93]":"bg-[#E6FAEF] text-[#005C2B] border-[#91E9BA]"),children:e.status})}),(0,a.jsx)("td",{className:"text-xs text-[#475467] p-[10px] h-[56px]",children:e.issueType}),(0,a.jsx)("td",{className:"text-xs text-[#475467] p-[10px] h-[56px]",children:e.suggestedFix})]},t))})]})})]})]})]})]})]})]})};return(0,a.jsx)("div",{className:"flex h-[calc(100vh-70px)] relative w-full overflow-hidden",children:(0,a.jsx)("div",{className:"flex flex-col flex-1 transition-[margin] duration-300 ease-in-out",children:(0,a.jsx)("div",{className:"flex-1 flex flex-col justify-end relative overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-y-auto pb-5 mb-[180px] [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]",children:(0,a.jsxs)("div",{className:"flex flex-col",children:[e.map((t,s)=>{let r=s>0?e[s-1]:null,n=!r||r.sender!==t.sender,p=!r||P(t.timestamp)!==P(r.timestamp);return(0,a.jsxs)(l.Fragment,{children:[p&&(0,a.jsxs)("div",{className:"relative my-4",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-dotted border-[#E6EAEF]"})}),(0,a.jsx)("div",{className:"relative flex justify-center",children:(0,a.jsx)("span",{className:"bg-white px-4 py-1 text-[13px] text-[#101828] border border-[#E6EAEF] rounded-[30px]",children:P(t.timestamp)})})]}),(0,a.jsxs)("div",{className:"bg-white group hover:bg-gray-50 transition-colors flex items-start px-5 ".concat(n&&"mt-5"),children:[(0,a.jsx)("div",{className:"w-8 flex-shrink-0 mr-2 flex items-center justify-center",children:n?(0,a.jsx)(i.default,{src:t.avatar,alt:"avatar",width:40,height:40,className:"rounded-[7px]",style:{backgroundColor:null==t?void 0:t.color,border:"1px solid #E6EAEF"}}):(0,a.jsx)("span",{className:"text-xs text-[#98A2B3] opacity-0 group-hover:opacity-100 transition-opacity mt-1",children:new Date(t.timestamp).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit",hour12:!1})})}),(0,a.jsxs)("div",{className:"flex-grow",children:[n&&(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("span",{className:"font-bold text-[15px] text-[#1D2939]",children:t.sender}),(0,a.jsx)("span",{className:"text-xs text-[#98A2B3]",children:new Date(t.timestamp).toLocaleTimeString([],{hour:"numeric",minute:"2-digit",hour12:!0})})]}),(0,a.jsxs)("div",{className:"flex items-start justify-between relative",children:[en(t),(0,a.jsxs)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity flex items-center ml-4 absolute right-0 bottom-full bg-white shadow-md rounded-[8px] border border-[#E6EAEF] p-[2px]",children:[(0,a.jsx)("button",{className:"pb-[4px] px-[10px] hover:bg-gray-200 rounded",children:(0,a.jsx)("span",{className:"inline-flex items-center justify-center w-4 h-4 text-base",children:"✅"})}),(0,a.jsx)("button",{className:"pb-[4px] px-[10px] hover:bg-gray-200 rounded",children:(0,a.jsx)("span",{className:"inline-flex items-center justify-center w-4 h-4 text-base",children:"\uD83D\uDC40"})}),(0,a.jsx)("button",{className:"pb-[4px] px-[10px] hover:bg-gray-200 rounded",children:(0,a.jsx)("span",{className:"inline-flex items-center justify-center w-4 h-4 text-base",children:"\uD83D\uDE4C"})}),(0,a.jsx)("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,a.jsx)(c.Z,{size:16,className:"text-[#667085]"})}),(0,a.jsx)("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,a.jsx)(o.Z,{size:16,className:"text-[#667085]"})}),(0,a.jsx)("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,a.jsx)(d.Z,{size:16,className:"text-[#667085]"})}),(0,a.jsx)("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,a.jsx)(x.Z,{size:16,className:"text-[#667085]"})}),(0,a.jsx)("button",{className:"py-[7px] px-[10px] hover:bg-gray-200 rounded",children:(0,a.jsx)(A.Z,{size:16,className:"text-[#667085]"})})]})]})]})]})]},t.id)}),(0,a.jsx)("div",{ref:s})]})})})})})}},56792:function(e,t,s){"use strict";s.d(t,{Z:function(){return a}});var a={bot:{src:"/_next/static/media/bot.dfdf2c7a.png",height:80,width:80,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAHlBMVEVMaXEAYC4AlUQAm0YAjUEAfDgrJCgKXTEAj0EAkEKCxuBiAAAACnRSTlMAB73oOFyq4Hx6x9l+tAAAAAlwSFlzAAAsSwAALEsBpT2WqQAAAChJREFUeJxjYEACjIxQBhsbhGZlZ2cF0SxMzMxMLCAVHExMnGiKwQAACx0AUUBiaOwAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},RedBot:{src:"/_next/static/media/bot-red.3a55df2b.png",height:40,width:40,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAIVBMVEVMaXFQAAB3BwJtCARtCQReBQB4CgJvCgRLEAskLS4hKCqjVWtSAAAAC3RSTlMABO01ulzJe9m0qZQuNjoAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAoSURBVHicY2BAAoyMUJqTC8Jg5eBgBdHMbExMbMwgGXYWFnZUxRAAAA2RAGBI3TANAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},blueBot:{src:"/_next/static/media/blue-bot.f296e2d3.png",height:40,width:40,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAKlBMVEXv7/rr7vjt7vj19v7x8f6Hh/ivr/q6u+9gYapzc//Y1/3T0v1mZ2JdX1kQXXeiAAAAA3RSTlP+nJxhYYB9AAAACXBIWXMAAAsTAAALEwEAmpwYAAAANklEQVR4nBXKyQ3AMBDEMNma9ZWk/3YDfwnSJGDHBBJBvtcAzrXmhaf2riNkVI37TaLS5cb2AyAvAORP3/L0AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},google:{src:"/_next/static/media/google.3c81f1fb.svg",height:16,width:16,blurWidth:0,blurHeight:0},send:{src:"/_next/static/media/send.7163c084.svg",height:18,width:17,blurWidth:0,blurHeight:0},disco:{src:"/_next/static/media/disco.672244de.png",height:192,width:192,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAaVBMVEVMaXHi2M2nopzZsWDKqnHy01GUkIC0m2ODeG8Zl/84f+Lmlwbyuy7Oplrew47k0KassLjamCLdr13j5eXVx6WwqJ7cniSXin7CwLzcw5mPfmV/dWh/fHevtq++qnPu7/TCu7bXybm2t7Oodw53AAAAH3RSTlMA/IlTgiVGFf0LJCo85G/2o3DNqIb6jPf4+/h6mErb4mYc6AAAAAlwSFlzAAAhOAAAITgBRZYxYAAAAEBJREFUeJwlwccBgCAAALGjgw17V9D9h/RhAhSBn7MYB2XVxKHvQNciJaEhtHlaXwmMz+L9Ccxb9vt9gVPysMp8UogCzxGkjJoAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},teammates:{src:"/_next/static/media/teammates.bbd3adac.svg",height:32,width:32,blurWidth:0,blurHeight:0},facebook:{src:"/_next/static/media/facebook.fca64f6c.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAALVBMVEVWmvD//fpHkvBNlfAMcfMee/HX6Py10/kpgvOEt/f///+ex/jG3foAZPJgovbihSuXAAAABHRSTlP+4f7+rTCusQAAAAlwSFlzAAAsSwAALEsBpT2WqQAAADhJREFUeJwFwYkRwDAMAjDsA/wk6f7jVkIYYjrgpuaTAXJ2CEisogTx7B4KYN/bBNyv6rURzqp0/DHzAXV/loHlAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},twitter:{src:"/_next/static/media/twitter.913bc175.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAGFBMVEUAAAABAQEeHh4PDw9qampFRUVYWFhSUlIoHbPFAAAAAXRSTlPhl+sKiAAAAAlwSFlzAAAsSwAALEsBpT2WqQAAAC9JREFUeJw1i0EOACAMwihD/f+PzWbk1KRFYiZRqzAIsr0GnLSDSp463mkoT69/vxFyAH2eHpTfAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},instagram:{src:"/_next/static/media/instagram.07bbefde.png",height:96,width:96,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAPFBMVEX6+vr++v76tezYrP3+pcn9tL77pen6+vn8//////7+r8/+wK7+4an/rNf+07Hpqvz/7aj6ed3+3Lb+rKgwumMZAAAACHRSTlPg////////4NsSfKYAAAAJcEhZcwAAITgAACE4AUWWMWAAAAA+SURBVHicFcs5EsAgDANAYWOQuJP8/68Zum0WkCQKUHLflgRtt/IYwTJnyZngFz3axRrvikZI54yebiNJ1R9EDgII9TTKpAAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},youtube:{src:"/_next/static/media/youtube.b9208f26.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAGFBMVEX5+fn9/////Pz8WVn8Hh78JSX+AAD8n57mNweJAAAAAXRSTlPhl+sKiAAAAAlwSFlzAAAsSwAALEsBpT2WqQAAACxJREFUeJxNi7ENADAMg4ydNP9/3ClVNyRAwmAksG0Q01U9iJzk5IenNt79AhbIAKGvX29oAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},tiktok:{src:"/_next/static/media/tiktok.029728a5.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAPFBMVEUBAQFMHietfojQv8Y1Exqzs7cBDQwAAAAAAAAZCQxVgYLI9PSxxcp1fIFNjYtHREYdOTfPr7sdVlNtoqCdn0cDAAAACHRSTlPh////////4WC/TK4AAAAJcEhZcwAALEsAACxLAaU9lqkAAAA4SURBVHicY2DgAAMGEM3GxcnBAWZws3KCGHy8rEwsHAwcQsyMPCwgKQFmYUGIGn4mRpA2doh2dgBFkAH9NNczawAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},channel:{src:"/_next/static/media/channel.71b0d2d9.png",height:1827,width:1986,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAMAAAACh/xsAAAAFVBMVEX6+P77+v328//7+/3w6/3////u5v959aEiAAAABXRSTlP94vju40yQVCYAAAAJcEhZcwAAITgAACE4AUWWMWAAAAAnSURBVHicY2BkAgMWBhY2MGBhYGYAAVZGBmZWMEBhMLCygqQYoWoAFpgAqnCsrYsAAAAASUVORK5CYII=",blurWidth:8,blurHeight:7},user:{src:"/_next/static/media/user.cc6e44b6.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAG1BMVEXz/vwD0sHq/PpK4NPU+PVi5NkY18jB9PCF6uJqQ8ocAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAM0lEQVR4nB3JwQ3AMAzEMMlnO9l/4iL9ESBVAFUUPdM8jM6PaH508oq+M7fhrOoe4uqaDxOVAKOLLWTAAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},createChannel:{src:"/_next/static/media/create-channel.d750c07c.png",height:2332,width:2544,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAMAAAACh/xsAAAAD1BMVEX+/v7t6P308vz6+vrm5OpbWQr2AAAACXBIWXMAACxLAAAsSwGlPZapAAAAJ0lEQVR4nEXHwQ0AMAzCQDDZf+aKKFKPD5bNko+mDApDEn2tbk/7AQyWAF9SvWHtAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:7},megaphone:{src:"/_next/static/media/megaphone.d567f808.png",height:24,width:24,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAJ1BMVEVMaXEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACv15x2AAAADXRSTlMAaRhcwINMCyY5ndOqyc6ZIAAAAAlwSFlzAAALEwAACxMBAJqcGAAAADRJREFUeJxVyrkNACAMwEDnI5Cw/7wIiYarXBg+OhRySZUba4dNRDAfmjeIbg83yBDmnZ8DGa0AvpK+HLsAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},visaLogo:{src:"/_next/static/media/visa-logo.e39f2d7e.svg",height:12,width:33,blurWidth:0,blurHeight:0},fileDoc:{src:"/_next/static/media/FileDoc.69079eba.svg",height:20,width:20,blurWidth:0,blurHeight:0},border:{src:"/_next/static/media/Border.8c3c175f.svg",height:1024,width:44,blurWidth:0,blurHeight:0}}},11492:function(e,t,s){"use strict";s.d(t,{d:function(){return c},z:function(){return o}});var a=s(75376),l=s(32486),i=s(91007),r=s(53447),n=s(58983);let c=(0,r.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=l.forwardRef((e,t)=>{let{className:s,variant:l,size:r,asChild:o=!1,...d}=e,x=o?i.g7:"button";return(0,a.jsx)(x,{className:(0,n.cn)(c({variant:l,size:r,className:s})),ref:t,...d})});o.displayName="Button"},58983:function(e,t,s){"use strict";s.d(t,{cn:function(){return i},k:function(){return r}});var a=s(89824),l=s(97215);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,l.m6)((0,a.W)(t))}let r=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"}},function(e){e.O(0,[8863,7140,4208,7542,2344,1744],function(){return e(e.s=42725)}),_N_E=e.O()}]);