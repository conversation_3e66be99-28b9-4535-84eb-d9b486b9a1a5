(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7060],{33658:function(e,t,A){Promise.resolve().then(A.bind(A,20336))},9824:function(e,t,A){"use strict";A.d(t,{Z:function(){return o}});var a=A(32486);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=function(){for(var e=arguments.length,t=Array(e),A=0;A<e;A++)t[A]=arguments[A];return t.filter((e,t,A)=>!!e&&A.indexOf(e)===t).join(" ")};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,a.forwardRef)((e,t)=>{let{color:A="currentColor",size:r=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:s="",children:d,iconNode:c,...u}=e;return(0,a.createElement)("svg",{ref:t,...n,width:r,height:r,stroke:A,strokeWidth:o?24*Number(i)/Number(r):i,className:l("lucide",s),...u},[...c.map(e=>{let[t,A]=e;return(0,a.createElement)(t,A)}),...Array.isArray(d)?d:[d]])}),o=(e,t)=>{let A=(0,a.forwardRef)((A,n)=>{let{className:o,...s}=A;return(0,a.createElement)(i,{ref:n,iconNode:t,className:l("lucide-".concat(r(e)),o),...s})});return A.displayName="".concat(e),A}},23972:function(e,t,A){"use strict";A.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,A(9824).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},33512:function(e,t,A){"use strict";A.d(t,{Z:function(){return a}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,A(9824).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},39713:function(e,t,A){"use strict";A.d(t,{default:function(){return r.a}});var a=A(74033),r=A.n(a)},47411:function(e,t,A){"use strict";var a=A(13362);A.o(a,"useParams")&&A.d(t,{useParams:function(){return a.useParams}}),A.o(a,"usePathname")&&A.d(t,{usePathname:function(){return a.usePathname}}),A.o(a,"useRouter")&&A.d(t,{useRouter:function(){return a.useRouter}}),A.o(a,"useSearchParams")&&A.d(t,{useSearchParams:function(){return a.useSearchParams}})},74033:function(e,t,A){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var A in t)Object.defineProperty(e,A,{enumerable:!0,get:t[A]})}(t,{default:function(){return o},getImageProps:function(){return i}});let a=A(60723),r=A(25738),l=A(28863),n=a._(A(44543));function i(e){let{props:t}=(0,r.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,A]of Object.entries(t))void 0===A&&delete t[e];return{props:t}}let o=l.Image},20336:function(e,t,A){"use strict";A.r(t);var a=A(75376),r=A(32486),l=A(23972),n=A(33512),i=A(39713),o=A(97220),s=A(56792),d=A(72738),c=A.n(d),u=A(47411),h=A(86418);t.default=()=>{var e,t;let A=(0,r.useRef)(null),[d,g]=(0,r.useState)(!1),[m,v]=(0,r.useState)(!1),{state:f}=(0,r.useContext)(o.R),{user:p,orgData:w}=f,b=(0,u.useRouter)(),[x,C]=(0,r.useState)(null);(0,r.useEffect)(()=>{let e=localStorage.getItem("orgId")||"";(async()=>{let t=await (0,h.Gl)("/organisations/".concat(e,"/get-started"));if((null==t?void 0:t.status)===200||(null==t?void 0:t.status)===201){var A;C(null==t?void 0:null===(A=t.data)||void 0===A?void 0:A.data)}})()},[]);let E=()=>{let e=A.current;e&&(g(e.scrollLeft>0),v(e.scrollLeft+e.clientWidth<e.scrollWidth))},B=e=>{let t=A.current;t&&t.scrollBy({left:"left"===e?-250:250,behavior:"smooth"})};return(0,r.useEffect)(()=>{let e=A.current;if(!e)return;E();let t=()=>E();return e.addEventListener("scroll",t),window.addEventListener("resize",E),()=>{e.removeEventListener("scroll",t),window.removeEventListener("resize",E)}},[]),(0,a.jsxs)("div",{className:"bg-[#f6f7f9] h-screen overflow-y-auto px-10 py-10 relative w-full scrollbar-hide scroll-smooth m-auto",children:[(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h2",{className:"font-semibold text-sm mb-3",children:"Get started"}),(0,a.jsxs)("p",{className:"text-lg font-extrabold",children:[(0,a.jsx)("span",{className:"text-3xl",children:"\uD83D\uDC4B"})," Welcome,"," ",(0,a.jsx)("span",{className:"text-purple-800",children:null==p?void 0:p.username}),"! Ready to dive in?"]})]}),(0,a.jsxs)("div",{className:"",children:[(0,a.jsx)("h3",{className:"font-bold text-sm",children:"Say hello to someone"}),(0,a.jsxs)("p",{className:"font-semibold text-sm text-gray-500 mb-2",children:["Here are a few of your ",null==w?void 0:w.name," teammates. Send someone a message and introduce yourself."]}),(0,a.jsxs)("div",{className:"relative py-2 max-w-4xl overflow-hidden",children:[d&&(0,a.jsx)("button",{onClick:()=>B("left"),className:"absolute left-2 top-1/2 -translate-y-1/2 bg-white shadow p-2 rounded-full z-10 hover:bg-gray-100",children:(0,a.jsx)(l.Z,{})}),(0,a.jsx)("div",{ref:A,className:"flex gap-3 overflow-x-scroll scrollbar-hide scroll-smooth",children:null==x?void 0:null===(e=x.org_users_profile)||void 0===e?void 0:e.map((e,t)=>(0,a.jsxs)("div",{className:"flex-none w-40 h-48 bg-white border rounded-md shadow cursor-pointer",children:[(0,a.jsx)(i.default,{src:e.avatar_url||(null===s.Z||void 0===s.Z?void 0:s.Z.user),alt:e.name,className:"w-full h-32 object-cover rounded-t-md mx-auto",height:100,width:100,unoptimized:!0}),(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("p",{className:"text-sm font-semibold",children:e.name.slice(0,15)}),(0,a.jsx)("div",{className:"border rounded-full h-2.5 w-2.5 border-gray-600 ".concat(e.is_online&&"bg-green-600")})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.is_online?"Active":"Away"})]})]},t))}),m&&(0,a.jsx)("button",{onClick:()=>B("right"),className:"absolute right-2 top-1/2 -translate-y-1/2 bg-white shadow p-2 rounded-full z-10 hover:bg-gray-100",children:(0,a.jsx)(n.Z,{})})]})]}),(0,a.jsxs)("div",{className:"mt-10 mx-w-4xl",children:[(0,a.jsxs)("div",{className:"",children:[(0,a.jsx)("h3",{className:"font-bold text-sm",children:"Explore channels to join"}),(0,a.jsx)("p",{className:"font-semibold text-sm text-gray-500 mb-2",children:"Channels are organized spaces for conversations. Here are few suggestions."})]}),(0,a.jsx)("div",{className:"border rounded-md bg-white",children:null==x?void 0:null===(t=x.org_channels)||void 0===t?void 0:t.map((e,t)=>{var A,r;return(0,a.jsxs)("div",{className:"p-4 cursor-pointer flex items-center justify-between border-b",onClick:()=>b.push("/client/home/<USER>/".concat(null==e?void 0:e.channels_id)),children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("p",{className:"text-base font-semibold text-blue-500",children:["#",e.name]}),""!==e.recentPosts&&(0,a.jsxs)("span",{className:"text-[10px] font-semibold p-0.5 bg-[#ade3ed] text-blue-900",children:[e.recentPosts," RECENT POSTS"]})]}),(0,a.jsx)("div",{className:"flex items-center gap-2 text-gray-500 font-semibold text-sm",children:(null==e?void 0:e.last_post_time)==="No posts yet"?(0,a.jsx)("p",{className:"",children:null==e?void 0:e.last_post_time}):(0,a.jsxs)("p",{className:"",children:["Last post"," ",c()(null==e?void 0:e.last_post_time).startOf("minute").fromNow()]})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("p",{className:"text-gray-500 font-semibold text-sm",children:e.numberMembers}),(0,a.jsx)("div",{className:"flex items-center",children:null==e?void 0:null===(r=e.member_avatars)||void 0===r?void 0:null===(A=r.slice(0,5))||void 0===A?void 0:A.map((e,t)=>(0,a.jsx)("div",{className:"w-9 h-9 rounded-xl overflow-hidden border-2 border-white ".concat(0!==t?"-ml-3":""),children:(0,a.jsx)(i.default,{width:35,height:35,src:e||(null===s.Z||void 0===s.Z?void 0:s.Z.user),alt:e,className:"object-cover w-full h-full"})},t))})]})]},t)})})]})]})}},56792:function(e,t,A){"use strict";A.d(t,{Z:function(){return a}});var a={bot:{src:"/_next/static/media/bot.dfdf2c7a.png",height:80,width:80,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAHlBMVEVMaXEAYC4AlUQAm0YAjUEAfDgrJCgKXTEAj0EAkEKCxuBiAAAACnRSTlMAB73oOFyq4Hx6x9l+tAAAAAlwSFlzAAAsSwAALEsBpT2WqQAAAChJREFUeJxjYEACjIxQBhsbhGZlZ2cF0SxMzMxMLCAVHExMnGiKwQAACx0AUUBiaOwAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},RedBot:{src:"/_next/static/media/bot-red.3a55df2b.png",height:40,width:40,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAIVBMVEVMaXFQAAB3BwJtCARtCQReBQB4CgJvCgRLEAskLS4hKCqjVWtSAAAAC3RSTlMABO01ulzJe9m0qZQuNjoAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAoSURBVHicY2BAAoyMUJqTC8Jg5eBgBdHMbExMbMwgGXYWFnZUxRAAAA2RAGBI3TANAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},blueBot:{src:"/_next/static/media/blue-bot.f296e2d3.png",height:40,width:40,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAKlBMVEXv7/rr7vjt7vj19v7x8f6Hh/ivr/q6u+9gYapzc//Y1/3T0v1mZ2JdX1kQXXeiAAAAA3RSTlP+nJxhYYB9AAAACXBIWXMAAAsTAAALEwEAmpwYAAAANklEQVR4nBXKyQ3AMBDEMNma9ZWk/3YDfwnSJGDHBBJBvtcAzrXmhaf2riNkVI37TaLS5cb2AyAvAORP3/L0AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},google:{src:"/_next/static/media/google.3c81f1fb.svg",height:16,width:16,blurWidth:0,blurHeight:0},send:{src:"/_next/static/media/send.7163c084.svg",height:18,width:17,blurWidth:0,blurHeight:0},disco:{src:"/_next/static/media/disco.672244de.png",height:192,width:192,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAaVBMVEVMaXHi2M2nopzZsWDKqnHy01GUkIC0m2ODeG8Zl/84f+Lmlwbyuy7Oplrew47k0KassLjamCLdr13j5eXVx6WwqJ7cniSXin7CwLzcw5mPfmV/dWh/fHevtq++qnPu7/TCu7bXybm2t7Oodw53AAAAH3RSTlMA/IlTgiVGFf0LJCo85G/2o3DNqIb6jPf4+/h6mErb4mYc6AAAAAlwSFlzAAAhOAAAITgBRZYxYAAAAEBJREFUeJwlwccBgCAAALGjgw17V9D9h/RhAhSBn7MYB2XVxKHvQNciJaEhtHlaXwmMz+L9Ccxb9vt9gVPysMp8UogCzxGkjJoAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},teammates:{src:"/_next/static/media/teammates.bbd3adac.svg",height:32,width:32,blurWidth:0,blurHeight:0},facebook:{src:"/_next/static/media/facebook.fca64f6c.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAALVBMVEVWmvD//fpHkvBNlfAMcfMee/HX6Py10/kpgvOEt/f///+ex/jG3foAZPJgovbihSuXAAAABHRSTlP+4f7+rTCusQAAAAlwSFlzAAAsSwAALEsBpT2WqQAAADhJREFUeJwFwYkRwDAMAjDsA/wk6f7jVkIYYjrgpuaTAXJ2CEisogTx7B4KYN/bBNyv6rURzqp0/DHzAXV/loHlAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},twitter:{src:"/_next/static/media/twitter.913bc175.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAGFBMVEUAAAABAQEeHh4PDw9qampFRUVYWFhSUlIoHbPFAAAAAXRSTlPhl+sKiAAAAAlwSFlzAAAsSwAALEsBpT2WqQAAAC9JREFUeJw1i0EOACAMwihD/f+PzWbk1KRFYiZRqzAIsr0GnLSDSp463mkoT69/vxFyAH2eHpTfAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},instagram:{src:"/_next/static/media/instagram.07bbefde.png",height:96,width:96,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAPFBMVEX6+vr++v76tezYrP3+pcn9tL77pen6+vn8//////7+r8/+wK7+4an/rNf+07Hpqvz/7aj6ed3+3Lb+rKgwumMZAAAACHRSTlPg////////4NsSfKYAAAAJcEhZcwAAITgAACE4AUWWMWAAAAA+SURBVHicFcs5EsAgDANAYWOQuJP8/68Zum0WkCQKUHLflgRtt/IYwTJnyZngFz3axRrvikZI54yebiNJ1R9EDgII9TTKpAAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},youtube:{src:"/_next/static/media/youtube.b9208f26.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAGFBMVEX5+fn9/////Pz8WVn8Hh78JSX+AAD8n57mNweJAAAAAXRSTlPhl+sKiAAAAAlwSFlzAAAsSwAALEsBpT2WqQAAACxJREFUeJxNi7ENADAMg4ydNP9/3ClVNyRAwmAksG0Q01U9iJzk5IenNt79AhbIAKGvX29oAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},tiktok:{src:"/_next/static/media/tiktok.029728a5.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAPFBMVEUBAQFMHietfojQv8Y1Exqzs7cBDQwAAAAAAAAZCQxVgYLI9PSxxcp1fIFNjYtHREYdOTfPr7sdVlNtoqCdn0cDAAAACHRSTlPh////////4WC/TK4AAAAJcEhZcwAALEsAACxLAaU9lqkAAAA4SURBVHicY2DgAAMGEM3GxcnBAWZws3KCGHy8rEwsHAwcQsyMPCwgKQFmYUGIGn4mRpA2doh2dgBFkAH9NNczawAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},channel:{src:"/_next/static/media/channel.71b0d2d9.png",height:1827,width:1986,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAMAAAACh/xsAAAAFVBMVEX6+P77+v328//7+/3w6/3////u5v959aEiAAAABXRSTlP94vju40yQVCYAAAAJcEhZcwAAITgAACE4AUWWMWAAAAAnSURBVHicY2BkAgMWBhY2MGBhYGYAAVZGBmZWMEBhMLCygqQYoWoAFpgAqnCsrYsAAAAASUVORK5CYII=",blurWidth:8,blurHeight:7},user:{src:"/_next/static/media/user.cc6e44b6.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAG1BMVEXz/vwD0sHq/PpK4NPU+PVi5NkY18jB9PCF6uJqQ8ocAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAM0lEQVR4nB3JwQ3AMAzEMMlnO9l/4iL9ESBVAFUUPdM8jM6PaH508oq+M7fhrOoe4uqaDxOVAKOLLWTAAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},createChannel:{src:"/_next/static/media/create-channel.d750c07c.png",height:2332,width:2544,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAMAAAACh/xsAAAAD1BMVEX+/v7t6P308vz6+vrm5OpbWQr2AAAACXBIWXMAACxLAAAsSwGlPZapAAAAJ0lEQVR4nEXHwQ0AMAzCQDDZf+aKKFKPD5bNko+mDApDEn2tbk/7AQyWAF9SvWHtAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:7},megaphone:{src:"/_next/static/media/megaphone.d567f808.png",height:24,width:24,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAJ1BMVEVMaXEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACv15x2AAAADXRSTlMAaRhcwINMCyY5ndOqyc6ZIAAAAAlwSFlzAAALEwAACxMBAJqcGAAAADRJREFUeJxVyrkNACAMwEDnI5Cw/7wIiYarXBg+OhRySZUba4dNRDAfmjeIbg83yBDmnZ8DGa0AvpK+HLsAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},visaLogo:{src:"/_next/static/media/visa-logo.e39f2d7e.svg",height:12,width:33,blurWidth:0,blurHeight:0},fileDoc:{src:"/_next/static/media/FileDoc.69079eba.svg",height:20,width:20,blurWidth:0,blurHeight:0},border:{src:"/_next/static/media/Border.8c3c175f.svg",height:1024,width:44,blurWidth:0,blurHeight:0}}},86418:function(e,t,A){"use strict";A.d(t,{Gl:function(){return n},HH:function(){return o},Q_:function(){return s},_x:function(){return c},an:function(){return u},i1:function(){return g},jx:function(){return h},x9:function(){return d},xo:function(){return i}});var a=A(20818),r=A(13352);let l=A(18648).env.NEXT_PUBLIC_BASE_URL,n=async e=>{let t=localStorage.getItem("token")||"";try{return await a.Z.get(l+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var A,r,n;return((null==e?void 0:null===(A=e.response)||void 0===A?void 0:A.status)===401||(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(r=n.data)||void 0===r?void 0:r.status_code)===401)&&(localStorage.clear(),window.location.href="/auth/login"),e}},i=async(e,t)=>{let A=localStorage.getItem("token")||"";try{return await a.Z.post(l+e,t,{headers:{Authorization:"Bearer ".concat(A),"Content-Type":"application/json"}})}catch(e){var n,i,o,s,d;return r.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(n=i.data)||void 0===n?void 0:n.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(s=d.data)||void 0===s?void 0:s.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},o=async(e,t)=>{let A=localStorage.getItem("token")||"";try{return await a.Z.post(l+e,t,{headers:{Authorization:"Bearer ".concat(A),"Content-Type":"application/json"}})}catch(e){var n,i;return r.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(n=i.data)||void 0===n?void 0:n.message),e}},s=async(e,t)=>{let A=localStorage.getItem("token")||"";try{return await a.Z.post(l+e,t,{headers:{Authorization:"Bearer ".concat(A),"Content-Type":"application/json"}})}catch(e){return e}},d=async(e,t)=>{let A=localStorage.getItem("token")||"";try{return await a.Z.post(l+e,t,{headers:{Authorization:"Bearer ".concat(A),"Content-Type":"multipart/form-data"}})}catch(e){var n,i,o,s,d;return r.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(n=i.data)||void 0===n?void 0:n.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(s=d.data)||void 0===s?void 0:s.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},c=async(e,t)=>{let A=localStorage.getItem("token")||"";try{return await a.Z.patch(l+e,t,{headers:{Authorization:"Bearer ".concat(A),"Content-Type":"application/json"}})}catch(e){var n,i,o,s,d;return r.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(n=i.data)||void 0===n?void 0:n.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(s=d.data)||void 0===s?void 0:s.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},u=async(e,t)=>{let A=localStorage.getItem("token")||"";try{return await a.Z.put(l+e,t,{headers:{Authorization:"Bearer ".concat(A),"Content-Type":"application/json"}})}catch(e){var n,i,o,s,d;return r.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(n=i.data)||void 0===n?void 0:n.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(s=d.data)||void 0===s?void 0:s.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},h=async e=>{let t=localStorage.getItem("token")||"";try{return await a.Z.delete(l+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var A,n;return r.Z.error(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(A=n.data)||void 0===A?void 0:A.message),e}},g=async e=>{let t=localStorage.getItem("token")||"";try{return await a.Z.delete(l+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){return e}}}},function(e){e.O(0,[748,8863,5300,7220,7542,2344,1744],function(){return e(e.s=33658)}),_N_E=e.O()}]);