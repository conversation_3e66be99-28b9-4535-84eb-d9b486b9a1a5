(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9092],{83298:function(e,A,t){Promise.resolve().then(t.bind(t,22319))},9824:function(e,A,t){"use strict";t.d(A,{Z:function(){return s}});var r=t(32486);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,A=Array(e),t=0;t<e;t++)A[t]=arguments[t];return A.filter((e,A,t)=>!!e&&t.indexOf(e)===A).join(" ")};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,r.forwardRef)((e,A)=>{let{color:t="currentColor",size:n=24,strokeWidth:l=2,absoluteStrokeWidth:s,className:o="",children:u,iconNode:c,...d}=e;return(0,r.createElement)("svg",{ref:A,...a,width:n,height:n,stroke:t,strokeWidth:s?24*Number(l)/Number(n):l,className:i("lucide",o),...d},[...c.map(e=>{let[A,t]=e;return(0,r.createElement)(A,t)}),...Array.isArray(u)?u:[u]])}),s=(e,A)=>{let t=(0,r.forwardRef)((t,a)=>{let{className:s,...o}=t;return(0,r.createElement)(l,{ref:a,iconNode:A,className:i("lucide-".concat(n(e)),s),...o})});return t.displayName="".concat(e),t}},33512:function(e,A,t){"use strict";t.d(A,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9824).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},18208:function(e,A,t){"use strict";t.d(A,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(9824).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},39713:function(e,A,t){"use strict";t.d(A,{default:function(){return n.a}});var r=t(74033),n=t.n(r)},47411:function(e,A,t){"use strict";var r=t(13362);t.o(r,"useParams")&&t.d(A,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(A,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(A,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(A,{useSearchParams:function(){return r.useSearchParams}})},74033:function(e,A,t){"use strict";Object.defineProperty(A,"__esModule",{value:!0}),function(e,A){for(var t in A)Object.defineProperty(e,t,{enumerable:!0,get:A[t]})}(A,{default:function(){return s},getImageProps:function(){return l}});let r=t(60723),n=t(25738),i=t(28863),a=r._(t(44543));function l(e){let{props:A}=(0,n.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,t]of Object.entries(A))void 0===t&&delete A[e];return{props:A}}let s=i.Image},22319:function(e,A,t){"use strict";t.r(A),t.d(A,{default:function(){return h}});var r=t(75376),n=t(33512),i=t(18208),a=t(39713),l=t(32486),s=t(97220),o=t(61838),u=t(56792),c=t(47411),d=t(11492),g=()=>{let e=(0,c.useRouter)();return(0,r.jsxs)("nav",{className:"flex items-center justify-between bg-white px-6 py-4 border-b border-[#E6EAEF]",children:[(0,r.jsx)("h2",{className:"text-[#1D2939] text-base lg:text-lg font-bold",children:"My Workflows"}),(0,r.jsx)("div",{className:"flex items-center gap-3",children:(0,r.jsxs)(d.z,{variant:"outline",className:"border-blue-50 h-9",onClick:()=>{e.push("/client/home/<USER>/new")},children:[(0,r.jsx)(i.Z,{className:"w-5 h-5",color:"#8686F9"}),(0,r.jsx)("span",{className:"ml-1 text-[13px] font-semibold text-blue-200",children:"Add new workflow"})]})})]})};function h(){let{state:e}=(0,l.useContext)(s.R),{workflows:A}=e,[t,h]=(0,l.useState)(""),f=(0,c.useRouter)(),m=(0,o.y)(A,t);return(0,r.jsxs)("div",{className:"flex flex-col h-[calc(100vh-70px)] relative w-full overflow-hidden",children:[(0,r.jsx)(g,{}),(0,r.jsxs)("div",{className:"p-6 w-full overflow-y-auto",children:[(0,r.jsx)("div",{className:"mb-4 flex items-center gap-3",children:(0,r.jsx)("input",{type:"text",placeholder:"Find a workflow",className:"w-full max-w-sm px-4 py-2 border rounded-md text-sm",value:t,onChange:e=>h(e.target.value)})}),null==m?void 0:m.map((e,A)=>(0,r.jsxs)("div",{className:"flex items-center justify-between py-3 hover:bg-gray-50 transition-all group  border mb-4 cursor-pointer p-3 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex gap-3 items-center",children:[(0,r.jsx)("div",{className:"relative flex items-center justify-center size-8 rounded",children:(0,r.jsx)(a.default,{src:(null==e?void 0:e.app_logo)||(null===u.Z||void 0===u.Z?void 0:u.Z.blueBot),alt:"image",width:36,height:36,className:"object-cover rounded size-8 border",unoptimized:!0})}),(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{className:"flex items-center flex-wrap gap-x-1",children:(0,r.jsx)("span",{className:"font-semibold text-gray-800",children:null==e?void 0:e.name})}),(null==e?void 0:e.description)&&(0,r.jsx)("p",{className:"text-gray-600 mt-0.5",children:null==e?void 0:e.description})]})]}),(0,r.jsx)("div",{className:"flex gap-2 items-center mt-1",children:(0,r.jsx)(n.Z,{className:"h-4 w-4 text-gray-400 group-hover:text-indigo-600"})})]},A)),(null==m?void 0:m.length)===0&&(0,r.jsxs)("div",{className:"block text-center mt-40 text-gray-400",children:[(0,r.jsx)("p",{children:"You have no workflow, click the button below to create new workflow"}),(0,r.jsxs)(d.z,{variant:"outline",className:"border-blue-50 h-9 mt-5",onClick:()=>f.push("/client/home/<USER>/new"),children:[(0,r.jsx)(i.Z,{className:"w-5 h-5",color:"#8686F9"}),(0,r.jsx)("span",{className:"ml-1 text-[13px] font-semibold text-blue-200",children:"Add New"})]})]})]})]})}},56792:function(e,A,t){"use strict";t.d(A,{Z:function(){return r}});var r={bot:{src:"/_next/static/media/bot.dfdf2c7a.png",height:80,width:80,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAHlBMVEVMaXEAYC4AlUQAm0YAjUEAfDgrJCgKXTEAj0EAkEKCxuBiAAAACnRSTlMAB73oOFyq4Hx6x9l+tAAAAAlwSFlzAAAsSwAALEsBpT2WqQAAAChJREFUeJxjYEACjIxQBhsbhGZlZ2cF0SxMzMxMLCAVHExMnGiKwQAACx0AUUBiaOwAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},RedBot:{src:"/_next/static/media/bot-red.3a55df2b.png",height:40,width:40,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAIVBMVEVMaXFQAAB3BwJtCARtCQReBQB4CgJvCgRLEAskLS4hKCqjVWtSAAAAC3RSTlMABO01ulzJe9m0qZQuNjoAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAoSURBVHicY2BAAoyMUJqTC8Jg5eBgBdHMbExMbMwgGXYWFnZUxRAAAA2RAGBI3TANAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},blueBot:{src:"/_next/static/media/blue-bot.f296e2d3.png",height:40,width:40,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAKlBMVEXv7/rr7vjt7vj19v7x8f6Hh/ivr/q6u+9gYapzc//Y1/3T0v1mZ2JdX1kQXXeiAAAAA3RSTlP+nJxhYYB9AAAACXBIWXMAAAsTAAALEwEAmpwYAAAANklEQVR4nBXKyQ3AMBDEMNma9ZWk/3YDfwnSJGDHBBJBvtcAzrXmhaf2riNkVI37TaLS5cb2AyAvAORP3/L0AAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},google:{src:"/_next/static/media/google.3c81f1fb.svg",height:16,width:16,blurWidth:0,blurHeight:0},send:{src:"/_next/static/media/send.7163c084.svg",height:18,width:17,blurWidth:0,blurHeight:0},disco:{src:"/_next/static/media/disco.672244de.png",height:192,width:192,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAaVBMVEVMaXHi2M2nopzZsWDKqnHy01GUkIC0m2ODeG8Zl/84f+Lmlwbyuy7Oplrew47k0KassLjamCLdr13j5eXVx6WwqJ7cniSXin7CwLzcw5mPfmV/dWh/fHevtq++qnPu7/TCu7bXybm2t7Oodw53AAAAH3RSTlMA/IlTgiVGFf0LJCo85G/2o3DNqIb6jPf4+/h6mErb4mYc6AAAAAlwSFlzAAAhOAAAITgBRZYxYAAAAEBJREFUeJwlwccBgCAAALGjgw17V9D9h/RhAhSBn7MYB2XVxKHvQNciJaEhtHlaXwmMz+L9Ccxb9vt9gVPysMp8UogCzxGkjJoAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},teammates:{src:"/_next/static/media/teammates.bbd3adac.svg",height:32,width:32,blurWidth:0,blurHeight:0},facebook:{src:"/_next/static/media/facebook.fca64f6c.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAALVBMVEVWmvD//fpHkvBNlfAMcfMee/HX6Py10/kpgvOEt/f///+ex/jG3foAZPJgovbihSuXAAAABHRSTlP+4f7+rTCusQAAAAlwSFlzAAAsSwAALEsBpT2WqQAAADhJREFUeJwFwYkRwDAMAjDsA/wk6f7jVkIYYjrgpuaTAXJ2CEisogTx7B4KYN/bBNyv6rURzqp0/DHzAXV/loHlAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},twitter:{src:"/_next/static/media/twitter.913bc175.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAGFBMVEUAAAABAQEeHh4PDw9qampFRUVYWFhSUlIoHbPFAAAAAXRSTlPhl+sKiAAAAAlwSFlzAAAsSwAALEsBpT2WqQAAAC9JREFUeJw1i0EOACAMwihD/f+PzWbk1KRFYiZRqzAIsr0GnLSDSp463mkoT69/vxFyAH2eHpTfAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},instagram:{src:"/_next/static/media/instagram.07bbefde.png",height:96,width:96,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAPFBMVEX6+vr++v76tezYrP3+pcn9tL77pen6+vn8//////7+r8/+wK7+4an/rNf+07Hpqvz/7aj6ed3+3Lb+rKgwumMZAAAACHRSTlPg////////4NsSfKYAAAAJcEhZcwAAITgAACE4AUWWMWAAAAA+SURBVHicFcs5EsAgDANAYWOQuJP8/68Zum0WkCQKUHLflgRtt/IYwTJnyZngFz3axRrvikZI54yebiNJ1R9EDgII9TTKpAAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},youtube:{src:"/_next/static/media/youtube.b9208f26.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAGFBMVEX5+fn9/////Pz8WVn8Hh78JSX+AAD8n57mNweJAAAAAXRSTlPhl+sKiAAAAAlwSFlzAAAsSwAALEsBpT2WqQAAACxJREFUeJxNi7ENADAMg4ydNP9/3ClVNyRAwmAksG0Q01U9iJzk5IenNt79AhbIAKGvX29oAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},tiktok:{src:"/_next/static/media/tiktok.029728a5.png",height:128,width:128,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAPFBMVEUBAQFMHietfojQv8Y1Exqzs7cBDQwAAAAAAAAZCQxVgYLI9PSxxcp1fIFNjYtHREYdOTfPr7sdVlNtoqCdn0cDAAAACHRSTlPh////////4WC/TK4AAAAJcEhZcwAALEsAACxLAaU9lqkAAAA4SURBVHicY2DgAAMGEM3GxcnBAWZws3KCGHy8rEwsHAwcQsyMPCwgKQFmYUGIGn4mRpA2doh2dgBFkAH9NNczawAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},channel:{src:"/_next/static/media/channel.71b0d2d9.png",height:1827,width:1986,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAMAAAACh/xsAAAAFVBMVEX6+P77+v328//7+/3w6/3////u5v959aEiAAAABXRSTlP94vju40yQVCYAAAAJcEhZcwAAITgAACE4AUWWMWAAAAAnSURBVHicY2BkAgMWBhY2MGBhYGYAAVZGBmZWMEBhMLCygqQYoWoAFpgAqnCsrYsAAAAASUVORK5CYII=",blurWidth:8,blurHeight:7},user:{src:"/_next/static/media/user.cc6e44b6.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAG1BMVEXz/vwD0sHq/PpK4NPU+PVi5NkY18jB9PCF6uJqQ8ocAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAM0lEQVR4nB3JwQ3AMAzEMMlnO9l/4iL9ESBVAFUUPdM8jM6PaH508oq+M7fhrOoe4uqaDxOVAKOLLWTAAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},createChannel:{src:"/_next/static/media/create-channel.d750c07c.png",height:2332,width:2544,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAMAAAACh/xsAAAAD1BMVEX+/v7t6P308vz6+vrm5OpbWQr2AAAACXBIWXMAACxLAAAsSwGlPZapAAAAJ0lEQVR4nEXHwQ0AMAzCQDDZf+aKKFKPD5bNko+mDApDEn2tbk/7AQyWAF9SvWHtAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:7},megaphone:{src:"/_next/static/media/megaphone.d567f808.png",height:24,width:24,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAJ1BMVEVMaXEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACv15x2AAAADXRSTlMAaRhcwINMCyY5ndOqyc6ZIAAAAAlwSFlzAAALEwAACxMBAJqcGAAAADRJREFUeJxVyrkNACAMwEDnI5Cw/7wIiYarXBg+OhRySZUba4dNRDAfmjeIbg83yBDmnZ8DGa0AvpK+HLsAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},visaLogo:{src:"/_next/static/media/visa-logo.e39f2d7e.svg",height:12,width:33,blurWidth:0,blurHeight:0},fileDoc:{src:"/_next/static/media/FileDoc.69079eba.svg",height:20,width:20,blurWidth:0,blurHeight:0},border:{src:"/_next/static/media/Border.8c3c175f.svg",height:1024,width:44,blurWidth:0,blurHeight:0}}},11492:function(e,A,t){"use strict";t.d(A,{d:function(){return s},z:function(){return o}});var r=t(75376),n=t(32486),i=t(91007),a=t(53447),l=t(58983);let s=(0,a.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=n.forwardRef((e,A)=>{let{className:t,variant:n,size:a,asChild:o=!1,...u}=e,c=o?i.g7:"button";return(0,r.jsx)(c,{className:(0,l.cn)(s({variant:n,size:a,className:t})),ref:A,...u})});o.displayName="Button"},58983:function(e,A,t){"use strict";t.d(A,{cn:function(){return i},k:function(){return a}});var r=t(89824),n=t(97215);function i(){for(var e=arguments.length,A=Array(e),t=0;t<e;t++)A[t]=arguments[t];return(0,n.m6)((0,r.W)(A))}let a=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"},61838:function(e,A,t){"use strict";t.d(A,{y:function(){return r}});let r=(e,A)=>null==e?void 0:e.filter(e=>Object.values(e).join(" ").toLowerCase().match(A))},29626:function(e,A,t){"use strict";t.d(A,{F:function(){return i},e:function(){return a}});var r=t(32486);function n(e,A){if("function"==typeof e)return e(A);null!=e&&(e.current=A)}function i(...e){return A=>{let t=!1,r=e.map(e=>{let r=n(e,A);return t||"function"!=typeof r||(t=!0),r});if(t)return()=>{for(let A=0;A<r.length;A++){let t=r[A];"function"==typeof t?t():n(e[A],null)}}}}function a(...e){return r.useCallback(i(...e),e)}},91007:function(e,A,t){"use strict";t.d(A,{Z8:function(){return a},g7:function(){return l},sA:function(){return o}});var r=t(32486),n=t(29626),i=t(75376);function a(e){let A=function(e){let A=r.forwardRef((e,A)=>{let{children:t,...i}=e;if(r.isValidElement(t)){let e,a;let l=(e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.ref:(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.props.ref:t.props.ref||t.ref,s=function(e,A){let t={...A};for(let r in A){let n=e[r],i=A[r];/^on[A-Z]/.test(r)?n&&i?t[r]=(...e)=>{let A=i(...e);return n(...e),A}:n&&(t[r]=n):"style"===r?t[r]={...n,...i}:"className"===r&&(t[r]=[n,i].filter(Boolean).join(" "))}return{...e,...t}}(i,t.props);return t.type!==r.Fragment&&(s.ref=A?(0,n.F)(A,l):l),r.cloneElement(t,s)}return r.Children.count(t)>1?r.Children.only(null):null});return A.displayName=`${e}.SlotClone`,A}(e),t=r.forwardRef((e,t)=>{let{children:n,...a}=e,l=r.Children.toArray(n),s=l.find(u);if(s){let e=s.props.children,n=l.map(A=>A!==s?A:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(A,{...a,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,i.jsx)(A,{...a,ref:t,children:n})});return t.displayName=`${e}.Slot`,t}var l=a("Slot"),s=Symbol("radix.slottable");function o(e){let A=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return A.displayName=`${e}.Slottable`,A.__radixId=s,A}function u(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}},53447:function(e,A,t){"use strict";t.d(A,{j:function(){return a}});var r=t(89824);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=r.W,a=(e,A)=>t=>{var r;if((null==A?void 0:A.variants)==null)return i(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:a,defaultVariants:l}=A,s=Object.keys(a).map(e=>{let A=null==t?void 0:t[e],r=null==l?void 0:l[e];if(null===A)return null;let i=n(A)||n(r);return a[e][i]}),o=t&&Object.entries(t).reduce((e,A)=>{let[t,r]=A;return void 0===r||(e[t]=r),e},{});return i(e,s,null==A?void 0:null===(r=A.compoundVariants)||void 0===r?void 0:r.reduce((e,A)=>{let{class:t,className:r,...n}=A;return Object.entries(n).every(e=>{let[A,t]=e;return Array.isArray(t)?t.includes({...l,...o}[A]):({...l,...o})[A]===t})?[...e,t,r]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}}},function(e){e.O(0,[8863,7140,7220,7542,2344,1744],function(){return e(e.s=83298)}),_N_E=e.O()}]);