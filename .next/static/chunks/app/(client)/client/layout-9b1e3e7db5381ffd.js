(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8044],{54137:function(e,t,a){Promise.resolve().then(a.bind(a,624))},36221:function(e,t,a){"use strict";a.d(t,{r:function(){return i}});var l=a(75376),s=a(32486),n=a(46962);function i(e){let{textToCopy:t,tooltipText:a="Copied",children:i}=e,[o,d]=(0,s.useState)(!1),r=async()=>{try{await navigator.clipboard.writeText(t),d(!0),setTimeout(()=>d(!1),2e3)}catch(e){console.error("Failed to copy text:",e)}};return(0,l.jsx)(n.pn,{children:(0,l.jsxs)(n.u,{open:o,children:[(0,l.jsx)(n.aJ,{asChild:!0,children:(0,l.jsx)("div",{onClick:r,className:"cursor-pointer inline-flex",children:i})}),(0,l.jsx)(n._v,{side:"top",className:"bg-black text-white",children:a})]})})}},56039:function(e,t,a){"use strict";var l=a(32486),s=a(86418);t.Z=()=>{let[e,t]=(0,l.useState)("");return{firstChannel:async e=>{var a,l,n;if(!e)return null;let i=await (0,s.Gl)("/organisations/".concat(e,"/user-channels"));if((null==i?void 0:i.status)===200||(null==i?void 0:i.status)===201){let e=null==i?void 0:null===(n=i.data)||void 0===n?void 0:n.data[0];e&&(localStorage.setItem("channelId",e.channels_id),localStorage.setItem("channelName",e.name),t(e.channels_id))}return null==i?void 0:null===(l=i.data)||void 0===l?void 0:null===(a=l.data[0])||void 0===a?void 0:a.channels_id},channelsId:e}}},22423:function(e,t,a){"use strict";var l=a(75376),s=a(32486),n=a(97712),i=a(97220),o=a(86418);t.Z=()=>{let{state:e,dispatch:t}=(0,s.useContext)(i.R),a=localStorage.getItem("orgId")||"";return(0,s.useEffect)(()=>{a&&(null==e?void 0:e.token)&&(async()=>{let e=await (0,o.Gl)("/organisations/".concat(a,"/user-channels"));if((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201){var l;t({type:n.a.CHANNELS,payload:null==e?void 0:null===(l=e.data)||void 0===l?void 0:l.data})}t({type:n.a.CHANNEL_LOADING,payload:!1})})()},[a,null==e?void 0:e.channelCallback,t]),(0,s.useEffect)(()=>{a&&(null==e?void 0:e.token)&&(async()=>{let e=await (0,o.Gl)("/organisations/".concat(a,"/channels?limit=100"));if((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201){var l;t({type:n.a.ALL_CHANNELS,payload:null==e?void 0:null===(l=e.data)||void 0===l?void 0:l.data})}t({type:n.a.CHANNEL_LOADING,payload:!1})})()},[a,null==e?void 0:e.channelCallback,t]),(0,s.useEffect)(()=>{a&&(async()=>{let e=await (0,o.Gl)("/organisations/".concat(a,"/dms"));if((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201){var l;t({type:n.a.DMS,payload:null==e?void 0:null===(l=e.data)||void 0===l?void 0:l.data})}})()},[a,t,null==e?void 0:e.groupCallback,null==e?void 0:e.dmCount]),(0,s.useEffect)(()=>{a&&(async()=>{let e=await (0,o.Gl)("/organisations/".concat(a,"/fetch-bots"));if((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201){var l;t({type:n.a.AGENT_DM,payload:null==e?void 0:null===(l=e.data)||void 0===l?void 0:l.data})}})()},[a,t,null==e?void 0:e.agentCallback]),(0,s.useEffect)(()=>{a&&(async()=>{let e=await (0,o.Gl)("/organisations/".concat(a,"/workflows"));if((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201){var l;t({type:n.a.WORKFLOWS,payload:null==e?void 0:null===(l=e.data)||void 0===l?void 0:l.data})}})()},[a,t,null==e?void 0:e.workflowCallback]),(0,l.jsx)(l.Fragment,{})}},624:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return Y}});var l=a(75376),s=a(47411),n=a(32486),i=a(97712),o=a(97220),d=a(61639),r=a(22397),c=a(57292),u=a(56927),x=a(90937),p=()=>{let e=localStorage.getItem("channelName")||"",t=(0,s.useRouter)(),{state:a,dispatch:p}=(0,n.useContext)(o.R),m=(0,s.usePathname)();return(0,n.useEffect)(()=>{let e=e=>{let{orgData:t}=e.detail;t&&p({type:i.a.ORG_DATA,payload:t})};return window.addEventListener("creditBalanceUpdated",e),()=>{window.removeEventListener("creditBalanceUpdated",e)}},[p]),(0,l.jsxs)("div",{className:"fixed h-[60px] w-full z-40 flex items-center justify-between px-4 lg:px-6 py-3 bg-blue-500 text-white",children:[(0,l.jsxs)("div",{className:"hidden lg:flex items-center gap-6 relative cursor-pointer",children:[(0,l.jsx)("div",{onClick:()=>t.back(),children:(0,l.jsx)(x.J,{})}),(0,l.jsx)("div",{onClick:()=>t.forward(),children:(0,l.jsx)(x.xS,{})})]}),(0,l.jsxs)("div",{className:"flex items-center gap-6 relative cursor-pointer",children:[(0,l.jsx)("div",{className:"flex lg:hidden",onClick:()=>p({type:i.a.OPEN_SIDEBAR,payload:!(null==a?void 0:a.openSidebar)}),children:(null==a?void 0:a.openSidebar)?(0,l.jsx)(r.Z,{className:"text-white cursor-pointer",onClick:()=>p({type:i.a.OPEN_SIDEBAR,payload:!1})}):(0,l.jsx)(d.Z,{})}),!(null==m?void 0:m.includes("/organization/create"))&&(0,l.jsx)("div",{className:"flex lg:hidden border w-[80px] items-center justify-center rounded text-center text-xs py-1",onClick:()=>p({type:i.a.CHANNEL_BAR,payload:!(null==a?void 0:a.channelBar)}),children:(null==a?void 0:a.channelBar)?(0,l.jsx)("p",{children:"Close menu"}):(0,l.jsx)("p",{children:"Open menu"})})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between gap-6 w-full ml-[20px] lg:ml-[410px]",children:[m.includes("/client/welcome")?(0,l.jsx)("div",{}):(0,l.jsxs)("div",{className:"hidden md:flex relative",children:[(0,l.jsx)(c.Z,{className:"absolute text-white left-3 top-2.5",size:16}),(0,l.jsx)("input",{type:"text",placeholder:e?"Search ".concat(e):"Search",className:"bg-blue-100 text-white text-sm placeholder-white pl-10 pr-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-400 w-48 md:w-64 lg:w-80"})]}),(0,l.jsx)("div",{className:"flex md:hidden"}),(0,l.jsx)("div",{className:"flex items-center gap-6 bg-white px-2 py-1 rounded-lg",children:(0,l.jsxs)("div",{className:"flex items-center gap-2 cursor-pointer",onClick:()=>t.push("/client/settings/organisation/billing/all-plans"),children:[(0,l.jsx)("div",{className:"border border-[#d0d0fd] bg-[f1f1fe] rounded-md p-1",children:(0,l.jsx)(u.Z,{name:"money",svgProps:{color:"#8686F9"}})}),(0,l.jsxs)("p",{className:"text-black text-[10px] sm:text-xs",children:[a&&a.orgData?a.orgData.credit_balance:0," ",(0,l.jsx)("span",{className:"text-[#667085]",children:"AI Credits"})]})]})})]})]})},m=a(37847),f=a(59327),v=a(71753),h=a(47720),g=a(16669),j=a(39713),y=a(25368),N=a(86418),b=a(73003),w=a(26242),C=a(56039),S=a(5870),E=a(75148),_=a(51888),k=a(93193),I=a(56792),A=a(16006),R=a(33599),D=a(11492),O=a(74178),z=a(99402),L=a(89863);let T=[{emoji:"\uD83D\uDCC5",text:"In a meeting",duration:"1 hour",section:"For HNG Inc"},{emoji:"\uD83D\uDE97",text:"Commuting",duration:"30 minutes",section:"For HNG Inc"},{emoji:"\uD83E\uDD12",text:"Out sick",duration:"Today",section:"For HNG Inc"},{emoji:"\uD83C\uDF34",text:"Vacationing",duration:"Don’t clear",section:"For HNG Inc"},{emoji:"\uD83C\uDFE1",text:"Working remotely",duration:"Today",section:"For HNG Inc"},{emoji:"\uD83D\uDCC5",text:"In a meeting",duration:"Based on your Google Calendar",section:"Automatically updates"}].reduce((e,t)=>{let a=t.section||"Other";return e[a]||(e[a]=[]),e[a].push(t),e},{});var Z=()=>{let{state:e,dispatch:t}=(0,n.useContext)(o.R),{user:a,statusCallback:s,status:d}=e,[c,u]=(0,n.useState)(null==a?void 0:a.text),[x,p]=(0,n.useState)(null==a?void 0:a.icon),[m,f]=(0,n.useState)(!1),[v,h]=(0,n.useState)(null==a?void 0:a.status_timeout),[g,j]=(0,n.useState)(null==a?void 0:a.pause_notification),[y,w]=(0,n.useState)(!1);(0,n.useEffect)(()=>{u(null==a?void 0:a.text),p(null==a?void 0:a.icon),h(null==a?void 0:a.status_timeout),j(null==a?void 0:a.pause_notification),s&&(u(""),p("")),setTimeout(()=>{t({type:i.a.STATUS_CALLBACK,payload:!1})},2e3)},[null==a?void 0:a.text,null==a?void 0:a.icon,s]);let C=async()=>{w(!0);let a=await (0,N.xo)("/profile/change-status",{icon:x,text:c,status_timeout:v,pause_notification:g,clear_status:!1});((null==a?void 0:a.status)===200||(null==a?void 0:a.status)===201)&&(t({type:i.a.PROFILE_CALLBACK,payload:!(null==e?void 0:e.profileCallback)}),t({type:i.a.STATUS,payload:!1})),w(!1)};return(0,l.jsx)(A.Vq,{open:d,onOpenChange:()=>t({type:i.a.STATUS,payload:!1}),children:(0,l.jsxs)(A.cZ,{className:"max-w-[500px] rounded-lg px-0",children:[(0,l.jsxs)(A.fK,{className:"flex px-5 flex-row items-center justify-between",children:[(0,l.jsx)(A.$N,{className:"text-xl font-semibold",children:"Set a status"}),(0,l.jsx)(A.GG,{asChild:!0,children:(0,l.jsxs)("button",{onClick:()=>t({type:i.a.STATUS,payload:!1}),children:[(0,l.jsx)(r.Z,{size:20,className:"text-[#667085] -mt-1.5"}),(0,l.jsx)("span",{className:"sr-only",children:"Close"})]})})]}),(0,l.jsxs)("div",{className:"flex mx-5 items-center gap-2 border rounded px-3 py-2 mb-3",children:[(0,l.jsxs)(O.J2,{open:m,onOpenChange:f,children:[(0,l.jsx)(O.xo,{asChild:!0,children:(0,l.jsx)("button",{className:"text-2xl hover:opacity-80",children:x||(0,l.jsx)(E.Z,{})})}),(0,l.jsx)(O.yk,{className:"p-0 w-full max-w-xs",children:(0,l.jsx)(z.Z,{data:L,onEmojiSelect:e=>{p(e.native),f(!1)}})})]}),(0,l.jsx)("input",{type:"text",value:c,onChange:e=>u(e.target.value),placeholder:"What's your status?",className:"flex-1 border-0 focus:outline-none"}),(c||x)&&(0,l.jsx)("button",{onClick:()=>{p(""),u(""),t({type:i.a.STATUS,payload:!1})},children:(0,l.jsx)(r.Z,{className:"w-4 h-4 text-gray-400"})})]}),!x&&!c&&(0,l.jsx)("div",{className:"space-y-2 text-sm",children:Object.entries(T).map(e=>{let[t,a]=e;return(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-xs m-5 text-gray-500 my-3",children:t}),(0,l.jsx)("div",{children:a.map((e,t)=>(0,l.jsxs)("div",{onClick:()=>{p(e.emoji),u(e.text),h(e.duration)},className:"flex items-center gap-3 px-5 py-2 rounded hover:bg-blue-500 hover:text-white rounded-none cursor-pointer",children:[(0,l.jsxs)("span",{className:"flex gap-2 items-center",children:[(0,l.jsx)("span",{children:e.emoji}),(0,l.jsx)("span",{children:e.text})]}),(0,l.jsx)("span",{children:"-"}),(0,l.jsx)("span",{className:"text-xs text-gray-400",children:e.duration})]},t))})]},t)})}),(x||c)&&(0,l.jsxs)("div",{className:"px-5",children:[(0,l.jsx)("label",{className:"text-sm text-gray-700 block mb-1",children:"Remove status after..."}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("select",{value:v,onChange:e=>h(e.target.value),className:"w-full border rounded px-3 py-2 appearance-none focus:outline-none",children:["30 minutes","1 hour","Today","This week","Don’t remove"].map(e=>(0,l.jsx)("option",{value:e,children:e},e))}),(0,l.jsx)(R.Z,{className:"absolute right-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-500 pointer-events-none"})]})]}),(0,l.jsx)("div",{className:"flex items-center gap-2 px-5",children:(0,l.jsxs)("label",{className:"flex items-center text-sm gap-2",children:[(0,l.jsx)("input",{type:"checkbox",className:"form-checkbox",checked:g,onChange:()=>j(e=>!e)}),"Pause notifications"]})}),(0,l.jsxs)("div",{className:"flex justify-end gap-3 mt-6 px-5",children:[(0,l.jsx)(D.z,{variant:"outline",className:"h-9 border border-[#101828]/40",onClick:()=>t({type:i.a.STATUS,payload:!1}),children:"Cancel"}),(0,l.jsxs)(D.z,{className:"bg-blue-300 h-9 text-white px-7 d-flex items-center gap-2",disabled:!x&&!c,onClick:C,children:["Save ",y&&(0,l.jsx)(b.Z,{})]})]})]})})},F=e=>{let{user:t}=e,{state:a,dispatch:s}=(0,n.useContext)(o.R),{orgData:d,user:r}=a,[c,u]=(0,n.useState)(!1),[x,p]=(0,n.useState)(!1),[m,f]=(0,n.useState)(""),[v,h]=(0,n.useState)("");(0,n.useEffect)(()=>{f((null==r?void 0:r.text)||""),h((null==r?void 0:r.icon)||"")},[r]);let g=async()=>{localStorage.clear(),window.location.href="/auth/login"},b=async()=>{h(""),f("");let e=await (0,N.xo)("/profile/change-status",{icon:"",text:"",status_timeout:"",pause_notification:!1,clear_status:!0});((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201)&&(s({type:i.a.PROFILE_CALLBACK,payload:!(null==a?void 0:a.profileCallback)}),s({type:i.a.STATUS_CALLBACK,payload:!(null==a?void 0:a.statusCallback)}))};return(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsxs)(y.h_,{open:c,onOpenChange:u,children:[(0,l.jsx)(y.$F,{asChild:!0,children:(0,l.jsxs)("div",{className:"flex items-center justify-center flex-col rounded-md cursor-pointer bg-blue-300 ".concat(v||m?"pt-2":""),children:[(0,l.jsx)("span",{className:"text-sm",children:v}),(0,l.jsx)("div",{className:"rounded-md h-[40px] w-[40px] cursor-pointer hover:opacity-90",onClick:()=>u(!0),children:(0,l.jsx)(j.default,{src:(null==t?void 0:t.avatar_url)||(null===I.Z||void 0===I.Z?void 0:I.Z.user),alt:"",width:40,height:40,className:"rounded-md w-full h-full"})})]})}),(0,l.jsxs)(y.AW,{className:"w-[300px] p-0 mb-8 ml-2",side:"right",align:"start",sideOffset:5,children:[(0,l.jsx)("div",{className:"p-5 pb-4",children:(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"w-[36px] h-[36px] rounded-md overflow-hidden border",children:(0,l.jsx)(j.default,{src:(null==t?void 0:t.avatar_url)||(null===I.Z||void 0===I.Z?void 0:I.Z.user),alt:"",width:36,height:36,className:"rounded-md"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-semibold text-sm",children:null==t?void 0:t.username}),(0,l.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,l.jsx)("div",{className:"w-2 h-2 rounded-full bg-green-600"}),(0,l.jsx)("span",{className:"text-xs text-gray-500",children:"Active"})]})]})]})}),(0,l.jsx)("div",{className:"px-5 py-1 mb-2",children:(0,l.jsxs)("div",{className:"rounded-md border px-3 h-[40px] flex items-center cursor-pointer transition-all ".concat(x?"bg-gray-50 border-gray-300":"border-gray-200"),onMouseEnter:()=>p(!0),onMouseLeave:()=>p(!1),onClick:()=>{s({type:i.a.STATUS,payload:!0}),u(!1)},children:[(v||m)&&(0,l.jsxs)("div",{className:"flex items-center gap-2 text-gray-500",children:[x?(0,l.jsx)(S.Z,{className:"w-[18px] h-[18px]"}):(0,l.jsx)("span",{className:"text-lg",children:v}),(0,l.jsx)("span",{className:"text-sm truncate w-[180px]",children:m})]}),!v&&!m&&(0,l.jsxs)("div",{className:"flex items-center gap-2 text-gray-500",children:[x?(0,l.jsx)("span",{className:"text-lg",children:"\uD83D\uDE0A"}):(0,l.jsx)(E.Z,{className:"w-[22px] h-[22px]"}),(0,l.jsx)("span",{className:"text-sm truncate",children:"Update your status"})]})]})}),(v||m)&&(0,l.jsx)(y.Xi,{onClick:b,className:"px-5 py-1.5 cursor-pointer hover:bg-blue-500 hover:text-white",children:(0,l.jsx)("span",{children:"Clear status"})}),(0,l.jsx)(y.Xi,{className:"px-5 py-1.5 cursor-pointer hover:bg-blue-500 hover:text-white",children:(0,l.jsxs)("span",{children:["Set yourself as ",(0,l.jsx)("span",{className:"font-semibold",children:"away"})]})}),(0,l.jsx)(y.Xi,{className:"px-5 py-1.5 cursor-pointer hover:bg-blue-500 hover:text-white",children:(0,l.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,l.jsx)("span",{children:"Pause notifications"}),(0,l.jsx)(_.Z,{size:16})]})}),(0,l.jsx)(y.VD,{className:"my-2 bg-gray-200"}),(0,l.jsx)(y.Xi,{onClick:()=>{s({type:i.a.PROFILE,payload:t}),s({type:i.a.SHOW_USER_PROFILE,payload:!0})},className:"px-5 py-1.5 cursor-pointer hover:bg-blue-500 hover:text-white",children:(0,l.jsx)("span",{className:"w-full",children:"Profile"})}),(0,l.jsx)(y.Xi,{className:"px-5 py-1.5 cursor-pointer hover:bg-blue-500 hover:text-white",children:(0,l.jsx)("span",{className:"w-full",children:"Preferences"})}),(0,l.jsx)(y.VD,{className:"my-1 bg-gray-200"}),(0,l.jsx)(y.Xi,{className:"px-5 py-2 cursor-pointer hover:bg-blue-500 hover:text-white",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(k.Z,{size:18}),(0,l.jsxs)("span",{children:["Upgrade ",(0,l.jsx)("span",{className:"capitalize",children:null==d?void 0:d.name})]})]})}),(0,l.jsx)(y.Xi,{onClick:g,className:"px-5 py-2 cursor-pointer hover:bg-blue-500 hover:text-white",children:(null==d?void 0:d.name)?(0,l.jsxs)("span",{children:["Sign out of ",(0,l.jsx)("span",{className:"capitalize",children:null==d?void 0:d.name})]}):(0,l.jsx)("span",{children:"Sign out"})})]})]}),(0,l.jsx)(Z,{})]})},G=a(22423),P=a(13352),U=()=>{let e=(0,s.usePathname)(),{state:t,dispatch:a}=(0,n.useContext)(o.R),{user:d}=t,[r,c]=(0,n.useState)(null),[u,p]=(0,n.useState)(!1),[S,E]=(0,n.useState)(null),[_,k]=(0,n.useState)(!0),I=localStorage.getItem("channelId")||"",{firstChannel:A}=(0,C.Z)(),[R,D]=(0,n.useState)([]),[O,z]=(0,n.useState)(!1);(0,n.useEffect)(()=>{let e=localStorage.getItem("orgId")||"";(async()=>{let t=await (0,N.Gl)("/users/organisations");if((null==t?void 0:t.status)===200||(null==t?void 0:t.status)===201){var l,s,n;c((0,w.BY)(null==t?void 0:null===(l=t.data)||void 0===l?void 0:l.data)),L();let o=null==t?void 0:null===(n=t.data)||void 0===n?void 0:null===(s=n.data)||void 0===s?void 0:s.find(t=>t.id===e);E(o),a({type:i.a.ORG_DATA,payload:o}),a({type:i.a.ORG_ID,payload:null==o?void 0:o.id}),localStorage.setItem("orgId",null==o?void 0:o.id),k(!1)}})()},[null==t?void 0:t.callback,O]),(0,n.useEffect)(()=>{(async()=>{let e=await (0,N.Gl)("/organisations/pin");((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201)&&D(e.data.data)})()},[O]);let L=async()=>{let e=localStorage.getItem("orgId")||"",t=JSON.parse(localStorage.getItem("user")||"{}"),l=await (0,N.Gl)("/users/".concat(t.id,"/organisations/").concat(e,"/roles"));if((null==l?void 0:l.status)===200||(null==l?void 0:l.status)===201){var s,n,o,d;a({type:i.a.ROLE,payload:null==l?void 0:null===(n=l.data)||void 0===n?void 0:null===(s=n.data)||void 0===s?void 0:s.role_name}),localStorage.setItem("role",null==l?void 0:null===(d=l.data)||void 0===d?void 0:null===(o=d.data)||void 0===o?void 0:o.role_name)}},T=async e=>{localStorage.removeItem("channelId"),a({type:i.a.LOADING,payload:!0});let t={current_org:null==e?void 0:e.id},l=await (0,N.an)("/users/switch-org",t);if((null==l?void 0:l.status)===200||(null==l?void 0:l.status)===201){var s,n,o,d,r,c,u,x,m,f,v;localStorage.setItem("token",null==l?void 0:null===(n=l.data)||void 0===n?void 0:null===(s=n.data)||void 0===s?void 0:s.access_token),localStorage.setItem("orgId",null==l?void 0:null===(r=l.data)||void 0===r?void 0:null===(d=r.data)||void 0===d?void 0:null===(o=d.organisation)||void 0===o?void 0:o.id),a({type:i.a.ORG_ID,payload:null==l?void 0:null===(x=l.data)||void 0===x?void 0:null===(u=x.data)||void 0===u?void 0:null===(c=u.organisation)||void 0===c?void 0:c.id});let e=await A(null==l?void 0:null===(v=l.data)||void 0===v?void 0:null===(f=v.data)||void 0===f?void 0:null===(m=f.organisation)||void 0===m?void 0:m.id);p(!1),a({type:i.a.LOADING,payload:!1}),L(),e?window.location.href="/client/home/<USER>/".concat(e):window.location.href="/client"}else a({type:i.a.LOADING,payload:!1})},Z=async e=>{localStorage.removeItem("channelId"),a({type:i.a.LOADING,payload:!0});let t={current_org:null==e?void 0:e.org_id},l=await (0,N.an)("/users/switch-org",t);if((null==l?void 0:l.status)===200||(null==l?void 0:l.status)===201){var s,n,o,d,r,c,u,x,m,f,v;localStorage.setItem("token",null==l?void 0:null===(n=l.data)||void 0===n?void 0:null===(s=n.data)||void 0===s?void 0:s.access_token),localStorage.setItem("orgId",null==l?void 0:null===(r=l.data)||void 0===r?void 0:null===(d=r.data)||void 0===d?void 0:null===(o=d.organisation)||void 0===o?void 0:o.id),a({type:i.a.ORG_ID,payload:null==l?void 0:null===(x=l.data)||void 0===x?void 0:null===(u=x.data)||void 0===u?void 0:null===(c=u.organisation)||void 0===c?void 0:c.id});let e=await A(null==l?void 0:null===(v=l.data)||void 0===v?void 0:null===(f=v.data)||void 0===f?void 0:null===(m=f.organisation)||void 0===m?void 0:m.id);p(!1),a({type:i.a.LOADING,payload:!1}),L(),window.location.href="/client/home/<USER>/".concat(e)}else a({type:i.a.LOADING,payload:!1})},U=async(e,t)=>{t.stopPropagation();let a=await (0,N.xo)("/organisations/pin",{org_id:e});((null==a?void 0:a.status)===200||(null==a?void 0:a.status)===201)&&(z(!O),P.Z.success(a.data.message))},B=async(e,t)=>{t.stopPropagation();let a=await (0,N.jx)("/organisations/pin/".concat(e));((null==a?void 0:a.status)===200||(null==a?void 0:a.status)===201)&&(z(!O),P.Z.success(a.data.message))};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(G.Z,{}),(0,l.jsx)("div",{className:"fixed bg-primary-200 top-[40px] z-30 lg:translate-x-0 transition-transform duration-300 ease-in-out\n               ".concat((null==t?void 0:t.openSidebar)===!0?"translate-x-0":"-translate-x-full"),children:(0,l.jsxs)("div",{className:"flex h-[calc(100vh-30px)]",onClick:()=>{localStorage.setItem("channelName",""),a({type:i.a.CHANNEL_BAR,payload:!1})},children:[(0,l.jsxs)("div",{className:"bg-blue-500 w-[65px] flex flex-col justify-start pb-8",children:[(0,l.jsx)("div",{className:"h-full w-full text-[#344054] rounded-[12px] z-auto flex flex-col justify-between ".concat((null==e?void 0:e.includes("/welcome"))?"invisible":""),children:(0,l.jsxs)("div",{className:"relative flex flex-col items-center justify-center gap-5 mt-4",children:[(0,l.jsx)("div",{className:"flex items-center justify-center p-2 relative",children:(0,l.jsxs)(y.h_,{open:u,onOpenChange:p,children:[(0,l.jsx)(y.$F,{asChild:!0,children:(0,l.jsx)("div",{className:"h-[42px] w-[42px] flex items-center justify-center border border-border shadow-md bg-white rounded-sm cursor-pointer",children:_?(0,l.jsx)(b.Z,{width:"25",height:"25",color:"#7b50fb"}):(0,l.jsx)("div",{className:"h-[35px] w-[35px] rounded-sm border flex items-center justify-center",children:(null==S?void 0:S.logo_url)?(0,l.jsx)(j.default,{src:null==S?void 0:S.logo_url,width:35,height:35,alt:"",className:"h-[35px] w-[35px] rounded-sm"}):(0,l.jsx)("h3",{className:"text-primary-500 font-bold text-sm",children:(0,w.Qm)(null==S?void 0:S.name)})})})}),!e.includes("/client/welcome")&&!e.includes("/client/invited")&&(0,l.jsxs)(y.AW,{className:"ml-6 p-0 rounded-xl w-[300px] cursor-pointer",children:[(0,l.jsx)("div",{className:"max-h-[400px] overflow-y-auto",children:null==r?void 0:r.map((e,t)=>(0,l.jsx)(y.Xi,{className:"hover:bg-[#f2f4f7] relative w-full cursor-pointer rounded-xs px-4 py-3",onClick:()=>T(e),children:(0,l.jsxs)("div",{className:"flex w-full items-center gap-3",children:[(0,l.jsx)("div",{className:"h-[40px] w-[40px] rounded-md border p-[5px] flex items-center justify-center ".concat((null==e?void 0:e.id)===(null==S?void 0:S.id)?"border-2 border-blue-200":""),children:(null==e?void 0:e.logo_url)?(0,l.jsx)(j.default,{src:null==e?void 0:e.logo_url,width:35,height:35,alt:"",className:"h-full w-full rounded-sm"}):(0,l.jsx)("h3",{className:"text-primary-500 font-bold text-base",children:(0,w.Qm)(null==e?void 0:e.name)})}),(0,l.jsx)("div",{children:(0,l.jsx)("h3",{className:"text-[15px] mb-[1px] capitalize",children:null==e?void 0:e.name})}),(0,l.jsx)("div",{onClick:t=>(null==e?void 0:e.pinned)?B(null==e?void 0:e.id,t):U(null==e?void 0:e.id,t),className:"absolute top-6 right-5 z-10 hover:bg-gray-300 p-1 rounded-lg ".concat((null==e?void 0:e.pinned)?"bg-gray-300":""),children:(0,l.jsx)(m.Z,{size:15})})]})},t))}),(0,l.jsx)("hr",{}),(0,l.jsx)(g.default,{href:"/client/organization/create",children:(0,l.jsx)("div",{className:"sticky bottom-0 bg-white w-full p-4 hover:bg-[#f2f4f7]",onClick:()=>{p(!1)},children:(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)(x.pO,{}),(0,l.jsx)("h3",{className:"text-[15px] mb-0",children:"Add an organization"})]})})})]})]})}),!e.includes("/client/welcome")&&!e.includes("/client/invited")&&(0,l.jsx)(g.default,{href:"/client/organization/create",className:"relative cursor-pointer",children:(0,l.jsx)(x.$t,{})}),null==R?void 0:R.map((e,t)=>(0,l.jsxs)("div",{className:"group relative h-[35px] w-[35px] flex items-center justify-center bg-white rounded-sm cursor-pointer",onClick:()=>Z(e),children:[(0,l.jsx)("div",{className:"h-[35px] w-[35px] rounded-sm flex items-center justify-center",children:(null==e?void 0:e.avatar_url)?(0,l.jsx)(j.default,{src:null==e?void 0:e.avatar_url,width:35,height:35,alt:"",className:"h-[35px] w-[35px] rounded-sm",unoptimized:!0}):(0,l.jsx)("h3",{className:"text-primary-500 font-bold text-sm",children:(0,w.Qm)(null==e?void 0:e.org_name)})}),(0,l.jsx)("div",{className:"absolute -right-2 -top-2 cursor-pointer bg-white rounded-full hidden group-hover:flex items-center justify-center p-1 shadow",onClick:t=>{B(null==e?void 0:e.org_id,t)},children:(0,l.jsx)(f.Z,{color:"red",size:12})})]},t))]})}),(0,l.jsxs)("div",{className:"relative flex flex-col items-center",children:[(0,l.jsx)(g.default,{href:"/client/notifications",className:"flex flex-col group z-50 mb-1 cursor-pointer items-center justify-center w-full ".concat("/client/notifications"===e?"font-medium scale-[1.1]":"hover:font-medium "," "),children:(0,l.jsx)("span",{className:"p-[8px] rounded-[7px]  ".concat((null==e?void 0:e.includes("/client/notifications"))?"bg-blue-200":"group-hover:bg-blue-200"),children:(0,l.jsx)(x.Dk,{})})}),(0,l.jsx)(g.default,{href:"/client/settings/personal/account",className:"flex flex-col group z-50 mb-1 cursor-pointer items-center justify-center w-full ".concat("/client/settings"===e?"font-medium scale-[1.1]":"hover:font-medium "," "),children:(0,l.jsx)("span",{className:"p-[8px] rounded-[7px]  ".concat((null==e?void 0:e.includes("/client/settings"))?"bg-blue-200":"group-hover:bg-blue-200"),children:(0,l.jsx)(x.UG,{})})}),(0,l.jsx)(F,{user:d})]})]}),(0,l.jsx)("div",{className:"bg-blue-400 w-[80px] flex flex-col justify-start",children:!e.includes("/client/welcome")&&!e.includes("/client/invited")&&(0,l.jsx)("div",{className:"h-full w-full text-[#344054] rounded-[12px] z-auto flex flex-col justify-between",children:(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(v.Z,{className:"md:hidden absolute right-0 top-0",onClick:()=>a({type:i.a.OPEN_SIDEBAR,payload:!1})}),(0,l.jsxs)("div",{className:"flex flex-col items-center mt-[20px] w-full text-[12px]",onClick:()=>a({type:i.a.OPEN_SIDEBAR,payload:!1}),children:[(0,l.jsxs)(g.default,{href:I?"/client/home/<USER>/".concat(I):"/client",className:"flex flex-col group mb-2 items-center justify-center space-y-1 w-full p-[5px] ".concat("/client/home"===e?"font-medium scale-[1.1]":"hover:font-medium "," "),children:[(0,l.jsx)("span",{className:"p-[8px] rounded-[7px]  ".concat("/client"===e||(null==e?void 0:e.includes("/home"))?"bg-blue-200":"group-hover:bg-blue-200"),children:(0,l.jsx)(x.tv,{})}),(0,l.jsx)("span",{className:"text-white",children:"Home"})]}),(0,l.jsxs)(g.default,{href:"/client/dm",className:"flex flex-col group mb-2 items-center justify-center space-y-1 w-full p-[5px] ".concat("/client"===e?"font-medium scale-[1.1]":"hover:font-medium "," "),children:[(0,l.jsx)("span",{className:"p-[8px] rounded-[7px]  ".concat((null==e?void 0:e.includes("/client/dm"))?"bg-blue-200":"group-hover:bg-blue-200"),children:(0,l.jsx)(x.rL,{})}),(0,l.jsx)("span",{className:"text-white",children:"DMs"})]}),(0,l.jsxs)(g.default,{href:"/client/people",className:"flex flex-col group mb-2 items-center justify-center space-y-1 w-full p-[5px] ".concat((null==e?void 0:e.includes("/client/people"))?"font-medium scale-[1.1]":"hover:font-medium "," "),children:[(0,l.jsx)("span",{className:"p-[8px] rounded-[7px]  ".concat((null==e?void 0:e.includes("/client/people"))?"bg-blue-200":"group-hover:bg-blue-200"),children:(0,l.jsx)(x.t0,{})}),(0,l.jsx)("span",{className:"text-white",children:"People"})]}),(0,l.jsxs)(g.default,{href:"/client/agents",className:"flex flex-col group mb-2 items-center justify-center space-y-1 w-full p-[5px] ".concat("/client/agents"===e?"font-medium scale-[1.1]":"hover:font-medium "," "),children:[(0,l.jsx)("span",{className:"p-[8px] rounded-[7px]  ".concat((null==e?void 0:e.includes("/client/agents"))?"bg-blue-200":"group-hover:bg-blue-200"),children:(0,l.jsx)(x.I0,{})}),(0,l.jsx)("span",{className:"text-white",children:"Agents"})]}),(0,l.jsxs)(g.default,{href:"/client/later",className:"flex flex-col group mb-2 items-center justify-center space-y-1 w-full p-[5px] ".concat("/client/later"===e?"font-medium scale-[1.1]":"hover:font-medium "," "),children:[(0,l.jsx)("span",{className:"p-[8px] rounded-[7px]  ".concat((null==e?void 0:e.includes("/client/later"))?"bg-blue-200":"group-hover:bg-blue-200"),children:(0,l.jsx)(h.Z,{color:"white",size:18})}),(0,l.jsx)("span",{className:"text-white",children:"Later"})]})]})]})})})]})})]})},B=()=>{let{state:e,dispatch:t}=(0,n.useContext)(o.R),a=localStorage.getItem("orgId")||"";return(0,n.useEffect)(()=>{a&&(async()=>{let e=await (0,N.Gl)("/organisations/".concat(a,"/recent-dm"));if((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201){var l;t({type:i.a.RECENT_DM,payload:null==e?void 0:null===(l=e.data)||void 0===l?void 0:l.data})}})()},[a,null==e?void 0:e.dmCount]),(0,n.useEffect)(()=>{a&&(async()=>{let e=await (0,N.Gl)("/organisations/".concat(a,"/recent-dm"));if((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201){var l;t({type:i.a.RECENT_PEOPLE,payload:null==e?void 0:null===(l=e.data)||void 0===l?void 0:l.data})}})()},[a,null==e?void 0:e.dmCount]),(0,n.useEffect)(()=>{let e=localStorage.getItem("orgId")||"";(async()=>{let a=await (0,N.Gl)("/organisations/".concat(e,"/saved/message"));if((null==a?void 0:a.status)===200||(null==a?void 0:a.status)===201){var l,s;t({type:i.a.LATER,payload:a.data.data});let e=null==a?void 0:null===(s=a.data)||void 0===s?void 0:null===(l=s.data)||void 0===l?void 0:l.map(e=>({id:e.id,thread_id:e.thread_id}));t({type:i.a.BOOKMARKS,payload:e})}})()},[]),(0,l.jsx)(l.Fragment,{})},H=a(53189),M=a(37313),V=a(90822),W=a(36221),K=()=>{let{state:e,dispatch:t}=(0,n.useContext)(o.R),{user:a}=e,[d,c]=(0,n.useState)(!1),u=(0,s.usePathname)();return(0,n.useEffect)(()=>{t({type:i.a.SHOW_PROFILE,payload:!1})},[u]),(0,l.jsx)("div",{className:"fixed mt-[60px] right-0 top-0 z-20 w-full sm:w-[408px] h-full bg-white border-l border-[#E6EAEF] shadow-[-3px_0px_25px_0px_#DFDFDF] ".concat((null==e?void 0:e.showUserProfile)?"translate-x-0":"translate-x-full"),children:(0,l.jsxs)("div",{className:"flex flex-col h-full",children:[(0,l.jsxs)("nav",{className:"flex items-center justify-between p-5 py-[23px] border-b border-[#E6EAEF]",children:[(0,l.jsx)("h2",{className:"text-[#1D2939] text-lg font-bold",children:"Profile"}),(0,l.jsx)("button",{onClick:()=>{t({type:i.a.USER_DATA}),t({type:i.a.SHOW_USER_PROFILE,payload:!1})},className:"text-[#344054] p-1 border border-input rounded-[0.3125rem]",children:(0,l.jsx)(r.Z,{className:"size-5 text-[#344054]"})})]}),(0,l.jsxs)("div",{className:"py-5 flex flex-col gap-5 overflow-y-auto [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar-thumb]:rounded-full",children:[(0,l.jsxs)("div",{className:"flex flex-col gap-5 px-5",children:[(0,l.jsx)(j.default,{src:(null==a?void 0:a.avatar_url)||(null===I.Z||void 0===I.Z?void 0:I.Z.user),alt:null==a?void 0:a.username,width:250,height:250,className:"rounded-[9px] border  h-[250px] w-[250px] object-cover",unoptimized:!0}),(0,l.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between gap-3",children:[(0,l.jsx)("h2",{className:"text-[#101828] text-[22px] font-black",children:null==a?void 0:a.display_name}),(0,l.jsx)("button",{className:"text-[#6868F7] font-semibold cursor-pointer",onClick:()=>{c(!0)},children:"Edit"})]}),(0,l.jsx)("p",{className:"text-[#344054] text-lg",children:(null==a?void 0:a.title)||"Product Manager"}),(0,l.jsxs)("div",{className:"flex gap-[10px] items-center",children:[(0,l.jsx)(x.yP,{color:"#475467"}),(0,l.jsx)("p",{className:"text-[15px] text-[#344054]",children:"Away, Notifications snoozed"})]}),(0,l.jsxs)("div",{className:"flex gap-[10px] items-center",children:[(0,l.jsx)(x.T3,{}),(0,l.jsx)("p",{className:"text-[15px] text-[#344054]",children:null==a?void 0:a.timezone})]})]}),(0,l.jsxs)("div",{className:"flex gap-3 items-center",children:[(0,l.jsx)(D.z,{variant:"outline",className:"h-fit p-[7px] border-[#E6EAEF]",children:(0,l.jsx)(H.Z,{size:20,strokeWidth:1.5,color:"#667085"})}),(0,l.jsxs)(D.z,{variant:"outline",className:"h-fit py-[7px] px-[10px] border-[#E6EAEF] font-semibold text-[13px] text-[#344054] gap-1",children:[(0,l.jsx)(x.yP,{})," Mute"]}),(0,l.jsxs)(D.z,{variant:"outline",className:"h-fit py-[7px] px-[10px] border-[#E6EAEF] font-semibold text-[13px] text-[#344054] gap-1",children:[(0,l.jsx)(x.eh,{})," Hide"]}),(0,l.jsxs)(D.z,{variant:"outline",className:"h-fit py-[7px] px-[10px] border-[#E6EAEF] font-semibold text-[13px] text-[#344054] gap-1",children:[(0,l.jsx)(x._g,{})," View Files"]}),(0,l.jsx)(D.z,{variant:"outline",className:"h-fit p-[7px] border-[#E6EAEF] font-semibold text-[13px] text-[#344054] gap-1",children:(0,l.jsx)(x.TI,{})})]})]}),(0,l.jsx)("div",{className:"border-t border-[#E6EAEF]"}),(0,l.jsxs)("div",{className:"flex flex-col gap-2 mb-20 px-5",children:[(0,l.jsx)("h4",{className:"text-[15px] text-[#101828] font-bold",children:"Contact Information"}),(0,l.jsxs)("div",{className:"flex justify-between items-center gap-2",children:[(0,l.jsxs)("div",{className:"flex gap-[10px] items-center",children:[(0,l.jsx)(M.Z,{size:20,color:"#475467"}),(0,l.jsx)("span",{className:"text-sm text-[#6868F7]",children:null==a?void 0:a.phone}),(0,l.jsx)("span",{className:"text-sm text-[#667085]",children:"whatsapp only"})]}),(0,l.jsx)(W.r,{textToCopy:null==a?void 0:a.phone,children:(0,l.jsx)(x.TI,{})})]}),(0,l.jsxs)("div",{className:"flex justify-between items-center gap-2",children:[(0,l.jsxs)("div",{className:"flex gap-[10px] items-center",children:[(0,l.jsx)(x.bV,{}),(0,l.jsx)("span",{className:"text-sm text-[#6868F7]",children:null==a?void 0:a.email})]}),(0,l.jsx)(W.r,{textToCopy:null==a?void 0:a.email,tooltipText:"Email copied!",children:(0,l.jsx)(x.TI,{})})]})]})]}),(0,l.jsx)(V.Z,{isOpen:d,onClose:()=>c(!1)})]})})};let X=()=>{let{state:e,dispatch:t}=(0,n.useContext)(o.R);return(0,n.useEffect)(()=>{let a=async()=>{try{let a=await (0,N.Gl)("/subscriptions/plans");if((null==a?void 0:a.status)===200||(null==a?void 0:a.status)===201){var e;t({type:i.a.SUBSCRIPTION_PLANS,payload:null==a?void 0:null===(e=a.data)||void 0===e?void 0:e.data})}}catch(e){console.error("Failed to fetch subscription plans:",e)}};(null==e?void 0:e.subscriptionPlans)||a()},[t,null==e?void 0:e.subscriptionPlans]),{subscriptionPlans:null==e?void 0:e.subscriptionPlans,isLoading:!(null==e?void 0:e.subscriptionPlans)}};function Y(e){let{children:t}=e,[a,d]=(0,n.useState)(!0),r=(0,s.useRouter)(),{state:c,dispatch:u}=(0,n.useContext)(o.R);return(X(),(0,n.useEffect)(()=>{if(!localStorage.getItem("token")){r.push("/auth/login");return}},[r]),(0,n.useEffect)(()=>{let e=localStorage.getItem("token"),t=localStorage.getItem("orgId");u({type:i.a.TOKEN,payload:e}),u({type:i.a.ORG_ID,payload:t}),(async()=>{let e=await (0,N.Gl)("/subscriptions/current/".concat(t));if((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201){var a;u({type:i.a.CURRENT_SUBCRIPTION,payload:null==e?void 0:null===(a=e.data)||void 0===a?void 0:a.data})}})()},[u,r]),(0,n.useEffect)(()=>{(async()=>{let e=await (0,N.Gl)("/profile");if((null==e?void 0:e.status)===200||(null==e?void 0:e.status)===201){var t;u({type:i.a.USER,payload:null==e?void 0:null===(t=e.data)||void 0===t?void 0:t.data})}})()},[u,null==c?void 0:c.profileCallback]),(0,n.useEffect)(()=>{let e=localStorage.getItem("orgId"),t=async()=>{let t=await (0,N.Gl)("/organisations/".concat(e,"/invites"));if((null==t?void 0:t.status)===200||(null==t?void 0:t.status)===201){let e=t.data.data.filter(e=>"invited"===e.status);u({type:i.a.ORG_INVITES,payload:e})}};(async()=>{let t=await (0,N.Gl)("/organisations/".concat(e,"/users?page=1&limit=50"));if((null==t?void 0:t.status)===200||(null==t?void 0:t.status)===201){var a;u({type:i.a.ORG_MEMBERS,payload:null==t?void 0:null===(a=t.data)||void 0===a?void 0:a.data})}d(!1)})(),t()},[u]),a)?null:(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(B,{}),(0,l.jsx)(p,{}),(0,l.jsxs)("div",{className:"w-full flex relative",children:[(0,l.jsx)(U,{}),(0,l.jsxs)("div",{className:"w-full relative",children:[t,(0,l.jsx)(K,{})]})]})]})}},25368:function(e,t,a){"use strict";a.d(t,{$F:function(){return c},AW:function(){return x},Ju:function(){return f},KM:function(){return h},VD:function(){return v},Xi:function(){return p},_x:function(){return u},h_:function(){return r},qB:function(){return m}});var l=a(75376),s=a(32486),n=a(18493),i=a(51888),o=a(28780),d=a(58983);let r=n.fC,c=n.xz;n.ZA,n.Uv,n.Tr;let u=n.Ee;s.forwardRef((e,t)=>{let{className:a,inset:s,children:o,...r}=e;return(0,l.jsxs)(n.fF,{ref:t,className:(0,d.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",s&&"pl-8",a),...r,children:[o,(0,l.jsx)(i.Z,{className:"ml-auto h-4 w-4"})]})}).displayName=n.fF.displayName,s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,l.jsx)(n.tu,{ref:t,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...s})}).displayName=n.tu.displayName;let x=s.forwardRef((e,t)=>{let{className:a,sideOffset:s=4,...i}=e;return(0,l.jsx)(n.Uv,{children:(0,l.jsx)(n.VY,{ref:t,sideOffset:s,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...i})})});x.displayName=n.VY.displayName;let p=s.forwardRef((e,t)=>{let{className:a,inset:s,...i}=e;return(0,l.jsx)(n.ck,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s&&"pl-8",a),...i})});p.displayName=n.ck.displayName,s.forwardRef((e,t)=>{let{className:a,children:s,checked:i,...r}=e;return(0,l.jsxs)(n.oC,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:i,...r,children:[(0,l.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,l.jsx)(n.wU,{children:(0,l.jsx)(o.Z,{className:"h-4 w-4"})})}),s]})}).displayName=n.oC.displayName;let m=s.forwardRef((e,t)=>{let{className:a,children:s,...i}=e;return(0,l.jsxs)(n.Rk,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...i,children:[(0,l.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,l.jsx)(n.wU,{children:(0,l.jsx)("div",{className:"bg-gradient-to-b from-[#8860F8] to-[#7141F8] w-5 h-5 rounded-sm flex items-center justify-center",children:(0,l.jsx)(o.Z,{color:"#ffffff"})})})}),s]})});m.displayName=n.Rk.displayName;let f=s.forwardRef((e,t)=>{let{className:a,inset:s,...i}=e;return(0,l.jsx)(n.__,{ref:t,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",s&&"pl-8",a),...i})});f.displayName=n.__.displayName;let v=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,l.jsx)(n.Z0,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",a),...s})});v.displayName=n.Z0.displayName;let h=e=>{let{className:t,...a}=e;return(0,l.jsx)("span",{className:(0,d.cn)("ml-auto text-xs tracking-widest opacity-60",t),...a})};h.displayName="DropdownMenuShortcut"},74178:function(e,t,a){"use strict";a.d(t,{J2:function(){return o},xo:function(){return d},yk:function(){return r}});var l=a(75376),s=a(32486),n=a(80500),i=a(58983);let o=n.fC,d=n.xz,r=s.forwardRef((e,t)=>{let{className:a,align:s="center",sideOffset:o=4,...d}=e;return(0,l.jsx)(n.h_,{children:(0,l.jsx)(n.VY,{ref:t,align:s,sideOffset:o,className:(0,i.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...d})})});r.displayName=n.VY.displayName},46962:function(e,t,a){"use strict";a.d(t,{_v:function(){return c},aJ:function(){return r},pn:function(){return o},u:function(){return d}});var l=a(75376),s=a(32486),n=a(82288),i=a(58983);let o=n.zt,d=n.fC,r=n.xz,c=s.forwardRef((e,t)=>{let{className:a,sideOffset:s=4,...o}=e;return(0,l.jsx)(n.VY,{ref:t,sideOffset:s,className:(0,i.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]",a),...o})});c.displayName=n.VY.displayName},26242:function(e,t,a){"use strict";a.d(t,{BY:function(){return n},CZ:function(){return l},Qm:function(){return s},ju:function(){return i},mu:function(){return d},z2:function(){return o}}),a(32486),a(13352);let l=e=>e<1e3?e.toString():e<1e6?"".concat(Math.floor(e/1e3),"k+"):"".concat(Math.floor(e/1e6),"m+"),s=e=>null==e?void 0:e.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2),n=e=>e.sort((e,t)=>e.name.toLowerCase()<t.name.toLowerCase()?-1:e.name.toLowerCase()>t.name.toLowerCase()?1:0);function i(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=document.createElement("div");return t.innerHTML=e,t.textContent||t.innerText||""}function o(e){return"active"===e?"text-green-600 bg-green-50 border-green-200":"text-gray-600 bg-gray-50 border-gray-200"}function d(e,t){e(!0),t&&t(),setTimeout(()=>e(!1),1e3)}}},function(e){e.O(0,[4269,6029,4362,8863,7140,6092,4144,5300,5614,2987,6343,6329,9254,8659,3975,2616,7220,937,6539,9513,822,7542,2344,1744],function(){return e(e.s=54137)}),_N_E=e.O()}]);