(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8496],{87801:function(e,r,t){Promise.resolve().then(t.bind(t,80451))},39713:function(e,r,t){"use strict";t.d(r,{default:function(){return i.a}});var n=t(74033),i=t.n(n)},47411:function(e,r,t){"use strict";var n=t(13362);t.o(n,"useParams")&&t.d(r,{useParams:function(){return n.useParams}}),t.o(n,"usePathname")&&t.d(r,{usePathname:function(){return n.usePathname}}),t.o(n,"useRouter")&&t.d(r,{useRouter:function(){return n.useRouter}}),t.o(n,"useSearchParams")&&t.d(r,{useSearchParams:function(){return n.useSearchParams}})},74033:function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{default:function(){return a},getImageProps:function(){return u}});let n=t(60723),i=t(25738),o=t(28863),s=n._(t(44543));function u(e){let{props:r}=(0,i.getImgProps)(e,{defaultLoader:s.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,t]of Object.entries(r))void 0===t&&delete r[e];return{props:r}}let a=o.Image},80451:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return a}});var n=t(75376),i=t(32486),o=t(97220),s=t(20945),u=t(47411);function a(e){let{children:r}=e,[t,a]=(0,i.useState)(!0),l=(0,u.useRouter)();if((0,i.useEffect)(()=>{if(!localStorage.getItem("token")){l.push("/auth/login");return}a(!1)},[l]),!t)return(0,n.jsx)(o.DataProvider,{children:(0,n.jsx)(s.Z,{children:(0,n.jsx)("div",{className:"w-full relative",children:r})})})}},20945:function(e,r,t){"use strict";var n=t(75376),i=t(39713),o=t(32486),s=t(11492);class u extends o.Component{static getDerivedStateFromError(e){return{hasError:!0}}componentDidCatch(e,r){console.error("ErrorBoundary caught an error",e,r)}resetError(){this.setState({hasError:!1}),window.location.href="/client"}render(){return this.state.hasError?(0,n.jsx)("div",{children:(0,n.jsxs)("div",{className:"max-w-full h-screen flex flex-col items-center justify-center gap-5",children:[(0,n.jsx)(i.default,{src:"/images/error-img.svg",height:"100",width:"400",alt:"error image",className:"w-60 h-40"}),(0,n.jsx)("p",{className:"text-xl font-semibold leading-6 text-slate-600",children:"Ooops!! Something went wrong"}),(0,n.jsx)("p",{className:"text-neutral-700 text-center leading-6",children:"We cannot load this page at the moment"}),(0,n.jsx)(s.z,{onClick:this.resetError,className:"bg-gradient-to-b from-[#8760f8] to-[#7141f8] py-2 px-6 text-white",children:"Go Back to Dashboard"})]})}):this.props.children}constructor(e){super(e),this.state={hasError:!1},this.resetError=this.resetError.bind(this)}}r.Z=u},11492:function(e,r,t){"use strict";t.d(r,{d:function(){return a},z:function(){return l}});var n=t(75376),i=t(32486),o=t(91007),s=t(53447),u=t(58983);let a=(0,s.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=i.forwardRef((e,r)=>{let{className:t,variant:i,size:s,asChild:l=!1,...c}=e,d=l?o.g7:"button";return(0,n.jsx)(d,{className:(0,u.cn)(a({variant:i,size:s,className:t})),ref:r,...c})});l.displayName="Button"},58983:function(e,r,t){"use strict";t.d(r,{cn:function(){return o},k:function(){return s}});var n=t(89824),i=t(97215);function o(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,i.m6)((0,n.W)(r))}let s=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"},29626:function(e,r,t){"use strict";t.d(r,{F:function(){return o},e:function(){return s}});var n=t(32486);function i(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function o(...e){return r=>{let t=!1,n=e.map(e=>{let n=i(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():i(e[r],null)}}}}function s(...e){return n.useCallback(o(...e),e)}},91007:function(e,r,t){"use strict";t.d(r,{Z8:function(){return s},g7:function(){return u},sA:function(){return l}});var n=t(32486),i=t(29626),o=t(75376);function s(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...o}=e;if(n.isValidElement(t)){let e,s;let u=(e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.ref:(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.props.ref:t.props.ref||t.ref,a=function(e,r){let t={...r};for(let n in r){let i=e[n],o=r[n];/^on[A-Z]/.test(n)?i&&o?t[n]=(...e)=>{let r=o(...e);return i(...e),r}:i&&(t[n]=i):"style"===n?t[n]={...i,...o}:"className"===n&&(t[n]=[i,o].filter(Boolean).join(" "))}return{...e,...t}}(o,t.props);return t.type!==n.Fragment&&(a.ref=r?(0,i.F)(r,u):u),n.cloneElement(t,a)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:i,...s}=e,u=n.Children.toArray(i),a=u.find(c);if(a){let e=a.props.children,i=u.map(r=>r!==a?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(r,{...s,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,o.jsx)(r,{...s,ref:t,children:i})});return t.displayName=`${e}.Slot`,t}var u=s("Slot"),a=Symbol("radix.slottable");function l(e){let r=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=a,r}function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}},53447:function(e,r,t){"use strict";t.d(r,{j:function(){return s}});var n=t(89824);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=n.W,s=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return o(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:s,defaultVariants:u}=r,a=Object.keys(s).map(e=>{let r=null==t?void 0:t[e],n=null==u?void 0:u[e];if(null===r)return null;let o=i(r)||i(n);return s[e][o]}),l=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return o(e,a,null==r?void 0:null===(n=r.compoundVariants)||void 0===n?void 0:n.reduce((e,r)=>{let{class:t,className:n,...i}=r;return Object.entries(i).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...u,...l}[r]):({...u,...l})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}}},function(e){e.O(0,[8863,7140,7220,7542,2344,1744],function(){return e(e.s=87801)}),_N_E=e.O()}]);