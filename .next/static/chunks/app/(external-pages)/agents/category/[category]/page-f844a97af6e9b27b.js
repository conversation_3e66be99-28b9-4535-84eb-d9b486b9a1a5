(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8697],{65890:function(e,t,a){Promise.resolve().then(a.bind(a,21144))},21144:function(e,t,a){"use strict";a.d(t,{default:function(){return N}});var n=a(75376),s=a(32486),l=a(12160),r=a(21789),o=a(42296),i=a(32541),c=a(42752),d=a(63539),m=a(39713),g=a(72589),h=a(97220),u=a(97712),x=a(46031);let p=e=>e.map(e=>({id:e.id,section:e.category||"General",title:e.app_name,excerpt:e.app_description,bot:e.app_logo||"/images/bot-gray.svg",color:f(e.category)})),f=e=>({"Application Performance Monitoring":"#1D5A69","Cloud Monitoring":"#344054","Website Testing":"#700902","Webhook Testing":"#2E8DFF","Social Media":"#009143",Agent:"#7141F8","Customer Support":"#10B981","Sales and Marketing":"#F59E0B","Engineering DevOps":"#3B82F6","Documents and Analysis":"#8B5CF6","Strategy Business":"#EF4444","Content and Video":"#EC4899","":"#344054"})[e]||"#344054";function N(e){let{categoryName:t}=e,{state:a,dispatch:f}=(0,s.useContext)(h.R),[N,A]=(0,s.useState)(""),[y,j]=(0,s.useState)(t),[C,E]=(0,s.useState)([]),[b,v]=(0,s.useState)(!1),[w,S]=(0,s.useState)([]),[k,P]=(0,s.useState)(!0),[_,F]=(0,s.useState)({firstName:"",lastName:"",email:"",monitoringNeed:""}),T=_.firstName&&_.lastName&&_.email&&_.monitoringNeed,M=e=>{F(t=>({...t,[e.target.name]:e.target.value}))},D=e=>{e.preventDefault(),A("")},L=()=>{let e=[...w];e="All"===y?e.filter(e=>e.section===t):e.filter(e=>e.section===y),""!==N&&(e=e.filter(e=>e.title.toLowerCase().includes(N.toLowerCase()))),E(e)},R=async()=>{try{P(!0),console.log("Refreshing agents cache");let e=await (0,g.S)(),t=Date.now();f({type:u.a.MARKETPLACE_AGENTS,payload:e}),f({type:u.a.MARKETPLACE_AGENTS_CACHE_TIME,payload:t});let a=p(e);S(a)}catch(e){console.error("Error refreshing agents cache:",e)}finally{P(!1)}};return(0,s.useEffect)(()=>{(async()=>{try{P(!0);let e=Date.now(),t=a.marketPlaceAgentsCacheTime,n=t&&e-t<3e5;if(a.marketPlaceAgents&&a.marketPlaceAgents.length>0&&n){console.log("Loading agents from cache",{agentsCount:a.marketPlaceAgents.length,cacheAge:Math.round((e-t)/1e3)+"s"});let n=p(a.marketPlaceAgents);S(n),P(!1);return}let s=a.marketPlaceAgents&&0!==a.marketPlaceAgents.length?"cache expired":"no cache";console.log("Fetching agents from API (".concat(s,")"));let l=await (0,g.S)();f({type:u.a.MARKETPLACE_AGENTS,payload:l}),f({type:u.a.MARKETPLACE_AGENTS_CACHE_TIME,payload:e});let r=p(l);S(r)}catch(e){console.error("Error loading agents:",e),S([])}finally{P(!1)}})()},[a.marketPlaceAgents,a.marketPlaceAgentsCacheTime,f]),(0,s.useEffect)(()=>{L()},[y,N,w]),(0,s.useEffect)(()=>{},[a.marketPlaceAgents,a.marketPlaceAgentsCacheTime,f,R]),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(l.Z,{agentsSearch:N,handleAgentsSearch:D,setAgentsSearch:A}),(0,n.jsxs)("div",{className:"space-y-5",children:[(0,n.jsx)(r.Z,{agentsSearch:N,handleAgentsSearch:D,setAgentsSearch:A,agentsCount:C.length}),(0,n.jsx)(o.Z,{setAgentsCategory:j,categories:x.u})]}),k?(0,n.jsx)(c.Z,{}):(0,n.jsx)(i.Z,{agentsDisplayed:C,setOpenDialog:v}),(0,n.jsx)(d.Z,{}),b&&(0,n.jsx)("div",{className:"fixed top-0 right-0 left-0 bottom-0 flex items-center backdrop-blur-sm justify-center max-h-screen z-50",children:(0,n.jsx)("div",{className:"w-[90%] md:w-[60%] md:h-[70%]",children:(0,n.jsxs)("div",{className:"flex border border-blue-800 rounded-lg w-full h-full bg-white ",children:[(0,n.jsx)("div",{className:"rounded-lg hidden md:flex w-2/5",children:(0,n.jsx)(m.default,{src:"/images/agents-request-image.svg",height:607,width:422,className:"rounded-lg object-cover w-full h-full",alt:"Request Agent Image"})}),(0,n.jsx)("div",{className:"rounded-lg w-full md:w-3/5",children:(0,n.jsxs)("form",{action:"",className:"p-5 space-y-6",children:[(0,n.jsxs)("div",{className:"",children:[(0,n.jsx)("h2",{className:"font-bold text-xl",children:"Request an Agent"}),(0,n.jsx)("p",{className:"text-sm text-[#475467]",children:"Can't find an agent you need? Feel free to shoot us a\n                      request and we will try to bring them on."})]}),(0,n.jsxs)("div",{className:"flex gap-4",children:[(0,n.jsxs)("div",{className:"flex flex-col text-sm w-1/2 gap-2",children:[(0,n.jsx)("label",{htmlFor:"firstName",children:"First Name"}),(0,n.jsx)("input",{type:"text",placeholder:"John",className:"text-[#475467] border outline-none rounded-md py-3 px-4 w-full",onChange:M,value:_.firstName,name:"firstName"})]}),(0,n.jsxs)("div",{className:"flex flex-col text-sm w-1/2 gap-2",children:[(0,n.jsx)("label",{htmlFor:"lastName",children:"Last Name"}),(0,n.jsx)("input",{type:"text",placeholder:"Doe",className:"text-[#475467] border outline-none rounded-md py-3 px-4 w-full",onChange:M,value:_.lastName,name:"lastName"})]})]}),(0,n.jsxs)("div",{className:"flex flex-col text-sm gap-2",children:[(0,n.jsx)("label",{htmlFor:"email",children:"Email"}),(0,n.jsx)("input",{type:"email",placeholder:"<EMAIL>",className:"text-[#475467] border outline-none rounded-md py-3 px-4",onChange:M,value:_.email,name:"email"})]}),(0,n.jsxs)("div",{className:"flex flex-col text-sm gap-2",children:[(0,n.jsx)("label",{htmlFor:"monitoringNeed",children:"Monitoring Need"}),(0,n.jsx)("input",{type:"text",placeholder:"E.g. I need an agent to monitor API response times",maxLength:80,max:80,className:" border outline-none rounded-md py-3 px-4",onChange:M,value:_.monitoringNeed,name:"monitoringNeed"})]}),(0,n.jsxs)("div",{className:"flex items-center gap-4 justify-end text-sm",children:[(0,n.jsx)("button",{onClick:()=>v(!1),className:"border px-4 py-3 rounded-md",children:"Cancel"}),(0,n.jsx)("button",{disabled:!T,className:"px-4 py-3 border rounded-md ".concat(T?"opacity-100":"opacity-60 cursor-not-allowed"),children:"Send Request"})]})]})})]})})})]})}}},function(e){e.O(0,[8863,6092,7220,8071,7542,2344,1744],function(){return e(e.s=65890)}),_N_E=e.O()}]);