(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2707],{26236:function(e,t,a){Promise.resolve().then(a.bind(a,11511))},11511:function(e,t,a){"use strict";a.d(t,{default:function(){return N}});var s=a(75376),n=a(32486),l=a(12160),r=a(21789),o=a(42296),c=a(32541),i=a(42752),d=a(63539),m=a(39713),g=a(72589),h=a(97220),u=a(97712),x=a(46031);let p=e=>e.map(e=>({id:e.id,section:e.category||"General",title:e.app_name,excerpt:e.app_description,bot:e.app_logo||"/images/bot-gray.svg",color:f(e.category)})),f=e=>({"Customer Support":"#10B981","Sales and Marketing":"#F59E0B","Engineering DevOps":"#3B82F6","Documents and Analysis":"#8B5CF6","Strategy Business":"#EF4444","Content and Video":"#EC4899","":"#344054"})[e]||"#344054";function N(){let{state:e,dispatch:t}=(0,n.useContext)(h.R),[a,f]=(0,n.useState)(""),[N,y]=(0,n.useState)("All"),[j,A]=(0,n.useState)([]),[C,E]=(0,n.useState)(!1),[b,v]=(0,n.useState)([]),[w,S]=(0,n.useState)(!0),[k,P]=(0,n.useState)({firstName:"",lastName:"",email:"",monitoringNeed:""}),_=k.firstName&&k.lastName&&k.email&&k.monitoringNeed,T=e=>{P(t=>({...t,[e.target.name]:e.target.value}))},F=e=>{e.preventDefault(),f("")},L=()=>{let e=[...b];"All"!==N&&(e=e.filter(e=>e.section===N)),""!==a&&(e=e.filter(e=>e.title.toLowerCase().includes(a.toLowerCase()))),A(e)},M=async()=>{try{S(!0),console.log("Refreshing agents cache");let e=await (0,g.S)(),a=Date.now();t({type:u.a.MARKETPLACE_AGENTS,payload:e}),t({type:u.a.MARKETPLACE_AGENTS_CACHE_TIME,payload:a});let s=p(e);v(s),A(s)}catch(e){console.error("Error refreshing agents cache:",e)}finally{S(!1)}};return(0,n.useEffect)(()=>{(async()=>{try{S(!0);let a=Date.now(),s=e.marketPlaceAgentsCacheTime,n=s&&a-s<3e5;if(e.marketPlaceAgents&&e.marketPlaceAgents.length>0&&n){console.log("Loading agents from cache",{agentsCount:e.marketPlaceAgents.length,cacheAge:Math.round((a-s)/1e3)+"s"});let t=p(e.marketPlaceAgents);v(t),A(t),S(!1);return}let l=e.marketPlaceAgents&&0!==e.marketPlaceAgents.length?"cache expired":"no cache";console.log("Fetching agents from API (".concat(l,")"));let r=await (0,g.S)();t({type:u.a.MARKETPLACE_AGENTS,payload:r}),t({type:u.a.MARKETPLACE_AGENTS_CACHE_TIME,payload:a});let o=p(r);v(o),A(o)}catch(e){console.error("Error loading agents:",e),v([]),A([])}finally{S(!1)}})()},[e.marketPlaceAgents,e.marketPlaceAgentsCacheTime,t]),(0,n.useEffect)(()=>{L()},[N,a,b]),(0,n.useEffect)(()=>{},[e.marketPlaceAgents,e.marketPlaceAgentsCacheTime,t,M]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(l.Z,{agentsSearch:a,handleAgentsSearch:F,setAgentsSearch:f}),(0,s.jsxs)("div",{className:"space-y-5",children:[(0,s.jsx)(r.Z,{agentsSearch:a,handleAgentsSearch:F,setAgentsSearch:f,agentsCount:b.length}),(0,s.jsx)(o.Z,{setAgentsCategory:y,categories:x.u})]}),w?(0,s.jsx)(i.Z,{}):(0,s.jsx)(c.Z,{agentsDisplayed:j,setOpenDialog:E}),(0,s.jsx)(d.Z,{}),C&&(0,s.jsx)("div",{className:"fixed top-0 right-0 left-0 bottom-0 flex items-center backdrop-blur-sm justify-center max-h-screen z-50",children:(0,s.jsx)("div",{className:"w-[90%] md:w-[60%] md:h-[70%]",children:(0,s.jsxs)("div",{className:"flex border border-blue-800 rounded-lg w-full h-full bg-white ",children:[(0,s.jsx)("div",{className:"rounded-lg hidden md:flex w-2/5",children:(0,s.jsx)(m.default,{src:"/images/agents-request-image.svg",height:607,width:422,className:"rounded-lg object-cover w-full h-full",alt:"Request Agent Image"})}),(0,s.jsx)("div",{className:"rounded-lg w-full md:w-3/5",children:(0,s.jsxs)("form",{action:"",className:"p-5 space-y-6",children:[(0,s.jsxs)("div",{className:"",children:[(0,s.jsx)("h2",{className:"font-bold text-xl",children:"Request an Agent"}),(0,s.jsx)("p",{className:"text-sm text-[#475467]",children:"Can't find an agent you need? Feel free to shoot us a\n                      request and we will try to bring them on."})]}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsxs)("div",{className:"flex flex-col text-sm w-1/2 gap-2",children:[(0,s.jsx)("label",{htmlFor:"firstName",children:"First Name"}),(0,s.jsx)("input",{type:"text",placeholder:"John",className:"text-[#475467] border outline-none rounded-md py-3 px-4 w-full",onChange:T,value:k.firstName,name:"firstName"})]}),(0,s.jsxs)("div",{className:"flex flex-col text-sm w-1/2 gap-2",children:[(0,s.jsx)("label",{htmlFor:"lastName",children:"Last Name"}),(0,s.jsx)("input",{type:"text",placeholder:"Doe",className:"text-[#475467] border outline-none rounded-md py-3 px-4 w-full",onChange:T,value:k.lastName,name:"lastName"})]})]}),(0,s.jsxs)("div",{className:"flex flex-col text-sm gap-2",children:[(0,s.jsx)("label",{htmlFor:"email",children:"Email"}),(0,s.jsx)("input",{type:"email",placeholder:"<EMAIL>",className:"text-[#475467] border outline-none rounded-md py-3 px-4",onChange:T,value:k.email,name:"email"})]}),(0,s.jsxs)("div",{className:"flex flex-col text-sm gap-2",children:[(0,s.jsx)("label",{htmlFor:"monitoringNeed",children:"Monitoring Need"}),(0,s.jsx)("input",{type:"text",placeholder:"E.g. I need an agent to monitor API response times",maxLength:80,max:80,className:" border outline-none rounded-md py-3 px-4",onChange:T,value:k.monitoringNeed,name:"monitoringNeed"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-4 justify-end text-sm",children:[(0,s.jsx)("button",{onClick:()=>E(!1),className:"border px-4 py-3 rounded-md",children:"Cancel"}),(0,s.jsx)("button",{disabled:!_,className:"px-4 py-3 border rounded-md ".concat(_?"opacity-100":"opacity-60 cursor-not-allowed"),children:"Send Request"})]})]})})]})})})]})}}},function(e){e.O(0,[8863,6092,7220,8071,7542,2344,1744],function(){return e(e.s=26236)}),_N_E=e.O()}]);