(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8720],{30702:function(e,t,a){Promise.resolve().then(a.bind(a,61214))},9824:function(e,t,a){"use strict";a.d(t,{Z:function(){return o}});var n=a(32486);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&a.indexOf(e)===t).join(" ")};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,n.forwardRef)((e,t)=>{let{color:a="currentColor",size:r=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:d="",children:c,iconNode:u,...m}=e;return(0,n.createElement)("svg",{ref:t,...l,width:r,height:r,stroke:a,strokeWidth:o?24*Number(i)/Number(r):i,className:s("lucide",d),...m},[...u.map(e=>{let[t,a]=e;return(0,n.createElement)(t,a)}),...Array.isArray(c)?c:[c]])}),o=(e,t)=>{let a=(0,n.forwardRef)((a,l)=>{let{className:o,...d}=a;return(0,n.createElement)(i,{ref:l,iconNode:t,className:s("lucide-".concat(r(e)),o),...d})});return a.displayName="".concat(e),a}},53532:function(e,t,a){"use strict";a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(9824).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},53189:function(e,t,a){"use strict";a.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,a(9824).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},39713:function(e,t,a){"use strict";a.d(t,{default:function(){return r.a}});var n=a(74033),r=a.n(n)},16669:function(e,t,a){"use strict";a.d(t,{default:function(){return r.a}});var n=a(6092),r=a.n(n)},47411:function(e,t,a){"use strict";var n=a(13362);a.o(n,"useParams")&&a.d(t,{useParams:function(){return n.useParams}}),a.o(n,"usePathname")&&a.d(t,{usePathname:function(){return n.usePathname}}),a.o(n,"useRouter")&&a.d(t,{useRouter:function(){return n.useRouter}}),a.o(n,"useSearchParams")&&a.d(t,{useSearchParams:function(){return n.useSearchParams}})},74033:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return o},getImageProps:function(){return i}});let n=a(60723),r=a(25738),s=a(28863),l=n._(a(44543));function i(e){let{props:t}=(0,r.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,a]of Object.entries(t))void 0===a&&delete t[e];return{props:t}}let o=s.Image},61214:function(e,t,a){"use strict";a.d(t,{default:function(){return h}});var n=a(75376),r=a(32486);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(9824).Z)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);var l=e=>{let{agent:t}=e;return(0,n.jsxs)("div",{className:"space-y-8",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,n.jsx)(s,{className:"text-red-500",size:20}),(0,n.jsxs)("h2",{className:"text-xl font-semibold text-gray-900",children:["What is ",t.app_name,"?"]})]}),(0,n.jsx)("div",{className:"prose max-w-none",children:(0,n.jsx)("p",{className:"text-gray-700 mb-4",children:t.app_description})})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-6",children:["What data does ",t.app_name," extract?"]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{className:"flex items-start gap-3 p-4 bg-gray-50 rounded-lg",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"}),(0,n.jsx)("div",{children:(0,n.jsx)("div",{className:"font-medium text-gray-900",children:"Title/place name"})})]}),(0,n.jsxs)("div",{className:"flex items-start gap-3 p-4 bg-gray-50 rounded-lg",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-orange-400 rounded-full mt-2 flex-shrink-0"}),(0,n.jsx)("div",{children:(0,n.jsx)("div",{className:"font-medium text-gray-900",children:"Subtitle, category, place ID, and URL"})})]}),(0,n.jsxs)("div",{className:"flex items-start gap-3 p-4 bg-gray-50 rounded-lg",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0"}),(0,n.jsx)("div",{children:(0,n.jsx)("div",{className:"font-medium text-gray-900",children:"Address"})})]}),(0,n.jsxs)("div",{className:"flex items-start gap-3 p-4 bg-gray-50 rounded-lg",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"}),(0,n.jsx)("div",{children:(0,n.jsx)("div",{className:"font-medium text-gray-900",children:"Location, plus code and exact coordinates"})})]}),(0,n.jsxs)("div",{className:"flex items-start gap-3 p-4 bg-gray-50 rounded-lg",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"}),(0,n.jsx)("div",{children:(0,n.jsx)("div",{className:"font-medium text-gray-900",children:"Phone number"})})]}),(0,n.jsxs)("div",{className:"flex items-start gap-3 p-4 bg-gray-50 rounded-lg",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"}),(0,n.jsx)("div",{children:(0,n.jsx)("div",{className:"font-medium text-gray-900",children:"Website, if available"})})]}),(0,n.jsxs)("div",{className:"flex items-start gap-3 p-4 bg-gray-50 rounded-lg",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-pink-400 rounded-full mt-2 flex-shrink-0"}),(0,n.jsx)("div",{children:(0,n.jsx)("div",{className:"font-medium text-gray-900",children:"Company contact details from website (company email, phone number and social media profiles)"})})]}),(0,n.jsxs)("div",{className:"flex items-start gap-3 p-4 bg-gray-50 rounded-lg",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-red-600 rounded-full mt-2 flex-shrink-0"}),(0,n.jsx)("div",{children:(0,n.jsx)("div",{className:"font-medium text-gray-900",children:"Business leads enrichment (full name, work email address, phone number, job title, LinkedIn profile)"})})]}),(0,n.jsxs)("div",{className:"flex items-start gap-3 p-4 bg-gray-50 rounded-lg",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-blue-300 rounded-full mt-2 flex-shrink-0"}),(0,n.jsx)("div",{children:(0,n.jsx)("div",{className:"font-medium text-gray-900",children:"Search results"})})]}),(0,n.jsxs)("div",{className:"flex items-start gap-3 p-4 bg-gray-50 rounded-lg",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-gray-600 rounded-full mt-2 flex-shrink-0"}),(0,n.jsx)("div",{children:(0,n.jsx)("div",{className:"font-medium text-gray-900",children:"Review count and review distribution"})})]}),(0,n.jsxs)("div",{className:"flex items-start gap-3 p-4 bg-gray-50 rounded-lg",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"}),(0,n.jsx)("div",{children:(0,n.jsx)("div",{className:"font-medium text-gray-900",children:"Average rating (totalScore)"})})]}),(0,n.jsxs)("div",{className:"flex items-start gap-3 p-4 bg-gray-50 rounded-lg",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-gray-700 rounded-full mt-2 flex-shrink-0"}),(0,n.jsx)("div",{children:(0,n.jsx)("div",{className:"font-medium text-gray-900",children:"List of images"})})]})]})]})]})},i=a(39713),o=a(16669),d=a(53532),c=a(53189),u=a(47411),m=a(86418),g=e=>{var t;let{agent:a}=e,s=(0,u.useRouter)(),[l,g]=r.useState(!1),x=async()=>{g(!0);try{if(!localStorage.getItem("token")){let e="/client/agents/browse-agents/".concat(a.id,"?json_url=").concat(encodeURIComponent(a.json_url)),t="/auth/login?redirect=".concat(encodeURIComponent(e));s.push(t);return}let l=localStorage.getItem("orgId")||"",i={chat_type:"bot",participant_id:a.id};localStorage.setItem("channelName",a.app_name);let o=await (0,m.xo)("/organisations/".concat(l,"/dms"),i);if((null==o?void 0:o.status)===200||(null==o?void 0:o.status)===201){var e,t,n,r;s.push("/client/home/<USER>/".concat(null==o?void 0:null===(t=o.data)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.channel_id,"/").concat(null==o?void 0:null===(r=o.data)||void 0===r?void 0:null===(n=r.data)||void 0===n?void 0:n.participant_id))}else s.push("/client/agents/browse-agents/".concat(a.id,"?json_url=").concat(encodeURIComponent(a.json_url)))}catch(e){console.error("Error navigating to agent:",e),s.push("/client/agents/browse-agents/".concat(a.id,"?json_url=").concat(encodeURIComponent(a.json_url)))}finally{g(!1)}};return(0,n.jsxs)("div",{className:"mt-10",children:[(0,n.jsx)("div",{className:"flex items-center gap-2 text-sm text-gray-600 mb-6",children:(0,n.jsxs)(o.default,{href:"/agents",className:"flex items-center gap-1 hover:text-gray-900 transition-colors",children:[(0,n.jsx)(d.Z,{size:16}),"Go to Store"]})}),(0,n.jsxs)("div",{className:"flex items-start justify-between",children:[(0,n.jsxs)("div",{className:"flex items-start gap-4",children:[(0,n.jsx)("div",{className:"w-16 h-16 rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center",children:a.app_logo?(0,n.jsx)(i.default,{src:a.app_logo,alt:a.app_name,width:64,height:64,className:"w-full h-full object-cover"}):(0,n.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center",children:(0,n.jsx)("span",{className:"text-white font-semibold text-sm",children:a.app_name.charAt(0).toUpperCase()})})}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:a.app_name}),(0,n.jsx)("div",{className:"flex items-center gap-4 text-sm text-gray-600 mb-3",children:(0,n.jsxs)("span",{children:["Developed by"," ",(0,n.jsx)("span",{className:"font-medium text-gray-900",children:(null===(t=a.provider)||void 0===t?void 0:t.organization)||"Telex"})]})}),(0,n.jsx)("p",{className:"text-gray-700 mb-4 max-w-2xl",children:a.app_description}),(0,n.jsxs)("div",{className:"flex items-center gap-6 text-sm",children:[(0,n.jsxs)("div",{className:"flex items-center gap-1",children:[(0,n.jsx)("div",{className:"flex",children:[1,2,3,4,5].map(e=>(0,n.jsx)(c.Z,{size:16,className:"text-yellow-400 fill-current"},e))}),(0,n.jsx)("span",{className:"font-medium",children:"5.0 (93)"})]}),(0,n.jsxs)("div",{className:"flex items-center gap-1",children:[(0,n.jsx)("span",{className:"text-gray-600",children:"Pricing:"}),(0,n.jsx)("span",{className:"font-medium",children:"Pay per event"})]}),(0,n.jsxs)("div",{className:"text-xs text-gray-500",children:["Created at"," ",new Date(a.created_at).toLocaleString("en-US",{dateStyle:"medium",timeStyle:"short"})]})]})]})]}),(0,n.jsx)("div",{className:"flex items-center gap-3",children:(0,n.jsx)("button",{onClick:x,disabled:l,className:"bg-primary-500 text-xs md:text-sm text-white font-semibold py-3 px-6 rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition duration-300 ease-in-out mb-3",children:(0,n.jsx)("span",{className:"text-sm text-white cursor-pointer hover:underline",children:l?"Loading...":"Use this Agent in Telex"})})})]})]})},x=e=>{let{tabs:t,activeTab:a,onTabChange:r}=e;return(0,n.jsx)("div",{className:"border-b border-gray-200",children:(0,n.jsx)("nav",{className:"flex space-x-8",children:t.map(e=>(0,n.jsx)("button",{onClick:()=>r(e),className:"py-3 px-1 border-b-2 font-medium text-sm transition-colors ".concat(a===e?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:e},e))})})},h=e=>{let{agent:t}=e;console.log("agent",t);let[a,s]=(0,r.useState)("Description");return(0,n.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-0 py-8",children:[(0,n.jsx)(g,{agent:t}),(0,n.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8 mt-8",children:[(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)(x,{tabs:["Description","Pricing","API"],activeTab:a,onTabChange:s}),(0,n.jsx)("div",{className:"mt-6",children:(()=>{switch(a){case"Description":default:return(0,n.jsx)(l,{agent:t});case"Pricing":return(0,n.jsx)("div",{className:"p-6",children:"Pricing information coming soon..."});case"API":return(0,n.jsx)("div",{className:"p-6",children:"API documentation coming soon..."})}})()})]}),(0,n.jsx)("div",{className:"lg:w-80"})]})]})}},86418:function(e,t,a){"use strict";a.d(t,{Gl:function(){return l},HH:function(){return o},Q_:function(){return d},_x:function(){return u},an:function(){return m},i1:function(){return x},jx:function(){return g},x9:function(){return c},xo:function(){return i}});var n=a(20818),r=a(13352);let s=a(18648).env.NEXT_PUBLIC_BASE_URL,l=async e=>{let t=localStorage.getItem("token")||"";try{return await n.Z.get(s+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var a,r,l;return((null==e?void 0:null===(a=e.response)||void 0===a?void 0:a.status)===401||(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(r=l.data)||void 0===r?void 0:r.status_code)===401)&&(localStorage.clear(),window.location.href="/auth/login"),e}},i=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await n.Z.post(s+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var l,i,o,d,c;return r.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(l=i.data)||void 0===l?void 0:l.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},o=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await n.Z.post(s+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var l,i;return r.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(l=i.data)||void 0===l?void 0:l.message),e}},d=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await n.Z.post(s+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){return e}},c=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await n.Z.post(s+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"multipart/form-data"}})}catch(e){var l,i,o,d,c;return r.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(l=i.data)||void 0===l?void 0:l.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},u=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await n.Z.patch(s+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var l,i,o,d,c;return r.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(l=i.data)||void 0===l?void 0:l.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},m=async(e,t)=>{let a=localStorage.getItem("token")||"";try{return await n.Z.put(s+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var l,i,o,d,c;return r.Z.error(null==e?void 0:null===(i=e.response)||void 0===i?void 0:null===(l=i.data)||void 0===l?void 0:l.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},g=async e=>{let t=localStorage.getItem("token")||"";try{return await n.Z.delete(s+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var a,l;return r.Z.error(null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(a=l.data)||void 0===a?void 0:a.message),e}},x=async e=>{let t=localStorage.getItem("token")||"";try{return await n.Z.delete(s+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){return e}}}},function(e){e.O(0,[8863,6092,5300,7542,2344,1744],function(){return e(e.s=30702)}),_N_E=e.O()}]);