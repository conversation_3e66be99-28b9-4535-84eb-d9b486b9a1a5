(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8989],{8685:function(e,t,r){Promise.resolve().then(r.bind(r,38747))},9824:function(e,t,r){"use strict";r.d(t,{Z:function(){return l}});var n=r(32486);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:c=2,absoluteStrokeWidth:l,className:o="",children:u,iconNode:d,...h}=e;return(0,n.createElement)("svg",{ref:t,...a,width:s,height:s,stroke:r,strokeWidth:l?24*Number(c)/Number(s):c,className:i("lucide",o),...h},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),l=(e,t)=>{let r=(0,n.forwardRef)((r,a)=>{let{className:l,...o}=r;return(0,n.createElement)(c,{ref:a,iconNode:t,className:i("lucide-".concat(s(e)),l),...o})});return r.displayName="".concat(e),r}},20384:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},38747:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return i}});var n=r(75376);r(32486);var s=r(20384);function i(e){let{error:t,reset:r}=e;return(0,n.jsx)("div",{className:"bg-white relative min-h-screen flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center px-4",children:[(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("div",{className:"w-24 h-24 mx-auto mb-6 bg-red-100 rounded-full flex items-center justify-center",children:(0,n.jsx)("svg",{className:"w-12 h-12 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Something went wrong"}),(0,n.jsx)("p",{className:"text-gray-600 mb-8 max-w-md mx-auto",children:"We encountered an error while loading the agents page. This might be a temporary issue."})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("button",{onClick:r,className:"inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors",children:[(0,n.jsx)(s.Z,{className:"w-4 h-4 mr-2"}),"Try again"]}),(0,n.jsx)("div",{className:"text-sm text-gray-500",children:(0,n.jsx)("p",{children:"If the problem persists, please contact support."})})]}),!1]})})}}},function(e){e.O(0,[7542,2344,1744],function(){return e(e.s=8685)}),_N_E=e.O()}]);