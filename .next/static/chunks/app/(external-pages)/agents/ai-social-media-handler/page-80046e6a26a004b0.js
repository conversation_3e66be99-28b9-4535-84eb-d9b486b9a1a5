(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1643],{50021:function(e,t,s){Promise.resolve().then(s.bind(s,39890)),Promise.resolve().then(s.bind(s,81702)),Promise.resolve().then(s.bind(s,85462))},39890:function(e,t,s){"use strict";var a=s(75376),n=s(32486),i=s(54411),r=s.n(i);s(50500);var l=s(11492),o=s(47411);t.default=()=>{let e=(0,o.useRouter)();return(0,n.useEffect)(()=>{r().init({duration:1e3,once:!0,easing:"ease-out"})},[]),(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[300px] sm:min-h-[468px] bg-cover bg-no-repeat w-full",style:{backgroundImage:"url(/images/agents/cta.png)",backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center text-center text-white space-y-4 max-w-[484px] mx-auto px-4","data-aos":"fade-up",children:[(0,a.jsx)("h2",{className:"text-white text-2xl sm:text-3xl lg:text-4xl font-semibold",children:"Ready to Put Your Monitoring on Autopilot?"}),(0,a.jsx)("p",{className:"text-white text-sm sm:text-base",children:"Activate Ruby in minutes and gain complete visibility over your\n          brand's online presence."}),(0,a.jsxs)("div",{className:"flex justify-center gap-4 flex-wrap",children:[(0,a.jsx)(l.z,{onClick:()=>{e.push("/auth/sign-up")},size:"lg",className:"text-white bg-gradient-to-r from-[#A080FA] to-[#7141F8] px-6 transition-all duration-300 hover:scale-105",children:"Activate @Ruby Now"}),(0,a.jsx)(l.z,{onClick:()=>{e.push("/agents")},size:"lg",className:"px-6 bg-white text-[#7141F8] transition-all duration-300 hover:scale-105",children:"Browse Other Agents"})]})]})})}},81702:function(e,t,s){"use strict";var a=s(75376),n=s(32486),i=s(16669),r=s(39713),l=s(54411),o=s.n(l);s(50500),t.default=()=>((0,n.useEffect)(()=>{o().init({duration:1e3,once:!0,easing:"ease-out"})},[]),(0,a.jsx)("div",{className:"min-h-[555px] bg-cover bg-center bg-no-repeat",style:{backgroundImage:"url(/images/agents/ruby-hero.png)"},"data-aos":"fade-up","data-aos-delay":"100",children:(0,a.jsx)("div",{className:"px-4 md:px-6 text-center mx-auto pt-[100px] sm:pt-[120px]",children:(0,a.jsxs)("div",{className:"flex flex-col items-center text-center gap-10 max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 text-sm","data-aos":"fade-up","data-aos-delay":"200",children:[(0,a.jsx)(i.default,{href:"/agents",className:"text-[#667085] hover:underline",children:"Agents"}),(0,a.jsx)("span",{className:"text-[#667085]",children:">"}),(0,a.jsx)(i.default,{href:"/agents/ai-social-media-handler",className:"text-[#1D2939]",children:"Ruby - AI Social Media Handler"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl md:text-[42px] font-semibold text-[#1D2939] mb-3","data-aos":"fade-up","data-aos-delay":"300",children:["Meet Ruby, Your"," ",(0,a.jsx)("span",{className:"text-[#5F5FE1]",children:"AI Social Media Handler"})]}),(0,a.jsx)("p",{className:"text-base sm:text-lg text-[#344054]","data-aos":"fade-up","data-aos-delay":"400",children:"Stay on top of every mention, comment, and trend—without the\n              constant scrolling. Ruby monitors your brand's online presence,\n              alerts you in real time, and helps you engage smarter."})]}),(0,a.jsx)(r.default,{src:"/images/agents/ruby.svg",alt:"Ruby preview",width:384,height:126,"data-aos":"fade-up","data-aos-delay":"500"})]})})}))},85462:function(e,t,s){"use strict";var a=s(75376),n=s(32486),i=s(54411),r=s.n(i);s(50500);var l=s(39713),o=s(11492);let d=[{title:"Real-time Alerts:",description:"Get notified instantly when your brand is mentioned or a post gains traction."},{title:"Sentiment Analysis:",description:"Know whether people love or hate what's being said about you."},{title:"Competitor Insights:",description:"Keep an eye on your industry and respond strategically."},{title:"Engagement Tracking:",description:"Track likes, shares, and replies without lifting a finger."}],c=[{title:"Marketing Teams:",description:"Track campaign performance and audience reactions effortlessly."},{title:"Business Owners:",description:"Stay informed without spending hours on social media."},{title:"PR Teams:",description:"Identify potential brand crises before they escalate."},{title:"Social Media Managers:",description:"Automate monitoring and focus on strategy."}];t.default=()=>((0,n.useEffect)(()=>{r().init({duration:1e3,once:!0,easing:"ease-out"})},[]),(0,a.jsxs)("div",{className:"py-[50px] sm:py-[100px] max-w-7xl mx-auto px-4 md:px-6 space-y-10 overflow-x-hidden",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-start gap-8","data-aos":"fade-left",children:[(0,a.jsxs)("div",{className:"w-full md:w-2/3",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3 mb-3",children:[(0,a.jsx)("div",{className:"w-3 h-3 shrink-0 rounded-[3px] bg-[#FA8F45] my-[7px]"}),(0,a.jsx)("h2",{className:"text-lg sm:text-xl font-bold text-[#101828]",children:"Let Ruby Handle the Monitoring, While You Focus on Growth"})]}),(0,a.jsxs)("div",{className:"pl-6",children:[(0,a.jsx)("p",{className:"text-[#475467] text-sm sm:text-base mb-6",children:"Meet Ruby, your AI-powered Social Media Handler, built to keep your brand ahead online. Ruby connects seamlessly with your existing platforms, delivering real-time monitoring and smart insights to help you engage better, respond faster, and grow your audience effortlessly."}),(0,a.jsx)(l.default,{src:"/images/agents/social-card.png",alt:"Ruby Monitoring",width:768,height:253})]})]}),(0,a.jsxs)("div",{className:"w-full md:w-1/3 p-5 bg-[#1c1c1c] rounded-[14px] gap-5 flex flex-col","data-aos":"fade-left","data-aos-delay":"200",children:[(0,a.jsxs)("div",{className:"flex items-center gap-[10px] flex-col text-center",children:[(0,a.jsx)("div",{className:"p-[6px] rounded-[70px] bg-[#303030]",children:(0,a.jsx)(l.default,{src:"/images/agents/bell.svg",alt:"Ruby Monitoring",width:40,height:40})}),(0,a.jsx)("p",{className:"text-white text-sm font-medium",children:"Ready to stay on top of every mention and boost engagement effortlessly?"})]}),(0,a.jsx)(o.z,{className:"h-12 group bg-white px-6 rounded-[11px] border-2 border-[#8860F8] transition-all duration-300 hover:scale-105",children:(0,a.jsx)("span",{className:"bg-gradient-to-r from-[#8860F8] to-[#7141F8] text-transparent bg-clip-text",children:"Manage My Social Accounts"})})]})]}),(0,a.jsxs)("div",{className:"","data-aos":"fade-left",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3 mb-3",children:[(0,a.jsx)("div",{className:"w-3 h-3 shrink-0 rounded-[3px] bg-[#FA8F45] my-[7px]"}),(0,a.jsx)("h2",{className:"text-lg sm:text-xl font-bold text-[#101828]",children:"Why Use Ruby To Handle You Social Media?"})]}),(0,a.jsxs)("div",{className:"pl-6",children:[(0,a.jsx)("p",{className:"text-[#475467] text-sm sm:text-base mb-4",children:"Ruby isn't just a monitoring tool, she is an intelligent agent that\n            adapts to your brand's voice and goals. By handling routine tasks,\n            Ruby frees your team to focus on creativity and strategy, helping\n            you grow and strengthen your online presence."}),(0,a.jsxs)("p",{className:"text-[#475467] text-sm sm:text-base mb-3",children:["With"," ",(0,a.jsx)("span",{className:"font-medium text-[#101828]",children:"Ruby-AI Social Media Handler"}),", you get:"]}),(0,a.jsx)("div",{className:"flex flex-col gap-3",children:d.map((e,t)=>(0,a.jsxs)("div",{className:"flex gap-1 flex-wrap text-sm sm:text-base","data-aos":"fade-left","data-aos-delay":100*t,children:[(0,a.jsx)("p",{children:"✅"}),(0,a.jsx)("p",{className:"text-[#475467] font-bold",children:e.title}),(0,a.jsx)("p",{className:"text-[#475467]",children:e.description})]},t))})]})]}),(0,a.jsxs)("div",{className:"","data-aos":"fade-left",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3 mb-3",children:[(0,a.jsx)("div",{className:"w-3 h-3 shrink-0 rounded-[3px] bg-[#FA8F45] my-[7px]"}),(0,a.jsx)("h2",{className:"text-lg sm:text-xl font-bold text-[#101828]",children:"How It Works"})]}),(0,a.jsxs)("div",{className:"pl-6",children:[(0,a.jsx)("p",{className:"text-[#475467] text-sm sm:text-base mb-4",children:"Choosing to manage your social media with Ruby is an easy process that goes as follows:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside text-[#475467] flex flex-col gap-3 text-sm sm:text-base",children:[(0,a.jsx)("li",{"data-aos":"fade-left","data-aos-delay":"100",children:"Start a chat with Ruby."}),(0,a.jsx)("li",{"data-aos":"fade-left","data-aos-delay":"200",children:"Connect your social media accounts to Telex via chat."}),(0,a.jsx)("li",{"data-aos":"fade-left","data-aos-delay":"300",children:"Choose the notifications you want—mentions, trends, engagement spikes, etc."}),(0,a.jsx)("li",{"data-aos":"fade-left","data-aos-delay":"400",children:"Get real-time updates in a Telex channel created specifically for your social media handling and respond instantly."})]})]})]}),(0,a.jsxs)("div",{className:"","data-aos":"fade-left",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3 mb-3",children:[(0,a.jsx)("div",{className:"w-3 h-3 shrink-0 rounded-[3px] bg-[#FA8F45] my-[7px]"}),(0,a.jsx)("h2",{className:"text-lg sm:text-xl font-bold text-[#101828]",children:"How Your Team Benefits"})]}),(0,a.jsxs)("div",{className:"pl-6",children:[(0,a.jsx)("p",{className:"text-[#475467] text-sm sm:text-base mb-4",children:"No matter your role, staying on top of social media can be overwhelming. Ruby ensures you never miss an important mention, trend, or engagement spike, so you can focus on what truly matters. Get the insights you need, exactly when you need them, without the noise."}),(0,a.jsxs)("p",{className:"text-[#475467] text-sm sm:text-base mb-3",children:["Users who can benefit from using"," ",(0,a.jsxs)("span",{className:"font-medium text-[#101828]",children:["Ruby-AI Social Media Handler"," "]}),"are:"]}),(0,a.jsx)("div",{className:"flex flex-col gap-3",children:c.map((e,t)=>(0,a.jsxs)("div",{className:"flex gap-1 flex-wrap text-sm sm:text-base","data-aos":"fade-left","data-aos-delay":100*t,children:[(0,a.jsx)("p",{children:"✅"}),(0,a.jsx)("p",{className:"text-[#475467] font-bold",children:e.title}),(0,a.jsx)("p",{className:"text-[#475467]",children:e.description})]},t))})]})]})]}))},11492:function(e,t,s){"use strict";s.d(t,{d:function(){return o},z:function(){return d}});var a=s(75376),n=s(32486),i=s(91007),r=s(53447),l=s(58983);let o=(0,r.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,t)=>{let{className:s,variant:n,size:r,asChild:d=!1,...c}=e,x=d?i.g7:"button";return(0,a.jsx)(x,{className:(0,l.cn)(o({variant:n,size:r,className:s})),ref:t,...c})});d.displayName="Button"},58983:function(e,t,s){"use strict";s.d(t,{cn:function(){return i},k:function(){return r}});var a=s(89824),n=s(97215);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,n.m6)((0,a.W)(t))}let r=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"}},function(e){e.O(0,[4142,8863,7140,6092,8571,7542,2344,1744],function(){return e(e.s=50021)}),_N_E=e.O()}]);