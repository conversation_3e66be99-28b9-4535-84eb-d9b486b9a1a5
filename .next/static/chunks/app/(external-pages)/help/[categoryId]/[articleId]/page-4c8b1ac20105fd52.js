(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7406],{25188:function(t,e,l){Promise.resolve().then(l.bind(l,92445))},92445:function(t,e,l){"use strict";l.r(e);var a=l(75376),o=l(32486),n=l(66236),c=l(65593),s=l(10952),i=l(4125);e.default=function(t){let{params:e}=t,{categoryId:l,articleId:r}=e,[d,u]=(0,o.useState)(""),[f,h]=(0,o.useState)(null),[v,m]=(0,o.useState)(!0);return((0,o.useEffect)(()=>{let t=async()=>{try{var t,e;let l=localStorage.getItem("token");if(!l)throw Error("No token found");let a=await (0,s.Gl)("/help-center/articles/".concat(r),l);(null==a?void 0:a.status)===200&&(null===(t=a.data)||void 0===t?void 0:t.status)==="success"?h(null==a?void 0:null===(e=a.data)||void 0===e?void 0:e.data):console.error("Failed to fetch articles:",a)}catch(t){console.error("Failed to fetch article",t)}finally{m(!1)}};(async()=>{try{var t;let e=localStorage.getItem("token");if(!e)throw Error("No token found");let a=await (0,s.Gl)("/help-center/categories",e);if((null==a?void 0:a.status)===200&&(null===(t=a.data)||void 0===t?void 0:t.status)==="success"){let t=(a.data.data||[]).find(t=>t.id===l);t&&u(t.name)}}catch(t){console.error("Failed to fetch category",t)}})(),t()},[l,r]),v)?(0,a.jsx)(c.Z,{}):(0,a.jsxs)("div",{className:"container mx-auto p-4 max-w-[852px] mt-12 mb-6",children:[(0,a.jsx)(i.Z,{}),f&&(0,a.jsx)(n.Z,{items:[{label:d,href:"/help/".concat(l)},{label:f.title,href:"/help/".concat(l,"/").concat(r)}]}),(0,a.jsxs)("div",{className:"lg:ml-36 lg:mt-10",children:[(0,a.jsx)("h1",{className:"leading-[150%] text-2xl font-bold",children:null==f?void 0:f.title}),(0,a.jsx)("p",{className:"my-[30px] leading-[150%]",children:null==f?void 0:f.content})]})]})}}},function(t){t.O(0,[4269,7140,6092,5300,2987,5463,7542,2344,1744],function(){return t(t.s=25188)}),_N_E=t.O()}]);