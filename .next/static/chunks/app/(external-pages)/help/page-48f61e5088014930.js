(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4372],{56985:function(e,t,n){Promise.resolve().then(n.bind(n,90437))},9824:function(e,t,n){"use strict";n.d(t,{Z:function(){return o}});var i=n(32486);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&n.indexOf(e)===t).join(" ")};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,i.forwardRef)((e,t)=>{let{color:n="currentColor",size:r=24,strokeWidth:s=2,absoluteStrokeWidth:o,className:d="",children:c,iconNode:u,...m}=e;return(0,i.createElement)("svg",{ref:t,...a,width:r,height:r,stroke:n,strokeWidth:o?24*Number(s)/Number(r):s,className:l("lucide",d),...m},[...u.map(e=>{let[t,n]=e;return(0,i.createElement)(t,n)}),...Array.isArray(c)?c:[c]])}),o=(e,t)=>{let n=(0,i.forwardRef)((n,a)=>{let{className:o,...d}=n;return(0,i.createElement)(s,{ref:a,iconNode:t,className:l("lucide-".concat(r(e)),o),...d})});return n.displayName="".concat(e),n}},61639:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,n(9824).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},22397:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,n(9824).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},16669:function(e,t,n){"use strict";n.d(t,{default:function(){return r.a}});var i=n(6092),r=n.n(i)},65683:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return i}});let i=n(60723)._(n(32486)).default.createContext(null)},90437:function(e,t,n){"use strict";var i=n(75376),r=n(61639),l=n(22397),a=n(16669),s=n(32486);t.default=function(){let[e,t]=(0,s.useState)(!1);return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(r.Z,{className:"md:hidden ml-6",onClick:()=>t(!0)}),e?(0,i.jsxs)("div",{className:"fixed z-[999999999] p-6 top-0 md:p-0 bg-white w-4/6 md:w-3/12 md:flex flex-col gap-8 md:sticky h-screen md:top-5 md:bg-transparent",children:[(0,i.jsx)(l.Z,{className:"",onClick:()=>t(!1)}),[{id:"1",title:"Getting Started",sublinks:[{id:"1",name:"Welcome",link:"#welcome"}]},{id:"2",title:"Channel Managment",sublinks:[{id:"1",name:"How can a Channel be created on Telex?",link:"#create"},{id:"2",name:"Can more than one channel be created on Telex?",link:"#more"},{id:"3",name:"How many members are allowed per channel?",link:"#members"},{id:"4",name:"Can a channel be retrieved after it is deleted?",link:"#retieved"}]},{id:"1",title:"Telex AI Summary Integration",sublinks:[{id:"1",name:"How does Telex Ai Summary Works",link:"#how"}]},{id:"1",title:"Apps Integration",sublinks:[{id:"1",name:"How do i integrate apps with telex",link:"#app"}]},{id:"1",title:"User Roles Permission",sublinks:[{id:"1",name:"How can i add members into a channel",link:"#add"}]},{id:"1",title:"Custom Workflows",sublinks:[{id:"1",name:"Can i create custom work flows ",link:"#workflow"}]},{id:"1",title:"Teams",sublinks:[{id:"1",name:"How do i manage my team roles and permission ",link:"#team"}]}].map(e=>(0,i.jsxs)("div",{className:"flex flex-col gap-4 ",children:[(0,i.jsx)("h1",{className:"font-bold text-lg",children:e.title}),e.sublinks.map(e=>(0,i.jsx)(a.default,{href:e.link,className:"text-[#656565] text-[16px] hover:text-[#262626]",children:e.name},e.id))]},e.id))]}):""]})}}},function(e){e.O(0,[6092,7542,2344,1744],function(){return e(e.s=56985)}),_N_E=e.O()}]);