(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5575],{13507:function(e,t,s){Promise.resolve().then(s.bind(s,25619))},9824:function(e,t,s){"use strict";s.d(t,{Z:function(){return o}});var i=s(32486);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&s.indexOf(e)===t).join(" ")};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var r={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,i.forwardRef)((e,t)=>{let{color:s="currentColor",size:l=24,strokeWidth:a=2,absoluteStrokeWidth:o,className:d="",children:c,iconNode:h,...p}=e;return(0,i.createElement)("svg",{ref:t,...r,width:l,height:l,stroke:s,strokeWidth:o?24*Number(a)/Number(l):a,className:n("lucide",d),...p},[...h.map(e=>{let[t,s]=e;return(0,i.createElement)(t,s)}),...Array.isArray(c)?c:[c]])}),o=(e,t)=>{let s=(0,i.forwardRef)((s,r)=>{let{className:o,...d}=s;return(0,i.createElement)(a,{ref:r,iconNode:t,className:n("lucide-".concat(l(e)),o),...d})});return s.displayName="".concat(e),s}},69076:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,s(9824).Z)("CheckCheck",[["path",{d:"M18 6 7 17l-5-5",key:"116fxf"}],["path",{d:"m22 10-7.5 7.5L13 16",key:"ke71qq"}]])},28780:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,s(9824).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},51888:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,s(9824).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},22397:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,s(9824).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},39713:function(e,t,s){"use strict";s.d(t,{default:function(){return l.a}});var i=s(74033),l=s.n(i)},16669:function(e,t,s){"use strict";s.d(t,{default:function(){return l.a}});var i=s(6092),l=s.n(i)},74033:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return o},getImageProps:function(){return a}});let i=s(60723),l=s(25738),n=s(28863),r=i._(s(44543));function a(e){let{props:t}=(0,l.getImgProps)(e,{defaultLoader:r.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let o=n.Image},55439:function(e,t,s){"use strict";var i=s(75376),l=s(32486),n=s(43806);t.Z=()=>{let[e,t]=(0,l.useState)({firstName:"",lastName:"",email:"",company:""}),s=Object.values(e).every(e=>""!==e.trim()),r=e=>{let{name:s,value:i}=e.target;t(e=>({...e,[s]:i}))};return(0,i.jsxs)("section",{className:"flex flex-col md:flex-row md:justify-between items-center py-[57.5px] px-[100px] bg-cover bg-center gap-10 md:gap-0",style:{backgroundImage:"url('/images/resources_individual_bg.jpeg')"},children:[(0,i.jsxs)(n.E.div,{initial:{opacity:0,x:-80},animate:{opacity:1,x:0},transition:{duration:.7,ease:"easeOut"},className:"flex flex-col items-center md:items-start w-[300px] md:w-[620px] space-y-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("h1",{className:"text-white text-xl text-center md:text-left md:text-4xl font-semibold md:leading-[54px]",children:"Ready to Secure Your MongoDB?"}),(0,i.jsx)("p",{className:"text-white text-xs text-center md:text-left md:text-[16px] font-normal md:leading-6",children:"Don’t wait for an outage to impact your customers. Take control today with Telex."})]}),(0,i.jsx)("button",{className:"w-[140px] h-8 md:w-52 md:h-11 bg-white flex items-center justify-center border border-[#7141F8] text-[#7141F8] rounded-lg text-[10px] md:text-sm font-medium cursor-pointer transition-all duration-300 hover:bg-[#7141F8] hover:text-white",children:(0,i.jsx)("p",{children:"Start My Free Trial Today"})})]}),(0,i.jsxs)(n.E.div,{initial:{opacity:0,x:80},animate:{opacity:1,x:0},transition:{duration:.7,ease:"easeOut",delay:.2},className:"bg-white border border-[#E6EAEF] rounded-[14px] px-5 py-6 space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-5",children:[(0,i.jsxs)("div",{className:"flex flex-col gap-1.5",children:[(0,i.jsx)("label",{className:"text-[#1D2939] text-sm font-normal",children:"First Name"}),(0,i.jsx)("input",{type:"text",name:"firstName",value:e.firstName,onChange:r,placeholder:"John",className:"w-full h-11 px-4 border border-[#E6EAEF] rounded-sm outline-none bg-transparent",required:!0})]}),(0,i.jsxs)("div",{className:"flex flex-col gap-1.5",children:[(0,i.jsx)("label",{className:"text-[#1D2939] text-sm font-normal",children:"Last Name"}),(0,i.jsx)("input",{type:"text",name:"lastName",value:e.lastName,onChange:r,placeholder:"Doe",className:"w-full h-11 px-4 border border-[#E6EAEF] rounded-sm outline-none bg-transparent",required:!0})]})]}),(0,i.jsxs)("div",{className:"flex flex-col gap-1.5",children:[(0,i.jsx)("label",{className:"text-[#1D2939] text-sm font-normal",children:"Email"}),(0,i.jsx)("input",{type:"email",name:"email",value:e.email,onChange:r,placeholder:"<EMAIL>",className:"h-11 px-4 border border-[#E6EAEF] rounded-sm outline-none bg-transparent",required:!0})]}),(0,i.jsxs)("div",{className:"flex flex-col gap-1.5",children:[(0,i.jsx)("label",{className:"text-[#1D2939] text-sm font-normal",children:"Company Name"}),(0,i.jsx)("input",{type:"text",name:"company",value:e.company,onChange:r,placeholder:"JK Holdings",className:"h-11 px-4 border border-[#E6EAEF] rounded-sm outline-none bg-transparent",required:!0})]}),(0,i.jsx)("div",{className:"flex justify-center md:justify-end",children:(0,i.jsx)("button",{disabled:!s,className:"w-[159px] h-11 bg-gradient-to-b from-[#8860F8] to-[#7141F8] flex items-center justify-center text-white text-sm font-normal border border-[#7141F8] rounded-lg transition-all duration-300 ".concat(s?"opacity-100 hover:opacity-80":"opacity-40 cursor-not-allowed"),children:(0,i.jsx)("p",{children:"Talk To An Expert"})})})]})]})}},25619:function(e,t,s){"use strict";s.r(t);var i=s(75376),l=s(51888),n=s(22397),r=s(69076),a=s(28780),o=s(39713),d=s(16669),c=s(42211),h=s(32486),p=s(55439);let m=e=>{(0,h.useEffect)(()=>{document.title=e},[e])},x=[{id:"problem",label:"The Problem"},{id:"solution",label:"The Solution"},{id:"why-telex",label:"Why Telex"},{id:"who-uses",label:"Who Uses Telex"}];t.default=()=>{let[e,t]=(0,h.useState)("problem"),[s,u]=(0,h.useState)(!1),g=(0,h.useRef)({});m("Catch MongoDB Downtime Before Clients - Telex"),(0,h.useEffect)(()=>{let e=()=>{let e=x[0].id,s=1/0;for(let t of x){let i=g.current[t.id];if(i){let l=Math.abs(i.getBoundingClientRect().top-112);l<s&&(s=l,e=t.id)}}t(e)};return window.addEventListener("scroll",e,{passive:!0}),()=>window.removeEventListener("scroll",e)},[]);let f=e=>{let t=g.current[e];if(t){let e=t.getBoundingClientRect().top+window.scrollY;window.scrollTo({top:e-112,behavior:"smooth"})}};return(0,i.jsxs)("main",{className:"max-w-[1400px] mx-auto pt-8",children:[(0,i.jsxs)("div",{className:"flex",children:[(0,i.jsx)("div",{className:"xl:hidden ".concat(s?"hidden":"flex items-center justify-center"," sticky top-20 left-0 h-screen border-r border-[#F2F4F7] px-1"),onClick:()=>u(!0),children:(0,i.jsx)(l.Z,{size:20})}),(0,i.jsxs)("div",{className:"xl:block ".concat(s?"block":"hidden"," fixed z-20 xl:z-0 xl:sticky bg-white top-20 h-screen left-0 pl-10 xl:pl-[100px] py-20 pr-10 shadow-sm border-x border-[#F2F4F7]"),children:[(0,i.jsxs)("div",{className:"w-[100px] md:w-44 space-y-5",children:[(0,i.jsxs)("div",{className:"flex flex-col md:flex-row items-center text-center md:text-left gap-[14px] text-sm text-[#344054] font-medium",children:[(0,i.jsx)("div",{className:"relative w-12 h-12 rounded-xl overflow-hidden",children:(0,i.jsx)(o.default,{src:"/images/blog_user.jpeg",alt:"Blog User",fill:!0,className:"object-cover object-center"})}),(0,i.jsx)("h3",{children:"Brenda Franklin"})]}),(0,i.jsx)("div",{className:"text-[#101828] text-sm font-normal space-y-4",children:x.map(t=>(0,i.jsx)("h3",{onClick:()=>f(t.id),className:"transition-all duration-300 cursor-pointer ".concat(e===t.id?"text-[#7141F8]":"hover:text-[#7141F8]"),children:t.label},t.id))})]}),(0,i.jsx)(n.Z,{size:16,className:"absolute top-4 right-4 block xl:hidden",onClick:()=>u(!1)})]}),(0,i.jsxs)("div",{className:"w-full",children:[(0,i.jsxs)("div",{className:"bg-[#F9FAFB] flex justify-center md:justify-start items-center gap-3 px-4 xl:px-10 py-5 text-[#667085] text-[10px] md:text-sm font-normal",children:[(0,i.jsx)(d.default,{href:"/",className:"transition-all duration-300 hover:underline",children:"Home"}),(0,i.jsx)(l.Z,{width:9,height:21}),(0,i.jsx)(d.default,{href:"/resources",className:"transition-all duration-300 hover:underline",children:"Resources"}),(0,i.jsx)(l.Z,{width:9,height:21}),(0,i.jsx)("h3",{className:"text-[#1D2939]",children:"Catch MongoDB Downtime Before Clients"})]}),(0,i.jsxs)("div",{className:"flex flex-col items-center md:items-start px-4 xl:px-10 py-[30px] border-y border-[#F2F4F7] space-y-8",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-center md:justify-start gap-3 text-[10px] md:text-sm text-[#667085]",children:[(0,i.jsx)("h5",{className:"text-[#E36914] font-medium",children:"BLOG"}),(0,i.jsx)("div",{className:"bg-[#D0D5DD] w-1.5 h-1.5 rounded-full"}),(0,i.jsx)("h5",{children:"6 min read"}),(0,i.jsx)("div",{className:"bg-[#D0D5DD] w-1.5 h-1.5 rounded-full"}),(0,i.jsx)("h5",{children:"Last updated: 12 March 2025"})]}),(0,i.jsx)("h1",{className:"text-[#101828] text-center md:text-left text-xl md:leading-9 md:text-[42px] font-semibold",children:"Catch MongoDB Downtime Before Clients"})]}),(0,i.jsx)("div",{className:"w-[310px] h-full md:w-[600px] xl:w-[995px] md:h-[222px] flex items-center justify-center border border-[#F2F4F7] rounded-lg shadow-md bg-cover bg-center p-4 xl:p-0",style:{backgroundImage:"url('/images/blog_hero_bg.jpeg')"},children:(0,i.jsxs)("div",{className:"flex flex-col md:flex-row gap-[10px]",children:[(0,i.jsxs)("div",{className:"flex flex-col items-center",children:[(0,i.jsx)("div",{className:"bg-[#E6F1FF] w-20 h-20 flex items-center justify-center border-2 border-white rounded-t-2xl",children:(0,i.jsx)(o.default,{src:"/images/bot_dark_blue.svg",alt:"Blue Bot",width:80,height:80})}),(0,i.jsx)("div",{className:"bg-[#F5F9FF] w-[84px] h-[26px] flex items-center justify-center border-2 border-white rounded-md text-[#101828] text-sm font-medium py-2 px-3 -mt-3",children:(0,i.jsx)("h2",{children:"Vox"})})]}),(0,i.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,i.jsxs)("div",{className:"relative w-[296px] bg-white flex items-center border-2 border-white rounded-md shadow-md text-[#101828] text-sm font-normal py-[14px] pl-3 pr-[33px]",children:[(0,i.jsx)("p",{children:"Hi, I’m Vox. I keep an eye on your MongoDB database 24/7"}),(0,i.jsxs)("div",{className:"absolute right-1 bottom-1 flex items-center gap-1",children:[(0,i.jsx)(r.Z,{size:12,className:"text-[#6868F7]"}),(0,i.jsx)("p",{className:"text-[#98A2B3] text-[10px]",children:"20:55"})]})]}),(0,i.jsxs)("div",{className:"relative w-[296px] bg-white flex items-center border-2 border-white rounded-md shadow-md text-[#101828] text-sm font-normal py-[14px] pl-3 pr-[33px]",children:[(0,i.jsx)("p",{children:"I make sure it stays online and responsive."}),(0,i.jsxs)("div",{className:"absolute right-1 bottom-1 flex items-center gap-1",children:[(0,i.jsx)(a.Z,{size:12,className:"text-[#667085]"}),(0,i.jsx)("p",{className:"text-[#98A2B3] text-[10px]",children:"20:55"})]})]})]})]})})]}),(0,i.jsxs)("div",{className:"pt-12 pb-20 px-4 xl:px-10 space-y-16 w-[320px] md:w-[600px] xl:w-[995px]",children:[(0,i.jsxs)("div",{id:"problem",ref:e=>{g.current.problem=e},className:"space-y-6",children:[(0,i.jsx)("h1",{className:"text-[#101828] text-base md:text-2xl font-semibold",children:"The Problem: MongoDB Downtime Hurts More Than You Realize"}),(0,i.jsx)("p",{className:"text-[#344054] text-sm md:text-base font-normal",children:"MongoDB is the backbone of many high-performance applications, but when it goes down, your users feel it first. Slow queries, failed transactions, and complete outages can impact customer experience and revenue. The question is: do you want to hear about the issue from your clients, or do you want to prevent it before it reaches them?"})]}),(0,i.jsxs)("div",{id:"solution",ref:e=>{g.current.solution=e},className:"space-y-6",children:[(0,i.jsx)("h1",{className:"text-[#101828] text-base md:text-2xl font-semibold",children:"The Solution: Proactive Monitoring, Zero Guesswork"}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)("p",{className:"text-[#344054] text-sm md:text-base font-normal",children:"With Telex, you don’t have to rely on manual checks or wait for a support ticket to realize your MongoDB instance is failing. Our AI-driven monitoring ensures you’re the first to know—so you can take action before your customers even notice. Here’s how:"}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("p",{className:"text-[#344054] text-sm md:text-base font-normal",children:[(0,i.jsx)("span",{className:"text-[#101828] font-medium",children:"✅ Real-Time Health Checks"})," ","– Monitor MongoDB’s availability, query response times, and replica set status 24/7."]}),(0,i.jsxs)("p",{className:"text-[#344054] text-sm md:text-base font-normal",children:[(0,i.jsx)("span",{className:"text-[#101828] font-medium",children:"\uD83D\uDEA8 Instant Alerts"})," ","– Receive notifications via Slack, email, or SMS the moment an issue arises."]}),(0,i.jsxs)("p",{className:"text-[#344054] text-sm md:text-base font-normal",children:[(0,i.jsx)("span",{className:"text-[#101828] font-medium",children:"\uD83D\uDCCA Performance Tracking"})," ","– Get real-time analytics on slow queries, locked operations, and resource usage."]}),(0,i.jsxs)("p",{className:"text-[#344054] text-sm md:text-base font-normal",children:[(0,i.jsx)("span",{className:"text-[#101828] font-medium",children:"\uD83D\uDCC9 Trend Analysis & Prevention"})," ","– Identify patterns leading to failures and resolve them before they escalate."]})]})]})]}),(0,i.jsxs)("div",{id:"why-telex",ref:e=>{g.current["why-telex"]=e},className:"space-y-6",children:[(0,i.jsx)("h1",{className:"text-[#101828] text-base md:text-2xl font-semibold",children:"Why Telex is the Best Solution for MongoDB Monitoring?"}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)("p",{className:"text-[#344054] text-sm md:text-base font-normal",children:"Your MySQL uptime, monitored effortlessly in three steps:"}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("p",{className:"text-[#344054] text-sm md:text-base font-normal",children:[(0,i.jsx)("span",{className:"text-[#101828] font-medium",children:"No Manual Intervention"})," ","– Save hours of database troubleshooting and focus on growth."]}),(0,i.jsxs)("p",{className:"text-[#344054] text-sm md:text-base font-normal",children:[(0,i.jsx)("span",{className:"text-[#101828] font-medium",children:"Define Your Alert Triggers"})," ","– Choose when you want to be notified—high latency, full downtime, or anything in between."]}),(0,i.jsxs)("p",{className:"text-[#344054] text-sm md:text-base font-normal",children:[(0,i.jsx)("span",{className:"text-[#101828] font-medium",children:"Optimized for Scale"})," ","– Whether you’re managing a single database or a complex sharded cluster, Telex adapts to your needs."]}),(0,i.jsxs)("p",{className:"text-[#344054] text-sm md:text-base font-normal",children:[(0,i.jsx)("span",{className:"text-[#101828] font-medium",children:"Faster Incident Resolution"})," ","– Let Telex watch your database while you focus on handling issues."]})]})]})]}),(0,i.jsxs)("div",{id:"who-uses",ref:e=>{g.current["who-uses"]=e},className:"space-y-6",children:[(0,i.jsx)("h1",{className:"text-[#101828] text-base md:text-2xl font-semibold",children:"Who Uses Telex for MongoDB Monitoring?"}),(0,i.jsx)("div",{children:(0,i.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-2 gap-6",children:[(0,i.jsx)(c.Z,{className:"parallax-effect",tiltMaxAngleX:10,tiltMaxAngleY:10,perspective:1e3,scale:1.01,transitionSpeed:1500,gyroscope:!0,children:(0,i.jsxs)("div",{className:"p-5 border border-[#E4E7EC] rounded-xl shadow-md",children:[(0,i.jsx)(o.default,{src:"/images/Quotes.svg",alt:"Quotes",width:32,height:32,className:"mb-4"}),(0,i.jsx)("h3",{className:"text-base text-[#101828] font-normal",children:"Telex caught a database issue before our customers did—saved us from a major outage."}),(0,i.jsxs)("div",{className:"flex items-center gap-2 text-[#4B4BB4] text-sm font-semibold border-t border-dashed border-[#E6EAEF] pt-4 mt-4",children:[(0,i.jsx)(o.default,{src:"/images/user_laptop.png",alt:"User using Laptop",width:24,height:36}),(0,i.jsx)("p",{children:"DevOps Engineers"})]})]})}),(0,i.jsx)(c.Z,{className:"parallax-effect",tiltMaxAngleX:10,tiltMaxAngleY:10,perspective:1e3,scale:1.01,transitionSpeed:1500,gyroscope:!0,children:(0,i.jsxs)("div",{className:"p-5 border border-[#E4E7EC] rounded-xl shadow-md",children:[(0,i.jsx)(o.default,{src:"/images/Quotes.svg",alt:"Quotes",width:32,height:32,className:"mb-4"}),(0,i.jsx)("h3",{className:"text-base text-[#101828] font-normal",children:"We can’t afford downtime. Telex ensures our checkout process is always available."}),(0,i.jsxs)("div",{className:"flex items-center gap-2 text-[#4B4BB4] text-sm font-semibold border-t border-dashed border-[#E6EAEF] pt-4 mt-4",children:[(0,i.jsx)(o.default,{src:"/images/shopping_bag.png",alt:"Shopping Bag",width:24,height:36}),(0,i.jsx)("p",{children:"E-commerce Platforms"})]})]})}),(0,i.jsx)(c.Z,{className:"parallax-effect",tiltMaxAngleX:10,tiltMaxAngleY:10,perspective:1e3,scale:1.01,transitionSpeed:1500,gyroscope:!0,children:(0,i.jsxs)("div",{className:"p-5 border border-[#E4E7EC] rounded-xl shadow-md",children:[(0,i.jsx)(o.default,{src:"/images/Quotes.svg",alt:"Quotes",width:32,height:32,className:"mb-4"}),(0,i.jsx)("h3",{className:"text-base text-[#101828] font-normal",children:"Monitoring MySQL manually was killing productivity. Telex freed up hours every week."}),(0,i.jsxs)("div",{className:"flex items-center gap-2 text-[#4B4BB4] text-sm font-semibold border-t border-dashed border-[#E6EAEF] pt-4 mt-4",children:[(0,i.jsx)(o.default,{src:"/images/graph_icon.png",alt:"Graph",width:24,height:36}),(0,i.jsx)("p",{children:"SaaS Companies"})]})]})})]})})]})]})]})]}),(0,i.jsx)(p.Z,{})]})}},42211:function(e,t,s){"use strict";s.d(t,{Z:function(){return d}});var i=s(75376),l=s(32486);let n=(e,t,s,i)=>{e.style.transition=`${t} ${s}ms ${i}`},r=(e,t,s)=>Math.min(Math.max(e,t),s);class a{constructor(e,t){this.glareAngle=0,this.glareOpacity=0,this.calculateGlareSize=e=>{let{width:t,height:s}=e,i=Math.sqrt(t**2+s**2);return{width:i,height:i}},this.setSize=e=>{let t=this.calculateGlareSize(e);this.glareEl.style.width=`${t.width}px`,this.glareEl.style.height=`${t.height}px`},this.update=(e,t,s,i)=>{this.updateAngle(e,t.glareReverse),this.updateOpacity(e,t,s,i)},this.updateAngle=(e,t)=>{let{xPercentage:s,yPercentage:i}=e;this.glareAngle=(s?180/Math.PI*Math.atan2(i,-s):0)-(t?180:0)},this.updateOpacity=(e,t,s,i)=>{let{xPercentage:l,yPercentage:n}=e,{glarePosition:a,glareReverse:o,glareMaxOpacity:d}=t,c=s?-1:1,h=i?-1:1,p=o?-1:1,m=0;switch(a){case"top":m=-l*c*p;break;case"right":m=n*h*p;break;case"bottom":case void 0:m=l*c*p;break;case"left":m=-n*h*p;break;case"all":m=Math.hypot(l,n)}let x=r(m,0,100);this.glareOpacity=x*d/100},this.render=e=>{let{glareColor:t}=e;this.glareEl.style.transform=`rotate(${this.glareAngle}deg) translate(-50%, -50%)`,this.glareEl.style.opacity=this.glareOpacity.toString(),this.glareEl.style.background=`linear-gradient(0deg, rgba(255,255,255,0) 0%, ${t} 100%)`},this.glareWrapperEl=document.createElement("div"),this.glareEl=document.createElement("div"),this.glareWrapperEl.appendChild(this.glareEl),this.glareWrapperEl.className="glare-wrapper",this.glareEl.className="glare";let s=this.calculateGlareSize(e),i={position:"absolute",top:"50%",left:"50%",transformOrigin:"0% 0%",pointerEvents:"none",width:`${s.width}px`,height:`${s.height}px`};Object.assign(this.glareWrapperEl.style,{position:"absolute",top:"0",left:"0",width:"100%",height:"100%",overflow:"hidden",borderRadius:t,WebkitMaskImage:"-webkit-radial-gradient(white, black)",pointerEvents:"none"}),Object.assign(this.glareEl.style,i)}}class o{constructor(){this.tiltAngleX=0,this.tiltAngleY=0,this.tiltAngleXPercentage=0,this.tiltAngleYPercentage=0,this.update=(e,t)=>{this.updateTilt(e,t),this.updateTiltManualInput(e,t),this.updateTiltReverse(t),this.updateTiltLimits(t)},this.updateTilt=(e,t)=>{let{xPercentage:s,yPercentage:i}=e,{tiltMaxAngleX:l,tiltMaxAngleY:n}=t;this.tiltAngleX=s*l/100,this.tiltAngleY=-(i*n/100*1)},this.updateTiltManualInput=(e,t)=>{let{tiltAngleXManual:s,tiltAngleYManual:i,tiltMaxAngleX:l,tiltMaxAngleY:n}=t;(null!==s||null!==i)&&(this.tiltAngleX=null!==s?s:0,this.tiltAngleY=null!==i?i:0,e.xPercentage=100*this.tiltAngleX/l,e.yPercentage=100*this.tiltAngleY/n)},this.updateTiltReverse=e=>{let t=e.tiltReverse?-1:1;this.tiltAngleX=t*this.tiltAngleX,this.tiltAngleY=t*this.tiltAngleY},this.updateTiltLimits=e=>{let{tiltAxis:t}=e;this.tiltAngleX=r(this.tiltAngleX,-90,90),this.tiltAngleY=r(this.tiltAngleY,-90,90),t&&(this.tiltAngleX="x"===t?this.tiltAngleX:0,this.tiltAngleY="y"===t?this.tiltAngleY:0)},this.updateTiltAnglesPercentage=e=>{let{tiltMaxAngleX:t,tiltMaxAngleY:s}=e;this.tiltAngleXPercentage=this.tiltAngleX/t*100,this.tiltAngleYPercentage=this.tiltAngleY/s*100},this.render=e=>{e.style.transform+=`rotateX(${this.tiltAngleX}deg) rotateY(${this.tiltAngleY}deg) `}}}class d extends l.PureComponent{constructor(){super(...arguments),this.wrapperEl={node:null,size:{width:0,height:0,left:0,top:0},clientPosition:{x:null,y:null,xPercentage:0,yPercentage:0},updateAnimationId:null,scale:1},this.tilt=null,this.glare=null,this.addDeviceOrientationEventListener=async()=>{if(!window.DeviceOrientationEvent)return;let e=DeviceOrientationEvent.requestPermission;"function"==typeof e?"granted"===await e()&&window.addEventListener("deviceorientation",this.onMove):window.addEventListener("deviceorientation",this.onMove)},this.setSize=()=>{this.setWrapperElSize(),this.glare&&this.glare.setSize(this.wrapperEl.size)},this.mainLoop=e=>{null!==this.wrapperEl.updateAnimationId&&cancelAnimationFrame(this.wrapperEl.updateAnimationId),this.processInput(e),this.update(e.type),this.wrapperEl.updateAnimationId=requestAnimationFrame(this.renderFrame)},this.onEnter=e=>{let{onEnter:t}=this.props;this.setSize(),this.wrapperEl.node.style.willChange="transform",this.setTransitions(),t&&t({event:e})},this.onMove=e=>{this.mainLoop(e),this.emitOnMove(e)},this.onLeave=e=>{let{onLeave:t}=this.props;if(this.setTransitions(),t&&t({event:e}),this.props.reset){let e=new CustomEvent("autoreset");this.onMove(e)}},this.processInput=e=>{let{scale:t}=this.props;switch(e.type){case"mousemove":this.wrapperEl.clientPosition.x=e.pageX,this.wrapperEl.clientPosition.y=e.pageY,this.wrapperEl.scale=t;break;case"touchmove":this.wrapperEl.clientPosition.x=e.touches[0].pageX,this.wrapperEl.clientPosition.y=e.touches[0].pageY,this.wrapperEl.scale=t;break;case"deviceorientation":this.processInputDeviceOrientation(e),this.wrapperEl.scale=t;break;case"autoreset":{let{tiltAngleXInitial:e,tiltAngleYInitial:t,tiltMaxAngleX:s,tiltMaxAngleY:i}=this.props;this.wrapperEl.clientPosition.xPercentage=r(e/s*100,-100,100),this.wrapperEl.clientPosition.yPercentage=r(t/i*100,-100,100),this.wrapperEl.scale=1}}},this.processInputDeviceOrientation=e=>{if(!e.gamma||!e.beta||!this.props.gyroscope)return;let{tiltMaxAngleX:t,tiltMaxAngleY:s}=this.props,i=e.gamma;this.wrapperEl.clientPosition.xPercentage=e.beta/t*100,this.wrapperEl.clientPosition.yPercentage=i/s*100,this.wrapperEl.clientPosition.xPercentage=r(this.wrapperEl.clientPosition.xPercentage,-100,100),this.wrapperEl.clientPosition.yPercentage=r(this.wrapperEl.clientPosition.yPercentage,-100,100)},this.update=e=>{let{tiltEnable:t,flipVertically:s,flipHorizontally:i}=this.props;"autoreset"!==e&&"deviceorientation"!==e&&"propChange"!==e&&this.updateClientInput(),t&&this.tilt.update(this.wrapperEl.clientPosition,this.props),this.updateFlip(),this.tilt.updateTiltAnglesPercentage(this.props),this.glare&&this.glare.update(this.wrapperEl.clientPosition,this.props,s,i)},this.updateClientInput=()=>{let e,t;let{trackOnWindow:s}=this.props;if(s){let{x:s,y:i}=this.wrapperEl.clientPosition;e=i/window.innerHeight*200-100,t=s/window.innerWidth*200-100}else{let{size:{width:s,height:i,left:l,top:n},clientPosition:{x:r,y:a}}=this.wrapperEl;e=(a-n)/i*200-100,t=(r-l)/s*200-100}this.wrapperEl.clientPosition.xPercentage=r(e,-100,100),this.wrapperEl.clientPosition.yPercentage=r(t,-100,100)},this.updateFlip=()=>{let{flipVertically:e,flipHorizontally:t}=this.props;e&&(this.tilt.tiltAngleX+=180,this.tilt.tiltAngleY*=-1),t&&(this.tilt.tiltAngleY+=180)},this.renderFrame=()=>{this.resetWrapperElTransform(),this.renderPerspective(),this.tilt.render(this.wrapperEl.node),this.renderScale(),this.glare&&this.glare.render(this.props)}}componentDidMount(){if(this.tilt=new o,this.initGlare(),this.setSize(),this.addEventListeners(),"undefined"==typeof CustomEvent)return;let e=new CustomEvent("autoreset");this.mainLoop(e);let t=new CustomEvent("initial");this.emitOnMove(t)}componentWillUnmount(){null!==this.wrapperEl.updateAnimationId&&cancelAnimationFrame(this.wrapperEl.updateAnimationId),this.removeEventListeners()}componentDidUpdate(){let e=new CustomEvent("propChange");this.mainLoop(e),this.emitOnMove(e)}addEventListeners(){let{trackOnWindow:e,gyroscope:t}=this.props;window.addEventListener("resize",this.setSize),e&&(window.addEventListener("mouseenter",this.onEnter),window.addEventListener("mousemove",this.onMove),window.addEventListener("mouseout",this.onLeave),window.addEventListener("touchstart",this.onEnter),window.addEventListener("touchmove",this.onMove),window.addEventListener("touchend",this.onLeave)),t&&this.addDeviceOrientationEventListener()}removeEventListeners(){let{trackOnWindow:e,gyroscope:t}=this.props;window.removeEventListener("resize",this.setSize),e&&(window.removeEventListener("mouseenter",this.onEnter),window.removeEventListener("mousemove",this.onMove),window.removeEventListener("mouseout",this.onLeave),window.removeEventListener("touchstart",this.onEnter),window.removeEventListener("touchmove",this.onMove),window.removeEventListener("touchend",this.onLeave)),t&&window.DeviceOrientationEvent&&window.removeEventListener("deviceorientation",this.onMove)}setWrapperElSize(){let e=this.wrapperEl.node.getBoundingClientRect();this.wrapperEl.size.width=this.wrapperEl.node.offsetWidth,this.wrapperEl.size.height=this.wrapperEl.node.offsetHeight,this.wrapperEl.size.left=e.left+window.scrollX,this.wrapperEl.size.top=e.top+window.scrollY}initGlare(){let{glareEnable:e,glareBorderRadius:t}=this.props;e&&(this.glare=new a(this.wrapperEl.size,t),this.wrapperEl.node.appendChild(this.glare.glareWrapperEl))}emitOnMove(e){let{onMove:t}=this.props;if(!t)return;let s=0,i=0;this.glare&&(s=this.glare.glareAngle,i=this.glare.glareOpacity),t({tiltAngleX:this.tilt.tiltAngleX,tiltAngleY:this.tilt.tiltAngleY,tiltAngleXPercentage:this.tilt.tiltAngleXPercentage,tiltAngleYPercentage:this.tilt.tiltAngleYPercentage,glareAngle:s,glareOpacity:i,event:e})}resetWrapperElTransform(){this.wrapperEl.node.style.transform=""}renderPerspective(){let{perspective:e}=this.props;this.wrapperEl.node.style.transform+=`perspective(${e}px) `}renderScale(){let{scale:e}=this.wrapperEl;this.wrapperEl.node.style.transform+=`scale3d(${e},${e},${e})`}setTransitions(){let{transitionSpeed:e,transitionEasing:t}=this.props;n(this.wrapperEl.node,"all",e,t),this.glare&&n(this.glare.glareEl,"opacity",e,t)}render(){let{children:e,className:t,style:s}=this.props;return(0,i.jsx)("div",{ref:e=>{this.wrapperEl.node=e},onMouseEnter:this.onEnter,onMouseMove:this.onMove,onMouseLeave:this.onLeave,onTouchStart:this.onEnter,onTouchMove:this.onMove,onTouchEnd:this.onLeave,className:t,style:s,children:e})}}d.defaultProps={scale:1,perspective:1e3,flipVertically:!1,flipHorizontally:!1,reset:!0,transitionEasing:"cubic-bezier(.03,.98,.52,.99)",transitionSpeed:400,trackOnWindow:!1,gyroscope:!1,tiltEnable:!0,tiltReverse:!1,tiltAngleXInitial:0,tiltAngleYInitial:0,tiltMaxAngleX:20,tiltMaxAngleY:20,tiltAxis:void 0,tiltAngleXManual:null,tiltAngleYManual:null,glareEnable:!1,glareMaxOpacity:.7,glareColor:"#ffffff",glarePosition:"bottom",glareReverse:!1,glareBorderRadius:"0"}}},function(e){e.O(0,[8863,6092,3806,7542,2344,1744],function(){return e(e.s=13507)}),_N_E=e.O()}]);