(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5122],{95459:function(e,t,r){Promise.resolve().then(r.bind(r,47937))},9824:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var i=r(32486);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,i.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:s,className:c="",children:d,iconNode:g,...u}=e;return(0,i.createElement)("svg",{ref:t,...n,width:a,height:a,stroke:r,strokeWidth:s?24*Number(l)/Number(a):l,className:o("lucide",c),...u},[...g.map(e=>{let[t,r]=e;return(0,i.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),s=(e,t)=>{let r=(0,i.forwardRef)((r,n)=>{let{className:s,...c}=r;return(0,i.createElement)(l,{ref:n,iconNode:t,className:o("lucide-".concat(a(e)),s),...c})});return r.displayName="".concat(e),r}},33512:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(9824).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},57292:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(9824).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},39713:function(e,t,r){"use strict";r.d(t,{default:function(){return a.a}});var i=r(74033),a=r.n(i)},16669:function(e,t,r){"use strict";r.d(t,{default:function(){return a.a}});var i=r(6092),a=r.n(i)},74033:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return s},getImageProps:function(){return l}});let i=r(60723),a=r(25738),o=r(28863),n=i._(r(44543));function l(e){let{props:t}=(0,a.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let s=o.Image},47937:function(e,t,r){"use strict";var i=r(75376),a=r(57292),o=r(33512),n=r(39713),l=r(16669),s=r(32486);let c=[{type:"ESSENTIAL GUIDE",tagColor:"#E36914",bgColor:"#FEF1E8",date:"29.03.2025",title:"Getting started (Account Creator & Invited User)",image:"/images/article_content_orange.svg",action:"Read guide",link:"/resources/getting-started-guide"},{type:"ESSENTIAL GUIDE",tagColor:"#2E8DFF",bgColor:"#E6F1FF",date:"26.03.2025",title:"Managing Your Profile Preferences",image:"/images/article_content_blue.svg",action:"Read guide",link:"/resources"},{type:"BLOG",tagColor:"#6868F7",bgColor:"#F1F1FE",date:"24.03.2025",title:"Why Downtime is Killing Your Conversions",image:"/images/article_content_purple.svg",action:"Read article",link:"/resources"},{type:"BLOG",tagColor:"#009143",bgColor:"#F2FCF7",date:"22.03.2025",title:"Test Stripe Webhooks in Real-Time",image:"/images/article_content_green.svg",action:"Read article",link:"/resources"},{type:"ESSENTIAL GUIDE",tagColor:"#2E8DFF",bgColor:"#FFFFFF",date:"21.03.2025",title:"Setting Up Notification Preferences",image:"/images/article_content_blue.svg",action:"Read guide",link:"/resources"},{type:"GUIDE",tagColor:"#009143",bgColor:"#FFFFFF",date:"19.03.2025",title:"Find the Right Agent For Your Need",image:"/images/article_content_green.svg",action:"Read guide",link:"/resources"},{type:"BLOG",tagColor:"#E36914",bgColor:"#FFFFFF",date:"16.03.2025",title:"Monitor AWS EC2 Health in Real-Time",image:"/images/article_content_orange.svg",action:"Read article",link:"/resources"},{type:"BLOG",tagColor:"#344054",bgColor:"#F9FAFB",date:"12.03.2025",title:"Catch MongoDB Downtime Before Clients",image:"/images/article_content_gray.svg",action:"Read article",link:"/resources/catch-mongodb-downtime-before-clients"},{type:"ESSENTIAL GUIDE",tagColor:"#E36914",bgColor:"#FEF1E8",date:"10.03.2025",title:"Managing Your Channels as an Org Admin",image:"/images/article_content_orange.svg",action:"Read guide",link:"/resources"},{type:"BLOG",tagColor:"#6868F7",bgColor:"#F1F1FE",date:"7.03.2025",title:"Why Downtime is Killing Your Conversions",image:"/images/article_content_purple.svg",action:"Read article",link:"/resources"},{type:"ESSENTIAL GUIDE",tagColor:"#2E8DFF",bgColor:"#FFFFFF",date:"7.03.2025",title:"How to Automate Tasks With Workflows",image:"/images/article_content_blue.svg",action:"Read guide",link:"/resources"},{type:"BLOG",tagColor:"#6868F7",bgColor:"#FFFFFF",date:"4.03.2025",title:"Why Downtime is Killing Your Conversions",image:"/images/article_content_purple.svg",action:"Read article",link:"/resources"}];t.default=()=>{let[e,t]=(0,s.useState)("All"),[r,d]=(0,s.useState)(""),g=c.filter(t=>{let i="All"===e||"Blog"===e&&"BLOG"===t.type||"Guides"===e&&("GUIDE"===t.type||"ESSENTIAL GUIDE"===t.type),a=t.title.toLowerCase().includes(r.toLowerCase());return i&&a});return(0,i.jsxs)("section",{className:"xl:px-[100px] px-16 py-20 space-y-10",children:[(0,i.jsx)("div",{className:"flex flex-col items-center gap-8 xl:flex-row md:justify-between xl:gap-0",children:(0,i.jsxs)("div",{className:"md:w-[392px] h-[40px] flex items-center text-[#667085] gap-1.5 border border-[#E6EAEF] rounded-md p-2.5",children:[(0,i.jsx)(a.Z,{size:16}),(0,i.jsx)("input",{type:"text",placeholder:"Search for a topic",value:r,onChange:e=>{d(e.target.value)},className:"w-full outline-none bg-transparent"})]})}),g.length>0?(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-12 md:gap-8",children:g.map((e,t)=>(0,i.jsxs)("div",{className:"relative flex flex-col gap-8 border border-[#E4E7EC] rounded-[10px] px-5 py-6 shadow-md overflow-hidden",style:{backgroundColor:e.bgColor},children:[(0,i.jsxs)("div",{className:"flex items-center justify-between text-[10px] md:text-xs font-medium",children:[(0,i.jsx)("h3",{style:{color:e.tagColor},children:e.type}),(0,i.jsx)("p",{className:"text-[#667085]",children:e.date})]}),(0,i.jsx)("div",{className:"relative z-10 h-[140px] md:h-[188px]",children:(0,i.jsx)("h2",{className:"text-[#101828] font-medium text-xl md:text-2xl",children:e.title})}),(0,i.jsxs)(l.default,{href:e.link,className:"relative z-10 flex items-center text-[#344054] text-xs md:text-sm font-semibold gap-1 cursor-pointer transition-all duration-300 hover:underline",children:[(0,i.jsx)("p",{children:e.action}),(0,i.jsx)(o.Z,{size:16})]}),(0,i.jsx)(n.default,{src:e.image,alt:"Article Content",width:126,height:126,className:"absolute w-[100px] h-[100px] md:w-[126px] md:h-[126px] bottom-0 right-0"})]},t))}):(0,i.jsxs)("div",{className:"w-[250px] md:w-full flex flex-col items-center text-center text-[#667085]",children:[(0,i.jsx)(n.default,{src:"/images/bot-gray.svg",alt:"Gray Bot",width:200,height:400,className:"opacity-10"}),(0,i.jsx)("h2",{className:"mb-2",children:"No more Topics fits your search"}),(0,i.jsx)("p",{children:"Try and search again!!!"})]})]})}}},function(e){e.O(0,[8863,6092,7542,2344,1744],function(){return e(e.s=95459)}),_N_E=e.O()}]);