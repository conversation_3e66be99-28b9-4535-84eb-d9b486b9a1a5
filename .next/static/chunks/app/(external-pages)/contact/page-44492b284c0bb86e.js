(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{14966:function(e,t,r){Promise.resolve().then(r.t.bind(r,6092,23)),Promise.resolve().then(r.bind(r,7103))},9824:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var n=r(32486);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:u="",children:d,iconNode:c,...f}=e;return(0,n.createElement)("svg",{ref:t,...s,width:a,height:a,stroke:r,strokeWidth:i?24*Number(l)/Number(a):l,className:o("lucide",u),...f},[...c.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),i=(e,t)=>{let r=(0,n.forwardRef)((r,s)=>{let{className:i,...u}=r;return(0,n.createElement)(l,{ref:s,iconNode:t,className:o("lucide-".concat(a(e)),i),...u})});return r.displayName="".concat(e),r}},1855:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},20603:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},65683:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return n}});let n=r(60723)._(r(32486)).default.createContext(null)},7103:function(e,t,r){"use strict";var n=r(75376),a=r(32486),o=r(11492),s=r(20603),l=r(1855),i=r(15125);t.default=()=>{let[e,t]=(0,a.useState)(""),[r,u]=(0,a.useState)(""),[d,c]=(0,a.useState)(""),[f,m]=(0,a.useState)(""),[p,h]=(0,a.useState)(""),[g,x]=(0,a.useState)(!1),{toast:v}=(0,i.pm)(),y=()=>e&&r&&d&&f?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r)?/^[0-9]{10,15}$/.test(d)?(h(""),!0):(h("Please enter a valid phone number"),!1):(h("Please enter a valid email address"),!1):(h("All fields are required"),!1),b=async n=>{if(n.preventDefault(),y()){x(!0);try{if(!(await fetch("https://api.staging.telex.im/api/v1/contact",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e,email:r,phone_number:d,message:f})})).ok)throw Error("Failed to send message");v({title:"Message Sent!",description:"Thank you, ".concat(e,"! We've received your message.")}),t(""),u(""),c(""),m("")}catch(t){console.error("Error sending data:",t),v({title:"Oops! Something Went Wrong.",description:"We're sorry, ".concat(e,". There was an issue sending your message. Please try again later.")})}finally{x(!1)}}};return(0,n.jsxs)("form",{className:"flex flex-col gap-[1.56rem] w-full",onSubmit:b,children:[(0,n.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,n.jsx)("label",{htmlFor:"name",className:"text-md text-[#1D2939] font-normal",children:"Name"}),(0,n.jsx)("input",{type:"text",name:"name",id:"name",value:e,onChange:e=>t(e.target.value),placeholder:"Enter your full name",className:"rounded-[0.25rem] px-4 py-[0.81rem] border-[1px] border-solid ".concat(p&&""===e?"border-red-500":"border-[#D0D0FD]"," placeholder-[#667085] text-sm font-medium leading-normal")})]}),(0,n.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,n.jsx)("label",{htmlFor:"email",className:"text-md text-[#1D2939] font-normal",children:"Email"}),(0,n.jsx)("input",{type:"text",name:"email",id:"email",value:r,onChange:e=>u(e.target.value),placeholder:"Enter your email",className:"rounded-[0.25rem] px-4 py-[0.81rem] border-[1px] border-solid ".concat(p&&!r.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)?"border-red-500":"border-[#D0D0FD]"," placeholder-[#667085] text-sm font-medium leading-normal")})]}),(0,n.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,n.jsx)("label",{htmlFor:"phone",className:"text-md text-[#1D2939] font-normal",children:"Phone Number"}),(0,n.jsx)("input",{type:"text",name:"phone",id:"phone",value:d,onChange:e=>c(e.target.value),placeholder:"Enter your phone number",className:"rounded-[0.25rem] px-4 py-[0.81rem] border-[1px] border-solid ".concat(p&&!d.match(/^[0-9]{10,15}$/)?"border-red-500":"border-[#D0D0FD]"," placeholder-[#667085] text-sm font-medium leading-normal")})]}),(0,n.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,n.jsx)("label",{htmlFor:"message",className:"text-md text-[#1D2939] font-normal",children:"Message"}),(0,n.jsx)("textarea",{name:"message",id:"message",value:f,onChange:e=>m(e.target.value),rows:10,className:"rounded-[0.25rem] px-4 py-[0.81rem] border-[1px] border-solid ".concat(p&&""===f?"border-red-500":"border-[#D0D0FD]"," placeholder-[#667085] text-xs font-medium leading-normal")}),""!==p&&(0,n.jsx)("p",{className:"text-red-400 text-sm",children:p})]}),(0,n.jsxs)(o.z,{type:"submit",className:"flex gap-2 bg-primary-500 text-white rounded-[0.375rem]",children:[g?(0,n.jsx)(l.Z,{className:"animate-spin"}):(0,n.jsx)(s.Z,{}),(0,n.jsx)("span",{className:"text-[0.81131rem] font-medium",children:g?"Sending":"Send"})]})]})}},11492:function(e,t,r){"use strict";r.d(t,{d:function(){return i},z:function(){return u}});var n=r(75376),a=r(32486),o=r(91007),s=r(53447),l=r(58983);let i=(0,s.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=a.forwardRef((e,t)=>{let{className:r,variant:a,size:s,asChild:u=!1,...d}=e,c=u?o.g7:"button";return(0,n.jsx)(c,{className:(0,l.cn)(i({variant:a,size:s,className:r})),ref:t,...d})});u.displayName="Button"},15125:function(e,t,r){"use strict";r.d(t,{pm:function(){return f}});var n=r(32486);let a=0,o=new Map,s=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},l=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?s(r):e.toasts.forEach(e=>{s(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},i=[],u={toasts:[]};function d(e){u=l(u,e),i.forEach(e=>{e(u)})}function c(e){let{...t}=e,r=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),n=()=>d({type:"DISMISS_TOAST",toastId:r});return d({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||n()}}}),{id:r,dismiss:n,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function f(){let[e,t]=n.useState(u);return n.useEffect(()=>(i.push(t),()=>{let e=i.indexOf(t);e>-1&&i.splice(e,1)}),[e]),{...e,toast:c,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},58983:function(e,t,r){"use strict";r.d(t,{cn:function(){return o},k:function(){return s}});var n=r(89824),a=r(97215);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,n.W)(t))}let s=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"},29626:function(e,t,r){"use strict";r.d(t,{F:function(){return o},e:function(){return s}});var n=r(32486);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}function s(...e){return n.useCallback(o(...e),e)}},91007:function(e,t,r){"use strict";r.d(t,{Z8:function(){return s},g7:function(){return l},sA:function(){return u}});var n=r(32486),a=r(29626),o=r(75376);function s(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){let e,s;let l=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,i=function(e,t){let r={...t};for(let n in t){let a=e[n],o=t[n];/^on[A-Z]/.test(n)?a&&o?r[n]=(...e)=>{let t=o(...e);return a(...e),t}:a&&(r[n]=a):"style"===n?r[n]={...a,...o}:"className"===n&&(r[n]=[a,o].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(i.ref=t?(0,a.F)(t,l):l),n.cloneElement(r,i)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:a,...s}=e,l=n.Children.toArray(a),i=l.find(d);if(i){let e=i.props.children,a=l.map(t=>t!==i?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...s,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,o.jsx)(t,{...s,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var l=s("Slot"),i=Symbol("radix.slottable");function u(e){let t=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=i,t}function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}},53447:function(e,t,r){"use strict";r.d(t,{j:function(){return s}});var n=r(89824);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=n.W,s=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return o(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:s,defaultVariants:l}=t,i=Object.keys(s).map(e=>{let t=null==r?void 0:r[e],n=null==l?void 0:l[e];if(null===t)return null;let o=a(t)||a(n);return s[e][o]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return o(e,i,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...u}[t]):({...l,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}},function(e){e.O(0,[7140,6092,7542,2344,1744],function(){return e(e.s=14966)}),_N_E=e.O()}]);