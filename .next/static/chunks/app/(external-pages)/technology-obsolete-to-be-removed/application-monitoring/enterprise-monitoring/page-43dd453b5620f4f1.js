(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4620],{97324:function(e,t,n){Promise.resolve().then(n.t.bind(n,28863,23)),Promise.resolve().then(n.t.bind(n,6092,23)),Promise.resolve().then(n.bind(n,41340)),Promise.resolve().then(n.bind(n,70100)),Promise.resolve().then(n.bind(n,91917)),Promise.resolve().then(n.bind(n,62904)),Promise.resolve().then(n.bind(n,76296)),Promise.resolve().then(n.bind(n,71557)),Promise.resolve().then(n.bind(n,29640)),Promise.resolve().then(n.bind(n,80221))},39713:function(e,t,n){"use strict";n.d(t,{default:function(){return i.a}});var o=n(74033),i=n.n(o)},74033:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return a},getImageProps:function(){return l}});let o=n(60723),i=n(25738),s=n(28863),r=o._(n(44543));function l(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:r.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,n]of Object.entries(t))void 0===n&&delete t[e];return{props:t}}let a=s.Image},41340:function(e,t,n){"use strict";var o=n(75376),i=n(39713);n(32486),t.default=e=>{var t;return(0,o.jsx)("div",{className:"relative px-4 md:px-6",children:(0,o.jsxs)("div",{className:"max-w-7xl mx-auto py-[60px]",children:[(0,o.jsx)("h1",{className:"text-2xl md:text-3xl lg:text-[32px] font-semibold mb-10 lg:leading-snug md:w-[80%] lg:w-[50%]",children:null==e?void 0:e.heading}),(0,o.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8",children:null==e?void 0:null===(t=e.items)||void 0===t?void 0:t.map((e,t)=>(0,o.jsxs)("div",{className:"py-6 bg-white rounded-lg",children:[(null==e?void 0:e.image)&&(0,o.jsx)(i.default,{src:e.image||"",alt:"",width:(null==e?void 0:e.title.includes("Oracle"))||(null==e?void 0:e.title.includes("MySQL"))||(null==e?void 0:e.title.includes("Postgre"))||(null==e?void 0:e.title.includes("MongoDB"))||(null==e?void 0:e.title.includes("Redis"))?80:150,height:80,className:"mb-3"}),(0,o.jsx)("h2",{className:"text-lg font-medium mb-2",children:e.title}),(0,o.jsx)("p",{className:"text-gray-500 mb-4",children:null==e?void 0:e.content})]},t))})]})})}},99625:function(e,t,n){"use strict";n.d(t,{Accordion:function(){return l},AccordionContent:function(){return d},AccordionItem:function(){return a},AccordionTrigger:function(){return c}});var o=n(75376),i=n(32486),s=n(75737),r=n(58983);let l=s.fC,a=i.forwardRef((e,t)=>{let{className:n,...i}=e;return(0,o.jsx)(s.ck,{ref:t,className:(0,r.cn)("border-b",n),...i})});a.displayName="AccordionItem";let c=i.forwardRef((e,t)=>{let{className:n,children:i,...l}=e;return(0,o.jsx)(s.h4,{className:"flex",children:(0,o.jsx)(s.xz,{ref:t,className:(0,r.cn)("flex flex-1 text-left items-start justify-between py-4 font-semibold transition-all [&[data-state=open]>svg]:rotate-180",n),...l,children:i})})});c.displayName=s.xz.displayName;let d=i.forwardRef((e,t)=>{let{className:n,children:i,...l}=e;return(0,o.jsx)(s.VY,{ref:t,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...l,children:(0,o.jsx)("div",{className:(0,r.cn)("pb-4 pt-0",n),children:i})})});d.displayName=s.VY.displayName},58983:function(e,t,n){"use strict";n.d(t,{cn:function(){return s},k:function(){return r}});var o=n(89824),i=n(97215);function s(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,i.m6)((0,o.W)(t))}let r=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"},80221:function(e,t,n){"use strict";n.d(t,{default:function(){return d}});var o=n(75376),i=n(32486),s=n(99625),r=n(71304),l=n(59503),a=n(18208),c=n(16669);function d(e){let{faq:t}=e,[n,d]=(0,i.useState)(null);return(0,o.jsx)(o.Fragment,{children:(0,o.jsx)("main",{className:"flex justify-center w-full max-w-3xl m-auto pt-[40px] md:pt-[96px] pb-[50px]",children:(0,o.jsxs)("div",{className:"w-full",children:[(0,o.jsx)("div",{className:"flex justify-center",children:(0,o.jsxs)("div",{className:"px-3 w-fit py-2 text-sm mb-4 font-medium text-[#40258D] border-2 border-[#F1F1FE] rounded-[50px] bg-white flex items-center gap-1",children:[(0,o.jsx)(r.Z,{className:"w-4 h-4"}),(0,o.jsx)("span",{children:"Faqs"})]})}),(0,o.jsxs)("div",{className:"text-center mb-10",children:[(0,o.jsx)("h2",{className:"text-[#101828] font-semibold sm:text-4xl text-3xl leading-10",children:"Got a Question? We Have The Answer"}),(0,o.jsx)("p",{className:"text-[#344054] text-base font-normal leading-6 mt-2 max-w-lg mx-auto",children:"Here’s everything you may want to know before you bring Telex agents on board."})]}),(0,o.jsx)("div",{className:"flex justify-center w-full items-center mb-10",children:(0,o.jsx)(s.Accordion,{type:"single",collapsible:!0,className:"w-full flex flex-col gap-[16px]",value:null!=n?n:void 0,onValueChange:e=>d(e),children:(t||[{id:0,question:"How does Telex work?",answer:'<p>Telex works on three simple layers that create one powerful workspace:</p>\n\n      <ul style="margin: 16px 0; padding-left: 20px; list-style: none;">\n        <li style="margin-bottom: 2px;">\n          <strong > • The Foundation:</strong> At its core, Telex is a polished and intuitive communication hub, much like the collaboration tools you already know. You have channels, threads, and direct messages for your team.\n        </li>\n        <li style="margin-bottom: 2px;">\n          <strong > • The Magic:</strong> The difference is our no-code AI builder. This allows you to create specialized "AI colleagues" from easy-to-use templates or from scratch. You can give them names, roles, and specific instructions on what tasks to perform.\n        </li>\n        <li style="margin-bottom: 2px;">\n          <strong > • The Workflow:</strong> You collaborate with these AI colleagues directly in your channels, just as you would with a human. You can <span style="color: #059669; font-weight: 500;">@mention</span> them to assign work, ask questions, or have them generate reports. They work alongside your human team, providing updates and completing tasks 24/7.\n        </li>\n      </ul>\n\n      <p style="margin-top: 16px;">In short, Telex combines a familiar chat interface with a powerful AI factory, creating a single command center for your entire business.</p>'},{id:1,question:"Is it only for AI or can my team also use it?",answer:'<p style="margin-bottom: 16px;">Telex is designed specifically for your entire team-both human and Al. This is our core principle. We believe the future of work isn\'t about replacing humans, but about augmenting them with powerful Al teammates.</p>\n      <p style="margin-bottom: 16px;">Your human team members will use Telex for all their communication, file sharing, and project collaboration. Your Al colleagues exist in the same channels, ready to be assigned work. This creates a true hybrid team where, for example, your human marketing lead can directly ask your "Al Research Assistant" to pull the latest stats for a report, all in the same project channel.</p>'},{id:2,question:"Can I create custom agents?",answer:'<p style="margin-bottom: 16px;">Absolutely. This is the heart of the Telex platform.</p>\n      <p style="margin-bottom: 16px;">While we provide a library of pre-built templates to get you started in minutes (e.g., "Social Media Assistant," "Customer Support Agent"), our intuitive, no-code builder gives you the power to create completely custom AI colleagues tailored to your exact business needs.</p>\n      <p style="margin-bottom: 16px;">You can define their purpose, give them access to specific knowledge, and build unique workflows for them to follow. If you can write a list of instructions, you can build a custom agent in Telex.</p>\n'},{id:3,question:"Can it integrate with my current systems?",answer:'<p style="margin-bottom: 16px;">Yes. A truly useful AI colleague needs to be able to work with the tools your business already relies on. Our integration philosophy is built on two pillars:</p>\n      <ul style="margin: 16px 0; padding-left: 20px; list-style: none;">\n        <li style="margin-bottom: 2px;">\n          <strong > • Native Integrations:</strong> We offer built-in connections to popular platforms like Google Drive, HubSpot, Salesforce, Jira, and more, allowing your agents to pull data and perform actions in those systems.\n        </li>\n        <li style="margin-bottom: 2px;">\n          <strong > • Open Connectivity:</strong> For everything else, our AI agents can interact with any platform that has an API. You can configure them to send and receive information via webhooks and API calls, effectively giving your AI team the ability to connect to virtually any modern software tool.\n        </li>\n      </ul>\n'},{id:4,question:"What platforms is it available on?",answer:'<p>Telex is designed to be available wherever you work. You can access our platform on:</p>\n      <ul style="margin: 16px 0; padding-left: 20px; list-style: none;">\n        <li style="margin-bottom: 2px;">\n          <strong > • Web:</strong> A full-featured experience in any modern web browser.\n        </li>\n        <li style="margin-bottom: 2px;">\n          <strong > • Desktop:</strong> A dedicated native app for both Windows and macOS.\n        </li>\n        <li style="margin-bottom: 2px;">\n          <strong > • Mobile:</strong> A companion app for iOS and Android for when you\'re on the go.\n        </li>\n      </ul>\n      <p>Mobile: A companion app for iOS and Android for when you\'re on the go. Our goal is to provide a seamless experience across all your devices, so you\'re always connected to your human and AI team members.\n</p>\n'}]).map(e=>(0,o.jsxs)(s.AccordionItem,{value:"item-".concat(e.id),className:"mb-6 border-0",children:[(0,o.jsxs)(s.AccordionTrigger,{className:"flex justify-between items-center w-full p-8 rounded-lg bg-[#FAFAFF] border border-[#E5E8FF]",children:[(0,o.jsx)("p",{className:"w-full leading-[150%] font-semibold text-[#101828] text-[14px] md:text-[16px] text-left",children:e.question}),n==="item-".concat(e.id)?(0,o.jsx)(l.Z,{className:"text-[#7141F8] w-6 h-6"}):(0,o.jsx)(a.Z,{className:"text-[#7141F8] w-6 h-6"})]}),(0,o.jsx)(s.AccordionContent,{className:"mt-4 px-[20px]",children:(0,o.jsx)("div",{className:"w-full lg:text-[16px] sm:text-[16px] xs:text-[14px] leading-relaxed text-[#344054]",dangerouslySetInnerHTML:{__html:e.answer}})})]},e.id))})}),(0,o.jsxs)("div",{className:"w-full",children:[(0,o.jsx)("p",{className:"w-full justify-center items-center text-[18px] text-center inline-block mb-[24px] font-normal text-[#475467]",children:"Have a question not listed?"}),(0,o.jsx)(c.default,{href:"/contact",children:(0,o.jsx)("button",{className:"border text-sm rounded-md px-4 mx-auto font-semibold py-2 border-[#7141F8] bg-white text-[#7141F8] block",children:"Contact Us"})})]})]})})})}},70100:function(e,t,n){"use strict";n.r(t),t.default={src:"/_next/static/media/enterprise-bg.0ef616ed.svg",height:702,width:1440,blurWidth:0,blurHeight:0}},91917:function(e,t,n){"use strict";n.r(t),t.default={src:"/_next/static/media/microsoft-logo.cbb24271.svg",height:70,width:186,blurWidth:0,blurHeight:0}},62904:function(e,t,n){"use strict";n.r(t),t.default={src:"/_next/static/media/oracle-logo.66f6acc6.svg",height:70,width:273,blurWidth:0,blurHeight:0}},76296:function(e,t,n){"use strict";n.r(t),t.default={src:"/_next/static/media/salesforce-logo.c678952c.svg",height:70,width:101,blurWidth:0,blurHeight:0}},71557:function(e,t,n){"use strict";n.r(t),t.default={src:"/_next/static/media/sap-logo.ef5a0256.svg",height:70,width:71,blurWidth:0,blurHeight:0}},29640:function(e,t,n){"use strict";n.r(t),t.default={src:"/_next/static/media/workday-logo.37400920.svg",height:70,width:177,blurWidth:0,blurHeight:0}}},function(e){e.O(0,[8863,7140,6092,4144,5614,824,7542,2344,1744],function(){return e(e.s=97324)}),_N_E=e.O()}]);