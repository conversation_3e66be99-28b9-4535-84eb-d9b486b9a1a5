(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8855],{94959:function(e,o,n){Promise.resolve().then(n.t.bind(n,6092,23)),Promise.resolve().then(n.bind(n,28465)),Promise.resolve().then(n.bind(n,27201))},47411:function(e,o,n){"use strict";var r=n(13362);n.o(r,"useParams")&&n.d(o,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(o,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(o,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(o,{useSearchParams:function(){return r.useSearchParams}})},65683:function(e,o,n){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"RouterContext",{enumerable:!0,get:function(){return r}});let r=n(60723)._(n(32486)).default.createContext(null)},28465:function(e,o,n){"use strict";var r=n(75376),c=n(47411);n(32486);var t=n(90937);o.default=e=>{let{description:o,item:n}=e,a=(0,c.useRouter)();return(0,r.jsxs)("div",{className:"md:sticky top-24 bg-black text-white p-6 rounded-xl shadow-md  w-[250px] h-[250px] flex flex-col items-center text-center  mx-auto md:mx-0 flex-shrink-0 md:self-start",children:[(0,r.jsx)(t.iX,{}),(0,r.jsx)("p",{className:"text-sm mt-2",children:o}),(0,r.jsx)("button",{onClick:()=>{let e=localStorage.getItem("token")||"";a.push("/dashboard/applications/".concat(n.id,"?json_url=").concat(encodeURIComponent(n.json_url))),e||localStorage.setItem("route","/dashboard/applications/".concat(n.id,"?json_url=").concat(encodeURIComponent(n.json_url)))},className:"w-full mt-4 bg-white px-4 py-2 rounded-md text-primary-500 text-sm",children:"Integrate Now"})]})}},27201:function(e,o,n){"use strict";n.d(o,{default:function(){return d}});var r=n(75376),c=n(47411),t=n(32486),a=n(36727),l=n(19154),s=n(68436),i={'code[class*="language-"]':{color:"#ccc",background:"none",fontFamily:"Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace",fontSize:"1em",textAlign:"left",whiteSpace:"pre",wordSpacing:"normal",wordBreak:"normal",wordWrap:"normal",lineHeight:"1.5",MozTabSize:"4",OTabSize:"4",tabSize:"4",WebkitHyphens:"none",MozHyphens:"none",msHyphens:"none",hyphens:"none"},'pre[class*="language-"]':{color:"#ccc",background:"#2d2d2d",fontFamily:"Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace",fontSize:"1em",textAlign:"left",whiteSpace:"pre",wordSpacing:"normal",wordBreak:"normal",wordWrap:"normal",lineHeight:"1.5",MozTabSize:"4",OTabSize:"4",tabSize:"4",WebkitHyphens:"none",MozHyphens:"none",msHyphens:"none",hyphens:"none",padding:"1em",margin:".5em 0",overflow:"auto"},':not(pre) > code[class*="language-"]':{background:"#2d2d2d",padding:".1em",borderRadius:".3em",whiteSpace:"normal"},comment:{color:"#999"},"block-comment":{color:"#999"},prolog:{color:"#999"},doctype:{color:"#999"},cdata:{color:"#999"},punctuation:{color:"#ccc"},tag:{color:"#e2777a"},"attr-name":{color:"#e2777a"},namespace:{color:"#e2777a"},deleted:{color:"#e2777a"},"function-name":{color:"#6196cc"},boolean:{color:"#f08d49"},number:{color:"#f08d49"},function:{color:"#f08d49"},property:{color:"#f8c555"},"class-name":{color:"#f8c555"},constant:{color:"#f8c555"},symbol:{color:"#f8c555"},selector:{color:"#cc99cd"},important:{color:"#cc99cd",fontWeight:"bold"},atrule:{color:"#cc99cd"},keyword:{color:"#cc99cd"},builtin:{color:"#cc99cd"},string:{color:"#7ec699"},char:{color:"#7ec699"},"attr-value":{color:"#7ec699"},regex:{color:"#7ec699"},variable:{color:"#7ec699"},operator:{color:"#67cdcc"},entity:{color:"#67cdcc",cursor:"help"},url:{color:"#67cdcc"},bold:{fontWeight:"bold"},italic:{fontStyle:"italic"},inserted:{color:"green"}};let u=e=>{let{inline:o,className:n,children:c,...t}=e,a=/language-(\w+)/.exec(n||"");return!o&&a?(0,r.jsx)(s.Z,{...t,PreTag:"div",language:a[1],style:i,useInlineStyles:!0,customStyle:{backgroundColor:"transparent",padding:"20px",margin:0,fontSize:"0.95em"},children:String(c).replace(/\n$/,"")}):(0,r.jsx)("code",{className:"bg-gray-100 px-1 py-0.5 rounded",children:c})};var d=()=>{let[e,o]=(0,t.useState)("");return(0,c.useRouter)(),(0,t.useEffect)(()=>{o(localStorage.getItem("markdown")||"")},[]),(0,r.jsx)("div",{className:"",children:(0,r.jsx)("div",{className:"prose max-w-none",children:(0,r.jsx)(a.UG,{children:e,remarkPlugins:[l.Z],components:{code:u}})})})}}},function(e){e.O(0,[6092,6859,8436,937,7542,2344,1744],function(){return e(e.s=94959)}),_N_E=e.O()}]);