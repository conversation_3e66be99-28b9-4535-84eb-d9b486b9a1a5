(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[386],{1202:function(e,t,r){Promise.resolve().then(r.bind(r,6961))},9824:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});var n=r(32486);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:u="",children:c,iconNode:d,...f}=e;return(0,n.createElement)("svg",{ref:t,...l,width:o,height:o,stroke:r,strokeWidth:a?24*Number(s)/Number(o):s,className:i("lucide",u),...f},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),a=(e,t)=>{let r=(0,n.forwardRef)((r,l)=>{let{className:a,...u}=r;return(0,n.createElement)(s,{ref:l,iconNode:t,className:i("lucide-".concat(o(e)),a),...u})});return r.displayName="".concat(e),r}},71753:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},78710:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},39713:function(e,t,r){"use strict";r.d(t,{default:function(){return o.a}});var n=r(74033),o=r.n(n)},16669:function(e,t,r){"use strict";r.d(t,{default:function(){return o.a}});var n=r(6092),o=r.n(n)},47411:function(e,t,r){"use strict";var n=r(13362);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},74033:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return a},getImageProps:function(){return s}});let n=r(60723),o=r(25738),i=r(28863),l=n._(r(44543));function s(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let a=i.Image},6961:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return h}});var n=r(75376),o=r(47411),i=r(32486),l=r(97712),s=r(97220),a=r(78710),u=r(71753),c=r(16669),d=()=>{let e=(0,o.usePathname)(),{state:t,dispatch:r}=(0,i.useContext)(s.R),d=[{id:"home",icon:a.Z,label:"Dashboard",link:"/developer/dashboard"}];return(0,n.jsx)("div",{className:"fixed top-0 z-50 md:z-0 md:translate-x-0 transition-transform duration-300 ease-in-out\n               ".concat((null==t?void 0:t.openSidebar)===!0?"translate-x-0":"-translate-x-40"),children:(0,n.jsx)("div",{className:"h-[100vh] w-[110px] p-[8px] -z-50\n        md:flex flex-col justify-start",children:(0,n.jsx)("div",{className:"h-full w-full text-[#344054] rounded-[12px] z-auto bg-[#f2f4f7] flex flex-col justify-between",children:(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)(u.Z,{className:"md:hidden absolute right-0 top-0",onClick:()=>r({type:l.a.OPEN_SIDEBAR,payload:!1})}),(0,n.jsx)("div",{className:"flex flex-col items-center mt-6 w-full text-[12px]",onClick:()=>r({type:l.a.OPEN_SIDEBAR,payload:!1}),children:d.map(t=>{let r="home"===t.id?"/developer/dashboard"===e:e.includes(t.id);return(0,n.jsxs)(c.default,{href:t.link,className:"flex flex-col group mb-2 items-center justify-center space-y-1 w-full p-[5px] ".concat(r?"font-semibold scale-[1.1]":"hover:font-semibold "," ").concat(e.includes("/developer/dashboard")?"pointer-events-none cursor-not-allowed":""),children:[(0,n.jsx)("span",{className:"p-[8px] rounded-[7px]  ".concat(r?"bg-[#7b50fb]":"group-hover:bg-[#7b50fb]"),children:(0,n.jsx)(t.icon,{strokeWidth:1.2,className:"".concat(r?"fill-white stroke-[#7b50fb]":"group-hover:fill-white group-hover:stroke-[#7b50fb]")})}),(0,n.jsx)("span",{children:t.label})]},t.id)})})]})})})})},f=r(20945);function h(e){let{children:t}=e,[r,a]=(0,i.useState)(!0),u=(0,o.useRouter)(),{dispatch:c}=(0,i.useContext)(s.R);if((0,i.useEffect)(()=>{let e=localStorage.getItem("token");if(c({type:l.a.TOKEN,payload:e}),!e){u.push("/developer/login");return}a(!1)},[c,u]),!r)return(0,n.jsx)(s.DataProvider,{children:(0,n.jsx)(f.Z,{children:(0,n.jsxs)("div",{className:"w-full flex relative",children:[(0,n.jsx)(d,{}),(0,n.jsx)("div",{className:"md:ml-[110px] w-full relative",children:t})]})})})}},20945:function(e,t,r){"use strict";var n=r(75376),o=r(39713),i=r(32486),l=r(11492);class s extends i.Component{static getDerivedStateFromError(e){return{hasError:!0}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error",e,t)}resetError(){this.setState({hasError:!1}),window.location.href="/client"}render(){return this.state.hasError?(0,n.jsx)("div",{children:(0,n.jsxs)("div",{className:"max-w-full h-screen flex flex-col items-center justify-center gap-5",children:[(0,n.jsx)(o.default,{src:"/images/error-img.svg",height:"100",width:"400",alt:"error image",className:"w-60 h-40"}),(0,n.jsx)("p",{className:"text-xl font-semibold leading-6 text-slate-600",children:"Ooops!! Something went wrong"}),(0,n.jsx)("p",{className:"text-neutral-700 text-center leading-6",children:"We cannot load this page at the moment"}),(0,n.jsx)(l.z,{onClick:this.resetError,className:"bg-gradient-to-b from-[#8760f8] to-[#7141f8] py-2 px-6 text-white",children:"Go Back to Dashboard"})]})}):this.props.children}constructor(e){super(e),this.state={hasError:!1},this.resetError=this.resetError.bind(this)}}t.Z=s},11492:function(e,t,r){"use strict";r.d(t,{d:function(){return a},z:function(){return u}});var n=r(75376),o=r(32486),i=r(91007),l=r(53447),s=r(58983);let a=(0,l.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=o.forwardRef((e,t)=>{let{className:r,variant:o,size:l,asChild:u=!1,...c}=e,d=u?i.g7:"button";return(0,n.jsx)(d,{className:(0,s.cn)(a({variant:o,size:l,className:r})),ref:t,...c})});u.displayName="Button"},58983:function(e,t,r){"use strict";r.d(t,{cn:function(){return i},k:function(){return l}});var n=r(89824),o=r(97215);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.m6)((0,n.W)(t))}let l=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"},29626:function(e,t,r){"use strict";r.d(t,{F:function(){return i},e:function(){return l}});var n=r(32486);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function l(...e){return n.useCallback(i(...e),e)}},91007:function(e,t,r){"use strict";r.d(t,{Z8:function(){return l},g7:function(){return s},sA:function(){return u}});var n=r(32486),o=r(29626),i=r(75376);function l(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){let e,l;let s=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,a=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(a.ref=t?(0,o.F)(t,s):s),n.cloneElement(r,a)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...l}=e,s=n.Children.toArray(o),a=s.find(c);if(a){let e=a.props.children,o=s.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...l,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}var s=l("Slot"),a=Symbol("radix.slottable");function u(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=a,t}function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}},53447:function(e,t,r){"use strict";r.d(t,{j:function(){return l}});var n=r(89824);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.W,l=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:s}=t,a=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],n=null==s?void 0:s[e];if(null===t)return null;let i=o(t)||o(n);return l[e][i]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return i(e,a,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...u}[t]):({...s,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}},function(e){e.O(0,[8863,7140,6092,7220,7542,2344,1744],function(){return e(e.s=1202)}),_N_E=e.O()}]);