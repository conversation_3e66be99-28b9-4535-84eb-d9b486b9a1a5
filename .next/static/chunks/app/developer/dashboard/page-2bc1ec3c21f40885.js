(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5498],{53662:function(e,t,a){Promise.resolve().then(a.bind(a,45040))},33599:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(9824).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},47411:function(e,t,a){"use strict";var r=a(13362);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},82183:function(e,t,a){"use strict";/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=a(32486),l="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},n=r.useState,s=r.useEffect,o=r.useLayoutEffect,i=r.useDebugValue;function d(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!l(e,a)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var a=t(),r=n({inst:{value:a,getSnapshot:t}}),l=r[0].inst,c=r[1];return o(function(){l.value=a,l.getSnapshot=t,d(l)&&c({inst:l})},[e,a,t]),s(function(){return d(l)&&c({inst:l}),e(function(){d(l)&&c({inst:l})})},[e]),i(a),a};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},37286:function(e,t,a){"use strict";e.exports=a(82183)},45040:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return w}});var r=a(75376),l=a(32486),n=a(73003),s=a(33599),o=a(47411),i=a(89564),d=a(25368),c=a(97712),u=a(97220),x=a(10952);let m="user",f=()=>{let[e,t]=(0,l.useState)(null),[a,r]=(0,l.useState)(!1),[n,s]=(0,l.useState)(null),{state:o,dispatch:i}=(0,l.useContext)(u.R),d=e=>{t(t=>{if(!t)return e.id?e:(console.error("Attempting to set user data without an id"),null);let a={...t,...e};return localStorage.setItem(m,JSON.stringify(a)),a}),window.dispatchEvent(new Event("userDataUpdated"))},f=async()=>{r(!0),s(null);let e=localStorage.getItem("token");if(!e){r(!1),s("No token found");return}try{let a=await (0,x.Gl)("/profile",e);if((null==a?void 0:a.status)===200||(null==a?void 0:a.status)===201){var t;let e=null==a?void 0:null===(t=a.data)||void 0===t?void 0:t.data;if(!e.id)throw Error("User data is missing id");d(e),localStorage.setItem("user",JSON.stringify(e)),i({type:c.a.CALLBACK,payload:!(null==o?void 0:o.callback)})}else s("Failed to fetch user data")}catch(e){s("An error occurred while fetching user data"),console.error(e)}finally{r(!1)}},p=async()=>{r(!0),s(null);let t=localStorage.getItem("token");if(!t){r(!1),s("No token found");return}try{let a=await (0,x.jx)("/profile/image",t);if((null==a?void 0:a.status)===200||(null==a?void 0:a.status)===201)return d({...e,avatar_url:""}),!0;return s("Failed to delete profile picture"),!1}catch(e){return s("An error occurred while deleting profile picture"),console.error(e),!1}finally{r(!1)}};return(0,l.useEffect)(()=>{let e=()=>{let e=localStorage.getItem(m);if(e)try{let a=JSON.parse(e);a&&a.id?t(a):(console.error("Stored user data is invalid"),localStorage.removeItem(m))}catch(e){console.error("Error parsing stored user data",e),localStorage.removeItem(m)}else t(null)};return e(),window.addEventListener("userDataUpdated",e),window.addEventListener("storage",e),()=>{window.removeEventListener("userDataUpdated",e),window.removeEventListener("storage",e)}},[]),{user:e,updateUser:d,clearUser:()=>{localStorage.clear(),t(null),window.dispatchEvent(new Event("userDataUpdated"))},fetchAndUpdateUser:f,deleteProfilePicture:p,loading:a,error:n}};var p=a(58983),v=()=>{var e;let{user:t}=f(),a=(0,o.useRouter)(),[n,c]=(0,l.useState)(Date.now());(0,l.useEffect)(()=>{c(Date.now())},[t]);let u=async()=>{a.push("/developer/login")};return(0,r.jsxs)(d.h_,{children:[(0,r.jsx)(d.$F,{asChild:!0,children:(0,r.jsxs)("button",{type:"button",className:"flex items-center rounded-full p-1 hover:bg-subtle",children:[t&&(0,r.jsx)("div",{className:"border rounded-full",children:(0,r.jsxs)(i.qE,{className:"size-8 sm:size-10",children:[(0,r.jsx)(i.F$,{src:(()=>{if(!(null==t?void 0:t.avatar_url))return"";let e=t.avatar_url.includes("?")?"&":"?";return"".concat(t.avatar_url).concat(e,"t=").concat(n)})()}),(0,r.jsx)(i.Q5,{className:"bg-primary/30 uppercase",children:(t.full_name||t.username||"").split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2)})]})}),(0,r.jsx)(s.Z,{"data-testid":"chevronDown",className:(0,p.cn)("size-4 text-neutral-dark-2 sm:size-5")})]})}),(0,r.jsxs)(d.AW,{className:"mr-1 w-56",align:"end",children:[(0,r.jsx)(d.Ju,{className:"pb-0 pt-3 text-center",children:null==t?void 0:t.full_name}),(0,r.jsx)("span",{className:"block px-2 pb-1 text-xs text-neutral-dark-1 text-center",children:null!==(e=null==t?void 0:t.email)&&void 0!==e?e:"Signed In"}),(0,r.jsx)(d.VD,{}),(0,r.jsx)(d.VD,{}),(0,r.jsxs)(d.Xi,{onClick:u,className:"cursor-pointer",children:[(0,r.jsx)("span",{className:"font-medium",children:"Log out"}),(0,r.jsx)(d.KM,{children:"⇧Q"})]})]})]})};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let h=(0,a(9824).Z)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]]);var g=a(39713),j=a(11492),b=a(13352);let N=e=>{let[t,a]=(0,l.useState)(!1),[s,o]=(0,l.useState)(!1),[i,d]=(0,l.useState)(""),[c,u]=(0,l.useState)(""),[m,f]=(0,l.useState)("");(0,l.useEffect)(()=>{let e=localStorage.getItem("token")||"",t=localStorage.getItem("orgId")||"";u(e),f(t)},[c]);let p=async t=>{if(t.preventDefault(),!i){b.Z.error("You must provide a valid JSON URL!!");return}a(!0);let r=await (0,x.xo)("/organisations/".concat(m,"/agents"),{json_url:i},c);((null==r?void 0:r.status)===200||(null==r?void 0:r.status)===201)&&(e.closeModal(),b.Z.success("Agent created successfully")),a(!1)},v=e=>{o(e)},h=async e=>{e.preventDefault()};return(0,r.jsxs)("div",{className:"w-screen h-screen fixed top-0 right-0 flex items-center justify-center",children:[(0,r.jsx)("div",{onClick:()=>e.closeModal(),className:"w-full h-full absolute bg-black opacity-20"}),(0,r.jsxs)("div",{className:"bg-white w-[400px] lg:w-[600px] flex flex-col rounded-xl  z-10 gap-8",children:[(0,r.jsxs)("div",{className:"border-b border-gray-200 p-6 pb-4",children:[(0,r.jsx)("h1",{className:"text-[#1D2939] lg:text-xl text-lg font-semibold leading-normal",children:"Create an Agent"}),(0,r.jsxs)("div",{className:"flex gap-4 mt-4",children:[(0,r.jsx)("button",{onClick:()=>v(!1),className:"text-sm font-medium ".concat(s?"text-[#1D2939]":"border-b-2 border-[#4A90E2] text-[#4A90E2]"," pb-2 "),children:"JSON URL"}),(0,r.jsx)("button",{onClick:()=>v(!0),className:"text-sm font-medium pb-2 ".concat(s?"border-b-2 border-[#4A90E2] text-[#4A90E2]":"text-[#1D2939]"),children:"Manual Data"})]})]}),s?(0,r.jsxs)("form",{onSubmit:h,className:"w-full px-5",children:[(0,r.jsx)("div",{className:"flex flex-col gap-[16px] mb-3",children:(0,r.jsxs)("div",{className:"w-full flex flex-col gap-[8px] relative",children:[(0,r.jsx)("label",{htmlFor:"name",className:"text-[14px] font-[400] leading-[21px]",children:"Agent Name"}),(0,r.jsx)("div",{className:"w-full flex flex-col gap-[2px]",children:(0,r.jsx)("input",{id:"name",placeholder:"Enter agent name",className:"w-full text-[12px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border border-[#D0D0FD]\n                                        outline-none rounded-md py-[13px] pl-[13px]"})})]})}),(0,r.jsx)("div",{className:"flex flex-col gap-[16px] mb-3",children:(0,r.jsxs)("div",{className:"w-full flex flex-col gap-[8px] relative",children:[(0,r.jsx)("label",{htmlFor:"provider",className:"text-[14px] font-[400] leading-[21px]",children:"Provider"}),(0,r.jsx)("div",{className:"w-full flex flex-col gap-[2px]",children:(0,r.jsx)("input",{id:"provider",placeholder:"Enter agent provider",className:"w-full text-[12px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border border-[#D0D0FD]\n                                        outline-none rounded-md py-[13px] pl-[13px]"})})]})}),(0,r.jsx)("div",{className:"flex flex-col gap-[16px] mb-3",children:(0,r.jsxs)("div",{className:"w-full flex flex-col gap-[8px] relative",children:[(0,r.jsx)("label",{htmlFor:"category",className:"text-[14px] font-[400] leading-[21px]",children:"Category"}),(0,r.jsx)("div",{className:"w-full flex flex-col gap-[2px]",children:(0,r.jsx)("input",{id:"category",placeholder:"Enter agent category",className:"w-full text-[12px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border border-[#D0D0FD]\n                                        outline-none rounded-md py-[13px] pl-[13px]"})})]})}),(0,r.jsx)("div",{className:"flex flex-col gap-[16px] mb-3",children:(0,r.jsxs)("div",{className:"w-full flex flex-col gap-[8px] relative",children:[(0,r.jsx)("label",{htmlFor:"description",className:"text-[14px] font-[400] leading-[21px]",children:"Description"}),(0,r.jsx)("div",{className:"w-full flex flex-col gap-[2px]",children:(0,r.jsx)("textarea",{name:"",id:"description",cols:100,placeholder:"Enter agent description",className:"w-full text-[12px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border border-[#D0D0FD]\n                                        outline-none rounded-md py-[13px] pl-[13px]"})})]})}),(0,r.jsx)("div",{className:"flex flex-col gap-[24px] mt-5 mb-10",children:(0,r.jsx)("div",{className:"flex flex-col gap-[16px]",children:(0,r.jsx)(j.z,{type:"submit",variant:"default",className:"py-6 bg-[#7141F8] hover:bg-[#8760f8] text-white",children:t?(0,r.jsxs)("span",{className:"flex items-center gap-x-2",children:[(0,r.jsx)("span",{className:"animate-pulse",children:"Processing..."})," ",(0,r.jsx)(n.Z,{width:"20",height:"40"})]}):(0,r.jsx)("span",{children:"Create an Agent"})})})})]}):(0,r.jsxs)("form",{onSubmit:p,className:"w-full px-5",children:[(0,r.jsx)("div",{className:"flex flex-col gap-[16px]",children:(0,r.jsxs)("div",{className:"w-full flex flex-col gap-[8px] relative",children:[(0,r.jsx)("label",{htmlFor:"url",className:"text-[14px] font-[400] leading-[21px]",children:"JSON URL"}),(0,r.jsx)("div",{className:"w-full flex flex-col gap-[2px]",children:(0,r.jsx)("input",{value:i,onChange:e=>{d(e.target.value)},id:"url",type:"url",placeholder:"Enter agent json url",className:"w-full text-[12px] text-[#667085] leading-[15.12px] font-[500] h-[48px] border border-[#D0D0FD]\n                                        outline-none rounded-md py-[13px] pl-[13px]"})})]})}),(0,r.jsx)("div",{className:"flex flex-col gap-[24px] mt-5 mb-10 pb-5",children:(0,r.jsx)("div",{className:"flex flex-col gap-[16px]",children:(0,r.jsx)(j.z,{type:"submit",variant:"default",className:"py-6 bg-[#7141F8] hover:bg-[#8760f8] text-white",children:t?(0,r.jsxs)("span",{className:"flex items-center gap-x-2",children:[(0,r.jsx)("span",{className:"animate-pulse",children:"Processing..."})," ",(0,r.jsx)(n.Z,{width:"20",height:"40"})]}):(0,r.jsx)("span",{children:"Create an Agent"})})})})]})]})]})},y=e=>{var t,a,l,n,s,o,i,d,c,u,x,m;return(0,r.jsxs)("div",{className:"w-screen h-screen fixed top-0 right-0 flex items-center justify-center",children:[(0,r.jsx)("div",{onClick:()=>e.closeModal(),className:"w-full h-full absolute bg-black opacity-20"}),(0,r.jsx)("div",{className:"bg-white w-[400px] lg:w-[600px] flex flex-col rounded-xl  z-10 gap-8",children:(0,r.jsxs)("div",{className:"border-b border-gray-200 p-6 pb-4",children:[(0,r.jsx)("h1",{className:"text-[#1D2939] border-b border-gray-200 lg:text-xl text-lg pb-5 mb-3 font-semibold leading-normal",children:"Agent Details"}),(0,r.jsxs)("div",{className:"w-full agent_container max-h-[70vh] overflow-y-auto no-scrollbar",children:[(0,r.jsx)("div",{className:"border-b border-gray-200 pb-5",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("h1",{className:"text-base",children:[(0,r.jsx)("strong",{children:"Name"}),": ",e.agent.app_name]}),(0,r.jsxs)("h1",{className:"text-base",children:[(0,r.jsx)("strong",{children:"Organization"}),":"," ",null===(a=e.agent)||void 0===a?void 0:null===(t=a.provider)||void 0===t?void 0:t.organization]}),(0,r.jsxs)("p",{className:"text-base",children:[(0,r.jsx)("strong",{children:"Agent Key"}),":"," ",(null===(l=e.agent)||void 0===l?void 0:l.preshared_key)?e.agent.preshared_key:"Not Available"]}),(0,r.jsxs)("p",{className:"text-base",children:[(0,r.jsx)("strong",{children:"Status"}),":"," ",(null===(n=e.agent)||void 0===n?void 0:n.is_active)?"Active":"Inactive"]}),(0,r.jsxs)("p",{className:"text-base",children:[(0,r.jsx)("strong",{children:"URL"}),": ",e.agent.json_url]}),(0,r.jsxs)("p",{className:"text-base",children:[(0,r.jsx)("strong",{children:"Paid"}),": ",e.agent.is_paid?"Paid":"Free"]}),(0,r.jsxs)("p",{className:"text-base",children:[(0,r.jsx)("strong",{children:"Approved"}),":"," ",(null===(s=e.agent)||void 0===s?void 0:s.is_approved)?"Approved":"Not Approved"]}),(0,r.jsxs)("p",{className:"text-base",children:[(0,r.jsx)("strong",{children:"Version"}),":"," ",(null===(o=e.agent)||void 0===o?void 0:o.version)?null===(i=e.agent)||void 0===i?void 0:i.version:"1.0.0"]})]})}),(0,r.jsxs)("div",{className:"border-b border-gray-200 mt-5",children:[(0,r.jsx)("strong",{className:"mt-3",children:"Description"}),(0,r.jsx)("p",{className:"text-[rgba(110,110,111,1)] mb-3",children:null===(d=e.agent)||void 0===d?void 0:d.app_description})]}),(0,r.jsxs)("div",{className:"border-b border-gray-200 mt-5 pb-5",children:[(0,r.jsx)("strong",{className:"lg:text-xl text-lg",children:"Agent Prices"}),null===(u=e.agent)||void 0===u?void 0:null===(c=u.prices)||void 0===c?void 0:c.map((e,t)=>(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("p",{className:"text-base mt-5",children:[(0,r.jsx)("strong",{children:"Amount"}),": ",e.amount]}),(0,r.jsxs)("p",{className:"text-base",children:[(0,r.jsx)("strong",{children:"Currency Code"}),": ",e.currency]}),(0,r.jsxs)("p",{className:"text-base",children:[(0,r.jsx)("strong",{children:"Operation Type"}),": ",e.operation_type]})]},t))]}),(0,r.jsxs)("div",{className:"border-b border-gray-200 mt-5 pb-5 mb-5",children:[(0,r.jsx)("strong",{className:"lg:text-xl text-lg",children:"Agent Skills"}),null===(m=e.agent)||void 0===m?void 0:null===(x=m.skills)||void 0===x?void 0:x.map((e,t)=>{var a;return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("p",{className:"text-base mt-5",children:[(0,r.jsx)("strong",{children:"Name"}),": ",e.name]}),(0,r.jsxs)("p",{className:"text-base",children:[(0,r.jsx)("strong",{children:"Tags"}),": ",null===(a=e.tags)||void 0===a?void 0:a.join(", ")]}),(0,r.jsxs)("p",{className:"text-base",children:[(0,r.jsx)("strong",{children:"Description"}),": ",e.description]})]},t)})]})]})]})})]})};var w=function(){let[e,t]=(0,l.useState)(!0),[a,s]=(0,l.useState)(!1),[o,i]=(0,l.useState)([]),[d,c]=(0,l.useState)(null),[u,m]=(0,l.useState)(!1),f=e=>{c(e),m(!0)};(0,l.useEffect)(()=>{let e=localStorage.getItem("token")||"";(async()=>{let a=await (0,x.Gl)("/agents/me",e);if((null==a?void 0:a.status)===200){var r;i(null==a?void 0:null===(r=a.data)||void 0===r?void 0:r.data)}else i([]);t(!1)})()},[]);let p=()=>{s(!0)};return(0,r.jsxs)(r.Fragment,{children:[a&&(0,r.jsx)(N,{closeModal:()=>{s(!1)}}),u&&d&&(0,r.jsx)(y,{agent:d,closeModal:()=>{m(!1),c(null)}}),(0,r.jsxs)("div",{className:"mx-auto xl:max-w-[1440px] grow max-w-screen",children:[(0,r.jsxs)("div",{className:"p-3 flex items-center justify-between",children:[(0,r.jsx)("div",{className:"flex flex-col mt-6 mb-10",children:(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(g.default,{src:"/TelexIcon.svg",alt:"Icon",width:40,height:40}),(0,r.jsx)("h1",{className:"font-semibold text-4xl text-center flex justify-center items-center max-lg:text-2xl",children:"Telex Developer"})]})}),(0,r.jsx)(v,{})]}),(0,r.jsxs)("main",{className:"w-full pb-6 px-6 md:pt-3 md:pb-16",children:[e&&(0,r.jsx)("div",{className:"flex items-center justify-center mt-20",children:(0,r.jsx)(n.Z,{width:"50",height:"50",color:"#6B46FF"})}),0!=o.length||e?(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4",children:[o.map((e,t)=>(0,r.jsxs)("div",{onClick:()=>f(e),className:"border border-gray-300 p-4 rounded hover:bg-gray-100 cursor-pointer transition-colors",children:[(0,r.jsx)("h1",{className:"font-bold",children:e.app_name}),(0,r.jsx)("p",{children:e.is_active?"Active":"Inactive"}),(0,r.jsx)("a",{href:e.json_url,className:"mt-3",children:e.json_url}),(0,r.jsx)("p",{className:"text-[rgba(110,110,111,1)] mt-5",children:e.app_description})]},t)),!e&&(0,r.jsxs)("div",{onClick:p,className:"flex flex-col group  items-center justify-center border border-gray-300 cursor-pointer p-4 text-center rounded hover:bg-gray-100 transition-colors",children:[(0,r.jsx)("h3",{className:"font-bold",children:"Create an Agent"}),(0,r.jsx)("p",{className:"text-[rgba(110,110,111,1)]",children:"Create a new organisational agent"}),(0,r.jsx)(h,{className:"group-hover:fill-white group-hover:stroke-[#7b50fb] mt-5"})]})]})}):(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:"w-full",children:(0,r.jsxs)("section",{className:"w-full max-w-2xl px-5 mx-auto",children:[(0,r.jsx)("div",{className:"flex flex-col justify-center items-center mt-20",children:(0,r.jsxs)("div",{className:"flex gap-2 justify-center",children:[(0,r.jsx)(g.default,{src:"/TelexIcon.svg",alt:"Icon",width:40,height:40}),(0,r.jsx)("h1",{className:"font-semibold text-4xl text-center flex justify-center items-center max-lg:text-2xl",children:"Telex"})]})}),(0,r.jsxs)("div",{className:"mt-[50px] text-center",children:[(0,r.jsx)("h1",{className:"font-semibold mb-3 leading-8 text-[46px] max-lg:text-3xl max-lg:font-bold",children:"Welcome to Telex Developer"}),(0,r.jsx)("p",{className:"my-8 text-center text-md md:text-lg text-balance text-[rgba(110,110,111,1)]",children:"Seamlessly create and manage intelligent agents to power real-time automation, communication, and integration across your organization."}),(0,r.jsx)("div",{children:(0,r.jsx)(j.z,{onClick:p,className:" bg-blue-400 w-[180px] py-6 px-10 text-white text-base font-medium hover hover:bg-blue-300",children:"Create an Agent"})}),(0,r.jsx)("p",{className:"text-sm md:text-md text-left sm:text-center text-[rgba(110,110,111,1)] my-6 lg:w-[450px] mx-auto",children:"By continuing, you are agreeing to our Privacy Policy, Main Service Agreement, Terms of Service, and Cookie Policy."})]})]})})})]})]})]})}},89564:function(e,t,a){"use strict";a.d(t,{F$:function(){return i},Q5:function(){return d},qE:function(){return o}});var r=a(75376),l=a(34450),n=a(32486),s=a(58983);let o=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(l.fC,{ref:t,className:(0,s.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",a),...n})});o.displayName=l.fC.displayName;let i=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(l.Ee,{ref:t,className:(0,s.cn)("aspect-square h-full w-full",a),...n})});i.displayName=l.Ee.displayName;let d=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(l.NY,{ref:t,className:(0,s.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",a),...n})});d.displayName=l.NY.displayName},11492:function(e,t,a){"use strict";a.d(t,{d:function(){return i},z:function(){return d}});var r=a(75376),l=a(32486),n=a(91007),s=a(53447),o=a(58983);let i=(0,s.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=l.forwardRef((e,t)=>{let{className:a,variant:l,size:s,asChild:d=!1,...c}=e,u=d?n.g7:"button";return(0,r.jsx)(u,{className:(0,o.cn)(i({variant:l,size:s,className:a})),ref:t,...c})});d.displayName="Button"},25368:function(e,t,a){"use strict";a.d(t,{$F:function(){return c},AW:function(){return x},Ju:function(){return p},KM:function(){return h},VD:function(){return v},Xi:function(){return m},_x:function(){return u},h_:function(){return d},qB:function(){return f}});var r=a(75376),l=a(32486),n=a(18493),s=a(51888),o=a(28780),i=a(58983);let d=n.fC,c=n.xz;n.ZA,n.Uv,n.Tr;let u=n.Ee;l.forwardRef((e,t)=>{let{className:a,inset:l,children:o,...d}=e;return(0,r.jsxs)(n.fF,{ref:t,className:(0,i.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",l&&"pl-8",a),...d,children:[o,(0,r.jsx)(s.Z,{className:"ml-auto h-4 w-4"})]})}).displayName=n.fF.displayName,l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,r.jsx)(n.tu,{ref:t,className:(0,i.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...l})}).displayName=n.tu.displayName;let x=l.forwardRef((e,t)=>{let{className:a,sideOffset:l=4,...s}=e;return(0,r.jsx)(n.Uv,{children:(0,r.jsx)(n.VY,{ref:t,sideOffset:l,className:(0,i.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...s})})});x.displayName=n.VY.displayName;let m=l.forwardRef((e,t)=>{let{className:a,inset:l,...s}=e;return(0,r.jsx)(n.ck,{ref:t,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",l&&"pl-8",a),...s})});m.displayName=n.ck.displayName,l.forwardRef((e,t)=>{let{className:a,children:l,checked:s,...d}=e;return(0,r.jsxs)(n.oC,{ref:t,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:s,...d,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(n.wU,{children:(0,r.jsx)(o.Z,{className:"h-4 w-4"})})}),l]})}).displayName=n.oC.displayName;let f=l.forwardRef((e,t)=>{let{className:a,children:l,...s}=e;return(0,r.jsxs)(n.Rk,{ref:t,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...s,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(n.wU,{children:(0,r.jsx)("div",{className:"bg-gradient-to-b from-[#8860F8] to-[#7141F8] w-5 h-5 rounded-sm flex items-center justify-center",children:(0,r.jsx)(o.Z,{color:"#ffffff"})})})}),l]})});f.displayName=n.Rk.displayName;let p=l.forwardRef((e,t)=>{let{className:a,inset:l,...s}=e;return(0,r.jsx)(n.__,{ref:t,className:(0,i.cn)("px-2 py-1.5 text-sm font-semibold",l&&"pl-8",a),...s})});p.displayName=n.__.displayName;let v=l.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,r.jsx)(n.Z0,{ref:t,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",a),...l})});v.displayName=n.Z0.displayName;let h=e=>{let{className:t,...a}=e;return(0,r.jsx)("span",{className:(0,i.cn)("ml-auto text-xs tracking-widest opacity-60",t),...a})};h.displayName="DropdownMenuShortcut"},73003:function(e,t,a){"use strict";var r=a(75376);a(32486);var l=a(10983);t.Z=e=>{let{height:t,width:a,color:n}=e;return(0,r.jsx)(l.iT,{height:t||20,width:a||20,color:n||"#fff",visible:!0,ariaLabel:"oval-loading",secondaryColor:n||"#fff",strokeWidth:5,strokeWidthSecondary:5})}},58983:function(e,t,a){"use strict";a.d(t,{cn:function(){return n},k:function(){return s}});var r=a(89824),l=a(97215);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,l.m6)((0,r.W)(t))}let s=e=>e<50?"/Progress-success.svg":e>=50&&e<70?"/Progress-warning.svg":"/Progress-danger.svg"},10952:function(e,t,a){"use strict";a.d(t,{Gl:function(){return o},an:function(){return d},jx:function(){return c},xo:function(){return i}});var r=a(20818),l=a(13352),n=a(18648);let s=n.env.NEXT_PUBLIC_BASE_URL;n.env.NEXT_PUBLIC_INTEGRATION_URL;let o=async(e,t)=>{try{return await r.Z.get(s+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var a,l,n;return((null==e?void 0:null===(a=e.response)||void 0===a?void 0:a.status)===401||(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(l=n.data)||void 0===l?void 0:l.status_code)===401)&&(localStorage.clear(),window.location.href="/auth/login"),e}},i=async(e,t,a)=>{try{return await r.Z.post(s+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var n,o,i,d,c;return l.Z.error(null==e?void 0:null===(o=e.response)||void 0===o?void 0:null===(n=o.data)||void 0===n?void 0:n.message),(null==e?void 0:null===(i=e.response)||void 0===i?void 0:i.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},d=async(e,t,a)=>{try{return await r.Z.put(s+e,t,{headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}})}catch(e){var n,o,i,d,c;return l.Z.error(null==e?void 0:null===(o=e.response)||void 0===o?void 0:null===(n=o.data)||void 0===n?void 0:n.message),(null==e?void 0:null===(i=e.response)||void 0===i?void 0:i.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(c=e.response)||void 0===c?void 0:null===(d=c.data)||void 0===d?void 0:d.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}},c=async(e,t)=>{try{return await r.Z.delete(s+e,{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}})}catch(e){var a,n,o,i,d;return l.Z.error(null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(a=n.data)||void 0===a?void 0:a.message),(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)===401&&(localStorage.clear(),window.location.href="/auth/login"),(null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(i=d.data)||void 0===i?void 0:i.status_code)===401&&(localStorage.clear(),window.location.href="/auth/login"),e}}},34450:function(e,t,a){"use strict";a.d(t,{NY:function(){return S},Ee:function(){return w},fC:function(){return y}});var r=a(32486),l=a(32192),n=a(15920),s=a(79315),o=a(89801),i=a(37286);function d(){return()=>{}}var c=a(75376),u="Avatar",[x,m]=(0,l.b)(u),[f,p]=x(u),v=r.forwardRef((e,t)=>{let{__scopeAvatar:a,...l}=e,[n,s]=r.useState("idle");return(0,c.jsx)(f,{scope:a,imageLoadingStatus:n,onImageLoadingStatusChange:s,children:(0,c.jsx)(o.WV.span,{...l,ref:t})})});v.displayName=u;var h="AvatarImage",g=r.forwardRef((e,t)=>{let{__scopeAvatar:a,src:l,onLoadingStatusChange:u=()=>{},...x}=e,m=p(h,a),f=function(e,t){let{referrerPolicy:a,crossOrigin:l}=t,n=(0,i.useSyncExternalStore)(d,()=>!0,()=>!1),o=r.useRef(null),c=n?(o.current||(o.current=new window.Image),o.current):null,[u,x]=r.useState(()=>N(c,e));return(0,s.b)(()=>{x(N(c,e))},[c,e]),(0,s.b)(()=>{let e=e=>()=>{x(e)};if(!c)return;let t=e("loaded"),r=e("error");return c.addEventListener("load",t),c.addEventListener("error",r),a&&(c.referrerPolicy=a),"string"==typeof l&&(c.crossOrigin=l),()=>{c.removeEventListener("load",t),c.removeEventListener("error",r)}},[c,l,a]),u}(l,x),v=(0,n.W)(e=>{u(e),m.onImageLoadingStatusChange(e)});return(0,s.b)(()=>{"idle"!==f&&v(f)},[f,v]),"loaded"===f?(0,c.jsx)(o.WV.img,{...x,ref:t,src:l}):null});g.displayName=h;var j="AvatarFallback",b=r.forwardRef((e,t)=>{let{__scopeAvatar:a,delayMs:l,...n}=e,s=p(j,a),[i,d]=r.useState(void 0===l);return r.useEffect(()=>{if(void 0!==l){let e=window.setTimeout(()=>d(!0),l);return()=>window.clearTimeout(e)}},[l]),i&&"loaded"!==s.imageLoadingStatus?(0,c.jsx)(o.WV.span,{...n,ref:t}):null});function N(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}b.displayName=j;var y=v,w=g,S=b},53447:function(e,t,a){"use strict";a.d(t,{j:function(){return s}});var r=a(89824);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=r.W,s=(e,t)=>a=>{var r;if((null==t?void 0:t.variants)==null)return n(e,null==a?void 0:a.class,null==a?void 0:a.className);let{variants:s,defaultVariants:o}=t,i=Object.keys(s).map(e=>{let t=null==a?void 0:a[e],r=null==o?void 0:o[e];if(null===t)return null;let n=l(t)||l(r);return s[e][n]}),d=a&&Object.entries(a).reduce((e,t)=>{let[a,r]=t;return void 0===r||(e[a]=r),e},{});return n(e,i,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:a,className:r,...l}=t;return Object.entries(l).every(e=>{let[t,a]=e;return Array.isArray(a)?a.includes({...o,...d}[t]):({...o,...d})[t]===a})?[...e,a,r]:e},[]),null==a?void 0:a.class,null==a?void 0:a.className)}}},function(e){e.O(0,[4269,8863,7140,4144,5300,5614,2987,6343,6329,9254,3975,7220,7542,2344,1744],function(){return e(e.s=53662)}),_N_E=e.O()}]);