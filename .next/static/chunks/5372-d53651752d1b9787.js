(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5372],{39713:function(e,t,i){"use strict";i.d(t,{default:function(){return r.a}});var s=i(74033),r=i.n(s)},1044:function(){},74033:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return o},getImageProps:function(){return n}});let s=i(60723),r=i(25738),l=i(28863),a=s._(i(44543));function n(e){let{props:t}=(0,r.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,i]of Object.entries(t))void 0===i&&delete t[e];return{props:t}}let o=l.Image},58485:function(e,t,i){"use strict";var s=i(18648);i(1044);var r=i(32486),l=r&&"object"==typeof r&&"default"in r?r:{default:r},a=void 0!==s&&s.env&&!0,n=function(e){return"[object String]"===Object.prototype.toString.call(e)},o=function(){function e(e){var t=void 0===e?{}:e,i=t.name,s=void 0===i?"stylesheet":i,r=t.optimizeForSpeed,l=void 0===r?a:r;d(n(s),"`name` must be a string"),this._name=s,this._deletedRulePlaceholder="#"+s+"-deleted-rule____{}",d("boolean"==typeof l,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=l,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var o="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=o?o.getAttribute("content"):null}var t=e.prototype;return t.setOptimizeForSpeed=function(e){d("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),d(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},t.isOptimizeForSpeed=function(){return this._optimizeForSpeed},t.inject=function(){var e=this;if(d(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(a||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,i){return"number"==typeof i?e._serverSheet.cssRules[i]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),i},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},t.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},t.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},t.insertRule=function(e,t){if(d(n(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var i=this.getSheet();"number"!=typeof t&&(t=i.cssRules.length);try{i.insertRule(e,t)}catch(t){return a||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var s=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,s))}return this._rulesCount++},t.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var i="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!i.cssRules[e])return e;i.deleteRule(e);try{i.insertRule(t,e)}catch(s){a||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),i.insertRule(this._deletedRulePlaceholder,e)}}else{var s=this._tags[e];d(s,"old rule at index `"+e+"` not found"),s.textContent=t}return e},t.deleteRule=function(e){if("undefined"==typeof window){this._serverSheet.deleteRule(e);return}if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];d(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},t.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},t.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,i){return i?t=t.concat(Array.prototype.map.call(e.getSheetForTag(i).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},t.makeStyleTag=function(e,t,i){t&&d(n(t),"makeStyleTag accepts only strings as second parameter");var s=document.createElement("style");this._nonce&&s.setAttribute("nonce",this._nonce),s.type="text/css",s.setAttribute("data-"+e,""),t&&s.appendChild(document.createTextNode(t));var r=document.head||document.getElementsByTagName("head")[0];return i?r.insertBefore(s,i):r.appendChild(s),s},function(e,t){for(var i=0;i<t.length;i++){var s=t[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}(e.prototype,[{key:"length",get:function(){return this._rulesCount}}]),e}();function d(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var c=function(e){for(var t=5381,i=e.length;i;)t=33*t^e.charCodeAt(--i);return t>>>0},u={};function p(e,t){if(!t)return"jsx-"+e;var i=String(t),s=e+i;return u[s]||(u[s]="jsx-"+c(e+"-"+i)),u[s]}function h(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var i=e+t;return u[i]||(u[i]=t.replace(/__jsx-style-dynamic-selector/g,e)),u[i]}var f=function(){function e(e){var t=void 0===e?{}:e,i=t.styleSheet,s=void 0===i?null:i,r=t.optimizeForSpeed,l=void 0!==r&&r;this._sheet=s||new o({name:"styled-jsx",optimizeForSpeed:l}),this._sheet.inject(),s&&"boolean"==typeof l&&(this._sheet.setOptimizeForSpeed(l),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var i=this.getIdAndRules(e),s=i.styleId,r=i.rules;if(s in this._instancesCounts){this._instancesCounts[s]+=1;return}var l=r.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[s]=l,this._instancesCounts[s]=1},t.remove=function(e){var t=this,i=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(i in this._instancesCounts,"styleId: `"+i+"` not found"),this._instancesCounts[i]-=1,this._instancesCounts[i]<1){var s=this._fromServer&&this._fromServer[i];s?(s.parentNode.removeChild(s),delete this._fromServer[i]):(this._indices[i].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[i]),delete this._instancesCounts[i]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],i=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return i[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,i;return t=this.cssRules(),void 0===(i=e)&&(i={}),t.map(function(e){var t=e[0],s=e[1];return l.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:i.nonce?i.nonce:void 0,dangerouslySetInnerHTML:{__html:s}})})},t.getIdAndRules=function(e){var t=e.children,i=e.dynamic,s=e.id;if(i){var r=p(s,i);return{styleId:r,rules:Array.isArray(t)?t.map(function(e){return h(r,e)}):[h(r,t)]}}return{styleId:p(s),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=r.createContext(null);m.displayName="StyleSheetContext";var g=l.default.useInsertionEffect||l.default.useLayoutEffect,v="undefined"!=typeof window?new f:void 0;function b(e){var t=v||r.useContext(m);return t&&("undefined"==typeof window?t.add(e):g(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}b.dynamic=function(e){return e.map(function(e){return p(e[0],e[1])}).join(" ")},t.style=b},91167:function(e,t,i){"use strict";e.exports=i(58485).style},57788:function(){},35224:function(){},44819:function(e,t,i){"use strict";i.d(t,{W_:function(){return a},tl:function(){return o},LW:function(){return d}});var s=i(1e3),r=i(96763);function l(e,t,i,s){return e.params.createElements&&Object.keys(s).forEach(l=>{if(!i[l]&&!0===i.auto){let a=(0,r.e)(e.el,`.${s[l]}`)[0];a||((a=(0,r.c)("div",s[l])).className=s[l],e.el.append(a)),i[l]=a,t[l]=a}}),i}function a(e){let{swiper:t,extendParams:i,on:s,emit:a}=e;function n(e){let i;return e&&"string"==typeof e&&t.isElement&&(i=t.el.querySelector(e)||t.hostEl.querySelector(e))?i:(e&&("string"==typeof e&&(i=[...document.querySelectorAll(e)]),t.params.uniqueNavElements&&"string"==typeof e&&i&&i.length>1&&1===t.el.querySelectorAll(e).length?i=t.el.querySelector(e):i&&1===i.length&&(i=i[0])),e&&!i)?e:i}function o(e,i){let s=t.params.navigation;(e=(0,r.m)(e)).forEach(e=>{e&&(e.classList[i?"add":"remove"](...s.disabledClass.split(" ")),"BUTTON"===e.tagName&&(e.disabled=i),t.params.watchOverflow&&t.enabled&&e.classList[t.isLocked?"add":"remove"](s.lockClass))})}function d(){let{nextEl:e,prevEl:i}=t.navigation;if(t.params.loop){o(i,!1),o(e,!1);return}o(i,t.isBeginning&&!t.params.rewind),o(e,t.isEnd&&!t.params.rewind)}function c(e){e.preventDefault(),(!t.isBeginning||t.params.loop||t.params.rewind)&&(t.slidePrev(),a("navigationPrev"))}function u(e){e.preventDefault(),(!t.isEnd||t.params.loop||t.params.rewind)&&(t.slideNext(),a("navigationNext"))}function p(){let e=t.params.navigation;if(t.params.navigation=l(t,t.originalParams.navigation,t.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(e.nextEl||e.prevEl))return;let i=n(e.nextEl),s=n(e.prevEl);Object.assign(t.navigation,{nextEl:i,prevEl:s}),i=(0,r.m)(i),s=(0,r.m)(s);let a=(i,s)=>{i&&i.addEventListener("click","next"===s?u:c),!t.enabled&&i&&i.classList.add(...e.lockClass.split(" "))};i.forEach(e=>a(e,"next")),s.forEach(e=>a(e,"prev"))}function h(){let{nextEl:e,prevEl:i}=t.navigation;e=(0,r.m)(e),i=(0,r.m)(i);let s=(e,i)=>{e.removeEventListener("click","next"===i?u:c),e.classList.remove(...t.params.navigation.disabledClass.split(" "))};e.forEach(e=>s(e,"next")),i.forEach(e=>s(e,"prev"))}i({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),t.navigation={nextEl:null,prevEl:null},s("init",()=>{!1===t.params.navigation.enabled?f():(p(),d())}),s("toEdge fromEdge lock unlock",()=>{d()}),s("destroy",()=>{h()}),s("enable disable",()=>{let{nextEl:e,prevEl:i}=t.navigation;if(e=(0,r.m)(e),i=(0,r.m)(i),t.enabled){d();return}[...e,...i].filter(e=>!!e).forEach(e=>e.classList.add(t.params.navigation.lockClass))}),s("click",(e,i)=>{let{nextEl:s,prevEl:l}=t.navigation;s=(0,r.m)(s),l=(0,r.m)(l);let n=i.target,o=l.includes(n)||s.includes(n);if(t.isElement&&!o){let e=i.path||i.composedPath&&i.composedPath();e&&(o=e.find(e=>s.includes(e)||l.includes(e)))}if(t.params.navigation.hideOnClick&&!o){let e;if(t.pagination&&t.params.pagination&&t.params.pagination.clickable&&(t.pagination.el===n||t.pagination.el.contains(n)))return;s.length?e=s[0].classList.contains(t.params.navigation.hiddenClass):l.length&&(e=l[0].classList.contains(t.params.navigation.hiddenClass)),!0===e?a("navigationShow"):a("navigationHide"),[...s,...l].filter(e=>!!e).forEach(e=>e.classList.toggle(t.params.navigation.hiddenClass))}});let f=()=>{t.el.classList.add(...t.params.navigation.navigationDisabledClass.split(" ")),h()};Object.assign(t.navigation,{enable:()=>{t.el.classList.remove(...t.params.navigation.navigationDisabledClass.split(" ")),p(),d()},disable:f,update:d,init:p,destroy:h})}function n(e){return void 0===e&&(e=""),`.${e.trim().replace(/([\.:!+\/()[\]])/g,"\\$1").replace(/ /g,".")}`}function o(e){let t,{swiper:i,extendParams:s,on:a,emit:o}=e,d="swiper-pagination";s({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=>e,formatFractionTotal:e=>e,bulletClass:`${d}-bullet`,bulletActiveClass:`${d}-bullet-active`,modifierClass:`${d}-`,currentClass:`${d}-current`,totalClass:`${d}-total`,hiddenClass:`${d}-hidden`,progressbarFillClass:`${d}-progressbar-fill`,progressbarOppositeClass:`${d}-progressbar-opposite`,clickableClass:`${d}-clickable`,lockClass:`${d}-lock`,horizontalClass:`${d}-horizontal`,verticalClass:`${d}-vertical`,paginationDisabledClass:`${d}-disabled`}}),i.pagination={el:null,bullets:[]};let c=0;function u(){return!i.params.pagination.el||!i.pagination.el||Array.isArray(i.pagination.el)&&0===i.pagination.el.length}function p(e,t){let{bulletActiveClass:s}=i.params.pagination;e&&(e=e[`${"prev"===t?"previous":"next"}ElementSibling`])&&(e.classList.add(`${s}-${t}`),(e=e[`${"prev"===t?"previous":"next"}ElementSibling`])&&e.classList.add(`${s}-${t}-${t}`))}function h(e){let t=e.target.closest(n(i.params.pagination.bulletClass));if(!t)return;e.preventDefault();let s=(0,r.i)(t)*i.params.slidesPerGroup;if(i.params.loop){var l,a,o;if(i.realIndex===s)return;let e=(l=i.realIndex,a=s,(l%=o=i.slides.length,(a%=o)===l+1)?"next":a===l-1?"previous":void 0);"next"===e?i.slideNext():"previous"===e?i.slidePrev():i.slideToLoop(s)}else i.slideTo(s)}function f(){let e,s;let l=i.rtl,a=i.params.pagination;if(u())return;let d=i.pagination.el;d=(0,r.m)(d);let h=i.virtual&&i.params.virtual.enabled?i.virtual.slides.length:i.slides.length,f=i.params.loop?Math.ceil(h/i.params.slidesPerGroup):i.snapGrid.length;if(i.params.loop?(s=i.previousRealIndex||0,e=i.params.slidesPerGroup>1?Math.floor(i.realIndex/i.params.slidesPerGroup):i.realIndex):void 0!==i.snapIndex?(e=i.snapIndex,s=i.previousSnapIndex):(s=i.previousIndex||0,e=i.activeIndex||0),"bullets"===a.type&&i.pagination.bullets&&i.pagination.bullets.length>0){let n,o,u;let h=i.pagination.bullets;if(a.dynamicBullets&&(t=(0,r.h)(h[0],i.isHorizontal()?"width":"height",!0),d.forEach(e=>{e.style[i.isHorizontal()?"width":"height"]=`${t*(a.dynamicMainBullets+4)}px`}),a.dynamicMainBullets>1&&void 0!==s&&((c+=e-(s||0))>a.dynamicMainBullets-1?c=a.dynamicMainBullets-1:c<0&&(c=0)),u=((o=(n=Math.max(e-c,0))+(Math.min(h.length,a.dynamicMainBullets)-1))+n)/2),h.forEach(e=>{let t=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(e=>`${a.bulletActiveClass}${e}`)].map(e=>"string"==typeof e&&e.includes(" ")?e.split(" "):e).flat();e.classList.remove(...t)}),d.length>1)h.forEach(t=>{let s=(0,r.i)(t);s===e?t.classList.add(...a.bulletActiveClass.split(" ")):i.isElement&&t.setAttribute("part","bullet"),a.dynamicBullets&&(s>=n&&s<=o&&t.classList.add(...`${a.bulletActiveClass}-main`.split(" ")),s===n&&p(t,"prev"),s===o&&p(t,"next"))});else{let t=h[e];if(t&&t.classList.add(...a.bulletActiveClass.split(" ")),i.isElement&&h.forEach((t,i)=>{t.setAttribute("part",i===e?"bullet-active":"bullet")}),a.dynamicBullets){let e=h[n],t=h[o];for(let e=n;e<=o;e+=1)h[e]&&h[e].classList.add(...`${a.bulletActiveClass}-main`.split(" "));p(e,"prev"),p(t,"next")}}if(a.dynamicBullets){let e=Math.min(h.length,a.dynamicMainBullets+4),s=(t*e-t)/2-u*t,r=l?"right":"left";h.forEach(e=>{e.style[i.isHorizontal()?r:"top"]=`${s}px`})}}d.forEach((t,s)=>{if("fraction"===a.type&&(t.querySelectorAll(n(a.currentClass)).forEach(t=>{t.textContent=a.formatFractionCurrent(e+1)}),t.querySelectorAll(n(a.totalClass)).forEach(e=>{e.textContent=a.formatFractionTotal(f)})),"progressbar"===a.type){let s;s=a.progressbarOpposite?i.isHorizontal()?"vertical":"horizontal":i.isHorizontal()?"horizontal":"vertical";let r=(e+1)/f,l=1,o=1;"horizontal"===s?l=r:o=r,t.querySelectorAll(n(a.progressbarFillClass)).forEach(e=>{e.style.transform=`translate3d(0,0,0) scaleX(${l}) scaleY(${o})`,e.style.transitionDuration=`${i.params.speed}ms`})}"custom"===a.type&&a.renderCustom?((0,r.s)(t,a.renderCustom(i,e+1,f)),0===s&&o("paginationRender",t)):(0===s&&o("paginationRender",t),o("paginationUpdate",t)),i.params.watchOverflow&&i.enabled&&t.classList[i.isLocked?"add":"remove"](a.lockClass)})}function m(){let e=i.params.pagination;if(u())return;let t=i.virtual&&i.params.virtual.enabled?i.virtual.slides.length:i.grid&&i.params.grid.rows>1?i.slides.length/Math.ceil(i.params.grid.rows):i.slides.length,s=i.pagination.el;s=(0,r.m)(s);let l="";if("bullets"===e.type){let s=i.params.loop?Math.ceil(t/i.params.slidesPerGroup):i.snapGrid.length;i.params.freeMode&&i.params.freeMode.enabled&&s>t&&(s=t);for(let t=0;t<s;t+=1)e.renderBullet?l+=e.renderBullet.call(i,t,e.bulletClass):l+=`<${e.bulletElement} ${i.isElement?'part="bullet"':""} class="${e.bulletClass}"></${e.bulletElement}>`}"fraction"===e.type&&(l=e.renderFraction?e.renderFraction.call(i,e.currentClass,e.totalClass):`<span class="${e.currentClass}"></span> / <span class="${e.totalClass}"></span>`),"progressbar"===e.type&&(l=e.renderProgressbar?e.renderProgressbar.call(i,e.progressbarFillClass):`<span class="${e.progressbarFillClass}"></span>`),i.pagination.bullets=[],s.forEach(t=>{"custom"!==e.type&&(0,r.s)(t,l||""),"bullets"===e.type&&i.pagination.bullets.push(...t.querySelectorAll(n(e.bulletClass)))}),"custom"!==e.type&&o("paginationRender",s[0])}function g(){let e;i.params.pagination=l(i,i.originalParams.pagination,i.params.pagination,{el:"swiper-pagination"});let t=i.params.pagination;t.el&&("string"==typeof t.el&&i.isElement&&(e=i.el.querySelector(t.el)),e||"string"!=typeof t.el||(e=[...document.querySelectorAll(t.el)]),e||(e=t.el),e&&0!==e.length&&(i.params.uniqueNavElements&&"string"==typeof t.el&&Array.isArray(e)&&e.length>1&&(e=[...i.el.querySelectorAll(t.el)]).length>1&&(e=e.find(e=>(0,r.b)(e,".swiper")[0]===i.el)),Array.isArray(e)&&1===e.length&&(e=e[0]),Object.assign(i.pagination,{el:e}),(e=(0,r.m)(e)).forEach(e=>{"bullets"===t.type&&t.clickable&&e.classList.add(...(t.clickableClass||"").split(" ")),e.classList.add(t.modifierClass+t.type),e.classList.add(i.isHorizontal()?t.horizontalClass:t.verticalClass),"bullets"===t.type&&t.dynamicBullets&&(e.classList.add(`${t.modifierClass}${t.type}-dynamic`),c=0,t.dynamicMainBullets<1&&(t.dynamicMainBullets=1)),"progressbar"===t.type&&t.progressbarOpposite&&e.classList.add(t.progressbarOppositeClass),t.clickable&&e.addEventListener("click",h),i.enabled||e.classList.add(t.lockClass)})))}function v(){let e=i.params.pagination;if(u())return;let t=i.pagination.el;t&&(t=(0,r.m)(t)).forEach(t=>{t.classList.remove(e.hiddenClass),t.classList.remove(e.modifierClass+e.type),t.classList.remove(i.isHorizontal()?e.horizontalClass:e.verticalClass),e.clickable&&(t.classList.remove(...(e.clickableClass||"").split(" ")),t.removeEventListener("click",h))}),i.pagination.bullets&&i.pagination.bullets.forEach(t=>t.classList.remove(...e.bulletActiveClass.split(" ")))}a("changeDirection",()=>{if(!i.pagination||!i.pagination.el)return;let e=i.params.pagination,{el:t}=i.pagination;(t=(0,r.m)(t)).forEach(t=>{t.classList.remove(e.horizontalClass,e.verticalClass),t.classList.add(i.isHorizontal()?e.horizontalClass:e.verticalClass)})}),a("init",()=>{!1===i.params.pagination.enabled?b():(g(),m(),f())}),a("activeIndexChange",()=>{void 0===i.snapIndex&&f()}),a("snapIndexChange",()=>{f()}),a("snapGridLengthChange",()=>{m(),f()}),a("destroy",()=>{v()}),a("enable disable",()=>{let{el:e}=i.pagination;e&&(e=(0,r.m)(e)).forEach(e=>e.classList[i.enabled?"remove":"add"](i.params.pagination.lockClass))}),a("lock unlock",()=>{f()}),a("click",(e,t)=>{let s=t.target,l=(0,r.m)(i.pagination.el);if(i.params.pagination.el&&i.params.pagination.hideOnClick&&l&&l.length>0&&!s.classList.contains(i.params.pagination.bulletClass)){if(i.navigation&&(i.navigation.nextEl&&s===i.navigation.nextEl||i.navigation.prevEl&&s===i.navigation.prevEl))return;!0===l[0].classList.contains(i.params.pagination.hiddenClass)?o("paginationShow"):o("paginationHide"),l.forEach(e=>e.classList.toggle(i.params.pagination.hiddenClass))}});let b=()=>{i.el.classList.add(i.params.pagination.paginationDisabledClass);let{el:e}=i.pagination;e&&(e=(0,r.m)(e)).forEach(e=>e.classList.add(i.params.pagination.paginationDisabledClass)),v()};Object.assign(i.pagination,{enable:()=>{i.el.classList.remove(i.params.pagination.paginationDisabledClass);let{el:e}=i.pagination;e&&(e=(0,r.m)(e)).forEach(e=>e.classList.remove(i.params.pagination.paginationDisabledClass)),g(),m(),f()},disable:b,render:m,update:f,init:g,destroy:v})}function d(e){let t,i,a,o,{swiper:d,extendParams:c,on:u,emit:p}=e,h=(0,s.g)(),f=!1,m=null,g=null;function v(){if(!d.params.scrollbar.el||!d.scrollbar.el)return;let{scrollbar:e,rtlTranslate:t}=d,{dragEl:s,el:r}=e,l=d.params.scrollbar,n=d.params.loop?d.progressLoop:d.progress,o=i,c=(a-i)*n;t?(c=-c)>0?(o=i-c,c=0):-c+i>a&&(o=a+c):c<0?(o=i+c,c=0):c+i>a&&(o=a-c),d.isHorizontal()?(s.style.transform=`translate3d(${c}px, 0, 0)`,s.style.width=`${o}px`):(s.style.transform=`translate3d(0px, ${c}px, 0)`,s.style.height=`${o}px`),l.hide&&(clearTimeout(m),r.style.opacity=1,m=setTimeout(()=>{r.style.opacity=0,r.style.transitionDuration="400ms"},1e3))}function b(){if(!d.params.scrollbar.el||!d.scrollbar.el)return;let{scrollbar:e}=d,{dragEl:t,el:s}=e;t.style.width="",t.style.height="",a=d.isHorizontal()?s.offsetWidth:s.offsetHeight,o=d.size/(d.virtualSize+d.params.slidesOffsetBefore-(d.params.centeredSlides?d.snapGrid[0]:0)),i="auto"===d.params.scrollbar.dragSize?a*o:parseInt(d.params.scrollbar.dragSize,10),d.isHorizontal()?t.style.width=`${i}px`:t.style.height=`${i}px`,o>=1?s.style.display="none":s.style.display="",d.params.scrollbar.hide&&(s.style.opacity=0),d.params.watchOverflow&&d.enabled&&e.el.classList[d.isLocked?"add":"remove"](d.params.scrollbar.lockClass)}function w(e){return d.isHorizontal()?e.clientX:e.clientY}function y(e){let s;let{scrollbar:l,rtlTranslate:n}=d,{el:o}=l;s=Math.max(Math.min(s=(w(e)-(0,r.d)(o)[d.isHorizontal()?"left":"top"]-(null!==t?t:i/2))/(a-i),1),0),n&&(s=1-s);let c=d.minTranslate()+(d.maxTranslate()-d.minTranslate())*s;d.updateProgress(c),d.setTranslate(c),d.updateActiveIndex(),d.updateSlidesClasses()}function S(e){let i=d.params.scrollbar,{scrollbar:s,wrapperEl:r}=d,{el:l,dragEl:a}=s;f=!0,t=e.target===a?w(e)-e.target.getBoundingClientRect()[d.isHorizontal()?"left":"top"]:null,e.preventDefault(),e.stopPropagation(),r.style.transitionDuration="100ms",a.style.transitionDuration="100ms",y(e),clearTimeout(g),l.style.transitionDuration="0ms",i.hide&&(l.style.opacity=1),d.params.cssMode&&(d.wrapperEl.style["scroll-snap-type"]="none"),p("scrollbarDragStart",e)}function T(e){let{scrollbar:t,wrapperEl:i}=d,{el:s,dragEl:r}=t;f&&(e.preventDefault&&e.cancelable?e.preventDefault():e.returnValue=!1,y(e),i.style.transitionDuration="0ms",s.style.transitionDuration="0ms",r.style.transitionDuration="0ms",p("scrollbarDragMove",e))}function E(e){let t=d.params.scrollbar,{scrollbar:i,wrapperEl:s}=d,{el:l}=i;f&&(f=!1,d.params.cssMode&&(d.wrapperEl.style["scroll-snap-type"]="",s.style.transitionDuration=""),t.hide&&(clearTimeout(g),g=(0,r.n)(()=>{l.style.opacity=0,l.style.transitionDuration="400ms"},1e3)),p("scrollbarDragEnd",e),t.snapOnRelease&&d.slideToClosest())}function x(e){let{scrollbar:t,params:i}=d,s=t.el;if(!s)return;let r=!!i.passiveListeners&&{passive:!1,capture:!1},l=!!i.passiveListeners&&{passive:!0,capture:!1};if(!s)return;let a="on"===e?"addEventListener":"removeEventListener";s[a]("pointerdown",S,r),h[a]("pointermove",T,r),h[a]("pointerup",E,l)}function C(){let e,t;let{scrollbar:i,el:s}=d;d.params.scrollbar=l(d,d.originalParams.scrollbar,d.params.scrollbar,{el:"swiper-scrollbar"});let a=d.params.scrollbar;if(a.el){if("string"==typeof a.el&&d.isElement&&(e=d.el.querySelector(a.el)),e||"string"!=typeof a.el)e||(e=a.el);else if(!(e=h.querySelectorAll(a.el)).length)return;d.params.uniqueNavElements&&"string"==typeof a.el&&e.length>1&&1===s.querySelectorAll(a.el).length&&(e=s.querySelector(a.el)),e.length>0&&(e=e[0]),e.classList.add(d.isHorizontal()?a.horizontalClass:a.verticalClass),!e||(t=e.querySelector(n(d.params.scrollbar.dragClass)))||(t=(0,r.c)("div",d.params.scrollbar.dragClass),e.append(t)),Object.assign(i,{el:e,dragEl:t}),!a.draggable||d.params.scrollbar.el&&d.scrollbar.el&&x("on"),e&&e.classList[d.enabled?"remove":"add"](...(0,r.j)(d.params.scrollbar.lockClass))}}function _(){let e=d.params.scrollbar,t=d.scrollbar.el;t&&t.classList.remove(...(0,r.j)(d.isHorizontal()?e.horizontalClass:e.verticalClass)),d.params.scrollbar.el&&d.scrollbar.el&&x("off")}c({scrollbar:{el:null,dragSize:"auto",hide:!1,draggable:!1,snapOnRelease:!0,lockClass:"swiper-scrollbar-lock",dragClass:"swiper-scrollbar-drag",scrollbarDisabledClass:"swiper-scrollbar-disabled",horizontalClass:"swiper-scrollbar-horizontal",verticalClass:"swiper-scrollbar-vertical"}}),d.scrollbar={el:null,dragEl:null},u("changeDirection",()=>{if(!d.scrollbar||!d.scrollbar.el)return;let e=d.params.scrollbar,{el:t}=d.scrollbar;(t=(0,r.m)(t)).forEach(t=>{t.classList.remove(e.horizontalClass,e.verticalClass),t.classList.add(d.isHorizontal()?e.horizontalClass:e.verticalClass)})}),u("init",()=>{!1===d.params.scrollbar.enabled?M():(C(),b(),v())}),u("update resize observerUpdate lock unlock changeDirection",()=>{b()}),u("setTranslate",()=>{v()}),u("setTransition",(e,t)=>{d.params.scrollbar.el&&d.scrollbar.el&&(d.scrollbar.dragEl.style.transitionDuration=`${t}ms`)}),u("enable disable",()=>{let{el:e}=d.scrollbar;e&&e.classList[d.enabled?"remove":"add"](...(0,r.j)(d.params.scrollbar.lockClass))}),u("destroy",()=>{_()});let M=()=>{d.el.classList.add(...(0,r.j)(d.params.scrollbar.scrollbarDisabledClass)),d.scrollbar.el&&d.scrollbar.el.classList.add(...(0,r.j)(d.params.scrollbar.scrollbarDisabledClass)),_()};Object.assign(d.scrollbar,{enable:()=>{d.el.classList.remove(...(0,r.j)(d.params.scrollbar.scrollbarDisabledClass)),d.scrollbar.el&&d.scrollbar.el.classList.remove(...(0,r.j)(d.params.scrollbar.scrollbarDisabledClass)),C(),b(),v()},disable:M,updateSize:b,setTranslate:v,init:C,destroy:_})}},1e3:function(e,t,i){"use strict";function s(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function r(e,t){void 0===e&&(e={}),void 0===t&&(t={});let i=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>0>i.indexOf(e)).forEach(i=>{void 0===e[i]?e[i]=t[i]:s(t[i])&&s(e[i])&&Object.keys(t[i]).length>0&&r(e[i],t[i])})}i.d(t,{a:function(){return o},g:function(){return a}});let l={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function a(){let e="undefined"!=typeof document?document:{};return r(e,l),e}let n={document:l,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function o(){let e="undefined"!=typeof window?window:{};return r(e,n),e}},96763:function(e,t,i){"use strict";i.d(t,{a:function(){return c},b:function(){return S},c:function(){return m},d:function(){return g},e:function(){return p},f:function(){return n},h:function(){return T},i:function(){return y},j:function(){return r},k:function(){return o},m:function(){return E},n:function(){return a},q:function(){return w},r:function(){return b},s:function(){return x},t:function(){return v},u:function(){return u},v:function(){return f},w:function(){return h},x:function(){return function e(){let t=Object(arguments.length<=0?void 0:arguments[0]),i=["__proto__","constructor","prototype"];for(let s=1;s<arguments.length;s+=1){let r=s<0||arguments.length<=s?void 0:arguments[s];if(null!=r&&("undefined"!=typeof window&&void 0!==window.HTMLElement?!(r instanceof HTMLElement):!r||1!==r.nodeType&&11!==r.nodeType)){let s=Object.keys(Object(r)).filter(e=>0>i.indexOf(e));for(let i=0,l=s.length;i<l;i+=1){let l=s[i],a=Object.getOwnPropertyDescriptor(r,l);void 0!==a&&a.enumerable&&(d(t[l])&&d(r[l])?r[l].__swiper__?t[l]=r[l]:e(t[l],r[l]):!d(t[l])&&d(r[l])?(t[l]={},r[l].__swiper__?t[l]=r[l]:e(t[l],r[l])):t[l]=r[l])}}}return t}},y:function(){return l}});var s=i(1e3);function r(e){return void 0===e&&(e=""),e.trim().split(" ").filter(e=>!!e.trim())}function l(e){Object.keys(e).forEach(t=>{try{e[t]=null}catch(e){}try{delete e[t]}catch(e){}})}function a(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function n(){return Date.now()}function o(e,t){let i,r,l;void 0===t&&(t="x");let a=(0,s.a)(),n=function(e){let t;let i=(0,s.a)();return i.getComputedStyle&&(t=i.getComputedStyle(e,null)),!t&&e.currentStyle&&(t=e.currentStyle),t||(t=e.style),t}(e);return a.WebKitCSSMatrix?((r=n.transform||n.webkitTransform).split(",").length>6&&(r=r.split(", ").map(e=>e.replace(",",".")).join(", ")),l=new a.WebKitCSSMatrix("none"===r?"":r)):i=(l=n.MozTransform||n.OTransform||n.MsTransform||n.msTransform||n.transform||n.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,")).toString().split(","),"x"===t&&(r=a.WebKitCSSMatrix?l.m41:16===i.length?parseFloat(i[12]):parseFloat(i[4])),"y"===t&&(r=a.WebKitCSSMatrix?l.m42:16===i.length?parseFloat(i[13]):parseFloat(i[5])),r||0}function d(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function c(e,t,i){e.style.setProperty(t,i)}function u(e){let t,{swiper:i,targetPosition:r,side:l}=e,a=(0,s.a)(),n=-i.translate,o=null,d=i.params.speed;i.wrapperEl.style.scrollSnapType="none",a.cancelAnimationFrame(i.cssModeFrameID);let c=r>n?"next":"prev",u=(e,t)=>"next"===c&&e>=t||"prev"===c&&e<=t,p=()=>{t=new Date().getTime(),null===o&&(o=t);let e=n+(.5-Math.cos(Math.max(Math.min((t-o)/d,1),0)*Math.PI)/2)*(r-n);if(u(e,r)&&(e=r),i.wrapperEl.scrollTo({[l]:e}),u(e,r)){i.wrapperEl.style.overflow="hidden",i.wrapperEl.style.scrollSnapType="",setTimeout(()=>{i.wrapperEl.style.overflow="",i.wrapperEl.scrollTo({[l]:e})}),a.cancelAnimationFrame(i.cssModeFrameID);return}i.cssModeFrameID=a.requestAnimationFrame(p)};p()}function p(e,t){void 0===t&&(t="");let i=(0,s.a)(),r=[...e.children];return(i.HTMLSlotElement&&e instanceof HTMLSlotElement&&r.push(...e.assignedElements()),t)?r.filter(e=>e.matches(t)):r}function h(e,t){let i=(0,s.a)(),r=t.contains(e);return!r&&i.HTMLSlotElement&&t instanceof HTMLSlotElement&&!(r=[...t.assignedElements()].includes(e))&&(r=function(e,t){let i=[t];for(;i.length>0;){let t=i.shift();if(e===t)return!0;i.push(...t.children,...t.shadowRoot?t.shadowRoot.children:[],...t.assignedElements?t.assignedElements():[])}}(e,t)),r}function f(e){try{console.warn(e);return}catch(e){}}function m(e,t){void 0===t&&(t=[]);let i=document.createElement(e);return i.classList.add(...Array.isArray(t)?t:r(t)),i}function g(e){let t=(0,s.a)(),i=(0,s.g)(),r=e.getBoundingClientRect(),l=i.body,a=e.clientTop||l.clientTop||0,n=e.clientLeft||l.clientLeft||0,o=e===t?t.scrollY:e.scrollTop,d=e===t?t.scrollX:e.scrollLeft;return{top:r.top+o-a,left:r.left+d-n}}function v(e,t){let i=[];for(;e.previousElementSibling;){let s=e.previousElementSibling;t?s.matches(t)&&i.push(s):i.push(s),e=s}return i}function b(e,t){let i=[];for(;e.nextElementSibling;){let s=e.nextElementSibling;t?s.matches(t)&&i.push(s):i.push(s),e=s}return i}function w(e,t){return(0,s.a)().getComputedStyle(e,null).getPropertyValue(t)}function y(e){let t,i=e;if(i){for(t=0;null!==(i=i.previousSibling);)1===i.nodeType&&(t+=1);return t}}function S(e,t){let i=[],s=e.parentElement;for(;s;)t?s.matches(t)&&i.push(s):i.push(s),s=s.parentElement;return i}function T(e,t,i){let r=(0,s.a)();return i?e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(r.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(r.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom")):e.offsetWidth}function E(e){return(Array.isArray(e)?e:[e]).filter(e=>!!e)}function x(e,t){void 0===t&&(t=""),"undefined"!=typeof trustedTypes?e.innerHTML=trustedTypes.createPolicy("html",{createHTML:e=>e}).createHTML(t):e.innerHTML=t}},59989:function(e,t,i){"use strict";let s,r,l;i.d(t,{tq:function(){return W},o5:function(){return X}});var a=i(32486),n=i(1e3),o=i(96763);function d(){return s||(s=function(){let e=(0,n.a)(),t=(0,n.g)();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}()),s}function c(e){return void 0===e&&(e={}),r||(r=function(e){let{userAgent:t}=void 0===e?{}:e,i=d(),s=(0,n.a)(),r=s.navigator.platform,l=t||s.navigator.userAgent,a={ios:!1,android:!1},o=s.screen.width,c=s.screen.height,u=l.match(/(Android);?[\s\/]+([\d.]+)?/),p=l.match(/(iPad).*OS\s([\d_]+)/),h=l.match(/(iPod)(.*OS\s([\d_]+))?/),f=!p&&l.match(/(iPhone\sOS|iOS)\s([\d_]+)/),m="MacIntel"===r;return!p&&m&&i.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${o}x${c}`)>=0&&((p=l.match(/(Version)\/([\d.]+)/))||(p=[0,1,"13_0_0"]),m=!1),u&&"Win32"!==r&&(a.os="android",a.android=!0),(p||f||h)&&(a.os="ios",a.ios=!0),a}(e)),r}function u(){return l||(l=function(){let e=(0,n.a)(),t=c(),i=!1;function s(){let t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&0>t.indexOf("chrome")&&0>t.indexOf("android")}if(s()){let t=String(e.navigator.userAgent);if(t.includes("Version/")){let[e,s]=t.split("Version/")[1].split(" ")[0].split(".").map(e=>Number(e));i=e<16||16===e&&s<2}}let r=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),l=s(),a=l||r&&t.ios;return{isSafari:i||l,needPerspectiveFix:i,need3dFix:a,isWebView:r}}()),l}let p=(e,t,i)=>{t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)},h=(e,t,i)=>{t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)},f=(e,t)=>{if(!e||e.destroyed||!e.params)return;let i=t.closest(e.isElement?"swiper-slide":`.${e.params.slideClass}`);if(i){let t=i.querySelector(`.${e.params.lazyPreloaderClass}`);!t&&e.isElement&&(i.shadowRoot?t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{i.shadowRoot&&(t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`))&&t.remove()})),t&&t.remove()}},m=(e,t)=>{if(!e.slides[t])return;let i=e.slides[t].querySelector('[loading="lazy"]');i&&i.removeAttribute("loading")},g=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext,i=e.slides.length;if(!i||!t||t<0)return;t=Math.min(t,i);let s="auto"===e.params.slidesPerView?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),r=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){let i=[r-t];i.push(...Array.from({length:t}).map((e,t)=>r+s+t)),e.slides.forEach((t,s)=>{i.includes(t.column)&&m(e,s)});return}let l=r+s-1;if(e.params.rewind||e.params.loop)for(let s=r-t;s<=l+t;s+=1){let t=(s%i+i)%i;(t<r||t>l)&&m(e,t)}else for(let s=Math.max(r-t,0);s<=Math.min(l+t,i-1);s+=1)s!==r&&(s>l||s<r)&&m(e,s)};function v(e){let{swiper:t,runCallbacks:i,direction:s,step:r}=e,{activeIndex:l,previousIndex:a}=t,n=s;n||(n=l>a?"next":l<a?"prev":"reset"),t.emit(`transition${r}`),i&&"reset"===n?t.emit(`slideResetTransition${r}`):i&&l!==a&&(t.emit(`slideChangeTransition${r}`),"next"===n?t.emit(`slideNextTransition${r}`):t.emit(`slidePrevTransition${r}`))}function b(e,t,i){let s=(0,n.a)(),{params:r}=e,l=r.edgeSwipeDetection,a=r.edgeSwipeThreshold;return!l||!(i<=a)&&!(i>=s.innerWidth-a)||"prevent"===l&&(t.preventDefault(),!0)}function w(e){let t=(0,n.g)(),i=e;i.originalEvent&&(i=i.originalEvent);let s=this.touchEventsData;if("pointerdown"===i.type){if(null!==s.pointerId&&s.pointerId!==i.pointerId)return;s.pointerId=i.pointerId}else"touchstart"===i.type&&1===i.targetTouches.length&&(s.touchId=i.targetTouches[0].identifier);if("touchstart"===i.type){b(this,i,i.targetTouches[0].pageX);return}let{params:r,touches:l,enabled:a}=this;if(!a||!r.simulateTouch&&"mouse"===i.pointerType||this.animating&&r.preventInteractionOnTransition)return;!this.animating&&r.cssMode&&r.loop&&this.loopFix();let d=i.target;if("wrapper"===r.touchEventsTarget&&!(0,o.w)(d,this.wrapperEl)||"which"in i&&3===i.which||"button"in i&&i.button>0||s.isTouched&&s.isMoved)return;let c=!!r.noSwipingClass&&""!==r.noSwipingClass,u=i.composedPath?i.composedPath():i.path;c&&i.target&&i.target.shadowRoot&&u&&(d=u[0]);let p=r.noSwipingSelector?r.noSwipingSelector:`.${r.noSwipingClass}`,h=!!(i.target&&i.target.shadowRoot);if(r.noSwiping&&(h?function(e,t){return void 0===t&&(t=this),function t(i){if(!i||i===(0,n.g)()||i===(0,n.a)())return null;i.assignedSlot&&(i=i.assignedSlot);let s=i.closest(e);return s||i.getRootNode?s||t(i.getRootNode().host):null}(t)}(p,d):d.closest(p))){this.allowClick=!0;return}if(r.swipeHandler&&!d.closest(r.swipeHandler))return;l.currentX=i.pageX,l.currentY=i.pageY;let f=l.currentX,m=l.currentY;if(!b(this,i,f))return;Object.assign(s,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),l.startX=f,l.startY=m,s.touchStartTime=(0,o.f)(),this.allowClick=!0,this.updateSize(),this.swipeDirection=void 0,r.threshold>0&&(s.allowThresholdMove=!1);let g=!0;d.matches(s.focusableElements)&&(g=!1,"SELECT"===d.nodeName&&(s.isTouched=!1)),t.activeElement&&t.activeElement.matches(s.focusableElements)&&t.activeElement!==d&&("mouse"===i.pointerType||"mouse"!==i.pointerType&&!d.matches(s.focusableElements))&&t.activeElement.blur();let v=g&&this.allowTouchMove&&r.touchStartPreventDefault;(r.touchStartForcePreventDefault||v)&&!d.isContentEditable&&i.preventDefault(),r.freeMode&&r.freeMode.enabled&&this.freeMode&&this.animating&&!r.cssMode&&this.freeMode.onTouchStart(),this.emit("touchStart",i)}function y(e){let t,i;let s=(0,n.g)(),r=this.touchEventsData,{params:l,touches:a,rtlTranslate:d,enabled:c}=this;if(!c||!l.simulateTouch&&"mouse"===e.pointerType)return;let u=e;if(u.originalEvent&&(u=u.originalEvent),"pointermove"===u.type&&(null!==r.touchId||u.pointerId!==r.pointerId))return;if("touchmove"===u.type){if(!(t=[...u.changedTouches].find(e=>e.identifier===r.touchId))||t.identifier!==r.touchId)return}else t=u;if(!r.isTouched){r.startMoving&&r.isScrolling&&this.emit("touchMoveOpposite",u);return}let p=t.pageX,h=t.pageY;if(u.preventedByNestedSwiper){a.startX=p,a.startY=h;return}if(!this.allowTouchMove){u.target.matches(r.focusableElements)||(this.allowClick=!1),r.isTouched&&(Object.assign(a,{startX:p,startY:h,currentX:p,currentY:h}),r.touchStartTime=(0,o.f)());return}if(l.touchReleaseOnEdges&&!l.loop){if(this.isVertical()){if(h<a.startY&&this.translate<=this.maxTranslate()||h>a.startY&&this.translate>=this.minTranslate()){r.isTouched=!1,r.isMoved=!1;return}}else if(d&&(p>a.startX&&-this.translate<=this.maxTranslate()||p<a.startX&&-this.translate>=this.minTranslate()))return;else if(!d&&(p<a.startX&&this.translate<=this.maxTranslate()||p>a.startX&&this.translate>=this.minTranslate()))return}if(s.activeElement&&s.activeElement.matches(r.focusableElements)&&s.activeElement!==u.target&&"mouse"!==u.pointerType&&s.activeElement.blur(),s.activeElement&&u.target===s.activeElement&&u.target.matches(r.focusableElements)){r.isMoved=!0,this.allowClick=!1;return}r.allowTouchCallbacks&&this.emit("touchMove",u),a.previousX=a.currentX,a.previousY=a.currentY,a.currentX=p,a.currentY=h;let f=a.currentX-a.startX,m=a.currentY-a.startY;if(this.params.threshold&&Math.sqrt(f**2+m**2)<this.params.threshold)return;if(void 0===r.isScrolling){let e;this.isHorizontal()&&a.currentY===a.startY||this.isVertical()&&a.currentX===a.startX?r.isScrolling=!1:f*f+m*m>=25&&(e=180*Math.atan2(Math.abs(m),Math.abs(f))/Math.PI,r.isScrolling=this.isHorizontal()?e>l.touchAngle:90-e>l.touchAngle)}if(r.isScrolling&&this.emit("touchMoveOpposite",u),void 0===r.startMoving&&(a.currentX!==a.startX||a.currentY!==a.startY)&&(r.startMoving=!0),r.isScrolling||"touchmove"===u.type&&r.preventTouchMoveFromPointerMove){r.isTouched=!1;return}if(!r.startMoving)return;this.allowClick=!1,!l.cssMode&&u.cancelable&&u.preventDefault(),l.touchMoveStopPropagation&&!l.nested&&u.stopPropagation();let g=this.isHorizontal()?f:m,v=this.isHorizontal()?a.currentX-a.previousX:a.currentY-a.previousY;l.oneWayMovement&&(g=Math.abs(g)*(d?1:-1),v=Math.abs(v)*(d?1:-1)),a.diff=g,g*=l.touchRatio,d&&(g=-g,v=-v);let b=this.touchesDirection;this.swipeDirection=g>0?"prev":"next",this.touchesDirection=v>0?"prev":"next";let w=this.params.loop&&!l.cssMode,y="next"===this.touchesDirection&&this.allowSlideNext||"prev"===this.touchesDirection&&this.allowSlidePrev;if(!r.isMoved){if(w&&y&&this.loopFix({direction:this.swipeDirection}),r.startTranslate=this.getTranslate(),this.setTransition(0),this.animating){let e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});this.wrapperEl.dispatchEvent(e)}r.allowMomentumBounce=!1,l.grabCursor&&(!0===this.allowSlideNext||!0===this.allowSlidePrev)&&this.setGrabCursor(!0),this.emit("sliderFirstMove",u)}if(new Date().getTime(),!1!==l._loopSwapReset&&r.isMoved&&r.allowThresholdMove&&b!==this.touchesDirection&&w&&y&&Math.abs(g)>=1){Object.assign(a,{startX:p,startY:h,currentX:p,currentY:h,startTranslate:r.currentTranslate}),r.loopSwapReset=!0,r.startTranslate=r.currentTranslate;return}this.emit("sliderMove",u),r.isMoved=!0,r.currentTranslate=g+r.startTranslate;let S=!0,T=l.resistanceRatio;if(l.touchReleaseOnEdges&&(T=0),g>0?(w&&y&&!i&&r.allowThresholdMove&&r.currentTranslate>(l.centeredSlides?this.minTranslate()-this.slidesSizesGrid[this.activeIndex+1]-("auto"!==l.slidesPerView&&this.slides.length-l.slidesPerView>=2?this.slidesSizesGrid[this.activeIndex+1]+this.params.spaceBetween:0)-this.params.spaceBetween:this.minTranslate())&&this.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),r.currentTranslate>this.minTranslate()&&(S=!1,l.resistance&&(r.currentTranslate=this.minTranslate()-1+(-this.minTranslate()+r.startTranslate+g)**T))):g<0&&(w&&y&&!i&&r.allowThresholdMove&&r.currentTranslate<(l.centeredSlides?this.maxTranslate()+this.slidesSizesGrid[this.slidesSizesGrid.length-1]+this.params.spaceBetween+("auto"!==l.slidesPerView&&this.slides.length-l.slidesPerView>=2?this.slidesSizesGrid[this.slidesSizesGrid.length-1]+this.params.spaceBetween:0):this.maxTranslate())&&this.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:this.slides.length-("auto"===l.slidesPerView?this.slidesPerViewDynamic():Math.ceil(parseFloat(l.slidesPerView,10)))}),r.currentTranslate<this.maxTranslate()&&(S=!1,l.resistance&&(r.currentTranslate=this.maxTranslate()+1-(this.maxTranslate()-r.startTranslate-g)**T))),S&&(u.preventedByNestedSwiper=!0),!this.allowSlideNext&&"next"===this.swipeDirection&&r.currentTranslate<r.startTranslate&&(r.currentTranslate=r.startTranslate),!this.allowSlidePrev&&"prev"===this.swipeDirection&&r.currentTranslate>r.startTranslate&&(r.currentTranslate=r.startTranslate),this.allowSlidePrev||this.allowSlideNext||(r.currentTranslate=r.startTranslate),l.threshold>0){if(Math.abs(g)>l.threshold||r.allowThresholdMove){if(!r.allowThresholdMove){r.allowThresholdMove=!0,a.startX=a.currentX,a.startY=a.currentY,r.currentTranslate=r.startTranslate,a.diff=this.isHorizontal()?a.currentX-a.startX:a.currentY-a.startY;return}}else{r.currentTranslate=r.startTranslate;return}}l.followFinger&&!l.cssMode&&((l.freeMode&&l.freeMode.enabled&&this.freeMode||l.watchSlidesProgress)&&(this.updateActiveIndex(),this.updateSlidesClasses()),l.freeMode&&l.freeMode.enabled&&this.freeMode&&this.freeMode.onTouchMove(),this.updateProgress(r.currentTranslate),this.setTranslate(r.currentTranslate))}function S(e){let t,i;let s=this,r=s.touchEventsData,l=e;if(l.originalEvent&&(l=l.originalEvent),"touchend"===l.type||"touchcancel"===l.type){if(!(t=[...l.changedTouches].find(e=>e.identifier===r.touchId))||t.identifier!==r.touchId)return}else{if(null!==r.touchId||l.pointerId!==r.pointerId)return;t=l}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(l.type)&&!(["pointercancel","contextmenu"].includes(l.type)&&(s.browser.isSafari||s.browser.isWebView)))return;r.pointerId=null,r.touchId=null;let{params:a,touches:n,rtlTranslate:d,slidesGrid:c,enabled:u}=s;if(!u||!a.simulateTouch&&"mouse"===l.pointerType)return;if(r.allowTouchCallbacks&&s.emit("touchEnd",l),r.allowTouchCallbacks=!1,!r.isTouched){r.isMoved&&a.grabCursor&&s.setGrabCursor(!1),r.isMoved=!1,r.startMoving=!1;return}a.grabCursor&&r.isMoved&&r.isTouched&&(!0===s.allowSlideNext||!0===s.allowSlidePrev)&&s.setGrabCursor(!1);let p=(0,o.f)(),h=p-r.touchStartTime;if(s.allowClick){let e=l.path||l.composedPath&&l.composedPath();s.updateClickedSlide(e&&e[0]||l.target,e),s.emit("tap click",l),h<300&&p-r.lastClickTime<300&&s.emit("doubleTap doubleClick",l)}if(r.lastClickTime=(0,o.f)(),(0,o.n)(()=>{s.destroyed||(s.allowClick=!0)}),!r.isTouched||!r.isMoved||!s.swipeDirection||0===n.diff&&!r.loopSwapReset||r.currentTranslate===r.startTranslate&&!r.loopSwapReset){r.isTouched=!1,r.isMoved=!1,r.startMoving=!1;return}if(r.isTouched=!1,r.isMoved=!1,r.startMoving=!1,i=a.followFinger?d?s.translate:-s.translate:-r.currentTranslate,a.cssMode)return;if(a.freeMode&&a.freeMode.enabled){s.freeMode.onTouchEnd({currentPos:i});return}let f=i>=-s.maxTranslate()&&!s.params.loop,m=0,g=s.slidesSizesGrid[0];for(let e=0;e<c.length;e+=e<a.slidesPerGroupSkip?1:a.slidesPerGroup){let t=e<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;void 0!==c[e+t]?(f||i>=c[e]&&i<c[e+t])&&(m=e,g=c[e+t]-c[e]):(f||i>=c[e])&&(m=e,g=c[c.length-1]-c[c.length-2])}let v=null,b=null;a.rewind&&(s.isBeginning?b=a.virtual&&a.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1:s.isEnd&&(v=0));let w=(i-c[m])/g,y=m<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;if(h>a.longSwipesMs){if(!a.longSwipes){s.slideTo(s.activeIndex);return}"next"===s.swipeDirection&&(w>=a.longSwipesRatio?s.slideTo(a.rewind&&s.isEnd?v:m+y):s.slideTo(m)),"prev"===s.swipeDirection&&(w>1-a.longSwipesRatio?s.slideTo(m+y):null!==b&&w<0&&Math.abs(w)>a.longSwipesRatio?s.slideTo(b):s.slideTo(m))}else{if(!a.shortSwipes){s.slideTo(s.activeIndex);return}s.navigation&&(l.target===s.navigation.nextEl||l.target===s.navigation.prevEl)?l.target===s.navigation.nextEl?s.slideTo(m+y):s.slideTo(m):("next"===s.swipeDirection&&s.slideTo(null!==v?v:m+y),"prev"===s.swipeDirection&&s.slideTo(null!==b?b:m))}}function T(){let e=this,{params:t,el:i}=e;if(i&&0===i.offsetWidth)return;t.breakpoints&&e.setBreakpoint();let{allowSlideNext:s,allowSlidePrev:r,snapGrid:l}=e,a=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();let n=a&&t.loop;"auto"!==t.slidesPerView&&!(t.slidesPerView>1)||!e.isEnd||e.isBeginning||e.params.centeredSlides||n?e.params.loop&&!a?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0):e.slideTo(e.slides.length-1,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout(()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()},500)),e.allowSlidePrev=r,e.allowSlideNext=s,e.params.watchOverflow&&l!==e.snapGrid&&e.checkOverflow()}function E(e){this.enabled&&!this.allowClick&&(this.params.preventClicks&&e.preventDefault(),this.params.preventClicksPropagation&&this.animating&&(e.stopPropagation(),e.stopImmediatePropagation()))}function x(){let{wrapperEl:e,rtlTranslate:t,enabled:i}=this;if(!i)return;this.previousTranslate=this.translate,this.isHorizontal()?this.translate=-e.scrollLeft:this.translate=-e.scrollTop,0===this.translate&&(this.translate=0),this.updateActiveIndex(),this.updateSlidesClasses();let s=this.maxTranslate()-this.minTranslate();(0===s?0:(this.translate-this.minTranslate())/s)!==this.progress&&this.updateProgress(t?-this.translate:this.translate),this.emit("setTranslate",this.translate,!1)}function C(e){f(this,e.target),!this.params.cssMode&&("auto"===this.params.slidesPerView||this.params.autoHeight)&&this.update()}function _(){!this.documentTouchHandlerProceeded&&(this.documentTouchHandlerProceeded=!0,this.params.touchReleaseOnEdges&&(this.el.style.touchAction="auto"))}let M=(e,t)=>{let i=(0,n.g)(),{params:s,el:r,wrapperEl:l,device:a}=e,o=!!s.nested,d="on"===t?"addEventListener":"removeEventListener";r&&"string"!=typeof r&&(i[d]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:o}),r[d]("touchstart",e.onTouchStart,{passive:!1}),r[d]("pointerdown",e.onTouchStart,{passive:!1}),i[d]("touchmove",e.onTouchMove,{passive:!1,capture:o}),i[d]("pointermove",e.onTouchMove,{passive:!1,capture:o}),i[d]("touchend",e.onTouchEnd,{passive:!0}),i[d]("pointerup",e.onTouchEnd,{passive:!0}),i[d]("pointercancel",e.onTouchEnd,{passive:!0}),i[d]("touchcancel",e.onTouchEnd,{passive:!0}),i[d]("pointerout",e.onTouchEnd,{passive:!0}),i[d]("pointerleave",e.onTouchEnd,{passive:!0}),i[d]("contextmenu",e.onTouchEnd,{passive:!0}),(s.preventClicks||s.preventClicksPropagation)&&r[d]("click",e.onClick,!0),s.cssMode&&l[d]("scroll",e.onScroll),s.updateOnWindowResize?e[t](a.ios||a.android?"resize orientationchange observerUpdate":"resize observerUpdate",T,!0):e[t]("observerUpdate",T,!0),r[d]("load",e.onLoad,{capture:!0}))},P=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;var L={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};let k={eventsEmitter:{on(e,t,i){let s=this;if(!s.eventsListeners||s.destroyed||"function"!=typeof t)return s;let r=i?"unshift":"push";return e.split(" ").forEach(e=>{s.eventsListeners[e]||(s.eventsListeners[e]=[]),s.eventsListeners[e][r](t)}),s},once(e,t,i){let s=this;if(!s.eventsListeners||s.destroyed||"function"!=typeof t)return s;function r(){s.off(e,r),r.__emitterProxy&&delete r.__emitterProxy;for(var i=arguments.length,l=Array(i),a=0;a<i;a++)l[a]=arguments[a];t.apply(s,l)}return r.__emitterProxy=t,s.on(e,r,i)},onAny(e,t){return!this.eventsListeners||this.destroyed||"function"!=typeof e||0>this.eventsAnyListeners.indexOf(e)&&this.eventsAnyListeners[t?"unshift":"push"](e),this},offAny(e){if(!this.eventsListeners||this.destroyed||!this.eventsAnyListeners)return this;let t=this.eventsAnyListeners.indexOf(e);return t>=0&&this.eventsAnyListeners.splice(t,1),this},off(e,t){let i=this;return i.eventsListeners&&!i.destroyed&&i.eventsListeners&&e.split(" ").forEach(e=>{void 0===t?i.eventsListeners[e]=[]:i.eventsListeners[e]&&i.eventsListeners[e].forEach((s,r)=>{(s===t||s.__emitterProxy&&s.__emitterProxy===t)&&i.eventsListeners[e].splice(r,1)})}),i},emit(){let e,t,i;let s=this;if(!s.eventsListeners||s.destroyed||!s.eventsListeners)return s;for(var r=arguments.length,l=Array(r),a=0;a<r;a++)l[a]=arguments[a];return"string"==typeof l[0]||Array.isArray(l[0])?(e=l[0],t=l.slice(1,l.length),i=s):(e=l[0].events,t=l[0].data,i=l[0].context||s),t.unshift(i),(Array.isArray(e)?e:e.split(" ")).forEach(e=>{s.eventsAnyListeners&&s.eventsAnyListeners.length&&s.eventsAnyListeners.forEach(s=>{s.apply(i,[e,...t])}),s.eventsListeners&&s.eventsListeners[e]&&s.eventsListeners[e].forEach(e=>{e.apply(i,t)})}),s}},update:{updateSize:function(){let e,t;let i=this.el;e=void 0!==this.params.width&&null!==this.params.width?this.params.width:i.clientWidth,t=void 0!==this.params.height&&null!==this.params.height?this.params.height:i.clientHeight,0===e&&this.isHorizontal()||0===t&&this.isVertical()||(e=e-parseInt((0,o.q)(i,"padding-left")||0,10)-parseInt((0,o.q)(i,"padding-right")||0,10),t=t-parseInt((0,o.q)(i,"padding-top")||0,10)-parseInt((0,o.q)(i,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),Object.assign(this,{width:e,height:t,size:this.isHorizontal()?e:t}))},updateSlides:function(){let e;let t=this;function i(e,i){return parseFloat(e.getPropertyValue(t.getDirectionLabel(i))||0)}let s=t.params,{wrapperEl:r,slidesEl:l,size:a,rtlTranslate:n,wrongRTL:d}=t,c=t.virtual&&s.virtual.enabled,u=c?t.virtual.slides.length:t.slides.length,p=(0,o.e)(l,`.${t.params.slideClass}, swiper-slide`),h=c?t.virtual.slides.length:p.length,f=[],m=[],g=[],v=s.slidesOffsetBefore;"function"==typeof v&&(v=s.slidesOffsetBefore.call(t));let b=s.slidesOffsetAfter;"function"==typeof b&&(b=s.slidesOffsetAfter.call(t));let w=t.snapGrid.length,y=t.slidesGrid.length,S=s.spaceBetween,T=-v,E=0,x=0;if(void 0===a)return;"string"==typeof S&&S.indexOf("%")>=0?S=parseFloat(S.replace("%",""))/100*a:"string"==typeof S&&(S=parseFloat(S)),t.virtualSize=-S,p.forEach(e=>{n?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""}),s.centeredSlides&&s.cssMode&&((0,o.a)(r,"--swiper-centered-offset-before",""),(0,o.a)(r,"--swiper-centered-offset-after",""));let C=s.grid&&s.grid.rows>1&&t.grid;C?t.grid.initSlides(p):t.grid&&t.grid.unsetSlides();let _="auto"===s.slidesPerView&&s.breakpoints&&Object.keys(s.breakpoints).filter(e=>void 0!==s.breakpoints[e].slidesPerView).length>0;for(let r=0;r<h;r+=1){let l;if(e=0,p[r]&&(l=p[r]),C&&t.grid.updateSlide(r,l,p),!p[r]||"none"!==(0,o.q)(l,"display")){if("auto"===s.slidesPerView){_&&(p[r].style[t.getDirectionLabel("width")]="");let a=getComputedStyle(l),n=l.style.transform,d=l.style.webkitTransform;if(n&&(l.style.transform="none"),d&&(l.style.webkitTransform="none"),s.roundLengths)e=t.isHorizontal()?(0,o.h)(l,"width",!0):(0,o.h)(l,"height",!0);else{let t=i(a,"width"),s=i(a,"padding-left"),r=i(a,"padding-right"),n=i(a,"margin-left"),o=i(a,"margin-right"),d=a.getPropertyValue("box-sizing");if(d&&"border-box"===d)e=t+n+o;else{let{clientWidth:i,offsetWidth:a}=l;e=t+s+r+n+o+(a-i)}}n&&(l.style.transform=n),d&&(l.style.webkitTransform=d),s.roundLengths&&(e=Math.floor(e))}else e=(a-(s.slidesPerView-1)*S)/s.slidesPerView,s.roundLengths&&(e=Math.floor(e)),p[r]&&(p[r].style[t.getDirectionLabel("width")]=`${e}px`);p[r]&&(p[r].swiperSlideSize=e),g.push(e),s.centeredSlides?(T=T+e/2+E/2+S,0===E&&0!==r&&(T=T-a/2-S),0===r&&(T=T-a/2-S),.001>Math.abs(T)&&(T=0),s.roundLengths&&(T=Math.floor(T)),x%s.slidesPerGroup==0&&f.push(T),m.push(T)):(s.roundLengths&&(T=Math.floor(T)),(x-Math.min(t.params.slidesPerGroupSkip,x))%t.params.slidesPerGroup==0&&f.push(T),m.push(T),T=T+e+S),t.virtualSize+=e+S,E=e,x+=1}}if(t.virtualSize=Math.max(t.virtualSize,a)+b,n&&d&&("slide"===s.effect||"coverflow"===s.effect)&&(r.style.width=`${t.virtualSize+S}px`),s.setWrapperSize&&(r.style[t.getDirectionLabel("width")]=`${t.virtualSize+S}px`),C&&t.grid.updateWrapperSize(e,f),!s.centeredSlides){let e=[];for(let i=0;i<f.length;i+=1){let r=f[i];s.roundLengths&&(r=Math.floor(r)),f[i]<=t.virtualSize-a&&e.push(r)}f=e,Math.floor(t.virtualSize-a)-Math.floor(f[f.length-1])>1&&f.push(t.virtualSize-a)}if(c&&s.loop){let e=g[0]+S;if(s.slidesPerGroup>1){let i=Math.ceil((t.virtual.slidesBefore+t.virtual.slidesAfter)/s.slidesPerGroup),r=e*s.slidesPerGroup;for(let e=0;e<i;e+=1)f.push(f[f.length-1]+r)}for(let i=0;i<t.virtual.slidesBefore+t.virtual.slidesAfter;i+=1)1===s.slidesPerGroup&&f.push(f[f.length-1]+e),m.push(m[m.length-1]+e),t.virtualSize+=e}if(0===f.length&&(f=[0]),0!==S){let e=t.isHorizontal()&&n?"marginLeft":t.getDirectionLabel("marginRight");p.filter((e,t)=>!s.cssMode||!!s.loop||t!==p.length-1).forEach(t=>{t.style[e]=`${S}px`})}if(s.centeredSlides&&s.centeredSlidesBounds){let e=0;g.forEach(t=>{e+=t+(S||0)});let t=(e-=S)>a?e-a:0;f=f.map(e=>e<=0?-v:e>t?t+b:e)}if(s.centerInsufficientSlides){let e=0;g.forEach(t=>{e+=t+(S||0)}),e-=S;let t=(s.slidesOffsetBefore||0)+(s.slidesOffsetAfter||0);if(e+t<a){let i=(a-e-t)/2;f.forEach((e,t)=>{f[t]=e-i}),m.forEach((e,t)=>{m[t]=e+i})}}if(Object.assign(t,{slides:p,snapGrid:f,slidesGrid:m,slidesSizesGrid:g}),s.centeredSlides&&s.cssMode&&!s.centeredSlidesBounds){(0,o.a)(r,"--swiper-centered-offset-before",`${-f[0]}px`),(0,o.a)(r,"--swiper-centered-offset-after",`${t.size/2-g[g.length-1]/2}px`);let e=-t.snapGrid[0],i=-t.slidesGrid[0];t.snapGrid=t.snapGrid.map(t=>t+e),t.slidesGrid=t.slidesGrid.map(e=>e+i)}if(h!==u&&t.emit("slidesLengthChange"),f.length!==w&&(t.params.watchOverflow&&t.checkOverflow(),t.emit("snapGridLengthChange")),m.length!==y&&t.emit("slidesGridLengthChange"),s.watchSlidesProgress&&t.updateSlidesOffset(),t.emit("slidesUpdated"),!c&&!s.cssMode&&("slide"===s.effect||"fade"===s.effect)){let e=`${s.containerModifierClass}backface-hidden`,i=t.el.classList.contains(e);h<=s.maxBackfaceHiddenSlides?i||t.el.classList.add(e):i&&t.el.classList.remove(e)}},updateAutoHeight:function(e){let t;let i=this,s=[],r=i.virtual&&i.params.virtual.enabled,l=0;"number"==typeof e?i.setTransition(e):!0===e&&i.setTransition(i.params.speed);let a=e=>r?i.slides[i.getSlideIndexByData(e)]:i.slides[e];if("auto"!==i.params.slidesPerView&&i.params.slidesPerView>1){if(i.params.centeredSlides)(i.visibleSlides||[]).forEach(e=>{s.push(e)});else for(t=0;t<Math.ceil(i.params.slidesPerView);t+=1){let e=i.activeIndex+t;if(e>i.slides.length&&!r)break;s.push(a(e))}}else s.push(a(i.activeIndex));for(t=0;t<s.length;t+=1)if(void 0!==s[t]){let e=s[t].offsetHeight;l=e>l?e:l}(l||0===l)&&(i.wrapperEl.style.height=`${l}px`)},updateSlidesOffset:function(){let e=this.slides,t=this.isElement?this.isHorizontal()?this.wrapperEl.offsetLeft:this.wrapperEl.offsetTop:0;for(let i=0;i<e.length;i+=1)e[i].swiperSlideOffset=(this.isHorizontal()?e[i].offsetLeft:e[i].offsetTop)-t-this.cssOverflowAdjustment()},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);let t=this.params,{slides:i,rtlTranslate:s,snapGrid:r}=this;if(0===i.length)return;void 0===i[0].swiperSlideOffset&&this.updateSlidesOffset();let l=-e;s&&(l=e),this.visibleSlidesIndexes=[],this.visibleSlides=[];let a=t.spaceBetween;"string"==typeof a&&a.indexOf("%")>=0?a=parseFloat(a.replace("%",""))/100*this.size:"string"==typeof a&&(a=parseFloat(a));for(let e=0;e<i.length;e+=1){let n=i[e],o=n.swiperSlideOffset;t.cssMode&&t.centeredSlides&&(o-=i[0].swiperSlideOffset);let d=(l+(t.centeredSlides?this.minTranslate():0)-o)/(n.swiperSlideSize+a),c=(l-r[0]+(t.centeredSlides?this.minTranslate():0)-o)/(n.swiperSlideSize+a),u=-(l-o),h=u+this.slidesSizesGrid[e],f=u>=0&&u<=this.size-this.slidesSizesGrid[e],m=u>=0&&u<this.size-1||h>1&&h<=this.size||u<=0&&h>=this.size;m&&(this.visibleSlides.push(n),this.visibleSlidesIndexes.push(e)),p(n,m,t.slideVisibleClass),p(n,f,t.slideFullyVisibleClass),n.progress=s?-d:d,n.originalProgress=s?-c:c}},updateProgress:function(e){if(void 0===e){let t=this.rtlTranslate?-1:1;e=this&&this.translate&&this.translate*t||0}let t=this.params,i=this.maxTranslate()-this.minTranslate(),{progress:s,isBeginning:r,isEnd:l,progressLoop:a}=this,n=r,o=l;if(0===i)s=0,r=!0,l=!0;else{s=(e-this.minTranslate())/i;let t=1>Math.abs(e-this.minTranslate()),a=1>Math.abs(e-this.maxTranslate());r=t||s<=0,l=a||s>=1,t&&(s=0),a&&(s=1)}if(t.loop){let t=this.getSlideIndexByData(0),i=this.getSlideIndexByData(this.slides.length-1),s=this.slidesGrid[t],r=this.slidesGrid[i],l=this.slidesGrid[this.slidesGrid.length-1],n=Math.abs(e);(a=n>=s?(n-s)/l:(n+l-r)/l)>1&&(a-=1)}Object.assign(this,{progress:s,progressLoop:a,isBeginning:r,isEnd:l}),(t.watchSlidesProgress||t.centeredSlides&&t.autoHeight)&&this.updateSlidesProgress(e),r&&!n&&this.emit("reachBeginning toEdge"),l&&!o&&this.emit("reachEnd toEdge"),(n&&!r||o&&!l)&&this.emit("fromEdge"),this.emit("progress",s)},updateSlidesClasses:function(){let e,t,i;let{slides:s,params:r,slidesEl:l,activeIndex:a}=this,n=this.virtual&&r.virtual.enabled,d=this.grid&&r.grid&&r.grid.rows>1,c=e=>(0,o.e)(l,`.${r.slideClass}${e}, swiper-slide${e}`)[0];if(n){if(r.loop){let t=a-this.virtual.slidesBefore;t<0&&(t=this.virtual.slides.length+t),t>=this.virtual.slides.length&&(t-=this.virtual.slides.length),e=c(`[data-swiper-slide-index="${t}"]`)}else e=c(`[data-swiper-slide-index="${a}"]`)}else d?(e=s.find(e=>e.column===a),i=s.find(e=>e.column===a+1),t=s.find(e=>e.column===a-1)):e=s[a];e&&!d&&(i=(0,o.r)(e,`.${r.slideClass}, swiper-slide`)[0],r.loop&&!i&&(i=s[0]),t=(0,o.t)(e,`.${r.slideClass}, swiper-slide`)[0],r.loop),s.forEach(s=>{h(s,s===e,r.slideActiveClass),h(s,s===i,r.slideNextClass),h(s,s===t,r.slidePrevClass)}),this.emitSlidesClasses()},updateActiveIndex:function(e){let t,i;let s=this,r=s.rtlTranslate?s.translate:-s.translate,{snapGrid:l,params:a,activeIndex:n,realIndex:o,snapIndex:d}=s,c=e,u=e=>{let t=e-s.virtual.slidesBefore;return t<0&&(t=s.virtual.slides.length+t),t>=s.virtual.slides.length&&(t-=s.virtual.slides.length),t};if(void 0===c&&(c=function(e){let t;let{slidesGrid:i,params:s}=e,r=e.rtlTranslate?e.translate:-e.translate;for(let e=0;e<i.length;e+=1)void 0!==i[e+1]?r>=i[e]&&r<i[e+1]-(i[e+1]-i[e])/2?t=e:r>=i[e]&&r<i[e+1]&&(t=e+1):r>=i[e]&&(t=e);return s.normalizeSlideIndex&&(t<0||void 0===t)&&(t=0),t}(s)),l.indexOf(r)>=0)t=l.indexOf(r);else{let e=Math.min(a.slidesPerGroupSkip,c);t=e+Math.floor((c-e)/a.slidesPerGroup)}if(t>=l.length&&(t=l.length-1),c===n&&!s.params.loop){t!==d&&(s.snapIndex=t,s.emit("snapIndexChange"));return}if(c===n&&s.params.loop&&s.virtual&&s.params.virtual.enabled){s.realIndex=u(c);return}let p=s.grid&&a.grid&&a.grid.rows>1;if(s.virtual&&a.virtual.enabled&&a.loop)i=u(c);else if(p){let e=s.slides.find(e=>e.column===c),t=parseInt(e.getAttribute("data-swiper-slide-index"),10);Number.isNaN(t)&&(t=Math.max(s.slides.indexOf(e),0)),i=Math.floor(t/a.grid.rows)}else if(s.slides[c]){let e=s.slides[c].getAttribute("data-swiper-slide-index");i=e?parseInt(e,10):c}else i=c;Object.assign(s,{previousSnapIndex:d,snapIndex:t,previousRealIndex:o,realIndex:i,previousIndex:n,activeIndex:c}),s.initialized&&g(s),s.emit("activeIndexChange"),s.emit("snapIndexChange"),(s.initialized||s.params.runCallbacksOnInit)&&(o!==i&&s.emit("realIndexChange"),s.emit("slideChange"))},updateClickedSlide:function(e,t){let i;let s=this.params,r=e.closest(`.${s.slideClass}, swiper-slide`);!r&&this.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach(e=>{!r&&e.matches&&e.matches(`.${s.slideClass}, swiper-slide`)&&(r=e)});let l=!1;if(r){for(let e=0;e<this.slides.length;e+=1)if(this.slides[e]===r){l=!0,i=e;break}}if(r&&l)this.clickedSlide=r,this.virtual&&this.params.virtual.enabled?this.clickedIndex=parseInt(r.getAttribute("data-swiper-slide-index"),10):this.clickedIndex=i;else{this.clickedSlide=void 0,this.clickedIndex=void 0;return}s.slideToClickedSlide&&void 0!==this.clickedIndex&&this.clickedIndex!==this.activeIndex&&this.slideToClickedSlide()}},translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");let{params:t,rtlTranslate:i,translate:s,wrapperEl:r}=this;if(t.virtualTranslate)return i?-s:s;if(t.cssMode)return s;let l=(0,o.k)(r,e);return l+=this.cssOverflowAdjustment(),i&&(l=-l),l||0},setTranslate:function(e,t){let{rtlTranslate:i,params:s,wrapperEl:r,progress:l}=this,a=0,n=0;this.isHorizontal()?a=i?-e:e:n=e,s.roundLengths&&(a=Math.floor(a),n=Math.floor(n)),this.previousTranslate=this.translate,this.translate=this.isHorizontal()?a:n,s.cssMode?r[this.isHorizontal()?"scrollLeft":"scrollTop"]=this.isHorizontal()?-a:-n:s.virtualTranslate||(this.isHorizontal()?a-=this.cssOverflowAdjustment():n-=this.cssOverflowAdjustment(),r.style.transform=`translate3d(${a}px, ${n}px, 0px)`);let o=this.maxTranslate()-this.minTranslate();(0===o?0:(e-this.minTranslate())/o)!==l&&this.updateProgress(e),this.emit("setTranslate",this.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,i,s,r){let l;void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),void 0===s&&(s=!0);let a=this,{params:n,wrapperEl:d}=a;if(a.animating&&n.preventInteractionOnTransition)return!1;let c=a.minTranslate(),u=a.maxTranslate();if(l=s&&e>c?c:s&&e<u?u:e,a.updateProgress(l),n.cssMode){let e=a.isHorizontal();if(0===t)d[e?"scrollLeft":"scrollTop"]=-l;else{if(!a.support.smoothScroll)return(0,o.u)({swiper:a,targetPosition:-l,side:e?"left":"top"}),!0;d.scrollTo({[e?"left":"top"]:-l,behavior:"smooth"})}return!0}return 0===t?(a.setTransition(0),a.setTranslate(l),i&&(a.emit("beforeTransitionStart",t,r),a.emit("transitionEnd"))):(a.setTransition(t),a.setTranslate(l),i&&(a.emit("beforeTransitionStart",t,r),a.emit("transitionStart")),a.animating||(a.animating=!0,a.onTranslateToWrapperTransitionEnd||(a.onTranslateToWrapperTransitionEnd=function(e){a&&!a.destroyed&&e.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onTranslateToWrapperTransitionEnd),a.onTranslateToWrapperTransitionEnd=null,delete a.onTranslateToWrapperTransitionEnd,a.animating=!1,i&&a.emit("transitionEnd"))}),a.wrapperEl.addEventListener("transitionend",a.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){this.params.cssMode||(this.wrapperEl.style.transitionDuration=`${e}ms`,this.wrapperEl.style.transitionDelay=0===e?"0ms":""),this.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);let{params:i}=this;i.cssMode||(i.autoHeight&&this.updateAutoHeight(),v({swiper:this,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);let{params:i}=this;this.animating=!1,i.cssMode||(this.setTransition(0),v({swiper:this,runCallbacks:e,direction:t,step:"End"}))}},slide:{slideTo:function(e,t,i,s,r){let l;void 0===e&&(e=0),void 0===i&&(i=!0),"string"==typeof e&&(e=parseInt(e,10));let a=this,n=e;n<0&&(n=0);let{params:d,snapGrid:c,slidesGrid:p,previousIndex:h,activeIndex:f,rtlTranslate:m,wrapperEl:g,enabled:v}=a;if(!v&&!s&&!r||a.destroyed||a.animating&&d.preventInteractionOnTransition)return!1;void 0===t&&(t=a.params.speed);let b=Math.min(a.params.slidesPerGroupSkip,n),w=b+Math.floor((n-b)/a.params.slidesPerGroup);w>=c.length&&(w=c.length-1);let y=-c[w];if(d.normalizeSlideIndex)for(let e=0;e<p.length;e+=1){let t=-Math.floor(100*y),i=Math.floor(100*p[e]),s=Math.floor(100*p[e+1]);void 0!==p[e+1]?t>=i&&t<s-(s-i)/2?n=e:t>=i&&t<s&&(n=e+1):t>=i&&(n=e)}if(a.initialized&&n!==f&&(!a.allowSlideNext&&(m?y>a.translate&&y>a.minTranslate():y<a.translate&&y<a.minTranslate())||!a.allowSlidePrev&&y>a.translate&&y>a.maxTranslate()&&(f||0)!==n))return!1;n!==(h||0)&&i&&a.emit("beforeSlideChangeStart"),a.updateProgress(y),l=n>f?"next":n<f?"prev":"reset";let S=a.virtual&&a.params.virtual.enabled;if(!(S&&r)&&(m&&-y===a.translate||!m&&y===a.translate))return a.updateActiveIndex(n),d.autoHeight&&a.updateAutoHeight(),a.updateSlidesClasses(),"slide"!==d.effect&&a.setTranslate(y),"reset"!==l&&(a.transitionStart(i,l),a.transitionEnd(i,l)),!1;if(d.cssMode){let e=a.isHorizontal(),i=m?y:-y;if(0===t)S&&(a.wrapperEl.style.scrollSnapType="none",a._immediateVirtual=!0),S&&!a._cssModeVirtualInitialSet&&a.params.initialSlide>0?(a._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{g[e?"scrollLeft":"scrollTop"]=i})):g[e?"scrollLeft":"scrollTop"]=i,S&&requestAnimationFrame(()=>{a.wrapperEl.style.scrollSnapType="",a._immediateVirtual=!1});else{if(!a.support.smoothScroll)return(0,o.u)({swiper:a,targetPosition:i,side:e?"left":"top"}),!0;g.scrollTo({[e?"left":"top"]:i,behavior:"smooth"})}return!0}let T=u().isSafari;return S&&!r&&T&&a.isElement&&a.virtual.update(!1,!1,n),a.setTransition(t),a.setTranslate(y),a.updateActiveIndex(n),a.updateSlidesClasses(),a.emit("beforeTransitionStart",t,s),a.transitionStart(i,l),0===t?a.transitionEnd(i,l):a.animating||(a.animating=!0,a.onSlideToWrapperTransitionEnd||(a.onSlideToWrapperTransitionEnd=function(e){a&&!a.destroyed&&e.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onSlideToWrapperTransitionEnd),a.onSlideToWrapperTransitionEnd=null,delete a.onSlideToWrapperTransitionEnd,a.transitionEnd(i,l))}),a.wrapperEl.addEventListener("transitionend",a.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,i,s){void 0===e&&(e=0),void 0===i&&(i=!0),"string"==typeof e&&(e=parseInt(e,10));let r=this;if(r.destroyed)return;void 0===t&&(t=r.params.speed);let l=r.grid&&r.params.grid&&r.params.grid.rows>1,a=e;if(r.params.loop){if(r.virtual&&r.params.virtual.enabled)a+=r.virtual.slidesBefore;else{let e;if(l){let t=a*r.params.grid.rows;e=r.slides.find(e=>1*e.getAttribute("data-swiper-slide-index")===t).column}else e=r.getSlideIndexByData(a);let t=l?Math.ceil(r.slides.length/r.params.grid.rows):r.slides.length,{centeredSlides:i}=r.params,n=r.params.slidesPerView;"auto"===n?n=r.slidesPerViewDynamic():(n=Math.ceil(parseFloat(r.params.slidesPerView,10)),i&&n%2==0&&(n+=1));let o=t-e<n;if(i&&(o=o||e<Math.ceil(n/2)),s&&i&&"auto"!==r.params.slidesPerView&&!l&&(o=!1),o){let s=i?e<r.activeIndex?"prev":"next":e-r.activeIndex-1<r.params.slidesPerView?"next":"prev";r.loopFix({direction:s,slideTo:!0,activeSlideIndex:"next"===s?e+1:e-t+1,slideRealIndex:"next"===s?r.realIndex:void 0})}if(l){let e=a*r.params.grid.rows;a=r.slides.find(t=>1*t.getAttribute("data-swiper-slide-index")===e).column}else a=r.getSlideIndexByData(a)}}return requestAnimationFrame(()=>{r.slideTo(a,t,i,s)}),r},slideNext:function(e,t,i){void 0===t&&(t=!0);let s=this,{enabled:r,params:l,animating:a}=s;if(!r||s.destroyed)return s;void 0===e&&(e=s.params.speed);let n=l.slidesPerGroup;"auto"===l.slidesPerView&&1===l.slidesPerGroup&&l.slidesPerGroupAuto&&(n=Math.max(s.slidesPerViewDynamic("current",!0),1));let o=s.activeIndex<l.slidesPerGroupSkip?1:n,d=s.virtual&&l.virtual.enabled;if(l.loop){if(a&&!d&&l.loopPreventsSliding)return!1;if(s.loopFix({direction:"next"}),s._clientLeft=s.wrapperEl.clientLeft,s.activeIndex===s.slides.length-1&&l.cssMode)return requestAnimationFrame(()=>{s.slideTo(s.activeIndex+o,e,t,i)}),!0}return l.rewind&&s.isEnd?s.slideTo(0,e,t,i):s.slideTo(s.activeIndex+o,e,t,i)},slidePrev:function(e,t,i){void 0===t&&(t=!0);let s=this,{params:r,snapGrid:l,slidesGrid:a,rtlTranslate:n,enabled:o,animating:d}=s;if(!o||s.destroyed)return s;void 0===e&&(e=s.params.speed);let c=s.virtual&&r.virtual.enabled;if(r.loop){if(d&&!c&&r.loopPreventsSliding)return!1;s.loopFix({direction:"prev"}),s._clientLeft=s.wrapperEl.clientLeft}function u(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}let p=u(n?s.translate:-s.translate),h=l.map(e=>u(e)),f=r.freeMode&&r.freeMode.enabled,m=l[h.indexOf(p)-1];if(void 0===m&&(r.cssMode||f)){let e;l.forEach((t,i)=>{p>=t&&(e=i)}),void 0!==e&&(m=f?l[e]:l[e>0?e-1:e])}let g=0;if(void 0!==m&&((g=a.indexOf(m))<0&&(g=s.activeIndex-1),"auto"===r.slidesPerView&&1===r.slidesPerGroup&&r.slidesPerGroupAuto&&(g=Math.max(g=g-s.slidesPerViewDynamic("previous",!0)+1,0))),r.rewind&&s.isBeginning){let r=s.params.virtual&&s.params.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1;return s.slideTo(r,e,t,i)}return r.loop&&0===s.activeIndex&&r.cssMode?(requestAnimationFrame(()=>{s.slideTo(g,e,t,i)}),!0):s.slideTo(g,e,t,i)},slideReset:function(e,t,i){if(void 0===t&&(t=!0),!this.destroyed)return void 0===e&&(e=this.params.speed),this.slideTo(this.activeIndex,e,t,i)},slideToClosest:function(e,t,i,s){if(void 0===t&&(t=!0),void 0===s&&(s=.5),this.destroyed)return;void 0===e&&(e=this.params.speed);let r=this.activeIndex,l=Math.min(this.params.slidesPerGroupSkip,r),a=l+Math.floor((r-l)/this.params.slidesPerGroup),n=this.rtlTranslate?this.translate:-this.translate;if(n>=this.snapGrid[a]){let e=this.snapGrid[a];n-e>(this.snapGrid[a+1]-e)*s&&(r+=this.params.slidesPerGroup)}else{let e=this.snapGrid[a-1];n-e<=(this.snapGrid[a]-e)*s&&(r-=this.params.slidesPerGroup)}return r=Math.min(r=Math.max(r,0),this.slidesGrid.length-1),this.slideTo(r,e,t,i)},slideToClickedSlide:function(){let e;let t=this;if(t.destroyed)return;let{params:i,slidesEl:s}=t,r="auto"===i.slidesPerView?t.slidesPerViewDynamic():i.slidesPerView,l=t.getSlideIndexWhenGrid(t.clickedIndex),a=t.isElement?"swiper-slide":`.${i.slideClass}`,n=t.grid&&t.params.grid&&t.params.grid.rows>1;if(i.loop){if(t.animating)return;e=parseInt(t.clickedSlide.getAttribute("data-swiper-slide-index"),10),i.centeredSlides?t.slideToLoop(e):l>(n?(t.slides.length-r)/2-(t.params.grid.rows-1):t.slides.length-r)?(t.loopFix(),l=t.getSlideIndex((0,o.e)(s,`${a}[data-swiper-slide-index="${e}"]`)[0]),(0,o.n)(()=>{t.slideTo(l)})):t.slideTo(l)}else t.slideTo(l)}},loop:{loopCreate:function(e,t){let i=this,{params:s,slidesEl:r}=i;if(!s.loop||i.virtual&&i.params.virtual.enabled)return;let l=i.grid&&s.grid&&s.grid.rows>1;s.loopAddBlankSlides&&(s.slidesPerGroup>1||l)&&(()=>{let e=(0,o.e)(r,`.${s.slideBlankClass}`);e.forEach(e=>{e.remove()}),e.length>0&&(i.recalcSlides(),i.updateSlides())})();let a=s.slidesPerGroup*(l?s.grid.rows:1),n=i.slides.length%a!=0,d=l&&i.slides.length%s.grid.rows!=0,c=e=>{for(let t=0;t<e;t+=1){let e=i.isElement?(0,o.c)("swiper-slide",[s.slideBlankClass]):(0,o.c)("div",[s.slideClass,s.slideBlankClass]);i.slidesEl.append(e)}};n?s.loopAddBlankSlides?(c(a-i.slides.length%a),i.recalcSlides(),i.updateSlides()):(0,o.v)("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)"):d&&(s.loopAddBlankSlides?(c(s.grid.rows-i.slides.length%s.grid.rows),i.recalcSlides(),i.updateSlides()):(0,o.v)("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)")),(0,o.e)(r,`.${s.slideClass}, swiper-slide`).forEach((e,t)=>{e.setAttribute("data-swiper-slide-index",t)}),i.loopFix({slideRealIndex:e,direction:s.centeredSlides?void 0:"next",initial:t})},loopFix:function(e){let{slideRealIndex:t,slideTo:i=!0,direction:s,setTranslate:r,activeSlideIndex:l,initial:a,byController:n,byMousewheel:d}=void 0===e?{}:e,c=this;if(!c.params.loop)return;c.emit("beforeLoopFix");let{slides:u,allowSlidePrev:p,allowSlideNext:h,slidesEl:f,params:m}=c,{centeredSlides:g,initialSlide:v}=m;if(c.allowSlidePrev=!0,c.allowSlideNext=!0,c.virtual&&m.virtual.enabled){i&&(m.centeredSlides||0!==c.snapIndex?m.centeredSlides&&c.snapIndex<m.slidesPerView?c.slideTo(c.virtual.slides.length+c.snapIndex,0,!1,!0):c.snapIndex===c.snapGrid.length-1&&c.slideTo(c.virtual.slidesBefore,0,!1,!0):c.slideTo(c.virtual.slides.length,0,!1,!0)),c.allowSlidePrev=p,c.allowSlideNext=h,c.emit("loopFix");return}let b=m.slidesPerView;"auto"===b?b=c.slidesPerViewDynamic():(b=Math.ceil(parseFloat(m.slidesPerView,10)),g&&b%2==0&&(b+=1));let w=m.slidesPerGroupAuto?b:m.slidesPerGroup,y=g?Math.max(w,Math.ceil(b/2)):w;y%w!=0&&(y+=w-y%w),y+=m.loopAdditionalSlides,c.loopedSlides=y;let S=c.grid&&m.grid&&m.grid.rows>1;u.length<b+y||"cards"===c.params.effect&&u.length<b+2*y?(0,o.v)("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):S&&"row"===m.grid.fill&&(0,o.v)("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");let T=[],E=[],x=S?Math.ceil(u.length/m.grid.rows):u.length,C=a&&x-v<b&&!g,_=C?v:c.activeIndex;void 0===l?l=c.getSlideIndex(u.find(e=>e.classList.contains(m.slideActiveClass))):_=l;let M="next"===s||!s,P="prev"===s||!s,L=0,k=0,O=(S?u[l].column:l)+(g&&void 0===r?-b/2+.5:0);if(O<y){L=Math.max(y-O,w);for(let e=0;e<y-O;e+=1){let t=e-Math.floor(e/x)*x;if(S){let e=x-t-1;for(let t=u.length-1;t>=0;t-=1)u[t].column===e&&T.push(t)}else T.push(x-t-1)}}else if(O+b>x-y){k=Math.max(O-(x-2*y),w),C&&(k=Math.max(k,b-x+v+1));for(let e=0;e<k;e+=1){let t=e-Math.floor(e/x)*x;S?u.forEach((e,i)=>{e.column===t&&E.push(i)}):E.push(t)}}if(c.__preventObserver__=!0,requestAnimationFrame(()=>{c.__preventObserver__=!1}),"cards"===c.params.effect&&u.length<b+2*y&&(E.includes(l)&&E.splice(E.indexOf(l),1),T.includes(l)&&T.splice(T.indexOf(l),1)),P&&T.forEach(e=>{u[e].swiperLoopMoveDOM=!0,f.prepend(u[e]),u[e].swiperLoopMoveDOM=!1}),M&&E.forEach(e=>{u[e].swiperLoopMoveDOM=!0,f.append(u[e]),u[e].swiperLoopMoveDOM=!1}),c.recalcSlides(),"auto"===m.slidesPerView?c.updateSlides():S&&(T.length>0&&P||E.length>0&&M)&&c.slides.forEach((e,t)=>{c.grid.updateSlide(t,e,c.slides)}),m.watchSlidesProgress&&c.updateSlidesOffset(),i){if(T.length>0&&P){if(void 0===t){let e=c.slidesGrid[_],t=c.slidesGrid[_+L]-e;d?c.setTranslate(c.translate-t):(c.slideTo(_+Math.ceil(L),0,!1,!0),r&&(c.touchEventsData.startTranslate=c.touchEventsData.startTranslate-t,c.touchEventsData.currentTranslate=c.touchEventsData.currentTranslate-t))}else if(r){let e=S?T.length/m.grid.rows:T.length;c.slideTo(c.activeIndex+e,0,!1,!0),c.touchEventsData.currentTranslate=c.translate}}else if(E.length>0&&M){if(void 0===t){let e=c.slidesGrid[_],t=c.slidesGrid[_-k]-e;d?c.setTranslate(c.translate-t):(c.slideTo(_-k,0,!1,!0),r&&(c.touchEventsData.startTranslate=c.touchEventsData.startTranslate-t,c.touchEventsData.currentTranslate=c.touchEventsData.currentTranslate-t))}else{let e=S?E.length/m.grid.rows:E.length;c.slideTo(c.activeIndex-e,0,!1,!0)}}}if(c.allowSlidePrev=p,c.allowSlideNext=h,c.controller&&c.controller.control&&!n){let e={slideRealIndex:t,direction:s,setTranslate:r,activeSlideIndex:l,byController:!0};Array.isArray(c.controller.control)?c.controller.control.forEach(t=>{!t.destroyed&&t.params.loop&&t.loopFix({...e,slideTo:t.params.slidesPerView===m.slidesPerView&&i})}):c.controller.control instanceof c.constructor&&c.controller.control.params.loop&&c.controller.control.loopFix({...e,slideTo:c.controller.control.params.slidesPerView===m.slidesPerView&&i})}c.emit("loopFix")},loopDestroy:function(){let{params:e,slidesEl:t}=this;if(!e.loop||!t||this.virtual&&this.params.virtual.enabled)return;this.recalcSlides();let i=[];this.slides.forEach(e=>{i[void 0===e.swiperSlideIndex?1*e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex]=e}),this.slides.forEach(e=>{e.removeAttribute("data-swiper-slide-index")}),i.forEach(e=>{t.append(e)}),this.recalcSlides(),this.slideTo(this.realIndex,0)}},grabCursor:{setGrabCursor:function(e){let t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;let i="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),i.style.cursor="move",i.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1})},unsetGrabCursor:function(){let e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1}))}},events:{attachEvents:function(){let{params:e}=this;this.onTouchStart=w.bind(this),this.onTouchMove=y.bind(this),this.onTouchEnd=S.bind(this),this.onDocumentTouchStart=_.bind(this),e.cssMode&&(this.onScroll=x.bind(this)),this.onClick=E.bind(this),this.onLoad=C.bind(this),M(this,"on")},detachEvents:function(){M(this,"off")}},breakpoints:{setBreakpoint:function(){let e=this,{realIndex:t,initialized:i,params:s,el:r}=e,l=s.breakpoints;if(!l||l&&0===Object.keys(l).length)return;let a=(0,n.g)(),d="window"!==s.breakpointsBase&&s.breakpointsBase?"container":s.breakpointsBase,c=["window","container"].includes(s.breakpointsBase)||!s.breakpointsBase?e.el:a.querySelector(s.breakpointsBase),u=e.getBreakpoint(l,d,c);if(!u||e.currentBreakpoint===u)return;let p=(u in l?l[u]:void 0)||e.originalParams,h=P(e,s),f=P(e,p),m=e.params.grabCursor,g=p.grabCursor,v=s.enabled;h&&!f?(r.classList.remove(`${s.containerModifierClass}grid`,`${s.containerModifierClass}grid-column`),e.emitContainerClasses()):!h&&f&&(r.classList.add(`${s.containerModifierClass}grid`),(p.grid.fill&&"column"===p.grid.fill||!p.grid.fill&&"column"===s.grid.fill)&&r.classList.add(`${s.containerModifierClass}grid-column`),e.emitContainerClasses()),m&&!g?e.unsetGrabCursor():!m&&g&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(t=>{if(void 0===p[t])return;let i=s[t]&&s[t].enabled,r=p[t]&&p[t].enabled;i&&!r&&e[t].disable(),!i&&r&&e[t].enable()});let b=p.direction&&p.direction!==s.direction,w=s.loop&&(p.slidesPerView!==s.slidesPerView||b),y=s.loop;b&&i&&e.changeDirection(),(0,o.x)(e.params,p);let S=e.params.enabled,T=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),v&&!S?e.disable():!v&&S&&e.enable(),e.currentBreakpoint=u,e.emit("_beforeBreakpoint",p),i&&(w?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!y&&T?(e.loopCreate(t),e.updateSlides()):y&&!T&&e.loopDestroy()),e.emit("breakpoint",p)},getBreakpoint:function(e,t,i){if(void 0===t&&(t="window"),!e||"container"===t&&!i)return;let s=!1,r=(0,n.a)(),l="window"===t?r.innerHeight:i.clientHeight,a=Object.keys(e).map(e=>"string"==typeof e&&0===e.indexOf("@")?{value:l*parseFloat(e.substr(1)),point:e}:{value:e,point:e});a.sort((e,t)=>parseInt(e.value,10)-parseInt(t.value,10));for(let e=0;e<a.length;e+=1){let{point:l,value:n}=a[e];"window"===t?r.matchMedia(`(min-width: ${n}px)`).matches&&(s=l):n<=i.clientWidth&&(s=l)}return s||"max"}},checkOverflow:{checkOverflow:function(){let{isLocked:e,params:t}=this,{slidesOffsetBefore:i}=t;if(i){let e=this.slides.length-1,t=this.slidesGrid[e]+this.slidesSizesGrid[e]+2*i;this.isLocked=this.size>t}else this.isLocked=1===this.snapGrid.length;!0===t.allowSlideNext&&(this.allowSlideNext=!this.isLocked),!0===t.allowSlidePrev&&(this.allowSlidePrev=!this.isLocked),e&&e!==this.isLocked&&(this.isEnd=!1),e!==this.isLocked&&this.emit(this.isLocked?"lock":"unlock")}},classes:{addClasses:function(){let{classNames:e,params:t,rtl:i,el:s,device:r}=this,l=function(e,t){let i=[];return e.forEach(e=>{"object"==typeof e?Object.keys(e).forEach(s=>{e[s]&&i.push(t+s)}):"string"==typeof e&&i.push(t+e)}),i}(["initialized",t.direction,{"free-mode":this.params.freeMode&&t.freeMode.enabled},{autoheight:t.autoHeight},{rtl:i},{grid:t.grid&&t.grid.rows>1},{"grid-column":t.grid&&t.grid.rows>1&&"column"===t.grid.fill},{android:r.android},{ios:r.ios},{"css-mode":t.cssMode},{centered:t.cssMode&&t.centeredSlides},{"watch-progress":t.watchSlidesProgress}],t.containerModifierClass);e.push(...l),s.classList.add(...e),this.emitContainerClasses()},removeClasses:function(){let{el:e,classNames:t}=this;e&&"string"!=typeof e&&(e.classList.remove(...t),this.emitContainerClasses())}}},O={};class z{constructor(){let e,t;for(var i=arguments.length,s=Array(i),r=0;r<i;r++)s[r]=arguments[r];1===s.length&&s[0].constructor&&"Object"===Object.prototype.toString.call(s[0]).slice(8,-1)?t=s[0]:[e,t]=s,t||(t={}),t=(0,o.x)({},t),e&&!t.el&&(t.el=e);let l=(0,n.g)();if(t.el&&"string"==typeof t.el&&l.querySelectorAll(t.el).length>1){let e=[];return l.querySelectorAll(t.el).forEach(i=>{let s=(0,o.x)({},t,{el:i});e.push(new z(s))}),e}let a=this;a.__swiper__=!0,a.support=d(),a.device=c({userAgent:t.userAgent}),a.browser=u(),a.eventsListeners={},a.eventsAnyListeners=[],a.modules=[...a.__modules__],t.modules&&Array.isArray(t.modules)&&a.modules.push(...t.modules);let p={};a.modules.forEach(e=>{var i;e({params:t,swiper:a,extendParams:(i=t,function(e){void 0===e&&(e={});let t=Object.keys(e)[0],s=e[t];if("object"!=typeof s||null===s||(!0===i[t]&&(i[t]={enabled:!0}),"navigation"===t&&i[t]&&i[t].enabled&&!i[t].prevEl&&!i[t].nextEl&&(i[t].auto=!0),["pagination","scrollbar"].indexOf(t)>=0&&i[t]&&i[t].enabled&&!i[t].el&&(i[t].auto=!0),!(t in i&&"enabled"in s))){(0,o.x)(p,e);return}"object"!=typeof i[t]||"enabled"in i[t]||(i[t].enabled=!0),i[t]||(i[t]={enabled:!1}),(0,o.x)(p,e)}),on:a.on.bind(a),once:a.once.bind(a),off:a.off.bind(a),emit:a.emit.bind(a)})});let h=(0,o.x)({},L,p);return a.params=(0,o.x)({},h,O,t),a.originalParams=(0,o.x)({},a.params),a.passedParams=(0,o.x)({},t),a.params&&a.params.on&&Object.keys(a.params.on).forEach(e=>{a.on(e,a.params.on[e])}),a.params&&a.params.onAny&&a.onAny(a.params.onAny),Object.assign(a,{enabled:a.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===a.params.direction,isVertical:()=>"vertical"===a.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return 8388608*Math.trunc(this.translate/8388608)},allowSlideNext:a.params.allowSlideNext,allowSlidePrev:a.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:a.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:a.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),a.emit("_swiper"),a.params.init&&a.init(),a}getDirectionLabel(e){return this.isHorizontal()?e:({width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"})[e]}getSlideIndex(e){let{slidesEl:t,params:i}=this,s=(0,o.e)(t,`.${i.slideClass}, swiper-slide`),r=(0,o.i)(s[0]);return(0,o.i)(e)-r}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find(t=>1*t.getAttribute("data-swiper-slide-index")===e))}getSlideIndexWhenGrid(e){return this.grid&&this.params.grid&&this.params.grid.rows>1&&("column"===this.params.grid.fill?e=Math.floor(e/this.params.grid.rows):"row"===this.params.grid.fill&&(e%=Math.ceil(this.slides.length/this.params.grid.rows))),e}recalcSlides(){let{slidesEl:e,params:t}=this;this.slides=(0,o.e)(e,`.${t.slideClass}, swiper-slide`)}enable(){this.enabled||(this.enabled=!0,this.params.grabCursor&&this.setGrabCursor(),this.emit("enable"))}disable(){this.enabled&&(this.enabled=!1,this.params.grabCursor&&this.unsetGrabCursor(),this.emit("disable"))}setProgress(e,t){e=Math.min(Math.max(e,0),1);let i=this.minTranslate(),s=(this.maxTranslate()-i)*e+i;this.translateTo(s,void 0===t?0:t),this.updateActiveIndex(),this.updateSlidesClasses()}emitContainerClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=e.el.className.split(" ").filter(t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){let t=this;return t.destroyed?"":e.className.split(" ").filter(e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)).join(" ")}emitSlidesClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=[];e.slides.forEach(i=>{let s=e.getSlideClasses(i);t.push({slideEl:i,classNames:s}),e.emit("_slideClass",i,s)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);let{params:i,slides:s,slidesGrid:r,slidesSizesGrid:l,size:a,activeIndex:n}=this,o=1;if("number"==typeof i.slidesPerView)return i.slidesPerView;if(i.centeredSlides){let e,t=s[n]?Math.ceil(s[n].swiperSlideSize):0;for(let i=n+1;i<s.length;i+=1)s[i]&&!e&&(t+=Math.ceil(s[i].swiperSlideSize),o+=1,t>a&&(e=!0));for(let i=n-1;i>=0;i-=1)s[i]&&!e&&(t+=s[i].swiperSlideSize,o+=1,t>a&&(e=!0))}else if("current"===e)for(let e=n+1;e<s.length;e+=1)(t?r[e]+l[e]-r[n]<a:r[e]-r[n]<a)&&(o+=1);else for(let e=n-1;e>=0;e-=1)r[n]-r[e]<a&&(o+=1);return o}update(){let e;let t=this;if(!t||t.destroyed)return;let{snapGrid:i,params:s}=t;function r(){let e=Math.min(Math.max(t.rtlTranslate?-1*t.translate:t.translate,t.maxTranslate()),t.minTranslate());t.setTranslate(e),t.updateActiveIndex(),t.updateSlidesClasses()}if(s.breakpoints&&t.setBreakpoint(),[...t.el.querySelectorAll('[loading="lazy"]')].forEach(e=>{e.complete&&f(t,e)}),t.updateSize(),t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),s.freeMode&&s.freeMode.enabled&&!s.cssMode)r(),s.autoHeight&&t.updateAutoHeight();else{if(("auto"===s.slidesPerView||s.slidesPerView>1)&&t.isEnd&&!s.centeredSlides){let i=t.virtual&&s.virtual.enabled?t.virtual.slides:t.slides;e=t.slideTo(i.length-1,0,!1,!0)}else e=t.slideTo(t.activeIndex,0,!1,!0);e||r()}s.watchOverflow&&i!==t.snapGrid&&t.checkOverflow(),t.emit("update")}changeDirection(e,t){void 0===t&&(t=!0);let i=this.params.direction;return e||(e="horizontal"===i?"vertical":"horizontal"),e===i||"horizontal"!==e&&"vertical"!==e||(this.el.classList.remove(`${this.params.containerModifierClass}${i}`),this.el.classList.add(`${this.params.containerModifierClass}${e}`),this.emitContainerClasses(),this.params.direction=e,this.slides.forEach(t=>{"vertical"===e?t.style.width="":t.style.height=""}),this.emit("changeDirection"),t&&this.update()),this}changeLanguageDirection(e){(!this.rtl||"rtl"!==e)&&(this.rtl||"ltr"!==e)&&(this.rtl="rtl"===e,this.rtlTranslate="horizontal"===this.params.direction&&this.rtl,this.rtl?(this.el.classList.add(`${this.params.containerModifierClass}rtl`),this.el.dir="rtl"):(this.el.classList.remove(`${this.params.containerModifierClass}rtl`),this.el.dir="ltr"),this.update())}mount(e){let t=this;if(t.mounted)return!0;let i=e||t.params.el;if("string"==typeof i&&(i=document.querySelector(i)),!i)return!1;i.swiper=t,i.parentNode&&i.parentNode.host&&i.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);let s=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`,r=i&&i.shadowRoot&&i.shadowRoot.querySelector?i.shadowRoot.querySelector(s()):(0,o.e)(i,s())[0];return!r&&t.params.createElements&&(r=(0,o.c)("div",t.params.wrapperClass),i.append(r),(0,o.e)(i,`.${t.params.slideClass}`).forEach(e=>{r.append(e)})),Object.assign(t,{el:i,wrapperEl:r,slidesEl:t.isElement&&!i.parentNode.host.slideSlots?i.parentNode.host:r,hostEl:t.isElement?i.parentNode.host:i,mounted:!0,rtl:"rtl"===i.dir.toLowerCase()||"rtl"===(0,o.q)(i,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===i.dir.toLowerCase()||"rtl"===(0,o.q)(i,"direction")),wrongRTL:"-webkit-box"===(0,o.q)(r,"display")}),!0}init(e){let t=this;if(t.initialized||!1===t.mount(e))return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(void 0,!0),t.attachEvents();let i=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&i.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),i.forEach(e=>{e.complete?f(t,e):e.addEventListener("load",e=>{f(t,e.target)})}),g(t),t.initialized=!0,g(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);let i=this,{params:s,el:r,wrapperEl:l,slides:a}=i;return void 0===i.params||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),s.loop&&i.loopDestroy(),t&&(i.removeClasses(),r&&"string"!=typeof r&&r.removeAttribute("style"),l&&l.removeAttribute("style"),a&&a.length&&a.forEach(e=>{e.classList.remove(s.slideVisibleClass,s.slideFullyVisibleClass,s.slideActiveClass,s.slideNextClass,s.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")})),i.emit("destroy"),Object.keys(i.eventsListeners).forEach(e=>{i.off(e)}),!1!==e&&(i.el&&"string"!=typeof i.el&&(i.el.swiper=null),(0,o.y)(i)),i.destroyed=!0),null}static extendDefaults(e){(0,o.x)(O,e)}static get extendedDefaults(){return O}static get defaults(){return L}static installModule(e){z.prototype.__modules__||(z.prototype.__modules__=[]);let t=z.prototype.__modules__;"function"==typeof e&&0>t.indexOf(e)&&t.push(e)}static use(e){return Array.isArray(e)?e.forEach(e=>z.installModule(e)):z.installModule(e),z}}Object.keys(k).forEach(e=>{Object.keys(k[e]).forEach(t=>{z.prototype[t]=k[e][t]})}),z.use([function(e){let{swiper:t,on:i,emit:s}=e,r=(0,n.a)(),l=null,a=null,o=()=>{t&&!t.destroyed&&t.initialized&&(s("beforeResize"),s("resize"))},d=()=>{t&&!t.destroyed&&t.initialized&&(l=new ResizeObserver(e=>{a=r.requestAnimationFrame(()=>{let{width:i,height:s}=t,r=i,l=s;e.forEach(e=>{let{contentBoxSize:i,contentRect:s,target:a}=e;a&&a!==t.el||(r=s?s.width:(i[0]||i).inlineSize,l=s?s.height:(i[0]||i).blockSize)}),(r!==i||l!==s)&&o()})})).observe(t.el)},c=()=>{a&&r.cancelAnimationFrame(a),l&&l.unobserve&&t.el&&(l.unobserve(t.el),l=null)},u=()=>{t&&!t.destroyed&&t.initialized&&s("orientationchange")};i("init",()=>{if(t.params.resizeObserver&&void 0!==r.ResizeObserver){d();return}r.addEventListener("resize",o),r.addEventListener("orientationchange",u)}),i("destroy",()=>{c(),r.removeEventListener("resize",o),r.removeEventListener("orientationchange",u)})},function(e){let{swiper:t,extendParams:i,on:s,emit:r}=e,l=[],a=(0,n.a)(),d=function(e,i){void 0===i&&(i={});let s=new(a.MutationObserver||a.WebkitMutationObserver)(e=>{if(t.__preventObserver__)return;if(1===e.length){r("observerUpdate",e[0]);return}let i=function(){r("observerUpdate",e[0])};a.requestAnimationFrame?a.requestAnimationFrame(i):a.setTimeout(i,0)});s.observe(e,{attributes:void 0===i.attributes||i.attributes,childList:t.isElement||(void 0===i.childList||i).childList,characterData:void 0===i.characterData||i.characterData}),l.push(s)};i({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",()=>{if(t.params.observer){if(t.params.observeParents){let e=(0,o.b)(t.hostEl);for(let t=0;t<e.length;t+=1)d(e[t])}d(t.hostEl,{childList:t.params.observeSlideChildren}),d(t.wrapperEl,{attributes:!1})}}),s("destroy",()=>{l.forEach(e=>{e.disconnect()}),l.splice(0,l.length)})}]);let A=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","swiperElementNodeName","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function I(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)&&!e.__swiper__}function D(e,t){let i=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>0>i.indexOf(e)).forEach(i=>{void 0===e[i]?e[i]=t[i]:I(t[i])&&I(e[i])&&Object.keys(t[i]).length>0?t[i].__swiper__?e[i]=t[i]:D(e[i],t[i]):e[i]=t[i]})}function $(e){return void 0===e&&(e={}),e.navigation&&void 0===e.navigation.nextEl&&void 0===e.navigation.prevEl}function j(e){return void 0===e&&(e={}),e.pagination&&void 0===e.pagination.el}function F(e){return void 0===e&&(e={}),e.scrollbar&&void 0===e.scrollbar.el}function G(e){void 0===e&&(e="");let t=e.split(" ").map(e=>e.trim()).filter(e=>!!e),i=[];return t.forEach(e=>{0>i.indexOf(e)&&i.push(e)}),i.join(" ")}let R=e=>{e&&!e.destroyed&&e.params.virtual&&(!e.params.virtual||e.params.virtual.enabled)&&(e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.emit("_virtualUpdated"),e.parallax&&e.params.parallax&&e.params.parallax.enabled&&e.parallax.setTranslate())};function N(){return(N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var s in i)Object.prototype.hasOwnProperty.call(i,s)&&(e[s]=i[s])}return e}).apply(this,arguments)}function B(e){return e.type&&e.type.displayName&&e.type.displayName.includes("SwiperSlide")}function V(e,t){return"undefined"==typeof window?(0,a.useEffect)(e,t):(0,a.useLayoutEffect)(e,t)}let H=(0,a.createContext)(null),q=(0,a.createContext)(null),W=(0,a.forwardRef)(function(e,t){var i;let{className:s,tag:r="div",wrapperTag:l="div",children:n,onSwiper:d,...c}=void 0===e?{}:e,u=!1,[p,h]=(0,a.useState)("swiper"),[f,m]=(0,a.useState)(null),[g,v]=(0,a.useState)(!1),b=(0,a.useRef)(!1),w=(0,a.useRef)(null),y=(0,a.useRef)(null),S=(0,a.useRef)(null),T=(0,a.useRef)(null),E=(0,a.useRef)(null),x=(0,a.useRef)(null),C=(0,a.useRef)(null),_=(0,a.useRef)(null),{params:M,passedParams:P,rest:k,events:O}=function(e,t){void 0===e&&(e={}),void 0===t&&(t=!0);let i={on:{}},s={},r={};D(i,L),i._emitClasses=!0,i.init=!1;let l={},a=A.map(e=>e.replace(/_/,""));return Object.keys(Object.assign({},e)).forEach(n=>{void 0!==e[n]&&(a.indexOf(n)>=0?I(e[n])?(i[n]={},r[n]={},D(i[n],e[n]),D(r[n],e[n])):(i[n]=e[n],r[n]=e[n]):0===n.search(/on[A-Z]/)&&"function"==typeof e[n]?t?s[`${n[2].toLowerCase()}${n.substr(3)}`]=e[n]:i.on[`${n[2].toLowerCase()}${n.substr(3)}`]=e[n]:l[n]=e[n])}),["navigation","pagination","scrollbar"].forEach(e=>{!0===i[e]&&(i[e]={}),!1===i[e]&&delete i[e]}),{params:i,passedParams:r,rest:l,events:s}}(c),{slides:H,slots:W}=function(e){let t=[],i={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]};return a.Children.toArray(e).forEach(e=>{if(B(e))t.push(e);else if(e.props&&e.props.slot&&i[e.props.slot])i[e.props.slot].push(e);else if(e.props&&e.props.children){let s=function e(t){let i=[];return a.Children.toArray(t).forEach(t=>{B(t)?i.push(t):t.props&&t.props.children&&e(t.props.children).forEach(e=>i.push(e))}),i}(e.props.children);s.length>0?s.forEach(e=>t.push(e)):i["container-end"].push(e)}else i["container-end"].push(e)}),{slides:t,slots:i}}(n),X=()=>{v(!g)};Object.assign(M.on,{_containerClasses(e,t){h(t)}});let Y=()=>{Object.assign(M.on,O),u=!0;let e={...M};if(delete e.wrapperClass,y.current=new z(e),y.current.virtual&&y.current.params.virtual.enabled){y.current.virtual.slides=H;let e={cache:!1,slides:H,renderExternal:m,renderExternalUpdate:!1};D(y.current.params.virtual,e),D(y.current.originalParams.virtual,e)}};w.current||Y(),y.current&&y.current.on("_beforeBreakpoint",X);let U=()=>{!u&&O&&y.current&&Object.keys(O).forEach(e=>{y.current.on(e,O[e])})},K=()=>{O&&y.current&&Object.keys(O).forEach(e=>{y.current.off(e,O[e])})};return(0,a.useEffect)(()=>()=>{y.current&&y.current.off("_beforeBreakpoint",X)}),(0,a.useEffect)(()=>{!b.current&&y.current&&(y.current.emitSlidesClasses(),b.current=!0)}),V(()=>{if(t&&(t.current=w.current),w.current)return y.current.destroyed&&Y(),function(e,t){let{el:i,nextEl:s,prevEl:r,paginationEl:l,scrollbarEl:a,swiper:n}=e;$(t)&&s&&r&&(n.params.navigation.nextEl=s,n.originalParams.navigation.nextEl=s,n.params.navigation.prevEl=r,n.originalParams.navigation.prevEl=r),j(t)&&l&&(n.params.pagination.el=l,n.originalParams.pagination.el=l),F(t)&&a&&(n.params.scrollbar.el=a,n.originalParams.scrollbar.el=a),n.init(i)}({el:w.current,nextEl:E.current,prevEl:x.current,paginationEl:C.current,scrollbarEl:_.current,swiper:y.current},M),d&&!y.current.destroyed&&d(y.current),()=>{y.current&&!y.current.destroyed&&y.current.destroy(!0,!1)}},[]),V(()=>{U();let e=function(e,t,i,s,r){let l=[];if(!t)return l;let a=e=>{0>l.indexOf(e)&&l.push(e)};if(i&&s){let e=s.map(r),t=i.map(r);e.join("")!==t.join("")&&a("children"),s.length!==i.length&&a("children")}return A.filter(e=>"_"===e[0]).map(e=>e.replace(/_/,"")).forEach(i=>{if(i in e&&i in t){if(I(e[i])&&I(t[i])){let s=Object.keys(e[i]),r=Object.keys(t[i]);s.length!==r.length?a(i):(s.forEach(s=>{e[i][s]!==t[i][s]&&a(i)}),r.forEach(s=>{e[i][s]!==t[i][s]&&a(i)}))}else e[i]!==t[i]&&a(i)}}),l}(P,S.current,H,T.current,e=>e.key);return S.current=P,T.current=H,e.length&&y.current&&!y.current.destroyed&&function(e){let t,i,s,r,l,a,n,d,{swiper:c,slides:u,passedParams:p,changedParams:h,nextEl:f,prevEl:m,scrollbarEl:g,paginationEl:v}=e,b=h.filter(e=>"children"!==e&&"direction"!==e&&"wrapperClass"!==e),{params:w,pagination:y,navigation:S,scrollbar:T,virtual:E,thumbs:x}=c;h.includes("thumbs")&&p.thumbs&&p.thumbs.swiper&&!p.thumbs.swiper.destroyed&&w.thumbs&&(!w.thumbs.swiper||w.thumbs.swiper.destroyed)&&(t=!0),h.includes("controller")&&p.controller&&p.controller.control&&w.controller&&!w.controller.control&&(i=!0),h.includes("pagination")&&p.pagination&&(p.pagination.el||v)&&(w.pagination||!1===w.pagination)&&y&&!y.el&&(s=!0),h.includes("scrollbar")&&p.scrollbar&&(p.scrollbar.el||g)&&(w.scrollbar||!1===w.scrollbar)&&T&&!T.el&&(r=!0),h.includes("navigation")&&p.navigation&&(p.navigation.prevEl||m)&&(p.navigation.nextEl||f)&&(w.navigation||!1===w.navigation)&&S&&!S.prevEl&&!S.nextEl&&(l=!0);let C=e=>{c[e]&&(c[e].destroy(),"navigation"===e?(c.isElement&&(c[e].prevEl.remove(),c[e].nextEl.remove()),w[e].prevEl=void 0,w[e].nextEl=void 0,c[e].prevEl=void 0,c[e].nextEl=void 0):(c.isElement&&c[e].el.remove(),w[e].el=void 0,c[e].el=void 0))};h.includes("loop")&&c.isElement&&(w.loop&&!p.loop?a=!0:!w.loop&&p.loop?n=!0:d=!0),b.forEach(e=>{if(I(w[e])&&I(p[e]))Object.assign(w[e],p[e]),("navigation"===e||"pagination"===e||"scrollbar"===e)&&"enabled"in p[e]&&!p[e].enabled&&C(e);else{let t=p[e];(!0===t||!1===t)&&("navigation"===e||"pagination"===e||"scrollbar"===e)?!1===t&&C(e):w[e]=p[e]}}),b.includes("controller")&&!i&&c.controller&&c.controller.control&&w.controller&&w.controller.control&&(c.controller.control=w.controller.control),h.includes("children")&&u&&E&&w.virtual.enabled?(E.slides=u,E.update(!0)):h.includes("virtual")&&E&&w.virtual.enabled&&(u&&(E.slides=u),E.update(!0)),h.includes("children")&&u&&w.loop&&(d=!0),t&&x.init()&&x.update(!0),i&&(c.controller.control=w.controller.control),s&&(c.isElement&&(!v||"string"==typeof v)&&((v=document.createElement("div")).classList.add("swiper-pagination"),v.part.add("pagination"),c.el.appendChild(v)),v&&(w.pagination.el=v),y.init(),y.render(),y.update()),r&&(c.isElement&&(!g||"string"==typeof g)&&((g=document.createElement("div")).classList.add("swiper-scrollbar"),g.part.add("scrollbar"),c.el.appendChild(g)),g&&(w.scrollbar.el=g),T.init(),T.updateSize(),T.setTranslate()),l&&(c.isElement&&(f&&"string"!=typeof f||((f=document.createElement("div")).classList.add("swiper-button-next"),(0,o.s)(f,c.hostEl.constructor.nextButtonSvg),f.part.add("button-next"),c.el.appendChild(f)),m&&"string"!=typeof m||((m=document.createElement("div")).classList.add("swiper-button-prev"),(0,o.s)(m,c.hostEl.constructor.prevButtonSvg),m.part.add("button-prev"),c.el.appendChild(m))),f&&(w.navigation.nextEl=f),m&&(w.navigation.prevEl=m),S.init(),S.update()),h.includes("allowSlideNext")&&(c.allowSlideNext=p.allowSlideNext),h.includes("allowSlidePrev")&&(c.allowSlidePrev=p.allowSlidePrev),h.includes("direction")&&c.changeDirection(p.direction,!1),(a||d)&&c.loopDestroy(),(n||d)&&c.loopCreate(),c.update()}({swiper:y.current,slides:H,passedParams:P,changedParams:e,nextEl:E.current,prevEl:x.current,scrollbarEl:_.current,paginationEl:C.current}),()=>{K()}}),V(()=>{R(y.current)},[f]),a.createElement(r,N({ref:w,className:G(`${p}${s?` ${s}`:""}`)},k),a.createElement(q.Provider,{value:y.current},W["container-start"],a.createElement(l,{className:(void 0===(i=M.wrapperClass)&&(i=""),i)?i.includes("swiper-wrapper")?i:`swiper-wrapper ${i}`:"swiper-wrapper"},W["wrapper-start"],M.virtual?function(e,t,i){if(!i)return null;let s=e=>{let i=e;return e<0?i=t.length+e:i>=t.length&&(i-=t.length),i},r=e.isHorizontal()?{[e.rtlTranslate?"right":"left"]:`${i.offset}px`}:{top:`${i.offset}px`},{from:l,to:n}=i,o=e.params.loop?-t.length:0,d=e.params.loop?2*t.length:t.length,c=[];for(let e=o;e<d;e+=1)e>=l&&e<=n&&c.push(t[s(e)]);return c.map((t,i)=>a.cloneElement(t,{swiper:e,style:r,key:t.props.virtualIndex||t.key||`slide-${i}`}))}(y.current,H,f):H.map((e,t)=>a.cloneElement(e,{swiper:y.current,swiperSlideIndex:t})),W["wrapper-end"]),$(M)&&a.createElement(a.Fragment,null,a.createElement("div",{ref:x,className:"swiper-button-prev"}),a.createElement("div",{ref:E,className:"swiper-button-next"})),F(M)&&a.createElement("div",{ref:_,className:"swiper-scrollbar"}),j(M)&&a.createElement("div",{ref:C,className:"swiper-pagination"}),W["container-end"]))});W.displayName="Swiper";let X=(0,a.forwardRef)(function(e,t){let{tag:i="div",children:s,className:r="",swiper:l,zoom:n,lazy:o,virtualIndex:d,swiperSlideIndex:c,...u}=void 0===e?{}:e,p=(0,a.useRef)(null),[h,f]=(0,a.useState)("swiper-slide"),[m,g]=(0,a.useState)(!1);function v(e,t,i){t===p.current&&f(i)}V(()=>{if(void 0!==c&&(p.current.swiperSlideIndex=c),t&&(t.current=p.current),p.current&&l){if(l.destroyed){"swiper-slide"!==h&&f("swiper-slide");return}return l.on("_slideClass",v),()=>{l&&l.off("_slideClass",v)}}}),V(()=>{l&&p.current&&!l.destroyed&&f(l.getSlideClasses(p.current))},[l]);let b={isActive:h.indexOf("swiper-slide-active")>=0,isVisible:h.indexOf("swiper-slide-visible")>=0,isPrev:h.indexOf("swiper-slide-prev")>=0,isNext:h.indexOf("swiper-slide-next")>=0},w=()=>"function"==typeof s?s(b):s;return a.createElement(i,N({ref:p,className:G(`${h}${r?` ${r}`:""}`),"data-swiper-slide-index":d,onLoad:()=>{g(!0)}},u),n&&a.createElement(H.Provider,{value:b},a.createElement("div",{className:"swiper-zoom-container","data-swiper-zoom":"number"==typeof n?n:void 0},w(),o&&!m&&a.createElement("div",{className:"swiper-lazy-preloader"}))),!n&&a.createElement(H.Provider,{value:b},w(),o&&!m&&a.createElement("div",{className:"swiper-lazy-preloader"})))});X.displayName="SwiperSlide"}}]);