"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9254],{39713:function(e,t,r){r.d(t,{default:function(){return o.a}});var n=r(74033),o=r.n(n)},74033:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return u},getImageProps:function(){return a}});let n=r(60723),o=r(25738),i=r(28863),l=n._(r(44543));function a(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let u=i.Image},25117:function(e,t,r){r.d(t,{ee:function(){return G},Eh:function(){return J},VY:function(){return q},fC:function(){return $},D7:function(){return k}});var n=r(32486),o=r(36329),i=r(54087),l="undefined"!=typeof document?n.useLayoutEffect:function(){};function a(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!a(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!a(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function u(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function f(e,t){let r=u(e);return Math.round(t*r)/r}function d(e){let t=n.useRef(e);return l(()=>{t.current=e}),t}let s=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?(0,o.x7)({element:r.current,padding:n}).fn(t):{}:r?(0,o.x7)({element:r,padding:n}).fn(t):{}}}),c=(e,t)=>({...(0,o.cv)(e),options:[e,t]}),p=(e,t)=>({...(0,o.uY)(e),options:[e,t]}),h=(e,t)=>({...(0,o.dr)(e),options:[e,t]}),g=(e,t)=>({...(0,o.RR)(e),options:[e,t]}),v=(e,t)=>({...(0,o.dp)(e),options:[e,t]}),m=(e,t)=>({...(0,o.Cp)(e),options:[e,t]}),y=(e,t)=>({...s(e),options:[e,t]});var x=r(89801),w=r(75376),b=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,w.jsx)(x.WV.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,w.jsx)("polygon",{points:"0,0 30,0 15,10"})})});b.displayName="Arrow";var A=r(29626),S=r(32192),P=r(15920),j=r(79315),C=r(30915),R="Popper",[O,k]=(0,S.b)(R),[z,_]=O(R),M=e=>{let{__scopePopper:t,children:r}=e,[o,i]=n.useState(null);return(0,w.jsx)(z,{scope:t,anchor:o,onAnchorChange:i,children:r})};M.displayName=R;var W="PopperAnchor",E=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:o,...i}=e,l=_(W,r),a=n.useRef(null),u=(0,A.e)(t,a);return n.useEffect(()=>{l.onAnchorChange((null==o?void 0:o.current)||a.current)}),o?null:(0,w.jsx)(x.WV.div,{...i,ref:u})});E.displayName=W;var H="PopperContent",[N,V]=O(H),Y=n.forwardRef((e,t)=>{var r,s,b,S,R,O,k,z;let{__scopePopper:M,side:W="bottom",sideOffset:E=0,align:V="center",alignOffset:Y=0,arrowPadding:I=0,avoidCollisions:B=!0,collisionBoundary:D=[],collisionPadding:$=0,sticky:G="partial",hideWhenDetached:q=!1,updatePositionStrategy:J="optimized",onPlaced:K,...Q}=e,T=_(H,M),[U,Z]=n.useState(null),ee=(0,A.e)(t,e=>Z(e)),[et,er]=n.useState(null),en=(0,C.t)(et),eo=null!==(k=null==en?void 0:en.width)&&void 0!==k?k:0,ei=null!==(z=null==en?void 0:en.height)&&void 0!==z?z:0,el="number"==typeof $?$:{top:0,right:0,bottom:0,left:0,...$},ea=Array.isArray(D)?D:[D],eu=ea.length>0,ef={padding:el,boundary:ea.filter(X),altBoundary:eu},{refs:ed,floatingStyles:es,placement:ec,isPositioned:ep,middlewareData:eh}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:s=[],platform:c,elements:{reference:p,floating:h}={},transform:g=!0,whileElementsMounted:v,open:m}=e,[y,x]=n.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[w,b]=n.useState(s);a(w,s)||b(s);let[A,S]=n.useState(null),[P,j]=n.useState(null),C=n.useCallback(e=>{e!==z.current&&(z.current=e,S(e))},[]),R=n.useCallback(e=>{e!==_.current&&(_.current=e,j(e))},[]),O=p||A,k=h||P,z=n.useRef(null),_=n.useRef(null),M=n.useRef(y),W=null!=v,E=d(v),H=d(c),N=d(m),V=n.useCallback(()=>{if(!z.current||!_.current)return;let e={placement:t,strategy:r,middleware:w};H.current&&(e.platform=H.current),(0,o.oo)(z.current,_.current,e).then(e=>{let t={...e,isPositioned:!1!==N.current};Y.current&&!a(M.current,t)&&(M.current=t,i.flushSync(()=>{x(t)}))})},[w,t,r,H,N]);l(()=>{!1===m&&M.current.isPositioned&&(M.current.isPositioned=!1,x(e=>({...e,isPositioned:!1})))},[m]);let Y=n.useRef(!1);l(()=>(Y.current=!0,()=>{Y.current=!1}),[]),l(()=>{if(O&&(z.current=O),k&&(_.current=k),O&&k){if(E.current)return E.current(O,k,V);V()}},[O,k,V,E,W]);let I=n.useMemo(()=>({reference:z,floating:_,setReference:C,setFloating:R}),[C,R]),B=n.useMemo(()=>({reference:O,floating:k}),[O,k]),D=n.useMemo(()=>{let e={position:r,left:0,top:0};if(!B.floating)return e;let t=f(B.floating,y.x),n=f(B.floating,y.y);return g?{...e,transform:"translate("+t+"px, "+n+"px)",...u(B.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,g,B.floating,y.x,y.y]);return n.useMemo(()=>({...y,update:V,refs:I,elements:B,floatingStyles:D}),[y,V,I,B,D])}({strategy:"fixed",placement:W+("center"!==V?"-"+V:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.Me)(...t,{animationFrame:"always"===J})},elements:{reference:T.anchor},middleware:[c({mainAxis:E+ei,alignmentAxis:Y}),B&&p({mainAxis:!0,crossAxis:!1,limiter:"partial"===G?h():void 0,...ef}),B&&g({...ef}),v({...ef,apply:e=>{let{elements:t,rects:r,availableWidth:n,availableHeight:o}=e,{width:i,height:l}=r.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(n,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),et&&y({element:et,padding:I}),F({arrowWidth:eo,arrowHeight:ei}),q&&m({strategy:"referenceHidden",...ef})]}),[eg,ev]=L(ec),em=(0,P.W)(K);(0,j.b)(()=>{ep&&(null==em||em())},[ep,em]);let ey=null===(r=eh.arrow)||void 0===r?void 0:r.x,ex=null===(s=eh.arrow)||void 0===s?void 0:s.y,ew=(null===(b=eh.arrow)||void 0===b?void 0:b.centerOffset)!==0,[eb,eA]=n.useState();return(0,j.b)(()=>{U&&eA(window.getComputedStyle(U).zIndex)},[U]),(0,w.jsx)("div",{ref:ed.setFloating,"data-radix-popper-content-wrapper":"",style:{...es,transform:ep?es.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eb,"--radix-popper-transform-origin":[null===(S=eh.transformOrigin)||void 0===S?void 0:S.x,null===(R=eh.transformOrigin)||void 0===R?void 0:R.y].join(" "),...(null===(O=eh.hide)||void 0===O?void 0:O.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,w.jsx)(N,{scope:M,placedSide:eg,onArrowChange:er,arrowX:ey,arrowY:ex,shouldHideArrow:ew,children:(0,w.jsx)(x.WV.div,{"data-side":eg,"data-align":ev,...Q,ref:ee,style:{...Q.style,animation:ep?void 0:"none"}})})})});Y.displayName=H;var I="PopperArrow",B={top:"bottom",right:"left",bottom:"top",left:"right"},D=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=V(I,r),i=B[o.placedSide];return(0,w.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,w.jsx)(b,{...n,ref:t,style:{...n.style,display:"block"}})})});function X(e){return null!==e}D.displayName=I;var F=e=>({name:"transformOrigin",options:e,fn(t){var r,n,o,i,l;let{placement:a,rects:u,middlewareData:f}=t,d=(null===(r=f.arrow)||void 0===r?void 0:r.centerOffset)!==0,s=d?0:e.arrowWidth,c=d?0:e.arrowHeight,[p,h]=L(a),g={start:"0%",center:"50%",end:"100%"}[h],v=(null!==(i=null===(n=f.arrow)||void 0===n?void 0:n.x)&&void 0!==i?i:0)+s/2,m=(null!==(l=null===(o=f.arrow)||void 0===o?void 0:o.y)&&void 0!==l?l:0)+c/2,y="",x="";return"bottom"===p?(y=d?g:"".concat(v,"px"),x="".concat(-c,"px")):"top"===p?(y=d?g:"".concat(v,"px"),x="".concat(u.floating.height+c,"px")):"right"===p?(y="".concat(-c,"px"),x=d?g:"".concat(m,"px")):"left"===p&&(y="".concat(u.floating.width+c,"px"),x=d?g:"".concat(m,"px")),{data:{x:y,y:x}}}});function L(e){let[t,r="center"]=e.split("-");return[t,r]}var $=M,G=E,q=Y,J=D},30915:function(e,t,r){r.d(t,{t:function(){return i}});var n=r(32486),o=r(79315);function i(e){let[t,r]=n.useState(void 0);return(0,o.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}}}]);