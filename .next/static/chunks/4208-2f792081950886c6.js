(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4208],{61690:function(t,e,n){var r;/*! decimal.js-light v2.5.1 https://github.com/MikeMcl/decimal.js-light/LICENCE */!function(o){"use strict";var i,a={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},u=!0,c="[DecimalError] ",l=c+"Invalid argument: ",s=c+"Exponent out of range: ",f=Math.floor,p=Math.pow,h=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,d=f(1286742750677284.5),y={};function v(t,e){var n,r,o,i,a,c,l,s,f=t.constructor,p=f.precision;if(!t.s||!e.s)return e.s||(e=new f(t)),u?A(e,p):e;if(l=t.d,s=e.d,a=t.e,o=e.e,l=l.slice(),i=a-o){for(i<0?(r=l,i=-i,c=s.length):(r=s,o=a,c=l.length),i>(c=(a=Math.ceil(p/7))>c?a+1:c+1)&&(i=c,r.length=1),r.reverse();i--;)r.push(0);r.reverse()}for((c=l.length)-(i=s.length)<0&&(i=c,r=s,s=l,l=r),n=0;i;)n=(l[--i]=l[i]+s[i]+n)/1e7|0,l[i]%=1e7;for(n&&(l.unshift(n),++o),c=l.length;0==l[--c];)l.pop();return e.d=l,e.e=o,u?A(e,p):e}function m(t,e,n){if(t!==~~t||t<e||t>n)throw Error(l+t)}function b(t){var e,n,r,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)(n=7-(r=t[e]+"").length)&&(i+=j(n)),i+=r;(n=7-(r=(a=t[e])+"").length)&&(i+=j(n))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}y.absoluteValue=y.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},y.comparedTo=y.cmp=function(t){var e,n,r,o;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,n=(r=this.d.length)<(o=t.d.length)?r:o;e<n;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return r===o?0:r>o^this.s<0?1:-1},y.decimalPlaces=y.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},y.dividedBy=y.div=function(t){return g(this,new this.constructor(t))},y.dividedToIntegerBy=y.idiv=function(t){var e=this.constructor;return A(g(this,new e(t),0,1),e.precision)},y.equals=y.eq=function(t){return!this.cmp(t)},y.exponent=function(){return O(this)},y.greaterThan=y.gt=function(t){return this.cmp(t)>0},y.greaterThanOrEqualTo=y.gte=function(t){return this.cmp(t)>=0},y.isInteger=y.isint=function(){return this.e>this.d.length-2},y.isNegative=y.isneg=function(){return this.s<0},y.isPositive=y.ispos=function(){return this.s>0},y.isZero=function(){return 0===this.s},y.lessThan=y.lt=function(t){return 0>this.cmp(t)},y.lessThanOrEqualTo=y.lte=function(t){return 1>this.cmp(t)},y.logarithm=y.log=function(t){var e,n=this.constructor,r=n.precision,o=r+5;if(void 0===t)t=new n(10);else if((t=new n(t)).s<1||t.eq(i))throw Error(c+"NaN");if(this.s<1)throw Error(c+(this.s?"NaN":"-Infinity"));return this.eq(i)?new n(0):(u=!1,e=g(S(this,o),S(t,o),o),u=!0,A(e,r))},y.minus=y.sub=function(t){return t=new this.constructor(t),this.s==t.s?E(this,t):v(this,(t.s=-t.s,t))},y.modulo=y.mod=function(t){var e,n=this.constructor,r=n.precision;if(!(t=new n(t)).s)throw Error(c+"NaN");return this.s?(u=!1,e=g(this,t,0,1).times(t),u=!0,this.minus(e)):A(new n(this),r)},y.naturalExponential=y.exp=function(){return x(this)},y.naturalLogarithm=y.ln=function(){return S(this)},y.negated=y.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},y.plus=y.add=function(t){return t=new this.constructor(t),this.s==t.s?v(this,t):E(this,(t.s=-t.s,t))},y.precision=y.sd=function(t){var e,n,r;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(l+t);if(e=O(this)+1,n=7*(r=this.d.length-1)+1,r=this.d[r]){for(;r%10==0;r/=10)n--;for(r=this.d[0];r>=10;r/=10)n++}return t&&e>n?e:n},y.squareRoot=y.sqrt=function(){var t,e,n,r,o,i,a,l=this.constructor;if(this.s<1){if(!this.s)return new l(0);throw Error(c+"NaN")}for(t=O(this),u=!1,0==(o=Math.sqrt(+this))||o==1/0?(((e=b(this.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=f((t+1)/2)-(t<0||t%2),r=new l(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):r=new l(o.toString()),o=a=(n=l.precision)+3;;)if(r=(i=r).plus(g(this,i,a+2)).times(.5),b(i.d).slice(0,a)===(e=b(r.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(A(i,n+1,0),i.times(i).eq(this)){r=i;break}}else if("9999"!=e)break;a+=4}return u=!0,A(r,n)},y.times=y.mul=function(t){var e,n,r,o,i,a,c,l,s,f=this.constructor,p=this.d,h=(t=new f(t)).d;if(!this.s||!t.s)return new f(0);for(t.s*=this.s,n=this.e+t.e,(l=p.length)<(s=h.length)&&(i=p,p=h,h=i,a=l,l=s,s=a),i=[],r=a=l+s;r--;)i.push(0);for(r=s;--r>=0;){for(e=0,o=l+r;o>r;)c=i[o]+h[r]*p[o-r-1]+e,i[o--]=c%1e7|0,e=c/1e7|0;i[o]=(i[o]+e)%1e7|0}for(;!i[--a];)i.pop();return e?++n:i.shift(),t.d=i,t.e=n,u?A(t,f.precision):t},y.toDecimalPlaces=y.todp=function(t,e){var n=this,r=n.constructor;return(n=new r(n),void 0===t)?n:(m(t,0,1e9),void 0===e?e=r.rounding:m(e,0,8),A(n,t+O(n)+1,e))},y.toExponential=function(t,e){var n,r=this,o=r.constructor;return void 0===t?n=k(r,!0):(m(t,0,1e9),void 0===e?e=o.rounding:m(e,0,8),n=k(r=A(new o(r),t+1,e),!0,t+1)),n},y.toFixed=function(t,e){var n,r,o=this.constructor;return void 0===t?k(this):(m(t,0,1e9),void 0===e?e=o.rounding:m(e,0,8),n=k((r=A(new o(this),t+O(this)+1,e)).abs(),!1,t+O(r)+1),this.isneg()&&!this.isZero()?"-"+n:n)},y.toInteger=y.toint=function(){var t=this.constructor;return A(new t(this),O(this)+1,t.rounding)},y.toNumber=function(){return+this},y.toPower=y.pow=function(t){var e,n,r,o,a,l,s=this,p=s.constructor,h=+(t=new p(t));if(!t.s)return new p(i);if(!(s=new p(s)).s){if(t.s<1)throw Error(c+"Infinity");return s}if(s.eq(i))return s;if(r=p.precision,t.eq(i))return A(s,r);if(l=(e=t.e)>=(n=t.d.length-1),a=s.s,l){if((n=h<0?-h:h)<=9007199254740991){for(o=new p(i),e=Math.ceil(r/7+4),u=!1;n%2&&M((o=o.times(s)).d,e),0!==(n=f(n/2));)M((s=s.times(s)).d,e);return u=!0,t.s<0?new p(i).div(o):A(o,r)}}else if(a<0)throw Error(c+"NaN");return a=a<0&&1&t.d[Math.max(e,n)]?-1:1,s.s=1,u=!1,o=t.times(S(s,r+12)),u=!0,(o=x(o)).s=a,o},y.toPrecision=function(t,e){var n,r,o=this,i=o.constructor;return void 0===t?(n=O(o),r=k(o,n<=i.toExpNeg||n>=i.toExpPos)):(m(t,1,1e9),void 0===e?e=i.rounding:m(e,0,8),n=O(o=A(new i(o),t,e)),r=k(o,t<=n||n<=i.toExpNeg,t)),r},y.toSignificantDigits=y.tosd=function(t,e){var n=this.constructor;return void 0===t?(t=n.precision,e=n.rounding):(m(t,1,1e9),void 0===e?e=n.rounding:m(e,0,8)),A(new n(this),t,e)},y.toString=y.valueOf=y.val=y.toJSON=function(){var t=O(this),e=this.constructor;return k(this,t<=e.toExpNeg||t>=e.toExpPos)};var g=function(){function t(t,e){var n,r=0,o=t.length;for(t=t.slice();o--;)n=t[o]*e+r,t[o]=n%1e7|0,r=n/1e7|0;return r&&t.unshift(r),t}function e(t,e,n,r){var o,i;if(n!=r)i=n>r?1:-1;else for(o=i=0;o<n;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function n(t,e,n){for(var r=0;n--;)t[n]-=r,r=t[n]<e[n]?1:0,t[n]=1e7*r+t[n]-e[n];for(;!t[0]&&t.length>1;)t.shift()}return function(r,o,i,a){var u,l,s,f,p,h,d,y,v,m,b,g,x,w,j,S,P,E,k=r.constructor,M=r.s==o.s?1:-1,_=r.d,T=o.d;if(!r.s)return new k(r);if(!o.s)throw Error(c+"Division by zero");for(s=0,l=r.e-o.e,P=T.length,j=_.length,y=(d=new k(M)).d=[];T[s]==(_[s]||0);)++s;if(T[s]>(_[s]||0)&&--l,(g=null==i?i=k.precision:a?i+(O(r)-O(o))+1:i)<0)return new k(0);if(g=g/7+2|0,s=0,1==P)for(f=0,T=T[0],g++;(s<j||f)&&g--;s++)x=1e7*f+(_[s]||0),y[s]=x/T|0,f=x%T|0;else{for((f=1e7/(T[0]+1)|0)>1&&(T=t(T,f),_=t(_,f),P=T.length,j=_.length),w=P,m=(v=_.slice(0,P)).length;m<P;)v[m++]=0;(E=T.slice()).unshift(0),S=T[0],T[1]>=1e7/2&&++S;do f=0,(u=e(T,v,P,m))<0?(b=v[0],P!=m&&(b=1e7*b+(v[1]||0)),(f=b/S|0)>1?(f>=1e7&&(f=1e7-1),h=(p=t(T,f)).length,m=v.length,1==(u=e(p,v,h,m))&&(f--,n(p,P<h?E:T,h))):(0==f&&(u=f=1),p=T.slice()),(h=p.length)<m&&p.unshift(0),n(v,p,m),-1==u&&(m=v.length,(u=e(T,v,P,m))<1&&(f++,n(v,P<m?E:T,m))),m=v.length):0===u&&(f++,v=[0]),y[s++]=f,u&&v[0]?v[m++]=_[w]||0:(v=[_[w]],m=1);while((w++<j||void 0!==v[0])&&g--)}return y[0]||y.shift(),d.e=l,A(d,a?i+O(d)+1:i)}}();function x(t,e){var n,r,o,a,c,l=0,f=0,h=t.constructor,d=h.precision;if(O(t)>16)throw Error(s+O(t));if(!t.s)return new h(i);for(null==e?(u=!1,c=d):c=e,a=new h(.03125);t.abs().gte(.1);)t=t.times(a),f+=5;for(c+=Math.log(p(2,f))/Math.LN10*2+5|0,n=r=o=new h(i),h.precision=c;;){if(r=A(r.times(t),c),n=n.times(++l),b((a=o.plus(g(r,n,c))).d).slice(0,c)===b(o.d).slice(0,c)){for(;f--;)o=A(o.times(o),c);return h.precision=d,null==e?(u=!0,A(o,d)):o}o=a}}function O(t){for(var e=7*t.e,n=t.d[0];n>=10;n/=10)e++;return e}function w(t,e,n){if(e>t.LN10.sd())throw u=!0,n&&(t.precision=n),Error(c+"LN10 precision limit exceeded");return A(new t(t.LN10),e)}function j(t){for(var e="";t--;)e+="0";return e}function S(t,e){var n,r,o,a,l,s,f,p,h,d=1,y=t,v=y.d,m=y.constructor,x=m.precision;if(y.s<1)throw Error(c+(y.s?"NaN":"-Infinity"));if(y.eq(i))return new m(0);if(null==e?(u=!1,p=x):p=e,y.eq(10))return null==e&&(u=!0),w(m,p);if(p+=10,m.precision=p,r=(n=b(v)).charAt(0),!(15e14>Math.abs(a=O(y))))return f=w(m,p+2,x).times(a+""),y=S(new m(r+"."+n.slice(1)),p-10).plus(f),m.precision=x,null==e?(u=!0,A(y,x)):y;for(;r<7&&1!=r||1==r&&n.charAt(1)>3;)r=(n=b((y=y.times(t)).d)).charAt(0),d++;for(a=O(y),r>1?(y=new m("0."+n),a++):y=new m(r+"."+n.slice(1)),s=l=y=g(y.minus(i),y.plus(i),p),h=A(y.times(y),p),o=3;;){if(l=A(l.times(h),p),b((f=s.plus(g(l,new m(o),p))).d).slice(0,p)===b(s.d).slice(0,p))return s=s.times(2),0!==a&&(s=s.plus(w(m,p+2,x).times(a+""))),s=g(s,new m(d),p),m.precision=x,null==e?(u=!0,A(s,x)):s;s=f,o+=2}}function P(t,e){var n,r,o;for((n=e.indexOf("."))>-1&&(e=e.replace(".","")),(r=e.search(/e/i))>0?(n<0&&(n=r),n+=+e.slice(r+1),e=e.substring(0,r)):n<0&&(n=e.length),r=0;48===e.charCodeAt(r);)++r;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(r,o)){if(o-=r,n=n-r-1,t.e=f(n/7),t.d=[],r=(n+1)%7,n<0&&(r+=7),r<o){for(r&&t.d.push(+e.slice(0,r)),o-=7;r<o;)t.d.push(+e.slice(r,r+=7));r=7-(e=e.slice(r)).length}else r-=o;for(;r--;)e+="0";if(t.d.push(+e),u&&(t.e>d||t.e<-d))throw Error(s+n)}else t.s=0,t.e=0,t.d=[0];return t}function A(t,e,n){var r,o,i,a,c,l,h,y,v=t.d;for(a=1,i=v[0];i>=10;i/=10)a++;if((r=e-a)<0)r+=7,o=e,h=v[y=0];else{if((y=Math.ceil((r+1)/7))>=(i=v.length))return t;for(a=1,h=i=v[y];i>=10;i/=10)a++;r%=7,o=r-7+a}if(void 0!==n&&(c=h/(i=p(10,a-o-1))%10|0,l=e<0||void 0!==v[y+1]||h%i,l=n<4?(c||l)&&(0==n||n==(t.s<0?3:2)):c>5||5==c&&(4==n||l||6==n&&(r>0?o>0?h/p(10,a-o):0:v[y-1])%10&1||n==(t.s<0?8:7))),e<1||!v[0])return l?(i=O(t),v.length=1,e=e-i-1,v[0]=p(10,(7-e%7)%7),t.e=f(-e/7)||0):(v.length=1,v[0]=t.e=t.s=0),t;if(0==r?(v.length=y,i=1,y--):(v.length=y+1,i=p(10,7-r),v[y]=o>0?(h/p(10,a-o)%p(10,o)|0)*i:0),l)for(;;){if(0==y){1e7==(v[0]+=i)&&(v[0]=1,++t.e);break}if(v[y]+=i,1e7!=v[y])break;v[y--]=0,i=1}for(r=v.length;0===v[--r];)v.pop();if(u&&(t.e>d||t.e<-d))throw Error(s+O(t));return t}function E(t,e){var n,r,o,i,a,c,l,s,f,p,h=t.constructor,d=h.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new h(t),u?A(e,d):e;if(l=t.d,p=e.d,r=e.e,s=t.e,l=l.slice(),a=s-r){for((f=a<0)?(n=l,a=-a,c=p.length):(n=p,r=s,c=l.length),a>(o=Math.max(Math.ceil(d/7),c)+2)&&(a=o,n.length=1),n.reverse(),o=a;o--;)n.push(0);n.reverse()}else{for((f=(o=l.length)<(c=p.length))&&(c=o),o=0;o<c;o++)if(l[o]!=p[o]){f=l[o]<p[o];break}a=0}for(f&&(n=l,l=p,p=n,e.s=-e.s),c=l.length,o=p.length-c;o>0;--o)l[c++]=0;for(o=p.length;o>a;){if(l[--o]<p[o]){for(i=o;i&&0===l[--i];)l[i]=1e7-1;--l[i],l[o]+=1e7}l[o]-=p[o]}for(;0===l[--c];)l.pop();for(;0===l[0];l.shift())--r;return l[0]?(e.d=l,e.e=r,u?A(e,d):e):new h(0)}function k(t,e,n){var r,o=O(t),i=b(t.d),a=i.length;return e?(n&&(r=n-a)>0?i=i.charAt(0)+"."+i.slice(1)+j(r):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+j(-o-1)+i,n&&(r=n-a)>0&&(i+=j(r))):o>=a?(i+=j(o+1-a),n&&(r=n-o-1)>0&&(i=i+"."+j(r))):((r=o+1)<a&&(i=i.slice(0,r)+"."+i.slice(r)),n&&(r=n-a)>0&&(o+1===a&&(i+="."),i+=j(r))),t.s<0?"-"+i:i}function M(t,e){if(t.length>e)return t.length=e,!0}function _(t){if(!t||"object"!=typeof t)throw Error(c+"Object expected");var e,n,r,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(r=t[n=o[e]])){if(f(r)===r&&r>=o[e+1]&&r<=o[e+2])this[n]=r;else throw Error(l+n+": "+r)}if(void 0!==(r=t[n="LN10"])){if(r==Math.LN10)this[n]=new this(r);else throw Error(l+n+": "+r)}return this}(a=function t(e){var n,r,o;function i(t){if(!(this instanceof i))return new i(t);if(this.constructor=i,t instanceof i){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(l+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return P(this,t.toString())}if("string"!=typeof t)throw Error(l+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,h.test(t))P(this,t);else throw Error(l+t)}if(i.prototype=y,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=_,void 0===e&&(e={}),e)for(n=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];n<o.length;)e.hasOwnProperty(r=o[n++])||(e[r]=this[r]);return i.config(e),i}(a)).default=a.Decimal=a,i=new a(1),void 0!==(r=(function(){return a}).call(e,n,e,t))&&(t.exports=r)}(0)},70445:function(t){"use strict";var e=Object.prototype.hasOwnProperty,n="~";function r(){}function o(t,e,n){this.fn=t,this.context=e,this.once=n||!1}function i(t,e,r,i,a){if("function"!=typeof r)throw TypeError("The listener must be a function");var u=new o(r,i||t,a),c=n?n+e:e;return t._events[c]?t._events[c].fn?t._events[c]=[t._events[c],u]:t._events[c].push(u):(t._events[c]=u,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new r:delete t._events[e]}function u(){this._events=new r,this._eventsCount=0}Object.create&&(r.prototype=Object.create(null),new r().__proto__||(n=!1)),u.prototype.eventNames=function(){var t,r,o=[];if(0===this._eventsCount)return o;for(r in t=this._events)e.call(t,r)&&o.push(n?r.slice(1):r);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},u.prototype.listeners=function(t){var e=n?n+t:t,r=this._events[e];if(!r)return[];if(r.fn)return[r.fn];for(var o=0,i=r.length,a=Array(i);o<i;o++)a[o]=r[o].fn;return a},u.prototype.listenerCount=function(t){var e=n?n+t:t,r=this._events[e];return r?r.fn?1:r.length:0},u.prototype.emit=function(t,e,r,o,i,a){var u=n?n+t:t;if(!this._events[u])return!1;var c,l,s=this._events[u],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,r),!0;case 4:return s.fn.call(s.context,e,r,o),!0;case 5:return s.fn.call(s.context,e,r,o,i),!0;case 6:return s.fn.call(s.context,e,r,o,i,a),!0}for(l=1,c=Array(f-1);l<f;l++)c[l-1]=arguments[l];s.fn.apply(s.context,c)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,r);break;case 4:s[l].fn.call(s[l].context,e,r,o);break;default:if(!c)for(p=1,c=Array(f-1);p<f;p++)c[p-1]=arguments[p];s[l].fn.apply(s[l].context,c)}}return!0},u.prototype.on=function(t,e,n){return i(this,t,e,n,!1)},u.prototype.once=function(t,e,n){return i(this,t,e,n,!0)},u.prototype.removeListener=function(t,e,r,o){var i=n?n+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var u=this._events[i];if(u.fn)u.fn!==e||o&&!u.once||r&&u.context!==r||a(this,i);else{for(var c=0,l=[],s=u.length;c<s;c++)(u[c].fn!==e||o&&!u[c].once||r&&u[c].context!==r)&&l.push(u[c]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},u.prototype.removeAllListeners=function(t){var e;return t?(e=n?n+t:t,this._events[e]&&a(this,e)):(this._events=new r,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=n,u.EventEmitter=u,t.exports=u},17686:function(t,e,n){var r=n(91662)(n(29150),"DataView");t.exports=r},47863:function(t,e,n){var r=n(10721),o=n(96891),i=n(21697),a=n(7842),u=n(72134);function c(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}c.prototype.clear=r,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,t.exports=c},40746:function(t,e,n){var r=n(42763),o=n(52603),i=n(50418),a=n(83948),u=n(74982);function c(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}c.prototype.clear=r,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,t.exports=c},87509:function(t,e,n){var r=n(91662)(n(29150),"Map");t.exports=r},5165:function(t,e,n){var r=n(37624),o=n(96221),i=n(32810),a=n(11280),u=n(77283);function c(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}c.prototype.clear=r,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,t.exports=c},68318:function(t,e,n){var r=n(91662)(n(29150),"Promise");t.exports=r},58759:function(t,e,n){var r=n(91662)(n(29150),"Set");t.exports=r},35705:function(t,e,n){var r=n(5165),o=n(70842),i=n(86250);function a(t){var e=-1,n=null==t?0:t.length;for(this.__data__=new r;++e<n;)this.add(t[e])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,t.exports=a},10513:function(t,e,n){var r=n(40746),o=n(25992),i=n(56643),a=n(47636),u=n(34966),c=n(92540);function l(t){var e=this.__data__=new r(t);this.size=e.size}l.prototype.clear=o,l.prototype.delete=i,l.prototype.get=a,l.prototype.has=u,l.prototype.set=c,t.exports=l},48828:function(t,e,n){var r=n(29150).Symbol;t.exports=r},28052:function(t,e,n){var r=n(29150).Uint8Array;t.exports=r},79548:function(t,e,n){var r=n(91662)(n(29150),"WeakMap");t.exports=r},15330:function(t){t.exports=function(t,e,n){switch(n.length){case 0:return t.call(e);case 1:return t.call(e,n[0]);case 2:return t.call(e,n[0],n[1]);case 3:return t.call(e,n[0],n[1],n[2])}return t.apply(e,n)}},39607:function(t){t.exports=function(t,e){for(var n=-1,r=null==t?0:t.length;++n<r;)if(!e(t[n],n,t))return!1;return!0}},89735:function(t){t.exports=function(t,e){for(var n=-1,r=null==t?0:t.length,o=0,i=[];++n<r;){var a=t[n];e(a,n,t)&&(i[o++]=a)}return i}},17250:function(t,e,n){var r=n(61635);t.exports=function(t,e){return!!(null==t?0:t.length)&&r(t,e,0)>-1}},51646:function(t){t.exports=function(t,e,n){for(var r=-1,o=null==t?0:t.length;++r<o;)if(n(e,t[r]))return!0;return!1}},64850:function(t,e,n){var r=n(23553),o=n(25725),i=n(73660),a=n(84416),u=n(99710),c=n(14206),l=Object.prototype.hasOwnProperty;t.exports=function(t,e){var n=i(t),s=!n&&o(t),f=!n&&!s&&a(t),p=!n&&!s&&!f&&c(t),h=n||s||f||p,d=h?r(t.length,String):[],y=d.length;for(var v in t)(e||l.call(t,v))&&!(h&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||u(v,y)))&&d.push(v);return d}},42147:function(t){t.exports=function(t,e){for(var n=-1,r=null==t?0:t.length,o=Array(r);++n<r;)o[n]=e(t[n],n,t);return o}},65034:function(t){t.exports=function(t,e){for(var n=-1,r=e.length,o=t.length;++n<r;)t[o+n]=e[n];return t}},39739:function(t){t.exports=function(t,e){for(var n=-1,r=null==t?0:t.length;++n<r;)if(e(t[n],n,t))return!0;return!1}},24162:function(t){t.exports=function(t){return t.split("")}},93473:function(t,e,n){var r=n(26421);t.exports=function(t,e){for(var n=t.length;n--;)if(r(t[n][0],e))return n;return -1}},96496:function(t,e,n){var r=n(80707);t.exports=function(t,e,n){"__proto__"==e&&r?r(t,e,{configurable:!0,enumerable:!0,value:n,writable:!0}):t[e]=n}},90109:function(t,e,n){var r=n(5836),o=n(33479)(r);t.exports=o},75454:function(t,e,n){var r=n(90109);t.exports=function(t,e){var n=!0;return r(t,function(t,r,o){return n=!!e(t,r,o)}),n}},18835:function(t,e,n){var r=n(46065);t.exports=function(t,e,n){for(var o=-1,i=t.length;++o<i;){var a=t[o],u=e(a);if(null!=u&&(void 0===c?u==u&&!r(u):n(u,c)))var c=u,l=a}return l}},75736:function(t){t.exports=function(t,e,n,r){for(var o=t.length,i=n+(r?1:-1);r?i--:++i<o;)if(e(t[i],i,t))return i;return -1}},14766:function(t,e,n){var r=n(65034),o=n(48624);t.exports=function t(e,n,i,a,u){var c=-1,l=e.length;for(i||(i=o),u||(u=[]);++c<l;){var s=e[c];n>0&&i(s)?n>1?t(s,n-1,i,a,u):r(u,s):a||(u[u.length]=s)}return u}},30409:function(t,e,n){var r=n(64836)();t.exports=r},5836:function(t,e,n){var r=n(30409),o=n(65497);t.exports=function(t,e){return t&&r(t,e,o)}},63271:function(t,e,n){var r=n(6094),o=n(89478);t.exports=function(t,e){e=r(e,t);for(var n=0,i=e.length;null!=t&&n<i;)t=t[o(e[n++])];return n&&n==i?t:void 0}},76729:function(t,e,n){var r=n(65034),o=n(73660);t.exports=function(t,e,n){var i=e(t);return o(t)?i:r(i,n(t))}},90979:function(t,e,n){var r=n(48828),o=n(27586),i=n(9941),a=r?r.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?o(t):i(t)}},21179:function(t){t.exports=function(t,e){return t>e}},43864:function(t){t.exports=function(t,e){return null!=t&&e in Object(t)}},61635:function(t,e,n){var r=n(75736),o=n(51198),i=n(65886);t.exports=function(t,e,n){return e==e?i(t,e,n):r(t,o,n)}},29491:function(t,e,n){var r=n(90979),o=n(56251);t.exports=function(t){return o(t)&&"[object Arguments]"==r(t)}},60577:function(t,e,n){var r=n(64825),o=n(56251);t.exports=function t(e,n,i,a,u){return e===n||(null!=e&&null!=n&&(o(e)||o(n))?r(e,n,i,a,t,u):e!=e&&n!=n)}},64825:function(t,e,n){var r=n(10513),o=n(24415),i=n(17919),a=n(38531),u=n(9153),c=n(73660),l=n(84416),s=n(14206),f="[object Arguments]",p="[object Array]",h="[object Object]",d=Object.prototype.hasOwnProperty;t.exports=function(t,e,n,y,v,m){var b=c(t),g=c(e),x=b?p:u(t),O=g?p:u(e);x=x==f?h:x,O=O==f?h:O;var w=x==h,j=O==h,S=x==O;if(S&&l(t)){if(!l(e))return!1;b=!0,w=!1}if(S&&!w)return m||(m=new r),b||s(t)?o(t,e,n,y,v,m):i(t,e,x,n,y,v,m);if(!(1&n)){var P=w&&d.call(t,"__wrapped__"),A=j&&d.call(e,"__wrapped__");if(P||A){var E=P?t.value():t,k=A?e.value():e;return m||(m=new r),v(E,k,n,y,m)}}return!!S&&(m||(m=new r),a(t,e,n,y,v,m))}},29455:function(t,e,n){var r=n(10513),o=n(60577);t.exports=function(t,e,n,i){var a=n.length,u=a,c=!i;if(null==t)return!u;for(t=Object(t);a--;){var l=n[a];if(c&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++a<u;){var s=(l=n[a])[0],f=t[s],p=l[1];if(c&&l[2]){if(void 0===f&&!(s in t))return!1}else{var h=new r;if(i)var d=i(f,p,s,t,e,h);if(!(void 0===d?o(p,f,3,i,h):d))return!1}}return!0}},51198:function(t){t.exports=function(t){return t!=t}},2473:function(t,e,n){var r=n(14119),o=n(46284),i=n(5635),a=n(62301),u=/^\[object .+?Constructor\]$/,c=Object.prototype,l=Function.prototype.toString,s=c.hasOwnProperty,f=RegExp("^"+l.call(s).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!i(t)||o(t))&&(r(t)?f:u).test(a(t))}},51662:function(t,e,n){var r=n(90979),o=n(43015),i=n(56251),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return i(t)&&o(t.length)&&!!a[r(t)]}},38807:function(t,e,n){var r=n(65577),o=n(94411),i=n(50132),a=n(73660),u=n(53151);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?a(t)?o(t[0],t[1]):r(t):u(t)}},51741:function(t,e,n){var r=n(63668),o=n(35622),i=Object.prototype.hasOwnProperty;t.exports=function(t){if(!r(t))return o(t);var e=[];for(var n in Object(t))i.call(t,n)&&"constructor"!=n&&e.push(n);return e}},60056:function(t){t.exports=function(t,e){return t<e}},61594:function(t,e,n){var r=n(90109),o=n(55747);t.exports=function(t,e){var n=-1,i=o(t)?Array(t.length):[];return r(t,function(t,r,o){i[++n]=e(t,r,o)}),i}},65577:function(t,e,n){var r=n(29455),o=n(336),i=n(43186);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(n){return n===t||r(n,t,e)}}},94411:function(t,e,n){var r=n(60577),o=n(82404),i=n(949),a=n(35834),u=n(77651),c=n(43186),l=n(89478);t.exports=function(t,e){return a(t)&&u(e)?c(l(t),e):function(n){var a=o(n,t);return void 0===a&&a===e?i(n,t):r(e,a,3)}}},37681:function(t,e,n){var r=n(42147),o=n(63271),i=n(38807),a=n(61594),u=n(55896),c=n(33188),l=n(13011),s=n(50132),f=n(73660);t.exports=function(t,e,n){e=e.length?r(e,function(t){return f(t)?function(e){return o(e,1===t.length?t[0]:t)}:t}):[s];var p=-1;return e=r(e,c(i)),u(a(t,function(t,n,o){return{criteria:r(e,function(e){return e(t)}),index:++p,value:t}}),function(t,e){return l(t,e,n)})}},86432:function(t){t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},16704:function(t,e,n){var r=n(63271);t.exports=function(t){return function(e){return r(e,t)}}},15810:function(t){var e=Math.ceil,n=Math.max;t.exports=function(t,r,o,i){for(var a=-1,u=n(e((r-t)/(o||1)),0),c=Array(u);u--;)c[i?u:++a]=t,t+=o;return c}},31766:function(t,e,n){var r=n(50132),o=n(14723),i=n(15820);t.exports=function(t,e){return i(o(t,e,r),t+"")}},88097:function(t,e,n){var r=n(92594),o=n(80707),i=n(50132),a=o?function(t,e){return o(t,"toString",{configurable:!0,enumerable:!1,value:r(e),writable:!0})}:i;t.exports=a},89844:function(t){t.exports=function(t,e,n){var r=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(n=n>o?o:n)<0&&(n+=o),o=e>n?0:n-e>>>0,e>>>=0;for(var i=Array(o);++r<o;)i[r]=t[r+e];return i}},80341:function(t,e,n){var r=n(90109);t.exports=function(t,e){var n;return r(t,function(t,r,o){return!(n=e(t,r,o))}),!!n}},55896:function(t){t.exports=function(t,e){var n=t.length;for(t.sort(e);n--;)t[n]=t[n].value;return t}},23553:function(t){t.exports=function(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}},96016:function(t,e,n){var r=n(48828),o=n(42147),i=n(73660),a=n(46065),u=1/0,c=r?r.prototype:void 0,l=c?c.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(i(e))return o(e,t)+"";if(a(e))return l?l.call(e):"";var n=e+"";return"0"==n&&1/e==-u?"-0":n}},51609:function(t,e,n){var r=n(23058),o=/^\s+/;t.exports=function(t){return t?t.slice(0,r(t)+1).replace(o,""):t}},33188:function(t){t.exports=function(t){return function(e){return t(e)}}},62714:function(t,e,n){var r=n(35705),o=n(17250),i=n(51646),a=n(24600),u=n(98046),c=n(92814);t.exports=function(t,e,n){var l=-1,s=o,f=t.length,p=!0,h=[],d=h;if(n)p=!1,s=i;else if(f>=200){var y=e?null:u(t);if(y)return c(y);p=!1,s=a,d=new r}else d=e?[]:h;t:for(;++l<f;){var v=t[l],m=e?e(v):v;if(v=n||0!==v?v:0,p&&m==m){for(var b=d.length;b--;)if(d[b]===m)continue t;e&&d.push(m),h.push(v)}else s(d,m,n)||(d!==h&&d.push(m),h.push(v))}return h}},24600:function(t){t.exports=function(t,e){return t.has(e)}},6094:function(t,e,n){var r=n(73660),o=n(35834),i=n(97697),a=n(41572);t.exports=function(t,e){return r(t)?t:o(t,e)?[t]:i(a(t))}},73736:function(t,e,n){var r=n(89844);t.exports=function(t,e,n){var o=t.length;return n=void 0===n?o:n,!e&&n>=o?t:r(t,e,n)}},4494:function(t,e,n){var r=n(46065);t.exports=function(t,e){if(t!==e){var n=void 0!==t,o=null===t,i=t==t,a=r(t),u=void 0!==e,c=null===e,l=e==e,s=r(e);if(!c&&!s&&!a&&t>e||a&&u&&l&&!c&&!s||o&&u&&l||!n&&l||!i)return 1;if(!o&&!a&&!s&&t<e||s&&n&&i&&!o&&!a||c&&n&&i||!u&&i||!l)return -1}return 0}},13011:function(t,e,n){var r=n(4494);t.exports=function(t,e,n){for(var o=-1,i=t.criteria,a=e.criteria,u=i.length,c=n.length;++o<u;){var l=r(i[o],a[o]);if(l){if(o>=c)return l;return l*("desc"==n[o]?-1:1)}}return t.index-e.index}},14853:function(t,e,n){var r=n(29150)["__core-js_shared__"];t.exports=r},33479:function(t,e,n){var r=n(55747);t.exports=function(t,e){return function(n,o){if(null==n)return n;if(!r(n))return t(n,o);for(var i=n.length,a=e?i:-1,u=Object(n);(e?a--:++a<i)&&!1!==o(u[a],a,u););return n}}},64836:function(t){t.exports=function(t){return function(e,n,r){for(var o=-1,i=Object(e),a=r(e),u=a.length;u--;){var c=a[t?u:++o];if(!1===n(i[c],c,i))break}return e}}},9845:function(t,e,n){var r=n(73736),o=n(94579),i=n(25165),a=n(41572);t.exports=function(t){return function(e){var n=o(e=a(e))?i(e):void 0,u=n?n[0]:e.charAt(0),c=n?r(n,1).join(""):e.slice(1);return u[t]()+c}}},75266:function(t,e,n){var r=n(38807),o=n(55747),i=n(65497);t.exports=function(t){return function(e,n,a){var u=Object(e);if(!o(e)){var c=r(n,3);e=i(e),n=function(t){return c(u[t],t,u)}}var l=t(e,n,a);return l>-1?u[c?e[l]:l]:void 0}}},54423:function(t,e,n){var r=n(15810),o=n(51665),i=n(12846);t.exports=function(t){return function(e,n,a){return a&&"number"!=typeof a&&o(e,n,a)&&(n=a=void 0),e=i(e),void 0===n?(n=e,e=0):n=i(n),a=void 0===a?e<n?1:-1:i(a),r(e,n,a,t)}}},98046:function(t,e,n){var r=n(58759),o=n(38926),i=n(92814),a=r&&1/i(new r([,-0]))[1]==1/0?function(t){return new r(t)}:o;t.exports=a},80707:function(t,e,n){var r=n(91662),o=function(){try{var t=r(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();t.exports=o},24415:function(t,e,n){var r=n(35705),o=n(39739),i=n(24600);t.exports=function(t,e,n,a,u,c){var l=1&n,s=t.length,f=e.length;if(s!=f&&!(l&&f>s))return!1;var p=c.get(t),h=c.get(e);if(p&&h)return p==e&&h==t;var d=-1,y=!0,v=2&n?new r:void 0;for(c.set(t,e),c.set(e,t);++d<s;){var m=t[d],b=e[d];if(a)var g=l?a(b,m,d,e,t,c):a(m,b,d,t,e,c);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!o(e,function(t,e){if(!i(v,e)&&(m===t||u(m,t,n,a,c)))return v.push(e)})){y=!1;break}}else if(!(m===b||u(m,b,n,a,c))){y=!1;break}}return c.delete(t),c.delete(e),y}},17919:function(t,e,n){var r=n(48828),o=n(28052),i=n(26421),a=n(24415),u=n(39816),c=n(92814),l=r?r.prototype:void 0,s=l?l.valueOf:void 0;t.exports=function(t,e,n,r,l,f,p){switch(n){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":if(t.byteLength!=e.byteLength||!f(new o(t),new o(e)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var h=u;case"[object Set]":var d=1&r;if(h||(h=c),t.size!=e.size&&!d)break;var y=p.get(t);if(y)return y==e;r|=2,p.set(t,e);var v=a(h(t),h(e),r,l,f,p);return p.delete(t),v;case"[object Symbol]":if(s)return s.call(t)==s.call(e)}return!1}},38531:function(t,e,n){var r=n(72746),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,n,i,a,u){var c=1&n,l=r(t),s=l.length;if(s!=r(e).length&&!c)return!1;for(var f=s;f--;){var p=l[f];if(!(c?p in e:o.call(e,p)))return!1}var h=u.get(t),d=u.get(e);if(h&&d)return h==e&&d==t;var y=!0;u.set(t,e),u.set(e,t);for(var v=c;++f<s;){var m=t[p=l[f]],b=e[p];if(i)var g=c?i(b,m,p,e,t,u):i(m,b,p,t,e,u);if(!(void 0===g?m===b||a(m,b,n,i,u):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,O=e.constructor;x!=O&&"constructor"in t&&"constructor"in e&&!("function"==typeof x&&x instanceof x&&"function"==typeof O&&O instanceof O)&&(y=!1)}return u.delete(t),u.delete(e),y}},66893:function(t,e,n){var r="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g;t.exports=r},72746:function(t,e,n){var r=n(76729),o=n(96320),i=n(65497);t.exports=function(t){return r(t,i,o)}},16253:function(t,e,n){var r=n(63592);t.exports=function(t,e){var n=t.__data__;return r(e)?n["string"==typeof e?"string":"hash"]:n.map}},336:function(t,e,n){var r=n(77651),o=n(65497);t.exports=function(t){for(var e=o(t),n=e.length;n--;){var i=e[n],a=t[i];e[n]=[i,a,r(a)]}return e}},91662:function(t,e,n){var r=n(2473),o=n(32739);t.exports=function(t,e){var n=o(t,e);return r(n)?n:void 0}},58818:function(t,e,n){var r=n(73849)(Object.getPrototypeOf,Object);t.exports=r},27586:function(t,e,n){var r=n(48828),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,u=r?r.toStringTag:void 0;t.exports=function(t){var e=i.call(t,u),n=t[u];try{t[u]=void 0;var r=!0}catch(t){}var o=a.call(t);return r&&(e?t[u]=n:delete t[u]),o}},96320:function(t,e,n){var r=n(89735),o=n(10803),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols,u=a?function(t){return null==t?[]:r(a(t=Object(t)),function(e){return i.call(t,e)})}:o;t.exports=u},9153:function(t,e,n){var r=n(17686),o=n(87509),i=n(68318),a=n(58759),u=n(79548),c=n(90979),l=n(62301),s="[object Map]",f="[object Promise]",p="[object Set]",h="[object WeakMap]",d="[object DataView]",y=l(r),v=l(o),m=l(i),b=l(a),g=l(u),x=c;(r&&x(new r(new ArrayBuffer(1)))!=d||o&&x(new o)!=s||i&&x(i.resolve())!=f||a&&x(new a)!=p||u&&x(new u)!=h)&&(x=function(t){var e=c(t),n="[object Object]"==e?t.constructor:void 0,r=n?l(n):"";if(r)switch(r){case y:return d;case v:return s;case m:return f;case b:return p;case g:return h}return e}),t.exports=x},32739:function(t){t.exports=function(t,e){return null==t?void 0:t[e]}},3728:function(t,e,n){var r=n(6094),o=n(25725),i=n(73660),a=n(99710),u=n(43015),c=n(89478);t.exports=function(t,e,n){e=r(e,t);for(var l=-1,s=e.length,f=!1;++l<s;){var p=c(e[l]);if(!(f=null!=t&&n(t,p)))break;t=t[p]}return f||++l!=s?f:!!(s=null==t?0:t.length)&&u(s)&&a(p,s)&&(i(t)||o(t))}},94579:function(t){var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},10721:function(t,e,n){var r=n(78131);t.exports=function(){this.__data__=r?r(null):{},this.size=0}},96891:function(t){t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}},21697:function(t,e,n){var r=n(78131),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(r){var n=e[t];return"__lodash_hash_undefined__"===n?void 0:n}return o.call(e,t)?e[t]:void 0}},7842:function(t,e,n){var r=n(78131),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return r?void 0!==e[t]:o.call(e,t)}},72134:function(t,e,n){var r=n(78131);t.exports=function(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=r&&void 0===e?"__lodash_hash_undefined__":e,this}},48624:function(t,e,n){var r=n(48828),o=n(25725),i=n(73660),a=r?r.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(a&&t&&t[a])}},99710:function(t){var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,n){var r=typeof t;return!!(n=null==n?9007199254740991:n)&&("number"==r||"symbol"!=r&&e.test(t))&&t>-1&&t%1==0&&t<n}},51665:function(t,e,n){var r=n(26421),o=n(55747),i=n(99710),a=n(5635);t.exports=function(t,e,n){if(!a(n))return!1;var u=typeof e;return("number"==u?!!(o(n)&&i(e,n.length)):"string"==u&&e in n)&&r(n[e],t)}},35834:function(t,e,n){var r=n(73660),o=n(46065),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(r(t))return!1;var n=typeof t;return!!("number"==n||"symbol"==n||"boolean"==n||null==t||o(t))||a.test(t)||!i.test(t)||null!=e&&t in Object(e)}},63592:function(t){t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},46284:function(t,e,n){var r,o=n(14853),i=(r=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||""))?"Symbol(src)_1."+r:"";t.exports=function(t){return!!i&&i in t}},63668:function(t){var e=Object.prototype;t.exports=function(t){var n=t&&t.constructor;return t===("function"==typeof n&&n.prototype||e)}},77651:function(t,e,n){var r=n(5635);t.exports=function(t){return t==t&&!r(t)}},42763:function(t){t.exports=function(){this.__data__=[],this.size=0}},52603:function(t,e,n){var r=n(93473),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,n=r(e,t);return!(n<0)&&(n==e.length-1?e.pop():o.call(e,n,1),--this.size,!0)}},50418:function(t,e,n){var r=n(93473);t.exports=function(t){var e=this.__data__,n=r(e,t);return n<0?void 0:e[n][1]}},83948:function(t,e,n){var r=n(93473);t.exports=function(t){return r(this.__data__,t)>-1}},74982:function(t,e,n){var r=n(93473);t.exports=function(t,e){var n=this.__data__,o=r(n,t);return o<0?(++this.size,n.push([t,e])):n[o][1]=e,this}},37624:function(t,e,n){var r=n(47863),o=n(40746),i=n(87509);t.exports=function(){this.size=0,this.__data__={hash:new r,map:new(i||o),string:new r}}},96221:function(t,e,n){var r=n(16253);t.exports=function(t){var e=r(this,t).delete(t);return this.size-=e?1:0,e}},32810:function(t,e,n){var r=n(16253);t.exports=function(t){return r(this,t).get(t)}},11280:function(t,e,n){var r=n(16253);t.exports=function(t){return r(this,t).has(t)}},77283:function(t,e,n){var r=n(16253);t.exports=function(t,e){var n=r(this,t),o=n.size;return n.set(t,e),this.size+=n.size==o?0:1,this}},39816:function(t){t.exports=function(t){var e=-1,n=Array(t.size);return t.forEach(function(t,r){n[++e]=[r,t]}),n}},43186:function(t){t.exports=function(t,e){return function(n){return null!=n&&n[t]===e&&(void 0!==e||t in Object(n))}}},17057:function(t,e,n){var r=n(63212);t.exports=function(t){var e=r(t,function(t){return 500===n.size&&n.clear(),t}),n=e.cache;return e}},78131:function(t,e,n){var r=n(91662)(Object,"create");t.exports=r},35622:function(t,e,n){var r=n(73849)(Object.keys,Object);t.exports=r},66149:function(t,e,n){t=n.nmd(t);var r=n(66893),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o&&r.process,u=function(){try{var t=i&&i.require&&i.require("util").types;if(t)return t;return a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=u},9941:function(t){var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},73849:function(t){t.exports=function(t,e){return function(n){return t(e(n))}}},14723:function(t,e,n){var r=n(15330),o=Math.max;t.exports=function(t,e,n){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,a=-1,u=o(i.length-e,0),c=Array(u);++a<u;)c[a]=i[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=i[a];return l[e]=n(c),r(t,this,l)}}},29150:function(t,e,n){var r=n(66893),o="object"==typeof self&&self&&self.Object===Object&&self,i=r||o||Function("return this")();t.exports=i},70842:function(t){t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},86250:function(t){t.exports=function(t){return this.__data__.has(t)}},92814:function(t){t.exports=function(t){var e=-1,n=Array(t.size);return t.forEach(function(t){n[++e]=t}),n}},15820:function(t,e,n){var r=n(88097),o=n(52466)(r);t.exports=o},52466:function(t){var e=Date.now;t.exports=function(t){var n=0,r=0;return function(){var o=e(),i=16-(o-r);if(r=o,i>0){if(++n>=800)return arguments[0]}else n=0;return t.apply(void 0,arguments)}}},25992:function(t,e,n){var r=n(40746);t.exports=function(){this.__data__=new r,this.size=0}},56643:function(t){t.exports=function(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n}},47636:function(t){t.exports=function(t){return this.__data__.get(t)}},34966:function(t){t.exports=function(t){return this.__data__.has(t)}},92540:function(t,e,n){var r=n(40746),o=n(87509),i=n(5165);t.exports=function(t,e){var n=this.__data__;if(n instanceof r){var a=n.__data__;if(!o||a.length<199)return a.push([t,e]),this.size=++n.size,this;n=this.__data__=new i(a)}return n.set(t,e),this.size=n.size,this}},65886:function(t){t.exports=function(t,e,n){for(var r=n-1,o=t.length;++r<o;)if(t[r]===e)return r;return -1}},25165:function(t,e,n){var r=n(24162),o=n(94579),i=n(47976);t.exports=function(t){return o(t)?i(t):r(t)}},97697:function(t,e,n){var r=n(17057),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,a=r(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,function(t,n,r,o){e.push(r?o.replace(i,"$1"):n||t)}),e});t.exports=a},89478:function(t,e,n){var r=n(46065),o=1/0;t.exports=function(t){if("string"==typeof t||r(t))return t;var e=t+"";return"0"==e&&1/t==-o?"-0":e}},62301:function(t){var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},23058:function(t){var e=/\s/;t.exports=function(t){for(var n=t.length;n--&&e.test(t.charAt(n)););return n}},47976:function(t){var e="\ud800-\udfff",n="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",r="\ud83c[\udffb-\udfff]",o="[^"+e+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",u="(?:"+n+"|"+r+")?",c="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[o,i,a].join("|")+")"+c+u+")*",s=RegExp(r+"(?="+r+")|(?:"+[o+n+"?",n,i,a,"["+e+"]"].join("|")+")"+(c+u+l),"g");t.exports=function(t){return t.match(s)||[]}},92594:function(t){t.exports=function(t){return function(){return t}}},86146:function(t,e,n){var r=n(5635),o=n(48925),i=n(51088),a=Math.max,u=Math.min;t.exports=function(t,e,n){var c,l,s,f,p,h,d=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw TypeError("Expected a function");function b(e){var n=c,r=l;return c=l=void 0,d=e,f=t.apply(r,n)}function g(t){var n=t-h,r=t-d;return void 0===h||n>=e||n<0||v&&r>=s}function x(){var t,n,r,i=o();if(g(i))return O(i);p=setTimeout(x,(t=i-h,n=i-d,r=e-t,v?u(r,s-n):r))}function O(t){return(p=void 0,m&&c)?b(t):(c=l=void 0,f)}function w(){var t,n=o(),r=g(n);if(c=arguments,l=this,h=n,r){if(void 0===p)return d=t=h,p=setTimeout(x,e),y?b(t):f;if(v)return clearTimeout(p),p=setTimeout(x,e),b(h)}return void 0===p&&(p=setTimeout(x,e)),f}return e=i(e)||0,r(n)&&(y=!!n.leading,s=(v="maxWait"in n)?a(i(n.maxWait)||0,e):s,m="trailing"in n?!!n.trailing:m),w.cancel=function(){void 0!==p&&clearTimeout(p),d=0,c=h=l=p=void 0},w.flush=function(){return void 0===p?f:O(o())},w}},26421:function(t){t.exports=function(t,e){return t===e||t!=t&&e!=e}},86925:function(t,e,n){var r=n(39607),o=n(75454),i=n(38807),a=n(73660),u=n(51665);t.exports=function(t,e,n){var c=a(t)?r:o;return n&&u(t,e,n)&&(e=void 0),c(t,i(e,3))}},82009:function(t,e,n){var r=n(75266)(n(30810));t.exports=r},30810:function(t,e,n){var r=n(75736),o=n(38807),i=n(92282),a=Math.max;t.exports=function(t,e,n){var u=null==t?0:t.length;if(!u)return -1;var c=null==n?0:i(n);return c<0&&(c=a(u+c,0)),r(t,o(e,3),c)}},62343:function(t,e,n){var r=n(14766),o=n(2322);t.exports=function(t,e){return r(o(t,e),1)}},82404:function(t,e,n){var r=n(63271);t.exports=function(t,e,n){var o=null==t?void 0:r(t,e);return void 0===o?n:o}},949:function(t,e,n){var r=n(43864),o=n(3728);t.exports=function(t,e){return null!=t&&o(t,e,r)}},50132:function(t){t.exports=function(t){return t}},25725:function(t,e,n){var r=n(29491),o=n(56251),i=Object.prototype,a=i.hasOwnProperty,u=i.propertyIsEnumerable,c=r(function(){return arguments}())?r:function(t){return o(t)&&a.call(t,"callee")&&!u.call(t,"callee")};t.exports=c},73660:function(t){var e=Array.isArray;t.exports=e},55747:function(t,e,n){var r=n(14119),o=n(43015);t.exports=function(t){return null!=t&&o(t.length)&&!r(t)}},96634:function(t,e,n){var r=n(90979),o=n(56251);t.exports=function(t){return!0===t||!1===t||o(t)&&"[object Boolean]"==r(t)}},84416:function(t,e,n){t=n.nmd(t);var r=n(29150),o=n(80443),i=e&&!e.nodeType&&e,a=i&&t&&!t.nodeType&&t,u=a&&a.exports===i?r.Buffer:void 0,c=u?u.isBuffer:void 0;t.exports=c||o},25010:function(t,e,n){var r=n(60577);t.exports=function(t,e){return r(t,e)}},14119:function(t,e,n){var r=n(90979),o=n(5635);t.exports=function(t){if(!o(t))return!1;var e=r(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},43015:function(t){t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},49572:function(t,e,n){var r=n(74337);t.exports=function(t){return r(t)&&t!=+t}},86197:function(t){t.exports=function(t){return null==t}},74337:function(t,e,n){var r=n(90979),o=n(56251);t.exports=function(t){return"number"==typeof t||o(t)&&"[object Number]"==r(t)}},5635:function(t){t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},56251:function(t){t.exports=function(t){return null!=t&&"object"==typeof t}},85758:function(t,e,n){var r=n(90979),o=n(58818),i=n(56251),a=Object.prototype,u=Function.prototype.toString,c=a.hasOwnProperty,l=u.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=r(t))return!1;var e=o(t);if(null===e)return!0;var n=c.call(e,"constructor")&&e.constructor;return"function"==typeof n&&n instanceof n&&u.call(n)==l}},62066:function(t,e,n){var r=n(90979),o=n(73660),i=n(56251);t.exports=function(t){return"string"==typeof t||!o(t)&&i(t)&&"[object String]"==r(t)}},46065:function(t,e,n){var r=n(90979),o=n(56251);t.exports=function(t){return"symbol"==typeof t||o(t)&&"[object Symbol]"==r(t)}},14206:function(t,e,n){var r=n(51662),o=n(33188),i=n(66149),a=i&&i.isTypedArray,u=a?o(a):r;t.exports=u},65497:function(t,e,n){var r=n(64850),o=n(51741),i=n(55747);t.exports=function(t){return i(t)?r(t):o(t)}},3249:function(t){t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},2322:function(t,e,n){var r=n(42147),o=n(38807),i=n(61594),a=n(73660);t.exports=function(t,e){return(a(t)?r:i)(t,o(e,3))}},16686:function(t,e,n){var r=n(96496),o=n(5836),i=n(38807);t.exports=function(t,e){var n={};return e=i(e,3),o(t,function(t,o,i){r(n,o,e(t,o,i))}),n}},60440:function(t,e,n){var r=n(18835),o=n(21179),i=n(50132);t.exports=function(t){return t&&t.length?r(t,i,o):void 0}},31896:function(t,e,n){var r=n(18835),o=n(21179),i=n(38807);t.exports=function(t,e){return t&&t.length?r(t,i(e,2),o):void 0}},63212:function(t,e,n){var r=n(5165);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw TypeError("Expected a function");var n=function(){var r=arguments,o=e?e.apply(this,r):r[0],i=n.cache;if(i.has(o))return i.get(o);var a=t.apply(this,r);return n.cache=i.set(o,a)||i,a};return n.cache=new(o.Cache||r),n}o.Cache=r,t.exports=o},97387:function(t,e,n){var r=n(18835),o=n(60056),i=n(50132);t.exports=function(t){return t&&t.length?r(t,i,o):void 0}},28906:function(t,e,n){var r=n(18835),o=n(38807),i=n(60056);t.exports=function(t,e){return t&&t.length?r(t,o(e,2),i):void 0}},38926:function(t){t.exports=function(){}},48925:function(t,e,n){var r=n(29150);t.exports=function(){return r.Date.now()}},53151:function(t,e,n){var r=n(86432),o=n(16704),i=n(35834),a=n(89478);t.exports=function(t){return i(t)?r(a(t)):o(t)}},84452:function(t,e,n){var r=n(54423)();t.exports=r},82391:function(t,e,n){var r=n(39739),o=n(38807),i=n(80341),a=n(73660),u=n(51665);t.exports=function(t,e,n){var c=a(t)?r:i;return n&&u(t,e,n)&&(e=void 0),c(t,o(e,3))}},9313:function(t,e,n){var r=n(14766),o=n(37681),i=n(31766),a=n(51665),u=i(function(t,e){if(null==t)return[];var n=e.length;return n>1&&a(t,e[0],e[1])?e=[]:n>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),o(t,r(e,1),[])});t.exports=u},10803:function(t){t.exports=function(){return[]}},80443:function(t){t.exports=function(){return!1}},3787:function(t,e,n){var r=n(86146),o=n(5635);t.exports=function(t,e,n){var i=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return o(n)&&(i="leading"in n?!!n.leading:i,a="trailing"in n?!!n.trailing:a),r(t,e,{leading:i,maxWait:e,trailing:a})}},12846:function(t,e,n){var r=n(51088),o=1/0;t.exports=function(t){return t?(t=r(t))===o||t===-o?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}},92282:function(t,e,n){var r=n(12846);t.exports=function(t){var e=r(t),n=e%1;return e==e?n?e-n:e:0}},51088:function(t,e,n){var r=n(51609),o=n(5635),i=n(46065),a=0/0,u=/^[-+]0x[0-9a-f]+$/i,c=/^0b[01]+$/i,l=/^0o[0-7]+$/i,s=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(i(t))return a;if(o(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=o(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=r(t);var n=c.test(t);return n||l.test(t)?s(t.slice(2),n?2:8):u.test(t)?a:+t}},41572:function(t,e,n){var r=n(96016);t.exports=function(t){return null==t?"":r(t)}},18234:function(t,e,n){var r=n(38807),o=n(62714);t.exports=function(t,e){return t&&t.length?o(t,r(e,2)):[]}},4501:function(t,e,n){var r=n(9845)("toUpperCase");t.exports=r},9824:function(t,e,n){"use strict";n.d(e,{Z:function(){return c}});var r=n(32486);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return e.filter((t,e,n)=>!!t&&n.indexOf(t)===e).join(" ")};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let u=(0,r.forwardRef)((t,e)=>{let{color:n="currentColor",size:o=24,strokeWidth:u=2,absoluteStrokeWidth:c,className:l="",children:s,iconNode:f,...p}=t;return(0,r.createElement)("svg",{ref:e,...a,width:o,height:o,stroke:n,strokeWidth:c?24*Number(u)/Number(o):u,className:i("lucide",l),...p},[...f.map(t=>{let[e,n]=t;return(0,r.createElement)(e,n)}),...Array.isArray(s)?s:[s]])}),c=(t,e)=>{let n=(0,r.forwardRef)((n,a)=>{let{className:c,...l}=n;return(0,r.createElement)(u,{ref:a,iconNode:e,className:i("lucide-".concat(o(t)),c),...l})});return n.displayName="".concat(t),n}},47720:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Bookmark",[["path",{d:"m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z",key:"1fy3hk"}]])},5426:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},35575:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Forward",[["polyline",{points:"15 17 20 12 15 7",key:"1w3sku"}],["path",{d:"M4 18v-2a4 4 0 0 1 4-4h12",key:"jmiej9"}]])},94912:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("MessageCircleMore",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}],["path",{d:"M8 12h.01",key:"czm47f"}],["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M16 12h.01",key:"1l6xoz"}]])},18208:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},6769:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("SmilePlus",[["path",{d:"M22 11v1a10 10 0 1 1-9-10",key:"ew0xw9"}],["path",{d:"M8 14s1.5 2 4 2 4-2 4-2",key:"1y1vjs"}],["line",{x1:"9",x2:"9.01",y1:"9",y2:"9",key:"yxxnd0"}],["line",{x1:"15",x2:"15.01",y1:"9",y2:"9",key:"1p4y9e"}],["path",{d:"M16 5h6",key:"1vod17"}],["path",{d:"M19 2v6",key:"4bpg5p"}]])},22397:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(9824).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},39713:function(t,e,n){"use strict";n.d(e,{default:function(){return o.a}});var r=n(74033),o=n.n(r)},74033:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),function(t,e){for(var n in e)Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}(e,{default:function(){return c},getImageProps:function(){return u}});let r=n(60723),o=n(25738),i=n(28863),a=r._(n(44543));function u(t){let{props:e}=(0,o.getImgProps)(t,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[t,n]of Object.entries(e))void 0===n&&delete e[t];return{props:e}}let c=i.Image},93646:function(t,e,n){"use strict";var r=n(97272);function o(){}function i(){}i.resetWarningCache=o,t.exports=function(){function t(t,e,n,o,i,a){if(a!==r){var u=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}function e(){return t}t.isRequired=t;var n={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:i,resetWarningCache:o};return n.PropTypes=n,n}},78794:function(t,e,n){t.exports=n(93646)()},97272:function(t){"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},72988:function(t,e){"use strict";/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),c=Symbol.for("react.context"),l=Symbol.for("react.server_context"),s=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),d=Symbol.for("react.lazy");Symbol.for("react.offscreen"),Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case o:case a:case i:case f:case p:return t;default:switch(t=t&&t.$$typeof){case l:case c:case s:case d:case h:case u:return t;default:return e}}case r:return e}}}(t)===o}},41853:function(t,e,n){"use strict";t.exports=n(72988)},92737:function(t,e,n){"use strict";n.d(e,{ZP:function(){return tS}});var r=n(32486),o=n(78794),i=n.n(o),a=Object.getOwnPropertyNames,u=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty;function l(t,e){return function(n,r,o){return t(n,r,o)&&e(n,r,o)}}function s(t){return function(e,n,r){if(!e||!n||"object"!=typeof e||"object"!=typeof n)return t(e,n,r);var o=r.cache,i=o.get(e),a=o.get(n);if(i&&a)return i===n&&a===e;o.set(e,n),o.set(n,e);var u=t(e,n,r);return o.delete(e),o.delete(n),u}}function f(t){return a(t).concat(u(t))}var p=Object.hasOwn||function(t,e){return c.call(t,e)};function h(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var d=Object.getOwnPropertyDescriptor,y=Object.keys;function v(t,e,n){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(!n.equals(t[r],e[r],r,r,t,e,n))return!1;return!0}function m(t,e){return h(t.getTime(),e.getTime())}function b(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function g(t,e){return t===e}function x(t,e,n){var r,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),u=t.entries(),c=0;(r=u.next())&&!r.done;){for(var l=e.entries(),s=!1,f=0;(o=l.next())&&!o.done;){if(a[f]){f++;continue}var p=r.value,h=o.value;if(n.equals(p[0],h[0],c,f,t,e,n)&&n.equals(p[1],h[1],p[0],h[0],t,e,n)){s=a[f]=!0;break}f++}if(!s)return!1;c++}return!0}function O(t,e,n){var r=y(t),o=r.length;if(y(e).length!==o)return!1;for(;o-- >0;)if(!k(t,e,n,r[o]))return!1;return!0}function w(t,e,n){var r,o,i,a=f(t),u=a.length;if(f(e).length!==u)return!1;for(;u-- >0;)if(!k(t,e,n,r=a[u])||(o=d(t,r),i=d(e,r),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function j(t,e){return h(t.valueOf(),e.valueOf())}function S(t,e){return t.source===e.source&&t.flags===e.flags}function P(t,e,n){var r,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),u=t.values();(r=u.next())&&!r.done;){for(var c=e.values(),l=!1,s=0;(o=c.next())&&!o.done;){if(!a[s]&&n.equals(r.value,o.value,r.value,o.value,t,e,n)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function A(t,e){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(t[n]!==e[n])return!1;return!0}function E(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function k(t,e,n,r){return("_owner"===r||"__o"===r||"__v"===r)&&(!!t.$$typeof||!!e.$$typeof)||p(e,r)&&n.equals(t[r],e[r],r,r,t,e,n)}var M=Array.isArray,_="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,T=Object.assign,C=Object.prototype.toString.call.bind(Object.prototype.toString),I=D();function D(t){void 0===t&&(t={});var e,n,r,o,i,a,u,c,f,p,d,y,k,I=t.circular,D=t.createInternalComparator,N=t.createState,L=t.strict,B=(n=(e=function(t){var e=t.circular,n=t.createCustomConfig,r=t.strict,o={areArraysEqual:r?w:v,areDatesEqual:m,areErrorsEqual:b,areFunctionsEqual:g,areMapsEqual:r?l(x,w):x,areNumbersEqual:h,areObjectsEqual:r?w:O,arePrimitiveWrappersEqual:j,areRegExpsEqual:S,areSetsEqual:r?l(P,w):P,areTypedArraysEqual:r?w:A,areUrlsEqual:E};if(n&&(o=T({},o,n(o))),e){var i=s(o.areArraysEqual),a=s(o.areMapsEqual),u=s(o.areObjectsEqual),c=s(o.areSetsEqual);o=T({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:u,areSetsEqual:c})}return o}(t)).areArraysEqual,r=e.areDatesEqual,o=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,u=e.areNumbersEqual,c=e.areObjectsEqual,f=e.arePrimitiveWrappersEqual,p=e.areRegExpsEqual,d=e.areSetsEqual,y=e.areTypedArraysEqual,k=e.areUrlsEqual,function(t,e,l){if(t===e)return!0;if(null==t||null==e)return!1;var s=typeof t;if(s!==typeof e)return!1;if("object"!==s)return"number"===s?u(t,e,l):"function"===s&&i(t,e,l);var h=t.constructor;if(h!==e.constructor)return!1;if(h===Object)return c(t,e,l);if(M(t))return n(t,e,l);if(null!=_&&_(t))return y(t,e,l);if(h===Date)return r(t,e,l);if(h===RegExp)return p(t,e,l);if(h===Map)return a(t,e,l);if(h===Set)return d(t,e,l);var v=C(t);return"[object Date]"===v?r(t,e,l):"[object RegExp]"===v?p(t,e,l):"[object Map]"===v?a(t,e,l):"[object Set]"===v?d(t,e,l):"[object Object]"===v?"function"!=typeof t.then&&"function"!=typeof e.then&&c(t,e,l):"[object URL]"===v?k(t,e,l):"[object Error]"===v?o(t,e,l):"[object Arguments]"===v?c(t,e,l):("[object Boolean]"===v||"[object Number]"===v||"[object String]"===v)&&f(t,e,l)}),R=D?D(B):function(t,e,n,r,o,i,a){return B(t,e,a)};return function(t){var e=t.circular,n=t.comparator,r=t.createState,o=t.equals,i=t.strict;if(r)return function(t,a){var u=r(),c=u.cache;return n(t,a,{cache:void 0===c?e?new WeakMap:void 0:c,equals:o,meta:u.meta,strict:i})};if(e)return function(t,e){return n(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return n(t,e,a)}}({circular:void 0!==I&&I,comparator:B,createState:N,equals:R,strict:void 0!==L&&L})}function N(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=-1;requestAnimationFrame(function r(o){if(n<0&&(n=o),o-n>e)t(o),n=-1;else{var i;i=r,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function L(t){return(L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function B(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function R(t){return(R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function z(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function U(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?z(Object(n),!0).forEach(function(e){$(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):z(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function $(t,e,n){var r;return(r=function(t,e){if("object"!==R(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==R(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===R(r)?r:String(r))in t)?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}D({strict:!0}),D({circular:!0}),D({circular:!0,strict:!0}),D({createInternalComparator:function(){return h}}),D({strict:!0,createInternalComparator:function(){return h}}),D({circular:!0,createInternalComparator:function(){return h}}),D({circular:!0,createInternalComparator:function(){return h},strict:!0});var F=function(t){return t},Z=function(t,e){return Object.keys(e).reduce(function(n,r){return U(U({},n),{},$({},r,t(r,e[r])))},{})},q=function(t,e,n){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(n)}).join(",")},W=function(t,e,n,r,o,i,a,u){};function Y(t,e){if(t){if("string"==typeof t)return H(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return H(t,e)}}function H(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var X=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},V=function(t,e){return t.map(function(t,n){return t*Math.pow(e,n)}).reduce(function(t,e){return t+e})},G=function(t,e){return function(n){return V(X(t,e),n)}},K=function(){for(var t,e,n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];var i=r[0],a=r[1],u=r[2],c=r[3];if(1===r.length)switch(r[0]){case"linear":i=0,a=0,u=1,c=1;break;case"ease":i=.25,a=.1,u=.25,c=1;break;case"ease-in":i=.42,a=0,u=1,c=1;break;case"ease-out":i=.42,a=0,u=.58,c=1;break;case"ease-in-out":i=0,a=0,u=.58,c=1;break;default:var l=r[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var s,f=function(t){if(Array.isArray(t))return t}(s=l[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{for(i=(n=n.call(t)).next;!(c=(r=i.call(n)).done)&&(u.push(r.value),4!==u.length);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(s,4)||Y(s,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],u=f[2],c=f[3]}else W(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",r)}W([i,u,a,c].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",r);var p=G(i,u),h=G(a,c),d=(t=i,e=u,function(n){var r;return V([].concat(function(t){if(Array.isArray(t))return H(t)}(r=X(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(r)||Y(r)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),n)}),y=function(t){for(var e=t>1?1:t,n=e,r=0;r<8;++r){var o,i=p(n)-e,a=d(n);if(1e-4>Math.abs(i-e)||a<1e-4)break;n=(o=n-i/a)>1?1:o<0?0:o}return h(n)};return y.isStepper=!1,y},J=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,n=void 0===e?100:e,r=t.damping,o=void 0===r?8:r,i=t.dt,a=void 0===i?17:i,u=function(t,e,r){var i=r+(-(t-e)*n-r*o)*a/1e3,u=r*a/1e3+t;return 1e-4>Math.abs(u-e)&&1e-4>Math.abs(i)?[e,0]:[u,i]};return u.isStepper=!0,u.dt=a,u},Q=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var r=e[0];if("string"==typeof r)switch(r){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return K(r);case"spring":return J();default:if("cubic-bezier"===r.split("(")[0])return K(r);W(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof r?r:(W(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function tt(t){return(tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function te(t){return function(t){if(Array.isArray(t))return ta(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||ti(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tn(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function tr(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?tn(Object(n),!0).forEach(function(e){to(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):tn(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function to(t,e,n){var r;return(r=function(t,e){if("object"!==tt(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==tt(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===tt(r)?r:String(r))in t)?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function ti(t,e){if(t){if("string"==typeof t)return ta(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ta(t,e)}}function ta(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var tu=function(t,e,n){return t+(e-t)*n},tc=function(t){return t.from!==t.to},tl=function t(e,n,r){var o=Z(function(t,n){if(tc(n)){var r,o=function(t){if(Array.isArray(t))return t}(r=e(n.from,n.to,n.velocity))||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{for(i=(n=n.call(t)).next;!(c=(r=i.call(n)).done)&&(u.push(r.value),2!==u.length);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(r,2)||ti(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return tr(tr({},n),{},{from:i,velocity:a})}return n},n);return r<1?Z(function(t,e){return tc(e)?tr(tr({},e),{},{velocity:tu(e.velocity,o[t].velocity,r),from:tu(e.from,o[t].from,r)}):e},n):t(e,o,r-1)},ts=function(t,e,n,r,o){var i,a,u=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),c=u.reduce(function(n,r){return tr(tr({},n),{},to({},r,[t[r],e[r]]))},{}),l=u.reduce(function(n,r){return tr(tr({},n),{},to({},r,{from:t[r],velocity:0,to:e[r]}))},{}),s=-1,f=function(){return null};return f=n.isStepper?function(r){i||(i=r);var a=(r-i)/n.dt;l=tl(n,l,a),o(tr(tr(tr({},t),e),Z(function(t,e){return e.from},l))),i=r,Object.values(l).filter(tc).length&&(s=requestAnimationFrame(f))}:function(i){a||(a=i);var u=(i-a)/r,l=Z(function(t,e){return tu.apply(void 0,te(e).concat([n(u)]))},c);if(o(tr(tr(tr({},t),e),l)),u<1)s=requestAnimationFrame(f);else{var p=Z(function(t,e){return tu.apply(void 0,te(e).concat([n(1)]))},c);o(tr(tr(tr({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(s)}}};function tf(t){return(tf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var tp=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function th(t){return function(t){if(Array.isArray(t))return td(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return td(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return td(t,void 0)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function td(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function ty(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function tv(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ty(Object(n),!0).forEach(function(e){tm(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ty(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function tm(t,e,n){return(e=tb(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function tb(t){var e=function(t,e){if("object"!==tf(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==tf(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===tf(e)?e:String(e)}function tg(t,e){return(tg=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tx(t,e){if(e&&("object"===tf(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return tO(t)}function tO(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function tw(t){return(tw=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var tj=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tg(t,e)}(i,t);var e,n,o=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=tw(i);return t=e?Reflect.construct(n,arguments,tw(this).constructor):n.apply(this,arguments),tx(this,t)});function i(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,i);var n,r=(n=o.call(this,t,e)).props,a=r.isActive,u=r.attributeName,c=r.from,l=r.to,s=r.steps,f=r.children,p=r.duration;if(n.handleStyleChange=n.handleStyleChange.bind(tO(n)),n.changeStyle=n.changeStyle.bind(tO(n)),!a||p<=0)return n.state={style:{}},"function"==typeof f&&(n.state={style:l}),tx(n);if(s&&s.length)n.state={style:s[0].style};else if(c){if("function"==typeof f)return n.state={style:c},tx(n);n.state={style:u?tm({},u,c):c}}else n.state={style:{}};return n}return n=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,n=t.canBegin;this.mounted=!0,e&&n&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,n=e.isActive,r=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,u=e.from,c=this.state.style;if(r){if(!n){var l={style:o?tm({},o,a):a};this.state&&c&&(o&&c[o]!==a||!o&&c!==a)&&this.setState(l);return}if(!I(t.to,a)||!t.canBegin||!t.isActive){var s=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=s||i?u:t.to;if(this.state&&c){var p={style:o?tm({},o,f):f};(o&&c[o]!==f||!o&&c!==f)&&this.setState(p)}this.runAnimation(tv(tv({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,n=t.from,r=t.to,o=t.duration,i=t.easing,a=t.begin,u=t.onAnimationEnd,c=t.onAnimationStart,l=ts(n,r,Q(i),o,this.changeStyle);this.manager.start([c,a,function(){e.stopJSAnimation=l()},o,u])}},{key:"runStepAnimation",value:function(t){var e=this,n=t.steps,r=t.begin,o=t.onAnimationStart,i=n[0],a=i.style,u=i.duration;return this.manager.start([o].concat(th(n.reduce(function(t,r,o){if(0===o)return t;var i=r.duration,a=r.easing,u=void 0===a?"ease":a,c=r.style,l=r.properties,s=r.onAnimationEnd,f=o>0?n[o-1]:r,p=l||Object.keys(c);if("function"==typeof u||"spring"===u)return[].concat(th(t),[e.runJSAnimation.bind(e,{from:f.style,to:c,duration:i,easing:u}),i]);var h=q(p,i,u),d=tv(tv(tv({},f.style),c),{},{transition:h});return[].concat(th(t),[d,i,s]).filter(F)},[a,Math.max(void 0===u?0:u,r)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){if(!this.manager){var e,n,r;this.manager=(e=function(){return null},n=!1,r=function t(r){if(!n){if(Array.isArray(r)){if(!r.length)return;var o=function(t){if(Array.isArray(t))return t}(r)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(r)||function(t,e){if(t){if("string"==typeof t)return B(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return B(t,void 0)}}(r)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);if("number"==typeof i){N(t.bind(null,a),i);return}t(i),N(t.bind(null,a));return}"object"===L(r)&&e(r),"function"==typeof r&&r()}},{stop:function(){n=!0},start:function(t){n=!1,r(t)},subscribe:function(t){return e=t,function(){e=function(){return null}}}})}var o=t.begin,i=t.duration,a=t.attributeName,u=t.to,c=t.easing,l=t.onAnimationStart,s=t.onAnimationEnd,f=t.steps,p=t.children,h=this.manager;if(this.unSubscribe=h.subscribe(this.handleStyleChange),"function"==typeof c||"function"==typeof p||"spring"===c){this.runJSAnimation(t);return}if(f.length>1){this.runStepAnimation(t);return}var d=a?tm({},a,u):u,y=q(Object.keys(d),i,c);h.start([l,o,tv(tv({},d),{},{transition:y}),i,s])}},{key:"render",value:function(){var t=this.props,e=t.children,n=(t.begin,t.duration),o=(t.attributeName,t.easing,t.isActive),i=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,tp)),a=r.Children.count(e),u=this.state.style;if("function"==typeof e)return e(u);if(!o||0===a||n<=0)return e;var c=function(t){var e=t.props,n=e.style,o=e.className;return(0,r.cloneElement)(t,tv(tv({},i),{},{style:tv(tv({},void 0===n?{}:n),u),className:o}))};return 1===a?c(r.Children.only(e)):r.createElement("div",null,r.Children.map(e,function(t){return c(t)}))}}],function(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,tb(r.key),r)}}(i.prototype,n),Object.defineProperty(i,"prototype",{writable:!1}),i}(r.PureComponent);tj.displayName="Animate",tj.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},tj.propTypes={from:i().oneOfType([i().object,i().string]),to:i().oneOfType([i().object,i().string]),attributeName:i().string,duration:i().number,begin:i().number,easing:i().oneOfType([i().string,i().func]),steps:i().arrayOf(i().shape({duration:i().number.isRequired,style:i().object.isRequired,easing:i().oneOfType([i().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),i().func]),properties:i().arrayOf("string"),onAnimationEnd:i().func})),children:i().oneOfType([i().node,i().func]),isActive:i().bool,canBegin:i().bool,onAnimationEnd:i().func,shouldReAnimate:i().bool,onAnimationStart:i().func,onAnimationReStart:i().func};var tS=tj},91697:function(t,e,n){"use strict";n.d(e,{u:function(){return rv}});var r,o,i,a,u,c,l,s,f,p,h,d,y,v,m,b,g,x=n(32486),O=n(86197),w=n.n(O),j=n(14119),S=n.n(j),P=n(84452),A=n.n(P),E=n(82404),k=n.n(E),M=n(9313),_=n.n(M),T=n(3787),C=n.n(T),I=n(89824),D=n(12445),N=n(81581),L=n(14166),B=n(46729);function R(t){return(R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function z(){return(z=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function U(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function $(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function F(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?$(Object(n),!0).forEach(function(e){var r,o;r=e,o=n[e],(r=function(t){var e=function(t,e){if("object"!=R(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=R(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==R(e)?e:e+""}(r))in t?Object.defineProperty(t,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[r]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):$(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function Z(t){return Array.isArray(t)&&(0,B.P2)(t[0])&&(0,B.P2)(t[1])?t.join(" ~ "):t}var q=function(t){var e=t.separator,n=void 0===e?" : ":e,r=t.contentStyle,o=t.itemStyle,i=void 0===o?{}:o,a=t.labelStyle,u=t.payload,c=t.formatter,l=t.itemSorter,s=t.wrapperClassName,f=t.labelClassName,p=t.label,h=t.labelFormatter,d=t.accessibilityLayer,y=F({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===r?{}:r),v=F({margin:0},void 0===a?{}:a),m=!w()(p),b=m?p:"",g=(0,I.Z)("recharts-default-tooltip",s),O=(0,I.Z)("recharts-tooltip-label",f);return m&&h&&null!=u&&(b=h(p,u)),x.createElement("div",z({className:g,style:y},void 0!==d&&d?{role:"status","aria-live":"assertive"}:{}),x.createElement("p",{className:O,style:v},x.isValidElement(b)?b:"".concat(b)),function(){if(u&&u.length){var t=(l?_()(u,l):u).map(function(t,e){if("none"===t.type)return null;var r=F({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},i),o=t.formatter||c||Z,a=t.value,l=t.name,s=a,f=l;if(o&&null!=s&&null!=f){var p=o(a,l,t,e,u);if(Array.isArray(p)){var h=function(t){if(Array.isArray(t))return t}(p)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{for(i=(n=n.call(t)).next;!(c=(r=i.call(n)).done)&&(u.push(r.value),2!==u.length);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(p,2)||function(t,e){if(t){if("string"==typeof t)return U(t,2);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return U(t,2)}}(p,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();s=h[0],f=h[1]}else s=p}return x.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:r},(0,B.P2)(f)?x.createElement("span",{className:"recharts-tooltip-item-name"},f):null,(0,B.P2)(f)?x.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,x.createElement("span",{className:"recharts-tooltip-item-value"},s),x.createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return x.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function W(t){return(W="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Y(t,e,n){var r;return(r=function(t,e){if("object"!=W(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=W(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==W(r)?r:r+"")in t)?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var H="recharts-tooltip-wrapper",X={visibility:"hidden"};function V(t){var e=t.allowEscapeViewBox,n=t.coordinate,r=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,u=t.tooltipDimension,c=t.viewBox,l=t.viewBoxDimension;if(i&&(0,B.hj)(i[r]))return i[r];var s=n[r]-u-o,f=n[r]+o;return e[r]?a[r]?s:f:a[r]?s<c[r]?Math.max(f,c[r]):Math.max(s,c[r]):f+u>c[r]+l?Math.max(s,c[r]):Math.max(f,c[r])}function G(t){return(G="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function K(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function J(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?K(Object(n),!0).forEach(function(e){tn(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):K(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function Q(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Q=function(){return!!t})()}function tt(t){return(tt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function te(t,e){return(te=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tn(t,e,n){return(e=tr(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function tr(t){var e=function(t,e){if("object"!=G(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=G(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==G(e)?e:e+""}var to=function(t){var e;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n);for(var t,e,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=tt(e),tn(t=function(t,e){if(e&&("object"===G(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,Q()?Reflect.construct(e,r||[],tt(this).constructor):e.apply(this,r)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),tn(t,"handleKeyDown",function(e){if("Escape"===e.key){var n,r,o,i;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(n=null===(r=t.props.coordinate)||void 0===r?void 0:r.x)&&void 0!==n?n:0,y:null!==(o=null===(i=t.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})}}),t}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&te(t,e)}(n,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(t=this.props.coordinate)||void 0===t?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null===(e=this.props.coordinate)||void 0===e?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,n,r,o,i,a,u,c,l,s,f,p,h,d,y,v,m,b,g=this,O=this.props,w=O.active,j=O.allowEscapeViewBox,S=O.animationDuration,P=O.animationEasing,A=O.children,E=O.coordinate,k=O.hasPayload,M=O.isAnimationActive,_=O.offset,T=O.position,C=O.reverseDirection,D=O.useTranslate3d,N=O.viewBox,L=O.wrapperStyle,R=(f=(t={allowEscapeViewBox:j,coordinate:E,offsetTopLeft:_,position:T,reverseDirection:C,tooltipBox:this.state.lastBoundingBox,useTranslate3d:D,viewBox:N}).allowEscapeViewBox,p=t.coordinate,h=t.offsetTopLeft,d=t.position,y=t.reverseDirection,v=t.tooltipBox,m=t.useTranslate3d,b=t.viewBox,v.height>0&&v.width>0&&p?(n=(e={translateX:l=V({allowEscapeViewBox:f,coordinate:p,key:"x",offsetTopLeft:h,position:d,reverseDirection:y,tooltipDimension:v.width,viewBox:b,viewBoxDimension:b.width}),translateY:s=V({allowEscapeViewBox:f,coordinate:p,key:"y",offsetTopLeft:h,position:d,reverseDirection:y,tooltipDimension:v.height,viewBox:b,viewBoxDimension:b.height}),useTranslate3d:m}).translateX,r=e.translateY,c={transform:e.useTranslate3d?"translate3d(".concat(n,"px, ").concat(r,"px, 0)"):"translate(".concat(n,"px, ").concat(r,"px)")}):c=X,{cssProperties:c,cssClasses:(i=(o={translateX:l,translateY:s,coordinate:p}).coordinate,a=o.translateX,u=o.translateY,(0,I.Z)(H,Y(Y(Y(Y({},"".concat(H,"-right"),(0,B.hj)(a)&&i&&(0,B.hj)(i.x)&&a>=i.x),"".concat(H,"-left"),(0,B.hj)(a)&&i&&(0,B.hj)(i.x)&&a<i.x),"".concat(H,"-bottom"),(0,B.hj)(u)&&i&&(0,B.hj)(i.y)&&u>=i.y),"".concat(H,"-top"),(0,B.hj)(u)&&i&&(0,B.hj)(i.y)&&u<i.y)))}),z=R.cssClasses,U=R.cssProperties,$=J(J({transition:M&&w?"transform ".concat(S,"ms ").concat(P):void 0},U),{},{pointerEvents:"none",visibility:!this.state.dismissed&&w&&k?"visible":"hidden",position:"absolute",top:0,left:0},L);return x.createElement("div",{tabIndex:-1,className:z,style:$,ref:function(t){g.wrapperNode=t}},A)}}],function(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,tr(r.key),r)}}(n.prototype,e),Object.defineProperty(n,"prototype",{writable:!1}),n}(x.PureComponent),ti=n(43160),ta=n(78262);function tu(t){return(tu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tc(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function tl(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?tc(Object(n),!0).forEach(function(e){th(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):tc(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function ts(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ts=function(){return!!t})()}function tf(t){return(tf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tp(t,e){return(tp=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function th(t,e,n){return(e=td(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function td(t){var e=function(t,e){if("object"!=tu(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=tu(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tu(e)?e:e+""}function ty(t){return t.dataKey}var tv=function(t){var e;function n(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),t=n,e=arguments,t=tf(t),function(t,e){if(e&&("object"===tu(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,ts()?Reflect.construct(t,e||[],tf(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tp(t,e)}(n,t),e=[{key:"render",value:function(){var t,e=this,n=this.props,r=n.active,o=n.allowEscapeViewBox,i=n.animationDuration,a=n.animationEasing,u=n.content,c=n.coordinate,l=n.filterNull,s=n.isAnimationActive,f=n.offset,p=n.payload,h=n.payloadUniqBy,d=n.position,y=n.reverseDirection,v=n.useTranslate3d,m=n.viewBox,b=n.wrapperStyle,g=null!=p?p:[];l&&g.length&&(g=(0,ta.z)(p.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),h,ty));var O=g.length>0;return x.createElement(to,{allowEscapeViewBox:o,animationDuration:i,animationEasing:a,isAnimationActive:s,active:r,coordinate:c,hasPayload:O,offset:f,position:d,reverseDirection:y,useTranslate3d:v,viewBox:m,wrapperStyle:b},(t=tl(tl({},this.props),{},{payload:g}),x.isValidElement(u)?x.cloneElement(u,t):"function"==typeof u?x.createElement(u,t):x.createElement(q,t)))}}],function(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,td(r.key),r)}}(n.prototype,e),Object.defineProperty(n,"prototype",{writable:!1}),n}(x.PureComponent);th(tv,"displayName","Tooltip"),th(tv,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!ti.x.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var tm=n(26109),tb=n(95273),tg=n(21105);function tx(){return(tx=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var tO=function(t){var e=t.cx,n=t.cy,r=t.r,o=t.className,i=(0,I.Z)("recharts-dot",o);return e===+e&&n===+n&&r===+r?x.createElement("circle",tx({},(0,tg.L6)(t,!1),(0,tb.Ym)(t),{className:i,cx:e,cy:n,r:r})):null},tw=n(22462),tj=n(69802),tS=n(40082),tP=n(74499);function tA(t){return(tA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tE(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function tk(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?tE(Object(n),!0).forEach(function(e){tM(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):tE(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function tM(t,e,n){var r;return(r=function(t,e){if("object"!=tA(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=tA(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==tA(r)?r:r+"")in t)?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var t_=["Webkit","Moz","O","ms"],tT=function(t,e){if(!t)return null;var n=t.replace(/(\w)/,function(t){return t.toUpperCase()}),r=t_.reduce(function(t,r){return tk(tk({},t),{},tM({},r+n,e))},{});return r[t]=e,r};function tC(t){return(tC="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tI(){return(tI=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function tD(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function tN(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?tD(Object(n),!0).forEach(function(e){tU(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):tD(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function tL(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,t$(r.key),r)}}function tB(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tB=function(){return!!t})()}function tR(t){return(tR=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tz(t,e){return(tz=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tU(t,e,n){return(e=t$(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function t$(t){var e=function(t,e){if("object"!=tC(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=tC(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tC(e)?e:e+""}var tF=function(t){var e=t.data,n=t.startIndex,r=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var u=e.length,c=(0,tj.x)().domain(A()(0,u)).range([o,o+i-a]),l=c.domain().map(function(t){return c(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:c(n),endX:c(r),scale:c,scaleValues:l}},tZ=function(t){return t.changedTouches&&!!t.changedTouches.length},tq=function(t){var e,n;function r(t){var e,n,o;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),n=r,o=[t],n=tR(n),tU(e=function(t,e){if(e&&("object"===tC(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,tB()?Reflect.construct(n,o||[],tR(this).constructor):n.apply(this,o)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),tU(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),tU(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,n=t.endIndex,r=t.onDragEnd,o=t.startIndex;null==r||r({endIndex:n,startIndex:o})}),e.detachDragEndListener()}),tU(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),tU(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),tU(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),tU(e,"handleSlideDragStart",function(t){var n=tZ(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:n.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tz(t,e)}(r,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,n=t.endX,o=this.state.scaleValues,i=this.props,a=i.gap,u=i.data.length-1,c=r.getIndexInRange(o,Math.min(e,n)),l=r.getIndexInRange(o,Math.max(e,n));return{startIndex:c-c%a,endIndex:l===u?u:l-l%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,n=e.data,r=e.tickFormatter,o=e.dataKey,i=(0,tP.F$)(n[t],o,t);return S()(r)?r(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,n=e.slideMoveStartX,r=e.startX,o=e.endX,i=this.props,a=i.x,u=i.width,c=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=t.pageX-n;p>0?p=Math.min(p,a+u-c-o,a+u-c-r):p<0&&(p=Math.max(p,a-r,a-o));var h=this.getIndex({startX:r+p,endX:o+p});(h.startIndex!==l||h.endIndex!==s)&&f&&f(h),this.setState({startX:r+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var n=tZ(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:n.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,n=e.brushMoveStartX,r=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[r],u=this.props,c=u.x,l=u.width,s=u.travellerWidth,f=u.onChange,p=u.gap,h=u.data,d={startX:this.state.startX,endX:this.state.endX},y=t.pageX-n;y>0?y=Math.min(y,c+l-s-a):y<0&&(y=Math.max(y,c-a)),d[r]=a+y;var v=this.getIndex(d),m=v.startIndex,b=v.endIndex,g=function(){var t=h.length-1;return"startX"===r&&(o>i?m%p==0:b%p==0)||o<i&&b===t||"endX"===r&&(o>i?b%p==0:m%p==0)||o>i&&b===t};this.setState(tU(tU({},r,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var n=this,r=this.state,o=r.scaleValues,i=r.startX,a=r.endX,u=this.state[e],c=o.indexOf(u);if(-1!==c){var l=c+t;if(-1!==l&&!(l>=o.length)){var s=o[l];"startX"===e&&s>=a||"endX"===e&&s<=i||this.setState(tU({},e,s),function(){n.props.onChange(n.getIndex({startX:n.state.startX,endX:n.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,n=t.y,r=t.width,o=t.height,i=t.fill,a=t.stroke;return x.createElement("rect",{stroke:a,fill:i,x:e,y:n,width:r,height:o})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,n=t.y,r=t.width,o=t.height,i=t.data,a=t.children,u=t.padding,c=x.Children.only(a);return c?x.cloneElement(c,{x:e,y:n,width:r,height:o,margin:u,compact:!0,data:i}):null}},{key:"renderTravellerLayer",value:function(t,e){var n,o,i=this,a=this.props,u=a.y,c=a.travellerWidth,l=a.height,s=a.traveller,f=a.ariaLabel,p=a.data,h=a.startIndex,d=a.endIndex,y=Math.max(t,this.props.x),v=tN(tN({},(0,tg.L6)(this.props,!1)),{},{x:y,y:u,width:c,height:l}),m=f||"Min value: ".concat(null===(n=p[h])||void 0===n?void 0:n.name,", Max value: ").concat(null===(o=p[d])||void 0===o?void 0:o.name);return x.createElement(L.m,{tabIndex:0,role:"slider","aria-label":m,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),i.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){i.setState({isTravellerFocused:!0})},onBlur:function(){i.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},r.renderTraveller(s,v))}},{key:"renderSlide",value:function(t,e){var n=this.props,r=n.y,o=n.height,i=n.stroke,a=n.travellerWidth;return x.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:i,fillOpacity:.2,x:Math.min(t,e)+a,y:r,width:Math.max(Math.abs(e-t)-a,0),height:o})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,n=t.endIndex,r=t.y,o=t.height,i=t.travellerWidth,a=t.stroke,u=this.state,c=u.startX,l=u.endX,s={pointerEvents:"none",fill:a};return x.createElement(L.m,{className:"recharts-brush-texts"},x.createElement(tS.x,tI({textAnchor:"end",verticalAnchor:"middle",x:Math.min(c,l)-5,y:r+o/2},s),this.getTextOfTick(e)),x.createElement(tS.x,tI({textAnchor:"start",verticalAnchor:"middle",x:Math.max(c,l)+i+5,y:r+o/2},s),this.getTextOfTick(n)))}},{key:"render",value:function(){var t=this.props,e=t.data,n=t.className,r=t.children,o=t.x,i=t.y,a=t.width,u=t.height,c=t.alwaysShowText,l=this.state,s=l.startX,f=l.endX,p=l.isTextActive,h=l.isSlideMoving,d=l.isTravellerMoving,y=l.isTravellerFocused;if(!e||!e.length||!(0,B.hj)(o)||!(0,B.hj)(i)||!(0,B.hj)(a)||!(0,B.hj)(u)||a<=0||u<=0)return null;var v=(0,I.Z)("recharts-brush",n),m=1===x.Children.count(r),b=tT("userSelect","none");return x.createElement(L.m,{className:v,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:b},this.renderBackground(),m&&this.renderPanorama(),this.renderSlide(s,f),this.renderTravellerLayer(s,"startX"),this.renderTravellerLayer(f,"endX"),(p||h||d||y||c)&&this.renderText())}}],n=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,n=t.y,r=t.width,o=t.height,i=t.stroke,a=Math.floor(n+o/2)-1;return x.createElement(x.Fragment,null,x.createElement("rect",{x:e,y:n,width:r,height:o,fill:i,stroke:"none"}),x.createElement("line",{x1:e+1,y1:a,x2:e+r-1,y2:a,fill:"none",stroke:"#fff"}),x.createElement("line",{x1:e+1,y1:a+2,x2:e+r-1,y2:a+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){return x.isValidElement(t)?x.cloneElement(t,e):S()(t)?t(e):r.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var n=t.data,r=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,u=t.startIndex,c=t.endIndex;if(n!==e.prevData||a!==e.prevUpdateId)return tN({prevData:n,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:r},n&&n.length?tF({data:n,width:r,x:o,travellerWidth:i,startIndex:u,endIndex:c}):{scale:null,scaleValues:null});if(e.scale&&(r!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+r-i]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:n,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:r,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var n=t.length,r=0,o=n-1;o-r>1;){var i=Math.floor((r+o)/2);t[i]>e?o=i:r=i}return e>=t[o]?o:r}}],e&&tL(r.prototype,e),n&&tL(r,n),Object.defineProperty(r,"prototype",{writable:!1}),r}(x.PureComponent);tU(tq,"displayName","Brush"),tU(tq,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var tW=n(91568),tY=n(89099),tH=n(46750),tX=function(t,e){var n=t.alwaysShow,r=t.ifOverflow;return n&&(r="extendDomain"),r===e},tV=n(16686),tG=n.n(tV),tK=n(86925),tJ=n.n(tK);function tQ(t){return(tQ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t0(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,t3(r.key),r)}}function t1(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function t2(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?t1(Object(n),!0).forEach(function(e){t6(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):t1(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function t6(t,e,n){return(e=t3(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function t3(t){var e=function(t,e){if("object"!=tQ(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=tQ(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tQ(e)?e:e+""}var t5=function(t,e){var n=t.x,r=t.y,o=e.x,i=e.y;return{x:Math.min(n,o),y:Math.min(r,i),width:Math.abs(o-n),height:Math.abs(i-r)}},t4=function(){var t,e;function n(t){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.bandAware,r=e.position;if(void 0!==t){if(r)switch(r){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(n){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),n=e[0],r=e[e.length-1];return n<=r?t>=n&&t<=r:t>=r&&t<=n}}],e=[{key:"create",value:function(t){return new n(t)}}],t&&t0(n.prototype,t),e&&t0(n,e),Object.defineProperty(n,"prototype",{writable:!1}),n}();t6(t4,"EPS",1e-4);var t8=function(t){var e=Object.keys(t).reduce(function(e,n){return t2(t2({},e),{},t6({},n,t4.create(t[n])))},{});return t2(t2({},e),{},{apply:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.bandAware,o=n.position;return tG()(t,function(t,n){return e[n].apply(t,{bandAware:r,position:o})})},isInRange:function(t){return tJ()(t,function(t,n){return e[n].isInRange(t)})}})},t7=n(25197);function t9(){return(t9=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function et(t){return(et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ee(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function en(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ee(Object(n),!0).forEach(function(e){ea(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ee(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function er(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(er=function(){return!!t})()}function eo(t){return(eo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ei(t,e){return(ei=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ea(t,e,n){return(e=eu(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function eu(t){var e=function(t,e){if("object"!=et(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=et(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==et(e)?e:e+""}var ec=function(t){var e=t.x,n=t.y,r=t.xAxis,o=t.yAxis,i=t8({x:r.scale,y:o.scale}),a=i.apply({x:e,y:n},{bandAware:!0});return tX(t,"discard")&&!i.isInRange(a)?null:a},el=function(t){var e;function n(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),t=n,e=arguments,t=eo(t),function(t,e){if(e&&("object"===et(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,er()?Reflect.construct(t,e||[],eo(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ei(t,e)}(n,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,r=t.y,o=t.r,i=t.alwaysShow,a=t.clipPathId,u=(0,B.P2)(e),c=(0,B.P2)(r);if((0,t7.Z)(void 0===i,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!c)return null;var l=ec(this.props);if(!l)return null;var s=l.x,f=l.y,p=this.props,h=p.shape,d=p.className,y=en(en({clipPath:tX(this.props,"hidden")?"url(#".concat(a,")"):void 0},(0,tg.L6)(this.props,!0)),{},{cx:s,cy:f});return x.createElement(L.m,{className:(0,I.Z)("recharts-reference-dot",d)},n.renderDot(h,y),tH._.renderCallByParent(this.props,{x:s-o,y:f-o,width:2*o,height:2*o}))}}],function(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,eu(r.key),r)}}(n.prototype,e),Object.defineProperty(n,"prototype",{writable:!1}),n}(x.Component);ea(el,"displayName","ReferenceDot"),ea(el,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),ea(el,"renderDot",function(t,e){return x.isValidElement(t)?x.cloneElement(t,e):S()(t)?t(e):x.createElement(tO,t9({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var es=n(82391),ef=n.n(es);n(82009);var ep=n(63212),eh=n.n(ep)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),ed=(0,x.createContext)(void 0),ey=(0,x.createContext)(void 0),ev=(0,x.createContext)(void 0),em=(0,x.createContext)({}),eb=(0,x.createContext)(void 0),eg=(0,x.createContext)(0),ex=(0,x.createContext)(0),eO=function(t){var e=t.state,n=e.xAxisMap,r=e.yAxisMap,o=e.offset,i=t.clipPathId,a=t.children,u=t.width,c=t.height,l=eh(o);return x.createElement(ed.Provider,{value:n},x.createElement(ey.Provider,{value:r},x.createElement(em.Provider,{value:o},x.createElement(ev.Provider,{value:l},x.createElement(eb.Provider,{value:i},x.createElement(eg.Provider,{value:c},x.createElement(ex.Provider,{value:u},a)))))))},ew=function(t){var e=(0,x.useContext)(ed);null!=e||(0,D.Z)(!1);var n=e[t];return null!=n||(0,D.Z)(!1),n},ej=function(t){var e=(0,x.useContext)(ey);null!=e||(0,D.Z)(!1);var n=e[t];return null!=n||(0,D.Z)(!1),n};function eS(t){return(eS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eP(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eP=function(){return!!t})()}function eA(t){return(eA=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function eE(t,e){return(eE=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ek(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function eM(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ek(Object(n),!0).forEach(function(e){e_(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ek(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function e_(t,e,n){return(e=eT(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function eT(t){var e=function(t,e){if("object"!=eS(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=eS(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eS(e)?e:e+""}function eC(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function eI(){return(eI=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var eD=function(t,e,n,r,o,i,a,u,c){var l=o.x,s=o.y,f=o.width,p=o.height;if(n){var h=c.y,d=t.y.apply(h,{position:i});if(tX(c,"discard")&&!t.y.isInRange(d))return null;var y=[{x:l+f,y:d},{x:l,y:d}];return"left"===u?y.reverse():y}if(e){var v=c.x,m=t.x.apply(v,{position:i});if(tX(c,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(r){var g=c.segment.map(function(e){return t.apply(e,{position:i})});return tX(c,"discard")&&ef()(g,function(e){return!t.isInRange(e)})?null:g}return null};function eN(t){var e,n,r,o=t.x,i=t.y,a=t.segment,u=t.xAxisId,c=t.yAxisId,l=t.shape,s=t.className,f=t.alwaysShow,p=(0,x.useContext)(eb),h=ew(u),d=ej(c),y=(0,x.useContext)(ev);if(!p||!y)return null;(0,t7.Z)(void 0===f,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var v=eD(t8({x:h.scale,y:d.scale}),(0,B.P2)(o),(0,B.P2)(i),a&&2===a.length,y,t.position,h.orientation,d.orientation,t);if(!v)return null;var m=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{for(i=(n=n.call(t)).next;!(c=(r=i.call(n)).done)&&(u.push(r.value),2!==u.length);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(v,2)||function(t,e){if(t){if("string"==typeof t)return eC(t,2);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return eC(t,2)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),b=m[0],g=b.x,O=b.y,w=m[1],j=w.x,P=w.y,A=eM(eM({clipPath:tX(t,"hidden")?"url(#".concat(p,")"):void 0},(0,tg.L6)(t,!0)),{},{x1:g,y1:O,x2:j,y2:P});return x.createElement(L.m,{className:(0,I.Z)("recharts-reference-line",s)},(e=l,n=A,x.isValidElement(e)?x.cloneElement(e,n):S()(e)?e(n):x.createElement("line",eI({},n,{className:"recharts-reference-line-line"}))),tH._.renderCallByParent(t,t5({x:(r={x1:g,y1:O,x2:j,y2:P}).x1,y:r.y1},{x:r.x2,y:r.y2})))}var eL=function(t){var e;function n(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),t=n,e=arguments,t=eA(t),function(t,e){if(e&&("object"===eS(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,eP()?Reflect.construct(t,e||[],eA(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&eE(t,e)}(n,t),e=[{key:"render",value:function(){return x.createElement(eN,this.props)}}],function(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,eT(r.key),r)}}(n.prototype,e),Object.defineProperty(n,"prototype",{writable:!1}),n}(x.Component);function eB(){return(eB=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function eR(t){return(eR="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ez(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function eU(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ez(Object(n),!0).forEach(function(e){eq(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ez(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function e$(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(e$=function(){return!!t})()}function eF(t){return(eF=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function eZ(t,e){return(eZ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function eq(t,e,n){return(e=eW(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function eW(t){var e=function(t,e){if("object"!=eR(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=eR(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eR(e)?e:e+""}e_(eL,"displayName","ReferenceLine"),e_(eL,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});var eY=function(t,e,n,r,o){var i=o.x1,a=o.x2,u=o.y1,c=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=t8({x:l.scale,y:s.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:n?f.y.apply(u,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:r?f.y.apply(c,{position:"end"}):f.y.rangeMax};return!tX(o,"discard")||f.isInRange(p)&&f.isInRange(h)?t5(p,h):null},eH=function(t){var e;function n(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),t=n,e=arguments,t=eF(t),function(t,e){if(e&&("object"===eR(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,e$()?Reflect.construct(t,e||[],eF(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&eZ(t,e)}(n,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,r=t.x2,o=t.y1,i=t.y2,a=t.className,u=t.alwaysShow,c=t.clipPathId;(0,t7.Z)(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=(0,B.P2)(e),s=(0,B.P2)(r),f=(0,B.P2)(o),p=(0,B.P2)(i),h=this.props.shape;if(!l&&!s&&!f&&!p&&!h)return null;var d=eY(l,s,f,p,this.props);if(!d&&!h)return null;var y=tX(this.props,"hidden")?"url(#".concat(c,")"):void 0;return x.createElement(L.m,{className:(0,I.Z)("recharts-reference-area",a)},n.renderRect(h,eU(eU({clipPath:y},(0,tg.L6)(this.props,!0)),d)),tH._.renderCallByParent(this.props,d))}}],function(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,eW(r.key),r)}}(n.prototype,e),Object.defineProperty(n,"prototype",{writable:!1}),n}(x.Component);function eX(t){return function(t){if(Array.isArray(t))return eV(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return eV(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return eV(t,void 0)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function eV(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}eq(eH,"displayName","ReferenceArea"),eq(eH,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),eq(eH,"renderRect",function(t,e){return x.isValidElement(t)?x.cloneElement(t,e):S()(t)?t(e):x.createElement(tw.A,eB({},e,{className:"recharts-reference-area-rect"}))});var eG=function(t,e,n,r,o){var i=(0,tg.NN)(t,eL),a=(0,tg.NN)(t,el),u=[].concat(eX(i),eX(a)),c=(0,tg.NN)(t,eH),l="".concat(r,"Id"),s=r[0],f=e;if(u.length&&(f=u.reduce(function(t,e){if(e.props[l]===n&&tX(e.props,"extendDomain")&&(0,B.hj)(e.props[s])){var r=e.props[s];return[Math.min(t[0],r),Math.max(t[1],r)]}return t},f)),c.length){var p="".concat(s,"1"),h="".concat(s,"2");f=c.reduce(function(t,e){if(e.props[l]===n&&tX(e.props,"extendDomain")&&(0,B.hj)(e.props[p])&&(0,B.hj)(e.props[h])){var r=e.props[p],o=e.props[h];return[Math.min(t[0],r,o),Math.max(t[1],r,o)]}return t},f)}return o&&o.length&&(f=o.reduce(function(t,e){return(0,B.hj)(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},eK=n(73845),eJ=n(38269),eQ=n(70445),e0=new(n.n(eQ)()),e1="recharts.syncMouseEvents";function e2(t){return(e2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function e6(t,e,n){return(e=e3(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function e3(t){var e=function(t,e){if("object"!=e2(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=e2(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==e2(e)?e:e+""}var e5=(r=function t(){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,t),e6(this,"activeIndex",0),e6(this,"coordinateList",[]),e6(this,"layout","horizontal")},o=[{key:"setDetails",value:function(t){var e,n=t.coordinateList,r=void 0===n?null:n,o=t.container,i=void 0===o?null:o,a=t.layout,u=void 0===a?null:a,c=t.offset,l=void 0===c?null:c,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!==(e=null!=r?r:this.coordinateList)&&void 0!==e?e:[],this.container=null!=i?i:this.container,this.layout=null!=u?u:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,n=this.container.getBoundingClientRect(),r=n.x,o=n.y,i=n.height,a=this.coordinateList[this.activeIndex].coordinate,u=(null===(t=window)||void 0===t?void 0:t.scrollX)||0,c=(null===(e=window)||void 0===e?void 0:e.scrollY)||0,l=o+this.offset.top+i/2+c;this.mouseHandlerCallback({pageX:r+a+u,pageY:l})}}}],function(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,e3(r.key),r)}}(r.prototype,o),Object.defineProperty(r,"prototype",{writable:!1}),r),e4=n(27232),e8=n(55973);function e7(t){return(e7="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var e9=["x","y","top","left","width","height","className"];function nt(){return(nt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function ne(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}var nn=function(t){var e=t.x,n=void 0===e?0:e,r=t.y,o=void 0===r?0:r,i=t.top,a=void 0===i?0:i,u=t.left,c=void 0===u?0:u,l=t.width,s=void 0===l?0:l,f=t.height,p=void 0===f?0:f,h=t.className,d=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ne(Object(n),!0).forEach(function(e){var r,o;r=e,o=n[e],(r=function(t){var e=function(t,e){if("object"!=e7(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=e7(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==e7(e)?e:e+""}(r))in t?Object.defineProperty(t,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[r]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ne(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}({x:n,y:o,top:a,left:c,width:s,height:p},function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,e9));return(0,B.hj)(n)&&(0,B.hj)(o)&&(0,B.hj)(s)&&(0,B.hj)(p)&&(0,B.hj)(a)&&(0,B.hj)(c)?x.createElement("path",nt({},(0,tg.L6)(d,!0),{className:(0,I.Z)("recharts-cross",h),d:"M".concat(n,",").concat(a,"v").concat(p,"M").concat(c,",").concat(o,"h").concat(s)})):null};function nr(t){var e=t.cx,n=t.cy,r=t.radius,o=t.startAngle,i=t.endAngle;return{points:[(0,eK.op)(e,n,r,o),(0,eK.op)(e,n,r,i)],cx:e,cy:n,radius:r,startAngle:o,endAngle:i}}var no=n(75300);function ni(t){return(ni="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function na(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function nu(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?na(Object(n),!0).forEach(function(e){var r,o;r=e,o=n[e],(r=function(t){var e=function(t,e){if("object"!=ni(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=ni(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ni(e)?e:e+""}(r))in t?Object.defineProperty(t,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[r]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):na(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function nc(t){var e,n,r,o,i=t.element,a=t.tooltipEventType,u=t.isActive,c=t.activeCoordinate,l=t.activePayload,s=t.offset,f=t.activeTooltipIndex,p=t.tooltipAxisBandSize,h=t.layout,d=t.chartName,y=null!==(n=i.props.cursor)&&void 0!==n?n:null===(r=i.type.defaultProps)||void 0===r?void 0:r.cursor;if(!i||!y||!u||!c||"ScatterChart"!==d&&"axis"!==a)return null;var v=e8.H;if("ScatterChart"===d)o=c,v=nn;else if("BarChart"===d)e=p/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===h?c.x-e:s.left+.5,y:"horizontal"===h?s.top+.5:c.y-e,width:"horizontal"===h?p:s.width-1,height:"horizontal"===h?s.height-1:p},v=tw.A;else if("radial"===h){var m=nr(c),b=m.cx,g=m.cy,O=m.radius;o={cx:b,cy:g,startAngle:m.startAngle,endAngle:m.endAngle,innerRadius:O,outerRadius:O},v=no.L}else o={points:function(t,e,n){var r,o,i,a;if("horizontal"===t)i=r=e.x,o=n.top,a=n.top+n.height;else if("vertical"===t)a=o=e.y,r=n.left,i=n.left+n.width;else if(null!=e.cx&&null!=e.cy){if("centric"!==t)return nr(e);var u=e.cx,c=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=(0,eK.op)(u,c,l,f),h=(0,eK.op)(u,c,s,f);r=p.x,o=p.y,i=h.x,a=h.y}return[{x:r,y:o},{x:i,y:a}]}(h,c,s)},v=e8.H;var w=nu(nu(nu(nu({stroke:"#ccc",pointerEvents:"none"},s),o),(0,tg.L6)(y,!1)),{},{payload:l,payloadIndex:f,className:(0,I.Z)("recharts-tooltip-cursor",y.className)});return(0,x.isValidElement)(y)?(0,x.cloneElement)(y,w):(0,x.createElement)(v,w)}var nl=["item"],ns=["children","className","width","height","style","compact","title","desc"];function nf(t){return(nf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function np(){return(np=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function nh(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||ng(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nd(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function ny(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ny=function(){return!!t})()}function nv(t){return(nv=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function nm(t,e){return(nm=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function nb(t){return function(t){if(Array.isArray(t))return nx(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||ng(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ng(t,e){if(t){if("string"==typeof t)return nx(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return nx(t,e)}}function nx(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function nO(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function nw(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?nO(Object(n),!0).forEach(function(e){nj(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):nO(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function nj(t,e,n){return(e=nS(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function nS(t){var e=function(t,e){if("object"!=nf(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=nf(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nf(e)?e:e+""}var nP={xAxis:["bottom","top"],yAxis:["left","right"]},nA={width:"100%",height:"100%"},nE={x:0,y:0};function nk(t){return t}var nM=function(t,e,n,r){var o=e.find(function(t){return t&&t.index===n});if(o){if("horizontal"===t)return{x:o.coordinate,y:r.y};if("vertical"===t)return{x:r.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=r.radius;return nw(nw(nw({},r),(0,eK.op)(r.cx,r.cy,a,i)),{},{angle:i,radius:a})}var u=o.coordinate,c=r.angle;return nw(nw(nw({},r),(0,eK.op)(r.cx,r.cy,u,c)),{},{angle:c,radius:u})}return nE},n_=function(t,e){var n=e.graphicalItems,r=e.dataStartIndex,o=e.dataEndIndex,i=(null!=n?n:[]).reduce(function(t,e){var n=e.props.data;return n&&n.length?[].concat(nb(t),nb(n)):t},[]);return i.length>0?i:t&&t.length&&(0,B.hj)(r)&&(0,B.hj)(o)?t.slice(r,o+1):[]};function nT(t){return"number"===t?[0,"auto"]:void 0}var nC=function(t,e,n,r){var o=t.graphicalItems,i=t.tooltipAxis,a=n_(e,t);return n<0||!o||!o.length||n>=a.length?null:o.reduce(function(o,u){var c,l,s=null!==(c=u.props.data)&&void 0!==c?c:e;if(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=n&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),i.dataKey&&!i.allowDuplicatedCategory){var f=void 0===s?a:s;l=(0,B.Ap)(f,i.dataKey,r)}else l=s&&s[n]||a[n];return l?[].concat(nb(o),[(0,tP.Qo)(u,l)]):o},[])},nI=function(t,e,n,r){var o=r||{x:t.chartX,y:t.chartY},i="horizontal"===n?o.x:"vertical"===n?o.y:"centric"===n?o.angle:o.radius,a=t.orderedTooltipTicks,u=t.tooltipAxis,c=t.tooltipTicks,l=(0,tP.VO)(i,a,c,u);if(l>=0&&c){var s=c[l]&&c[l].value,f=nC(t,e,l,s),p=nM(n,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},nD=function(t,e){var n=e.axes,r=e.graphicalItems,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,u=e.dataStartIndex,c=e.dataEndIndex,l=t.layout,s=t.children,f=t.stackOffset,p=(0,tP.NA)(l,o);return n.reduce(function(e,n){var h=void 0!==n.type.defaultProps?nw(nw({},n.type.defaultProps),n.props):n.props,d=h.type,y=h.dataKey,v=h.allowDataOverflow,m=h.allowDuplicatedCategory,b=h.scale,g=h.ticks,x=h.includeHidden,O=h[i];if(e[O])return e;var j=n_(t.data,{graphicalItems:r.filter(function(t){var e;return(i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i])===O}),dataStartIndex:u,dataEndIndex:c}),S=j.length;(function(t,e,n){if("number"===n&&!0===e&&Array.isArray(t)){var r=null==t?void 0:t[0],o=null==t?void 0:t[1];if(r&&o&&(0,B.hj)(r)&&(0,B.hj)(o))return!0}return!1})(h.domain,v,d)&&(k=(0,tP.LG)(h.domain,null,v),p&&("number"===d||"auto"!==b)&&(_=(0,tP.gF)(j,y,"category")));var P=nT(d);if(!k||0===k.length){var E,k,M,_,T,C=null!==(T=h.domain)&&void 0!==T?T:P;if(y){if(k=(0,tP.gF)(j,y,d),"category"===d&&p){var I=(0,B.bv)(k);m&&I?(M=k,k=A()(0,S)):m||(k=(0,tP.ko)(C,k,n).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(nb(t),[e])},[]))}else if("category"===d)k=m?k.filter(function(t){return""!==t&&!w()(t)}):(0,tP.ko)(C,k,n).reduce(function(t,e){return t.indexOf(e)>=0||""===e||w()(e)?t:[].concat(nb(t),[e])},[]);else if("number"===d){var D=(0,tP.ZI)(j,r.filter(function(t){var e,n,r=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(n=t.type.defaultProps)||void 0===n?void 0:n.hide;return r===O&&(x||!o)}),y,o,l);D&&(k=D)}p&&("number"===d||"auto"!==b)&&(_=(0,tP.gF)(j,y,"category"))}else k=p?A()(0,S):a&&a[O]&&a[O].hasStack&&"number"===d?"expand"===f?[0,1]:(0,tP.EB)(a[O].stackGroups,u,c):(0,tP.s6)(j,r.filter(function(t){var e=i in t.props?t.props[i]:t.type.defaultProps[i],n="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===O&&(x||!n)}),d,l,!0);"number"===d?(k=eG(s,k,O,o,g),C&&(k=(0,tP.LG)(C,k,v))):"category"===d&&C&&k.every(function(t){return C.indexOf(t)>=0})&&(k=C)}return nw(nw({},e),{},nj({},O,nw(nw({},h),{},{axisType:o,domain:k,categoricalDomain:_,duplicateDomain:M,originalDomain:null!==(E=h.domain)&&void 0!==E?E:P,isCategorical:p,layout:l})))},{})},nN=function(t,e){var n=e.graphicalItems,r=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,u=e.dataStartIndex,c=e.dataEndIndex,l=t.layout,s=t.children,f=n_(t.data,{graphicalItems:n,dataStartIndex:u,dataEndIndex:c}),p=f.length,h=(0,tP.NA)(l,o),d=-1;return n.reduce(function(t,e){var y,v=(void 0!==e.type.defaultProps?nw(nw({},e.type.defaultProps),e.props):e.props)[i],m=nT("number");return t[v]?t:(d++,y=h?A()(0,p):a&&a[v]&&a[v].hasStack?eG(s,y=(0,tP.EB)(a[v].stackGroups,u,c),v,o):eG(s,y=(0,tP.LG)(m,(0,tP.s6)(f,n.filter(function(t){var e,n,r=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(n=t.type.defaultProps)||void 0===n?void 0:n.hide;return r===v&&!o}),"number",l),r.defaultProps.allowDataOverflow),v,o),nw(nw({},t),{},nj({},v,nw(nw({axisType:o},r.defaultProps),{},{hide:!0,orientation:k()(nP,"".concat(o,".").concat(d%2),null),domain:y,originalDomain:m,isCategorical:h,layout:l}))))},{})},nL=function(t,e){var n=e.axisType,r=void 0===n?"xAxis":n,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,u=e.dataStartIndex,c=e.dataEndIndex,l=t.children,s="".concat(r,"Id"),f=(0,tg.NN)(l,o),p={};return f&&f.length?p=nD(t,{axes:f,graphicalItems:i,axisType:r,axisIdKey:s,stackGroups:a,dataStartIndex:u,dataEndIndex:c}):i&&i.length&&(p=nN(t,{Axis:o,graphicalItems:i,axisType:r,axisIdKey:s,stackGroups:a,dataStartIndex:u,dataEndIndex:c})),p},nB=function(t){var e=(0,B.Kt)(t),n=(0,tP.uY)(e,!1,!0);return{tooltipTicks:n,orderedTooltipTicks:_()(n,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:(0,tP.zT)(e,n)}},nR=function(t){var e=t.children,n=t.defaultShowTooltip,r=(0,tg.sP)(e,tq),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),r&&r.props&&(r.props.startIndex>=0&&(o=r.props.startIndex),r.props.endIndex>=0&&(i=r.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!n}},nz=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},nU=function(t,e){var n=t.props,r=t.graphicalItems,o=t.xAxisMap,i=void 0===o?{}:o,a=t.yAxisMap,u=void 0===a?{}:a,c=n.width,l=n.height,s=n.children,f=n.margin||{},p=(0,tg.sP)(s,tq),h=(0,tg.sP)(s,tm.D),d=Object.keys(u).reduce(function(t,e){var n=u[e],r=n.orientation;return n.mirror||n.hide?t:nw(nw({},t),{},nj({},r,t[r]+n.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(i).reduce(function(t,e){var n=i[e],r=n.orientation;return n.mirror||n.hide?t:nw(nw({},t),{},nj({},r,k()(t,"".concat(r))+n.height))},{top:f.top||0,bottom:f.bottom||0}),v=nw(nw({},y),d),m=v.bottom;p&&(v.bottom+=p.props.height||tq.defaultProps.height),h&&e&&(v=(0,tP.By)(v,r,n,e));var b=c-v.left-v.right,g=l-v.top-v.bottom;return nw(nw({brushBottom:m},v),{},{width:Math.max(b,0),height:Math.max(g,0)})},n$=["points","className","baseLinePoints","connectNulls"];function nF(){return(nF=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function nZ(t){return function(t){if(Array.isArray(t))return nq(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return nq(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return nq(t,void 0)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nq(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var nW=function(t){return t&&t.x===+t.x&&t.y===+t.y},nY=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=[[]];return t.forEach(function(t){nW(t)?e[e.length-1].push(t):e[e.length-1].length>0&&e.push([])}),nW(t[0])&&e[e.length-1].push(t[0]),e[e.length-1].length<=0&&(e=e.slice(0,-1)),e},nH=function(t,e){var n=nY(t);e&&(n=[n.reduce(function(t,e){return[].concat(nZ(t),nZ(e))},[])]);var r=n.map(function(t){return t.reduce(function(t,e,n){return"".concat(t).concat(0===n?"M":"L").concat(e.x,",").concat(e.y)},"")}).join("");return 1===n.length?"".concat(r,"Z"):r},nX=function(t,e,n){var r=nH(t,n);return"".concat("Z"===r.slice(-1)?r.slice(0,-1):r,"L").concat(nH(e.reverse(),n).slice(1))},nV=function(t){var e=t.points,n=t.className,r=t.baseLinePoints,o=t.connectNulls,i=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,n$);if(!e||!e.length)return null;var a=(0,I.Z)("recharts-polygon",n);if(r&&r.length){var u=i.stroke&&"none"!==i.stroke,c=nX(e,r,o);return x.createElement("g",{className:a},x.createElement("path",nF({},(0,tg.L6)(i,!0),{fill:"Z"===c.slice(-1)?i.fill:"none",stroke:"none",d:c})),u?x.createElement("path",nF({},(0,tg.L6)(i,!0),{fill:"none",d:nH(e,o)})):null,u?x.createElement("path",nF({},(0,tg.L6)(i,!0),{fill:"none",d:nH(r,o)})):null)}var l=nH(e,o);return x.createElement("path",nF({},(0,tg.L6)(i,!0),{fill:"Z"===l.slice(-1)?i.fill:"none",className:a,d:l}))};function nG(t){return(nG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nK(){return(nK=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function nJ(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function nQ(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?nJ(Object(n),!0).forEach(function(e){n3(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):nJ(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function n0(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,n5(r.key),r)}}function n1(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(n1=function(){return!!t})()}function n2(t){return(n2=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function n6(t,e){return(n6=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function n3(t,e,n){return(e=n5(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function n5(t){var e=function(t,e){if("object"!=nG(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=nG(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nG(e)?e:e+""}var n4=Math.PI/180,n8=function(t){var e,n;function r(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=n2(t),function(t,e){if(e&&("object"===nG(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,n1()?Reflect.construct(t,e||[],n2(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&n6(t,e)}(r,t),e=[{key:"getTickLineCoord",value:function(t){var e=this.props,n=e.cx,r=e.cy,o=e.radius,i=e.orientation,a=e.tickSize,u=(0,eK.op)(n,r,o,t.coordinate),c=(0,eK.op)(n,r,o+("inner"===i?-1:1)*(a||8),t.coordinate);return{x1:u.x,y1:u.y,x2:c.x,y2:c.y}}},{key:"getTickTextAnchor",value:function(t){var e=this.props.orientation,n=Math.cos(-t.coordinate*n4);return n>1e-5?"outer"===e?"start":"end":n<-.00001?"outer"===e?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,n=t.cy,r=t.radius,o=t.axisLine,i=t.axisLineType,a=nQ(nQ({},(0,tg.L6)(this.props,!1)),{},{fill:"none"},(0,tg.L6)(o,!1));if("circle"===i)return x.createElement(tO,nK({className:"recharts-polar-angle-axis-line"},a,{cx:e,cy:n,r:r}));var u=this.props.ticks.map(function(t){return(0,eK.op)(e,n,r,t.coordinate)});return x.createElement(nV,nK({className:"recharts-polar-angle-axis-line"},a,{points:u}))}},{key:"renderTicks",value:function(){var t=this,e=this.props,n=e.ticks,o=e.tick,i=e.tickLine,a=e.tickFormatter,u=e.stroke,c=(0,tg.L6)(this.props,!1),l=(0,tg.L6)(o,!1),s=nQ(nQ({},c),{},{fill:"none"},(0,tg.L6)(i,!1)),f=n.map(function(e,n){var f=t.getTickLineCoord(e),p=nQ(nQ(nQ({textAnchor:t.getTickTextAnchor(e)},c),{},{stroke:"none",fill:u},l),{},{index:n,payload:e,x:f.x2,y:f.y2});return x.createElement(L.m,nK({className:(0,I.Z)("recharts-polar-angle-axis-tick",(0,eK.$S)(o)),key:"tick-".concat(e.coordinate)},(0,tb.bw)(t.props,e,n)),i&&x.createElement("line",nK({className:"recharts-polar-angle-axis-tick-line"},s,f)),o&&r.renderTickItem(o,p,a?a(e.value,n):e.value))});return x.createElement(L.m,{className:"recharts-polar-angle-axis-ticks"},f)}},{key:"render",value:function(){var t=this.props,e=t.ticks,n=t.radius,r=t.axisLine;return!(n<=0)&&e&&e.length?x.createElement(L.m,{className:(0,I.Z)("recharts-polar-angle-axis",this.props.className)},r&&this.renderAxisLine(),this.renderTicks()):null}}],n=[{key:"renderTickItem",value:function(t,e,n){return x.isValidElement(t)?x.cloneElement(t,e):S()(t)?t(e):x.createElement(tS.x,nK({},e,{className:"recharts-polar-angle-axis-tick-value"}),n)}}],e&&n0(r.prototype,e),n&&n0(r,n),Object.defineProperty(r,"prototype",{writable:!1}),r}(x.PureComponent);n3(n8,"displayName","PolarAngleAxis"),n3(n8,"axisType","angleAxis"),n3(n8,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var n7=n(31896),n9=n.n(n7),rt=n(28906),re=n.n(rt),rn=["cx","cy","angle","ticks","axisLine"],rr=["ticks","tick","angle","tickFormatter","stroke"];function ro(t){return(ro="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ri(){return(ri=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function ra(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function ru(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ra(Object(n),!0).forEach(function(e){rh(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ra(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function rc(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function rl(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,rd(r.key),r)}}function rs(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(rs=function(){return!!t})()}function rf(t){return(rf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function rp(t,e){return(rp=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function rh(t,e,n){return(e=rd(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function rd(t){var e=function(t,e){if("object"!=ro(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=ro(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ro(e)?e:e+""}var ry=function(t){var e,n;function r(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=rf(t),function(t,e){if(e&&("object"===ro(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,rs()?Reflect.construct(t,e||[],rf(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&rp(t,e)}(r,t),e=[{key:"getTickValueCoord",value:function(t){var e=t.coordinate,n=this.props,r=n.angle,o=n.cx,i=n.cy;return(0,eK.op)(o,i,e,r)}},{key:"getTickTextAnchor",value:function(){var t;switch(this.props.orientation){case"left":t="end";break;case"right":t="start";break;default:t="middle"}return t}},{key:"getViewBox",value:function(){var t=this.props,e=t.cx,n=t.cy,r=t.angle,o=t.ticks,i=n9()(o,function(t){return t.coordinate||0});return{cx:e,cy:n,startAngle:r,endAngle:r,innerRadius:re()(o,function(t){return t.coordinate||0}).coordinate||0,outerRadius:i.coordinate||0}}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,n=t.cy,r=t.angle,o=t.ticks,i=t.axisLine,a=rc(t,rn),u=o.reduce(function(t,e){return[Math.min(t[0],e.coordinate),Math.max(t[1],e.coordinate)]},[1/0,-1/0]),c=(0,eK.op)(e,n,u[0],r),l=(0,eK.op)(e,n,u[1],r),s=ru(ru(ru({},(0,tg.L6)(a,!1)),{},{fill:"none"},(0,tg.L6)(i,!1)),{},{x1:c.x,y1:c.y,x2:l.x,y2:l.y});return x.createElement("line",ri({className:"recharts-polar-radius-axis-line"},s))}},{key:"renderTicks",value:function(){var t=this,e=this.props,n=e.ticks,o=e.tick,i=e.angle,a=e.tickFormatter,u=e.stroke,c=rc(e,rr),l=this.getTickTextAnchor(),s=(0,tg.L6)(c,!1),f=(0,tg.L6)(o,!1),p=n.map(function(e,n){var c=t.getTickValueCoord(e),p=ru(ru(ru(ru({textAnchor:l,transform:"rotate(".concat(90-i,", ").concat(c.x,", ").concat(c.y,")")},s),{},{stroke:"none",fill:u},f),{},{index:n},c),{},{payload:e});return x.createElement(L.m,ri({className:(0,I.Z)("recharts-polar-radius-axis-tick",(0,eK.$S)(o)),key:"tick-".concat(e.coordinate)},(0,tb.bw)(t.props,e,n)),r.renderTickItem(o,p,a?a(e.value,n):e.value))});return x.createElement(L.m,{className:"recharts-polar-radius-axis-ticks"},p)}},{key:"render",value:function(){var t=this.props,e=t.ticks,n=t.axisLine,r=t.tick;return e&&e.length?x.createElement(L.m,{className:(0,I.Z)("recharts-polar-radius-axis",this.props.className)},n&&this.renderAxisLine(),r&&this.renderTicks(),tH._.renderCallByParent(this.props,this.getViewBox())):null}}],n=[{key:"renderTickItem",value:function(t,e,n){return x.isValidElement(t)?x.cloneElement(t,e):S()(t)?t(e):x.createElement(tS.x,ri({},e,{className:"recharts-polar-radius-axis-tick-value"}),n)}}],e&&rl(r.prototype,e),n&&rl(r,n),Object.defineProperty(r,"prototype",{writable:!1}),r}(x.PureComponent);rh(ry,"displayName","PolarRadiusAxis"),rh(ry,"axisType","radiusAxis"),rh(ry,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});var rv=(a=(i={chartName:"PieChart",GraphicalChild:n(79375).b,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:n8},{axisType:"radiusAxis",AxisComp:ry}],formatAxisMap:eK.t9,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}).chartName,u=i.GraphicalChild,l=void 0===(c=i.defaultTooltipEventType)?"axis":c,f=void 0===(s=i.validateTooltipEventTypes)?["axis"]:s,p=i.axisComponents,h=i.legendContent,d=i.formatAxisMap,y=i.defaultProps,v=function(t,e){var n=e.graphicalItems,r=e.stackGroups,o=e.offset,i=e.updateId,a=e.dataStartIndex,u=e.dataEndIndex,c=t.barSize,l=t.layout,s=t.barGap,f=t.barCategoryGap,h=t.maxBarSize,d=nz(l),y=d.numericAxisName,v=d.cateAxisName,m=!!n&&!!n.length&&n.some(function(t){var e=(0,tg.Gf)(t&&t.type);return e&&e.indexOf("Bar")>=0}),b=[];return n.forEach(function(n,d){var g=n_(t.data,{graphicalItems:[n],dataStartIndex:a,dataEndIndex:u}),x=void 0!==n.type.defaultProps?nw(nw({},n.type.defaultProps),n.props):n.props,O=x.dataKey,j=x.maxBarSize,S=x["".concat(y,"Id")],P=x["".concat(v,"Id")],A=p.reduce(function(t,n){var r=e["".concat(n.axisType,"Map")],o=x["".concat(n.axisType,"Id")];r&&r[o]||"zAxis"===n.axisType||(0,D.Z)(!1);var i=r[o];return nw(nw({},t),{},nj(nj({},n.axisType,i),"".concat(n.axisType,"Ticks"),(0,tP.uY)(i)))},{}),E=A[v],k=A["".concat(v,"Ticks")],M=r&&r[S]&&r[S].hasStack&&(0,tP.O3)(n,r[S].stackGroups),_=(0,tg.Gf)(n.type).indexOf("Bar")>=0,T=(0,tP.zT)(E,k),C=[],I=m&&(0,tP.pt)({barSize:c,stackGroups:r,totalSize:"xAxis"===v?A[v].width:"yAxis"===v?A[v].height:void 0});if(_){var N,L,B=w()(j)?h:j,R=null!==(N=null!==(L=(0,tP.zT)(E,k,!0))&&void 0!==L?L:B)&&void 0!==N?N:0;C=(0,tP.qz)({barGap:s,barCategoryGap:f,bandSize:R!==T?R:T,sizeList:I[P],maxBarSize:B}),R!==T&&(C=C.map(function(t){return nw(nw({},t),{},{position:nw(nw({},t.position),{},{offset:t.position.offset-R/2})})}))}var z=n&&n.type&&n.type.getComposedData;z&&b.push({props:nw(nw({},z(nw(nw({},A),{},{displayedData:g,props:t,dataKey:O,item:n,bandSize:T,barPosition:C,offset:o,stackedData:M,layout:l,dataStartIndex:a,dataEndIndex:u}))),{},nj(nj(nj({key:n.key||"item-".concat(d)},y,A[y]),v,A[v]),"animationId",i)),childIndex:(0,tg.$R)(n,t.children),item:n})}),b},m=function(t,e){var n=t.props,r=t.dataStartIndex,o=t.dataEndIndex,i=t.updateId;if(!(0,tg.TT)({props:n}))return null;var c=n.children,l=n.layout,s=n.stackOffset,f=n.data,h=n.reverseStackOrder,y=nz(l),m=y.numericAxisName,b=y.cateAxisName,g=(0,tg.NN)(c,u),x=(0,tP.wh)(f,g,"".concat(m,"Id"),"".concat(b,"Id"),s,h),O=p.reduce(function(t,e){var i="".concat(e.axisType,"Map");return nw(nw({},t),{},nj({},i,nL(n,nw(nw({},e),{},{graphicalItems:g,stackGroups:e.axisType===m&&x,dataStartIndex:r,dataEndIndex:o}))))},{}),w=nU(nw(nw({},O),{},{props:n,graphicalItems:g}),null==e?void 0:e.legendBBox);Object.keys(O).forEach(function(t){O[t]=d(n,O[t],w,t.replace("Map",""),a)});var j=nB(O["".concat(b,"Map")]),S=v(n,nw(nw({},O),{},{dataStartIndex:r,dataEndIndex:o,updateId:i,graphicalItems:g,stackGroups:x,offset:w}));return nw(nw({formattedGraphicalItems:S,graphicalItems:g,offset:w,stackGroups:x},j),O)},b=function(t){var e;function n(t){var e,r,o,i,u;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),i=n,u=[t],i=nv(i),nj(o=function(t,e){if(e&&("object"===nf(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,ny()?Reflect.construct(i,u||[],nv(this).constructor):i.apply(this,u)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),nj(o,"accessibilityManager",new e5),nj(o,"handleLegendBBoxUpdate",function(t){if(t){var e=o.state,n=e.dataStartIndex,r=e.dataEndIndex,i=e.updateId;o.setState(nw({legendBBox:t},m({props:o.props,dataStartIndex:n,dataEndIndex:r,updateId:i},nw(nw({},o.state),{},{legendBBox:t}))))}}),nj(o,"handleReceiveSyncEvent",function(t,e,n){o.props.syncId===t&&(n!==o.eventEmitterSymbol||"function"==typeof o.props.syncMethod)&&o.applySyncEvent(e)}),nj(o,"handleBrushChange",function(t){var e=t.startIndex,n=t.endIndex;if(e!==o.state.dataStartIndex||n!==o.state.dataEndIndex){var r=o.state.updateId;o.setState(function(){return nw({dataStartIndex:e,dataEndIndex:n},m({props:o.props,dataStartIndex:e,dataEndIndex:n,updateId:r},o.state))}),o.triggerSyncEvent({dataStartIndex:e,dataEndIndex:n})}}),nj(o,"handleMouseEnter",function(t){var e=o.getMouseInfo(t);if(e){var n=nw(nw({},e),{},{isTooltipActive:!0});o.setState(n),o.triggerSyncEvent(n);var r=o.props.onMouseEnter;S()(r)&&r(n,t)}}),nj(o,"triggeredAfterMouseMove",function(t){var e=o.getMouseInfo(t),n=e?nw(nw({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};o.setState(n),o.triggerSyncEvent(n);var r=o.props.onMouseMove;S()(r)&&r(n,t)}),nj(o,"handleItemMouseEnter",function(t){o.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),nj(o,"handleItemMouseLeave",function(){o.setState(function(){return{isTooltipActive:!1}})}),nj(o,"handleMouseMove",function(t){t.persist(),o.throttleTriggeredAfterMouseMove(t)}),nj(o,"handleMouseLeave",function(t){o.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};o.setState(e),o.triggerSyncEvent(e);var n=o.props.onMouseLeave;S()(n)&&n(e,t)}),nj(o,"handleOuterEvent",function(t){var e,n=(0,tg.Bh)(t),r=k()(o.props,"".concat(n));n&&S()(r)&&r(null!==(e=/.*touch.*/i.test(n)?o.getMouseInfo(t.changedTouches[0]):o.getMouseInfo(t))&&void 0!==e?e:{},t)}),nj(o,"handleClick",function(t){var e=o.getMouseInfo(t);if(e){var n=nw(nw({},e),{},{isTooltipActive:!0});o.setState(n),o.triggerSyncEvent(n);var r=o.props.onClick;S()(r)&&r(n,t)}}),nj(o,"handleMouseDown",function(t){var e=o.props.onMouseDown;S()(e)&&e(o.getMouseInfo(t),t)}),nj(o,"handleMouseUp",function(t){var e=o.props.onMouseUp;S()(e)&&e(o.getMouseInfo(t),t)}),nj(o,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&o.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),nj(o,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&o.handleMouseDown(t.changedTouches[0])}),nj(o,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&o.handleMouseUp(t.changedTouches[0])}),nj(o,"handleDoubleClick",function(t){var e=o.props.onDoubleClick;S()(e)&&e(o.getMouseInfo(t),t)}),nj(o,"handleContextMenu",function(t){var e=o.props.onContextMenu;S()(e)&&e(o.getMouseInfo(t),t)}),nj(o,"triggerSyncEvent",function(t){void 0!==o.props.syncId&&e0.emit(e1,o.props.syncId,t,o.eventEmitterSymbol)}),nj(o,"applySyncEvent",function(t){var e=o.props,n=e.layout,r=e.syncMethod,i=o.state.updateId,a=t.dataStartIndex,u=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)o.setState(nw({dataStartIndex:a,dataEndIndex:u},m({props:o.props,dataStartIndex:a,dataEndIndex:u,updateId:i},o.state)));else if(void 0!==t.activeTooltipIndex){var c=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=o.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof r)s=r(h,t);else if("value"===r){s=-1;for(var d=0;d<h.length;d++)if(h[d].value===t.activeLabel){s=d;break}}var y=nw(nw({},p),{},{x:p.left,y:p.top}),v=Math.min(c,y.x+y.width),b=Math.min(l,y.y+y.height),g=h[s]&&h[s].value,x=nC(o.state,o.props.data,s),O=h[s]?{x:"horizontal"===n?h[s].coordinate:v,y:"horizontal"===n?b:h[s].coordinate}:nE;o.setState(nw(nw({},t),{},{activeLabel:g,activeCoordinate:O,activePayload:x,activeTooltipIndex:s}))}else o.setState(t)}),nj(o,"renderCursor",function(t){var e,n=o.state,r=n.isTooltipActive,i=n.activeCoordinate,u=n.activePayload,c=n.offset,l=n.activeTooltipIndex,s=n.tooltipAxisBandSize,f=o.getTooltipEventType(),p=null!==(e=t.props.active)&&void 0!==e?e:r,h=o.props.layout,d=t.key||"_recharts-cursor";return x.createElement(nc,{key:d,activeCoordinate:i,activePayload:u,activeTooltipIndex:l,chartName:a,element:t,isActive:p,layout:h,offset:c,tooltipAxisBandSize:s,tooltipEventType:f})}),nj(o,"renderPolarAxis",function(t,e,n){var r=k()(t,"type.axisType"),i=k()(o.state,"".concat(r,"Map")),a=t.type.defaultProps,u=void 0!==a?nw(nw({},a),t.props):t.props,c=i&&i[u["".concat(r,"Id")]];return(0,x.cloneElement)(t,nw(nw({},c),{},{className:(0,I.Z)(r,c.className),key:t.key||"".concat(e,"-").concat(n),ticks:(0,tP.uY)(c,!0)}))}),nj(o,"renderPolarGrid",function(t){var e=t.props,n=e.radialLines,r=e.polarAngles,i=e.polarRadius,a=o.state,u=a.radiusAxisMap,c=a.angleAxisMap,l=(0,B.Kt)(u),s=(0,B.Kt)(c),f=s.cx,p=s.cy,h=s.innerRadius,d=s.outerRadius;return(0,x.cloneElement)(t,{polarAngles:Array.isArray(r)?r:(0,tP.uY)(s,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(i)?i:(0,tP.uY)(l,!0).map(function(t){return t.coordinate}),cx:f,cy:p,innerRadius:h,outerRadius:d,key:t.key||"polar-grid",radialLines:n})}),nj(o,"renderLegend",function(){var t=o.state.formattedGraphicalItems,e=o.props,n=e.children,r=e.width,i=e.height,a=o.props.margin||{},u=r-(a.left||0)-(a.right||0),c=(0,tY.z)({children:n,formattedGraphicalItems:t,legendWidth:u,legendContent:h});if(!c)return null;var l=c.item,s=nd(c,nl);return(0,x.cloneElement)(l,nw(nw({},s),{},{chartWidth:r,chartHeight:i,margin:a,onBBoxUpdate:o.handleLegendBBoxUpdate}))}),nj(o,"renderTooltip",function(){var t,e=o.props,n=e.children,r=e.accessibilityLayer,i=(0,tg.sP)(n,tv);if(!i)return null;var a=o.state,u=a.isTooltipActive,c=a.activeCoordinate,l=a.activePayload,s=a.activeLabel,f=a.offset,p=null!==(t=i.props.active)&&void 0!==t?t:u;return(0,x.cloneElement)(i,{viewBox:nw(nw({},f),{},{x:f.left,y:f.top}),active:p,label:s,payload:p?l:[],coordinate:c,accessibilityLayer:r})}),nj(o,"renderBrush",function(t){var e=o.props,n=e.margin,r=e.data,i=o.state,a=i.offset,u=i.dataStartIndex,c=i.dataEndIndex,l=i.updateId;return(0,x.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:(0,tP.DO)(o.handleBrushChange,t.props.onChange),data:r,x:(0,B.hj)(t.props.x)?t.props.x:a.left,y:(0,B.hj)(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(n.bottom||0),width:(0,B.hj)(t.props.width)?t.props.width:a.width,startIndex:u,endIndex:c,updateId:"brush-".concat(l)})}),nj(o,"renderReferenceElement",function(t,e,n){if(!t)return null;var r=o.clipPathId,i=o.state,a=i.xAxisMap,u=i.yAxisMap,c=i.offset,l=t.type.defaultProps||{},s=t.props,f=s.xAxisId,p=void 0===f?l.xAxisId:f,h=s.yAxisId,d=void 0===h?l.yAxisId:h;return(0,x.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(n),xAxis:a[p],yAxis:u[d],viewBox:{x:c.left,y:c.top,width:c.width,height:c.height},clipPathId:r})}),nj(o,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,o=t.basePoint,i=t.childIndex,a=t.isRange,u=[],c=e.props.key,l=void 0!==e.item.type.defaultProps?nw(nw({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=nw(nw({index:i,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:(0,tP.fk)(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},(0,tg.L6)(s,!1)),(0,tb.Ym)(s));return u.push(n.renderActiveDot(s,f,"".concat(c,"-activePoint-").concat(i))),o?u.push(n.renderActiveDot(s,nw(nw({},f),{},{cx:o.x,cy:o.y}),"".concat(c,"-basePoint-").concat(i))):a&&u.push(null),u}),nj(o,"renderGraphicChild",function(t,e,n){var r=o.filterFormatItem(t,e,n);if(!r)return null;var i=o.getTooltipEventType(),a=o.state,u=a.isTooltipActive,c=a.tooltipAxis,l=a.activeTooltipIndex,s=a.activeLabel,f=o.props.children,p=(0,tg.sP)(f,tv),h=r.props,d=h.points,y=h.isRange,v=h.baseLine,m=void 0!==r.item.type.defaultProps?nw(nw({},r.item.type.defaultProps),r.item.props):r.item.props,b=m.activeDot,g=m.hide,O=m.activeBar,j=m.activeShape,S={};"axis"!==i&&p&&"click"===p.props.trigger?S={onClick:(0,tP.DO)(o.handleItemMouseEnter,t.props.onClick)}:"axis"!==i&&(S={onMouseLeave:(0,tP.DO)(o.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:(0,tP.DO)(o.handleItemMouseEnter,t.props.onMouseEnter)});var P=(0,x.cloneElement)(t,nw(nw({},r.props),S));if(!g&&u&&p&&(b||O||j)){if(l>=0){if(c.dataKey&&!c.allowDuplicatedCategory){var A="function"==typeof c.dataKey?function(t){return"function"==typeof c.dataKey?c.dataKey(t.payload):null}:"payload.".concat(c.dataKey.toString());k=(0,B.Ap)(d,A,s),M=y&&v&&(0,B.Ap)(v,A,s)}else k=null==d?void 0:d[l],M=y&&v&&v[l];if(j||O){var E=void 0!==t.props.activeIndex?t.props.activeIndex:l;return[(0,x.cloneElement)(t,nw(nw(nw({},r.props),S),{},{activeIndex:E})),null,null]}if(!w()(k))return[P].concat(nb(o.renderActivePoints({item:r,activePoint:k,basePoint:M,childIndex:l,isRange:y})))}else{var k,M,_,T=(null!==(_=o.getItemByXY(o.state.activeCoordinate))&&void 0!==_?_:{graphicalItem:P}).graphicalItem,C=T.item,I=void 0===C?t:C,D=T.childIndex,N=nw(nw(nw({},r.props),S),{},{activeIndex:D});return[(0,x.cloneElement)(I,N),null,null]}}return y?[P,null,null]:[P,null]}),nj(o,"renderCustomized",function(t,e,n){return(0,x.cloneElement)(t,nw(nw({key:"recharts-customized-".concat(n)},o.props),o.state))}),nj(o,"renderMap",{CartesianGrid:{handler:nk,once:!0},ReferenceArea:{handler:o.renderReferenceElement},ReferenceLine:{handler:nk},ReferenceDot:{handler:o.renderReferenceElement},XAxis:{handler:nk},YAxis:{handler:nk},Brush:{handler:o.renderBrush,once:!0},Bar:{handler:o.renderGraphicChild},Line:{handler:o.renderGraphicChild},Area:{handler:o.renderGraphicChild},Radar:{handler:o.renderGraphicChild},RadialBar:{handler:o.renderGraphicChild},Scatter:{handler:o.renderGraphicChild},Pie:{handler:o.renderGraphicChild},Funnel:{handler:o.renderGraphicChild},Tooltip:{handler:o.renderCursor,once:!0},PolarGrid:{handler:o.renderPolarGrid,once:!0},PolarAngleAxis:{handler:o.renderPolarAxis},PolarRadiusAxis:{handler:o.renderPolarAxis},Customized:{handler:o.renderCustomized}}),o.clipPathId="".concat(null!==(e=t.id)&&void 0!==e?e:(0,B.EL)("recharts"),"-clip"),o.throttleTriggeredAfterMouseMove=C()(o.triggeredAfterMouseMove,null!==(r=t.throttleDelay)&&void 0!==r?r:1e3/60),o.state={},o}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&nm(t,e)}(n,t),e=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(t=this.props.margin.left)&&void 0!==t?t:0,top:null!==(e=this.props.margin.top)&&void 0!==e?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,n=t.data,r=t.height,o=t.layout,i=(0,tg.sP)(e,tv);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var u=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,c=nC(this.state,n,a,u),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+r)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=nw(nw({},f),p.props.points[a].tooltipPosition),c=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:u,activePayload:c,activeCoordinate:f};this.setState(h),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var n,r;this.accessibilityManager.setDetails({offset:{left:null!==(n=this.props.margin.left)&&void 0!==n?n:0,top:null!==(r=this.props.margin.top)&&void 0!==r?r:0}})}return null}},{key:"componentDidUpdate",value:function(t){(0,tg.rL)([(0,tg.sP)(t.children,tv)],[(0,tg.sP)(this.props.children,tv)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=(0,tg.sP)(this.props.children,tv);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return f.indexOf(e)>=0?e:l}return l}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,n=e.getBoundingClientRect(),r=(0,tW.os)(n),o={chartX:Math.round(t.pageX-r.left),chartY:Math.round(t.pageY-r.top)},i=n.width/e.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var u=this.state,c=u.xAxisMap,l=u.yAxisMap,s=this.getTooltipEventType(),f=nI(this.state,this.props.data,this.props.layout,a);if("axis"!==s&&c&&l){var p=(0,B.Kt)(c).scale,h=(0,B.Kt)(l).scale,d=p&&p.invert?p.invert(o.chartX):null,y=h&&h.invert?h.invert(o.chartY):null;return nw(nw({},o),{},{xValue:d,yValue:y},f)}return f?nw(nw({},o),f):null}},{key:"inRange",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=this.props.layout,o=t/n,i=e/n;if("horizontal"===r||"vertical"===r){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var u=this.state,c=u.angleAxisMap,l=u.radiusAxisMap;if(c&&l){var s=(0,B.Kt)(c);return(0,eK.z3)({x:o,y:i},s)}return null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),n=(0,tg.sP)(t,tv),r={};return n&&"axis"===e&&(r="click"===n.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),nw(nw({},(0,tb.Ym)(this.props,this.handleOuterEvent)),r)}},{key:"addListener",value:function(){e0.on(e1,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){e0.removeListener(e1,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,n){for(var r=this.state.formattedGraphicalItems,o=0,i=r.length;o<i;o++){var a=r[o];if(a.item===t||a.props.key===t.key||e===(0,tg.Gf)(a.item.type)&&n===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,n=e.left,r=e.top,o=e.height,i=e.width;return x.createElement("defs",null,x.createElement("clipPath",{id:t},x.createElement("rect",{x:n,y:r,height:o,width:i})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var n=nh(e,2),r=n[0],o=n[1];return nw(nw({},t),{},nj({},r,o.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var n=nh(e,2),r=n[0],o=n[1];return nw(nw({},t),{},nj({},r,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null===(e=this.state.xAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null===(e=this.state.yAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,n=e.formattedGraphicalItems,r=e.activeItem;if(n&&n.length)for(var o=0,i=n.length;o<i;o++){var a=n[o],u=a.props,c=a.item,l=void 0!==c.type.defaultProps?nw(nw({},c.type.defaultProps),c.props):c.props,s=(0,tg.Gf)(c.type);if("Bar"===s){var f=(u.data||[]).find(function(e){return(0,tw.X)(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(u.data||[]).find(function(e){return(0,eK.z3)(t,e)});if(p)return{graphicalItem:a,payload:p}}else if((0,e4.lT)(a,r)||(0,e4.V$)(a,r)||(0,e4.w7)(a,r)){var h=(0,e4.a3)({graphicalItem:a,activeTooltipItem:r,itemData:l.data}),d=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:nw(nw({},a),{},{childIndex:d}),payload:(0,e4.w7)(a,r)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t,e,n=this;if(!(0,tg.TT)(this))return null;var r=this.props,o=r.children,i=r.className,a=r.width,u=r.height,c=r.style,l=r.compact,s=r.title,f=r.desc,p=nd(r,ns),h=(0,tg.L6)(p,!1);if(l)return x.createElement(eO,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},x.createElement(N.T,np({},h,{width:a,height:u,title:s,desc:f}),this.renderClipPath(),(0,tg.eu)(o,this.renderMap)));this.props.accessibilityLayer&&(h.tabIndex=null!==(t=this.props.tabIndex)&&void 0!==t?t:0,h.role=null!==(e=this.props.role)&&void 0!==e?e:"application",h.onKeyDown=function(t){n.accessibilityManager.keyboardEvent(t)},h.onFocus=function(){n.accessibilityManager.focus()});var d=this.parseEventsOfWrapper();return x.createElement(eO,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},x.createElement("div",np({className:(0,I.Z)("recharts-wrapper",i),style:nw({position:"relative",cursor:"default",width:a,height:u},c)},d,{ref:function(t){n.container=t}}),x.createElement(N.T,np({},h,{width:a,height:u,title:s,desc:f,style:nA}),this.renderClipPath(),(0,tg.eu)(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,nS(r.key),r)}}(n.prototype,e),Object.defineProperty(n,"prototype",{writable:!1}),n}(x.Component),nj(b,"displayName",a),nj(b,"defaultProps",nw({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},y)),nj(b,"getDerivedStateFromProps",function(t,e){var n=t.dataKey,r=t.data,o=t.children,i=t.width,a=t.height,u=t.layout,c=t.stackOffset,l=t.margin,s=e.dataStartIndex,f=e.dataEndIndex;if(void 0===e.updateId){var p=nR(t);return nw(nw(nw({},p),{},{updateId:0},m(nw(nw({props:t},p),{},{updateId:0}),e)),{},{prevDataKey:n,prevData:r,prevWidth:i,prevHeight:a,prevLayout:u,prevStackOffset:c,prevMargin:l,prevChildren:o})}if(n!==e.prevDataKey||r!==e.prevData||i!==e.prevWidth||a!==e.prevHeight||u!==e.prevLayout||c!==e.prevStackOffset||!(0,eJ.w)(l,e.prevMargin)){var h=nR(t),d={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},y=nw(nw({},nI(e,r,u)),{},{updateId:e.updateId+1}),v=nw(nw(nw({},h),d),y);return nw(nw(nw({},v),m(nw({props:t},v),e)),{},{prevDataKey:n,prevData:r,prevWidth:i,prevHeight:a,prevLayout:u,prevStackOffset:c,prevMargin:l,prevChildren:o})}if(!(0,tg.rL)(o,e.prevChildren)){var b,g,x,O,j=(0,tg.sP)(o,tq),S=j&&null!==(b=null===(g=j.props)||void 0===g?void 0:g.startIndex)&&void 0!==b?b:s,P=j&&null!==(x=null===(O=j.props)||void 0===O?void 0:O.endIndex)&&void 0!==x?x:f,A=w()(r)||S!==s||P!==f?e.updateId+1:e.updateId;return nw(nw({updateId:A},m(nw(nw({props:t},e),{},{updateId:A,dataStartIndex:S,dataEndIndex:P}),e)),{},{prevChildren:o,dataStartIndex:S,dataEndIndex:P})}return null}),nj(b,"renderActiveDot",function(t,e,n){var r;return r=(0,x.isValidElement)(t)?(0,x.cloneElement)(t,e):S()(t)?t(e):x.createElement(tO,e),x.createElement(L.m,{className:"recharts-active-dot",key:n},r)}),(g=(0,x.forwardRef)(function(t,e){return x.createElement(b,np({},t,{ref:e}))})).displayName=b.displayName,g)},85150:function(t,e,n){"use strict";n.d(e,{b:function(){return r}});var r=function(t){return null};r.displayName="Cell"},46750:function(t,e,n){"use strict";n.d(e,{_:function(){return P}});var r=n(32486),o=n(86197),i=n.n(o),a=n(14119),u=n.n(a),c=n(5635),l=n.n(c),s=n(89824),f=n(40082),p=n(21105),h=n(46729),d=n(73845);function y(t){return(y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var v=["offset"];function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function b(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function g(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?b(Object(n),!0).forEach(function(e){var r,o;r=e,o=n[e],(r=function(t){var e=function(t,e){if("object"!=y(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=y(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==y(e)?e:e+""}(r))in t?Object.defineProperty(t,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[r]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):b(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function x(){return(x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var O=function(t){var e=t.value,n=t.formatter,r=i()(t.children)?e:t.children;return u()(n)?n(r):r},w=function(t,e,n){var o,a,u=t.position,c=t.viewBox,l=t.offset,f=t.className,p=c.cx,y=c.cy,v=c.innerRadius,m=c.outerRadius,b=c.startAngle,g=c.endAngle,O=c.clockWise,w=(v+m)/2,j=(0,h.uY)(g-b)*Math.min(Math.abs(g-b),360),S=j>=0?1:-1;"insideStart"===u?(o=b+S*l,a=O):"insideEnd"===u?(o=g-S*l,a=!O):"end"===u&&(o=g+S*l,a=O),a=j<=0?a:!a;var P=(0,d.op)(p,y,w,o),A=(0,d.op)(p,y,w,o+(a?1:-1)*359),E="M".concat(P.x,",").concat(P.y,"\n    A").concat(w,",").concat(w,",0,1,").concat(a?0:1,",\n    ").concat(A.x,",").concat(A.y),k=i()(t.id)?(0,h.EL)("recharts-radial-line-"):t.id;return r.createElement("text",x({},n,{dominantBaseline:"central",className:(0,s.Z)("recharts-radial-bar-label",f)}),r.createElement("defs",null,r.createElement("path",{id:k,d:E})),r.createElement("textPath",{xlinkHref:"#".concat(k)},e))},j=function(t){var e=t.viewBox,n=t.offset,r=t.position,o=e.cx,i=e.cy,a=e.innerRadius,u=e.outerRadius,c=(e.startAngle+e.endAngle)/2;if("outside"===r){var l=(0,d.op)(o,i,u+n,c),s=l.x;return{x:s,y:l.y,textAnchor:s>=o?"start":"end",verticalAnchor:"middle"}}if("center"===r)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===r)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===r)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=(0,d.op)(o,i,(a+u)/2,c);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},S=function(t){var e=t.viewBox,n=t.parentViewBox,r=t.offset,o=t.position,i=e.x,a=e.y,u=e.width,c=e.height,s=c>=0?1:-1,f=s*r,p=s>0?"end":"start",d=s>0?"start":"end",y=u>=0?1:-1,v=y*r,m=y>0?"end":"start",b=y>0?"start":"end";if("top"===o)return g(g({},{x:i+u/2,y:a-s*r,textAnchor:"middle",verticalAnchor:p}),n?{height:Math.max(a-n.y,0),width:u}:{});if("bottom"===o)return g(g({},{x:i+u/2,y:a+c+f,textAnchor:"middle",verticalAnchor:d}),n?{height:Math.max(n.y+n.height-(a+c),0),width:u}:{});if("left"===o){var x={x:i-v,y:a+c/2,textAnchor:m,verticalAnchor:"middle"};return g(g({},x),n?{width:Math.max(x.x-n.x,0),height:c}:{})}if("right"===o){var O={x:i+u+v,y:a+c/2,textAnchor:b,verticalAnchor:"middle"};return g(g({},O),n?{width:Math.max(n.x+n.width-O.x,0),height:c}:{})}var w=n?{width:u,height:c}:{};return"insideLeft"===o?g({x:i+v,y:a+c/2,textAnchor:b,verticalAnchor:"middle"},w):"insideRight"===o?g({x:i+u-v,y:a+c/2,textAnchor:m,verticalAnchor:"middle"},w):"insideTop"===o?g({x:i+u/2,y:a+f,textAnchor:"middle",verticalAnchor:d},w):"insideBottom"===o?g({x:i+u/2,y:a+c-f,textAnchor:"middle",verticalAnchor:p},w):"insideTopLeft"===o?g({x:i+v,y:a+f,textAnchor:b,verticalAnchor:d},w):"insideTopRight"===o?g({x:i+u-v,y:a+f,textAnchor:m,verticalAnchor:d},w):"insideBottomLeft"===o?g({x:i+v,y:a+c-f,textAnchor:b,verticalAnchor:p},w):"insideBottomRight"===o?g({x:i+u-v,y:a+c-f,textAnchor:m,verticalAnchor:p},w):l()(o)&&((0,h.hj)(o.x)||(0,h.hU)(o.x))&&((0,h.hj)(o.y)||(0,h.hU)(o.y))?g({x:i+(0,h.h1)(o.x,u),y:a+(0,h.h1)(o.y,c),textAnchor:"end",verticalAnchor:"end"},w):g({x:i+u/2,y:a+c/2,textAnchor:"middle",verticalAnchor:"middle"},w)};function P(t){var e,n=t.offset,o=g({offset:void 0===n?5:n},function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,v)),a=o.viewBox,c=o.position,l=o.value,d=o.children,y=o.content,m=o.className,b=o.textBreakAll;if(!a||i()(l)&&i()(d)&&!(0,r.isValidElement)(y)&&!u()(y))return null;if((0,r.isValidElement)(y))return(0,r.cloneElement)(y,o);if(u()(y)){if(e=(0,r.createElement)(y,o),(0,r.isValidElement)(e))return e}else e=O(o);var P="cx"in a&&(0,h.hj)(a.cx),A=(0,p.L6)(o,!0);if(P&&("insideStart"===c||"insideEnd"===c||"end"===c))return w(o,e,A);var E=P?j(o):S(o);return r.createElement(f.x,x({className:(0,s.Z)("recharts-label",void 0===m?"":m)},A,E,{breakAll:b}),e)}P.displayName="Label";var A=function(t){var e=t.cx,n=t.cy,r=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,u=t.radius,c=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,d=t.left,y=t.width,v=t.height,m=t.clockWise,b=t.labelViewBox;if(b)return b;if((0,h.hj)(y)&&(0,h.hj)(v)){if((0,h.hj)(s)&&(0,h.hj)(f))return{x:s,y:f,width:y,height:v};if((0,h.hj)(p)&&(0,h.hj)(d))return{x:p,y:d,width:y,height:v}}return(0,h.hj)(s)&&(0,h.hj)(f)?{x:s,y:f,width:0,height:0}:(0,h.hj)(e)&&(0,h.hj)(n)?{cx:e,cy:n,startAngle:o||r||0,endAngle:i||r||0,innerRadius:c||0,outerRadius:l||u||a||0,clockWise:m}:t.viewBox?t.viewBox:{}};P.parseViewBox=A,P.renderCallByParent=function(t,e){var n,o,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&i&&!t.label)return null;var a=t.children,c=A(t),s=(0,p.NN)(a,P).map(function(t,n){return(0,r.cloneElement)(t,{viewBox:e||c,key:"label-".concat(n)})});return i?[(n=t.label,o=e||c,n?!0===n?r.createElement(P,{key:"label-implicit",viewBox:o}):(0,h.P2)(n)?r.createElement(P,{key:"label-implicit",viewBox:o,value:n}):(0,r.isValidElement)(n)?n.type===P?(0,r.cloneElement)(n,{key:"label-implicit",viewBox:o}):r.createElement(P,{key:"label-implicit",content:n,viewBox:o}):u()(n)?r.createElement(P,{key:"label-implicit",content:n,viewBox:o}):l()(n)?r.createElement(P,x({viewBox:o},n,{key:"label-implicit"})):null:null)].concat(function(t){if(Array.isArray(t))return m(t)}(s)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(s)||function(t,e){if(t){if("string"==typeof t)return m(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return m(t,void 0)}}(s)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):s}},26109:function(t,e,n){"use strict";n.d(e,{D:function(){return I}});var r=n(32486),o=n(14119),i=n.n(o),a=n(89824),u=n(25197),c=n(81581),l=n(78743),s=n(95273);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(){return(p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function h(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function d(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(d=function(){return!!t})()}function y(t){return(y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function v(t,e){return(v=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function m(t,e,n){return(e=b(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function b(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=f(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}var g=function(t){var e;function n(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),t=n,e=arguments,t=y(t),function(t,e){if(e&&("object"===f(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,d()?Reflect.construct(t,e||[],y(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&v(t,e)}(n,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,n=32/6,o=32/3,i=t.inactive?e:t.color;if("plainline"===t.type)return r.createElement("line",{strokeWidth:4,fill:"none",stroke:i,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return r.createElement("path",{strokeWidth:4,fill:"none",stroke:i,d:"M0,".concat(16,"h").concat(o,"\n            A").concat(n,",").concat(n,",0,1,1,").concat(2*o,",").concat(16,"\n            H").concat(32,"M").concat(2*o,",").concat(16,"\n            A").concat(n,",").concat(n,",0,1,1,").concat(o,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return r.createElement("path",{stroke:"none",fill:i,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(r.isValidElement(t.legendIcon)){var a=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?h(Object(n),!0).forEach(function(e){m(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}({},t);return delete a.legendIcon,r.cloneElement(t.legendIcon,a)}return r.createElement(l.v,{fill:i,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,n=e.payload,o=e.iconSize,l=e.layout,f=e.formatter,h=e.inactiveColor,d={x:0,y:0,width:32,height:32},y={display:"horizontal"===l?"inline-block":"block",marginRight:10},v={display:"inline-block",verticalAlign:"middle",marginRight:4};return n.map(function(e,n){var l=e.formatter||f,b=(0,a.Z)(m(m({"recharts-legend-item":!0},"legend-item-".concat(n),!0),"inactive",e.inactive));if("none"===e.type)return null;var g=i()(e.value)?null:e.value;(0,u.Z)(!i()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var x=e.inactive?h:e.color;return r.createElement("li",p({className:b,style:y,key:"legend-item-".concat(n)},(0,s.bw)(t.props,e,n)),r.createElement(c.T,{width:o,height:o,viewBox:d,style:v},t.renderIcon(e)),r.createElement("span",{className:"recharts-legend-item-text",style:{color:x}},l?l(g,e,n):g))})}},{key:"render",value:function(){var t=this.props,e=t.payload,n=t.layout,o=t.align;return e&&e.length?r.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===n?o:"left"}},this.renderItems()):null}}],function(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,b(r.key),r)}}(n.prototype,e),Object.defineProperty(n,"prototype",{writable:!1}),n}(r.PureComponent);m(g,"displayName","Legend"),m(g,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var x=n(46729),O=n(78262);function w(t){return(w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var j=["ref"];function S(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function P(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?S(Object(n),!0).forEach(function(e){_(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):S(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function A(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,T(r.key),r)}}function E(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(E=function(){return!!t})()}function k(t){return(k=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function M(t,e){return(M=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function _(t,e,n){return(e=T(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function T(t){var e=function(t,e){if("object"!=w(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=w(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==w(e)?e:e+""}function C(t){return t.value}var I=function(t){var e,n;function o(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,o);for(var t,e,n,r=arguments.length,i=Array(r),a=0;a<r;a++)i[a]=arguments[a];return e=o,n=[].concat(i),e=k(e),_(t=function(t,e){if(e&&("object"===w(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,E()?Reflect.construct(e,n||[],k(this).constructor):e.apply(this,n)),"lastBoundingBox",{width:-1,height:-1}),t}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&M(t,e)}(o,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?P({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,n,r=this.props,o=r.layout,i=r.align,a=r.verticalAlign,u=r.margin,c=r.chartWidth,l=r.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((c||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:u&&u.right||0}:{left:u&&u.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(n="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:u&&u.bottom||0}:{top:u&&u.top||0}),P(P({},e),n)}},{key:"render",value:function(){var t=this,e=this.props,n=e.content,o=e.width,i=e.height,a=e.wrapperStyle,u=e.payloadUniqBy,c=e.payload,l=P(P({position:"absolute",width:o||"auto",height:i||"auto"},this.getDefaultPosition(a)),a);return r.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(r.isValidElement(t))return r.cloneElement(t,e);if("function"==typeof t)return r.createElement(t,e);e.ref;var n=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(e,j);return r.createElement(g,n)}(n,P(P({},this.props),{},{payload:(0,O.z)(c,u,C)})))}}],n=[{key:"getWithHeight",value:function(t,e){var n=P(P({},this.defaultProps),t.props).layout;return"vertical"===n&&(0,x.hj)(t.props.height)?{height:t.props.height}:"horizontal"===n?{width:t.props.width||e}:null}}],e&&A(o.prototype,e),n&&A(o,n),Object.defineProperty(o,"prototype",{writable:!1}),o}(r.PureComponent);_(I,"displayName","Legend"),_(I,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"})},99202:function(t,e,n){"use strict";n.d(e,{h:function(){return d}});var r=n(89824),o=n(32486),i=n(3787),a=n.n(i),u=n(46729),c=n(25197),l=n(21105);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function p(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?f(Object(n),!0).forEach(function(e){var r,o;r=e,o=n[e],(r=function(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=s(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}(r))in t?Object.defineProperty(t,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[r]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var d=(0,o.forwardRef)(function(t,e){var n,i=t.aspect,s=t.initialDimension,f=void 0===s?{width:-1,height:-1}:s,d=t.width,y=void 0===d?"100%":d,v=t.height,m=void 0===v?"100%":v,b=t.minWidth,g=void 0===b?0:b,x=t.minHeight,O=t.maxHeight,w=t.children,j=t.debounce,S=void 0===j?0:j,P=t.id,A=t.className,E=t.onResize,k=t.style,M=(0,o.useRef)(null),_=(0,o.useRef)();_.current=E,(0,o.useImperativeHandle)(e,function(){return Object.defineProperty(M.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),M.current},configurable:!0})});var T=function(t){if(Array.isArray(t))return t}(n=(0,o.useState)({containerWidth:f.width,containerHeight:f.height}))||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{for(i=(n=n.call(t)).next;!(c=(r=i.call(n)).done)&&(u.push(r.value),2!==u.length);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(n,2)||function(t,e){if(t){if("string"==typeof t)return h(t,2);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return h(t,2)}}(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),C=T[0],I=T[1],D=(0,o.useCallback)(function(t,e){I(function(n){var r=Math.round(t),o=Math.round(e);return n.containerWidth===r&&n.containerHeight===o?n:{containerWidth:r,containerHeight:o}})},[]);(0,o.useEffect)(function(){var t=function(t){var e,n=t[0].contentRect,r=n.width,o=n.height;D(r,o),null===(e=_.current)||void 0===e||e.call(_,r,o)};S>0&&(t=a()(t,S,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),n=M.current.getBoundingClientRect();return D(n.width,n.height),e.observe(M.current),function(){e.disconnect()}},[D,S]);var N=(0,o.useMemo)(function(){var t=C.containerWidth,e=C.containerHeight;if(t<0||e<0)return null;(0,c.Z)((0,u.hU)(y)||(0,u.hU)(m),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",y,m),(0,c.Z)(!i||i>0,"The aspect(%s) must be greater than zero.",i);var n=(0,u.hU)(y)?t:y,r=(0,u.hU)(m)?e:m;i&&i>0&&(n?r=n/i:r&&(n=r*i),O&&r>O&&(r=O)),(0,c.Z)(n>0||r>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,r,y,m,g,x,i);var a=!Array.isArray(w)&&(0,l.Gf)(w.type).endsWith("Chart");return o.Children.map(w,function(t){return o.isValidElement(t)?(0,o.cloneElement)(t,p({width:n,height:r},a?{style:p({height:"100%",width:"100%",maxHeight:r,maxWidth:n},t.props.style)}:{})):t})},[i,w,m,O,x,g,C,y]);return o.createElement("div",{id:P?"".concat(P):void 0,className:(0,r.Z)("recharts-responsive-container",A),style:p(p({},void 0===k?{}:k),{},{width:y,height:m,minWidth:g,minHeight:x,maxHeight:O}),ref:M},N)})},40082:function(t,e,n){"use strict";n.d(e,{x:function(){return B}});var r=n(32486),o=n(86197),i=n.n(o),a=n(89824),u=n(46729),c=n(43160),l=n(21105),s=n(91568);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return h(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return h(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function d(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=f(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}(r.key),r)}}var y=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,v=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,m=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,b=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,g={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},x=Object.keys(g),O=function(){var t,e;function n(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||m.test(e)||(this.num=NaN,this.unit=""),x.includes(e)&&(this.num=t*g[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new n(NaN,""):new n(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new n(NaN,""):new n(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new n(NaN,""):new n(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new n(NaN,""):new n(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,r=p(null!==(e=b.exec(t))&&void 0!==e?e:[],3),o=r[1],i=r[2];return new n(parseFloat(o),null!=i?i:"")}}],t&&d(n.prototype,t),e&&d(n,e),Object.defineProperty(n,"prototype",{writable:!1}),n}();function w(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var n,r=p(null!==(n=y.exec(e))&&void 0!==n?n:[],4),o=r[1],i=r[2],a=r[3],u=O.parse(null!=o?o:""),c=O.parse(null!=a?a:""),l="*"===i?u.multiply(c):u.divide(c);if(l.isNaN())return"NaN";e=e.replace(y,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=p(null!==(s=v.exec(e))&&void 0!==s?s:[],4),h=f[1],d=f[2],m=f[3],b=O.parse(null!=h?h:""),g=O.parse(null!=m?m:""),x="+"===d?b.add(g):b.subtract(g);if(x.isNaN())return"NaN";e=e.replace(v,x.toString())}return e}var j=/\(([^()]*)\)/;function S(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var n=p(j.exec(e),2)[1];e=e.replace(j,w(n))}return e}(e),e=w(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var P=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],A=["dx","dy","angle","className","breakAll"];function E(){return(E=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function k(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function M(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(u.push(r.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return _(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var T=/[ \f\n\r\t\v\u2028\u2029]+/,C=function(t){var e=t.children,n=t.breakAll,r=t.style;try{var o=[];i()(e)||(o=n?e.toString().split(""):e.toString().split(T));var a=o.map(function(t){return{word:t,width:(0,s.xE)(t,r).width}}),u=n?0:(0,s.xE)("\xa0",r).width;return{wordsWithComputedWidth:a,spaceWidth:u}}catch(t){return null}},I=function(t,e,n,r,o){var i,a=t.maxLines,c=t.children,l=t.style,s=t.breakAll,f=(0,u.hj)(a),p=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var i=e.word,a=e.width,u=t[t.length-1];return u&&(null==r||o||u.width+a+n<Number(r))?(u.words.push(i),u.width+=a+n):t.push({words:[i],width:a}),t},[])},h=p(e);if(!f)return h;for(var d=function(t){var e=p(C({breakAll:s,style:l,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(r),e]},y=0,v=c.length-1,m=0;y<=v&&m<=c.length-1;){var b=Math.floor((y+v)/2),g=M(d(b-1),2),x=g[0],O=g[1],w=M(d(b),1)[0];if(x||w||(y=b+1),x&&w&&(v=b-1),!x&&w){i=O;break}m++}return i||h},D=function(t){return[{words:i()(t)?[]:t.toString().split(T)}]},N=function(t){var e=t.width,n=t.scaleToFit,r=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||n)&&!c.x.isSsr){var u=C({breakAll:i,children:r,style:o});return u?I({breakAll:i,children:r,maxLines:a,style:o},u.wordsWithComputedWidth,u.spaceWidth,e,n):D(r)}return D(r)},L="#808080",B=function(t){var e,n=t.x,o=void 0===n?0:n,i=t.y,c=void 0===i?0:i,s=t.lineHeight,f=void 0===s?"1em":s,p=t.capHeight,h=void 0===p?"0.71em":p,d=t.scaleToFit,y=void 0!==d&&d,v=t.textAnchor,m=t.verticalAnchor,b=t.fill,g=void 0===b?L:b,x=k(t,P),O=(0,r.useMemo)(function(){return N({breakAll:x.breakAll,children:x.children,maxLines:x.maxLines,scaleToFit:y,style:x.style,width:x.width})},[x.breakAll,x.children,x.maxLines,y,x.style,x.width]),w=x.dx,j=x.dy,M=x.angle,_=x.className,T=x.breakAll,C=k(x,A);if(!(0,u.P2)(o)||!(0,u.P2)(c))return null;var I=o+((0,u.hj)(w)?w:0),D=c+((0,u.hj)(j)?j:0);switch(void 0===m?"end":m){case"start":e=S("calc(".concat(h,")"));break;case"middle":e=S("calc(".concat((O.length-1)/2," * -").concat(f," + (").concat(h," / 2))"));break;default:e=S("calc(".concat(O.length-1," * -").concat(f,")"))}var B=[];if(y){var R=O[0].width,z=x.width;B.push("scale(".concat(((0,u.hj)(z)?z/R:1)/R,")"))}return M&&B.push("rotate(".concat(M,", ").concat(I,", ").concat(D,")")),B.length&&(C.transform=B.join(" ")),r.createElement("text",E({},(0,l.L6)(C,!0),{x:I,y:D,className:(0,a.Z)("recharts-text",_),textAnchor:void 0===v?"start":v,fill:g.includes("url")?L:g}),O.map(function(t,n){var o=t.words.join(T?"":" ");return r.createElement("tspan",{x:I,dy:0===n?e:f,key:"".concat(o,"-").concat(n)},o)}))}},14166:function(t,e,n){"use strict";n.d(e,{m:function(){return c}});var r=n(32486),o=n(89824),i=n(21105),a=["children","className"];function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var c=r.forwardRef(function(t,e){var n=t.children,c=t.className,l=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,a),s=(0,o.Z)("recharts-layer",c);return r.createElement("g",u({className:s},(0,i.L6)(l,!0),{ref:e}),n)})},81581:function(t,e,n){"use strict";n.d(e,{T:function(){return c}});var r=n(32486),o=n(89824),i=n(21105),a=["children","width","height","viewBox","className","style","title","desc"];function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function c(t){var e=t.children,n=t.width,c=t.height,l=t.viewBox,s=t.className,f=t.style,p=t.title,h=t.desc,d=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,a),y=l||{width:n,height:c,x:0,y:0},v=(0,o.Z)("recharts-surface",s);return r.createElement("svg",u({},(0,i.L6)(d,!0,"svg"),{className:v,width:n,height:c,style:f,viewBox:"".concat(y.x," ").concat(y.y," ").concat(y.width," ").concat(y.height)}),r.createElement("title",null,p),r.createElement("desc",null,h),e)}},79375:function(t,e,n){"use strict";n.d(e,{b:function(){return K}});var r=n(32486),o=n(92737),i=n(82404),a=n.n(i),u=n(25010),c=n.n(u),l=n(86197),s=n.n(l),f=n(14119),p=n.n(f),h=n(89824),d=n(14166),y=n(55973),v=n(40082),m=n(46750),b=n(5635),g=n.n(b),x=n(3249),O=n.n(x),w=n(21105),j=n(74499);function S(t){return(S="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var P=["valueAccessor"],A=["data","dataKey","clockWise","id","textBreakAll"];function E(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function k(){return(k=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function M(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function _(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?M(Object(n),!0).forEach(function(e){var r,o;r=e,o=n[e],(r=function(t){var e=function(t,e){if("object"!=S(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=S(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==S(e)?e:e+""}(r))in t?Object.defineProperty(t,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[r]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):M(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function T(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}var C=function(t){return Array.isArray(t.value)?O()(t.value):t.value};function I(t){var e=t.valueAccessor,n=void 0===e?C:e,o=T(t,P),i=o.data,a=o.dataKey,u=o.clockWise,c=o.id,l=o.textBreakAll,f=T(o,A);return i&&i.length?r.createElement(d.m,{className:"recharts-label-list"},i.map(function(t,e){var o=s()(a)?n(t,e):(0,j.F$)(t&&t.payload,a),i=s()(c)?{}:{id:"".concat(c,"-").concat(e)};return r.createElement(m._,k({},(0,w.L6)(t,!0),f,i,{parentViewBox:t.parentViewBox,value:o,textBreakAll:l,viewBox:m._.parseViewBox(s()(u)?t:_(_({},t),{},{clockWise:u})),key:"label-".concat(e),index:e}))})):null}I.displayName="LabelList",I.renderCallByParent=function(t,e){var n,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&o&&!t.label)return null;var i=t.children,a=(0,w.NN)(i,I).map(function(t,n){return(0,r.cloneElement)(t,{data:e,key:"labelList-".concat(n)})});return o?[(n=t.label)?!0===n?r.createElement(I,{key:"labelList-implicit",data:e}):r.isValidElement(n)||p()(n)?r.createElement(I,{key:"labelList-implicit",data:e,content:n}):g()(n)?r.createElement(I,k({data:e},n,{key:"labelList-implicit"})):null:null].concat(function(t){if(Array.isArray(t))return E(t)}(a)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(a)||function(t,e){if(t){if("string"==typeof t)return E(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return E(t,void 0)}}(a)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):a};var D=n(85150),N=n(43160),L=n(73845),B=n(46729),R=n(25197),z=n(95273),U=n(27232);function $(t){return($="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function F(){return(F=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function Z(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function q(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Z(Object(n),!0).forEach(function(e){V(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Z(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function W(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,G(r.key),r)}}function Y(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Y=function(){return!!t})()}function H(t){return(H=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function X(t,e){return(X=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function V(t,e,n){return(e=G(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function G(t){var e=function(t,e){if("object"!=$(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=$(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==$(e)?e:e+""}var K=function(t){var e,n;function i(t){var e,n,r;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,i),n=i,r=[t],n=H(n),V(e=function(t,e){if(e&&("object"===$(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,Y()?Reflect.construct(n,r||[],H(this).constructor):n.apply(this,r)),"pieRef",null),V(e,"sectorRefs",[]),V(e,"id",(0,B.EL)("recharts-pie-")),V(e,"handleAnimationEnd",function(){var t=e.props.onAnimationEnd;e.setState({isAnimationFinished:!0}),p()(t)&&t()}),V(e,"handleAnimationStart",function(){var t=e.props.onAnimationStart;e.setState({isAnimationFinished:!1}),p()(t)&&t()}),e.state={isAnimationFinished:!t.isAnimationActive,prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,sectorToFocus:0},e}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&X(t,e)}(i,t),e=[{key:"isActiveIndex",value:function(t){var e=this.props.activeIndex;return Array.isArray(e)?-1!==e.indexOf(t):t===e}},{key:"hasActiveIndex",value:function(){var t=this.props.activeIndex;return Array.isArray(t)?0!==t.length:t||0===t}},{key:"renderLabels",value:function(t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var e=this.props,n=e.label,o=e.labelLine,a=e.dataKey,u=e.valueKey,c=(0,w.L6)(this.props,!1),l=(0,w.L6)(n,!1),f=(0,w.L6)(o,!1),p=n&&n.offsetRadius||20,h=t.map(function(t,e){var h=(t.startAngle+t.endAngle)/2,y=(0,L.op)(t.cx,t.cy,t.outerRadius+p,h),v=q(q(q(q({},c),t),{},{stroke:"none"},l),{},{index:e,textAnchor:i.getTextAnchor(y.x,t.cx)},y),m=q(q(q(q({},c),t),{},{fill:"none",stroke:t.fill},f),{},{index:e,points:[(0,L.op)(t.cx,t.cy,t.outerRadius,h),y]}),b=a;return s()(a)&&s()(u)?b="value":s()(a)&&(b=u),r.createElement(d.m,{key:"label-".concat(t.startAngle,"-").concat(t.endAngle,"-").concat(t.midAngle,"-").concat(e)},o&&i.renderLabelLineItem(o,m,"line"),i.renderLabelItem(n,v,(0,j.F$)(t,b)))});return r.createElement(d.m,{className:"recharts-pie-labels"},h)}},{key:"renderSectorsStatically",value:function(t){var e=this,n=this.props,o=n.activeShape,i=n.blendStroke,a=n.inactiveShape;return t.map(function(n,u){if((null==n?void 0:n.startAngle)===0&&(null==n?void 0:n.endAngle)===0&&1!==t.length)return null;var c=e.isActiveIndex(u),l=a&&e.hasActiveIndex()?a:null,s=q(q({},n),{},{stroke:i?n.fill:n.stroke,tabIndex:-1});return r.createElement(d.m,F({ref:function(t){t&&!e.sectorRefs.includes(t)&&e.sectorRefs.push(t)},tabIndex:-1,className:"recharts-pie-sector"},(0,z.bw)(e.props,n,u),{key:"sector-".concat(null==n?void 0:n.startAngle,"-").concat(null==n?void 0:n.endAngle,"-").concat(n.midAngle,"-").concat(u)}),r.createElement(U.bn,F({option:c?o:l,isActive:c,shapeType:"sector"},s)))})}},{key:"renderSectorsWithAnimation",value:function(){var t=this,e=this.props,n=e.sectors,i=e.isAnimationActive,u=e.animationBegin,c=e.animationDuration,l=e.animationEasing,s=e.animationId,f=this.state,p=f.prevSectors,h=f.prevIsAnimationActive;return r.createElement(o.ZP,{begin:u,duration:c,isActive:i,easing:l,from:{t:0},to:{t:1},key:"pie-".concat(s,"-").concat(h),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(e){var o=e.t,i=[],u=(n&&n[0]).startAngle;return n.forEach(function(t,e){var n=p&&p[e],r=e>0?a()(t,"paddingAngle",0):0;if(n){var c=(0,B.k4)(n.endAngle-n.startAngle,t.endAngle-t.startAngle),l=q(q({},t),{},{startAngle:u+r,endAngle:u+c(o)+r});i.push(l),u=l.endAngle}else{var s=t.endAngle,f=t.startAngle,h=(0,B.k4)(0,s-f)(o),d=q(q({},t),{},{startAngle:u+r,endAngle:u+h+r});i.push(d),u=d.endAngle}}),r.createElement(d.m,null,t.renderSectorsStatically(i))})}},{key:"attachKeyboardHandlers",value:function(t){var e=this;t.onkeydown=function(t){if(!t.altKey)switch(t.key){case"ArrowLeft":var n=++e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[n].focus(),e.setState({sectorToFocus:n});break;case"ArrowRight":var r=--e.state.sectorToFocus<0?e.sectorRefs.length-1:e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[r].focus(),e.setState({sectorToFocus:r});break;case"Escape":e.sectorRefs[e.state.sectorToFocus].blur(),e.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var t=this.props,e=t.sectors,n=t.isAnimationActive,r=this.state.prevSectors;return n&&e&&e.length&&(!r||!c()(r,e))?this.renderSectorsWithAnimation():this.renderSectorsStatically(e)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var t=this,e=this.props,n=e.hide,o=e.sectors,i=e.className,a=e.label,u=e.cx,c=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.isAnimationActive,p=this.state.isAnimationFinished;if(n||!o||!o.length||!(0,B.hj)(u)||!(0,B.hj)(c)||!(0,B.hj)(l)||!(0,B.hj)(s))return null;var y=(0,h.Z)("recharts-pie",i);return r.createElement(d.m,{tabIndex:this.props.rootTabIndex,className:y,ref:function(e){t.pieRef=e}},this.renderSectors(),a&&this.renderLabels(o),m._.renderCallByParent(this.props,null,!1),(!f||p)&&I.renderCallByParent(this.props,o,!1))}}],n=[{key:"getDerivedStateFromProps",value:function(t,e){return e.prevIsAnimationActive!==t.isAnimationActive?{prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:[],isAnimationFinished:!0}:t.isAnimationActive&&t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:e.curSectors,isAnimationFinished:!0}:t.sectors!==e.curSectors?{curSectors:t.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(t,e){return t>e?"start":t<e?"end":"middle"}},{key:"renderLabelLineItem",value:function(t,e,n){if(r.isValidElement(t))return r.cloneElement(t,e);if(p()(t))return t(e);var o=(0,h.Z)("recharts-pie-label-line","boolean"!=typeof t?t.className:"");return r.createElement(y.H,F({},e,{key:n,type:"linear",className:o}))}},{key:"renderLabelItem",value:function(t,e,n){if(r.isValidElement(t))return r.cloneElement(t,e);var o=n;if(p()(t)&&(o=t(e),r.isValidElement(o)))return o;var i=(0,h.Z)("recharts-pie-label-text","boolean"==typeof t||p()(t)?"":t.className);return r.createElement(v.x,F({},e,{alignmentBaseline:"middle",className:i}),o)}}],e&&W(i.prototype,e),n&&W(i,n),Object.defineProperty(i,"prototype",{writable:!1}),i}(r.PureComponent);V(K,"displayName","Pie"),V(K,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!N.x.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),V(K,"parseDeltaAngle",function(t,e){return(0,B.uY)(e-t)*Math.min(Math.abs(e-t),360)}),V(K,"getRealPieData",function(t){var e=t.data,n=t.children,r=(0,w.L6)(t,!1),o=(0,w.NN)(n,D.b);return e&&e.length?e.map(function(t,e){return q(q(q({payload:t},r),t),o&&o[e]&&o[e].props)}):o&&o.length?o.map(function(t){return q(q({},r),t.props)}):[]}),V(K,"parseCoordinateOfPie",function(t,e){var n=e.top,r=e.left,o=e.width,i=e.height,a=(0,L.$4)(o,i);return{cx:r+(0,B.h1)(t.cx,o,o/2),cy:n+(0,B.h1)(t.cy,i,i/2),innerRadius:(0,B.h1)(t.innerRadius,a,0),outerRadius:(0,B.h1)(t.outerRadius,a,.8*a),maxRadius:t.maxRadius||Math.sqrt(o*o+i*i)/2}}),V(K,"getComposedData",function(t){var e,n,r=t.item,o=t.offset,i=void 0!==r.type.defaultProps?q(q({},r.type.defaultProps),r.props):r.props,a=K.getRealPieData(i);if(!a||!a.length)return null;var u=i.cornerRadius,c=i.startAngle,l=i.endAngle,f=i.paddingAngle,p=i.dataKey,h=i.nameKey,d=i.valueKey,y=i.tooltipType,v=Math.abs(i.minAngle),m=K.parseCoordinateOfPie(i,o),b=K.parseDeltaAngle(c,l),g=Math.abs(b),x=p;s()(p)&&s()(d)?((0,R.Z)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),x="value"):s()(p)&&((0,R.Z)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),x=d);var O=a.filter(function(t){return 0!==(0,j.F$)(t,x,0)}).length,w=g-O*v-(g>=360?O:O-1)*f,S=a.reduce(function(t,e){var n=(0,j.F$)(e,x,0);return t+((0,B.hj)(n)?n:0)},0);return S>0&&(e=a.map(function(t,e){var r,o=(0,j.F$)(t,x,0),i=(0,j.F$)(t,h,e),a=((0,B.hj)(o)?o:0)/S,l=(r=e?n.endAngle+(0,B.uY)(b)*f*(0!==o?1:0):c)+(0,B.uY)(b)*((0!==o?v:0)+a*w),s=(r+l)/2,p=(m.innerRadius+m.outerRadius)/2,d=[{name:i,value:o,payload:t,dataKey:x,type:y}],g=(0,L.op)(m.cx,m.cy,p,s);return n=q(q(q({percent:a,cornerRadius:u,name:i,tooltipPayload:d,midAngle:s,middleRadius:p,tooltipPosition:g},t),m),{},{value:(0,j.F$)(t,x),startAngle:r,endAngle:l,payload:t,paddingAngle:(0,B.uY)(b)*f})})),q(q({},m),{},{sectors:e,data:a})})},55973:function(t,e,n){"use strict";n.d(e,{H:function(){return H}});var r=n(32486);function o(){}function i(t,e,n){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+n)/6)}function a(t){this._context=t}function u(t){this._context=t}function c(t){this._context=t}a.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:i(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:i(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},u.prototype={areaStart:o,areaEnd:o,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:i(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},c.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var n=(this._x0+4*this._x1+t)/6,r=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(n,r):this._context.moveTo(n,r);break;case 3:this._point=4;default:i(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class l{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function s(t){this._context=t}function f(t){this._context=t}function p(t){return new f(t)}function h(t,e,n){var r=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(r||o<0&&-0),a=(n-t._y1)/(o||r<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*r)/(r+o)))||0}function d(t,e){var n=t._x1-t._x0;return n?(3*(t._y1-t._y0)/n-e)/2:e}function y(t,e,n){var r=t._x0,o=t._y0,i=t._x1,a=t._y1,u=(i-r)/3;t._context.bezierCurveTo(r+u,o+u*e,i-u,a-u*n,i,a)}function v(t){this._context=t}function m(t){this._context=new b(t)}function b(t){this._context=t}function g(t){this._context=t}function x(t){var e,n,r=t.length-1,o=Array(r),i=Array(r),a=Array(r);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<r-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[r-1]=2,i[r-1]=7,a[r-1]=8*t[r-1]+t[r],e=1;e<r;++e)n=o[e]/i[e-1],i[e]-=n,a[e]-=n*a[e-1];for(o[r-1]=a[r-1]/i[r-1],e=r-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(e=0,i[r-1]=(t[r]+o[r-1])/2;e<r-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function O(t,e){this._context=t,this._t=e}s.prototype={areaStart:o,areaEnd:o,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t=+t,e=+e,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}},f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},v.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:y(this,this._t0,d(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var n=NaN;if(e=+e,(t=+t)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,y(this,d(this,n=h(this,t,e)),n);break;default:y(this,this._t0,n=h(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=n}}},(m.prototype=Object.create(v.prototype)).point=function(t,e){v.prototype.point.call(this,e,t)},b.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,n,r,o,i){this._context.bezierCurveTo(e,t,r,n,i,o)}},g.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,n=t.length;if(n){if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===n)this._context.lineTo(t[1],e[1]);else for(var r=x(t),o=x(e),i=0,a=1;a<n;++i,++a)this._context.bezierCurveTo(r[0][i],o[0][i],r[1][i],o[1][i],t[a],e[a])}(this._line||0!==this._line&&1===n)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},O.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var n=this._x*(1-this._t)+t*this._t;this._context.lineTo(n,this._y),this._context.lineTo(n,e)}}this._x=t,this._y=e}};var w=n(42562),j=n(37226),S=n(55059);function P(t){return t[0]}function A(t){return t[1]}function E(t,e){var n=(0,j.Z)(!0),r=null,o=p,i=null,a=(0,S.d)(u);function u(u){var c,l,s,f=(u=(0,w.Z)(u)).length,p=!1;for(null==r&&(i=o(s=a())),c=0;c<=f;++c)!(c<f&&n(l=u[c],c,u))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(l,c,u),+e(l,c,u));if(s)return i=null,s+""||null}return t="function"==typeof t?t:void 0===t?P:(0,j.Z)(t),e="function"==typeof e?e:void 0===e?A:(0,j.Z)(e),u.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.Z)(+e),u):t},u.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.Z)(+t),u):e},u.defined=function(t){return arguments.length?(n="function"==typeof t?t:(0,j.Z)(!!t),u):n},u.curve=function(t){return arguments.length?(o=t,null!=r&&(i=o(r)),u):o},u.context=function(t){return arguments.length?(null==t?r=i=null:i=o(r=t),u):r},u}function k(t,e,n){var r=null,o=(0,j.Z)(!0),i=null,a=p,u=null,c=(0,S.d)(l);function l(l){var s,f,p,h,d,y=(l=(0,w.Z)(l)).length,v=!1,m=Array(y),b=Array(y);for(null==i&&(u=a(d=c())),s=0;s<=y;++s){if(!(s<y&&o(h=l[s],s,l))===v){if(v=!v)f=s,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),p=s-1;p>=f;--p)u.point(m[p],b[p]);u.lineEnd(),u.areaEnd()}}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),u.point(r?+r(h,s,l):m[s],n?+n(h,s,l):b[s]))}if(d)return u=null,d+""||null}function s(){return E().defined(o).curve(a).context(i)}return t="function"==typeof t?t:void 0===t?P:(0,j.Z)(+t),e="function"==typeof e?e:void 0===e?(0,j.Z)(0):(0,j.Z)(+e),n="function"==typeof n?n:void 0===n?A:(0,j.Z)(+n),l.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.Z)(+e),r=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.Z)(+e),l):t},l.x1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:(0,j.Z)(+t),l):r},l.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.Z)(+t),n=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.Z)(+t),l):e},l.y1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:(0,j.Z)(+t),l):n},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(n)},l.lineX1=function(){return s().x(r).y(e)},l.defined=function(t){return arguments.length?(o="function"==typeof t?t:(0,j.Z)(!!t),l):o},l.curve=function(t){return arguments.length?(a=t,null!=i&&(u=a(i)),l):a},l.context=function(t){return arguments.length?(null==t?i=u=null:u=a(i=t),l):i},l}var M=n(4501),_=n.n(M),T=n(14119),C=n.n(T),I=n(89824),D=n(95273),N=n(21105),L=n(46729);function B(t){return(B="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function R(){return(R=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function z(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function U(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?z(Object(n),!0).forEach(function(e){var r,o;r=e,o=n[e],(r=function(t){var e=function(t,e){if("object"!=B(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=B(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==B(e)?e:e+""}(r))in t?Object.defineProperty(t,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[r]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):z(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var $={curveBasisClosed:function(t){return new u(t)},curveBasisOpen:function(t){return new c(t)},curveBasis:function(t){return new a(t)},curveBumpX:function(t){return new l(t,!0)},curveBumpY:function(t){return new l(t,!1)},curveLinearClosed:function(t){return new s(t)},curveLinear:p,curveMonotoneX:function(t){return new v(t)},curveMonotoneY:function(t){return new m(t)},curveNatural:function(t){return new g(t)},curveStep:function(t){return new O(t,.5)},curveStepAfter:function(t){return new O(t,1)},curveStepBefore:function(t){return new O(t,0)}},F=function(t){return t.x===+t.x&&t.y===+t.y},Z=function(t){return t.x},q=function(t){return t.y},W=function(t,e){if(C()(t))return t;var n="curve".concat(_()(t));return("curveMonotone"===n||"curveBump"===n)&&e?$["".concat(n).concat("vertical"===e?"Y":"X")]:$[n]||p},Y=function(t){var e,n=t.type,r=t.points,o=void 0===r?[]:r,i=t.baseLine,a=t.layout,u=t.connectNulls,c=void 0!==u&&u,l=W(void 0===n?"linear":n,a),s=c?o.filter(function(t){return F(t)}):o;if(Array.isArray(i)){var f=c?i.filter(function(t){return F(t)}):i,p=s.map(function(t,e){return U(U({},t),{},{base:f[e]})});return(e="vertical"===a?k().y(q).x1(Z).x0(function(t){return t.base.x}):k().x(Z).y1(q).y0(function(t){return t.base.y})).defined(F).curve(l),e(p)}return(e="vertical"===a&&(0,L.hj)(i)?k().y(q).x1(Z).x0(i):(0,L.hj)(i)?k().x(Z).y1(q).y0(i):E().x(Z).y(q)).defined(F).curve(l),e(s)},H=function(t){var e=t.className,n=t.points,o=t.path,i=t.pathRef;if((!n||!n.length)&&!o)return null;var a=n&&n.length?Y(t):o;return r.createElement("path",R({},(0,N.L6)(t,!1),(0,D.Ym)(t),{className:(0,I.Z)("recharts-curve",e),d:a,ref:i}))}},22462:function(t,e,n){"use strict";n.d(e,{A:function(){return y},X:function(){return h}});var r=n(32486),o=n(89824),i=n(92737),a=n(21105);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function c(){return(c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function s(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function f(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?s(Object(n),!0).forEach(function(e){var r,o;r=e,o=n[e],(r=function(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=u(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}(r))in t?Object.defineProperty(t,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[r]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var p=function(t,e,n,r,o){var i,a=Math.min(Math.abs(n)/2,Math.abs(r)/2),u=r>=0?1:-1,c=n>=0?1:-1,l=r>=0&&n>=0||r<0&&n<0?1:0;if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+u*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+c*s[0],",").concat(e)),i+="L ".concat(t+n-c*s[1],",").concat(e),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+n,",").concat(e+u*s[1])),i+="L ".concat(t+n,",").concat(e+r-u*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+n-c*s[2],",").concat(e+r)),i+="L ".concat(t+c*s[3],",").concat(e+r),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+r-u*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+c*p,",").concat(e,"\n            L ").concat(t+n-c*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+n,",").concat(e+u*p,"\n            L ").concat(t+n,",").concat(e+r-u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+n-c*p,",").concat(e+r,"\n            L ").concat(t+c*p,",").concat(e+r,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+r-u*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(n," v ").concat(r," h ").concat(-n," Z");return i},h=function(t,e){if(!t||!e)return!1;var n=t.x,r=t.y,o=e.x,i=e.y,a=e.width,u=e.height;return!!(Math.abs(a)>0&&Math.abs(u)>0)&&n>=Math.min(o,o+a)&&n<=Math.max(o,o+a)&&r>=Math.min(i,i+u)&&r<=Math.max(i,i+u)},d={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},y=function(t){var e,n=f(f({},d),t),u=(0,r.useRef)(),s=function(t){if(Array.isArray(t))return t}(e=(0,r.useState)(-1))||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{for(i=(n=n.call(t)).next;!(c=(r=i.call(n)).done)&&(u.push(r.value),2!==u.length);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,2)||function(t,e){if(t){if("string"==typeof t)return l(t,2);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return l(t,2)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),h=s[0],y=s[1];(0,r.useEffect)(function(){if(u.current&&u.current.getTotalLength)try{var t=u.current.getTotalLength();t&&y(t)}catch(t){}},[]);var v=n.x,m=n.y,b=n.width,g=n.height,x=n.radius,O=n.className,w=n.animationEasing,j=n.animationDuration,S=n.animationBegin,P=n.isAnimationActive,A=n.isUpdateAnimationActive;if(v!==+v||m!==+m||b!==+b||g!==+g||0===b||0===g)return null;var E=(0,o.Z)("recharts-rectangle",O);return A?r.createElement(i.ZP,{canBegin:h>0,from:{width:b,height:g,x:v,y:m},to:{width:b,height:g,x:v,y:m},duration:j,animationEasing:w,isActive:A},function(t){var e=t.width,o=t.height,l=t.x,s=t.y;return r.createElement(i.ZP,{canBegin:h>0,from:"0px ".concat(-1===h?1:h,"px"),to:"".concat(h,"px 0px"),attributeName:"strokeDasharray",begin:S,duration:j,isActive:P,easing:w},r.createElement("path",c({},(0,a.L6)(n,!0),{className:E,d:p(l,s,e,o,x),ref:u})))}):r.createElement("path",c({},(0,a.L6)(n,!0),{className:E,d:p(v,m,b,g,x)}))}},75300:function(t,e,n){"use strict";n.d(e,{L:function(){return v}});var r=n(32486),o=n(89824),i=n(21105),a=n(73845),u=n(46729);function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(){return(l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function s(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function f(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?s(Object(n),!0).forEach(function(e){var r,o;r=e,o=n[e],(r=function(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=c(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}(r))in t?Object.defineProperty(t,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[r]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var p=function(t){var e=t.cx,n=t.cy,r=t.radius,o=t.angle,i=t.sign,u=t.isExternal,c=t.cornerRadius,l=t.cornerIsExternal,s=c*(u?1:-1)+r,f=Math.asin(c/s)/a.Wk,p=l?o:o+i*f;return{center:(0,a.op)(e,n,s,p),circleTangency:(0,a.op)(e,n,r,p),lineTangency:(0,a.op)(e,n,s*Math.cos(f*a.Wk),l?o-i*f:o),theta:f}},h=function(t){var e,n=t.cx,r=t.cy,o=t.innerRadius,i=t.outerRadius,c=t.startAngle,l=(e=t.endAngle,(0,u.uY)(e-c)*Math.min(Math.abs(e-c),359.999)),s=c+l,f=(0,a.op)(n,r,i,c),p=(0,a.op)(n,r,i,s),h="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(c>s),",\n    ").concat(p.x,",").concat(p.y,"\n  ");if(o>0){var d=(0,a.op)(n,r,o,c),y=(0,a.op)(n,r,o,s);h+="L ".concat(y.x,",").concat(y.y,"\n            A ").concat(o,",").concat(o,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(c<=s),",\n            ").concat(d.x,",").concat(d.y," Z")}else h+="L ".concat(n,",").concat(r," Z");return h},d=function(t){var e=t.cx,n=t.cy,r=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,l=t.startAngle,s=t.endAngle,f=(0,u.uY)(s-l),d=p({cx:e,cy:n,radius:o,angle:l,sign:f,cornerRadius:i,cornerIsExternal:c}),y=d.circleTangency,v=d.lineTangency,m=d.theta,b=p({cx:e,cy:n,radius:o,angle:s,sign:-f,cornerRadius:i,cornerIsExternal:c}),g=b.circleTangency,x=b.lineTangency,O=b.theta,w=c?Math.abs(l-s):Math.abs(l-s)-m-O;if(w<0)return a?"M ".concat(v.x,",").concat(v.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):h({cx:e,cy:n,innerRadius:r,outerRadius:o,startAngle:l,endAngle:s});var j="M ".concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(y.x,",").concat(y.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(w>180),",").concat(+(f<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(x.x,",").concat(x.y,"\n  ");if(r>0){var S=p({cx:e,cy:n,radius:r,angle:l,sign:f,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),P=S.circleTangency,A=S.lineTangency,E=S.theta,k=p({cx:e,cy:n,radius:r,angle:s,sign:-f,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),M=k.circleTangency,_=k.lineTangency,T=k.theta,C=c?Math.abs(l-s):Math.abs(l-s)-E-T;if(C<0&&0===i)return"".concat(j,"L").concat(e,",").concat(n,"Z");j+="L".concat(_.x,",").concat(_.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(M.x,",").concat(M.y,"\n      A").concat(r,",").concat(r,",0,").concat(+(C>180),",").concat(+(f>0),",").concat(P.x,",").concat(P.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(A.x,",").concat(A.y,"Z")}else j+="L".concat(e,",").concat(n,"Z");return j},y={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},v=function(t){var e,n=f(f({},y),t),a=n.cx,c=n.cy,s=n.innerRadius,p=n.outerRadius,v=n.cornerRadius,m=n.forceCornerRadius,b=n.cornerIsExternal,g=n.startAngle,x=n.endAngle,O=n.className;if(p<s||g===x)return null;var w=(0,o.Z)("recharts-sector",O),j=p-s,S=(0,u.h1)(v,j,0,!0);return e=S>0&&360>Math.abs(g-x)?d({cx:a,cy:c,innerRadius:s,outerRadius:p,cornerRadius:Math.min(S,j/2),forceCornerRadius:m,cornerIsExternal:b,startAngle:g,endAngle:x}):h({cx:a,cy:c,innerRadius:s,outerRadius:p,startAngle:g,endAngle:x}),r.createElement("path",l({},(0,i.L6)(n,!0),{className:w,d:e,role:"img"}))}},78743:function(t,e,n){"use strict";n.d(e,{v:function(){return I}});var r=n(32486),o=n(4501),i=n.n(o);let a=Math.cos,u=Math.sin,c=Math.sqrt,l=Math.PI,s=2*l;var f={draw(t,e){let n=c(e/l);t.moveTo(n,0),t.arc(0,0,n,0,s)}};let p=c(1/3),h=2*p,d=u(l/10)/u(7*l/10),y=u(s/10)*d,v=-a(s/10)*d,m=c(3),b=c(3)/2,g=1/c(12),x=(g/2+1)*3;var O=n(37226),w=n(55059);c(3),c(3);var j=n(89824),S=n(21105);function P(t){return(P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var A=["type","size","sizeType"];function E(){return(E=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function k(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function M(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?k(Object(n),!0).forEach(function(e){var r,o;r=e,o=n[e],(r=function(t){var e=function(t,e){if("object"!=P(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=P(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==P(e)?e:e+""}(r))in t?Object.defineProperty(t,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[r]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):k(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var _={symbolCircle:f,symbolCross:{draw(t,e){let n=c(e/5)/2;t.moveTo(-3*n,-n),t.lineTo(-n,-n),t.lineTo(-n,-3*n),t.lineTo(n,-3*n),t.lineTo(n,-n),t.lineTo(3*n,-n),t.lineTo(3*n,n),t.lineTo(n,n),t.lineTo(n,3*n),t.lineTo(-n,3*n),t.lineTo(-n,n),t.lineTo(-3*n,n),t.closePath()}},symbolDiamond:{draw(t,e){let n=c(e/h),r=n*p;t.moveTo(0,-n),t.lineTo(r,0),t.lineTo(0,n),t.lineTo(-r,0),t.closePath()}},symbolSquare:{draw(t,e){let n=c(e),r=-n/2;t.rect(r,r,n,n)}},symbolStar:{draw(t,e){let n=c(.8908130915292852*e),r=y*n,o=v*n;t.moveTo(0,-n),t.lineTo(r,o);for(let e=1;e<5;++e){let i=s*e/5,c=a(i),l=u(i);t.lineTo(l*n,-c*n),t.lineTo(c*r-l*o,l*r+c*o)}t.closePath()}},symbolTriangle:{draw(t,e){let n=-c(e/(3*m));t.moveTo(0,2*n),t.lineTo(-m*n,-n),t.lineTo(m*n,-n),t.closePath()}},symbolWye:{draw(t,e){let n=c(e/x),r=n/2,o=n*g,i=n*g+n,a=-r;t.moveTo(r,o),t.lineTo(r,i),t.lineTo(a,i),t.lineTo(-.5*r-b*o,b*r+-.5*o),t.lineTo(-.5*r-b*i,b*r+-.5*i),t.lineTo(-.5*a-b*i,b*a+-.5*i),t.lineTo(-.5*r+b*o,-.5*o-b*r),t.lineTo(-.5*r+b*i,-.5*i-b*r),t.lineTo(-.5*a+b*i,-.5*i-b*a),t.closePath()}}},T=Math.PI/180,C=function(t,e,n){if("area"===e)return t;switch(n){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var r=18*T;return 1.25*t*t*(Math.tan(r)-Math.tan(2*r)*Math.pow(Math.tan(r),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},I=function(t){var e,n=t.type,o=void 0===n?"circle":n,a=t.size,u=void 0===a?64:a,c=t.sizeType,l=void 0===c?"area":c,s=M(M({},function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,A)),{},{type:o,size:u,sizeType:l}),p=s.className,h=s.cx,d=s.cy,y=(0,S.L6)(s,!0);return h===+h&&d===+d&&u===+u?r.createElement("path",E({},y,{className:(0,j.Z)("recharts-symbols",p),transform:"translate(".concat(h,", ").concat(d,")"),d:(e=_["symbol".concat(i()(o))]||f,(function(t,e){let n=null,r=(0,w.d)(o);function o(){let o;if(n||(n=o=r()),t.apply(this,arguments).draw(n,+e.apply(this,arguments)),o)return n=null,o+""||null}return t="function"==typeof t?t:(0,O.Z)(t||f),e="function"==typeof e?e:(0,O.Z)(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"==typeof e?e:(0,O.Z)(e),o):t},o.size=function(t){return arguments.length?(e="function"==typeof t?t:(0,O.Z)(+t),o):e},o.context=function(t){return arguments.length?(n=null==t?null:t,o):n},o})().type(e).size(C(u,l,o))())})):null};I.registerSymbol=function(t,e){_["symbol".concat(i()(t))]=e}},27232:function(t,e,n){"use strict";n.d(e,{bn:function(){return C},a3:function(){return z},lT:function(){return I},V$:function(){return D},w7:function(){return N}});var r=n(32486),o=n(14119),i=n.n(o),a=n(85758),u=n.n(a),c=n(96634),l=n.n(c),s=n(25010),f=n.n(s),p=n(22462),h=n(89824),d=n(92737),y=n(21105);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function m(){return(m=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function g(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function x(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?g(Object(n),!0).forEach(function(e){var r,o;r=e,o=n[e],(r=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=v(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(r))in t?Object.defineProperty(t,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[r]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var O=function(t,e,n,r,o){var i=n-r;return"M ".concat(t,",").concat(e)+"L ".concat(t+n,",").concat(e)+"L ".concat(t+n-i/2,",").concat(e+o)+"L ".concat(t+n-i/2-r,",").concat(e+o)+"L ".concat(t,",").concat(e," Z")},w={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},j=function(t){var e,n=x(x({},w),t),o=(0,r.useRef)(),i=function(t){if(Array.isArray(t))return t}(e=(0,r.useState)(-1))||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{for(i=(n=n.call(t)).next;!(c=(r=i.call(n)).done)&&(u.push(r.value),2!==u.length);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,2)||function(t,e){if(t){if("string"==typeof t)return b(t,2);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return b(t,2)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),a=i[0],u=i[1];(0,r.useEffect)(function(){if(o.current&&o.current.getTotalLength)try{var t=o.current.getTotalLength();t&&u(t)}catch(t){}},[]);var c=n.x,l=n.y,s=n.upperWidth,f=n.lowerWidth,p=n.height,v=n.className,g=n.animationEasing,j=n.animationDuration,S=n.animationBegin,P=n.isUpdateAnimationActive;if(c!==+c||l!==+l||s!==+s||f!==+f||p!==+p||0===s&&0===f||0===p)return null;var A=(0,h.Z)("recharts-trapezoid",v);return P?r.createElement(d.ZP,{canBegin:a>0,from:{upperWidth:0,lowerWidth:0,height:p,x:c,y:l},to:{upperWidth:s,lowerWidth:f,height:p,x:c,y:l},duration:j,animationEasing:g,isActive:P},function(t){var e=t.upperWidth,i=t.lowerWidth,u=t.height,c=t.x,l=t.y;return r.createElement(d.ZP,{canBegin:a>0,from:"0px ".concat(-1===a?1:a,"px"),to:"".concat(a,"px 0px"),attributeName:"strokeDasharray",begin:S,duration:j,easing:g},r.createElement("path",m({},(0,y.L6)(n,!0),{className:A,d:O(c,l,e,i,u),ref:o})))}):r.createElement("g",null,r.createElement("path",m({},(0,y.L6)(n,!0),{className:A,d:O(c,l,s,f,p)})))},S=n(75300),P=n(14166),A=n(78743),E=["option","shapeType","propTransformer","activeClassName","isActive"];function k(t){return(k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function M(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function _(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?M(Object(n),!0).forEach(function(e){var r,o;r=e,o=n[e],(r=function(t){var e=function(t,e){if("object"!=k(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=k(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==k(e)?e:e+""}(r))in t?Object.defineProperty(t,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[r]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):M(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function T(t){var e=t.shapeType,n=t.elementProps;switch(e){case"rectangle":return r.createElement(p.A,n);case"trapezoid":return r.createElement(j,n);case"sector":return r.createElement(S.L,n);case"symbols":if("symbols"===e)return r.createElement(A.v,n);break;default:return null}}function C(t){var e,n=t.option,o=t.shapeType,a=t.propTransformer,c=t.activeClassName,s=t.isActive,f=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,E);if((0,r.isValidElement)(n))e=(0,r.cloneElement)(n,_(_({},f),(0,r.isValidElement)(n)?n.props:n));else if(i()(n))e=n(f);else if(u()(n)&&!l()(n)){var p=(void 0===a?function(t,e){return _(_({},e),t)}:a)(n,f);e=r.createElement(T,{shapeType:o,elementProps:p})}else e=r.createElement(T,{shapeType:o,elementProps:f});return s?r.createElement(P.m,{className:void 0===c?"recharts-active-shape":c},e):e}function I(t,e){return null!=e&&"trapezoids"in t.props}function D(t,e){return null!=e&&"sectors"in t.props}function N(t,e){return null!=e&&"points"in t.props}function L(t,e){var n,r,o=t.x===(null==e||null===(n=e.labelViewBox)||void 0===n?void 0:n.x)||t.x===e.x,i=t.y===(null==e||null===(r=e.labelViewBox)||void 0===r?void 0:r.y)||t.y===e.y;return o&&i}function B(t,e){var n=t.endAngle===e.endAngle,r=t.startAngle===e.startAngle;return n&&r}function R(t,e){var n=t.x===e.x,r=t.y===e.y,o=t.z===e.z;return n&&r&&o}function z(t){var e,n,r,o=t.activeTooltipItem,i=t.graphicalItem,a=t.itemData,u=(I(i,o)?e="trapezoids":D(i,o)?e="sectors":N(i,o)&&(e="points"),e),c=I(i,o)?null===(n=o.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:D(i,o)?null===(r=o.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:N(i,o)?o.payload:{},l=a.filter(function(t,e){var n=f()(c,t),r=i.props[u].filter(function(t){var e;return(I(i,o)?e=L:D(i,o)?e=B:N(i,o)&&(e=R),e)(t,o)}),a=i.props[u].indexOf(r[r.length-1]);return n&&e===a});return a.indexOf(l[l.length-1])}},74499:function(t,e,n){"use strict";n.d(e,{By:function(){return oa},VO:function(){return on},zF:function(){return oy},DO:function(){return oh},zT:function(){return oP},qz:function(){return oi},pt:function(){return oo},gF:function(){return oe},s6:function(){return ol},EB:function(){return oO},fk:function(){return or},wh:function(){return ob},O3:function(){return ox},uY:function(){return of},g$:function(){return og},Qo:function(){return oE},F$:function(){return ot},NA:function(){return os},ko:function(){return oA},ZI:function(){return oc},Hq:function(){return od},LG:function(){return oS}});var r,o,i,a,u,c,l,s={};n.r(s),n.d(s,{scaleBand:function(){return f.Z},scaleDiverging:function(){return function t(){var e=tN(nJ()(tv));return e.copy=function(){return nV(e,t())},tj.O.apply(e,arguments)}},scaleDivergingLog:function(){return function t(){var e=tq(nJ()).domain([.1,1,10]);return e.copy=function(){return nV(e,t()).base(e.base())},tj.O.apply(e,arguments)}},scaleDivergingPow:function(){return nQ},scaleDivergingSqrt:function(){return n0},scaleDivergingSymlog:function(){return function t(){var e=tH(nJ());return e.copy=function(){return nV(e,t()).constant(e.constant())},tj.O.apply(e,arguments)}},scaleIdentity:function(){return function t(e){var n;function r(t){return null==t||isNaN(t=+t)?n:t}return r.invert=r,r.domain=r.range=function(t){return arguments.length?(e=Array.from(t,td),r):e.slice()},r.unknown=function(t){return arguments.length?(n=t,r):n},r.copy=function(){return t(e).unknown(n)},e=arguments.length?Array.from(e,td):[0,1],tN(r)}},scaleImplicit:function(){return tX.O},scaleLinear:function(){return tL},scaleLog:function(){return function t(){let e=tq(tO()).domain([1,10]);return e.copy=()=>tx(e,t()).base(e.base()),tj.o.apply(e,arguments),e}},scaleOrdinal:function(){return tX.Z},scalePoint:function(){return f.x},scalePow:function(){return tQ},scaleQuantile:function(){return function t(){var e,n=[],r=[],o=[];function i(){var t=0,e=Math.max(1,r.length);for(o=Array(e-1);++t<e;)o[t-1]=function(t,e,n=j){if(!(!(r=t.length)||isNaN(e=+e))){if(e<=0||r<2)return+n(t[0],0,t);if(e>=1)return+n(t[r-1],r-1,t);var r,o=(r-1)*e,i=Math.floor(o),a=+n(t[i],i,t);return a+(+n(t[i+1],i+1,t)-a)*(o-i)}}(n,t/e);return a}function a(t){return null==t||isNaN(t=+t)?e:r[P(o,t)]}return a.invertExtent=function(t){var e=r.indexOf(t);return e<0?[NaN,NaN]:[e>0?o[e-1]:n[0],e<o.length?o[e]:n[n.length-1]]},a.domain=function(t){if(!arguments.length)return n.slice();for(let e of(n=[],t))null==e||isNaN(e=+e)||n.push(e);return n.sort(g),i()},a.range=function(t){return arguments.length?(r=Array.from(t),i()):r.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return o.slice()},a.copy=function(){return t().domain(n).range(r).unknown(e)},tj.o.apply(a,arguments)}},scaleQuantize:function(){return function t(){var e,n=0,r=1,o=1,i=[.5],a=[0,1];function u(t){return null!=t&&t<=t?a[P(i,t,0,o)]:e}function c(){var t=-1;for(i=Array(o);++t<o;)i[t]=((t+1)*r-(t-o)*n)/(o+1);return u}return u.domain=function(t){return arguments.length?([n,r]=t,n=+n,r=+r,c()):[n,r]},u.range=function(t){return arguments.length?(o=(a=Array.from(t)).length-1,c()):a.slice()},u.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[n,i[0]]:e>=o?[i[o-1],r]:[i[e-1],i[e]]},u.unknown=function(t){return arguments.length&&(e=t),u},u.thresholds=function(){return i.slice()},u.copy=function(){return t().domain([n,r]).range(a).unknown(e)},tj.o.apply(tN(u),arguments)}},scaleRadial:function(){return function t(){var e,n=tw(),r=[0,1],o=!1;function i(t){var r,i=Math.sign(r=n(t))*Math.sqrt(Math.abs(r));return isNaN(i)?e:o?Math.round(i):i}return i.invert=function(t){return n.invert(t1(t))},i.domain=function(t){return arguments.length?(n.domain(t),i):n.domain()},i.range=function(t){return arguments.length?(n.range((r=Array.from(t,td)).map(t1)),i):r.slice()},i.rangeRound=function(t){return i.range(t).round(!0)},i.round=function(t){return arguments.length?(o=!!t,i):o},i.clamp=function(t){return arguments.length?(n.clamp(t),i):n.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t(n.domain(),r).round(o).clamp(n.clamp()).unknown(e)},tj.o.apply(i,arguments),tN(i)}},scaleSequential:function(){return function t(){var e=tN(nX()(tv));return e.copy=function(){return nV(e,t())},tj.O.apply(e,arguments)}},scaleSequentialLog:function(){return function t(){var e=tq(nX()).domain([1,10]);return e.copy=function(){return nV(e,t()).base(e.base())},tj.O.apply(e,arguments)}},scaleSequentialPow:function(){return nG},scaleSequentialQuantile:function(){return function t(){var e=[],n=tv;function r(t){if(null!=t&&!isNaN(t=+t))return n((P(e,t,1)-1)/(e.length-1))}return r.domain=function(t){if(!arguments.length)return e.slice();for(let n of(e=[],t))null==n||isNaN(n=+n)||e.push(n);return e.sort(g),r},r.interpolator=function(t){return arguments.length?(n=t,r):n},r.range=function(){return e.map((t,r)=>n(r/(e.length-1)))},r.quantiles=function(t){return Array.from({length:t+1},(n,r)=>(function(t,e,n){if(!(!(r=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e=+e)>=e&&(yield e);else{let n=-1;for(let r of t)null!=(r=e(r,++n,t))&&(r=+r)>=r&&(yield r)}}(t,void 0))).length)||isNaN(e=+e))){if(e<=0||r<2)return t6(t);if(e>=1)return t2(t);var r,o=(r-1)*e,i=Math.floor(o),a=t2((function t(e,n,r=0,o=1/0,i){if(n=Math.floor(n),r=Math.floor(Math.max(0,r)),o=Math.floor(Math.min(e.length-1,o)),!(r<=n&&n<=o))return e;for(i=void 0===i?t3:function(t=g){if(t===g)return t3;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,n)=>{let r=t(e,n);return r||0===r?r:(0===t(n,n))-(0===t(e,e))}}(i);o>r;){if(o-r>600){let a=o-r+1,u=n-r+1,c=Math.log(a),l=.5*Math.exp(2*c/3),s=.5*Math.sqrt(c*l*(a-l)/a)*(u-a/2<0?-1:1),f=Math.max(r,Math.floor(n-u*l/a+s)),p=Math.min(o,Math.floor(n+(a-u)*l/a+s));t(e,n,f,p,i)}let a=e[n],u=r,c=o;for(t5(e,r,n),i(e[o],a)>0&&t5(e,r,o);u<c;){for(t5(e,u,c),++u,--c;0>i(e[u],a);)++u;for(;i(e[c],a)>0;)--c}0===i(e[r],a)?t5(e,r,c):t5(e,++c,o),c<=n&&(r=c+1),n<=c&&(o=c-1)}return e})(t,i).subarray(0,i+1));return a+(t6(t.subarray(i+1))-a)*(o-i)}})(e,r/t))},r.copy=function(){return t(n).domain(e)},tj.O.apply(r,arguments)}},scaleSequentialSqrt:function(){return nK},scaleSequentialSymlog:function(){return function t(){var e=tH(nX());return e.copy=function(){return nV(e,t()).constant(e.constant())},tj.O.apply(e,arguments)}},scaleSqrt:function(){return t0},scaleSymlog:function(){return function t(){var e=tH(tO());return e.copy=function(){return tx(e,t()).constant(e.constant())},tj.o.apply(e,arguments)}},scaleThreshold:function(){return function t(){var e,n=[.5],r=[0,1],o=1;function i(t){return null!=t&&t<=t?r[P(n,t,0,o)]:e}return i.domain=function(t){return arguments.length?(o=Math.min((n=Array.from(t)).length,r.length-1),i):n.slice()},i.range=function(t){return arguments.length?(r=Array.from(t),o=Math.min(n.length,r.length-1),i):r.slice()},i.invertExtent=function(t){var e=r.indexOf(t);return[n[e-1],n[e]]},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t().domain(n).range(r).unknown(e)},tj.o.apply(i,arguments)}},scaleTime:function(){return nY},scaleUtc:function(){return nH},tickFormat:function(){return tD}});var f=n(69802);let p=Math.sqrt(50),h=Math.sqrt(10),d=Math.sqrt(2);function y(t,e,n){let r,o,i;let a=(e-t)/Math.max(0,n),u=Math.floor(Math.log10(a)),c=a/Math.pow(10,u),l=c>=p?10:c>=h?5:c>=d?2:1;return(u<0?(r=Math.round(t*(i=Math.pow(10,-u)/l)),o=Math.round(e*i),r/i<t&&++r,o/i>e&&--o,i=-i):(r=Math.round(t/(i=Math.pow(10,u)*l)),o=Math.round(e/i),r*i<t&&++r,o*i>e&&--o),o<r&&.5<=n&&n<2)?y(t,e,2*n):[r,o,i]}function v(t,e,n){if(e=+e,t=+t,!((n=+n)>0))return[];if(t===e)return[t];let r=e<t,[o,i,a]=r?y(e,t,n):y(t,e,n);if(!(i>=o))return[];let u=i-o+1,c=Array(u);if(r){if(a<0)for(let t=0;t<u;++t)c[t]=-((i-t)/a);else for(let t=0;t<u;++t)c[t]=(i-t)*a}else if(a<0)for(let t=0;t<u;++t)c[t]=-((o+t)/a);else for(let t=0;t<u;++t)c[t]=(o+t)*a;return c}function m(t,e,n){return y(t=+t,e=+e,n=+n)[2]}function b(t,e,n){e=+e,t=+t,n=+n;let r=e<t,o=r?m(e,t,n):m(t,e,n);return(r?-1:1)*(o<0?-(1/o):o)}function g(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function x(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function O(t){let e,n,r;function o(t,r,o=0,i=t.length){if(o<i){if(0!==e(r,r))return i;do{let e=o+i>>>1;0>n(t[e],r)?o=e+1:i=e}while(o<i)}return o}return 2!==t.length?(e=g,n=(e,n)=>g(t(e),n),r=(e,n)=>t(e)-n):(e=t===g||t===x?t:w,n=t,r=t),{left:o,center:function(t,e,n=0,i=t.length){let a=o(t,e,n,i-1);return a>n&&r(t[a-1],e)>-r(t[a],e)?a-1:a},right:function(t,r,o=0,i=t.length){if(o<i){if(0!==e(r,r))return i;do{let e=o+i>>>1;0>=n(t[e],r)?o=e+1:i=e}while(o<i)}return o}}}function w(){return 0}function j(t){return null===t?NaN:+t}let S=O(g),P=S.right;function A(t,e,n){t.prototype=e.prototype=n,n.constructor=t}function E(t,e){var n=Object.create(t.prototype);for(var r in e)n[r]=e[r];return n}function k(){}S.left,O(j).center;var M="\\s*([+-]?\\d+)\\s*",_="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",T="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",C=/^#([0-9a-f]{3,8})$/,I=RegExp(`^rgb\\(${M},${M},${M}\\)$`),D=RegExp(`^rgb\\(${T},${T},${T}\\)$`),N=RegExp(`^rgba\\(${M},${M},${M},${_}\\)$`),L=RegExp(`^rgba\\(${T},${T},${T},${_}\\)$`),B=RegExp(`^hsl\\(${_},${T},${T}\\)$`),R=RegExp(`^hsla\\(${_},${T},${T},${_}\\)$`),z={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function U(){return this.rgb().formatHex()}function $(){return this.rgb().formatRgb()}function F(t){var e,n;return t=(t+"").trim().toLowerCase(),(e=C.exec(t))?(n=e[1].length,e=parseInt(e[1],16),6===n?Z(e):3===n?new Y(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===n?q(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===n?q(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=I.exec(t))?new Y(e[1],e[2],e[3],1):(e=D.exec(t))?new Y(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=N.exec(t))?q(e[1],e[2],e[3],e[4]):(e=L.exec(t))?q(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=B.exec(t))?J(e[1],e[2]/100,e[3]/100,1):(e=R.exec(t))?J(e[1],e[2]/100,e[3]/100,e[4]):z.hasOwnProperty(t)?Z(z[t]):"transparent"===t?new Y(NaN,NaN,NaN,0):null}function Z(t){return new Y(t>>16&255,t>>8&255,255&t,1)}function q(t,e,n,r){return r<=0&&(t=e=n=NaN),new Y(t,e,n,r)}function W(t,e,n,r){var o;return 1==arguments.length?((o=t)instanceof k||(o=F(o)),o)?new Y((o=o.rgb()).r,o.g,o.b,o.opacity):new Y:new Y(t,e,n,null==r?1:r)}function Y(t,e,n,r){this.r=+t,this.g=+e,this.b=+n,this.opacity=+r}function H(){return`#${K(this.r)}${K(this.g)}${K(this.b)}`}function X(){let t=V(this.opacity);return`${1===t?"rgb(":"rgba("}${G(this.r)}, ${G(this.g)}, ${G(this.b)}${1===t?")":`, ${t})`}`}function V(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function G(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function K(t){return((t=G(t))<16?"0":"")+t.toString(16)}function J(t,e,n,r){return r<=0?t=e=n=NaN:n<=0||n>=1?t=e=NaN:e<=0&&(t=NaN),new tt(t,e,n,r)}function Q(t){if(t instanceof tt)return new tt(t.h,t.s,t.l,t.opacity);if(t instanceof k||(t=F(t)),!t)return new tt;if(t instanceof tt)return t;var e=(t=t.rgb()).r/255,n=t.g/255,r=t.b/255,o=Math.min(e,n,r),i=Math.max(e,n,r),a=NaN,u=i-o,c=(i+o)/2;return u?(a=e===i?(n-r)/u+(n<r)*6:n===i?(r-e)/u+2:(e-n)/u+4,u/=c<.5?i+o:2-i-o,a*=60):u=c>0&&c<1?0:a,new tt(a,u,c,t.opacity)}function tt(t,e,n,r){this.h=+t,this.s=+e,this.l=+n,this.opacity=+r}function te(t){return(t=(t||0)%360)<0?t+360:t}function tn(t){return Math.max(0,Math.min(1,t||0))}function tr(t,e,n){return(t<60?e+(n-e)*t/60:t<180?n:t<240?e+(n-e)*(240-t)/60:e)*255}function to(t,e,n,r,o){var i=t*t,a=i*t;return((1-3*t+3*i-a)*e+(4-6*i+3*a)*n+(1+3*t+3*i-3*a)*r+a*o)/6}A(k,F,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:U,formatHex:U,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Q(this).formatHsl()},formatRgb:$,toString:$}),A(Y,W,E(k,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new Y(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new Y(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new Y(G(this.r),G(this.g),G(this.b),V(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:H,formatHex:H,formatHex8:function(){return`#${K(this.r)}${K(this.g)}${K(this.b)}${K((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:X,toString:X})),A(tt,function(t,e,n,r){return 1==arguments.length?Q(t):new tt(t,e,n,null==r?1:r)},E(k,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new tt(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new tt(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*e,o=2*n-r;return new Y(tr(t>=240?t-240:t+120,o,r),tr(t,o,r),tr(t<120?t+240:t-120,o,r),this.opacity)},clamp(){return new tt(te(this.h),tn(this.s),tn(this.l),V(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=V(this.opacity);return`${1===t?"hsl(":"hsla("}${te(this.h)}, ${100*tn(this.s)}%, ${100*tn(this.l)}%${1===t?")":`, ${t})`}`}}));var ti=t=>()=>t;function ta(t,e){var n=e-t;return n?function(e){return t+e*n}:ti(isNaN(t)?e:t)}var tu=function t(e){var n,r=1==(n=+(n=e))?ta:function(t,e){var r,o,i;return e-t?(r=t,o=e,r=Math.pow(r,i=n),o=Math.pow(o,i)-r,i=1/i,function(t){return Math.pow(r+t*o,i)}):ti(isNaN(t)?e:t)};function o(t,e){var n=r((t=W(t)).r,(e=W(e)).r),o=r(t.g,e.g),i=r(t.b,e.b),a=ta(t.opacity,e.opacity);return function(e){return t.r=n(e),t.g=o(e),t.b=i(e),t.opacity=a(e),t+""}}return o.gamma=t,o}(1);function tc(t){return function(e){var n,r,o=e.length,i=Array(o),a=Array(o),u=Array(o);for(n=0;n<o;++n)r=W(e[n]),i[n]=r.r||0,a[n]=r.g||0,u[n]=r.b||0;return i=t(i),a=t(a),u=t(u),r.opacity=1,function(t){return r.r=i(t),r.g=a(t),r.b=u(t),r+""}}}function tl(t,e){return t=+t,e=+e,function(n){return t*(1-n)+e*n}}tc(function(t){var e=t.length-1;return function(n){var r=n<=0?n=0:n>=1?(n=1,e-1):Math.floor(n*e),o=t[r],i=t[r+1],a=r>0?t[r-1]:2*o-i,u=r<e-1?t[r+2]:2*i-o;return to((n-r/e)*e,a,o,i,u)}}),tc(function(t){var e=t.length;return function(n){var r=Math.floor(((n%=1)<0?++n:n)*e),o=t[(r+e-1)%e],i=t[r%e],a=t[(r+1)%e],u=t[(r+2)%e];return to((n-r/e)*e,o,i,a,u)}});var ts=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,tf=RegExp(ts.source,"g");function tp(t,e){var n,r,o=typeof e;return null==e||"boolean"===o?ti(e):("number"===o?tl:"string"===o?(r=F(e))?(e=r,tu):function(t,e){var n,r,o,i,a,u=ts.lastIndex=tf.lastIndex=0,c=-1,l=[],s=[];for(t+="",e+="";(o=ts.exec(t))&&(i=tf.exec(e));)(a=i.index)>u&&(a=e.slice(u,a),l[c]?l[c]+=a:l[++c]=a),(o=o[0])===(i=i[0])?l[c]?l[c]+=i:l[++c]=i:(l[++c]=null,s.push({i:c,x:tl(o,i)})),u=tf.lastIndex;return u<e.length&&(a=e.slice(u),l[c]?l[c]+=a:l[++c]=a),l.length<2?s[0]?(n=s[0].x,function(t){return n(t)+""}):(r=e,function(){return r}):(e=s.length,function(t){for(var n,r=0;r<e;++r)l[(n=s[r]).i]=n.x(t);return l.join("")})}:e instanceof F?tu:e instanceof Date?function(t,e){var n=new Date;return t=+t,e=+e,function(r){return n.setTime(t*(1-r)+e*r),n}}:!ArrayBuffer.isView(n=e)||n instanceof DataView?Array.isArray(e)?function(t,e){var n,r=e?e.length:0,o=t?Math.min(r,t.length):0,i=Array(o),a=Array(r);for(n=0;n<o;++n)i[n]=tp(t[n],e[n]);for(;n<r;++n)a[n]=e[n];return function(t){for(n=0;n<o;++n)a[n]=i[n](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var n,r={},o={};for(n in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)n in t?r[n]=tp(t[n],e[n]):o[n]=e[n];return function(t){for(n in r)o[n]=r[n](t);return o}}:tl:function(t,e){e||(e=[]);var n,r=t?Math.min(e.length,t.length):0,o=e.slice();return function(i){for(n=0;n<r;++n)o[n]=t[n]*(1-i)+e[n]*i;return o}})(t,e)}function th(t,e){return t=+t,e=+e,function(n){return Math.round(t*(1-n)+e*n)}}function td(t){return+t}var ty=[0,1];function tv(t){return t}function tm(t,e){var n;return(e-=t=+t)?function(n){return(n-t)/e}:(n=isNaN(e)?NaN:.5,function(){return n})}function tb(t,e,n){var r=t[0],o=t[1],i=e[0],a=e[1];return o<r?(r=tm(o,r),i=n(a,i)):(r=tm(r,o),i=n(i,a)),function(t){return i(r(t))}}function tg(t,e,n){var r=Math.min(t.length,e.length)-1,o=Array(r),i=Array(r),a=-1;for(t[r]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<r;)o[a]=tm(t[a],t[a+1]),i[a]=n(e[a],e[a+1]);return function(e){var n=P(t,e,1,r)-1;return i[n](o[n](e))}}function tx(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function tO(){var t,e,n,r,o,i,a=ty,u=ty,c=tp,l=tv;function s(){var t,e,n,c=Math.min(a.length,u.length);return l!==tv&&(t=a[0],e=a[c-1],t>e&&(n=t,t=e,e=n),l=function(n){return Math.max(t,Math.min(e,n))}),r=c>2?tg:tb,o=i=null,f}function f(e){return null==e||isNaN(e=+e)?n:(o||(o=r(a.map(t),u,c)))(t(l(e)))}return f.invert=function(n){return l(e((i||(i=r(u,a.map(t),tl)))(n)))},f.domain=function(t){return arguments.length?(a=Array.from(t,td),s()):a.slice()},f.range=function(t){return arguments.length?(u=Array.from(t),s()):u.slice()},f.rangeRound=function(t){return u=Array.from(t),c=th,s()},f.clamp=function(t){return arguments.length?(l=!!t||tv,s()):l!==tv},f.interpolate=function(t){return arguments.length?(c=t,s()):c},f.unknown=function(t){return arguments.length?(n=t,f):n},function(n,r){return t=n,e=r,s()}}function tw(){return tO()(tv,tv)}var tj=n(7628),tS=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function tP(t){var e;if(!(e=tS.exec(t)))throw Error("invalid format: "+t);return new tA({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function tA(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function tE(t,e){if((n=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var n,r=t.slice(0,n);return[r.length>1?r[0]+r.slice(2):r,+t.slice(n+1)]}function tk(t){return(t=tE(Math.abs(t)))?t[1]:NaN}function tM(t,e){var n=tE(t,e);if(!n)return t+"";var r=n[0],o=n[1];return o<0?"0."+Array(-o).join("0")+r:r.length>o+1?r.slice(0,o+1)+"."+r.slice(o+1):r+Array(o-r.length+2).join("0")}tP.prototype=tA.prototype,tA.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};var t_={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>tM(100*t,e),r:tM,s:function(t,e){var n=tE(t,e);if(!n)return t+"";var o=n[0],i=n[1],a=i-(r=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,u=o.length;return a===u?o:a>u?o+Array(a-u+1).join("0"):a>0?o.slice(0,a)+"."+o.slice(a):"0."+Array(1-a).join("0")+tE(t,Math.max(0,e+a-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function tT(t){return t}var tC=Array.prototype.map,tI=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function tD(t,e,n,r){var o,u,c=b(t,e,n);switch((r=tP(null==r?",f":r)).type){case"s":var l=Math.max(Math.abs(t),Math.abs(e));return null!=r.precision||isNaN(u=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(tk(l)/3)))-tk(Math.abs(c))))||(r.precision=u),a(r,l);case"":case"e":case"g":case"p":case"r":null!=r.precision||isNaN(u=Math.max(0,tk(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(o=Math.abs(o=c)))-tk(o))+1)||(r.precision=u-("e"===r.type));break;case"f":case"%":null!=r.precision||isNaN(u=Math.max(0,-tk(Math.abs(c))))||(r.precision=u-("%"===r.type)*2)}return i(r)}function tN(t){var e=t.domain;return t.ticks=function(t){var n=e();return v(n[0],n[n.length-1],null==t?10:t)},t.tickFormat=function(t,n){var r=e();return tD(r[0],r[r.length-1],null==t?10:t,n)},t.nice=function(n){null==n&&(n=10);var r,o,i=e(),a=0,u=i.length-1,c=i[a],l=i[u],s=10;for(l<c&&(o=c,c=l,l=o,o=a,a=u,u=o);s-- >0;){if((o=m(c,l,n))===r)return i[a]=c,i[u]=l,e(i);if(o>0)c=Math.floor(c/o)*o,l=Math.ceil(l/o)*o;else if(o<0)c=Math.ceil(c*o)/o,l=Math.floor(l*o)/o;else break;r=o}return t},t}function tL(){var t=tw();return t.copy=function(){return tx(t,tL())},tj.o.apply(t,arguments),tN(t)}function tB(t,e){t=t.slice();var n,r=0,o=t.length-1,i=t[r],a=t[o];return a<i&&(n=r,r=o,o=n,n=i,i=a,a=n),t[r]=e.floor(i),t[o]=e.ceil(a),t}function tR(t){return Math.log(t)}function tz(t){return Math.exp(t)}function tU(t){return-Math.log(-t)}function t$(t){return-Math.exp(-t)}function tF(t){return isFinite(t)?+("1e"+t):t<0?0:t}function tZ(t){return(e,n)=>-t(-e,n)}function tq(t){let e,n;let r=t(tR,tz),o=r.domain,a=10;function u(){var i,u;return e=(i=a)===Math.E?Math.log:10===i&&Math.log10||2===i&&Math.log2||(i=Math.log(i),t=>Math.log(t)/i),n=10===(u=a)?tF:u===Math.E?Math.exp:t=>Math.pow(u,t),o()[0]<0?(e=tZ(e),n=tZ(n),t(tU,t$)):t(tR,tz),r}return r.base=function(t){return arguments.length?(a=+t,u()):a},r.domain=function(t){return arguments.length?(o(t),u()):o()},r.ticks=t=>{let r,i;let u=o(),c=u[0],l=u[u.length-1],s=l<c;s&&([c,l]=[l,c]);let f=e(c),p=e(l),h=null==t?10:+t,d=[];if(!(a%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),c>0){for(;f<=p;++f)for(r=1;r<a;++r)if(!((i=f<0?r/n(-f):r*n(f))<c)){if(i>l)break;d.push(i)}}else for(;f<=p;++f)for(r=a-1;r>=1;--r)if(!((i=f>0?r/n(-f):r*n(f))<c)){if(i>l)break;d.push(i)}2*d.length<h&&(d=v(c,l,h))}else d=v(f,p,Math.min(p-f,h)).map(n);return s?d.reverse():d},r.tickFormat=(t,o)=>{if(null==t&&(t=10),null==o&&(o=10===a?"s":","),"function"!=typeof o&&(a%1||null!=(o=tP(o)).precision||(o.trim=!0),o=i(o)),t===1/0)return o;let u=Math.max(1,a*t/r.ticks().length);return t=>{let r=t/n(Math.round(e(t)));return r*a<a-.5&&(r*=a),r<=u?o(t):""}},r.nice=()=>o(tB(o(),{floor:t=>n(Math.floor(e(t))),ceil:t=>n(Math.ceil(e(t)))})),r}function tW(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function tY(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function tH(t){var e=1,n=t(tW(1),tY(e));return n.constant=function(n){return arguments.length?t(tW(e=+n),tY(e)):e},tN(n)}i=(o=function(t){var e,n,o,i=void 0===t.grouping||void 0===t.thousands?tT:(e=tC.call(t.grouping,Number),n=t.thousands+"",function(t,r){for(var o=t.length,i=[],a=0,u=e[0],c=0;o>0&&u>0&&(c+u+1>r&&(u=Math.max(1,r-c)),i.push(t.substring(o-=u,o+u)),!((c+=u+1)>r));)u=e[a=(a+1)%e.length];return i.reverse().join(n)}),a=void 0===t.currency?"":t.currency[0]+"",u=void 0===t.currency?"":t.currency[1]+"",c=void 0===t.decimal?".":t.decimal+"",l=void 0===t.numerals?tT:(o=tC.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return o[+t]})}),s=void 0===t.percent?"%":t.percent+"",f=void 0===t.minus?"−":t.minus+"",p=void 0===t.nan?"NaN":t.nan+"";function h(t){var e=(t=tP(t)).fill,n=t.align,o=t.sign,h=t.symbol,d=t.zero,y=t.width,v=t.comma,m=t.precision,b=t.trim,g=t.type;"n"===g?(v=!0,g="g"):t_[g]||(void 0===m&&(m=12),b=!0,g="g"),(d||"0"===e&&"="===n)&&(d=!0,e="0",n="=");var x="$"===h?a:"#"===h&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",O="$"===h?u:/[%p]/.test(g)?s:"",w=t_[g],j=/[defgprs%]/.test(g);function S(t){var a,u,s,h=x,S=O;if("c"===g)S=w(t)+S,t="";else{var P=(t=+t)<0||1/t<0;if(t=isNaN(t)?p:w(Math.abs(t),m),b&&(t=function(t){e:for(var e,n=t.length,r=1,o=-1;r<n;++r)switch(t[r]){case".":o=e=r;break;case"0":0===o&&(o=r),e=r;break;default:if(!+t[r])break e;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t}(t)),P&&0==+t&&"+"!==o&&(P=!1),h=(P?"("===o?o:f:"-"===o||"("===o?"":o)+h,S=("s"===g?tI[8+r/3]:"")+S+(P&&"("===o?")":""),j){for(a=-1,u=t.length;++a<u;)if(48>(s=t.charCodeAt(a))||s>57){S=(46===s?c+t.slice(a+1):t.slice(a))+S,t=t.slice(0,a);break}}}v&&!d&&(t=i(t,1/0));var A=h.length+t.length+S.length,E=A<y?Array(y-A+1).join(e):"";switch(v&&d&&(t=i(E+t,E.length?y-S.length:1/0),E=""),n){case"<":t=h+t+S+E;break;case"=":t=h+E+t+S;break;case"^":t=E.slice(0,A=E.length>>1)+h+t+S+E.slice(A);break;default:t=E+h+t+S}return l(t)}return m=void 0===m?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),S.toString=function(){return t+""},S}return{format:h,formatPrefix:function(t,e){var n=h(((t=tP(t)).type="f",t)),r=3*Math.max(-8,Math.min(8,Math.floor(tk(e)/3))),o=Math.pow(10,-r),i=tI[8+r/3];return function(t){return n(o*t)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,a=o.formatPrefix;var tX=n(53475);function tV(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function tG(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function tK(t){return t<0?-t*t:t*t}function tJ(t){var e=t(tv,tv),n=1;return e.exponent=function(e){return arguments.length?1==(n=+e)?t(tv,tv):.5===n?t(tG,tK):t(tV(n),tV(1/n)):n},tN(e)}function tQ(){var t=tJ(tO());return t.copy=function(){return tx(t,tQ()).exponent(t.exponent())},tj.o.apply(t,arguments),t}function t0(){return tQ.apply(null,arguments).exponent(.5)}function t1(t){return Math.sign(t)*t*t}function t2(t,e){let n;if(void 0===e)for(let e of t)null!=e&&(n<e||void 0===n&&e>=e)&&(n=e);else{let r=-1;for(let o of t)null!=(o=e(o,++r,t))&&(n<o||void 0===n&&o>=o)&&(n=o)}return n}function t6(t,e){let n;if(void 0===e)for(let e of t)null!=e&&(n>e||void 0===n&&e>=e)&&(n=e);else{let r=-1;for(let o of t)null!=(o=e(o,++r,t))&&(n>o||void 0===n&&o>=o)&&(n=o)}return n}function t3(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:t>e?1:0)}function t5(t,e,n){let r=t[e];t[e]=t[n],t[n]=r}let t4=new Date,t8=new Date;function t7(t,e,n,r){function o(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return o.floor=e=>(t(e=new Date(+e)),e),o.ceil=n=>(t(n=new Date(n-1)),e(n,1),t(n),n),o.round=t=>{let e=o(t),n=o.ceil(t);return t-e<n-t?e:n},o.offset=(t,n)=>(e(t=new Date(+t),null==n?1:Math.floor(n)),t),o.range=(n,r,i)=>{let a;let u=[];if(n=o.ceil(n),i=null==i?1:Math.floor(i),!(n<r)||!(i>0))return u;do u.push(a=new Date(+n)),e(n,i),t(n);while(a<n&&n<r);return u},o.filter=n=>t7(e=>{if(e>=e)for(;t(e),!n(e);)e.setTime(e-1)},(t,r)=>{if(t>=t){if(r<0)for(;++r<=0;)for(;e(t,-1),!n(t););else for(;--r>=0;)for(;e(t,1),!n(t););}}),n&&(o.count=(e,r)=>(t4.setTime(+e),t8.setTime(+r),t(t4),t(t8),Math.floor(n(t4,t8))),o.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?o.filter(r?e=>r(e)%t==0:e=>o.count(0,e)%t==0):o:null),o}let t9=t7(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);t9.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?t7(e=>{e.setTime(Math.floor(e/t)*t)},(e,n)=>{e.setTime(+e+n*t)},(e,n)=>(n-e)/t):t9:null,t9.range;let et=t7(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());et.range;let ee=t7(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());ee.range;let en=t7(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());en.range;let er=t7(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());er.range;let eo=t7(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());eo.range;let ei=t7(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);ei.range;let ea=t7(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);ea.range;let eu=t7(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function ec(t){return t7(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}eu.range;let el=ec(0),es=ec(1),ef=ec(2),ep=ec(3),eh=ec(4),ed=ec(5),ey=ec(6);function ev(t){return t7(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}el.range,es.range,ef.range,ep.range,eh.range,ed.range,ey.range;let em=ev(0),eb=ev(1),eg=ev(2),ex=ev(3),eO=ev(4),ew=ev(5),ej=ev(6);em.range,eb.range,eg.range,ex.range,eO.range,ew.range,ej.range;let eS=t7(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());eS.range;let eP=t7(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());eP.range;let eA=t7(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());eA.every=t=>isFinite(t=Math.floor(t))&&t>0?t7(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,n)=>{e.setFullYear(e.getFullYear()+n*t)}):null,eA.range;let eE=t7(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function ek(t,e,n,r,o,i){let a=[[et,1,1e3],[et,5,5e3],[et,15,15e3],[et,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[r,1,864e5],[r,2,1728e5],[n,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function u(e,n,r){let o=Math.abs(n-e)/r,i=O(([,,t])=>t).right(a,o);if(i===a.length)return t.every(b(e/31536e6,n/31536e6,r));if(0===i)return t9.every(Math.max(b(e,n,r),1));let[u,c]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return u.every(c)}return[function(t,e,n){let r=e<t;r&&([t,e]=[e,t]);let o=n&&"function"==typeof n.range?n:u(t,e,n),i=o?o.range(t,+e+1):[];return r?i.reverse():i},u]}eE.every=t=>isFinite(t=Math.floor(t))&&t>0?t7(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,n)=>{e.setUTCFullYear(e.getUTCFullYear()+n*t)}):null,eE.range;let[eM,e_]=ek(eE,eP,em,eu,eo,en),[eT,eC]=ek(eA,eS,el,ei,er,ee);function eI(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function eD(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function eN(t,e,n){return{y:t,m:e,d:n,H:0,M:0,S:0,L:0}}var eL={"-":"",_:" ",0:"0"},eB=/^\s*\d+/,eR=/^%/,ez=/[\\^$*+?|[\]().{}]/g;function eU(t,e,n){var r=t<0?"-":"",o=(r?-t:t)+"",i=o.length;return r+(i<n?Array(n-i+1).join(e)+o:o)}function e$(t){return t.replace(ez,"\\$&")}function eF(t){return RegExp("^(?:"+t.map(e$).join("|")+")","i")}function eZ(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function eq(t,e,n){var r=eB.exec(e.slice(n,n+1));return r?(t.w=+r[0],n+r[0].length):-1}function eW(t,e,n){var r=eB.exec(e.slice(n,n+1));return r?(t.u=+r[0],n+r[0].length):-1}function eY(t,e,n){var r=eB.exec(e.slice(n,n+2));return r?(t.U=+r[0],n+r[0].length):-1}function eH(t,e,n){var r=eB.exec(e.slice(n,n+2));return r?(t.V=+r[0],n+r[0].length):-1}function eX(t,e,n){var r=eB.exec(e.slice(n,n+2));return r?(t.W=+r[0],n+r[0].length):-1}function eV(t,e,n){var r=eB.exec(e.slice(n,n+4));return r?(t.y=+r[0],n+r[0].length):-1}function eG(t,e,n){var r=eB.exec(e.slice(n,n+2));return r?(t.y=+r[0]+(+r[0]>68?1900:2e3),n+r[0].length):-1}function eK(t,e,n){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(n,n+6));return r?(t.Z=r[1]?0:-(r[2]+(r[3]||"00")),n+r[0].length):-1}function eJ(t,e,n){var r=eB.exec(e.slice(n,n+1));return r?(t.q=3*r[0]-3,n+r[0].length):-1}function eQ(t,e,n){var r=eB.exec(e.slice(n,n+2));return r?(t.m=r[0]-1,n+r[0].length):-1}function e0(t,e,n){var r=eB.exec(e.slice(n,n+2));return r?(t.d=+r[0],n+r[0].length):-1}function e1(t,e,n){var r=eB.exec(e.slice(n,n+3));return r?(t.m=0,t.d=+r[0],n+r[0].length):-1}function e2(t,e,n){var r=eB.exec(e.slice(n,n+2));return r?(t.H=+r[0],n+r[0].length):-1}function e6(t,e,n){var r=eB.exec(e.slice(n,n+2));return r?(t.M=+r[0],n+r[0].length):-1}function e3(t,e,n){var r=eB.exec(e.slice(n,n+2));return r?(t.S=+r[0],n+r[0].length):-1}function e5(t,e,n){var r=eB.exec(e.slice(n,n+3));return r?(t.L=+r[0],n+r[0].length):-1}function e4(t,e,n){var r=eB.exec(e.slice(n,n+6));return r?(t.L=Math.floor(r[0]/1e3),n+r[0].length):-1}function e8(t,e,n){var r=eR.exec(e.slice(n,n+1));return r?n+r[0].length:-1}function e7(t,e,n){var r=eB.exec(e.slice(n));return r?(t.Q=+r[0],n+r[0].length):-1}function e9(t,e,n){var r=eB.exec(e.slice(n));return r?(t.s=+r[0],n+r[0].length):-1}function nt(t,e){return eU(t.getDate(),e,2)}function ne(t,e){return eU(t.getHours(),e,2)}function nn(t,e){return eU(t.getHours()%12||12,e,2)}function nr(t,e){return eU(1+ei.count(eA(t),t),e,3)}function no(t,e){return eU(t.getMilliseconds(),e,3)}function ni(t,e){return no(t,e)+"000"}function na(t,e){return eU(t.getMonth()+1,e,2)}function nu(t,e){return eU(t.getMinutes(),e,2)}function nc(t,e){return eU(t.getSeconds(),e,2)}function nl(t){var e=t.getDay();return 0===e?7:e}function ns(t,e){return eU(el.count(eA(t)-1,t),e,2)}function nf(t){var e=t.getDay();return e>=4||0===e?eh(t):eh.ceil(t)}function np(t,e){return t=nf(t),eU(eh.count(eA(t),t)+(4===eA(t).getDay()),e,2)}function nh(t){return t.getDay()}function nd(t,e){return eU(es.count(eA(t)-1,t),e,2)}function ny(t,e){return eU(t.getFullYear()%100,e,2)}function nv(t,e){return eU((t=nf(t)).getFullYear()%100,e,2)}function nm(t,e){return eU(t.getFullYear()%1e4,e,4)}function nb(t,e){var n=t.getDay();return eU((t=n>=4||0===n?eh(t):eh.ceil(t)).getFullYear()%1e4,e,4)}function ng(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+eU(e/60|0,"0",2)+eU(e%60,"0",2)}function nx(t,e){return eU(t.getUTCDate(),e,2)}function nO(t,e){return eU(t.getUTCHours(),e,2)}function nw(t,e){return eU(t.getUTCHours()%12||12,e,2)}function nj(t,e){return eU(1+ea.count(eE(t),t),e,3)}function nS(t,e){return eU(t.getUTCMilliseconds(),e,3)}function nP(t,e){return nS(t,e)+"000"}function nA(t,e){return eU(t.getUTCMonth()+1,e,2)}function nE(t,e){return eU(t.getUTCMinutes(),e,2)}function nk(t,e){return eU(t.getUTCSeconds(),e,2)}function nM(t){var e=t.getUTCDay();return 0===e?7:e}function n_(t,e){return eU(em.count(eE(t)-1,t),e,2)}function nT(t){var e=t.getUTCDay();return e>=4||0===e?eO(t):eO.ceil(t)}function nC(t,e){return t=nT(t),eU(eO.count(eE(t),t)+(4===eE(t).getUTCDay()),e,2)}function nI(t){return t.getUTCDay()}function nD(t,e){return eU(eb.count(eE(t)-1,t),e,2)}function nN(t,e){return eU(t.getUTCFullYear()%100,e,2)}function nL(t,e){return eU((t=nT(t)).getUTCFullYear()%100,e,2)}function nB(t,e){return eU(t.getUTCFullYear()%1e4,e,4)}function nR(t,e){var n=t.getUTCDay();return eU((t=n>=4||0===n?eO(t):eO.ceil(t)).getUTCFullYear()%1e4,e,4)}function nz(){return"+0000"}function nU(){return"%"}function n$(t){return+t}function nF(t){return Math.floor(+t/1e3)}function nZ(t){return new Date(t)}function nq(t){return t instanceof Date?+t:+new Date(+t)}function nW(t,e,n,r,o,i,a,u,c,l){var s=tw(),f=s.invert,p=s.domain,h=l(".%L"),d=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function O(t){return(c(t)<t?h:u(t)<t?d:a(t)<t?y:i(t)<t?v:r(t)<t?o(t)<t?m:b:n(t)<t?g:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,nq)):p().map(nZ)},s.ticks=function(e){var n=p();return t(n[0],n[n.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?O:l(e)},s.nice=function(t){var n=p();return t&&"function"==typeof t.range||(t=e(n[0],n[n.length-1],null==t?10:t)),t?p(tB(n,t)):s},s.copy=function(){return tx(s,nW(t,e,n,r,o,i,a,u,c,l))},s}function nY(){return tj.o.apply(nW(eT,eC,eA,eS,el,ei,er,ee,et,c).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function nH(){return tj.o.apply(nW(eM,e_,eE,eP,em,ea,eo,en,et,l).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function nX(){var t,e,n,r,o,i=0,a=1,u=tv,c=!1;function l(e){return null==e||isNaN(e=+e)?o:u(0===n?.5:(e=(r(e)-t)*n,c?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var n,r;return arguments.length?([n,r]=e,u=t(n,r),l):[u(0),u(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,t=r(i=+i),e=r(a=+a),n=t===e?0:1/(e-t),l):[i,a]},l.clamp=function(t){return arguments.length?(c=!!t,l):c},l.interpolator=function(t){return arguments.length?(u=t,l):u},l.range=s(tp),l.rangeRound=s(th),l.unknown=function(t){return arguments.length?(o=t,l):o},function(o){return r=o,t=o(i),e=o(a),n=t===e?0:1/(e-t),l}}function nV(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function nG(){var t=tJ(nX());return t.copy=function(){return nV(t,nG()).exponent(t.exponent())},tj.O.apply(t,arguments)}function nK(){return nG.apply(null,arguments).exponent(.5)}function nJ(){var t,e,n,r,o,i,a,u=0,c=.5,l=1,s=1,f=tv,p=!1;function h(t){return isNaN(t=+t)?a:(t=.5+((t=+i(t))-e)*(s*t<s*e?r:o),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var n,r,o;return arguments.length?([n,r,o]=e,f=function(t,e){void 0===e&&(e=t,t=tp);for(var n=0,r=e.length-1,o=e[0],i=Array(r<0?0:r);n<r;)i[n]=t(o,o=e[++n]);return function(t){var e=Math.max(0,Math.min(r-1,Math.floor(t*=r)));return i[e](t-e)}}(t,[n,r,o]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([u,c,l]=a,t=i(u=+u),e=i(c=+c),n=i(l=+l),r=t===e?0:.5/(e-t),o=e===n?0:.5/(n-e),s=e<t?-1:1,h):[u,c,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(tp),h.rangeRound=d(th),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return i=a,t=a(u),e=a(c),n=a(l),r=t===e?0:.5/(e-t),o=e===n?0:.5/(n-e),s=e<t?-1:1,h}}function nQ(){var t=tJ(nJ());return t.copy=function(){return nV(t,nQ()).exponent(t.exponent())},tj.O.apply(t,arguments)}function n0(){return nQ.apply(null,arguments).exponent(.5)}function n1(t,e){if((o=t.length)>1)for(var n,r,o,i=1,a=t[e[0]],u=a.length;i<o;++i)for(r=a,a=t[e[i]],n=0;n<u;++n)a[n][1]+=a[n][0]=isNaN(r[n][1])?r[n][0]:r[n][1]}c=(u=function(t){var e=t.dateTime,n=t.date,r=t.time,o=t.periods,i=t.days,a=t.shortDays,u=t.months,c=t.shortMonths,l=eF(o),s=eZ(o),f=eF(i),p=eZ(i),h=eF(a),d=eZ(a),y=eF(u),v=eZ(u),m=eF(c),b=eZ(c),g={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return c[t.getMonth()]},B:function(t){return u[t.getMonth()]},c:null,d:nt,e:nt,f:ni,g:nv,G:nb,H:ne,I:nn,j:nr,L:no,m:na,M:nu,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:n$,s:nF,S:nc,u:nl,U:ns,V:np,w:nh,W:nd,x:null,X:null,y:ny,Y:nm,Z:ng,"%":nU},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return c[t.getUTCMonth()]},B:function(t){return u[t.getUTCMonth()]},c:null,d:nx,e:nx,f:nP,g:nL,G:nR,H:nO,I:nw,j:nj,L:nS,m:nA,M:nE,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:n$,s:nF,S:nk,u:nM,U:n_,V:nC,w:nI,W:nD,x:null,X:null,y:nN,Y:nB,Z:nz,"%":nU},O={a:function(t,e,n){var r=h.exec(e.slice(n));return r?(t.w=d.get(r[0].toLowerCase()),n+r[0].length):-1},A:function(t,e,n){var r=f.exec(e.slice(n));return r?(t.w=p.get(r[0].toLowerCase()),n+r[0].length):-1},b:function(t,e,n){var r=m.exec(e.slice(n));return r?(t.m=b.get(r[0].toLowerCase()),n+r[0].length):-1},B:function(t,e,n){var r=y.exec(e.slice(n));return r?(t.m=v.get(r[0].toLowerCase()),n+r[0].length):-1},c:function(t,n,r){return S(t,e,n,r)},d:e0,e:e0,f:e4,g:eG,G:eV,H:e2,I:e2,j:e1,L:e5,m:eQ,M:e6,p:function(t,e,n){var r=l.exec(e.slice(n));return r?(t.p=s.get(r[0].toLowerCase()),n+r[0].length):-1},q:eJ,Q:e7,s:e9,S:e3,u:eW,U:eY,V:eH,w:eq,W:eX,x:function(t,e,r){return S(t,n,e,r)},X:function(t,e,n){return S(t,r,e,n)},y:eG,Y:eV,Z:eK,"%":e8};function w(t,e){return function(n){var r,o,i,a=[],u=-1,c=0,l=t.length;for(n instanceof Date||(n=new Date(+n));++u<l;)37===t.charCodeAt(u)&&(a.push(t.slice(c,u)),null!=(o=eL[r=t.charAt(++u)])?r=t.charAt(++u):o="e"===r?" ":"0",(i=e[r])&&(r=i(n,o)),a.push(r),c=u+1);return a.push(t.slice(c,u)),a.join("")}}function j(t,e){return function(n){var r,o,i=eN(1900,void 0,1);if(S(i,t,n+="",0)!=n.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!e||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(r=(o=(r=eD(eN(i.y,0,1))).getUTCDay())>4||0===o?eb.ceil(r):eb(r),r=ea.offset(r,(i.V-1)*7),i.y=r.getUTCFullYear(),i.m=r.getUTCMonth(),i.d=r.getUTCDate()+(i.w+6)%7):(r=(o=(r=eI(eN(i.y,0,1))).getDay())>4||0===o?es.ceil(r):es(r),r=ei.offset(r,(i.V-1)*7),i.y=r.getFullYear(),i.m=r.getMonth(),i.d=r.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:"W"in i?1:0),o="Z"in i?eD(eN(i.y,0,1)).getUTCDay():eI(eN(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,eD(i)):eI(i)}}function S(t,e,n,r){for(var o,i,a=0,u=e.length,c=n.length;a<u;){if(r>=c)return -1;if(37===(o=e.charCodeAt(a++))){if(!(i=O[(o=e.charAt(a++))in eL?e.charAt(a++):o])||(r=i(t,n,r))<0)return -1}else if(o!=n.charCodeAt(r++))return -1}return r}return g.x=w(n,g),g.X=w(r,g),g.c=w(e,g),x.x=w(n,x),x.X=w(r,x),x.c=w(e,x),{format:function(t){var e=w(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=w(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,u.parse,l=u.utcFormat,u.utcParse;var n2=n(42562),n6=n(37226);function n3(t){for(var e=t.length,n=Array(e);--e>=0;)n[e]=e;return n}function n5(t,e){return t[e]}function n4(t){let e=[];return e.key=t,e}var n8=n(60440),n7=n.n(n8),n9=n(97387),rt=n.n(n9),re=n(86197),rn=n.n(re),rr=n(14119),ro=n.n(rr),ri=n(62066),ra=n.n(ri),ru=n(82404),rc=n.n(ru),rl=n(62343),rs=n.n(rl),rf=n(49572),rp=n.n(rf),rh=n(4501),rd=n.n(rh),ry=n(25010),rv=n.n(ry),rm=n(9313),rb=n.n(rm),rg=n(61690),rx=n.n(rg);function rO(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var rw=function(t){return t},rj={},rS=function(t){return t===rj},rP=function(t){return function e(){return 0==arguments.length||1==arguments.length&&rS(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},rA=function(t){return function t(e,n){return 1===e?n:rP(function(){for(var r=arguments.length,o=Array(r),i=0;i<r;i++)o[i]=arguments[i];var a=o.filter(function(t){return t!==rj}).length;return a>=e?n.apply(void 0,o):t(e-a,rP(function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var i=o.map(function(t){return rS(t)?e.shift():t});return n.apply(void 0,((function(t){if(Array.isArray(t))return rO(t)})(i)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(i)||function(t,e){if(t){if("string"==typeof t)return rO(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return rO(t,void 0)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},rE=function(t,e){for(var n=[],r=t;r<e;++r)n[r-t]=r;return n},rk=rA(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),rM=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];if(!e.length)return rw;var r=e.reverse(),o=r[0],i=r.slice(1);return function(){return i.reduce(function(t,e){return e(t)},o.apply(void 0,arguments))}},r_=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},rT=function(t){var e=null,n=null;return function(){for(var r=arguments.length,o=Array(r),i=0;i<r;i++)o[i]=arguments[i];return e&&o.every(function(t,n){return t===e[n]})?n:(e=o,n=t.apply(void 0,o))}},rC=(rA(function(t,e,n){var r=+t;return r+n*(+e-r)}),rA(function(t,e,n){var r=e-+t;return(n-t)/(r=r||1/0)}),rA(function(t,e,n){var r=e-+t;return Math.max(0,Math.min(1,(n-t)/(r=r||1/0)))}),{rangeStep:function(t,e,n){for(var r=new(rx())(t),o=0,i=[];r.lt(e)&&o<1e5;)i.push(r.toNumber()),r=r.add(n),o++;return i},getDigitCount:function(t){return 0===t?1:Math.floor(new(rx())(t).abs().log(10).toNumber())+1}});function rI(t){return function(t){if(Array.isArray(t))return rL(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||rN(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function rD(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var n=[],r=!0,o=!1,i=void 0;try{for(var a,u=t[Symbol.iterator]();!(r=(a=u.next()).done)&&(n.push(a.value),!e||n.length!==e);r=!0);}catch(t){o=!0,i=t}finally{try{r||null==u.return||u.return()}finally{if(o)throw i}}return n}}(t,e)||rN(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function rN(t,e){if(t){if("string"==typeof t)return rL(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return rL(t,e)}}function rL(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function rB(t){var e=rD(t,2),n=e[0],r=e[1],o=n,i=r;return n>r&&(o=r,i=n),[o,i]}function rR(t,e,n){if(t.lte(0))return new(rx())(0);var r=rC.getDigitCount(t.toNumber()),o=new(rx())(10).pow(r),i=t.div(o),a=1!==r?.05:.1,u=new(rx())(Math.ceil(i.div(a).toNumber())).add(n).mul(a).mul(o);return e?u:new(rx())(Math.ceil(u))}function rz(t,e,n){var r=1,o=new(rx())(t);if(!o.isint()&&n){var i=Math.abs(t);i<1?(r=new(rx())(10).pow(rC.getDigitCount(t)-1),o=new(rx())(Math.floor(o.div(r).toNumber())).mul(r)):i>1&&(o=new(rx())(Math.floor(t)))}else 0===t?o=new(rx())(Math.floor((e-1)/2)):n||(o=new(rx())(Math.floor(t)));var a=Math.floor((e-1)/2);return rM(rk(function(t){return o.add(new(rx())(t-a).mul(r)).toNumber()}),rE)(0,e)}var rU=rT(function(t){var e=rD(t,2),n=e[0],r=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),u=rD(rB([n,r]),2),c=u[0],l=u[1];if(c===-1/0||l===1/0){var s=l===1/0?[c].concat(rI(rE(0,o-1).map(function(){return 1/0}))):[].concat(rI(rE(0,o-1).map(function(){return-1/0})),[l]);return n>r?r_(s):s}if(c===l)return rz(c,o,i);var f=function t(e,n,r,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((n-e)/(r-1)))return{step:new(rx())(0),tickMin:new(rx())(0),tickMax:new(rx())(0)};var u=rR(new(rx())(n).sub(e).div(r-1),o,a),c=Math.ceil((i=e<=0&&n>=0?new(rx())(0):(i=new(rx())(e).add(n).div(2)).sub(new(rx())(i).mod(u))).sub(e).div(u).toNumber()),l=Math.ceil(new(rx())(n).sub(i).div(u).toNumber()),s=c+l+1;return s>r?t(e,n,r,o,a+1):(s<r&&(l=n>0?l+(r-s):l,c=n>0?c:c+(r-s)),{step:u,tickMin:i.sub(new(rx())(c).mul(u)),tickMax:i.add(new(rx())(l).mul(u))})}(c,l,a,i),p=f.step,h=f.tickMin,d=f.tickMax,y=rC.rangeStep(h,d.add(new(rx())(.1).mul(p)),p);return n>r?r_(y):y});rT(function(t){var e=rD(t,2),n=e[0],r=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),u=rD(rB([n,r]),2),c=u[0],l=u[1];if(c===-1/0||l===1/0)return[n,r];if(c===l)return rz(c,o,i);var s=rR(new(rx())(l).sub(c).div(a-1),i,0),f=rM(rk(function(t){return new(rx())(c).add(new(rx())(t).mul(s)).toNumber()}),rE)(0,a).filter(function(t){return t>=c&&t<=l});return n>r?r_(f):f});var r$=rT(function(t,e){var n=rD(t,2),r=n[0],o=n[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=rD(rB([r,o]),2),u=a[0],c=a[1];if(u===-1/0||c===1/0)return[r,o];if(u===c)return[u];var l=rR(new(rx())(c).sub(u).div(Math.max(e,2)-1),i,0),s=[].concat(rI(rC.rangeStep(new(rx())(u),new(rx())(c).sub(new(rx())(.99).mul(l)),l)),[c]);return r>o?r_(s):s}),rF=n(32486),rZ=n(12445),rq=n(14166),rW=n(21105),rY=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function rH(t){return(rH="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rX(){return(rX=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function rV(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function rG(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(rG=function(){return!!t})()}function rK(t){return(rK=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function rJ(t,e){return(rJ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function rQ(t,e,n){return(e=r0(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function r0(t){var e=function(t,e){if("object"!=rH(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=rH(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==rH(e)?e:e+""}var r1=function(t){var e;function n(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),t=n,e=arguments,t=rK(t),function(t,e){if(e&&("object"===rH(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,rG()?Reflect.construct(t,e||[],rK(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&rJ(t,e)}(n,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,n=t.layout,r=t.width,o=t.dataKey,i=t.data,a=t.dataPointFormatter,u=t.xAxis,c=t.yAxis,l=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,rY),s=(0,rW.L6)(l,!1);"x"===this.props.direction&&"number"!==u.type&&(0,rZ.Z)(!1);var f=i.map(function(t){var i,l,f=a(t,o),p=f.x,h=f.y,d=f.value,y=f.errorVal;if(!y)return null;var v=[];if(Array.isArray(y)){var m=function(t){if(Array.isArray(t))return t}(y)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{for(i=(n=n.call(t)).next;!(c=(r=i.call(n)).done)&&(u.push(r.value),2!==u.length);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(y,2)||function(t,e){if(t){if("string"==typeof t)return rV(t,2);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return rV(t,2)}}(y,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=m[0],l=m[1]}else i=l=y;if("vertical"===n){var b=u.scale,g=h+e,x=g+r,O=g-r,w=b(d-i),j=b(d+l);v.push({x1:j,y1:x,x2:j,y2:O}),v.push({x1:w,y1:g,x2:j,y2:g}),v.push({x1:w,y1:x,x2:w,y2:O})}else if("horizontal"===n){var S=c.scale,P=p+e,A=P-r,E=P+r,k=S(d-i),M=S(d+l);v.push({x1:A,y1:M,x2:E,y2:M}),v.push({x1:P,y1:k,x2:P,y2:M}),v.push({x1:A,y1:k,x2:E,y2:k})}return rF.createElement(rq.m,rX({className:"recharts-errorBar",key:"bar-".concat(v.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},s),v.map(function(t){return rF.createElement("line",rX({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return rF.createElement(rq.m,{className:"recharts-errorBars"},f)}}],function(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r0(r.key),r)}}(n.prototype,e),Object.defineProperty(n,"prototype",{writable:!1}),n}(rF.Component);rQ(r1,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),rQ(r1,"displayName","ErrorBar");var r2=n(46729),r6=n(89099);function r3(t){return(r3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function r5(t){return function(t){if(Array.isArray(t))return r4(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return r4(t,void 0);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return r4(t,void 0)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r4(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function r8(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function r7(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?r8(Object(n),!0).forEach(function(e){r9(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):r8(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function r9(t,e,n){var r;return(r=function(t,e){if("object"!=r3(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=r3(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==r3(r)?r:r+"")in t)?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function ot(t,e,n){return rn()(t)||rn()(e)?n:(0,r2.P2)(e)?rc()(t,e,n):ro()(e)?e(t):n}function oe(t,e,n,r){var o=rs()(t,function(t){return ot(t,e)});if("number"===n){var i=o.filter(function(t){return(0,r2.hj)(t)||parseFloat(t)});return i.length?[rt()(i),n7()(i)]:[1/0,-1/0]}return(r?o.filter(function(t){return!rn()(t)}):o).map(function(t){return(0,r2.P2)(t)||t instanceof Date?t:""})}var on=function(t){var e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(e=null==n?void 0:n.length)&&void 0!==e?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var u=o.range,c=0;c<a;c++){var l=c>0?r[c-1].coordinate:r[a-1].coordinate,s=r[c].coordinate,f=c>=a-1?r[0].coordinate:r[c+1].coordinate,p=void 0;if((0,r2.uY)(s-l)!==(0,r2.uY)(f-s)){var h=[];if((0,r2.uY)(f-s)===(0,r2.uY)(u[1]-u[0])){p=f;var d=s+u[1]-u[0];h[0]=Math.min(d,(d+l)/2),h[1]=Math.max(d,(d+l)/2)}else{p=l;var y=f+u[1]-u[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){i=r[c].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){i=r[c].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(n[g].coordinate+n[g+1].coordinate)/2||g>0&&g<a-1&&t>(n[g].coordinate+n[g-1].coordinate)/2&&t<=(n[g].coordinate+n[g+1].coordinate)/2||g===a-1&&t>(n[g].coordinate+n[g-1].coordinate)/2){i=n[g].index;break}return i},or=function(t){var e,n,r=t.type.displayName,o=null!==(e=t.type)&&void 0!==e&&e.defaultProps?r7(r7({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(r){case"Line":n=i;break;case"Area":case"Radar":n=i&&"none"!==i?i:a;break;default:n=a}return n},oo=function(t){var e=t.barSize,n=t.totalSize,r=t.stackGroups,o=void 0===r?{}:r;if(!o)return{};for(var i={},a=Object.keys(o),u=0,c=a.length;u<c;u++)for(var l=o[a[u]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],d=h.items,y=h.cateAxisId,v=d.filter(function(t){return(0,rW.Gf)(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?r7(r7({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];i[x]||(i[x]=[]);var O=rn()(g)?e:g;i[x].push({item:v[0],stackList:v.slice(1),barSize:rn()(O)?void 0:(0,r2.h1)(O,n,0)})}}return i},oi=function(t){var e,n=t.barGap,r=t.barCategoryGap,o=t.bandSize,i=t.sizeList,a=void 0===i?[]:i,u=t.maxBarSize,c=a.length;if(c<1)return null;var l=(0,r2.h1)(n,o,0,!0),s=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=o/c,h=a.reduce(function(t,e){return t+e.barSize||0},0);(h+=(c-1)*l)>=o&&(h-=(c-1)*l,l=0),h>=o&&p>0&&(f=!0,p*=.9,h=c*p);var d={offset:((o-h)/2>>0)-l,size:0};e=a.reduce(function(t,e){var n={item:e.item,position:{offset:d.offset+d.size+l,size:f?p:e.barSize}},r=[].concat(r5(t),[n]);return d=r[r.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){r.push({item:t,position:d})}),r},s)}else{var y=(0,r2.h1)(r,o,0,!0);o-2*y-(c-1)*l<=0&&(l=0);var v=(o-2*y-(c-1)*l)/c;v>1&&(v>>=0);var m=u===+u?Math.min(v,u):v;e=a.reduce(function(t,e,n){var r=[].concat(r5(t),[{item:e.item,position:{offset:y+(v+l)*n+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){r.push({item:t,position:r[r.length-1].position})}),r},s)}return e},oa=function(t,e,n,r){var o=n.children,i=n.width,a=n.margin,u=i-(a.left||0)-(a.right||0),c=(0,r6.z)({children:o,legendWidth:u});if(c){var l=r||{},s=l.width,f=l.height,p=c.align,h=c.verticalAlign,d=c.layout;if(("vertical"===d||"horizontal"===d&&"middle"===h)&&"center"!==p&&(0,r2.hj)(t[p]))return r7(r7({},t),{},r9({},p,t[p]+(s||0)));if(("horizontal"===d||"vertical"===d&&"center"===p)&&"middle"!==h&&(0,r2.hj)(t[h]))return r7(r7({},t),{},r9({},h,t[h]+(f||0)))}return t},ou=function(t,e,n,r,o){var i=e.props.children,a=(0,rW.NN)(i,r1).filter(function(t){var e;return e=t.props.direction,!!rn()(o)||("horizontal"===r?"yAxis"===o:"vertical"===r||"x"===e?"xAxis"===o:"y"!==e||"yAxis"===o)});if(a&&a.length){var u=a.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var r=ot(e,n);if(rn()(r))return t;var o=Array.isArray(r)?[rt()(r),n7()(r)]:[r,r],i=u.reduce(function(t,n){var r=ot(e,n,0),i=o[0]-Math.abs(Array.isArray(r)?r[0]:r),a=o[1]+Math.abs(Array.isArray(r)?r[1]:r);return[Math.min(i,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]},[1/0,-1/0])}return null},oc=function(t,e,n,r,o){var i=e.map(function(e){return ou(t,e,n,o,r)}).filter(function(t){return!rn()(t)});return i&&i.length?i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},ol=function(t,e,n,r,o){var i=e.map(function(e){var i=e.props.dataKey;return"number"===n&&i&&ou(t,e,i,r)||oe(t,i,n,o)});if("number"===n)return i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return i.reduce(function(t,e){for(var n=0,r=e.length;n<r;n++)a[e[n]]||(a[e[n]]=!0,t.push(e[n]));return t},[])},os=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},of=function(t,e,n){if(!t)return null;var r=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,u="scaleBand"===t.realScaleType?r.bandwidth()/2:2,c=(e||n)&&"category"===i&&r.bandwidth?r.bandwidth()/u:0;return(c="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*(0,r2.uY)(a[0]-a[1])*c:c,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:r(o?o.indexOf(t):t)+c,value:t,offset:c}}).filter(function(t){return!rp()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:r(t)+c,value:t,index:e,offset:c}}):r.ticks&&!n?r.ticks(t.tickCount).map(function(t){return{coordinate:r(t)+c,value:t,offset:c}}):r.domain().map(function(t,e){return{coordinate:r(t)+c,value:o?o[t]:t,index:e,offset:c}})},op=new WeakMap,oh=function(t,e){if("function"!=typeof e)return t;op.has(t)||op.set(t,new WeakMap);var n=op.get(t);if(n.has(e))return n.get(e);var r=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return n.set(e,r),r},od=function(t,e,n){var r=t.scale,o=t.type,i=t.layout,a=t.axisType;if("auto"===r)return"radial"===i&&"radiusAxis"===a?{scale:f.Z(),realScaleType:"band"}:"radial"===i&&"angleAxis"===a?{scale:tL(),realScaleType:"linear"}:"category"===o&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!n)?{scale:f.x(),realScaleType:"point"}:"category"===o?{scale:f.Z(),realScaleType:"band"}:{scale:tL(),realScaleType:"linear"};if(ra()(r)){var u="scale".concat(rd()(r));return{scale:(s[u]||f.x)(),realScaleType:s[u]?u:"point"}}return ro()(r)?{scale:r}:{scale:f.x(),realScaleType:"point"}},oy=function(t){var e=t.domain();if(e&&!(e.length<=2)){var n=e.length,r=t.range(),o=Math.min(r[0],r[1])-1e-4,i=Math.max(r[0],r[1])+1e-4,a=t(e[0]),u=t(e[n-1]);(a<o||a>i||u<o||u>i)&&t.domain([e[0],e[n-1]])}},ov={sign:function(t){var e=t.length;if(!(e<=0))for(var n=0,r=t[0].length;n<r;++n)for(var o=0,i=0,a=0;a<e;++a){var u=rp()(t[a][n][1])?t[a][n][0]:t[a][n][1];u>=0?(t[a][n][0]=o,t[a][n][1]=o+u,o=t[a][n][1]):(t[a][n][0]=i,t[a][n][1]=i+u,i=t[a][n][1])}},expand:function(t,e){if((r=t.length)>0){for(var n,r,o,i=0,a=t[0].length;i<a;++i){for(o=n=0;n<r;++n)o+=t[n][i][1]||0;if(o)for(n=0;n<r;++n)t[n][i][1]/=o}n1(t,e)}},none:n1,silhouette:function(t,e){if((n=t.length)>0){for(var n,r=0,o=t[e[0]],i=o.length;r<i;++r){for(var a=0,u=0;a<n;++a)u+=t[a][r][1]||0;o[r][1]+=o[r][0]=-u/2}n1(t,e)}},wiggle:function(t,e){if((o=t.length)>0&&(r=(n=t[e[0]]).length)>0){for(var n,r,o,i=0,a=1;a<r;++a){for(var u=0,c=0,l=0;u<o;++u){for(var s=t[e[u]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<u;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}c+=f,l+=p*f}n[a-1][1]+=n[a-1][0]=i,c&&(i-=l/c)}n[a-1][1]+=n[a-1][0]=i,n1(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var n=0,r=t[0].length;n<r;++n)for(var o=0,i=0;i<e;++i){var a=rp()(t[i][n][1])?t[i][n][0]:t[i][n][1];a>=0?(t[i][n][0]=o,t[i][n][1]=o+a,o=t[i][n][1]):(t[i][n][0]=0,t[i][n][1]=0)}}},om=function(t,e,n){var r=e.map(function(t){return t.props.dataKey}),o=ov[n];return(function(){var t=(0,n6.Z)([]),e=n3,n=n1,r=n5;function o(o){var i,a,u=Array.from(t.apply(this,arguments),n4),c=u.length,l=-1;for(let t of o)for(i=0,++l;i<c;++i)(u[i][l]=[0,+r(t,u[i].key,l,o)]).data=t;for(i=0,a=(0,n2.Z)(e(u));i<c;++i)u[a[i]].index=i;return n(u,a),u}return o.keys=function(e){return arguments.length?(t="function"==typeof e?e:(0,n6.Z)(Array.from(e)),o):t},o.value=function(t){return arguments.length?(r="function"==typeof t?t:(0,n6.Z)(+t),o):r},o.order=function(t){return arguments.length?(e=null==t?n3:"function"==typeof t?t:(0,n6.Z)(Array.from(t)),o):e},o.offset=function(t){return arguments.length?(n=null==t?n1:t,o):n},o})().keys(r).value(function(t,e){return+ot(t,e,0)}).order(n3).offset(o)(t)},ob=function(t,e,n,r,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce(function(t,e){var o,i=null!==(o=e.type)&&void 0!==o&&o.defaultProps?r7(r7({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var u=i[n],c=t[u]||{hasStack:!1,stackGroups:{}};if((0,r2.P2)(a)){var l=c.stackGroups[a]||{numericAxisId:n,cateAxisId:r,items:[]};l.items.push(e),c.hasStack=!0,c.stackGroups[a]=l}else c.stackGroups[(0,r2.EL)("_stackId_")]={numericAxisId:n,cateAxisId:r,items:[e]};return r7(r7({},t),{},r9({},u,c))},{});return Object.keys(a).reduce(function(e,i){var u=a[i];return u.hasStack&&(u.stackGroups=Object.keys(u.stackGroups).reduce(function(e,i){var a=u.stackGroups[i];return r7(r7({},e),{},r9({},i,{numericAxisId:n,cateAxisId:r,items:a.items,stackedData:om(t,a.items,o)}))},{})),r7(r7({},e),{},r9({},i,u))},{})},og=function(t,e){var n=e.realScaleType,r=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,u=n||e.scale;if("auto"!==u&&"linear"!==u)return null;if(o&&"number"===r&&i&&("auto"===i[0]||"auto"===i[1])){var c=t.domain();if(!c.length)return null;var l=rU(c,o,a);return t.domain([rt()(l),n7()(l)]),{niceTicks:l}}return o&&"number"===r?{niceTicks:r$(t.domain(),o,a)}:null},ox=function(t,e){var n,r=(null!==(n=t.type)&&void 0!==n&&n.defaultProps?r7(r7({},t.type.defaultProps),t.props):t.props).stackId;if((0,r2.P2)(r)){var o=e[r];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null},oO=function(t,e,n){return Object.keys(t).reduce(function(r,o){var i=t[o].stackedData.reduce(function(t,r){var o=r.slice(e,n+1).reduce(function(t,e){return[rt()(e.concat([t[0]]).filter(r2.hj)),n7()(e.concat([t[1]]).filter(r2.hj))]},[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],r[0]),Math.max(i[1],r[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},ow=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,oj=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,oS=function(t,e,n){if(ro()(t))return t(e,n);if(!Array.isArray(t))return e;var r=[];if((0,r2.hj)(t[0]))r[0]=n?t[0]:Math.min(t[0],e[0]);else if(ow.test(t[0])){var o=+ow.exec(t[0])[1];r[0]=e[0]-o}else ro()(t[0])?r[0]=t[0](e[0]):r[0]=e[0];if((0,r2.hj)(t[1]))r[1]=n?t[1]:Math.max(t[1],e[1]);else if(oj.test(t[1])){var i=+oj.exec(t[1])[1];r[1]=e[1]+i}else ro()(t[1])?r[1]=t[1](e[1]):r[1]=e[1];return r},oP=function(t,e,n){if(t&&t.scale&&t.scale.bandwidth){var r=t.scale.bandwidth();if(!n||r>0)return r}if(t&&e&&e.length>=2){for(var o=rb()(e,function(t){return t.coordinate}),i=1/0,a=1,u=o.length;a<u;a++){var c=o[a],l=o[a-1];i=Math.min((c.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return n?void 0:0},oA=function(t,e,n){return!t||!t.length||rv()(t,rc()(n,"type.defaultProps.domain"))?e:t},oE=function(t,e){var n=t.type.defaultProps?r7(r7({},t.type.defaultProps),t.props):t.props,r=n.dataKey,o=n.name,i=n.unit,a=n.formatter,u=n.tooltipType,c=n.chartType,l=n.hide;return r7(r7({},(0,rW.L6)(t,!1)),{},{dataKey:r,unit:i,formatter:a,name:o||r,color:or(t),value:ot(e,r),type:u,payload:e,chartType:c,hide:l})}},91568:function(t,e,n){"use strict";n.d(e,{os:function(){return f},xE:function(){return s}});var r=n(43160);function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function a(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?i(Object(n),!0).forEach(function(e){var r,i;r=e,i=n[e],(r=function(t){var e=function(t,e){if("object"!=o(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=o(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==o(e)?e:e+""}(r))in t?Object.defineProperty(t,r,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[r]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var u={widthCache:{},cacheCount:0},c={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},l="recharts_measurement_span",s=function(t){var e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||r.x.isSsr)return{width:0,height:0};var o=(Object.keys(e=a({},n)).forEach(function(t){e[t]||delete e[t]}),e),i=JSON.stringify({text:t,copyStyle:o});if(u.widthCache[i])return u.widthCache[i];try{var s=document.getElementById(l);s||((s=document.createElement("span")).setAttribute("id",l),s.setAttribute("aria-hidden","true"),document.body.appendChild(s));var f=a(a({},c),o);Object.assign(s.style,f),s.textContent="".concat(t);var p=s.getBoundingClientRect(),h={width:p.width,height:p.height};return u.widthCache[i]=h,++u.cacheCount>2e3&&(u.cacheCount=0,u.widthCache={}),h}catch(t){return{width:0,height:0}}},f=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}}},46729:function(t,e,n){"use strict";n.d(e,{Ap:function(){return O},EL:function(){return v},Kt:function(){return b},P2:function(){return d},bv:function(){return g},h1:function(){return m},hU:function(){return p},hj:function(){return h},k4:function(){return x},uY:function(){return f}});var r=n(62066),o=n.n(r),i=n(49572),a=n.n(i),u=n(82404),c=n.n(u),l=n(74337),s=n.n(l);n(86197);var f=function(t){return 0===t?0:t>0?1:-1},p=function(t){return o()(t)&&t.indexOf("%")===t.length-1},h=function(t){return s()(t)&&!a()(t)},d=function(t){return h(t)||o()(t)},y=0,v=function(t){var e=++y;return"".concat(t||"").concat(e)},m=function(t,e){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!h(t)&&!o()(t))return r;if(p(t)){var u=t.indexOf("%");n=e*parseFloat(t.slice(0,u))/100}else n=+t;return a()(n)&&(n=r),i&&n>e&&(n=e),n},b=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},g=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,n={},r=0;r<e;r++){if(n[t[r]])return!0;n[t[r]]=!0}return!1},x=function(t,e){return h(t)&&h(e)?function(n){return t+n*(e-t)}:function(){return e}};function O(t,e,n){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):c()(t,e))===n}):null}},43160:function(t,e,n){"use strict";n.d(e,{x:function(){return r}});var r={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout),get:function(t){return r[t]},set:function(t,e){if("string"==typeof t)r[t]=e;else{var n=Object.keys(t);n&&n.length&&n.forEach(function(e){r[e]=t[e]})}}}},25197:function(t,e,n){"use strict";n.d(e,{Z:function(){return r}});var r=function(t,e){for(var n=arguments.length,r=Array(n>2?n-2:0),o=2;o<n;o++)r[o-2]=arguments[o]}},73845:function(t,e,n){"use strict";n.d(e,{$4:function(){return m},$S:function(){return j},Wk:function(){return y},op:function(){return v},t9:function(){return b},z3:function(){return w}});var r=n(86197),o=n.n(r),i=n(32486),a=n(14119),u=n.n(a),c=n(46729),l=n(74499);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function p(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?f(Object(n),!0).forEach(function(e){h(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function h(t,e,n){var r;return(r=function(t,e){if("object"!=s(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=s(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==s(r)?r:r+"")in t)?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var y=Math.PI/180,v=function(t,e,n,r){return{x:t+Math.cos(-y*r)*n,y:e+Math.sin(-y*r)*n}},m=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(n.left||0)-(n.right||0)),Math.abs(e-(n.top||0)-(n.bottom||0)))/2},b=function(t,e,n,r,i){var a=t.width,u=t.height,s=t.startAngle,f=t.endAngle,y=(0,c.h1)(t.cx,a,a/2),v=(0,c.h1)(t.cy,u,u/2),b=m(a,u,n),g=(0,c.h1)(t.innerRadius,b,0),x=(0,c.h1)(t.outerRadius,b,.8*b);return Object.keys(e).reduce(function(t,n){var a,u=e[n],c=u.domain,m=u.reversed;if(o()(u.range))"angleAxis"===r?a=[s,f]:"radiusAxis"===r&&(a=[g,x]),m&&(a=[a[1],a[0]]);else{var b,O=function(t){if(Array.isArray(t))return t}(b=a=u.range)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,u=[],c=!0,l=!1;try{for(i=(n=n.call(t)).next;!(c=(r=i.call(n)).done)&&(u.push(r.value),2!==u.length);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(b,2)||function(t,e){if(t){if("string"==typeof t)return d(t,2);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return d(t,2)}}(b,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();s=O[0],f=O[1]}var w=(0,l.Hq)(u,i),j=w.realScaleType,S=w.scale;S.domain(c).range(a),(0,l.zF)(S);var P=(0,l.g$)(S,p(p({},u),{},{realScaleType:j})),A=p(p(p({},u),P),{},{range:a,radius:x,realScaleType:j,scale:S,cx:y,cy:v,innerRadius:g,outerRadius:x,startAngle:s,endAngle:f});return p(p({},t),{},h({},n,A))},{})},g=function(t,e){var n=t.x,r=t.y;return Math.sqrt(Math.pow(n-e.x,2)+Math.pow(r-e.y,2))},x=function(t,e){var n=t.x,r=t.y,o=e.cx,i=e.cy,a=g({x:n,y:r},{x:o,y:i});if(a<=0)return{radius:a};var u=Math.acos((n-o)/a);return r>i&&(u=2*Math.PI-u),{radius:a,angle:180*u/Math.PI,angleInRadian:u}},O=function(t){var e=t.startAngle,n=t.endAngle,r=Math.min(Math.floor(e/360),Math.floor(n/360));return{startAngle:e-360*r,endAngle:n-360*r}},w=function(t,e){var n,r=x({x:t.x,y:t.y},e),o=r.radius,i=r.angle,a=e.innerRadius,u=e.outerRadius;if(o<a||o>u)return!1;if(0===o)return!0;var c=O(e),l=c.startAngle,s=c.endAngle,f=i;if(l<=s){for(;f>s;)f-=360;for(;f<l;)f+=360;n=f>=l&&f<=s}else{for(;f>l;)f-=360;for(;f<s;)f+=360;n=f>=s&&f<=l}return n?p(p({},e),{},{radius:o,angle:f+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null},j=function(t){return(0,i.isValidElement)(t)||u()(t)||"boolean"==typeof t?"":t.className}},21105:function(t,e,n){"use strict";n.d(e,{$R:function(){return L},Bh:function(){return N},Gf:function(){return w},L6:function(){return T},NN:function(){return A},TT:function(){return k},eu:function(){return D},rL:function(){return C},sP:function(){return E}});var r=n(82404),o=n.n(r),i=n(86197),a=n.n(i),u=n(62066),c=n.n(u),l=n(14119),s=n.n(l),f=n(5635),p=n.n(f),h=n(32486),d=n(41853),y=n(46729),v=n(38269),m=n(95273),b=["children"],g=["children"];function x(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n={};for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;n[r]=t[r]}return n}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}var O={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},w=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},j=null,S=null,P=function t(e){if(e===j&&Array.isArray(S))return S;var n=[];return h.Children.forEach(e,function(e){a()(e)||((0,d.isFragment)(e)?n=n.concat(t(e.props.children)):n.push(e))}),S=n,j=e,n};function A(t,e){var n=[],r=[];return r=Array.isArray(e)?e.map(function(t){return w(t)}):[w(e)],P(t).forEach(function(t){var e=o()(t,"type.displayName")||o()(t,"type.name");-1!==r.indexOf(e)&&n.push(t)}),n}function E(t,e){var n=A(t,e);return n&&n[0]}var k=function(t){if(!t||!t.props)return!1;var e=t.props,n=e.width,r=e.height;return!!(0,y.hj)(n)&&!(n<=0)&&!!(0,y.hj)(r)&&!(r<=0)},M=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],_=function(t,e,n,r){var o,i=null!==(o=null===m.ry||void 0===m.ry?void 0:m.ry[r])&&void 0!==o?o:[];return e.startsWith("data-")||!s()(t)&&(r&&i.includes(e)||m.Yh.includes(e))||n&&m.nv.includes(e)},T=function(t,e,n){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,h.isValidElement)(t)&&(r=t.props),!p()(r))return null;var o={};return Object.keys(r).forEach(function(t){var i;_(null===(i=r)||void 0===i?void 0:i[t],t,e,n)&&(o[t]=r[t])}),o},C=function t(e,n){if(e===n)return!0;var r=h.Children.count(e);if(r!==h.Children.count(n))return!1;if(0===r)return!0;if(1===r)return I(Array.isArray(e)?e[0]:e,Array.isArray(n)?n[0]:n);for(var o=0;o<r;o++){var i=e[o],a=n[o];if(Array.isArray(i)||Array.isArray(a)){if(!t(i,a))return!1}else if(!I(i,a))return!1}return!0},I=function(t,e){if(a()(t)&&a()(e))return!0;if(!a()(t)&&!a()(e)){var n=t.props||{},r=n.children,o=x(n,b),i=e.props||{},u=i.children,c=x(i,g);if(r&&u)return(0,v.w)(o,c)&&C(r,u);if(!r&&!u)return(0,v.w)(o,c)}return!1},D=function(t,e){var n=[],r={};return P(t).forEach(function(t,o){if(t&&t.type&&c()(t.type)&&M.indexOf(t.type)>=0)n.push(t);else if(t){var i=w(t.type),a=e[i]||{},u=a.handler,l=a.once;if(u&&(!l||!r[i])){var s=u(t,i,o);n.push(s),r[i]=!0}}}),n},N=function(t){var e=t&&t.type;return e&&O[e]?O[e]:null},L=function(t,e){return P(e).indexOf(t)}},38269:function(t,e,n){"use strict";function r(t,e){for(var n in t)if(({}).hasOwnProperty.call(t,n)&&(!({}).hasOwnProperty.call(e,n)||t[n]!==e[n]))return!1;for(var r in e)if(({}).hasOwnProperty.call(e,r)&&!({}).hasOwnProperty.call(t,r))return!1;return!0}n.d(e,{w:function(){return r}})},89099:function(t,e,n){"use strict";n.d(e,{z:function(){return l}});var r=n(26109),o=n(74499),i=n(21105);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function c(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?u(Object(n),!0).forEach(function(e){var r,o;r=e,o=n[e],(r=function(t){var e=function(t,e){if("object"!=a(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=a(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==a(e)?e:e+""}(r))in t?Object.defineProperty(t,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[r]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}var l=function(t){var e,n=t.children,a=t.formattedGraphicalItems,u=t.legendWidth,l=t.legendContent,s=(0,i.sP)(n,r.D);if(!s)return null;var f=r.D.defaultProps,p=void 0!==f?c(c({},f),s.props):{};return e=s.props&&s.props.payload?s.props&&s.props.payload:"children"===l?(a||[]).reduce(function(t,e){var n=e.item,r=e.props,o=r.sectors||r.data||[];return t.concat(o.map(function(t){return{type:s.props.iconType||n.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(a||[]).map(function(t){var e=t.item,n=e.type.defaultProps,r=void 0!==n?c(c({},n),e.props):{},i=r.dataKey,a=r.name,u=r.legendType;return{inactive:r.hide,dataKey:i,type:p.iconType||u||"square",color:(0,o.fk)(e),value:a||i,payload:r}}),c(c(c({},p),r.D.getWithHeight(s,u)),{},{payload:e,item:s})}},78262:function(t,e,n){"use strict";n.d(e,{z:function(){return u}});var r=n(18234),o=n.n(r),i=n(14119),a=n.n(i);function u(t,e,n){return!0===e?o()(t,n):a()(e)?o()(t,e):t}},95273:function(t,e,n){"use strict";n.d(e,{Yh:function(){return u},Ym:function(){return f},bw:function(){return p},nv:function(){return s},ry:function(){return l}});var r=n(32486),o=n(5635),i=n.n(o);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var u=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],c=["points","pathLength"],l={svg:["viewBox","children"],polygon:c,polyline:c},s=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],f=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,r.isValidElement)(t)&&(n=t.props),!i()(n))return null;var o={};return Object.keys(n).forEach(function(t){s.includes(t)&&(o[t]=e||function(e){return n[t](n,e)})}),o},p=function(t,e,n){if(!i()(t)||"object"!==a(t))return null;var r=null;return Object.keys(t).forEach(function(o){var i=t[o];s.includes(o)&&"function"==typeof i&&(r||(r={}),r[o]=function(t){return i(e,n,t),null})}),r}},29626:function(t,e,n){"use strict";n.d(e,{F:function(){return i},e:function(){return a}});var r=n(32486);function o(t,e){if("function"==typeof t)return t(e);null!=t&&(t.current=e)}function i(...t){return e=>{let n=!1,r=t.map(t=>{let r=o(t,e);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let e=0;e<r.length;e++){let n=r[e];"function"==typeof n?n():o(t[e],null)}}}}function a(...t){return r.useCallback(i(...t),t)}},91007:function(t,e,n){"use strict";n.d(e,{Z8:function(){return a},g7:function(){return u},sA:function(){return l}});var r=n(32486),o=n(29626),i=n(75376);function a(t){let e=function(t){let e=r.forwardRef((t,e)=>{let{children:n,...i}=t;if(r.isValidElement(n)){let t,a;let u=(t=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?n.ref:(t=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?n.props.ref:n.props.ref||n.ref,c=function(t,e){let n={...e};for(let r in e){let o=t[r],i=e[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...t)=>{let e=i(...t);return o(...t),e}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...t,...n}}(i,n.props);return n.type!==r.Fragment&&(c.ref=e?(0,o.F)(e,u):u),r.cloneElement(n,c)}return r.Children.count(n)>1?r.Children.only(null):null});return e.displayName=`${t}.SlotClone`,e}(t),n=r.forwardRef((t,n)=>{let{children:o,...a}=t,u=r.Children.toArray(o),c=u.find(s);if(c){let t=c.props.children,o=u.map(e=>e!==c?e:r.Children.count(t)>1?r.Children.only(null):r.isValidElement(t)?t.props.children:null);return(0,i.jsx)(e,{...a,ref:n,children:r.isValidElement(t)?r.cloneElement(t,void 0,o):null})}return(0,i.jsx)(e,{...a,ref:n,children:o})});return n.displayName=`${t}.Slot`,n}var u=a("Slot"),c=Symbol("radix.slottable");function l(t){let e=({children:t})=>(0,i.jsx)(i.Fragment,{children:t});return e.displayName=`${t}.Slottable`,e.__radixId=c,e}function s(t){return r.isValidElement(t)&&"function"==typeof t.type&&"__radixId"in t.type&&t.type.__radixId===c}},53447:function(t,e,n){"use strict";n.d(e,{j:function(){return a}});var r=n(89824);let o=t=>"boolean"==typeof t?`${t}`:0===t?"0":t,i=r.W,a=(t,e)=>n=>{var r;if((null==e?void 0:e.variants)==null)return i(t,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:a,defaultVariants:u}=e,c=Object.keys(a).map(t=>{let e=null==n?void 0:n[t],r=null==u?void 0:u[t];if(null===e)return null;let i=o(e)||o(r);return a[t][i]}),l=n&&Object.entries(n).reduce((t,e)=>{let[n,r]=e;return void 0===r||(t[n]=r),t},{});return i(t,c,null==e?void 0:null===(r=e.compoundVariants)||void 0===r?void 0:r.reduce((t,e)=>{let{class:n,className:r,...o}=e;return Object.entries(o).every(t=>{let[e,n]=t;return Array.isArray(n)?n.includes({...u,...l}[e]):({...u,...l})[e]===n})?[...t,n,r]:t},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},69802:function(t,e,n){"use strict";n.d(e,{Z:function(){return i},x:function(){return a}});var r=n(7628),o=n(53475);function i(){var t,e,n=(0,o.Z)().unknown(void 0),a=n.domain,u=n.range,c=0,l=1,s=!1,f=0,p=0,h=.5;function d(){var n=a().length,r=l<c,o=r?l:c,i=r?c:l;t=(i-o)/Math.max(1,n-f+2*p),s&&(t=Math.floor(t)),o+=(i-o-t*(n-f))*h,e=t*(1-f),s&&(o=Math.round(o),e=Math.round(e));var d=(function(t,e,n){t=+t,e=+e,n=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+n;for(var r=-1,o=0|Math.max(0,Math.ceil((e-t)/n)),i=Array(o);++r<o;)i[r]=t+r*n;return i})(n).map(function(e){return o+t*e});return u(r?d.reverse():d)}return delete n.unknown,n.domain=function(t){return arguments.length?(a(t),d()):a()},n.range=function(t){return arguments.length?([c,l]=t,c=+c,l=+l,d()):[c,l]},n.rangeRound=function(t){return[c,l]=t,c=+c,l=+l,s=!0,d()},n.bandwidth=function(){return e},n.step=function(){return t},n.round=function(t){return arguments.length?(s=!!t,d()):s},n.padding=function(t){return arguments.length?(f=Math.min(1,p=+t),d()):f},n.paddingInner=function(t){return arguments.length?(f=Math.min(1,t),d()):f},n.paddingOuter=function(t){return arguments.length?(p=+t,d()):p},n.align=function(t){return arguments.length?(h=Math.max(0,Math.min(1,t)),d()):h},n.copy=function(){return i(a(),[c,l]).round(s).paddingInner(f).paddingOuter(p).align(h)},r.o.apply(d(),arguments)}function a(){return function t(e){var n=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(n())},e}(i.apply(null,arguments).paddingInner(1))}},7628:function(t,e,n){"use strict";function r(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function o(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}n.d(e,{O:function(){return o},o:function(){return r}})},53475:function(t,e,n){"use strict";n.d(e,{Z:function(){return function t(){var e=new r,n=[],o=[],i=u;function c(t){let r=e.get(t);if(void 0===r){if(i!==u)return i;e.set(t,r=n.push(t)-1)}return o[r%o.length]}return c.domain=function(t){if(!arguments.length)return n.slice();for(let o of(n=[],e=new r,t))e.has(o)||e.set(o,n.push(o)-1);return c},c.range=function(t){return arguments.length?(o=Array.from(t),c):o.slice()},c.unknown=function(t){return arguments.length?(i=t,c):i},c.copy=function(){return t(n,o).unknown(i)},a.o.apply(c,arguments),c}},O:function(){return u}});class r extends Map{constructor(t,e=i){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,n]of t)this.set(e,n)}get(t){return super.get(o(this,t))}has(t){return super.has(o(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},n){let r=e(n);return t.has(r)?t.get(r):(t.set(r,n),n)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},n){let r=e(n);return t.has(r)&&(n=t.get(r),t.delete(r)),n}(this,t))}}function o({_intern:t,_key:e},n){let r=e(n);return t.has(r)?t.get(r):n}function i(t){return null!==t&&"object"==typeof t?t.valueOf():t}var a=n(7628);let u=Symbol("implicit")},42562:function(t,e,n){"use strict";function r(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}n.d(e,{Z:function(){return r}}),Array.prototype.slice},37226:function(t,e,n){"use strict";function r(t){return function(){return t}}n.d(e,{Z:function(){return r}})},55059:function(t,e,n){"use strict";n.d(e,{d:function(){return c}});let r=Math.PI,o=2*r,i=o-1e-6;function a(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=arguments[e]+t[e]}class u{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?a:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return a;let n=10**e;return function(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=Math.round(arguments[e]*n)/n+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,n,r){this._append`Q${+t},${+e},${this._x1=+n},${this._y1=+r}`}bezierCurveTo(t,e,n,r,o,i){this._append`C${+t},${+e},${+n},${+r},${this._x1=+o},${this._y1=+i}`}arcTo(t,e,n,o,i){if(t=+t,e=+e,n=+n,o=+o,(i=+i)<0)throw Error(`negative radius: ${i}`);let a=this._x1,u=this._y1,c=n-t,l=o-e,s=a-t,f=u-e,p=s*s+f*f;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(p>1e-6){if(Math.abs(f*c-l*s)>1e-6&&i){let h=n-a,d=o-u,y=c*c+l*l,v=Math.sqrt(y),m=Math.sqrt(p),b=i*Math.tan((r-Math.acos((y+p-(h*h+d*d))/(2*v*m)))/2),g=b/m,x=b/v;Math.abs(g-1)>1e-6&&this._append`L${t+g*s},${e+g*f}`,this._append`A${i},${i},0,0,${+(f*h>s*d)},${this._x1=t+x*c},${this._y1=e+x*l}`}else this._append`L${this._x1=t},${this._y1=e}`}}arc(t,e,n,a,u,c){if(t=+t,e=+e,c=!!c,(n=+n)<0)throw Error(`negative radius: ${n}`);let l=n*Math.cos(a),s=n*Math.sin(a),f=t+l,p=e+s,h=1^c,d=c?a-u:u-a;null===this._x1?this._append`M${f},${p}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-p)>1e-6)&&this._append`L${f},${p}`,n&&(d<0&&(d=d%o+o),d>i?this._append`A${n},${n},0,1,${h},${t-l},${e-s}A${n},${n},0,1,${h},${this._x1=f},${this._y1=p}`:d>1e-6&&this._append`A${n},${n},0,${+(d>=r)},${h},${this._x1=t+n*Math.cos(u)},${this._y1=e+n*Math.sin(u)}`)}rect(t,e,n,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${n=+n}v${+r}h${-n}Z`}toString(){return this._}}function c(t){let e=3;return t.digits=function(n){if(!arguments.length)return e;if(null==n)e=null;else{let t=Math.floor(n);if(!(t>=0))throw RangeError(`invalid digits: ${n}`);e=t}return t},()=>new u(e)}u.prototype},12445:function(t,e,n){"use strict";function r(t,e){if(!t)throw Error("Invariant failed")}n.d(e,{Z:function(){return r}})}}]);