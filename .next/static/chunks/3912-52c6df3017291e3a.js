"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3912],{9824:function(e,t,r){r.d(t,{Z:function(){return c}});var n=r(32486);/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),u=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:l=2,absoluteStrokeWidth:c,className:a="",children:f,iconNode:s,...d}=e;return(0,n.createElement)("svg",{ref:t,...i,width:o,height:o,stroke:r,strokeWidth:c?24*Number(l)/Number(o):l,className:u("lucide",a),...d},[...s.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(f)?f:[f]])}),c=(e,t)=>{let r=(0,n.forwardRef)((r,i)=>{let{className:c,...a}=r;return(0,n.createElement)(l,{ref:i,iconNode:t,className:u("lucide-".concat(o(e)),c),...a})});return r.displayName="".concat(e),r}},51888:function(e,t,r){r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},5426:function(e,t,r){r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},57292:function(e,t,r){r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},39713:function(e,t,r){r.d(t,{default:function(){return o.a}});var n=r(74033),o=r.n(n)},47411:function(e,t,r){var n=r(13362);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},74033:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return c},getImageProps:function(){return l}});let n=r(60723),o=r(25738),u=r(28863),i=n._(r(44543));function l(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let c=u.Image},20100:function(e,t,r){r.d(t,{M:function(){return n}});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},29626:function(e,t,r){r.d(t,{F:function(){return u},e:function(){return i}});var n=r(32486);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function u(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function i(...e){return n.useCallback(u(...e),e)}},32192:function(e,t,r){r.d(t,{b:function(){return i},k:function(){return u}});var n=r(32486),o=r(75376);function u(e,t){let r=n.createContext(t),u=e=>{let{children:t,...u}=e,i=n.useMemo(()=>u,Object.values(u));return(0,o.jsx)(r.Provider,{value:i,children:t})};return u.displayName=e+"Provider",[u,function(o){let u=n.useContext(r);if(u)return u;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,t=[]){let r=[],u=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return u.scopeName=e,[function(t,u){let i=n.createContext(u),l=r.length;r=[...r,u];let c=t=>{let{scope:r,children:u,...c}=t,a=r?.[e]?.[l]||i,f=n.useMemo(()=>c,Object.values(c));return(0,o.jsx)(a.Provider,{value:f,children:u})};return c.displayName=t+"Provider",[c,function(r,o){let c=o?.[e]?.[l]||i,a=n.useContext(c);if(a)return a;if(void 0!==u)return u;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(u,...t)]}},89801:function(e,t,r){r.d(t,{WV:function(){return l},jH:function(){return c}});var n=r(32486),o=r(54087),u=r(91007),i=r(75376),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,u.Z8)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...u}=e,l=o?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l,{...u,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function c(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},91007:function(e,t,r){r.d(t,{Z8:function(){return i},g7:function(){return l},sA:function(){return a}});var n=r(32486),o=r(29626),u=r(75376);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...u}=e;if(n.isValidElement(r)){let e,i;let l=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,c=function(e,t){let r={...t};for(let n in t){let o=e[n],u=t[n];/^on[A-Z]/.test(n)?o&&u?r[n]=(...e)=>{let t=u(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...u}:"className"===n&&(r[n]=[o,u].filter(Boolean).join(" "))}return{...e,...r}}(u,r.props);return r.type!==n.Fragment&&(c.ref=t?(0,o.F)(t,l):l),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...i}=e,l=n.Children.toArray(o),c=l.find(f);if(c){let e=c.props.children,o=l.map(t=>t!==c?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,u.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,u.jsx)(t,{...i,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}var l=i("Slot"),c=Symbol("radix.slottable");function a(e){let t=({children:e})=>(0,u.jsx)(u.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=c,t}function f(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}},70144:function(e,t,r){r.d(t,{bU:function(){return S},fC:function(){return k}});var n=r(32486),o=r(20100),u=r(29626),i=r(32192),l=r(31413),c=r(75659),a=r(30915),f=r(89801),s=r(75376),d="Switch",[p,m]=(0,i.b)(d),[h,v]=p(d),y=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:i,checked:c,defaultChecked:a,required:p,disabled:m,value:v="on",onCheckedChange:y,form:b,...g}=e,[k,S]=n.useState(null),C=(0,u.e)(t,e=>S(e)),P=n.useRef(!1),E=!k||b||!!k.closest("form"),[j,N]=(0,l.T)({prop:c,defaultProp:null!=a&&a,onChange:y,caller:d});return(0,s.jsxs)(h,{scope:r,checked:j,disabled:m,children:[(0,s.jsx)(f.WV.button,{type:"button",role:"switch","aria-checked":j,"aria-required":p,"data-state":x(j),"data-disabled":m?"":void 0,disabled:m,value:v,...g,ref:C,onClick:(0,o.M)(e.onClick,e=>{N(e=>!e),E&&(P.current=e.isPropagationStopped(),P.current||e.stopPropagation())})}),E&&(0,s.jsx)(w,{control:k,bubbles:!P.current,name:i,value:v,checked:j,required:p,disabled:m,form:b,style:{transform:"translateX(-100%)"}})]})});y.displayName=d;var b="SwitchThumb",g=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,o=v(b,r);return(0,s.jsx)(f.WV.span,{"data-state":x(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t})});g.displayName=b;var w=n.forwardRef((e,t)=>{let{__scopeSwitch:r,control:o,checked:i,bubbles:l=!0,...f}=e,d=n.useRef(null),p=(0,u.e)(d,t),m=(0,c.D)(i),h=(0,a.t)(o);return n.useEffect(()=>{let e=d.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==i&&t){let r=new Event("click",{bubbles:l});t.call(e,i),e.dispatchEvent(r)}},[m,i,l]),(0,s.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i,...f,tabIndex:-1,ref:p,style:{...f.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function x(e){return e?"checked":"unchecked"}w.displayName="SwitchBubbleInput";var k=y,S=g},31413:function(e,t,r){r.d(t,{T:function(){return l}});var n,o=r(32486),u=r(79315),i=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||u.b;function l({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[u,l,c]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),u=o.useRef(r),l=o.useRef(t);return i(()=>{l.current=t},[t]),o.useEffect(()=>{u.current!==r&&(l.current?.(r),u.current=r)},[r,u]),[r,n,l]}({defaultProp:t,onChange:r}),a=void 0!==e,f=a?e:u;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==a){let t=a?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=a},[a,n])}return[f,o.useCallback(t=>{if(a){let r="function"==typeof t?t(e):t;r!==e&&c.current?.(r)}else l(t)},[a,e,l,c])]}Symbol("RADIX:SYNC_STATE")},79315:function(e,t,r){r.d(t,{b:function(){return o}});var n=r(32486),o=globalThis?.document?n.useLayoutEffect:()=>{}},75659:function(e,t,r){r.d(t,{D:function(){return o}});var n=r(32486);function o(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},30915:function(e,t,r){r.d(t,{t:function(){return u}});var n=r(32486),o=r(79315);function u(e){let[t,r]=n.useState(void 0);return(0,o.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let u=t[0];if("borderBoxSize"in u){let e=u.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}}}]);