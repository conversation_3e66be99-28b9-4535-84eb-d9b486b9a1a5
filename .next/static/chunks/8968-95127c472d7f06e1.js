"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8968],{64632:function(e,t,r){r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},98755:function(e,t,r){r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},83255:function(e,t,r){r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},22397:function(e,t,r){r.d(t,{Z:function(){return n}});/**
 * @license lucide-react v0.417.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(9824).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},94797:function(e,t,r){r.d(t,{Dx:function(){return en},VY:function(){return er},aV:function(){return et},dk:function(){return eo},fC:function(){return Q},h_:function(){return ee},x8:function(){return ei},xz:function(){return $}});var n=r(32486),o=r(20100),i=r(29626),a=r(32192),l=r(21971),u=r(31413),c=r(35878),s=r(5887),d=r(79872),f=r(53486),p=r(89801),v=r(67058),h=r(25081),g=r(15623),y=r(91007),b=r(75376),m="Dialog",[x,k]=(0,a.b)(m),[w,D]=x(m),j=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:i,onOpenChange:a,modal:c=!0}=e,s=n.useRef(null),d=n.useRef(null),[f,p]=(0,u.T)({prop:o,defaultProp:null!=i&&i,onChange:a,caller:m});return(0,b.jsx)(w,{scope:t,triggerRef:s,contentRef:d,contentId:(0,l.M)(),titleId:(0,l.M)(),descriptionId:(0,l.M)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:c,children:r})};j.displayName=m;var R="DialogTrigger",C=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=D(R,r),l=(0,i.e)(t,a.triggerRef);return(0,b.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":U(a.open),...n,ref:l,onClick:(0,o.M)(e.onClick,a.onOpenToggle)})});C.displayName=R;var E="DialogPortal",[M,I]=x(E,{forceMount:void 0}),N=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:i}=e,a=D(E,t);return(0,b.jsx)(M,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,b.jsx)(f.z,{present:r||a.open,children:(0,b.jsx)(d.h,{asChild:!0,container:i,children:e})}))})};N.displayName=E;var O="DialogOverlay",_=n.forwardRef((e,t)=>{let r=I(O,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,i=D(O,e.__scopeDialog);return i.modal?(0,b.jsx)(f.z,{present:n||i.open,children:(0,b.jsx)(P,{...o,ref:t})}):null});_.displayName=O;var F=(0,y.Z8)("DialogOverlay.RemoveScroll"),P=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(O,r);return(0,b.jsx)(h.Z,{as:F,allowPinchZoom:!0,shards:[o.contentRef],children:(0,b.jsx)(p.WV.div,{"data-state":U(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),S="DialogContent",W=n.forwardRef((e,t)=>{let r=I(S,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,i=D(S,e.__scopeDialog);return(0,b.jsx)(f.z,{present:n||i.open,children:i.modal?(0,b.jsx)(z,{...o,ref:t}):(0,b.jsx)(A,{...o,ref:t})})});W.displayName=S;var z=n.forwardRef((e,t)=>{let r=D(S,e.__scopeDialog),a=n.useRef(null),l=(0,i.e)(t,r.contentRef,a);return n.useEffect(()=>{let e=a.current;if(e)return(0,g.Ry)(e)},[]),(0,b.jsx)(Z,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault())})}),A=n.forwardRef((e,t)=>{let r=D(S,e.__scopeDialog),o=n.useRef(!1),i=n.useRef(!1);return(0,b.jsx)(Z,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,a;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current||null===(a=r.triggerRef.current)||void 0===a||a.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{var n,a;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let l=t.target;(null===(a=r.triggerRef.current)||void 0===a?void 0:a.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),Z=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:l,...u}=e,d=D(S,r),f=n.useRef(null),p=(0,i.e)(t,f);return(0,v.EW)(),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(s.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:l,children:(0,b.jsx)(c.XB,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":U(d.open),...u,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(G,{titleId:d.titleId}),(0,b.jsx)(J,{contentRef:f,descriptionId:d.descriptionId})]})]})}),V="DialogTitle",T=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(V,r);return(0,b.jsx)(p.WV.h2,{id:o.titleId,...n,ref:t})});T.displayName=V;var B="DialogDescription",H=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(B,r);return(0,b.jsx)(p.WV.p,{id:o.descriptionId,...n,ref:t})});H.displayName=B;var X="DialogClose",q=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=D(X,r);return(0,b.jsx)(p.WV.button,{type:"button",...n,ref:t,onClick:(0,o.M)(e.onClick,()=>i.onOpenChange(!1))})});function U(e){return e?"open":"closed"}q.displayName=X;var K="DialogTitleWarning",[L,Y]=(0,a.k)(K,{contentName:S,titleName:V,docsSlug:"dialog"}),G=e=>{let{titleId:t}=e,r=Y(K),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},J=e=>{let{contentRef:t,descriptionId:r}=e,o=Y("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");r&&n&&!document.getElementById(r)&&console.warn(i)},[i,t,r]),null},Q=j,$=C,ee=N,et=_,er=W,en=T,eo=H,ei=q},70144:function(e,t,r){r.d(t,{bU:function(){return D},fC:function(){return w}});var n=r(32486),o=r(20100),i=r(29626),a=r(32192),l=r(31413),u=r(75659),c=r(30915),s=r(89801),d=r(75376),f="Switch",[p,v]=(0,a.b)(f),[h,g]=p(f),y=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:a,checked:u,defaultChecked:c,required:p,disabled:v,value:g="on",onCheckedChange:y,form:b,...m}=e,[w,D]=n.useState(null),j=(0,i.e)(t,e=>D(e)),R=n.useRef(!1),C=!w||b||!!w.closest("form"),[E,M]=(0,l.T)({prop:u,defaultProp:null!=c&&c,onChange:y,caller:f});return(0,d.jsxs)(h,{scope:r,checked:E,disabled:v,children:[(0,d.jsx)(s.WV.button,{type:"button",role:"switch","aria-checked":E,"aria-required":p,"data-state":k(E),"data-disabled":v?"":void 0,disabled:v,value:g,...m,ref:j,onClick:(0,o.M)(e.onClick,e=>{M(e=>!e),C&&(R.current=e.isPropagationStopped(),R.current||e.stopPropagation())})}),C&&(0,d.jsx)(x,{control:w,bubbles:!R.current,name:a,value:g,checked:E,required:p,disabled:v,form:b,style:{transform:"translateX(-100%)"}})]})});y.displayName=f;var b="SwitchThumb",m=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,o=g(b,r);return(0,d.jsx)(s.WV.span,{"data-state":k(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t})});m.displayName=b;var x=n.forwardRef((e,t)=>{let{__scopeSwitch:r,control:o,checked:a,bubbles:l=!0,...s}=e,f=n.useRef(null),p=(0,i.e)(f,t),v=(0,u.D)(a),h=(0,c.t)(o);return n.useEffect(()=>{let e=f.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(v!==a&&t){let r=new Event("click",{bubbles:l});t.call(e,a),e.dispatchEvent(r)}},[v,a,l]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...s,tabIndex:-1,ref:p,style:{...s.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function k(e){return e?"checked":"unchecked"}x.displayName="SwitchBubbleInput";var w=y,D=m},75659:function(e,t,r){r.d(t,{D:function(){return o}});var n=r(32486);function o(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},30915:function(e,t,r){r.d(t,{t:function(){return i}});var n=r(32486),o=r(79315);function i(e){let[t,r]=n.useState(void 0);return(0,o.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}}}]);